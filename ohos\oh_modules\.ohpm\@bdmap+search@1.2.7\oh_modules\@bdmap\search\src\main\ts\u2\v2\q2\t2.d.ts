import { SuggestionSearchOption } from "../../../d/q2/r2";
import { SuggestionResult } from "../../../d/q2/s2";
import { BaseSearch } from "../../base/base";
export interface ISuggestionSearch {
    requestSuggestion(option: SuggestionSearchOption): Promise<SuggestionResult>;
}
export declare class SuggestionSearchImp extends BaseSearch implements ISuggestionSearch {
    requestSuggestion(z32: SuggestionSearchOption): Promise<SuggestionResult>;
}
