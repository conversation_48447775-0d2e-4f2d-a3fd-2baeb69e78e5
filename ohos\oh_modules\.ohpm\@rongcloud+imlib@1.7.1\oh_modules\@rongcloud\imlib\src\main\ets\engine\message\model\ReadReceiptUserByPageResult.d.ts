import { ReadReceiptUserInfo } from "./ReadReceiptUserInfo";
import { List } from "@kit.ArkTS";
/**
 * 已读回执结果，用于已读 V5 信息主动获取时返回。
 *
 * @version 1.5.0
 */
export declare class ReadReceiptUsersResult {
    /**
     * 当前分页请求 Token，用于下一次请求拉取
     * 在分页获取已读回执用户列表时有效，全量获取时忽略本字段
     */
    pageToken: string;
    /**
     * 用户信息列表
     */
    userArray: List<ReadReceiptUserInfo>;
    /**
     * 用户总数
     */
    totalCount: number;
}
