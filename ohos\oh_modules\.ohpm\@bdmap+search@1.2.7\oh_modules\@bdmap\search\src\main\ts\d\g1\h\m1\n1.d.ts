import { CityInfo, PoiInfo, SearchResult } from "../../../e/h/f1";
import { PoiAddrInfo } from "../o1";
/**
 * poi搜索结果。
 */
export interface PoiResult extends SearchResult {
    /**
     * 当前分页编号
     * 0-第一页；1-第二页，依此类推
     */
    currentPageNum?: number;
    /**
     * 所有分页数
     */
    totalPageNum?: number;
    /**
     * 单页可展示的POI数量
     */
    currentPageCapacity?: number;
    /**
     * 结果所有POI数量
     */
    totalPoiNum?: number;
    /**
     * POI信息列表
     * POI各字段在{@link PoiInfo}中定义
     */
    arrayPoiInfo?: PoiInfo[];
    /**
     * 是否包含门址信息
     */
    hasPoiAddr?: boolean;
    /**
     * POI门址信息列表
     * 门址各个字段在{@link PoiAddrInfo}中定义
     */
    arrayAddrInfo?: PoiAddrInfo[];
    /**
     * 城市信息列表
     * 城市检索，当前城市无结果，结果在其它城市时，返回其它城市信息
     */
    arrayCityInfo?: CityInfo[];
}
