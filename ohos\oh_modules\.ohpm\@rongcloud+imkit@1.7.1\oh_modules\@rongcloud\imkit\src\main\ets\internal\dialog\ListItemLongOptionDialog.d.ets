// @keepTs
// @ts-nocheck
import { ItemLongClickAction } from '../../base/click/ItemLongClickAction';
@CustomDialog
export declare struct ListItemLongOptionDialog {
    @Prop
    actionArray: Array<ItemLongClickAction>;
    controller: CustomDialogController;
    onShowTitle: Function;
    onClickItem: Function;
    @State
    touchDown: boolean;
    @State
    touchPosition: number;
    build(): void;
}
