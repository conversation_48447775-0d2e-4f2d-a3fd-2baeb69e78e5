// @keepTs
// @ts-nocheck
/**
 * Created on 2024/10/16
 * <AUTHOR>
 */
import { BaseMessageItemProvider } from '../../../../conversation/item/provider/BaseMessageItemProvider';
import { UiMessage } from '../../../../conversation/model/UiMessage';
import { HQVoiceMessage } from '@rongcloud/imlib';
export declare class HQVoiceMessageItemProvider extends BaseMessageItemProvider<HQVoiceMessage> {
    getMessageWrapBuilder(): WrappedBuilder<[
        Context,
        UiMessage,
        number
    ]>;
    isShowSummaryName(e177: Context, f177: HQVoiceMessage): boolean;
    getSummaryTextByMessageContent(z176: Context, a177: HQVoiceMessage): Promise<MutableStyledString>;
    private isDestruct;
}
@Builder
export declare function bindImageMessageData(n176: Context, o176: UiMessage, p176: number): void;
@Component
export declare struct HQVoiceMessageView {
    @Require
    @Prop
    index: number;
    @Require
    @Prop
    context: Context;
    @ObjectLink
    uiMessage: UiMessage;
    @State
    isShowRedForVoice: boolean;
    @State
    stateAnim: AnimationStatus;
    private hqVoiceMessage;
    private playListener;
    private downloadListener;
    aboutToAppear(): void;
    aboutToDisappear(): void;
    build(): void;
    /**
     * 播放当前语音消息
     */
    private onPlayStart;
    /**
     * 判断是否是本人
     * @returns
     */
    private isSelf;
    /**
     * 设置播放状态动画状态
     * @param state
     */
    private setStateAnim;
}
