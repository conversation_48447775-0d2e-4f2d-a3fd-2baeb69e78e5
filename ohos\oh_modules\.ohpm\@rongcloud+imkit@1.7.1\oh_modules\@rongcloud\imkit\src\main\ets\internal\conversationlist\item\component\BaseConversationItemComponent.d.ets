// @keepTs
// @ts-nocheck
import { BaseUiConversation } from '../../../../conversationlist/model/BaseUiConversation';
@Component
export declare struct BaseConversationItemComponent {
    private context;
    @ObjectLink
    baseConversation: BaseUiConversation;
    private idPrefix;
    private containerId;
    private unreadViewId;
    private portraitId;
    private titleId;
    private titleLayout;
    private contentId;
    private timeId;
    private notifyStateId;
    private readStateId;
    private contentController;
    private conversationAvatarStyle;
    /**
     * 会话信息内容改变
     */
    onDataChange(): void;
    @Builder
    customBuilder(): void;
    @BuilderParam
    customBuilderParam: () => void;
    aboutToAppear(): void;
    getSentStatusIcon(): Resource | undefined;
    build(): void;
    private getAlt;
    private isNoDisturb;
    private isShowReadStatus;
}
