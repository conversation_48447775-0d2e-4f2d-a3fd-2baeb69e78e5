          import { LatLng } from '@bdmap/base'; import Bundle from "../o/i1"; import Overlay from "./m"; import type { ColorString, IDotOption, Nullable } from "../../g1/a2";             export default class Dot extends Overlay { private mCenter; private mRadius; private mColor;                                   constructor(b37?: IDotOption);       init(): void;         getCenter(): LatLng;         center(center: LatLng): this;         setCenter(center: LatLng): void;         getColor(): any;         color(color: ColorString): this;         setColor(color: ColorString): void;         getRadius(): number;         radius(radius: number): this;         setRadius(radius: number): void; get typeName(): string;       dataFormat(): Nullable<Bundle>;       toBundle(): Promise<Bundle>;       toString(): string; } 