import LatLng from "./e"; import Point from "./f";                 export default class NativeMethods { static SetSysValues(filesDir: any, u5: any, densityDPI: any, resourceManager: any): any; static registerJSFunction(name: any, func: any): any; static sign(t5: any): any; static encodeUrlParamsValue(v: any): any;             static getDistanceByLL(s5: LatLng, p2: LatLng): number;           static ll2mc(q5: LatLng): Point;           static mc2ll(o5: Point): LatLng;           static wgsll2gcjll(n5: LatLng): LatLng;           static wgsll2bdll(m5: LatLng): LatLng;           static gcjll2bdll(l5: LatLng): LatLng;           static bdll2gcjll(k5: LatLng): LatLng; static getAESSaltKey(token: string): any; static getAESViKey(token: string): any; static getProjectionPt(j5: string): any; static decryptPNKD(i5: string): any; } 