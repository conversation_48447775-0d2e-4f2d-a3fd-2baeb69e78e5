import AbilityConstant from '@ohos.app.ability.AbilityConstant';
import UIAbility from '@ohos.app.ability.UIAbility';
import Want from '@ohos.app.ability.Want';
import window from '@ohos.window';
export default class EntryAbility extends UIAbility {
    onCreate(a45: Want, b45: AbilityConstant.LaunchParam): void;
    onDestroy(): void;
    onWindowStageCreate(w44: window.WindowStage): void;
    onWindowStageDestroy(): void;
    onForeground(): void;
    onBackground(): void;
}
