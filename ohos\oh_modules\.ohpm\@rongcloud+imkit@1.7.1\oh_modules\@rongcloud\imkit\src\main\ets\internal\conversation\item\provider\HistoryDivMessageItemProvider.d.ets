// @keepTs
// @ts-nocheck
import { MessageContent } from '@rongcloud/imlib';
import { BaseNotificationMessageItemProvider } from '../../../../conversation/item/provider/BaseNotificationMessageItemProvider';
import { UiMessage } from '../../../../conversation/model/UiMessage';
import { HistoryDividerMessage } from '../../message/HistoryDividerMessage';
export declare class HistoryDivMessageItemProvider extends BaseNotificationMessageItemProvider<HistoryDividerMessage> {
    getMessageWrapBuilder(): WrappedBuilder<[
        Context,
        UiMessage,
        number
    ]>;
    getSummaryTextByMessageContent(g173: Context, h173: MessageContent): Promise<MutableStyledString>;
}
@Builder
export declare function bindHistoryDivMessageData(q172: Context, r172: UiMessage, s172: number): void;
