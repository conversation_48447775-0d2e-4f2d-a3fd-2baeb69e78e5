// @keepTs
// @ts-nocheck
import { RichContentMessage } from '@rongcloud/imlib';
import { BaseMessageItemProvider } from '../../../../conversation/item/provider/BaseMessageItemProvider';
import { UiMessage } from '../../../../conversation/model/UiMessage';
export declare class RichContentMessageItemProvider extends BaseMessageItemProvider<RichContentMessage> {
    getMessageWrapBuilder(): WrappedBuilder<[
        Context,
        UiMessage,
        number
    ]>;
    isShowSummaryName(k198: Context, l198: RichContentMessage): boolean;
    getSummaryTextByMessageContent(g198: Context, h198: RichContentMessage): Promise<MutableStyledString>;
}
@Builder
export declare function bindImageMessageData(v197: Context, w197: UiMessage, x197: number): void;
@Component
export declare struct RichContentMessageView {
    @Require
    @Prop
    context: Context;
    @Require
    @Prop
    index: number;
    @ObjectLink
    uiMessage: UiMessage;
    private messageContent;
    @State
    imageWidth: number;
    @State
    imageHeight: number;
    aboutToAppear(): void;
    build(): void;
}
