export default class PermissionConstants {
    static LOG_TAG: string;
    static PERMISSION_CODE: number;
    static PERMISSION_CODE_IGNORE_BATTERY_OPTIMIZATIONS: number;
    static PERMISSION_CODE_MANAGE_EXTERNAL_STORAGE: number;
    static PERMISSION_CODE_SYSTEM_ALERT_WINDOW: number;
    static PERMISSION_CODE_REQUEST_INSTALL_PACKAGES: number;
    static PERMISSION_CODE_ACCESS_NOTIFICATION_POLICY: number;
    static PERMISSION_GROUP_CALENDAR: number;
    static PERMISSION_GROUP_CAMERA: number;
    static PERMISSION_GROUP_CONTACTS: number;
    static PERMISSION_GROUP_LOCATION: number;
    static PERMISSION_GROUP_LOCATION_ALWAYS: number;
    static PERMISSION_GROUP_LOCATION_WHEN_IN_USE: number;
    static PERMISSION_GROUP_MEDIA_LIBRARY: number;
    static PERMISSION_GROUP_MICROPHONE: number;
    static PERMISSION_GROUP_PHONE: number;
    static PERMISSION_GROUP_PHOTOS: number;
    static PERMISSION_GROUP_PHOTOS_ADD_ONLY: number;
    static PERMISSION_GROUP_REMINDERS: number;
    static PERMISSION_GROUP_SENSORS: number;
    static PERMISSION_GROUP_SMS: number;
    static PERMISSION_GROUP_SPEECH: number;
    static PERMISSION_GROUP_STORAGE: number;
    static PERMISSION_GROUP_IGNORE_BATTERY_OPTIMIZATIONS: number;
    static PERMISSION_GROUP_NOTIFICATION: number;
    static PERMISSION_GROUP_ACCESS_MEDIA_LOCATION: number;
    static PERMISSION_GROUP_ACTIVITY_RECOGNITION: number;
    static PERMISSION_GROUP_UNKNOWN: number;
    static PERMISSION_GROUP_BLUETOOTH: number;
    static PERMISSION_GROUP_MANAGE_EXTERNAL_STORAGE: number;
    static PERMISSION_GROUP_SYSTEM_ALERT_WINDOW: number;
    static PERMISSION_GROUP_REQUEST_INSTALL_PACKAGES: number;
    static PERMISSION_GROUP_APP_TRACK_TRANSPARENCY: number;
    static PERMISSION_GROUP_CRITICAL_ALERTS: number;
    static PERMISSION_GROUP_ACCESS_NOTIFICATION_POLICY: number;
    static PERMISSION_GROUP_BLUETOOTH_SCAN: number;
    static PERMISSION_GROUP_BLUETOOTH_ADVERTISE: number;
    static PERMISSION_GROUP_BLUETOOTH_CONNECT: number;
    static PERMISSION_GROUP_NEARBY_WIFI_DEVICES: number;
    static PERMISSION_GROUP_VIDEOS: number;
    static PERMISSION_GROUP_AUDIO: number;
    static PERMISSION_GROUP_SCHEDULE_EXACT_ALARM: number;
    static PERMISSION_GROUP_SENSORS_ALWAYS: number;
    static PERMISSION_STATUS_DENIED: number;
    static PERMISSION_STATUS_GRANTED: number;
    static PERMISSION_STATUS_RESTRICTED: number;
    static PERMISSION_STATUS_LIMITED: number;
    static PERMISSION_STATUS_NEVER_ASK_AGAIN: number;
    static SERVICE_STATUS_DISABLED: number;
    static SERVICE_STATUS_ENABLED: number;
    static SERVICE_STATUS_NOT_APPLICABLE: number;
}
