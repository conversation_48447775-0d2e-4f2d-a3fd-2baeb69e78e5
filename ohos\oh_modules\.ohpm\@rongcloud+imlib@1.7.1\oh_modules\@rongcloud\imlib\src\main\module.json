{"app": {"bundleName": "io.rong.testdemo", "versionCode": 1000000, "versionName": "1.0.0", "minAPIVersion": 50000012, "targetAPIVersion": 50000012, "apiReleaseType": "Release", "compileSdkVersion": "5.0.0.71", "compileSdkType": "HarmonyOS", "appEnvironments": [], "bundleType": "app"}, "module": {"name": "RongIMLib", "type": "har", "deviceTypes": ["default", "tablet", "2in1"], "requestPermissions": [{"name": "ohos.permission.GET_NETWORK_INFO", "reason": "$string:GET_NETWORK_INFO"}, {"name": "ohos.permission.INTERNET", "reason": "$string:INTERNET"}, {"name": "ohos.permission.STORE_PERSISTENT_DATA", "reason": "$string:STORE_PERSISTENT_DATA"}], "packageName": "@rongcloud/imlib", "installationFree": false, "virtualMachine": "ark12.0.2.0", "compileMode": "esmodule", "dependencies": []}}