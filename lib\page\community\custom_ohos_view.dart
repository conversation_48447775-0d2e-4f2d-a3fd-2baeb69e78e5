import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../api/community_api.dart';
import '../../models/community/aliyun_access_key.dart';
import '../../models/community/live_model.dart';
import '../../utils/manager/log_manager.dart';

typedef OnViewCreated = Function(CustomViewController);

///自定义OhosView
class CustomOhosView extends StatefulWidget {
  final OnViewCreated onViewCreated;

  const CustomOhosView(this.onViewCreated, {Key? key}) : super(key: key);

  @override
  State<CustomOhosView> createState() => _CustomOhosViewState();
}

class _CustomOhosViewState extends State<CustomOhosView> {
  late MethodChannel _channel;

  @override
  Widget build(BuildContext context) {
    return _getPlatformFaceView();
  }

  Widget _getPlatformFaceView() {
    return OhosView(
      viewType: 'com.rex.custom.ohos/customView',
      onPlatformViewCreated: _onPlatformViewCreated,
      creationParams: const <String, dynamic>{'initParams': 'hello world'},
      creationParamsCodec: const StandardMessageCodec(),
    );
  }

  void _onPlatformViewCreated(int id) {
    _channel = MethodChannel('com.rex.custom.ohos/customView$id');
    final controller = CustomViewController._(
      _channel,
    );
    widget.onViewCreated(controller);
  }
}

class CustomViewController {
  final MethodChannel _channel;
  final StreamController<String> _controller = StreamController<String>();

  // 存储获取到的所有videoID
  List<String> _pendingVideoIds = [];
  // 当前页码
  int _currentPage = 1;
  // 每页数量
  final int _pageSize = 10;
  // Live页面position参数
  int _lastPosition = 10;

  CustomViewController._(
    this._channel,
  ) {
    _channel.setMethodCallHandler(
      (call) async {
        switch (call.method) {
          case 'getMessageFromOhosView':
            // 从native端获取数据
            final result = call.arguments as String;
            _controller.sink.add(result);
            break;
        }
      },
    );
  }

  Stream<String> get customDataStream => _controller.stream;

  // 初始化视频队列（首次调用）
  Future<void> initVideoQueue(int position) async {
    _lastPosition = position;
    _currentPage = 1;
    _pendingVideoIds = [];

    // 获取第一页数据
    await _fetchVideosFromApi(position, _currentPage);
  }

  // 从接口获取数据并提取所有videoID
  Future<void> _fetchVideosFromApi(int position, int pageNo) async {
    try {
      LogManager().debug('开始获取直播数据，position: $position, 页码: $pageNo');

      // 从接口获取直播数据
      final liveModels =
          await communityAPI.getCommunityLiveData(position, pageNo, _pageSize);

      if (liveModels.isNotEmpty) {
        LogManager().debug('获取到${liveModels.length}条直播数据');

        // 遍历LiveModel列表，提取所有videoID
        for (var liveModel in liveModels) {
          String? videoId = _extractVideoId(liveModel);
          if (videoId != null && videoId.isNotEmpty) {
            _pendingVideoIds.add(videoId);
            LogManager().debug('找到videoId: $videoId, 当前队列长度: ${_pendingVideoIds.length}');
          }
        }
      } else {
        LogManager().debug('页码 $pageNo 未获取到直播数据');
      }
    } catch (e) {
      LogManager().debug('获取数据时出错: $e');
    }
  }

  // 发送下一个视频ID和阿里云配置到鸿蒙端
  Future<void> sendNextVideo() async {
    // 如果队列为空，获取更多数据
    if (_pendingVideoIds.isEmpty) {
      _currentPage++;
      await _fetchVideosFromApi(_lastPosition, _currentPage);
    }

    // 检查是否有可用的videoID
    if (_pendingVideoIds.isNotEmpty) {
      // 取出第一个videoID
      String videoId = _pendingVideoIds.removeAt(0);
      LogManager().debug('发送videoId: $videoId, 剩余队列长度: ${_pendingVideoIds.length}');

      try {
        // 获取最新的阿里云配置信息
        LogManager().debug('开始获取阿里云配置信息');
        AliyunAccessKey aliyun = await communityAPI.getAliyunAccessKey();
        LogManager().debug('获取到阿里云配置信息');

        // 发送到鸿蒙端
        LogManager().debug('发送数据到鸿蒙端');
        await _channel.invokeMethod('getMessageFromFlutterView', {
          'videoId': videoId,
          'accessKeyId': aliyun.accessKeyId,
          'accessKeySecret': aliyun.accessKeySecret,
          'securityToken': aliyun.securityToken
        });

        LogManager().debug('数据已发送到鸿蒙端');
      } catch (e) {
        LogManager().debug('获取配置或发送数据时出错: $e');
        // 将失败的videoID放回队列头部，以便下次重试
        _pendingVideoIds.insert(0, videoId);
      }
    } else {
      LogManager().debug('没有更多视频可发送');
    }
  }

  // 从LiveModel中提取videoId的辅助方法
  String? _extractVideoId(LiveModel liveModel) {
    // 检查firstImg是否包含videoId
    if (liveModel.firstImg != null &&
        liveModel.firstImg!.videoId != null &&
        liveModel.firstImg!.videoId!.isNotEmpty) {
      return liveModel.firstImg!.videoId;
    }

    // 检查imgTexts是否包含videoId
    if (liveModel.imgTexts != null && liveModel.imgTexts!.isNotEmpty) {
      for (var imgText in liveModel.imgTexts!) {
        if (imgText.videoId != null && imgText.videoId!.isNotEmpty) {
          return imgText.videoId;
        }
      }
    }

    return null;
  }

  Future<void> sendVideoToNative({
    required String videoId,
    required String accessKeyId,
    required String accessKeySecret,
    required String securityToken,
  }) async {
    try {
      LogManager().debug('直接发送videoId: $videoId 及STS信息到鸿蒙端');
      // 确保所有参数都非空
      if (videoId.isEmpty ||
          accessKeyId.isEmpty ||
          accessKeySecret.isEmpty ||
          securityToken.isEmpty) {
        LogManager().debug('Error: STS信息参数不完整');
        return;
      }

      await _channel.invokeMethod('getMessageFromFlutterView', {
        'videoId': videoId,
        'accessKeyId': accessKeyId,
        'accessKeySecret': accessKeySecret,
        'securityToken': securityToken
      });
      LogManager().debug('STS信息已成功发送到鸿蒙端');
    } catch (e) {
      LogManager().debug('发送STS信息时出错: $e');
      throw e;
    }
  }
}
