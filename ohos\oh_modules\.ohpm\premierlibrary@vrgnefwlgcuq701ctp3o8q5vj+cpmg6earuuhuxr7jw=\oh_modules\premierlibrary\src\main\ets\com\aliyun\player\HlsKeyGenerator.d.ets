import { OnKeyGenerateListener } from './IPlayer';
export declare class HlsKeyGenerator {
    private mOutKey?;
    private mOnKeyGenerateListener?;
    private getHlsOutKey;
    private static instance;
    static getInstance(): HlsKeyGenerator;
    static setOnKeyGenerateListener(i21: OnKeyGenerateListener): void;
    constructor();
    protected onHlsKeyInfoInit: Function;
    protected getHlsKey: Function;
    private getHlsKeyInternal;
}
