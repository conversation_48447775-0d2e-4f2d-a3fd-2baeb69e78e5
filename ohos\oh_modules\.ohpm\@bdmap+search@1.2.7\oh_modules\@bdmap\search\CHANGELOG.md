# Changelog
## [v1.0.0] 2024-03-22
### 变更
地图SDK发布
## [v1.0.1] 2024-05-22
### 变更
1. 搜索接口返回数据类型引入
2. BUG修复
## [v1.0.2] 2024-06-14
### 问题修复
1. POI多关键词检索返回数据异常问题修复
## [v1.1.0] 2024-07-04
### 变更
1. 路线规划相关接口更新: 新增RoutePlanSearch类, 用于封装路线规划相关方法, 相关字段更新
2. 新增推荐上车点，跨城公交路线规划接口
## [v1.1.2] 2024-07-17
### 问题修复
1. 修复混淆问题导致的部分接口异常
## [v1.2.0] 2024-10-25
1. 旧版本接口参数更新, 接口完全对齐安卓端
2. 新增AOI面检索、POI详情检索、室内POI检索、建筑物检索、行政区检索、天气检索
3. 新增路线分享接口
4. 构建字节码格式的HAR
## [v1.2.1] 2024-10-30
1. 修复了一些数据解析的BUG
## [v1.2.2] 2024-12-18
### 变更
1. 依赖库版本升级
## [v1.2.3] 2025-01-22
### 变更
1. 行政区检索问题修复
2. 驾车路线规划问题修复
3. 公交路线规划问题修复
4. SUG检索新增location字段
## [v1.2.4] 2025-02-12
1. 修复1.2.3包在DevEco Studio 5.0.2 Release中使用异常问题
## [v1.2.5] 2025-02-19
### 变更
1. 安全问题修复
2. 依赖库版本升级
## [v1.2.6] 2025-03-18
### 变更
1. 路线规划检索类RouteSearch新增可选参数useMultiThread，支持内部多线程请求
2. 修复全局坐标系问题
3. 修复跨城公交路线规划公交路线站点信息缺失问题
4. 依赖库版本升级
## [v1.2.7] 2025-05-08
### 变更
1. 公交路线规划vehicleInfo信息补全
2. POI检索label字段补全