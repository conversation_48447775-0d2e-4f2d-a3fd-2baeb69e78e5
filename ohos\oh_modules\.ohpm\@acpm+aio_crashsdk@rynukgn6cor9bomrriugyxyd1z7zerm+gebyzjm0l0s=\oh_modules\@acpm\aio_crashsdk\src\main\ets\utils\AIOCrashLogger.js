import hilog from '@ohos.hilog';
export class AIOCrashLogger {
    static setDebug(p6) {
        AIOCrashLogger.debugger = p6;
        if (p6) {
            AIOCrashLogger.info("AIOCrashLogger", "enable debug!");
        }
    }
    static debug(n6, o6) {
        if (AIOCrashLogger.debugger) {
            hilog.debug(0x0000, n6, o6);
        }
    }
    static info(l6, m6) {
        if (AIOCrashLogger.debugger) {
            hilog.info(0x0000, l6, m6);
        }
    }
    static warn(j6, k6) {
        if (AIOCrashLogger.debugger) {
            hilog.warn(0x0000, j6, k6);
        }
    }
    static error(h6, i6) {
        if (AIOCrashLogger.debugger) {
            hilog.error(0x0000, h6, i6);
        }
    }
}
AIOCrashLogger.debugger = false;
