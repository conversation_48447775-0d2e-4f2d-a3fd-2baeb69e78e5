// @keepTs
// @ts-nocheck
/**
 * Created on 2024/07/09
 * <AUTHOR>
 */
import { BaseConversationItemProvider } from '../../../../conversationlist/item/provider/BaseConversationItemProvider';
import { BaseUiConversation } from '../../../../conversationlist/model/BaseUiConversation';
export declare class PublicServiceConversationItemProvider extends BaseConversationItemProvider {
    getConversationWrapBuilder(): WrappedBuilder<[
        Context,
        BaseUiConversation,
        number
    ]>;
}
@Builder
export declare function bindPrivateConversationMessageData(o275: Context, p275: BaseUiConversation, q275: number): void;
@Component
export declare struct PublicServiceConversationItemView {
    @ObjectLink
    conversation: BaseUiConversation;
    build(): void;
}
