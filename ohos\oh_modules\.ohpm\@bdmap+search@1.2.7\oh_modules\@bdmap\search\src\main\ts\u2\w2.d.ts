/**
 * 参数构造器
 */
export interface ParamBuilder<T> {
    addParams(key: string, val: string | undefined | null): T;
    buildString(): string;
}
export declare class BaseParamBuilder implements ParamBuilder<BaseParamBuilder> {
    protected params: Map<string, string>;
    /**
     * @description 添加参数到BaseParamBuilder中
     * @param key {string} 参数名称
     * @param val {string} 参数值
     * @returns {BaseParamBuilder} BaseParamBuilder实例，用于链式调用
     */
    addParams(j35: string, k35: string | undefined | null): BaseParamBuilder;
    /**
     * 构建字符串，返回一个包含参数的 URL 字符串。如果参数为空或没有参数，则返回"空的"。
     * 每个参数使用"&"分隔，第一个参数不需要"&"。
     * @returns {string} 返回一个包含参数的 URL 字符串，如果参数为空或没有参数，则返回"空的"。
     */
    buildString(): string;
}
export declare function isEmpty(e35: object): boolean;
/**
 * 去除字符串里的htlm 标签
 *
 * @param src
 * @return
 */
export declare function trimHTMLMarker(d35: string): string;
