/**
 * 弱引用数组，多用与监听的多实例，减少循环引用
 * @version 1.0.0
 * @warning TS 对象的生命周期管理比 iOS Android 要弱很多，容易出现 WeakRef 过快销毁的情况，建议暂不使用该类。等后续有空研究明白再决定是否使用该类
 */
declare class WeakRefArray<T extends object> {
    private weakRefs;
    /**
     * 添加一个对象到数组里面
     * @param obj 对象
     */
    add(obj: T): void;
    /**
     * 从数组中移除一个对象
     * @param obj 对象
     */
    remove(obj: T): void;
    /**
     * 获取数组中的所有有效对象
     * @returns
     */
    getAll(): T[];
    /**
     * 清理已经被回收的对象
     */
    cleanup(): void;
}
