import { MessageContent } from '../MessageContent';
/**
 * 消息标识
 * @version 1.6.0
 */
declare const ReadReceiptMessageObjectName = "RC:ReadNtf";
/**
 * 已读回执消息
 * @version 1.6.0
 */
declare class ReadReceiptMessage extends MessageContent {
    /**
     * 最后一条消息的发送时间(毫秒)
     */
    lastMessageSendTime: number;
    /**
     * 最后一条消息的 uid（融云全局唯一 id）
     */
    messageUId: string;
    /**
     * 消息类型
     * 1: 发送最后一条消息的发送时间
     * 2: 发送最后一条消息的 uid
     */
    type: ReadReceiptType;
    /**
     * 无参构造方法
     */
    constructor();
    /**
     * 编码方法，将消息转为 json 字符串
     * @returns json 字符串
     */
    encode(): string;
    /**
     * 解码方法，将 json 字符串转为消息
     * @param contentString json 字符串
     */
    decode(contentString: string): void;
    /**
     * 获取类名
     * @returns 类名
     */
    getClassName(): string;
}
declare enum ReadReceiptType {
    Time = 1,
    Uid = 2
}
export { ReadReceiptMessage, ReadReceiptMessageObjectName, ReadReceiptType };
