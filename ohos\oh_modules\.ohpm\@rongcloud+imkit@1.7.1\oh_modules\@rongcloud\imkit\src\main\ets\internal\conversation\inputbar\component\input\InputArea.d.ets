// @keepTs
// @ts-nocheck
import { InputAreaController } from '../../InputAreaController';
@Component
export declare struct InputArea {
    private touchesY;
    private intervalId;
    private audioTapTs;
    private timeCount;
    private hasBeenSent;
    @State
    voiceTextOpacity: number;
    private GESTURE_DISTANCE;
    private readonly maxCount;
    @State
    count: number;
    @State
    mode: number;
    @Link
    inputAreaController: InputAreaController;
    @State
    dialogTxtBgColor: Resource;
    @State
    dialogCancelText: Resource;
    @State
    dialogImageRes: Resource;
    private messageClickListener;
    @State
    voiceInputTouchContent: string | Resource;
    private inputAreaComponentConfig;
    private audioDialog;
    private callStateChangeObserver;
    private RecallEditClickEventCallback;
    private AitChangeEventCallback;
    private AVRecorderEventCallback;
    private applicationStateChangeCallback;
    aboutToAppear(): void;
    aboutToDisappear(): void;
    private getComponentConfigVisibility;
    private getConfigContentBuilder;
    private getConfigComponent;
    build(): void;
    @Builder
    VoiceOrKeyboardComponent(): void;
    @Builder
    VoiceOrKeyboardButton(): void;
    @Builder
    ExpandTextAreaButton(): void;
    @Builder
    EmoticonButton(): void;
    @Builder
    SendOrPlusButton(): void;
    @Builder
    SendButton(): void;
    @Builder
    PluginButton(): void;
    @Builder
    TextAreaOrVoiceButton(): void;
    /**
     * 语音模式下，手指按下
     */
    private onTouchByDown;
    /**
     * 停止录音并发送消息
     * @param duration 录制时长
     * @param type Down 代表是开始录制时的定时器触发的发送；Up 代表是手指抬起触发的发送。
     */
    private stopRecordingAndSendMessage;
    /**
     * 销毁语音时长计时器并且重置秒数和id
     */
    private clearRecordTimeInterval;
    /**
     * 语音输入模式松开
     */
    private onTouchByUp;
    /**
     * 语音输入模式移动
     * @param event
     */
    private onTouchByMove;
    /**
     * 根据录音的振幅获取图片
     * @param maxAmplitude
     */
    private changeAudioImageResByMaxAmplitude;
    private onInsert;
    private updateMentionList;
    /**
     * 创建@信息
     */
    private createNewMention;
    private onWillDelete;
    private getMentionMap;
    private convertToMentionInfo;
    private isSpace;
    private isAlnum;
}
