import { MediaMessageContent } from '../MediaMessageContent';
/**
 * 消息标识
 * @version 1.2.0
 */
declare const SightMessageObjectName = "RC:SightMsg";
/**
 * 小视频消息
 * # 说明
 *```
 * 鸿蒙系统 mp4 格式的视频，可以播放，并保存到相册
 * avi/rmvb 格式的视频可以播放，但是无法保存到相册
 *```
 * @version 1.2.0
 * @warning 小视频时长受导航控制
 */
declare class SightMessage extends MediaMessageContent {
    /**
     * 视频缩略图 base64
     */
    base64: string;
    /**
     * 视频时长，单位秒
     */
    duration: number;
    /**
     * 视频大小
     */
    size: number;
    /**
     * 无参构造方法
     */
    constructor();
    /**
     * 编码方法，将消息转为 json 字符串
     * @returns json 字符串
     */
    encode(): string;
    /**
     * 解码方法，将 json 字符串转为消息
     * @param contentString json 字符串
     */
    decode(contentString: string): void;
    /**
     * 获取类名
     * @returns 类名
     */
    getClassName(): string;
}
export { SightMessage, SightMessageObjectName };
