import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import { HttpAuthHandlerFlutterApi, Reply } from './GeneratedOhosWebView';
import { InstanceManager } from './InstanceManager';
/**
 * Flutter API implementation for {@link HttpAuthHandler}.
 *
 * <p>This class may handle adding native instances that are attached to a Dart instance or passing
 * arguments of callbacks methods to a Dart instance.
 */
export declare class HttpAuthHandlerFlutterApiImpl {
    binaryMessenger: BinaryMessenger;
    instanceManager: InstanceManager;
    api: HttpAuthHandlerFlutterApi;
    /**
     * Constructs a {@link HttpAuthHandlerFlutterApiImpl}.
     *
     * @param binaryMessenger used to communicate with <PERSON><PERSON> over asynchronous messages
     * @param instanceManager maintains instances stored to communicate with attached Dart objects
     */
    constructor(binaryMessenger: BinaryMessenger, instanceManager: InstanceManager);
    /**
     * Stores the `HttpAuthHandler` instance and notifies <PERSON><PERSON> to create and store a new
     * `HttpAuthHandler` instance that is attached to this one. If `instance` has already been added,
     * this method does nothing.
     */
    create(instance: ESObject, callback: Reply<void>): void;
}
