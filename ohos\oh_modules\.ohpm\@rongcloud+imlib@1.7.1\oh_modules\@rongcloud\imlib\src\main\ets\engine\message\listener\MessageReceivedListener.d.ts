import { Message } from '../Message';
import { ReceivedInfo } from '../model/ReceivedInfo';
/**
 * 消息扩展监听
 * @version 1.3.0
 */
interface MessageReceivedListener {
    /**
     * 消息接收监听
     * @param message 消息体
     * @param info 消息接收信息
     */
    onMessageReceived(message: Message, info: ReceivedInfo): void;
    /**
     * 离线消息接收完成 远端消息同步完成回调，每次连接成功触发一次 远端没有消息的时候，连接成功后会立即触发
     * 远端有大量历史消息的时候，连接成功会等待消息接收完成之后触发
     */
    onOfflineMessageSyncCompleted(): void;
}
export { MessageReceivedListener };
