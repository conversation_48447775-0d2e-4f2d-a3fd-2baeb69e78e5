// @keepTs
// @ts-nocheck
/**
 * Created on 2024/07/09
 * <AUTHOR>
 */
import { SightMessage } from '@rongcloud/imlib';
import { BaseMessageItemProvider } from '../../../../conversation/item/provider/BaseMessageItemProvider';
import { UiMessage } from '../../../../conversation/model/UiMessage';
export declare class SightMessageItemProvider extends BaseMessageItemProvider<SightMessage> {
    getMessageWrapBuilder(): WrappedBuilder<[
        Context,
        UiMessage,
        number
    ]>;
    isShowSummaryName(r205: Context, s205: SightMessage): boolean;
    getSummaryTextByMessageContent(m205: Context, n205: SightMessage): Promise<MutableStyledString>;
    private isDestruct;
}
@Builder
export declare function bindImageMessageData(a205: Context, b205: UiMessage, c205: number): void;
@Component
export declare struct SightMessageView {
    @Require
    @Prop
    context: Context;
    @Require
    @Prop
    index: number;
    @ObjectLink
    uiMessage: UiMessage;
    @State
    imageWidth: number;
    @State
    imageHeight: number;
    private sightMessage;
    aboutToAppear(): void;
    build(): void;
    private convertSecondsToTime;
    private bubbleBorderRadius;
}
