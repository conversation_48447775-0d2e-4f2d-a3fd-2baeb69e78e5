import { MediaFormat } from './MediaFormat';
export declare class StsInfo {
    private mAccessKeyId;
    private mAccessKeySecret;
    private mSecurityToken;
    private mRegion;
    private mFormats;
    constructor();
    protected nativeGetAccessKeyId: Function;
    protected nativeSetAccessKeyId: Function;
    protected nativeGetAccessKeySecret: Function;
    protected nativeSetAccessKeySecret: Function;
    protected nativeGetSecurityToken: Function;
    protected nativeSetSecurityToken: Function;
    protected nativeGetRegion: Function;
    protected nativeSetRegion: Function;
    protected nativeGetFormatStr: Function;
    /**
     * 设置鉴权id
     *
     * @param accessKeyId 鉴权id
     */
    /****
     * Set the AccessKey ID for authentication.
     *
     * @param accessKeyId The AccessKey ID for authentication.
     */
    setAccessKeyId(v35: string): void;
    /**
     * 设置鉴权秘钥
     *
     * @param accessKeySecret 鉴权秘钥
     */
    /****
     * Set the AccessKey Secret for authentication.
     *
     * @param accessKeySecret The AccessKey Secret for authentication.
     */
    setAccessKeySecret(u35: string): void;
    /**
     * 设置安全token
     *
     * @param securityToken 安全token
     */
    /****
     * Set a token.
     *
     * @param securityToken The specified token.
     */
    setSecurityToken(t35: string): void;
    /**
     * 设置地域
     *
     * @param region 地域
     */
    /****
     * Specify regions.
     *
     * @param region The specified regions.
     */
    setRegion(s35: string): void;
    /**
     * 获取鉴权id
     *
     * @return 鉴权id
     */
    /****
     * Query the AccessKey ID for authentication.
     *
     * @return The AccessKey ID for authentication.
     */
    getAccessKeyId(): string;
    /**
     * 获取鉴权秘钥
     *
     * @return 鉴权秘钥
     */
    /****
     * Query the AccessKey Secret for authentication.
     *
     * @return The AccessKey Secret for authentication.
     */
    getAccessKeySecret(): string;
    /**
     * 获取安全token
     *
     * @return 安全token
     */
    /****
     * Query the token.
     *
     * @return The token.
     */
    getSecurityToken(): string;
    /**
     * 获取地域
     *
     * @return 地域
     */
    /****
     * Query region information.
     *
     * @return The region information.
     */
    getRegion(): string;
    /**
     * 获取服务器返回的格式
     * @return 服务器返回的格式
     */
    /****
     * Query the formats of the returned data.
     * @return The formats of the returned data.
     */
    getFormats(): MediaFormat[];
    /**
     * 设置服务器返回的格式。可以不设置。默认为 mp4|m3u8|mp3|flv
     * @param formats 服务器返回的格式
     */
    /****
     * Set the formats of the returned data. This parameter is optional. Default: mp4|m3u8|mp3|flv.
     * @param formats The formats of the returned data.
     */
    setFormats(r35: MediaFormat[]): void;
    getFormatStr(): string;
}
