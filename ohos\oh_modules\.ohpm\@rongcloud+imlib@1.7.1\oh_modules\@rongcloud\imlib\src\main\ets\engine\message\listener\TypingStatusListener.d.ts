import { ConversationIdentifier } from '../../conversation/ConversationIdentifier';
import { List } from '@kit.ArkTS';
import { TypingStatus } from '../model/TypingStatus';
/**
 * 输入状态监听
 * @version 1.3.0
 */
interface TypingStatusListener {
    /**
     * 输入状态发生变化
     * @param conId 会话标识
     * @param typingStatusList 输入状态变更列表
     */
    onTypingStatusChange(conId: ConversationIdentifier, typingStatusList: List<TypingStatus>): any;
}
export { TypingStatusListener };
