import { DVModelParameters } from '@ohos/flutter_ohos/src/main/ets/view/DynamicView/dynamicView';
export declare class DynamicUtils {
    static getParams(params: DVModelParameters, element: string): string | ESObject;
    static setParams(params: DVModelParameters, key: string, element: ESObject): void;
}
export declare class DVModelJson {
    compType: string;
    children: Array<ESObject>;
    attributes: ESObject;
    events: ESObject;
    build: ESObject;
    constructor(compType: string, children: Array<ESObject>, attributes: ESObject, events: ESObject, build?: ESObject);
}
