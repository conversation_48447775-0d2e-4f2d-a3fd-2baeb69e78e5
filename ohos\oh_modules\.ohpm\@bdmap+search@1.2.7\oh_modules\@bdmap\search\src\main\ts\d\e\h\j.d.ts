import type { LatLng } from '@bdmap/base';
import type { SearchResult } from "./f1";
/**
 * 推荐上车点查询请求参数
 */
export interface RecommendStopSearchOption {
    /**
     * 检索周边推荐上车点的位置坐标
     */
    location: LatLng;
    /**
     * 是否需要场站上车点推荐
     * 默认false
     */
    isNeedStationInfo?: boolean;
}
/**
 * 推荐上车点检索结果
 */
export interface RecommendStopResult extends SearchResult {
    /**
     * 场站上车点信息列表
     */
    stationInfoList?: RecommendStationInfo[];
    /**
     * 推荐上车点信息列表
     */
    recommendStopInfoList?: RecommendStopInfo[];
}
/**
 * 场站上车点信息
 */
export interface RecommendStationInfo {
    /**
     * 场站名字
     */
    stationName?: string;
    /**
     * 场站推荐上车点信息列表
     */
    recommendStops?: RecommendStopInfo[];
}
/**
 * 推荐上车点信息
 */
export interface RecommendStopInfo {
    /**
     * 推荐上车点名称
     */
    name: string;
    /**
     * 该推荐上车点对应的BD09坐标经度
     */
    bd09ll_x: number;
    /**
     * 该推荐上车点对应的BD09坐标纬度
     */
    bd09ll_y: number;
    /**
     * 该推荐上车点对应的GCJ02坐标经度
     */
    gcj02ll_x: number;
    /**
     * 该推荐上车点对应的GCJ02坐标纬度
     */
    gcj02ll_y: number;
    /**
     * 该推荐上车点距离查找点的距离
     */
    distance: number;
    /**
     * 该推荐上车点的poi的uid
     */
    id: string;
    /**
     * 该推荐上车点对应的详细地址信息
     */
    address: string;
}
