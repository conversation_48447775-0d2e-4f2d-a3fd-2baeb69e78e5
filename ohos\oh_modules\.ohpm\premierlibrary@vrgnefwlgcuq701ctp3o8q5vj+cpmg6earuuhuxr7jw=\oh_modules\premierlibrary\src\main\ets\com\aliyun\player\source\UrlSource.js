import { SourceBase } from './SourceBase';
export class UrlSource extends SourceBase {
    constructor() {
        super();
        this.nativeGetUri = () => {
            return this.mUri;
        };
        this.nativeSetUri_ = (y36) => {
            this.mUri = y36;
        };
        this.nativeGetExtraInfo = () => {
            return this.mExtraInfo;
        };
        this.nativeSetExtraInfo = (x36) => {
            this.mExtraInfo = x36;
        };
        this.nativeGetCacheFilePath = () => {
            return this.mCacheFilePath;
        };
        this.nativeSetCacheFilePath = (w36) => {
            this.mCacheFilePath = w36;
        };
        this.nativeGetOriginSize = () => {
            return this.mOriginSize;
        };
        this.nativeSetOriginSize = (v36) => {
            this.mOriginSize = v36;
        };
        this.mUri = "";
        this.mCacheFilePath = "";
        this.mQuality = "AUTO";
        this.mForceQuality = true;
        this.mOriginSize = 0;
        this.mExtraInfo = "";
    }
    getUri() {
        return this.mUri;
    }
    getExtraInfo() {
        return this.mExtraInfo;
    }
    setExtraInfo(m36) {
        this.mExtraInfo = m36;
    }
    setUri(l36) {
        this.mUri = l36;
    }
    getCacheFilePath() {
        return this.mCacheFilePath;
    }
    setCacheFilePath(k36) {
        this.mCacheFilePath = k36;
    }
    getOriginSize() {
        return this.mOriginSize;
    }
    setOriginSize(j36) {
        this.mOriginSize = j36;
    }
}
