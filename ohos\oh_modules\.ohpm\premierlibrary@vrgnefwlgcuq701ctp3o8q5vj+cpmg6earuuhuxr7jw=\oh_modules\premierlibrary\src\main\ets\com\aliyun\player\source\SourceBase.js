export class SourceBase {
    constructor() {
        this.mCoverPath = "";
        this.mTitle = "";
        this.mQuality = "";
        this.mForceQuality = false;
        this.nativeGetCoverPath = () => {
            return this.mCoverPath;
        };
        this.nativeSetCoverPath = (o35) => {
            this.mCoverPath = o35;
        };
        this.nativeGetTitle = () => {
            return this.mTitle;
        };
        this.nativeSetTitle = (n35) => {
            this.mTitle = n35;
        };
        this.nativeGetQuality = () => {
            return this.mQuality;
        };
        this.nativeIsForceQuality = () => {
            return this.mForceQuality;
        };
    }
    getCoverPath() {
        return this.mCoverPath;
    }
    setCoverPath(g35) {
        this.mCoverPath = g35;
    }
    getTitle() {
        return this.mTitle;
    }
    setTitle(f35) {
        this.mTitle = f35;
    }
    getQuality() {
        return this.mQuality;
    }
    isForceQuality() {
        return this.mForceQuality;
    }
}
