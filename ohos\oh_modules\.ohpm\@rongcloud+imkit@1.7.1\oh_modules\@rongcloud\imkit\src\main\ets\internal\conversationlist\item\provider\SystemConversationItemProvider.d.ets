// @keepTs
// @ts-nocheck
/**
 * Created on 2024/07/09
 * <AUTHOR>
 */
import { BaseConversationItemProvider } from '../../../../conversationlist/item/provider/BaseConversationItemProvider';
import { BaseUiConversation } from '../../../../conversationlist/model/BaseUiConversation';
export declare class SystemConversationItemProvider extends BaseConversationItemProvider {
    getConversationWrapBuilder(): WrappedBuilder<[
        Context,
        BaseUiConversation,
        number
    ]>;
}
@Builder
export declare function bindPrivateConversationMessageData(q276: Context, r276: BaseUiConversation, s276: number): void;
@Component
export declare struct SystemConversationItemView {
    @ObjectLink
    conversation: BaseUiConversation;
    build(): void;
}
