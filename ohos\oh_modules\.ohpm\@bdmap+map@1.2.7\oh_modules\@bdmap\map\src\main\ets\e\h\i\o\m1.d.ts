import { LatLng } from '@bdmap/base'; import type { Nullable } from "../../g1/a2";             export default class LatLngBound { private west; private south; private east; private north; sw: Nullable<LatLng>; ne: Nullable<LatLng>; constructor(n34?: LatLng, o34?: LatLng); setBox(l34?: LatLng, m34?: LatLng): void;             contains(point: Nullable<LatLng>): boolean;           getCenter(): LatLng; destroy(): void; toString(): string; } 