// @keepTs
// @ts-nocheck
/**
 * 路由管理
 * Created on 2024/10/21
 * <AUTHOR>
 * @version 1.0.0
 */
import { router } from '@kit.ArkUI';
import { RouterModel } from './model/RouterModel';
export declare class RouterManager {
    routerMap: Map<string, NavPathStack>;
    private static instance;
    private constructor();
    static getInstance(): RouterManager;
    getNavPathStack(h329: string): NavPathStack;
    navPush(c329: string, d329: RouterModel, e329?: (obj: PopInfo) => void, f329?: NavigationOptions): void;
    navReplace(z328: string, a329: RouterModel): void;
    navPop(w328: string, x328?: RouterModel): void;
    /**
     * Navi 的生命周期结束，常用于 Page 结束时
     */
    navFinish(u328: string): void;
    navClear(): void;
    routerPush(q328: RouterModel, r328?: router.RouterMode): Promise<void>;
    routerReplace(o328: RouterModel, p328?: router.RouterMode): Promise<void>;
    routerClear(): void;
    routerBack(n328?: RouterModel): void;
}
