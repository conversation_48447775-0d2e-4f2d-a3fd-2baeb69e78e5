import { SourceBase } from "./SourceBase";
export class LiveSts extends SourceBase {
    constructor() {
        super();
        this.nativeGetEncryptionTypeValue = () => {
            return this.mEncryptionType;
        };
        this.nativeSetEncryptionType = (z34) => {
            this.mEncryptionType = z34;
        };
        this.nativeGetSecurityToken = () => {
            return this.mSecurityToken;
        };
        this.nativeSetSecurityToken = (y34) => {
            this.mSecurityToken = y34;
        };
        this.nativeGetAccessKeyId = () => {
            return this.mAccessKeyId;
        };
        this.nativeSetAccessKeyId = (x34) => {
            this.mAccessKeyId = x34;
        };
        this.nativeGetAccessKeySecret = () => {
            return this.mAccessKeySecret;
        };
        this.nativeSetAccessKeySecret = (w34) => {
            this.mAccessKeySecret = w34;
        };
        this.nativeGetRegion = () => {
            return this.mRegion;
        };
        this.nativeSetRegion = (v34) => {
            this.mRegion = v34;
        };
        this.nativeGetUrl = () => {
            return this.mUrl;
        };
        this.nativeSetUrl = (u34) => {
            this.mUrl = u34;
        };
        this.nativeGetDomain = () => {
            return this.mDomain;
        };
        this.nativeSetDomain = (t34) => {
            this.mDomain = t34;
        };
        this.nativeGetApp = () => {
            return this.mApp;
        };
        this.nativeSetApp = (s34) => {
            this.mApp = s34;
        };
        this.nativeGetStream = () => {
            return this.mStream;
        };
        this.nativeSetStream = (r34) => {
            this.mStream = r34;
        };
        this.mAccessKeyId = "";
        this.mAccessKeySecret = "";
        this.mSecurityToken = "";
        this.mRegion = "";
        this.mUrl = "";
        this.mDomain = "";
        this.mApp = "";
        this.mStream = "";
        this.mEncryptionType = LiveEncryptionType.NoEncryption;
    }
    getEncryptionTypeValue() {
        return this.mEncryptionType;
    }
    setEncryptionType(y33) {
        this.mEncryptionType = y33;
    }
    getSecurityToken() {
        return this.mSecurityToken;
    }
    setSecurityToken(x33) {
        this.mSecurityToken = x33;
    }
    getAccessKeyId() {
        return this.mAccessKeyId;
    }
    setAccessKeyId(w33) {
        this.mAccessKeyId = w33;
    }
    getAccessKeySecret() {
        return this.mAccessKeySecret;
    }
    setAccessKeySecret(v33) {
        this.mAccessKeySecret = v33;
    }
    getRegion() {
        return this.mRegion;
    }
    setRegion(u33) {
        this.mRegion = u33;
    }
    getUrl() {
        return this.mUrl;
    }
    setUrl(t33) {
        this.mUrl = t33;
    }
    getDomain() {
        return this.mDomain;
    }
    setDomain(s33) {
        this.mDomain = s33;
    }
    getApp() {
        return this.mApp;
    }
    setApp(r33) {
        return this.mApp = r33;
    }
    getStream() {
        return this.mStream;
    }
    setStream(q33) {
        this.mStream = q33;
    }
}
export var LiveEncryptionType;
(function (p33) {
    p33[p33["NoEncryption"] = 0] = "NoEncryption";
    p33[p33["AliEncryption"] = 1] = "AliEncryption";
    p33[p33["WideVine_FairPlay"] = 2] = "WideVine_FairPlay";
})(LiveEncryptionType || (LiveEncryptionType = {}));
