import { Result } from './GeneratedOhosWebView';
import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import { InstanceManager } from './InstanceManager';
import { CookieManagerHostApi } from './GeneratedOhosWebView';
export declare class CookieManagerHostApiImpl extends CookieManagerHostApi {
    private binaryMessenger;
    private instanceManager;
    constructor(binaryMessenger: BinaryMessenger, instanceManager: InstanceManager);
    private getCookieManagerInstance;
    attachInstance(instanceIdentifier: number): void;
    setCookie(identifier: number, url: string, value: string): void;
    setAcceptThirdPartyCookies(identifier: number, accept: boolean): void;
    removeAllCookies(identifier: number, result: Result<boolean>): void;
}
export interface cookieManager {
    removeAllCookies(identifier: number, result: Result<boolean> | null): void;
    putAcceptThirdPartyCookieEnabled(accept: boolean): void;
    setCookie(url: string, value: string): void;
}
