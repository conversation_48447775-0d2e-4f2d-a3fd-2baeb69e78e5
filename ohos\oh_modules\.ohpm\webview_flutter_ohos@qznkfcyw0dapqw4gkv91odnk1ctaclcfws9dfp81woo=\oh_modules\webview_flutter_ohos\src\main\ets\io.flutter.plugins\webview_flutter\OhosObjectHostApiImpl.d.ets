import { OhosObjectHostApi as OhosObjectHostApi } from './GeneratedOhosWebView';
import { InstanceManager } from './InstanceManager';
export declare class OhosObjectHostApiImpl extends OhosObjectHostApi {
    private instanceManager;
    /**
     * Constructs a {@link OhosObjectHostApiImpl}.
     *
     * @param instanceManager maintains instances stored to communicate with Dart objects
     */
    constructor(instanceManager: InstanceManager);
    dispose(identifier: number): void;
}
