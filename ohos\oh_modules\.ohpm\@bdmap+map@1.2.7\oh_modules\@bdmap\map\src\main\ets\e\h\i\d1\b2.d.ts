import { Bounds } from '@bdmap/base'; import type p29 from "../j"; import Bundle from "../o/i1"; import BaseLayer from "./j1"; import type TileProvider from "./j4";         export default abstract class TileLayer extends BaseLayer { private mBundle; private maxTileTmp; private maxDisplay; private minDisplay; private initRectr; private initRectb; private initRectl; private initRectt; protected provider: TileProvider; protected datasource: number; protected urlString: string; constructor(a30: string, b30: TileProvider); private setLayerId;       setBaiduMap(z29: p29): void; getLayerId(): string; getUpdateType(): number; getTimerEscape(): number; setUrlString(url: string): void; getUrlString(str: string): string; protected setDatasource(type: number): void; setMaxTileTmp(y29: number): void; setSourceRegion(u29: Bounds): void; getSourceRegion(): Bounds; setTileProvide(s29: TileProvider): void; setDisplayLevel(q29: number, r29: number): void; update(): void; toBundle(): Bundle; } 