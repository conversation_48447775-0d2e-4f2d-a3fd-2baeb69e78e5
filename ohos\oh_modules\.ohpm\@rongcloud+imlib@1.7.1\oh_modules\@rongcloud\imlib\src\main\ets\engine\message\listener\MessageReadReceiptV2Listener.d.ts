import { ConversationIdentifier } from '../../conversation/ConversationIdentifier';
/**
 * 消息已读 V2 监听，只支持群聊
 * @version 1.4.0
 */
export interface MessageReadReceiptV2Listener {
    /**
     * 群组中收到了其他人发送的响应
     * @param conId 会话标识
     * @param messageUid 请求已读响应的消息 UId
     * @param respondUserIdList 响应了此消息的用户列表，key 用户 Id， value 响应时间（毫秒时间戳）
     */
    onMessageReceiptResponse(conId: ConversationIdentifier, messageUid: string, readCount: number, totalCount: number): any;
}
