import { UrlVideoPlayer } from './UrlVideoPlayer';
import { OhosSaasPlayer } from './nativeclass/OhosSaasPlayer';
import { LiveSts } from './source/LiveSts';
import { VidAuth } from './source/VidAuth';
import { VidMps } from './source/VidMps';
import { VidSts } from './source/VidSts';
import { StsInfo } from './source/StsInfo';
export class ApsaraVideoPlayer extends UrlVideoPlayer {
    constructor(e11, f11) {
        super(e11, f11);
    }
    setVidAuthDataSource(c11) {
        let d11 = this.getCorePlayer();
        if (d11 instanceof OhosSaasPlayer) {
            if (c11 instanceof VidAuth) {
                d11.setVidAuthSource(c11);
            }
        }
    }
    setVidStsDataSource(a11) {
        let b11 = this.getCorePlayer();
        if (b11 instanceof OhosSaasPlayer) {
            if (a11 instanceof VidSts) {
                b11.setVidStsSource(a11);
            }
        }
    }
    setVidMpsDataSource(y10) {
        let z10 = this.getCorePlayer();
        if (z10 instanceof OhosSaasPlayer) {
            if (y10 instanceof VidMps) {
                z10.setVidMpsSource(y10);
            }
        }
    }
    setLiveStsDataSource(w10) {
        let x10 = this.getCorePlayer();
        if (x10 instanceof OhosSaasPlayer) {
            if (w10 instanceof LiveSts) {
                x10.setLiveStsDataSource(w10);
            }
        }
    }
    updateVidAuth(u10) {
        let v10 = this.getCorePlayer();
        if (v10 instanceof OhosSaasPlayer) {
            if (u10 instanceof VidAuth) {
                v10.updateVidAuth(u10);
            }
        }
    }
    updateStsInfo(s10) {
        let t10 = this.getCorePlayer();
        if (t10 instanceof OhosSaasPlayer) {
            if (s10 instanceof StsInfo) {
                t10.updateStsInfo(s10);
            }
        }
    }
    createAlivcMediaPlayer(q10) {
        let r10 = new OhosSaasPlayer(q10);
        return r10;
    }
}
