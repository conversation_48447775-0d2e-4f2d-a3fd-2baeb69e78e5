// @keepTs
// @ts-nocheck
import { ConversationType } from '@rongcloud/imlib';
import { ItemProvider } from '../../base/item/provider/ItemProvider';
/**
 * 会话列表 item 用会话类型
 * 消息 item 用 ObjectName
 */
type ItemProviderKey = ConversationType | string;
/**
 * 展示列表内容的组件提供者缓存
 * 会话列表 item ，消息 item 都缓存再各自的 ServiceImpl 中
 */
export declare class ItemProviderCache<T extends object> {
    private providerMap;
    constructor();
    addItemProvider(c54: ItemProviderKey, d54: ItemProvider<T>): void;
    removeItemProvider(b54: ItemProviderKey): void;
    hasKey(a54: ItemProviderKey): boolean;
    getItemProvider(y53: ItemProviderKey): ItemProvider<T>;
}
export {};
