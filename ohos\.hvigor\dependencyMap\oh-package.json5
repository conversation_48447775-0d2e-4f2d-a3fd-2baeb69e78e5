{"modelVersion": "5.0.0", "name": "apptemplate", "version": "1.0.0", "description": "Please describe the basic information.", "main": "", "author": "", "license": "", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har", "@tencent/wechat_open_sdk": "1.0.14", "@free/global": "1.0.3", "@rongcloud/imkit": "^1.5.0", "@rongcloud/imlib": "^1.5.0", "@ohos/mqtt": "^2.0.22"}, "devDependencies": {"@ohos/hypium": "1.0.18"}, "overrides": {"@ohos/flutter_ohos": "file:./har/flutter.har", "shared_preferences_ohos": "file:./har/shared_preferences_ohos.har", "url_launcher_ohos": "file:./har/url_launcher_ohos.har", "webview_flutter_ohos": "file:./har/webview_flutter_ohos.har", "path_provider_ohos": "file:./har/path_provider_ohos.har", "@ohos/flutter_module": "file:./entry", "image_picker_ohos": "file:./har/image_picker_ohos.har", "permission_handler_ohos": "file:./har/permission_handler_ohos.har", "device_info_plus": "file:./har/device_info_plus.har", "package_info_plus": "file:./har/package_info_plus.har", "flutter_blue_plus_ohos": "file:./har/flutter_blue_plus_ohos.har", "iamgeqr_flutter_plugin": "file:./har/iamgeqr_flutter_plugin.har", "connectivity_plus": "file:./har/connectivity_plus.har", "mobile_scanner": "file:./har/mobile_scanner.har", "fluwx": "file:./har/fluwx.har", "open_app_settings": "file:./har/open_app_settings.har", "flutter_blue_plus": "file:./har/flutter_blue_plus.har", "camera_ohos": "file:./har/camera_ohos.har", "video_compress": "file:./har/video_compress.har"}, "dynamicDependencies": {}}