/**
 * @file 日期工具
 */
export default class DateTimeUtil {
    /**
     * 时分秒
     */
    getTime(): string;
    /**
     * 年月日
     */
    getDate(): string;
    /**
     * 日期不足两位补充0
     * @param value-数据值
     */
    fill(value: number): string;
    /**
     * 年月日格式修饰
     * @param year
     * @param month
     * @param date
     */
    concatDate(year: number, month: number, date: number): string;
    /**
     * Avoid repetition
     * 时分秒格式修饰
     * @param hours
     * @param minutes
     * @param seconds
     */
    concatTime(hours: number, minutes: number, seconds: number): string;
}
