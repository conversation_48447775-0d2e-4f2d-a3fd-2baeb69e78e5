            import ImageEntity from "../o/s"; import InfoWindow from "./u"; import { AnimateDefine } from "../../util/b1/c1"; import Bundle from "../o/i1"; import { LatLng } from '@bdmap/base'; import type { ColorString, IMarkerOption, Nullable } from "../../g1/a2"; import type OverlayMgr from "./j2"; import type { ImageOverlayData } from "../../g1/i1"; import type BmBitmapResource from "../c2/f2/t3"; import BaseMarker from "./r4";             export default class Marker extends BaseMarker { private mAnimateType; private mIcons; private mInfoWindow; private bmIconMarker; private bitmap; private mColor; private mDelPreIconResource;                                                   constructor(a39?: Nullable<IMarkerOption>);       init(): void;         changePosition(position: LatLng): void;           icons(icons: Array<ImageEntity>): this;         icon(z38: ImageEntity): this;       delPreIconResource(val: boolean): void; color(color: ColorString): void; setColor(color: ColorString): void; getColor(): any;       updateBmIcon(icon: ImageEntity): void;       updateBitMap(bitmap: BmBitmapResource): void;         getIcon(): ImageEntity;         setIcon(y38: ImageEntity): void;         getIcons(): ImageEntity[];           setIcons(icons: Array<ImageEntity>): void;           animateType(type: Nullable<AnimateDefine>): this;         getAnimateType(): AnimateDefine;         setAnimateType(type: Nullable<AnimateDefine>): void;         getInfoWindow(): InfoWindow;         infoWindow(info: Nullable<InfoWindow>): this;         setInfoWindow(x38: InfoWindow): void;         showInfoWindow(w38?: InfoWindow): void;         hideInfoWindow(): void;         get typeName(): string;       dataFormat(v38: Array<ImageOverlayData>): Nullable<Bundle>;       toString(): string;           toBundle(q38: OverlayMgr): Promise<Bundle>; } 