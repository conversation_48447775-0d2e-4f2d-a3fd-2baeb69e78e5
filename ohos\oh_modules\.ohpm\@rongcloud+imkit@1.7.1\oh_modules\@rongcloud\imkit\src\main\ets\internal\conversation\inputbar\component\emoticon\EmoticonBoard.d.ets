// @keepTs
// @ts-nocheck
import { InputAreaController } from '../../InputAreaController';
@Component
export declare struct EmoticonBoard {
    @Link
    inputAreaController: InputAreaController;
    @State
    private emoticonTabList;
    @State
    private currentIndex;
    private swiperController;
    private emoticonTabScroller;
    private inputAreaComponentConfig;
    private EmoticonBoardTabEventCallback;
    private EmoticonBoardEventCallback;
    aboutToAppear(): void;
    private updateEmoticonTabList;
    aboutToDisappear(): void;
    private getConfigContentBuilder;
    private getConfigComponent;
    build(): void;
}
