// @keepTs
// @ts-nocheck
import { ConversationType, Message } from '@rongcloud/imlib';
import { CombineMessage } from '../../message/content/CombineMessage';
export declare class InnerForWardServiceImpl {
    private JSON_FILE_NAME;
    private BASE64_PRE;
    private NO_USER;
    private TAG_BASE_HEAD;
    private TAG_TIME;
    private TAG_TXT;
    private TAG_GIF;
    private TAG_VC;
    private TAG_HQVC;
    private TAG_CARD;
    private TAG_STK;
    private TAG_IMG_TEXT;
    private TAG_SIGHT;
    private TAG_IMG;
    private TAG_COMBINE;
    private TAG_MSG_COMBINE_BODY;
    private TAG_FILE;
    private TAG_LBS;
    private TAG_VCSUMMARY;
    private TAG_VST;
    private TAG_RP;
    private TAG_BASE_BOTTOM;
    private MSG_BASE_HEAD_STYLE;
    private MSG_TIME;
    private MSG_SHOW_USER;
    private MSG_PORTRAIT;
    private MSG_USER_NAME;
    private MSG_SEND_TIME;
    private MSG_TEXT;
    private MSG_IMAG_URL;
    private MSG_FILE_NAME;
    private MSG_SIZE;
    private MSG_FILE_SIZE;
    private MSG_FILE_URL;
    private MSG_GIF_CONTENT;
    private MSG_GIF_DISPLAY;
    private MSG_GIF_CONTENT_DISPLAY;
    private MSG_FILE_TYPE;
    private MSG_FILE_ICON;
    private MSG_TITLE;
    private MSG_COMBINE_BODY;
    private MSG_FOOT;
    private MSG_LOCATION_NAME;
    private MSG_LATITUDE;
    private MSG_LONGITUDE;
    private MSG_IMAGE_BASE64;
    private MSG_DURATION;
    private MSG_UID;
    private localHtmlTags;
    private localHtmlCache;
    private combineInfos;
    private combinePortraits;
    private combineMessageSummary;
    private combineFileTypeBase64;
    /**
     * 根据消息列表，生成合并转发消息Html路径，也就是 localPath
     */
    private getLocalCombineUrl;
    /**
     * 生成合并转发消息 CombineMessage 的 nameList 字段
     */
    private getNameList;
    /**
     * 生成合并转发消息 CombineMessage 的 summaryList 字段
     */
    private getSummaryList;
    /**
     * 初始化合并转发的Html模版替换生成过程中的一些资源
     */
    private initLocalHtmlCache;
    /**
     * 合并转发的初始Html模版，在本地`src/main/resources/rawfile/combine.json`
     */
    private getLocalCombineHtmlJsonString;
    /**
     * 本地`combine.json`转化为Json，方便后续替换
     */
    private getLocalCombineHtmlJSON;
    /**
     * 根据消息类型，查找对应的Html标签，这样方便替换
     */
    private getHtmlFromType;
    /**
     * 合并转发Html替换生成
     */
    private getHtmlFromMessageList;
    /**
     * 合并转发Html的头部
     */
    private getHtmlBaseHead;
    /**
     * 合并转发Html的时间
     */
    private getHtmlTime;
    /**
     * 辅助函数：根据指定格式格式化日期
     */
    private formatDate;
    /**
     * 合并转发Html替换每个消息体对应的消息内容
     * 注意：跨页面透传数据，Message的content的类型均为变为object，允许通过 as 转换成对应消息来获取属性，但不能调用方法。
     */
    private getHtmlFromMessageContent;
    /**
     * 缓存UserInfo、头像、MessageSummary等。
     * 其中：相邻Message的senderId相同，则只展示第1个Message的senderId的 头像。
     * @param forwardMessages
     */
    private prepareCache;
    /**
     * 合并转发Html替换每个消息体对应的用户消息，包括头像、用户名、时间。
     */
    private setUserInfo;
    /**
     * 合并转发Html替换每个消息体中的用户消息：头像
     */
    private getUserPortrait;
    /**
     * 根据提供的地址，获取HTML可以加载的路径或者本地base64
     */
    private getUserPortraitData;
    private getFixedBase64;
    /**
     * 合并转发Html替换每个消息体中的用户消息：用户名
     * 注意：如果没有从UserDataService取得用户信息，则用户名返回targetID。
     */
    private getUserName;
    /**
     * 合并转发Html替换每个消息体中的用户消息：时间
     */
    private getSendTime;
    /**
     * 合并转发Html的底部数据
     */
    private getHtmlBaseBottom;
    /**
     * 获取缓存路径
     */
    private getCachePath;
    /**
     * 将处理后的合并转发html文件保存在本地，方便后续上传
     */
    private writeHtmlToSandbox;
    /**
     * 通过资源ID获取图片资源并转换为Base64
     * @param resourceId 图片资源的ID
     */
    private getIconAsBase64;
    /**
     * 获取默认文件后缀base64
     */
    private getDefaultIconAsBase64;
    /**
     * 清理合并转发的缓存
     */
    private clearCache;
    /**
     * 构建合并转发消息
     * @param forwardMessages 用来构建合并转发消息的消息数组
     * @returns 返回 CombineMessage，如果返回 undefined 则代表构建失败。
     */
    obtainCombineMessage(a298: Message[], b298?: ObtainCombineMessageAttribute): Promise<CombineMessage | undefined>;
    /**
     * 获取当前消息类型需要在会话列表展示的数据
     */
    getMessageSummary(w297: Message): Promise<string>;
    /**
     * 生成合并转发消息 CombineMessage 的 title 字段
     */
    getTitle(u297: ConversationType, v297: Array<string>): string;
}
export interface ObtainCombineMessageAttribute {
    unsupportedMessageHandler?: (message: Message) => Promise<string>;
}
