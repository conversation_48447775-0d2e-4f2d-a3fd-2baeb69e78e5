/**
 * 自定义媒体消息上传 & 下载接口
 * @version 1.4.0
 */
declare class MediaMessageTransfer {
    constructor();
    /**
     * 自定义媒体消息上传 & 下载成功
     * @param path 上传时为文件存储的远端地址，下载时为文件的本地路径
     */
    success(path: string): void;
    /**
     * 自定义媒体消息上传 & 下载失败
     */
    error(): void;
    /**
     * 取消自定义媒体消息上传 & 下载
     */
    cancel(): void;
    /**
     * 更新自定义上传 & 下载进度
     * @param progress 上传 & 下载进度，取值范围：[0, 100]。
     */
    updateProgress(progress: number): void;
}
export { MediaMessageTransfer };
