import 'package:retrofit/retrofit.dart';
import 'package:wuling_flutter_app/constant/constant.dart';
import 'package:dio/dio.dart';
import 'package:wuling_flutter_app/models/car/car_maintenance_model.dart';
import 'package:wuling_flutter_app/utils/http/index.dart';

import 'package:wuling_flutter_app/models/car/index.dart';

import '../models/car/car_mini_life_model.dart';
import '../models/index.dart';
part 'car_api.g.dart';

@RestApi(baseUrl: Constant.SERVICE_BASE_URL_OPEN)
abstract class CarAPI {
  factory CarAPI(Dio dio, {String? baseUrl}) = _CarAPI;

  @POST('junApi/sgmw/userCarRelation/queryDefaultCarInfo')
  Future<CarInfoModel> getDefaultCarInfo();

  @POST('junApi/sgmw/userCarRelation/queryDefaultCarStatus')
  Future<CarStatusResponseModel> getDefaultCarInfoAndStatus();

  @POST('junApi/sgmw/service/appQueryService')
  Future<List<CarServiceResponseModel>> getCarServiceList(
      @Field('servicePositionCodeList') List<String> servicePositionCodeList);

  @POST('{address}')
  Future<CarControlResponseModel> requestCarControlServiceWithURLStr(
    @Path('address') String address,
    @Body() Map<String, dynamic> body,
  );

  @POST('junApi/sgmw/car/info/tire/pressure')
  Future<CarTireInfoModel> getCarTireInfo(@Field('vin') String vin);

  @POST('junApi/sgmw/car/check/all')
  Future<CarCheckInfoModel> getCarCheckInfo(@Field('vin') String vin);

  @POST('junApi/sgmw/userCarRelation/queryGrantAuthByCarOwner')
  Future<List<CarAuthorizedUserModel>> queryAuthorizedUserWithVin(
      @Field('vin') String vin);

  @POST('junApi/sgmw/userCarRelation/deleteGrantAuth')
  Future<bool> deleteAuthorizedUserWithVin(
    @Field('vin') String vin,
    @Field('mobile') String mobile,
  );

  @POST('junApi/sgmw/userCarRelation/queryCarAllGrantAuth')
  Future<CarPermissionModel> queryCarAllGrantAuthWithVin(
    @Field('vin') String vin,
  );

  @GET("junApi/sgmw/userCarRelation/systemTime")
  Future<int> getNetworkTime();

  @POST('junApi/sgmw/userCarRelation/addOrUpdateGrantAuth')
  Future<bool> lendBluetoothKey(
    @Body() Map<String, dynamic> body,
  );

  @GET('sgmw/car/airConditioner/reserve/list')
  Future<CarAcReservationResponseModel> getAcReservationListWithVin(
      @Query('vin') String vin);

  @POST('sgmw/car/airConditioner/reserve/delete')
  Future<bool> deleteAcReservationWithIdList(
      @Field('primaryKeyList') List<int> deleteList);

  @POST('junApi/sgmw/car/yesterday/mileage')
  Future<CarMileageResponseModel> getYesterdayMileFromSevers(
    @Body() Map<String, dynamic> body,
  );

  @POST('junApi/sgmw/car/warning/charging/query')
  Future<CarChargeStatusModel> getAutoChargeStatusFromSevers(
    @Query('vin') String vin,
  );

  @POST('junApi/sgmw/car/info/health')
  Future<CarNewEnergyBatteryModel> getNewEnergyCarBatteryStatusWithVin(
    @Query('vin') String vin,
  );

  @POST('junApi/sgmw/car/option/smart/charge/query')
  Future<CarChargeReservationDataModel?>
      downloadChargeReservationInfoDataWithVin(@Field('vin') String vin);

  @POST('junApi/sgmw/car/option/smart/charge/reserve')
  Future<CarControlResponseModel> doChargeReservationNetWork(
    @Field('status') int status,
    @Field('vin') String vin,
    @Field('startTime') String startTime,
    @Field('endTime') String endTime,
  );

  @POST('junApi/sgmw/userCarRelation/queryAllCar')
  Future<List<CarSimplifiedInfoModel>> downloadCarListForChoose(
      @Field('appUserInfoList') List<int> appUserInfoList);

  @POST('junApi/sgmw/userCarRelation/switchFavoriteCar')
  Future<bool> doSetDefaultCarWithVin(@Field('vin') String vin);

  @POST('junApi/sgmw/car/control/ble/key/query')
  Future<CarBleKeyModel> getCarBleKey(
      @Field('vin') String vin, @Field('userId') String mobile);

  @POST('junApi/sgmw/userCarRelation/banma/set/pincode')
  Future<bool> setBanmaPinCode(@Field('pinCode') String pinCode,
      @Field('vin') String vin, @Field('channel') String channel);

  @POST('junApi/sgmw/userCarRelation/unbindCar')
  Future<bool> getSgmwUnbindCar(@Body() Map<String, dynamic> body);

  @POST('junApi/sgmw/car/setting/shakeLock')
  Future<bool> setShakeLockStatus(@Body() Map<String, dynamic> body);

  @POST('junApi/sgmw/userCarRelation/banma/get/airConfigs')
  Future<List<CarControlSettingModel>> getTemperatureSetting();

  @POST('junApi/sgmw/userCarRelation/banma/airConfigs/by/condition')
  Future updateCarSetting(@Body() Map<String, dynamic> body);

  @POST('junApi/sgmw/car/setting/saveOrUpdate')
  Future<bool> setBluetoothKeyAutoConnect(@Body() Map<String, dynamic> body);

  @POST('sgmw/base/mqtt/auth')
  Future<CarMqttTokenModel> getMQTTTokenWithVin(
    @Field('vin') String vin,
    @Field('type') String type,
  );


  /// 用车相关服务请求统一接口
  @POST("junApi/sgmw/service/appQueryService")
  Future<List<ServiceResponseModel>?> getSettingServiceData(
      @Body() Map<String, dynamic> body);

  /// 查询默认爱车和车况信息
  @POST("junApi/sgmw/userCarRelation/queryDefaultCarStatus")
  Future<CarModel?> downloadDefaultCarStatusModel();

  /// 设置循环预约充电 参数：vin, type,startTime,endTime
  @POST("junApi/sgmw/car/cycle/charge/reserve")
  Future<OpenOrderResultModel?> doCyclicReservationChargingNetWork(
      @Body() Map<String, dynamic> body);

  /// 查询循环预约充电数据 参数：vin
  @POST("junApi/sgmw/car/cycle/charge/query")
  Future<CyclicReservationChargingModel?>
  getCyclicReservationChargingDataNetWork(
      @Body() Map<String, dynamic> body);

  /// 取消循环预约充电数据 参数：vin
  @POST("junApi/sgmw/car/cancel/cycle/charge/reserve")
  Future<OpenOrderResultModel?> cancelCycliceReservationCharing(
      @Body() Map<String, dynamic> body);

  /// 设置循环预约充电上限 参数：vin,chargeLimit
  @POST("junApi/sgmw/car/cycle/charge/limit")
  Future<OpenOrderResultModel?> settingCharingLimit(
      @Body() Map<String, dynamic> body);

  ///获取车主认证人脸活体检测随机动作
  @GET("sgmw/identity/getCarAuthFaceVerifyAction")
  Future<CarAuthFaceVerifyActionModel?> getCarAuthFaceVerifyAction();

  ///车主认证人脸活体核身检测效验
  @POST("sgmw/identity/carOwnerAuthenticationFaceVerify")
  @MultiPart()
  Future<FaceRecogniModel?> carOwnerAuthenticationFaceVerify(
      @Query('drivingHomeImg') String drivingHomeImg,
      @Query('frontImage') String frontImage,
      @Query('backImage') String backImage,
      @Query('videoUrl') String videoUrl);

  ///车主认证
  @POST("junApi/sgmw/identity/carOwnerAuthentication")
  Future<bool> carOwnerAuthentication(@Body() Map<String, dynamic> body);

  ///通知服务获取
  @GET('sgmw/notification/query')
  Future<List<CarServiceModel>?> getCarNotificationServiceData(
    @Query('vsn') String vsn,
    @Query('vin') String vin,
    @Query('relation') int relation,
    @Query('providerCode') String providerCode,
    @Query('isAuthIdentity') int isAuthIdentity,
  );

  ///获取保养信息
  @GET('sgmw/car/message/maintenance/query')
  Future<CarMaintenanceModel?> getCarMaintenanceData(
    @Query('vin') String vin,
  );

  ///关闭保养信息提醒
  @POST('sgmw/car/message/maintenance/status')
  Future<bool> closeCarMaintenance(
    @Body() Map<String, dynamic> body,
  );

///关闭通知信息提醒
  @POST('sgmw/notification/close')
  Future<bool> closeCarNotificationService(
    @Body() Map<String, dynamic> body,
  );


  @POST('base/auto/park/api/car/control/parking/status/query')
  Future<RemoteParkBaseModel> getParkingStatusFromService( @Body() Map<String, dynamic> body,);

  @POST('junApi/sgmw/mini/getMiniLife')
  Future<CarMiniLifeModel?> getMiniLiveModelWithVin( @Body() Map<String, dynamic> body,);

}

final carAPI =
    CarAPI(HttpRequest().dio, baseUrl: Constant.SERVICE_BASE_URL_OPEN);
