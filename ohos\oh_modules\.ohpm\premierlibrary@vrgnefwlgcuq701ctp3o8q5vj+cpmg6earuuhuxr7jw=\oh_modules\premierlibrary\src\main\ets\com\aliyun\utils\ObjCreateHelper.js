import { MediaInfo } from '../player/nativeclass/MediaInfo';
import { TrackInfo } from "../player/nativeclass/TrackInfo";
import { Thumbnail } from "../player/nativeclass/Thumbnail";
import { PlayerConfig } from "../player/nativeclass/PlayerConfig";
export class ObjCreateHelper {
    constructor() {
        this.nativeCreateMediaInfo = () => {
            return new MediaInfo();
        };
        this.nativeCreateTrackInfo = () => {
            return new TrackInfo();
        };
        this.nativeCreateThumbnail = () => {
            return new Thumbnail();
        };
        this.nativeCreatePlayerConfig = () => {
            return new PlayerConfig();
        };
    }
}
