import { WeatherSearchOption } from "../../../d/u/e1";
import { SearchRequest } from '../../base/base';
import { UrlProvider } from "../../base/d3";
export declare class WeatherRequest extends SearchRequest {
    mOption: WeatherSearchOption;
    constructor(p42: WeatherSearchOption);
    private requestSuggestionbuildParam;
    getUrlDomain(w34: UrlProvider): string;
    private getDataTypeStr;
    private getCoordTypeStr;
    private getLanguageTypeStr;
}
