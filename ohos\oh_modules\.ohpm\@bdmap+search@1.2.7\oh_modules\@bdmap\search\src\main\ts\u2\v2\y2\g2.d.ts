import { BuildingResult } from "../../../d/e2/f2";
import { BuildingSearchOption } from "../../../d/e2/h2";
import { BaseSearch } from '../../base/base';
export interface IBuildingSearch {
    searchBuilding(option: BuildingSearchOption): Promise<BuildingResult>;
}
export declare class BuildingSearchImp extends BaseSearch implements IBuildingSearch {
    /**
     * @description 搜索建筑物信息
     * @param option BuildingSearchOption类型，包含搜索关键词、城市等参数
     * @returns Promise<BuildingResult>，返回搜索结果的Promise对象
     */
    searchBuilding(a7: BuildingSearchOption): Promise<BuildingResult>;
}
