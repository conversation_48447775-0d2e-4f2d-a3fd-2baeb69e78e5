import 'dart:collection';
import 'dart:convert';
import 'dart:io';

import 'package:city_pickers/city_pickers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_uikit/flutter_uikit.dart';
import 'package:image_picker_ohos/image_picker_ohos.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:wuling_flutter_app/common/action.dart';
import 'package:wuling_flutter_app/constant/constant.dart';
import 'package:wuling_flutter_app/models/global_data.dart';
import 'package:wuling_flutter_app/page/community/topic_post_details_page.dart';
import 'package:wuling_flutter_app/page/profile_page/address_page.dart';
import 'package:wuling_flutter_app/page/qrscan/BarcodeScannerPage.dart';
import 'package:wuling_flutter_app/page/store/store_detail/store_detail_page.dart';
import 'package:wuling_flutter_app/plugins/base_plugin/base_plugin.dart';
import 'package:wuling_flutter_app/plugins/share_plugin/share_plugin.dart';
import 'package:wuling_flutter_app/routes/jump_tool.dart';
import 'package:wuling_flutter_app/utils/encrypt/RSA_decryptor.dart';
import 'package:wuling_flutter_app/utils/encrypt/bagnbang_util.dart';
import 'package:wuling_flutter_app/utils/manager/loading_manager.dart';
import 'package:wuling_flutter_app/utils/manager/location_manager.dart';
import 'package:wuling_flutter_app/utils/manager/log_manager.dart';
import 'package:wuling_flutter_app/utils/manager/login_manager.dart';
import 'package:wuling_flutter_app/utils/qr_scanner_util.dart';
import 'package:wuling_flutter_app/widgets/webview/web_view_actions.dart';
import 'package:wuling_flutter_app/widgets/webview/webview.dart';
import 'package:wuling_flutter_cashier/com/sgmw/plugin/cashier/cashier_page.dart';
import 'package:wuling_flutter_cashier/com/sgmw/plugin/constant/constants.dart';

import '../../constant/service_constant.dart';
import '../../constant/web_view_url_tool.dart';
import '../../models/shop/dealer_model.dart';
import '../../page/community/send_topic_page.dart';
import '../../page/community/user_info_page.dart';
import 'package:image_picker_platform_interface/image_picker_platform_interface.dart';
import '../../api/file_upload_api.dart';
import '../../models/car/img_list_model.dart';
import '../../page/use_car_page/face_recognition.dart';
import '../../page/use_car_page/more_service_page.dart';
import '../../utils/manager/notification_manager.dart';
import '../common/location_dialog.dart';
import 'web_view_js_method.dart';

class WebViewJsChannel {
  static final WebViewJsChannel _instance = WebViewJsChannel._();

  factory WebViewJsChannel() => _instance;

  WebViewJsChannel._();

  final Map<String, Function(JavaScriptMessage)> _methods = {};

  //注入数据
  void injectData(BuildContext context, WebViewController controller) {
    _initData(context, controller);
    //循环_methods
    _methods.forEach((key, value) {
      controller.addJavaScriptChannel(key, onMessageReceived: value);
    });
  }

  void _initData(BuildContext context, WebViewController controller) {
    _methods.clear();
    //页面返回方法
    _methods.putIfAbsent(
        WebViewActions.goBack.name,
        () => (JavaScriptMessage message) async {
              if (await controller.canGoBack()) {
                await controller.goBack();
              } else {
                NavigatorAction.init(context).then(() {
                  controller.goBack();
                });
              }
            });

    //关闭页面方法
    _methods.putIfAbsent(
        WebViewActions.closePage.name,
        () => (JavaScriptMessage message) async {
              NavigatorAction.init(context).then(() {
                controller.goBack();
              });
            });

    _methods.putIfAbsent(
        WebViewActions.toast.name,
        () => (JavaScriptMessage message) {
              LoadingManager.showToast(message.message);
            });

    _methods.putIfAbsent(
        WebViewActions.getPosition.name,
        () => (JavaScriptMessage message) async {
              //todo 实现定位方法
              var location = await BasePlugin.getLocationGCJ02();
              // var location = await LocationManager.getLocation();
              LogManager().debug(location.toString());
              if (location != null) {
                LogManager().info("web调用定位成功！");
                controller.runJavaScript(
                    "javascript: getCoordinate('${location['subAdministrativeArea']!}', '${location['longitude']!}', '${location['latitude']!}', '${location['subLocality']!}')");
              } else {
                LogManager().info("web调用定位！");
                controller.runJavaScript(
                    "javascript: getCoordinate('柳州市','109.38097477363677','24.333322609756333','柳南区')");
              }
            });

    //打开登录页面
    _methods.putIfAbsent(
        WebViewActions.showNoLogin.name,
        () => (JavaScriptMessage message) {
              LogManager().debug("打开登录页面：${message.toString()}");
              if (!GlobalData().isLogin) {
                LoginManager().showLoginModal();
              }
            });

    _methods.putIfAbsent(
        WebViewActions.bangSafeCheckCode.name,
        () => (JavaScriptMessage message) {
              LogManager().info('Received message from JS: $message');
              Map<String, dynamic> data = jsonDecode(message.message);

              var queryResult = "";
              var jsonResult = "";
              if ("1" == data['type']) {
                //加密
                queryResult = BangBangUtil()
                    .encrypt(data['query'], BangBangUtil.DATA_TYPE_OTHER);
                jsonResult = BangBangUtil()
                    .encrypt(data['json'], BangBangUtil.DATA_TYPE_JSON);
              } else {
                // 解密
                jsonResult = BangBangUtil().decrypt(data['json']);
                if (jsonResult.isNotEmpty) {
                  //解密时，因为最后是拼在 '' 中传递，如果带有 \" 应该转义为 \\"
                  jsonResult = jsonResult.replaceAll("\\", "\\\\");
                  //因为使用单引号包围字符串，所以单引号需要转义
                  jsonResult = jsonResult.replaceAll("'", "\\'");
                }
              }
              controller.runJavaScript(
                  "javascript: bangSafeReceiveCode('${data['type']}','${data['state']}','$queryResult','$jsonResult')");
            });

    _methods.putIfAbsent(
      WebViewActions.carInfoChange.name,
      () => (JavaScriptMessage message) {
        Map<String, dynamic> data = jsonDecode(message.message);
        LogManager().info("web调用 carInfoChange,data：${data.toString()}");
        String providerCode = data['providerCode'];
        String vin = data['vin'];
        String type = data['type'].toString();//type : 1--表示切换爱车，2--表示解绑车辆
        NotificationManager().postNotification(
            Constant.NOTIFICATION_SWITCH_CAR_SUCCEED,
            userInfo: {'vin': vin ,'type': type});
      },
    );

    _methods.putIfAbsent(
      WebViewActions.uploadLog.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 uploadLog 方法
        LogManager().info("web调用 uploadLog");
        String fileUrl = '';
        controller.runJavaScript("javascript: uploadLogCallback('$fileUrl')");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.networkState.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 networkState 方法
        LogManager().info("web调用 networkState");
        doGetNetworkState();
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goRootPage.name,
      () => (JavaScriptMessage message) {
        LogManager().info("web调用 goRootPage");
        back2RootVC(context);
      },
    );

    _methods.putIfAbsent(
      WebViewActions.getIosPosition.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 getIosPosition 方法
        LogManager().info("web调用 getIosPosition");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.showShare.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 showShare 方法
        ShowAction.init(context).showShare().then((value) {
          if (value == "weixin") {
            SharePlugin.shareFriend("hello，朋友！").then((value) {
              LogManager().debug(value);
            });
          } else if (value == "pyq") {
            SharePlugin.shareCommon("hello，朋友圈！").then((value) {
              LogManager().debug(value);
            });
          }
        });
        LogManager().info("web调用 showShare");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goShare.name,
      () => (JavaScriptMessage message) {
        SharePlugin.shareData(jsonDecode(message.message)).then((value) {
          LogManager().debug(value);
        });
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goShareToMini.name,
      () => (JavaScriptMessage message) {
        Map<String, dynamic> data = jsonDecode(message.message);
        SharePlugin.shareFriend(
          data['path'],
          title:data['title'],
          description:data['description'],
          cover:data['cover'],
          userName: data["username"],
        );
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goSendPost.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goSendPost 方法
        LogManager().info("web调用 goSendPost");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goClub.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goClub 方法
        LogManager().info("web调用 goClub");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goPost.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goPost 方法
        LogManager().debug("H5 message: ${message.message}");
        Navigator.of(context).push(MaterialPageRoute(builder: (context) {
          return TopicPostDetailsPage(
              postId: int.parse(message.message), postTypeId: 14);
        }));
        LogManager().info("web调用 goPost");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goToUser.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goToUser 方法
        Navigator.of(context).push(MaterialPageRoute(builder: (context) {
          return UserInfoPage(nickname: "", userIdStr: message.message);
        }));
        LogManager().info("web调用 goToUser : ${message.message}");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goToUserHomePage.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goToUserHomePage 方法
        LogManager().info("web调用 goToUserHomePage");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.saveLocalImg.name,
      () => (JavaScriptMessage message) {
        BasePlugin.saveImage("data:image/png;base64,${message.message}");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.showPic.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 showPic 方法
        LogManager().info("web调用 showPic");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goCommonWeb.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goCommonWeb 方法
        Map<String, dynamic> data = jsonDecode(message.message);
        NavigatorAction.init(context,view: WebViewPage(url: data['url']));
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goCustomerService.name,
      () => (JavaScriptMessage message) {
        if (!GlobalData().isLogin) {
          showNotLoginAlertDialog(context);
          return;
        }
        String url = WebViewURLTool.kefuURLStrWithGroup(
            KefuGroup.mm.value, '', '');
        JumpTool().openWeb(context, url, true);
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goShareBtnWeb.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goShareBtnWeb 方法
        LogManager().info("web调用 goShareBtnWeb");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goCommonOthersWeb.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goCommonOthersWeb 方法
        LogManager().info("web调用 goCommonOthersWeb");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.addCalendarEvent.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 addCalendarEvent 方法
        LogManager().info("web调用 addCalendarEvent");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.sendSMS.name,
      () => (JavaScriptMessage message) {
        LogManager().info("web调用 sendSMS");
        List<String> msgs = parseParameter(message.message);
        // 检查数据是否至少包含两个元素
        if (msgs.length < 2) {
          LogManager().error("数据格式错误或元素不足: $msgs");
          return;
        }

        String phoneNumber = msgs[0];
        String messageBody = msgs[1];
        sendSMS(phoneNumber, messageBody);
      },
    );

    _methods.putIfAbsent(
      WebViewActions.forbidPanGesture.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 forbidPanGesture 方法
        LogManager().info("web调用 forbidPanGesture");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.appealAuthentication.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 appealAuthentication 方法
        LogManager().info("web调用 appealAuthentication");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.getIdCardInfo.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 getIdCardInfo 方法
        LogManager().info("web调用 getIdCardInfo");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.scanCode.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 scanCode 方法
        LogManager().info("web调用 scanCode");
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => BarcodeScannerPage(
                onPageCallBack: (data) => {
                      //二维码扫描回调，这里再处理响应的操作
                      QrScannerUtil().returnProcessing(context, data)
                    }),
          ),
        );
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goScanCode.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goScanCode 方法
        LogManager().info("web调用 goScanCode");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goTakePicture.name,
          () => (JavaScriptMessage message) async {
        LogManager().info("web调用 goTakePicture");
        try {
          bool hasAllPermissions = await checkAllPermissions();
          if(!hasAllPermissions) {
            return;
          }
          final ImagePickerOhos picker = ImagePickerOhos();
          final source = await showDialog<ImageSource>(
            context: context,
            useRootNavigator: true,
            builder: (BuildContext context) {
              return AlertDialog(
                title: const Text('选择图片来源'),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    ListTile(
                      leading: const Icon(Icons.photo_library),
                      title: const Text('相册'),
                      onTap: () => Navigator.pop(context, ImageSource.gallery),
                    ),
                    ListTile(
                      leading: const Icon(Icons.camera_alt),
                      title: const Text('相机'),
                      onTap: () => {
                        Navigator.pop(context, ImageSource.camera)
                      },
                    ),
                  ],
                ),
              );
            },
          );

          if (source != null) {
            final PickedFile? file = await picker.pickImage(
              source: source,
              maxWidth: 1920,
              maxHeight: 1080,
              imageQuality: 90,
            );

            if (file != null) {
              // 打印文件信息以便调试
              LogManager().info("文件路径: ${file.path}");
              // LogManager().info("文件名: ${file.name}");
              // final fileSize = await file.length();
              // LogManager()
              //     .info("文件大小: ${(fileSize / 1024).toStringAsFixed(2)}KB");

              // 调用上传方法
              _upHeadPotato(file,controller); // 修改这里，传入 file.path

              // // 将图片路径返回给 Web 页面
              // await controller.runJavaScript(
              //     "javascript: takePictureCallback('${file.path}')"
              // );
            }
          }
        } catch (e) {
          LogManager().error("拍照/选择图片失败: $e");
          LoadingManager.showToast("操作失败");
        }
      },
    );

    _methods.putIfAbsent(
      WebViewActions.faceDetect.name,
          () => (JavaScriptMessage message) async {
        LogManager().info("web调用 faceDetect");
        try {
          // 解析URLs
          final List<String> urls = message.message
              .split(',')
              .map((url) => url.trim())
              .where((url) => url.isNotEmpty)
              .toList();

          LogManager().info("""
        准备跳转到人脸识别页面:
        图片数量: ${urls.length}
        URLs列表:
        ${urls.asMap().entries.map((e) => '${e.key + 1}. ${e.value}').join('\n')}
      """);

          // 跳转到人脸识别页面并等待结果
          final result = await Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => FaceRecognition(
                imageUrls: urls,
              ),
            ),
          );

          if (result != null && result is Map<String, dynamic>) {
            final authResult = result['Authentication_result'];
            final resultCode = result['Authentication_result_code'];

            LogManager().info("""
          人脸识别结果:
          Authentication_result: $authResult (类型: ${authResult.runtimeType})
          Authentication_result_code: $resultCode (类型: ${resultCode.runtimeType})
          原始结果: $result
        """);

            // 调用 H5 的 webHandleStatus 方法
            await controller.runJavaScript(
                "javascript: webHandleStatus('face', ${authResult ?? ""}, '${resultCode ?? ""}')"
            );

            LogManager().info("成功调用 H5 webHandleStatus 方法");
          } else {
            LogManager().info("返回结果为空或格式不正确: $result");
          }

        } catch (e) {
          LogManager().error("人脸识别过程发生错误: $e");
          LoadingManager.showToast('人脸识别处理失败');
        }
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goAdvertisement.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goAdvertisement 方法
        LogManager().info("web调用 goAdvertisement");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.postCarBuyingExperience.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 postCarBuyingExperience 方法
        LogManager().info("web调用 postCarBuyingExperience");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goCarTypeDetail.name,
      () => (JavaScriptMessage message) {
        LogManager().debug(message.message);
        // TODO: 实现 goCarTypeDetail 方法
        LogManager().info("web调用 goCarTypeDetail");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.openInSystemBrowser.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 openInSystemBrowser 方法
        LogManager().info("web调用 openInSystemBrowser");
        List<String> msgs = parseParameter(message.message);
        // 检查数据是否至少包含1个元素
        if (msgs.isEmpty) {
          LogManager().error("数据格式错误或元素不足: $msgs");
          return;
        }

        String url = msgs[0];
        _openBrowser(url);
      },
    );

    _methods.putIfAbsent(
      WebViewActions.decryptText.name,
      () => (JavaScriptMessage message) {
        LogManager().info("web调用 decryptText");
        List<String> msgs = parseParameter(message.message);
        // 检查数据是否至少包含1个元素
        if (msgs.isEmpty) {
          LogManager().error("数据格式错误或元素不足: $msgs");
          return;
        }
        String decryptString = msgs[0];
        try {
          String deStr = doRsaDecode(decryptString);
          controller.runJavaScript("javascript: decryptSuccess('$deStr')");
        } catch (e) {
          LogManager().error("decryptText failed: $e");
        }
      },
    );

    _methods.putIfAbsent(
      WebViewActions.sureToBuyGift.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 sureToBuyGift 方法
        LogManager().info("web调用 sureToBuyGift");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goApprove.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goApprove 方法
        LogManager().info("web调用 goApprove");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goCommodityDetail.name,
      () => (JavaScriptMessage message) {
        LogManager().info("web调用 goCommodityDetail: ${message.message}");
        try {
          // 解析JSON参数
          Map<String, dynamic> data = jsonDecode(message.message);
          dynamic commodityId = data['id'];
          int code = data['code'] ?? 0;
          int cId = (commodityId is int) ? commodityId : int.parse(commodityId);

          // 检查是否有groupActivityId参数
          if (data.containsKey('groupActivityId')) {
            int groupActivityId = data['groupActivityId'];
            // 如果有groupActivityId，可以在这里处理额外逻辑
            LogManager().info(
                "goCommodityDetail with groupActivityId: $groupActivityId");
          }

          NavigatorAction.init(context,
              view: StoreDetailPage(id: cId, code: code));
        } catch (e) {
          LogManager().error("goCommodityDetail解析参数失败: $e");
          LoadingManager.showError("商品信息解析失败，请重试");
        }
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goNewCommodityDetail.name,
      () => (JavaScriptMessage message) {
        LogManager().info("web调用 goNewCommodityDetail: ${message.message}");
        try {
          // 解析JSON参数
          Map<String, dynamic> data = jsonDecode(message.message);
          dynamic commodityId = data['id'];
          int code = data['code'] ?? 0;

          int cId = (commodityId is int) ? commodityId : int.parse(commodityId);
          // 检查是否有shopId参数  注意shopId类型
          if (data.containsKey('shopId')) {
            dynamic shopId = data['shopId'];
            int sId = (shopId is int) ? shopId : int.parse(shopId);
            // 如果有shopId，可以在这里处理额外逻辑
            LogManager().info("goNewCommodityDetail with shopId: $sId");
            // 根据需要，可以将shopId传递给StoreDetailPage或进行其他处理
          }

          NavigatorAction.init(context,
              view: StoreDetailPage(id: cId, code: code));
        } catch (e) {
          LogManager().error("goNewCommodityDetail解析参数失败: $e");
          LoadingManager.showError("商品信息解析失败，请重试");
        }
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goOrderReturn.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goOrderReturn 方法
        LogManager().info("web调用 goOrderReturn");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goInvoiceList.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goInvoiceList 方法
        LogManager().info("web调用 goInvoiceList");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goShop.name,
      () => (JavaScriptMessage message) {
        NavigatorAction.init(
          context,
          isBack: true,
        );
        NotificationManager().postNotification(
            Constant.NOTIFICATION_MAIN_PAGE_SELECT_PAGE,
            userInfo: {'pageIndex': 3});
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goNewShop.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goNewShop 方法
        LogManager().info("web调用 goNewShop");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goCommodityIndexByPageCode.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goCommodityIndexByPageCode 方法
        LogManager().info("web调用 goCommodityIndexByPageCode");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goNavigation.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goNavigation 方法
        Map<String, dynamic> data = jsonDecode(message.message);
        NavigationParam param = NavigationParam(
            dealerShortName: data['dealerShortName'] ?? '未知地址',
            longitude: data['longitude'] ?? 0,
            latitude: data['latitude'] ?? 0);
        LocationDialog.showMapOptions(context, param);
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goChat.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goChat 方法
        LogManager().info("web调用 goChat");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goGroupChat.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goGroupChat 方法
        LogManager().info("web调用 goGroupChat");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goCarShop.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goCarShop 方法
        LogManager().info("web调用 goCarShop");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goLifeServiceList.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goLifeServiceList 方法
        LogManager().info("web调用 goLifeServiceList");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.shareToPartnerTreasureHouseActivity.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 shareToPartnerTreasureHouseActivity 方法
        LogManager().info("web调用 shareToPartnerTreasureHouseActivity");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.openWXMiniProgram.name,
      () => (JavaScriptMessage message) {
        SharePlugin.openMini(jsonDecode(message.message));
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goRecommend.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goRecommend 方法
        LogManager().info("web调用 goRecommend");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goTechArea.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goTechArea 方法
        LogManager().info("web调用 goTechArea");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goTechQA.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goTechQA 方法
        LogManager().info("web调用 goTechQA");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.checkWeChatAPP.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 checkWeChatAPP 方法
        LogManager().info("web调用 checkWeChatAPP");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.openAppWithURLSchemes.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 openAppWithURLSchemes 方法
        LogManager().info("web调用 openAppWithURLSchemes");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goBindCar.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goBindCar 方法
        LogManager().info("web调用 goBindCar");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.toggleCarShowSuccess.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 toggleCarShowSuccess 方法
        LogManager().info("web调用 toggleCarShowSuccess");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.closeAndOpenNewWeb.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 closeAndOpenNewWeb 方法
        LogManager().info("web调用 closeAndOpenNewWeb");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goAddressChoose.name,
      () => (JavaScriptMessage message) {
        NavigatorAction.init(context,view: AddressPage(type: 1,),then: (v){
          controller.runJavaScript("javascript:setAddress('$v')");
        });
      },
    );

    _methods.putIfAbsent(
      WebViewActions.jumpStep.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 jumpStep 方法
        LogManager().info("web调用 jumpStep");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goTopic.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goTopic 方法
        LogManager().info("web调用 goTopic");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.shareImage.name,
      () => (JavaScriptMessage message) {
        dynamic data = jsonDecode(message.message);
        if (data["type"] == 1) {
          SharePlugin.shareFriend(data["url"]);
        } else if (data["type"] == 2) {
          SharePlugin.shareCommon(data["url"]);
        } else if (data["type"] == 3) {
          ShowAction.init(context).showShare().then((value) {
            if (value == "weixin") {
              SharePlugin.shareFriend(data["url"]);
            } else if (value == "pyq") {
              SharePlugin.shareCommon(data["url"]);
            }
          });
        }
      },
    );

    _methods.putIfAbsent(
      WebViewActions.postReply.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 postReply 方法
        LogManager().info("web调用 postReply");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.hideTopView.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 hideTopView 方法
        LogManager().info("web调用 hideTopView");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.tipOffPost.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 tipOffPost 方法
        LogManager().info("web调用 tipOffPost");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goLabelPostList.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goLabelPostList 方法
        LogManager().info("web调用 goLabelPostList");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goTCLive.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goTCLive 方法
        LogManager().info("web调用 goTCLive");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goCommodityIndex.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goCommodityIndex 方法
        LogManager().info("web调用 goCommodityIndex");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goCouponGoodsList.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goCouponGoodsList 方法
        LogManager().info("web调用 goCouponGoodsList");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goShareMiniProgramToWechatSession.name,
      () => (JavaScriptMessage message) {
        Map<String,dynamic> map = jsonDecode(message.message);
        LogManager().debug("goShareMiniProgramToWechatSession.message:${map}");
        SharePlugin.shareFriend(map['f'],
            title: map['b'], description: map['b'],cover: map['d'],userName: map['e']);
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goCart.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goCart 方法
        LogManager().info("web调用 goCart");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goChangeMobile.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goChangeMobile 方法
        LogManager().info("web调用 goChangeMobile");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.forceLogOut.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 forceLogOut 方法
        LogManager().info("web调用 forceLogOut");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goWelfareGifts.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goWelfareGifts 方法
        LogManager().info("web调用 goWelfareGifts");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.getPateoToken.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 getPateoToken 方法
        LogManager().info("web调用 getPateoToken");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.getMessageToken.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 getMessageToken 方法
        LogManager().info("web调用 getMessageToken");
        String jsonStr = assembleJsonString();
        //设置登录信息
        WebViewJsMethod().messageToken(controller);
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goCarControl.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goCarControl 方法
        LogManager().info("web调用 goCarControl");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.pateoLogin.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 pateoLogin 方法
        LogManager().info("web调用 pateoLogin");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goPayApp.name,
      () => (JavaScriptMessage message) {
        OverlayCreate.context = context;
        // UIOverlay.toast("请前往五菱汽车微信小程序-我的-订单中进行支付");
        // return;
        LogManager()
            .info('web调用 goPayApp Received message from JS: ==$message');
        Map<String, dynamic> jsonMap = jsonDecode(message.message);
        String orderNo = jsonMap['orderNo'];
        String token = jsonMap['accessToken'];
        String cashierId = jsonMap['cashierId'];
        String target = jsonMap['target'];
        String application = jsonMap['application'];
        LogManager().info(
            "web调用 goPayApp orderNo=${orderNo},token=${token},cashierId=${cashierId},target=${target},application=${application}");

        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => CashierPage(
                orderNo: orderNo,
                token: token,
                cashierId: cashierId,
                target: target,
                application: application,
                wXAppID: Constant.kWXAppID,
                universalLinkURL: Constant.kUniversalLinkURL,
                userName: Constant.kMiniProgramUserName,
                basPath: Constant.kBasPath,
                miniProgramType: Constant.kMiniProgramType,
                isPro: Constant.ENV == 'PRO'),
          ),
        ).then((value) => {
              if ("1" == value)
                {
                  controller.runJavaScript(
                      "javascript:payCallBack('1','$orderNo','0')")
                  // LogManager().debug('支付成功 ${value}')
                }
              else
                {
                  controller.runJavaScript(
                      "javascript:payCallBack('0','$orderNo','0')")
                  // LogManager().debug('支付失败 ${value}')
                }
            });
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goScorePayApp.name,
      () => (JavaScriptMessage message) {
        OverlayCreate.context = context;
        // UIOverlay.toast("请前往五菱汽车微信小程序-我的-订单中进行支付");
        // return;
        LogManager()
            .info('web调用 goScorePayApp Received message from JS: ==$message');
        Map<String, dynamic> jsonMap = jsonDecode(message.message);
        String orderNo = jsonMap['orderNo'];
        String token = jsonMap['accessToken'];
        String cashierId = jsonMap['cashierId'];
        String target = jsonMap['target'];
        String application = jsonMap['application'];
        LogManager().info(
            "web调用 goScorePayApp orderNo=${orderNo},token=${token},cashierId=${cashierId},target=${target},application=${application}");

        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => CashierPage(
                orderNo: orderNo,
                token: token,
                cashierId: cashierId,
                target: target,
                application: application,
                wXAppID: Constant.kWXAppID,
                universalLinkURL: Constant.kUniversalLinkURL,
                userName: Constant.kMiniProgramUserName,
                basPath: Constant.kBasPath,
                miniProgramType: Constant.kMiniProgramType,
                isPro: Constant.ENV == 'PRO'),
          ),
        ).then((value) => {
              if ("1" == value)
                {
                  controller.runJavaScript(
                      "javascript:scorePayCallBack('1','$orderNo','0')")
                  // LogManager().debug('支付成功 ${value}')
                }
              else
                {
                  controller.runJavaScript(
                      "javascript:scorePayCallBack('0','$orderNo','0')")
                  // LogManager().debug('支付失败 ${value}')
                }
            });
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goPay.name,
      () => (JavaScriptMessage message) {
        OverlayCreate.context = context;
        UIOverlay.show("请前往五菱汽车微信小程序-我的-订单中进行支付");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goScorePay.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goScorePay 方法
        LogManager().info("web调用 goScorePay");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.getADSkipWithLinkTypeAndLinkUrl.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 getADSkipWithLinkTypeAndLinkUrl 方法
        LogManager().info("web调用 getADSkipWithLinkTypeAndLinkUrl");
        Map<String, dynamic> data = jsonDecode(message.message);
        int linkType = data['linkType'];
        String linkUrl = data['linkUrl'];
        JumpTool().jumpToAdvertisePage(context,
            linkType: linkType, linkUrl: linkUrl, eventPage: "");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.removeTaskCenterDot.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 removeTaskCenterDot 方法
        LogManager().info("web调用 removeTaskCenterDot");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.taskSensors.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 taskSensors 方法
        LogManager().info("web调用 taskSensors");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.getAppCode.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 getAppCode 方法
        LogManager().info("web调用 getAppCode");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goSendLingPost.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goSendLingPost 方法A
        Navigator.of(context).push(MaterialPageRoute(builder: (context) {
          return SendTopicPage();
        }));
      },
    );

    _methods.putIfAbsent(
      WebViewActions.refreshToTravelHome.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 refreshToTravelHome 方法
        LogManager().info("web调用 refreshToTravelHome");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goSignCalendar.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goSignCalendar 方法
        LogManager().info("web调用 goSignCalendar");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goMyInfo.name,
      () => (JavaScriptMessage message) {
        NavigatorAction.init(context, isBack: Navigator.canPop(context));
        NotificationManager().postNotification(
            Constant.NOTIFICATION_MAIN_PAGE_SELECT_PAGE,
            userInfo: {'pageIndex': 4});
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goWXOpenBusiness.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goWXOpenBusiness 方法
        LogManager().info("web调用 goWXOpenBusiness");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.jumpToTaoBao.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 jumpToTaoBao 方法
        LogManager().info("web调用 jumpToTaoBao");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.goTopicList.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 goTopicList 方法
        LogManager().info("web调用 goTopicList");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.faceDetect.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 faceDetect 方法
        LogManager().info("web调用 faceDetect");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.getCurrentEstimateInfo.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 getCurrentEstimateInfo 方法
        LogManager().info("web调用 getCurrentEstimateInfo");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.setWeixinPayReferer.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 setWeixinPayReferer 方法
        LogManager().info("web调用 setWeixinPayReferer");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );

    _methods.putIfAbsent(
      WebViewActions.scanCodeCharge.name,
      () => (JavaScriptMessage message) {
        // TODO: 实现 scanCodeCharge 方法
        LogManager().info("web调用 scanCodeCharge");
        controller.runJavaScript("javascript: // Your JavaScript code here");
      },
    );
  }

  // Dart 方法实现
  String assembleJsonString() {
    Map<String, dynamic> map = HashMap();
    if (GlobalData().isLogin) {
      var user = GlobalData().userModel;
      map.putIfAbsent("userId", () => user?.userIdStr);
      map["phoneNum"] = user?.mobile;
      map["phone"] = user?.mobile;
      // 校验相关
      map["accessToken"] = GlobalData().oauthModel?.accessToken;
      map["client_id"] = Constant.APP_CLIENT_ID;
      map["client_secret"] = Constant.APP_CLIENT_SECRET;
      map["salt"] = Constant.APP_SALT;
      // map["globalId"] = user?.globalId;
      // 车主信息
      var carInfo = GlobalData().carInfoModel;
      map["vin"] = carInfo?.vin;
    }
    // if (Platform.isOhos) {
    //   OhosDeviceInfo info = await DeviceInfoUtil().get();
    //   LogManager().debug(info);
    // }

    map["sgmwappCode"] = "sgmw_llb";
    map["appVersionCode"] = "2.4.10";
    map["appVersionCodeNum"] = "1569";
    map["imei"] = "5bccbe2d-ca38-4910-850a-2111ac6e47fd";
    // map["imsi"] = application.imsi;
    // map["model"] = application.model;
    // map["osVersion"] = application.osVersion;
    // 转换为 JSON 字符串
    String jsonStr = jsonEncode(map);

    // 替换单引号
    jsonStr = jsonStr.replaceAll("'", "\\'");

    return jsonStr;
  }

  List<String> parseParameter(String message) {
    try {
      // 尝试使用 jsonDecode 解析
      final dynamic decoded = jsonDecode(message);
      if (decoded is List<dynamic>) {
        // 如果解析成功并且是一个 List 类型，返回字符串数组
        return decoded.map((item) => item.toString()).toList();
      } else {
        // 如果解析结果不是 List，抛出异常进入手动解析流程
        throw const FormatException('Decoded JSON is not a list');
      }
    } catch (e) {
      // jsonDecode 解析失败，或者格式不对，进入手动解析流程
      return _manualParseArray(message);
    }
  }

  List<String> _manualParseArray(String str) {
    // 确保字符串以 [ 开头和 ] 结尾
    if (str.startsWith('[') && str.endsWith(']')) {
      // 去除开头和结尾的方括号
      str = str.substring(1, str.length - 1);

      // 按照逗号分割字符串，并去除首尾的引号和空格
      return str
          .split(',')
          .map((s) => s.trim().replaceAll('"', '').replaceAll("'", ""))
          .toList();
    } else if (str is String && str.isNotEmpty && !str.startsWith('[')) {
      return [str];
    } else {
      // 如果不符合预期格式，返回一个空数组或处理错误
      return [];
    }
  }

  /**
      获取网络状态，传值给H5
   */
  Future<void> doGetNetworkState() async {}

  // rsa解码
  String doRsaDecode(String encryptedText) {
    const String privateKey = '''**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************''';
    String decStr = RSADecryptor().decryptString(encryptedText, privateKey);
    return decStr;
  }

  //返回到根页面
  void back2RootVC(BuildContext context) {
    Navigator.of(context).popUntil((route) => route.isFirst);
  }

  void sendSMS(String phoneNumber, String body) async {
    final Uri smsUri = Uri(
      scheme: 'sms',
      path: phoneNumber,
      queryParameters: {'body': body},
    );

    if (await canLaunchUrl(smsUri)) {
      await launchUrl(smsUri);
    } else {
      LoadingManager.showToast("设备不支持发送文本信息");
    }
  }

  void _openBrowser(String url) async {
    final Uri uri = Uri.parse(url);

    if (await canLaunchUrl(uri)) {
      await launchUrl(
        uri,
        mode: LaunchMode.externalApplication, // 使用系统浏览器打开
      );
    } else {
      LoadingManager.showToast("无效链接");
    }
  }

  void _upHeadPotato(PickedFile file,WebViewController controller) async {
    try {
      // 创建 File 对象
      final File imageFile = File(file.path);

      // 创建 FileImage
      final FileImage fileImage = FileImage(imageFile);

      // 创建图片列表
      final List<ImageProvider> imageProviders = [fileImage];

      // 打印日志
      LogManager().info("开始上传图片: ${file.path} , imageFile = ${imageFile.path}");

      String uploadType = '1';
      String randName = '1';
      String deleteFlag = '0';

      ImgListModel? imgListModel = await fileUploadApi.uploadImage(
          imageFile, uploadType, randName, deleteFlag);
      // 刷新页面，重新获取授权列表
      LogManager().error("上传成功");
      // 处理返回结果
      if (imgListModel.fileUrls!.isNotEmpty) {
        final imageUrl = imgListModel.fileUrls!.first;
        LogManager().info("上传成功，图片URL: $imageUrl");
        LoadingManager.showToast('上传成功');

        // 调用 h5 的 getPicture 方法
        await controller.runJavaScript('''
        (function() {
          if (typeof getPicture === 'function') {
            getPicture('${imgListModel.fileUrls!.join(",")}');
          } else {
            console.error('getPicture method not found');
          }
        })();
      ''');
      } else {
        throw Exception('上传失败：未获取到图片URL');
      }
    } catch (e) {
      // 错误处理
      LogManager().error("上传失败: $e");
      LoadingManager.showToast('上传失败222：${e.toString()}');
    }
  }

  // 检查相机权限状态
  Future<void> checkCameraPermission(BuildContext context) async {
    // 检查单个权限
    final status = await Permission.camera.status;
    LogManager().debug('检查相机权限状态 status:$status');
    if (status.isGranted) {
      LogManager().debug("已获得相机权限");
      Navigator.pop(context, ImageSource.camera);
    }else {
      Navigator.pop(context);
      showDialog(
        context: context,
        useRootNavigator: true,
        builder: (ctx2) => AlertDialog(
          content: Text('五菱汽车将要访问您的相机和相册，我们将用于您发帖、扫码、车主认证、修改头像、更换背景、客服聊天、购车反馈等场景，点击确认以请求权限'),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(ctx2),
              child: Text('取消',style: TextStyle(color: Colors.grey),),
            ),
            TextButton(
              onPressed: () {
                requestCameraPermission(ctx2);
                Navigator.pop(ctx2);
              },
              child: Text('确认',style: TextStyle(color: Colors.blue),),
            ),
          ],
        ),
      );
    }
  }

  static Future<bool> requestCameraPermission(BuildContext context) async {
    final status = await Permission.camera.request();
    if (status.isGranted) {
      // 权限已授予
      LogManager().debug("已获得相机权限");
      Navigator.pop(context, ImageSource.camera);
    } else if (status.isDenied) {
      // 首次拒绝
      LogManager().debug("首次拒绝相机权限");
    } else if (status.isPermanentlyDenied) {
      // 永久拒绝，需引导至设置
      showDialog(
        context: context,
        useRootNavigator: true,
        builder: (ctx3) => AlertDialog(
          title: Text('需要相机权限，否则将无法使用该功能，是否打开设置？'),
          content: Text('请在系统设置中开启权限'),
          actions: [
            TextButton(
                onPressed: () {
                  openAppSettingsToChangePermissions();
                  Navigator.pop(ctx3);
                },
                child: Text('去设置',style: TextStyle(color: Colors.blue),)
            ),
          ],
        ),
      );
    }
    return false;
  }
  static Future<void> openAppSettingsToChangePermissions() async {
    await openAppSettings();
  }

  Future<bool> checkAllPermissions() async {
    bool cameraGranted = await requestPermission(Permission.camera);
    if(!cameraGranted) {
      LoadingManager.showToast('五菱汽车将要访问您的相机和相册，我们将用于您车主认证，请在设置中手动开启相机权限');
      try {
        await openAppSettings();
        return false;
      } catch (e) {
        LogManager().error("打开设置页面失败: $e");
      }
    }
    bool microphoneGranted = await requestPermission(Permission.microphone);
    if(!microphoneGranted) {
      LoadingManager.showToast('五菱汽车需要您授予麦克风权限，我们将用于您车主认证，请在设置中手动开启麦克风权限');
      try {
        await openAppSettings();
        return false;
      } catch (e) {
        LogManager().error("打开设置页面失败: $e");
      }
    }
    /*bool audioGranted = await requestPermission(Permission.audio);
    if(!audioGranted) {
      LoadingManager.showToast('五菱汽车需要您授予文件权限，我们将用于您车主认证，请在设置中手动开启文件权限');
      try {
        await openAppSettings();
        return false;
      } catch (e) {
        LogManager().error("打开设置页面失败: $e");
      }
    }*/
    return true;
  }

  // 请求单个权限
  static Future<bool> requestPermission(Permission permission) async {
    final status = await permission.request();
    return status.isGranted;
  }
}
