{"name": "@rongcloud/imkit", "version": "1.7.1", "description": "融云 IMKit SDK for HarmonyOS", "author": "rongcloud", "homepage": "https://www.rongcloud.cn", "keywords": ["融云", "IMKit", "RongCloud"], "license": "MIT", "dependencies": {"@rongcloud/imlib": "1.7.1", "@charles/amrnbconverter": "^1.0.1"}, "devDependencies": {}, "dynamicDependencies": {}, "types": "Index.d.ets", "artifactType": "obfuscation", "metadata": {"byteCodeHar": true, "sourceRoots": ["./src/main"], "debug": false, "declarationEntry": [], "useNormalizedOHMUrl": true}, "compatibleSdkVersion": 12, "compatibleSdkType": "HarmonyOS", "obfuscated": true}