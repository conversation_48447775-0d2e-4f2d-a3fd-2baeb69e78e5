import { AIOCrashDeviceInfo } from "./src/main/ets/utils/AIOCrashDeviceInfo";
import { AIOCrashHistory } from "./src/main/ets/AIOCrashHistory";
import { AIOCrashWatcher } from "./src/main/ets/AIOCrashWatcher";
class aio_crashsdk {
    static setup(b) {
    }
    static getFunctionsBinder() {
        const a = new Map();
        a.set("AIOCrashWatcher.Setup", AIOCrashWatcher.Setup);
        a.set("AIOCrashWatcher.SetContext", AIOCrashWatcher.SetContext);
        a.set("AIOCrashHistory.Query", AIOCrashHistory.Query);
        a.set("AIOCrashDeviceInfo.getVersionId", AIOCrashDeviceInfo.getVersionId);
        a.set("AIOCrashDeviceInfo.getProductModel", AIOCrashDeviceInfo.getProductModel);
        a.set("AIOCrashDeviceInfo.getSoftwareModel", AIOCrashDeviceInfo.getSoftwareModel);
        a.set("AIOCrashDeviceInfo.getHardwareModel", AIOCrashDeviceInfo.getHardwareModel);
        a.set("AIOCrashDeviceInfo.getDistributionOSVersion", AIOCrashDeviceInfo.getDistributionOSVersion);
        a.set("AIOCrashDeviceInfo.getDistributionOSApiVersion", AIOCrashDeviceInfo.getDistributionOSApiVersion);
        a.set("AIOCrashDeviceInfo.getSDKApiVersion", AIOCrashDeviceInfo.getSDKApiVersion);
        a.set("AIOCrashDeviceInfo.getODID", AIOCrashDeviceInfo.getODID);
        a.set("AIOCrashDeviceInfo.getABIList", AIOCrashDeviceInfo.getABIList);
        a.set("AIOCrashDeviceInfo.getBrand", AIOCrashDeviceInfo.getBrand);
        a.set("AIOCrashDeviceInfo.getModel", AIOCrashDeviceInfo.getModel);
        a.set("AIOCrashDeviceInfo.getOSName", AIOCrashDeviceInfo.getOSName);
        a.set("AIOCrashDeviceInfo.getOSVersion", AIOCrashDeviceInfo.getOSVersion);
        a.set("AIOCrashDeviceInfo.getOSSDKApiVersion", AIOCrashDeviceInfo.getOSSDKApiVersion);
        a.set("AIOCrashDeviceInfo.getApplicationBundleName", AIOCrashDeviceInfo.getApplicationBundleName);
        return a;
    }
}
export default aio_crashsdk;
