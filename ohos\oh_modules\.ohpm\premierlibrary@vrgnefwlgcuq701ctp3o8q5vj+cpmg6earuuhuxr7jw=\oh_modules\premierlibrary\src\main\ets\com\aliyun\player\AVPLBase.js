export class AVPLBase {
    constructor(d20, e20) {
        this.mNativePlayer = this.getNativePlayerWithContext(d20, e20);
        this.mPreRenderPlayer = this.getPrerenderPlayerWithContext(d20, e20);
        this.mListPlayer = this.createListPlayer(d20, e20, this.mNativePlayer.getNativeContextAddr(), this.mPreRenderPlayer.getNativeContextAddr());
    }
    getCurrentPlayer() {
        let c20 = this.getCurrentPlayerIndex();
        if (c20 == 1) {
            return this.mPreRenderPlayer;
        }
        else {
            return this.mNativePlayer;
        }
    }
    getCurrentPrerenderPlayer(b20) {
        if (b20 == 0) {
            return this.mNativePlayer;
        }
        else if (b20 == 1) {
            return this.mPreRenderPlayer;
        }
        return undefined;
    }
    getCorePlayer() {
        return this.mListPlayer;
    }
    getNativePlayer() {
        return this.mNativePlayer;
    }
    getPrerenderPlayer() {
        return this.mPreRenderPlayer;
    }
    removeSource(a20) {
        this.mListPlayer?.removeSource(a20);
    }
    clear() {
        this.mListPlayer?.clear();
    }
    getCurrentUid() {
        return this.mListPlayer?.getCurrentUid();
    }
    setMaxPreloadMemorySizeMB(z19) {
        this.mListPlayer?.setMaxPreloadMemorySizeMB(z19);
    }
    getMaxPreloadMemorySizeMB() {
        return this.mListPlayer?.getMaxPreloadMemorySizeMB();
    }
    SetMultiBitratesMode(y19) {
        this.mListPlayer?.SetMultiBitratesMode(y19);
    }
    GetMultiBitratesMode() {
        return this.mListPlayer?.GetMultiBitratesMode();
    }
    setPreloadCount(x19) {
        this.mListPlayer?.setPreloadCount(x19);
    }
    setPreloadScene(w19) {
        this.mListPlayer?.setPreloadScene(w19);
    }
    enablePreloadStrategy(u19, v19) {
        this.mListPlayer?.enablePreloadStrategy(u19, v19);
    }
    setPreloadStrategy(s19, t19) {
        this.mListPlayer?.setPreloadStrategyParam(s19, t19);
    }
    setPreloadCountWithPrevAndNext(q19, r19) {
        this.mListPlayer?.setPreloadCountWithPrevAndNext(q19, r19);
    }
    getNativeContextAddr() {
        return this.getCurrentPlayer()?.getNativeContextAddr();
    }
    prepare() {
        this.getCurrentPlayer().prepare();
    }
    start() {
        this.getCurrentPlayer()?.start();
    }
    pause() {
        this.getCurrentPlayer().pause();
    }
    stop() {
        this.getCurrentPlayer().stop();
    }
    setAutoPlay(p19) {
        this.getCurrentPlayer().setAutoPlay(p19);
    }
    setSurfaceId(o19) {
        this.getCurrentPlayer().setSurfaceId(o19);
    }
    setSpeed(n19) {
        this.getCurrentPlayer().setSpeed(n19);
    }
    setVolume(m19) {
        this.getCurrentPlayer().setVolume(m19);
    }
    getVolume() {
        return this.getCurrentPlayer().getVolume();
    }
    seekTo(k19, l19) {
        this.getCurrentPlayer().seekTo(k19, l19);
    }
    setStartTime(i19, j19) {
        this.getCurrentPlayer().setStartTime(i19, j19);
    }
    getDuration() {
        return this.getCurrentPlayer().getDuration();
    }
    getPlayedDuration() {
        return this.getCurrentPlayer().getPlayedDuration();
    }
    getCurrentPosition() {
        return this.getCurrentPlayer().getCurrentPosition();
    }
    getBufferedPosition() {
        return this.getCurrentPlayer().getBufferedPosition();
    }
    getPlayerStatus() {
        return this.getCurrentPlayer().getPlayerStatus();
    }
    enableHardwareDecoder(h19) {
        this.getCurrentPlayer().enableHardwareDecoder(h19);
    }
    release() {
        this.mListPlayer?.release();
        this.mNativePlayer?.release();
        this.mPreRenderPlayer?.release();
    }
    releaseAsync() {
        this.release();
    }
    setMute(g19) {
        this.getCurrentPlayer().setMute(g19);
    }
    isMuted() {
        return this.getCurrentPlayer().isMuted();
    }
    setScaleMode(f19) {
        this.getCurrentPlayer().setScaleMode(f19);
    }
    getScaleMode() {
        return this.getCurrentPlayer().getScaleMode();
    }
    setLoop(e19) {
        this.getCurrentPlayer().setLoop(e19);
    }
    isLoop() {
        return this.getCurrentPlayer().isLoop();
    }
    getVideoWidth() {
        return this.getCurrentPlayer().getVideoWidth();
    }
    getVideoHeight() {
        return this.getCurrentPlayer().getVideoHeight();
    }
    getVideoRotation() {
        return this.getCurrentPlayer().getVideoRotation();
    }
    reload() {
        this.getCurrentPlayer().reload();
    }
    setRotateMode(d19) {
        this.getCurrentPlayer().setRotateMode(d19);
    }
    getRotateMode() {
        return this.getCurrentPlayer().getRotateMode();
    }
    setMirrorMode(c19) {
        this.getCurrentPlayer().setMirrorMode(c19);
    }
    getMirrorMode() {
        return this.getCurrentPlayer().getMirrorMode();
    }
    setAlphaRenderMode(b19) {
        this.getCurrentPlayer().setAlphaRenderMode(b19);
    }
    getAlphaRenderMode() {
        return this.getCurrentPlayer().getAlphaRenderMode();
    }
    setVideoBackgroundColor(a19) {
        this.getCurrentPlayer().setVideoBackgroundColor(a19);
    }
    getSpeed() {
        return this.getCurrentPlayer().getSpeed();
    }
    isAutoPlay() {
        return this.getCurrentPlayer().isAutoPlay();
    }
    setConfig(z18) {
        if (this.mNativePlayer != null) {
            this.mNativePlayer.setConfig(z18);
        }
        if (this.mPreRenderPlayer != null) {
            this.mPreRenderPlayer.setConfig(z18);
        }
    }
    getConfig() {
        return this.getCurrentPlayer().getConfig();
    }
    setOption(x18, y18) {
        this.getCurrentPlayer().setOption(x18, y18);
    }
    setOptionNum(v18, w18) {
        this.getCurrentPlayer().setOptionNum(v18, w18);
    }
    getOption(u18) {
        return this.getCurrentPlayer().getOption(u18);
    }
    selectTrack(t18) {
        this.getCurrentPlayer()?.selectTrack(t18);
    }
    switchStream(s18) {
        this.getCurrentPlayer()?.switchStream(s18);
    }
    getMediaInfo() {
        return this.getCurrentPlayer()?.getMediaInfo();
    }
    getSubMediaInfo() {
        return this.getCurrentPlayer()?.getSubMediaInfo();
    }
    currentTrack(r18) {
        return this.getCurrentPlayer()?.currentTrack(r18);
    }
    addExtSubtitle(q18) {
        this.getCurrentPlayer()?.addExtSubtitle(q18);
    }
    selectExtSubtitle(o18, p18) {
        this.getCurrentPlayer()?.selectExtSubtitle(o18, p18);
    }
    setStreamDelay(m18, n18) {
        this.getCurrentPlayer()?.setStreamDelay(m18, n18);
    }
    setMaxAccurateSeekDelta(l18) {
        this.getCurrentPlayer()?.setMaxAccurateSeekDelta(l18);
    }
    setCacheConfig(k18) {
        this.getCurrentPlayer()?.setCacheConfig(k18);
    }
    setIPResolveType(j18) {
        this.getCurrentPlayer()?.setIPResolveType(j18);
    }
    setFastStart(i18) {
        this.getCurrentPlayer()?.setFastStart(i18);
    }
    snapShot() {
        this.getCurrentPlayer()?.snapShot();
    }
    clearScreen() {
        this.getCurrentPlayer()?.clearScreen();
    }
    getCacheFilePathByUrl(h18) {
        return this.getCurrentPlayer()?.getCacheFilePathByUrl(h18) || '';
    }
    getCacheFilePathByVid(d18, e18, f18, g18) {
        return this.getCurrentPlayer()?.getCacheFilePathByVid(d18, e18, f18, g18) || '';
    }
    getPropertyString(c18) {
        return this.getCurrentPlayer()?.getPropertyString(c18) || '';
    }
    setDefaultBandWidth(b18) {
        this.getCurrentPlayer()?.setDefaultBandWidth(b18);
    }
    sendCustomEvent(a18) {
        this.getCurrentPlayer()?.sendCustomEvent(a18);
    }
    setVideoTag(z17) {
        this.getCurrentPlayer()?.setVideoTag(z17);
    }
    setUserData(y17) {
        this.getCurrentPlayer()?.setUserData(y17);
    }
    setTraceId(x17) {
        this.getCurrentPlayer()?.setTraceId(x17);
    }
    getUserData() {
        return this.getCurrentPlayer()?.getUserData() || '';
    }
    setOnPreparedListener(w17) {
        this.getCurrentPlayer().setOnPreparedListener(w17);
    }
    setOnInfoListener(v17) {
        this.getCurrentPlayer().setOnInfoListener(v17);
    }
    setOnRenderingStartListener(u17) {
        this.getCurrentPlayer().setOnRenderingStartListener(u17);
    }
    setOnStateChangedListener(t17) {
        this.getCurrentPlayer().setOnStateChangedListener(t17);
    }
    setOnCompletionListener(s17) {
        this.getCurrentPlayer().setOnCompletionListener(s17);
    }
    setOnLoadingStatusListener(r17) {
        this.getCurrentPlayer().setOnLoadingStatusListener(r17);
    }
    setOnAVNotSyncStatusListener(q17) {
        this.getCurrentPlayer()?.setOnAVNotSyncStatusListener(q17);
    }
    setOnErrorListener(p17) {
        this.getCurrentPlayer().setOnErrorListener(p17);
    }
    setOnVideoSizeChangedListener(o17) {
        this.getCurrentPlayer().setOnVideoSizeChangedListener(o17);
    }
    setOnSeekCompleteListener(n17) {
        this.getCurrentPlayer().setOnSeekCompleteListener(n17);
    }
    setOnSubtitleDisplayListener(m17) {
        this.getCurrentPlayer().setOnSubtitleDisplayListener(m17);
    }
    setOnVideoRenderedListener(l17) {
        this.getCurrentPlayer().setOnVideoRenderedListener(l17);
    }
    setOnAudioInterruptEventListener(k17) {
        this.getCurrentPlayer().setOnAudioInterruptEventListener(k17);
    }
    setOnSubTrackReadyListener(j17) {
        this.getCurrentPlayer()?.setOnSubTrackReadyListener(j17);
    }
    setOnTrackReadyListener(i17) {
        this.getCurrentPlayer()?.setOnTrackReadyListener(i17);
    }
    setOnTrackChangedListener(h17) {
        this.getCurrentPlayer()?.setOnTrackChangedListener(h17);
    }
    setOnSnapShotListener(g17) {
        this.getCurrentPlayer()?.setOnSnapShotListener(g17);
    }
    setOnSeiDataListener(f17) {
        this.getCurrentPlayer()?.setOnSeiDataListener(f17);
    }
    setOnStreamSwitchedListener(e17) {
        this.getCurrentPlayer()?.setOnStreamSwitchedListener(e17);
    }
}
