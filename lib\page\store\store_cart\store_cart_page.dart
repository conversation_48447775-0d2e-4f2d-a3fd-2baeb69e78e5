import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wuling_flutter_app/api/store/store_api.dart';
import 'package:wuling_flutter_app/api/store/store_cart_api.dart';
import 'package:wuling_flutter_app/api/store/store_coupon_api.dart';
import 'package:wuling_flutter_app/common/action.dart';
import 'package:wuling_flutter_app/models/global_data.dart';
import 'package:wuling_flutter_app/models/index.dart';
import 'package:wuling_flutter_app/models/store/expired_commodity_list.dart';
import 'package:wuling_flutter_app/models/store/store_cart_list.dart';
import 'package:wuling_flutter_app/models/store/store_coupon/store_coupon_model.dart';
import 'package:wuling_flutter_app/models/store/store_item_model.dart';
import 'package:wuling_flutter_app/page/store/store_detail/store_detail_page.dart';
import 'package:wuling_flutter_app/page/store/store_navbar.dart';
import 'package:wuling_flutter_app/utils/manager/login_manager.dart';
import 'package:wuling_flutter_app/widgets/common/custom_refresher.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:wuling_flutter_app/widgets/webview/webview.dart';
import 'package:flutter_uikit/ui_overlay.dart';
import 'package:wuling_flutter_app/utils/manager/dialog_manager.dart';
import 'package:wuling_flutter_app/widgets/common/custom_dialog.dart';

import '../../../utils/manager/loading_manager.dart';
import '../../../constant/constant.dart';
import '../../../utils/manager/log_manager.dart';
import '../../../utils/manager/notification_manager.dart';

class StoreCartPage extends StatefulWidget {
  const StoreCartPage({super.key});

  @override
  State<StoreCartPage> createState() => _StoreCartPageState();
}

class _StoreCartPageState extends State<StoreCartPage> {
  final RefreshController _refreshController = RefreshController();
  List<CommodityList> cartItems = [];
  bool isAllSelected = true;
  bool isEditing = false;
  StoreCartList? storeCartList;
  List<ExpiredCommodityList> expiredCommodityList = [];
  List<StoreItemModel> recommendList = [];

  @override
  void initState() {
    super.initState();
    getCartList();
    getRecommendList();
    // 初始化为空列表
    cartItems = [];

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _refreshController.requestRefresh();
    });
  }

  //获取推荐商品列表
  getRecommendList() async {
    recommendList = await storeApi.getStoreRecommend(2);
    //print("recommendList的结果:" + jsonEncode(recommendList));
    setState(() {});
  }

  //获取购物车列表
  getCartList() async {
    final Map<String, dynamic> body = {"channelSourceId": 0};

    try {
      storeCartList = await storeApi.getCartList(body);
      //print("storeCartList的结果:" +
      //   jsonEncode(storeCartList?.cartInfo?.first.commodityList));

      if (storeCartList != null) {
        setState(() {
          cartItems.clear();
          // 获取正常商品，只获取isExpired为0的商品
          if (storeCartList!.cartInfo != null &&
              storeCartList!.cartInfo!.isNotEmpty) {
            for (var item in storeCartList!.cartInfo!) {
              // 只添加isExpired为0的正常商品
              if (item.isExpired == 0) {
                cartItems.addAll(item.commodityList ?? []);
              }
            }
            // 同步全选状态
            isAllSelected = storeCartList!.isAllSelect == 1;
            _checkAllSelected();
          }

          // 获取失效商品
          if (storeCartList!.expiredCommodityList != null &&
              storeCartList!.expiredCommodityList!.isNotEmpty) {
            expiredCommodityList =
                (storeCartList!.expiredCommodityList! as List)
                    .map((item) => ExpiredCommodityList.fromJson(item))
                    .toList();
            //print("失效商品的结果:" + jsonEncode(expiredCommodityList));
          }
        });
      } else {
        //print("购物车数据为空或格式不正确");
      }
    } catch (e) {
      //print("获取购物车数据出错: $e");
      setState(() {
        cartItems = [];
      });
    }
  }

  // 计算总价
  double get totalPrice {
    // 使用CommodityList的sellPrice和quantity
    return cartItems.where((item) => item.isCheck == 1).fold(0.0,
        (sum, item) => sum + ((item.sellPrice ?? 0.0) * (item.quantity ?? 1)));
  }

  // 计算选中的商品数量
  int get selectedItemCount {
    return cartItems.where((item) => item.isCheck == 1).length;
  }

  // 检查是否全选
  void _checkAllSelected() {
    if (cartItems.isEmpty) {
      setState(() {
        isAllSelected = false;
      });
      return;
    }

    // 检查是否所有商品都被选中
    bool allSelected = cartItems.every((item) => item.isCheck == 1);

    // 只有当状态发生变化时才更新UI
    if (isAllSelected != allSelected) {
      setState(() {
        isAllSelected = allSelected;
      });
    }
  }

  // 全选/取消全选
  void _toggleSelectAll() {
    setState(() {
      // 反转全选状态
      isAllSelected = !isAllSelected;

      // 更新所有商品的选中状态
      for (var item in cartItems) {
        item.isCheck = isAllSelected ? 1 : 0;
      }
      // TODO: 调用 storeApi.checkOrUnCheckToCart() 接口，传入所有商品ID和选中状态
      List<int> cartIds = cartItems.map((item) => item.cartId ?? 0).toList();
      storeApi.checkOrUnCheckToCart(body: {
        "channelSourceId": 0,
        "cartIds": cartIds,
        "isCheck": isAllSelected ? 1 : 0
      });
    });
  }

  // 刷新数据
  void _onRefresh() async {
    try {
      final Map<String, dynamic> body = {"channelSourceId": '0'};
      storeCartList = await storeApi.getCartList(body);

      setState(() {
        // 直接使用API返回的CommodityList，只获取isExpired为0的正常商品
        cartItems.clear();
        for (var item in storeCartList!.cartInfo!) {
          if (item.isExpired == 0) {
            cartItems.addAll(item.commodityList ?? []);
          }
        }

        // 同步全选状态
        isAllSelected = storeCartList!.isAllSelect == 1;
        _checkAllSelected();

        // 刷新失效商品数据
        if (storeCartList!.expiredCommodityList != null &&
            storeCartList!.expiredCommodityList!.isNotEmpty) {
          expiredCommodityList = (storeCartList!.expiredCommodityList! as List)
              .map((item) => ExpiredCommodityList.fromJson(item))
              .toList();
        } else {
          expiredCommodityList.clear();
        }
      });
    } catch (e) {
      LogManager().debug("刷新购物车数据出错: $e");
    } finally {
      _refreshController.refreshCompleted();
    }
  }

  // 更新商品数量
  void _updateItemQuantity(CommodityList item, bool isIncrease) {
    int originalQuantity = item.quantity ?? 1;

    setState(() {
      if (isIncrease) {
        // 增加数量
        item.quantity = originalQuantity + 1;
      } else if (originalQuantity > 1) {
        // 减少数量，但不能小于1
        item.quantity = originalQuantity - 1;
      }

      // TODO: 调用更新购物车数量的API
      storeApi.updateCartItemQuantity(body: {
        "channelSourceId": 0,
        "cartId": item.cartId ?? 0,
        "quantity": item.quantity
      }).catchError((error) {
        //print("更新数量出错: $error");
        // 如果API调用失败，回退状态
        setState(() {
          item.quantity = originalQuantity;
        });
      });
    });
  }

  // 删除商品
  void _removeItem(CommodityList item) {
    setState(() {
      cartItems.remove(item);

      storeApi.deleteCartItem(body: {
        "channelSourceId": 0,
        "cartIds": [item.cartId ?? 0]
      }).then((_) {
        // 发送购物车数量变化通知
        NotificationManager().postNotification(
            Constant.NOTIFICATION_CART_COUNT_CHANGED,
            userInfo: {'action': 'remove', 'quantity': 1});
      }).catchError((error) {
        setState(() {
          cartItems.add(item);
        });
      });

      _checkAllSelected();
    });
  }

  // 切换编辑模式
  void _toggleEditMode() {
    setState(() {
      isEditing = !isEditing;

      // 进入编辑模式时，所有商品变为未选中状态
      // 在编辑模式下，用户可以手动选择想要删除的商品，或者使用全选按钮选择全部商品
      if (isEditing) {
        for (var item in cartItems) {
          item.isCheck = 0;
        }
        isAllSelected = false;
      } else {
        // 退出编辑模式时，恢复所有商品为选中状态
        for (var item in cartItems) {
          item.isCheck = 1;
        }
        isAllSelected = true;
      }
    });
  }

  // 显示优惠券弹窗
  void _showCouponDialog(BuildContext context) {
    // 显示加载指示器
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return const Center(child: CircularProgressIndicator());
      },
    );

    // 获取所有选中商品的ID
    List<int> commodityIds = cartItems
        .where((item) => item.isCheck == 1)
        .map((item) => item.commodityId ?? 0)
        .where((id) => id > 0)
        .toList();

    // 没有选中商品时获取所有商品
    if (commodityIds.isEmpty) {
      commodityIds = cartItems
          .map((item) => item.commodityId ?? 0)
          .where((id) => id > 0)
          .toList();
    }

    // 确保有商品ID可以查询
    if (commodityIds.isEmpty) {
      // 关闭加载指示器
      Navigator.of(context).pop();

      // 显示无优惠券的信息
      _showEmptyCouponDialog(context);
      return;
    }

    // 查询所有商品的优惠券
    Future.wait(commodityIds.map((id) => storeCouponApi.couponList(id)))
        .then((results) {
      // 关闭加载指示器
      Navigator.of(context).pop();

      // 整合所有商品的优惠券
      List<StoreCouponModel> allCoupons = [];
      for (var coupons in results) {
        allCoupons.addAll(coupons);
      }

      // 去除重复的优惠券（根据ID去重）
      Map<int, StoreCouponModel> uniqueCouponsMap = {};
      for (var coupon in allCoupons) {
        uniqueCouponsMap[coupon.id] = coupon;
      }
      List<StoreCouponModel> uniqueCoupons = uniqueCouponsMap.values.toList();

      // 显示优惠券弹窗
      _displayCouponModal(context, uniqueCoupons);
    }).catchError((error) {
      // 关闭加载指示器
      Navigator.of(context).pop();
      //  print("获取优惠券失败: $error");

      // 显示无优惠券的信息
      _showEmptyCouponDialog(context);
    });
  }

  // 显示无优惠券的弹窗
  void _showEmptyCouponDialog(BuildContext context) {
    final appBarHeight = AppBar().preferredSize.height;
    final statusBarHeight = MediaQuery.of(context).padding.top;
    final couponBarHeight = 44.0;

    final modalHeight = MediaQuery.of(context).size.height -
        (appBarHeight + statusBarHeight + couponBarHeight);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      builder: (BuildContext context) {
        return Container(
          height: modalHeight,
          child: Column(
            children: [
              // 顶部标题栏
              Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                child: Stack(
                  children: [
                    const Align(
                      alignment: Alignment.center,
                      child: Text(
                        '优惠券',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    Positioned(
                      right: 0,
                      top: 0,
                      child: GestureDetector(
                        onTap: () => Navigator.pop(context),
                        child: const Icon(
                          Icons.close,
                          size: 24,
                          color: Color(0xFF333333),
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              Divider(height: 1, color: const Color(0xFFEEEEEE)),

              // 分割线和提示文字
              Container(
                padding: const EdgeInsets.symmetric(vertical: 20),
                child: Row(
                  children: [
                    Expanded(
                      child: Container(
                        height: 1,
                        color: const Color(0xFFEEEEEE),
                      ),
                    ),
                    const Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        '以下券可用于购物车中的商品',
                        style: TextStyle(
                          color: Colors.red,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    Expanded(
                      child: Container(
                        height: 1,
                        color: const Color(0xFFEEEEEE),
                      ),
                    ),
                  ],
                ),
              ),

              // 空状态
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.network(
                        'https://img.icons8.com/color/96/null/empty-box.png',
                        width: 120,
                        height: 120,
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        '您暂无可以使用的优惠券～',
                        style: TextStyle(
                          color: Color(0xFF999999),
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  // 显示优惠券列表弹窗
  void _displayCouponModal(
      BuildContext context, List<StoreCouponModel> coupons) {
    final appBarHeight = AppBar().preferredSize.height;
    final statusBarHeight = MediaQuery.of(context).padding.top;
    final couponBarHeight = 44.0;

    final modalHeight = MediaQuery.of(context).size.height -
        (appBarHeight + statusBarHeight + couponBarHeight);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (context, setState) {
          return Container(
            height: modalHeight,
            child: Column(
              children: [
                // 顶部标题栏
                Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                  child: Stack(
                    children: [
                      const Align(
                        alignment: Alignment.center,
                        child: Text(
                          '优惠券',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Positioned(
                        right: 0,
                        top: 0,
                        child: GestureDetector(
                          onTap: () => Navigator.pop(context),
                          child: const Icon(
                            Icons.close,
                            size: 24,
                            color: Color(0xFF333333),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                Divider(height: 1, color: const Color(0xFFEEEEEE)),

                // 分割线和提示文字
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 20),
                  child: Row(
                    children: [
                      Expanded(
                        child: Container(
                          height: 1,
                          color: const Color(0xFFEEEEEE),
                        ),
                      ),
                      const Padding(
                        padding: EdgeInsets.symmetric(horizontal: 16),
                        child: Text(
                          '以下券可用于购物车中的商品',
                          style: TextStyle(
                            color: Colors.red,
                            fontSize: 14,
                          ),
                        ),
                      ),
                      Expanded(
                        child: Container(
                          height: 1,
                          color: const Color(0xFFEEEEEE),
                        ),
                      ),
                    ],
                  ),
                ),

                // 优惠券列表
                Expanded(
                  child: coupons.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Image.network(
                                'https://img.icons8.com/color/96/null/empty-box.png',
                                width: 120,
                                height: 120,
                              ),
                              const SizedBox(height: 16),
                              const Text(
                                '您暂无可以使用的优惠券～',
                                style: TextStyle(
                                  color: Color(0xFF999999),
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: coupons.length,
                          itemBuilder: (context, index) {
                            return _buildCouponModelItem(
                                coupons[index], setState);
                          },
                        ),
                ),
              ],
            ),
          );
        });
      },
    );
  }

  // 构建优惠券项 - 使用StoreCouponModel
  Widget _buildCouponModelItem(StoreCouponModel coupon, StateSetter setState) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            offset: const Offset(0, 2),
            blurRadius: 6,
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              // 左侧金额部分
              Container(
                width: 100,
                height: 110,
                color: Colors.blue, // 使用蓝色背景，与StoreCoupon组件一致
                child: Stack(
                  children: [
                    Center(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            "${coupon.discountAmount.toStringAsFixed(2)}",
                            style: const TextStyle(
                              fontSize: 25,
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          Text(
                            "元",
                            style: const TextStyle(
                              fontSize: 10,
                              color: Colors.white,
                              height: 2.0,
                            ),
                          )
                        ],
                      ),
                    ),
                    Align(
                      alignment: Alignment.bottomCenter,
                      child: Padding(
                        padding: const EdgeInsets.only(bottom: 20),
                        child: Text(
                          "立减",
                          style: const TextStyle(
                            fontSize: 10,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              ),
              // 右侧详情部分
              Expanded(
                flex: 1,
                child: Container(
                  height: 110,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        coupon.name,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Container(
                        height: 35,
                        padding: const EdgeInsets.only(top: 2),
                        decoration: const BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: Color(0xFFEEEEEE),
                            ),
                          ),
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                border: Border.all(
                                  color: Colors.blue,
                                  width: 0.5,
                                ),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              padding: const EdgeInsets.only(
                                  top: 1, left: 5, right: 5),
                              child: Text(
                                coupon.channelName,
                                style: const TextStyle(
                                  fontSize: 10,
                                  color: Color(0xFF2196F3),
                                ),
                              ),
                            ),
                            ElevatedButton(
                              onPressed: coupon.isReceive == 0
                                  ? () {
                                      // 领取优惠券
                                      storeCouponApi.getCoupon({
                                        "issueId": coupon.id,
                                        "issueCode": coupon.issueCode,
                                        "dealerId": coupon.dealerId,
                                        "dealerCode": coupon.dealerCode,
                                        "serviceCode": coupon.serviceCode,
                                      }).then((value) {
                                        // 更新优惠券状态
                                        setState(() {
                                          coupon.isGet = true;
                                        });

                                        // 显示领取成功的提示
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          const SnackBar(
                                            content: Text('优惠券领取成功'),
                                            duration: Duration(seconds: 2),
                                          ),
                                        );
                                      }).catchError((error) {
                                        //  print("领取优惠券失败: $error");
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          const SnackBar(
                                            content: Text('优惠券领取失败'),
                                            duration: Duration(seconds: 2),
                                          ),
                                        );
                                      });
                                    }
                                  : null,
                              style: ElevatedButton.styleFrom(
                                backgroundColor: coupon.isReceive == 0
                                    ? Colors.blue
                                    : Colors.grey,
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 10, vertical: 5),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(30),
                                ),
                                elevation: 0,
                              ),
                              child: Text(
                                coupon.isReceive == 0 ? "去领取" : "已领取",
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 10,
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                '${_formatDate(coupon.beginTime)}',
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Color(0xFF999999),
                                ),
                              ),
                              Text(
                                '${_formatDate(coupon.endTim)}',
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Color(0xFF999999),
                                ),
                              ),
                            ],
                          ),
                          IconButton(
                            onPressed: () {
                              setState(() {
                                coupon.show = !coupon.show;
                              });
                            },
                            icon: Icon(
                              coupon.show
                                  ? Icons.keyboard_arrow_up
                                  : Icons.keyboard_arrow_down,
                              size: 20,
                            ),
                            padding: EdgeInsets.zero,
                            constraints: const BoxConstraints(),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          // 优惠券详情（点击展开/收起）
          coupon.show
              ? Container(
                  padding: const EdgeInsets.all(16),
                  width: double.infinity,
                  alignment: Alignment.topLeft,
                  child: Text(
                    coupon.introduction.isEmpty
                        ? '该优惠券可在购买商品时使用，详情请查看商品介绍。'
                        : coupon.introduction,
                    style: const TextStyle(fontSize: 12),
                  ),
                )
              : const SizedBox.shrink(),
        ],
      ),
    );
  }

  // 格式化日期
  String _formatDate(num timestamp) {
    final date = DateTime.fromMillisecondsSinceEpoch(timestamp.toInt());
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: UINavbar(
        text: "购物车",
        right: TextButton(
          onPressed: cartItems.isEmpty ? null : _toggleEditMode,
          child: Text(
            isEditing ? "完成" : "编辑",
            style: TextStyle(
              color: cartItems.isEmpty ? Colors.grey : const Color(0xFF333333),
              fontSize: 16,
            ),
          ),
        ),
      ),
      bottomNavigationBar: cartItems.isEmpty
          ? null
          : Container(
              height: 60 + MediaQuery.of(context).padding.bottom, // 添加底部安全区域
              padding: EdgeInsets.fromLTRB(
                16,
                0,
                16,
                MediaQuery.of(context).padding.bottom, // 底部安全区域
              ),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 5,
                    offset: const Offset(0, -1),
                  ),
                ],
              ),
              child: Container(
                height: 60,
                child: isEditing
                    ? _buildEditModeBottomBar()
                    : _buildNormalModeBottomBar(),
              ),
            ),
      body: Column(
        children: [
          // 优惠券提示条 - 仅在有商品时显示
          if (cartItems.isNotEmpty)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              color: const Color(0xFFF8F8F8),
              child: Row(
                children: [
                  const Icon(Icons.card_giftcard, color: Colors.red, size: 20),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      '购物车中商品有优惠券可领取哦～',
                      style: TextStyle(color: Colors.black87),
                    ),
                  ),
                  InkWell(
                    onTap: () {
                      _showCouponDialog(context);
                    },
                    child: Row(
                      children: const [
                        Text('优惠券', style: TextStyle(color: Colors.red)),
                        Icon(Icons.chevron_right, color: Colors.red, size: 20),
                      ],
                    ),
                  ),
                ],
              ),
            ),

          // 内容部分 - 不再需要额外的底部padding
          Expanded(
            child: cartItems.isEmpty
                ? _buildEmptyCartView()
                : CustomSmartRefresher(
                    controller: _refreshController,
                    onRefresh: _onRefresh,
                    child: ListView.builder(
                      itemCount: cartItems.length +
                          (expiredCommodityList.isEmpty ? 0 : 1) +
                          (expiredCommodityList.isEmpty
                              ? 0
                              : expiredCommodityList.length) +
                          1,
                      itemBuilder: (context, index) {
                        if (index < cartItems.length) {
                          return _buildCartItem(cartItems[index], index);
                        } else if (index == cartItems.length &&
                            expiredCommodityList.isNotEmpty) {
                          return _buildExpiredItemsTitle();
                        } else if (index > cartItems.length &&
                            index <
                                cartItems.length +
                                    expiredCommodityList.length) {
                          int expiredIndex = index - cartItems.length - 1;
                          return _buildExpiredItem(
                              expiredCommodityList[expiredIndex], expiredIndex);
                        } else {
                          return _buildRecommendations();
                        }
                      },
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  // 修改_buildEmptyCartView方法
  Widget _buildEmptyCartView() {
    return CustomSmartRefresher(
      controller: _refreshController,
      onRefresh: _onRefresh,
      child: SingleChildScrollView(
        child: Column(
          children: [
            // 空购物车提示部分
            SizedBox(height: 40),
            // 空购物车图标
            Image.asset(
              'assets/images/common/empty_cart.png',
              width: 120,
              height: 120,
              errorBuilder: (context, error, stackTrace) {
                return Icon(
                  Icons.shopping_cart_outlined,
                  size: 120,
                  color: Colors.grey[300],
                );
              },
            ),
            const SizedBox(height: 16),
            const Text(
              '挑点喜欢的装进购物车',
              style: TextStyle(
                color: Color(0xFF999999),
                fontSize: 16,
              ),
            ),
            const SizedBox(height: 50),

            // 直接使用已有的推荐商品部分
            _buildRecommendations(),
          ],
        ),
      ),
    );
  }

  // 编辑模式下的底部栏
  Widget _buildEditModeBottomBar() {
    return Row(
      children: [
        // 全选按钮 - 用于一键选择所有商品，方便用户进行批量操作
        GestureDetector(
          onTap: _toggleSelectAll,
          child: Row(
            children: [
              Icon(
                isAllSelected ? Icons.check_circle : Icons.check_circle_outline,
                color: isAllSelected ? Colors.red : Colors.grey,
                size: 24,
              ),
              const SizedBox(width: 8),
              const Text('全选'),
            ],
          ),
        ),
        const Spacer(),
        // 清除按钮 - 用于清除当前选中的商品（不会直接操作，会有确认对话框）
        OutlinedButton(
          onPressed: () {
            // 显示一键清理半模态弹窗，而不是直接显示确认对话框
            _showClearItemsDialog(context);
          },
          style: OutlinedButton.styleFrom(
            side: const BorderSide(color: Color(0xFFCCCCCC)),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(24),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 32),
          ),
          child: const Text('清除', style: TextStyle(color: Color(0xFF333333))),
        ),
        const SizedBox(width: 16),
        // 删除按钮 - 用于删除当前选中的商品（只有当有商品被选中时才可用）
        ElevatedButton(
          onPressed: selectedItemCount > 0
              ? () {
                  // 删除所有选中的商品，点击后会显示确认对话框
                  showDialog(
                    context: context,
                    builder: (context) => Dialog(
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            vertical: 24, horizontal: 16),
                        width: 280,
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Text(
                              "确认要删除选中的商品吗?",
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            const SizedBox(height: 24),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                Expanded(
                                  child: TextButton(
                                    onPressed: () =>
                                        Navigator.of(context).pop(),
                                    style: TextButton.styleFrom(
                                      foregroundColor: const Color(0xFF333333),
                                    ),
                                    child: const Text(
                                      "取消",
                                      style: TextStyle(fontSize: 16),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: TextButton(
                                    onPressed: () {
                                      Navigator.of(context).pop();
                                      // 执行删除操作 - 当用户在编辑模式下，选择了部分商品后，点击删除按钮确认后，将删除这些商品
                                      setState(() {
                                        // 获取要删除的购物车商品ID列表
                                        List<int> cartIdsToRemove = cartItems
                                            .where((item) => item.isCheck == 1)
                                            .map((item) => item.cartId ?? 0)
                                            .toList();

                                        if (cartIdsToRemove.isNotEmpty) {
                                          // TODO: 这里应该调用批量删除商品的API
                                          storeApi.deleteCartItem(body: {
                                            "channelSourceId": 0,
                                            "cartIds": cartIdsToRemove
                                          }).then((_) {
                                            // 发送购物车数量变化通知
                                            NotificationManager().postNotification(
                                                Constant
                                                    .NOTIFICATION_CART_COUNT_CHANGED,
                                                userInfo: {
                                                  'action': 'batch_remove',
                                                  'quantity':
                                                      cartIdsToRemove.length
                                                });
                                          }).catchError((error) {
                                            // 如果API调用失败，回退状态
                                            setState(() {
                                              cartItems.addAll(cartIdsToRemove
                                                  .map((id) => cartItems
                                                      .firstWhere((item) =>
                                                          item.cartId == id)));
                                            });
                                          });
                                        }

                                        // 从本地列表中移除选中的商品
                                        cartItems.removeWhere(
                                            (item) => item.isCheck == 1);
                                        _checkAllSelected();
                                      });
                                    },
                                    style: TextButton.styleFrom(
                                      foregroundColor: Colors.red,
                                    ),
                                    child: const Text(
                                      "确定",
                                      style: TextStyle(fontSize: 16),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }
              : null, // 如果没有选中任何商品，删除按钮将被禁用
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(24),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 32),
          ),
          child: const Text('删除', style: TextStyle(fontSize: 16)),
        ),
      ],
    );
  }

  // 正常模式下的底部栏
  Widget _buildNormalModeBottomBar() {
    return Row(
      children: [
        // 全选
        GestureDetector(
          onTap: _toggleSelectAll,
          child: Row(
            children: [
              Icon(
                isAllSelected ? Icons.check_circle : Icons.check_circle_outline,
                color: isAllSelected ? Colors.red : Colors.grey,
                size: 24,
              ),
              const SizedBox(width: 8),
              const Text('全选'),
            ],
          ),
        ),
        const Spacer(),
        // 合计
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Row(
              children: [
                const Text('总计: ', style: TextStyle(fontSize: 16)),
                Text(
                  '¥ ${totalPrice.toStringAsFixed(2)}',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                ),
                Text(
                  ' (${selectedItemCount}件)',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(width: 16),
        // 去结算按钮
        ElevatedButton(
          onPressed: totalPrice > 0
              ? () {
                  // 获取所有选中的商品ID
                  List<int> selectedCartIds = cartItems
                      .where((item) => item.isCheck == 1)
                      .map((item) => item.cartId ?? 0)
                      .toList();

                  if (selectedCartIds.isEmpty) {
                    return;
                  }

                  // 构建跳转数据
                  Map<String, dynamic> paymentData = {
                    "orderSource": "购物车",
                    "orderSourceType": "9",
                    //订单总价
                    // "totalPrice": storeCartList!.cartAmount,
                    // "channelSourceId": 0,
                    //"cartIds": selectedCartIds,
                    // "addressId": 0, // 收货地址ID
                    // "couponId": 0,  // 优惠券ID
                  };
                  // 使用WebViewPage跳转到H5支付页面
                  NavigatorAction.init(
                    context,
                    view: WebViewPage(
                      url:
                          "${Constant.MALL_CENTER_WEB_VIEW_BASE_URL}Order/Confirm?paymentData=${jsonEncode(paymentData)}",
                      titleName: "订单确认",
                    ),
                    then: (v) {
                      _onRefresh();
                    },
                  );
                }
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(24),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
          ),
          child: const Text('去结算', style: TextStyle(fontSize: 16)),
        ),
      ],
    );
  }

  // 构建购物车商品项
  Widget _buildCartItem(CommodityList item, int index) {
    // 检查商品是否失效
    bool isItemExpired = (item.expiredReason ?? 0) > 0;
    bool isSelectable = !isItemExpired && item.isCheck != 2; // isCheck=2表示禁止选择

    return Slidable(
      // 只允许从右向左滑动，提供单个商品的快速删除功能
      endActionPane: ActionPane(
        motion: const DrawerMotion(),
        extentRatio: 0.25, // 删除按钮宽度占比
        children: [
          CustomSlidableAction(
            onPressed: (context) async {
              bool? confirm = await showDialog(
                context: context,
                builder: (BuildContext context) {
                  return Dialog(
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 24, horizontal: 16),
                      width: 280,
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Text(
                            "确认要删除这个宝贝吗?",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 24),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              Expanded(
                                child: TextButton(
                                  onPressed: () =>
                                      Navigator.of(context).pop(false),
                                  style: TextButton.styleFrom(
                                    foregroundColor: const Color(0xFF333333),
                                  ),
                                  child: const Text(
                                    "取消",
                                    style: TextStyle(fontSize: 16),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: TextButton(
                                  onPressed: () =>
                                      Navigator.of(context).pop(true),
                                  style: TextButton.styleFrom(
                                    foregroundColor: Colors.red,
                                  ),
                                  child: const Text(
                                    "确定",
                                    style: TextStyle(fontSize: 16),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  );
                },
              );

              if (confirm == true) {
                _removeItem(item);
              }
            },
            backgroundColor: Colors.red,
            foregroundColor: Colors.white,
            child: const Text(
              '删除',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
      child: GestureDetector(
        // 添加点击事件，跳转到商品详情页
        onTap: () {
          NavigatorAction.init(
            context,
            view: StoreDetailPage(
              id: item.commodityId ?? 0,
              code: 0,
            ),
          );
        },
        child: Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            border: Border(
                bottom: BorderSide(color: Color(0xFFEEEEEE), width: 0.5)),
          ),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    // 选择按钮 - 在编辑模式下，用户可以点击这个按钮选择要删除的商品
                    GestureDetector(
                      onTap: isSelectable
                          ? () {
                              setState(() {
                                // 切换选中状态
                                item.isCheck = item.isCheck == 1 ? 0 : 1;
                                // TODO: 调用 storeApi.checkOrUnCheckToCart() 接口
                                storeApi.checkOrUnCheckToCart(body: {
                                  "channelSourceId": 0,
                                  "cartIds": [item.cartId ?? 0],
                                  "isCheck": item.isCheck
                                }).then((_) {
                                  // 接口调用成功后更新全选状态
                                  _checkAllSelected();
                                }).catchError((error) {
                                  //  print("更新选中状态出错: $error");
                                  // 如果API调用失败，回退状态
                                  setState(() {
                                    item.isCheck = item.isCheck == 1 ? 0 : 1;
                                  });
                                });

                                // 更新全选状态
                                _checkAllSelected();
                              });
                            }
                          : null,
                      child: Icon(
                        item.isCheck == 1
                            ? Icons.check_circle
                            : Icons.check_circle_outline,
                        color: item.isCheck == 1
                            ? Colors.red
                            : (isSelectable
                                ? Colors.grey
                                : Colors.grey.shade300),
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    // 商品图片
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(4),
                        color: Colors.grey[200],
                      ),
                      child: Image.network(
                        (item.skuImage != null && item.skuImage!.isNotEmpty)
                            ? item.skuImage!
                            : (item.commodityImage ??
                                'https://via.placeholder.com/80'),
                        fit: BoxFit.cover,
                      ),
                    ),
                    const SizedBox(width: 12),
                    // 商品信息
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            item.commodityName ?? '',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 8),
                          // 商品规格
                          InkWell(
                            onTap: () {
                              // 添加登录状态检查
                              if (!GlobalData().isLogin) {
                                showNotLoginAlertDialog(context);
                                return;
                              }

                              // 验证必要参数
                              if (item.commodityId == null ||
                                  item.commodityId == 0) {
                                LoadingManager.showToast("商品信息异常，无法选择规格");
                                return;
                              }

                              // 点击商品规格时弹出SKU选择弹窗，传递当前SKU ID和数量
                              ShowAction.init(context)
                                  .showSKU(
                                      item.commodityId ?? 0, // 商品ID
                                      0, // 渠道码
                                      item.commodityName ?? '', // 商品名称
                                      // 优先使用skuImage，为空时使用commodityImage
                                      (item.skuImage != null &&
                                              item.skuImage!.isNotEmpty)
                                          ? item.skuImage!
                                          : (item.commodityImage ?? ''), // 商品图片
                                      item.commodityClassifyId ?? 1, // 商品分类ID
                                      currentSkuId: item.skuId, // 传递当前SKU ID
                                      currentQuantity: item.quantity) // 传递当前数量
                                  .then((value) {
                                if (value != null) {
                                  // 获取用户选择的SKU和数量
                                  final selectedSku = value['sku'];
                                  final quantity = value['quantity'] ?? 1;
                                  //  print("SKU是: ${jsonEncode(selectedSku)}");

                                  // 检查是否有任何变化
                                  bool hasSkuChanged =
                                      selectedSku?.skuId != null &&
                                          selectedSku.skuId != item.skuId;
                                  bool hasQuantityChanged =
                                      quantity != item.quantity;

                                  if (hasSkuChanged || hasQuantityChanged) {
                                    // 先删除原来的购物车商品
                                    storeApi.deleteCartItem(body: {
                                      "channelSourceId": 0,
                                      "cartIds": [item.cartId ?? 0]
                                    }).then((_) {
                                      // 删除成功后，调用加入购物车API重新添加商品
                                      return storeCartApi.addCart({
                                        "quantity": quantity,
                                        "commoditySkuId": selectedSku.skuId,
                                        "commodityId": selectedSku.id,
                                        "commodityClassifyId":
                                            item.commodityClassifyId ?? 1,
                                        "channelSourceId": 0,
                                        "logisticsTakeMode":
                                            item.logisticsTakeMode ?? 1,
                                        "logisticsServiceDealerId":
                                            item.serviceDealerId ?? 0,
                                      });
                                    }).then((_) {
                                      // 成功回调
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        SnackBar(
                                          content: Text(hasSkuChanged
                                              ? '规格更新成功'
                                              : '数量更新成功'),
                                          duration: Duration(seconds: 2),
                                        ),
                                      );

                                      // 刷新购物车列表以显示最新数据
                                      _onRefresh();
                                    }).catchError((error) {
                                      //  print("更新失败: ${jsonEncode(error)}");
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        const SnackBar(
                                          content: Text('更新失败，请重试'),
                                          duration: Duration(seconds: 2),
                                        ),
                                      );

                                      // 刷新购物车列表以确保数据一致性
                                      _onRefresh();
                                    });
                                  } else {
                                    // 如果既没有改变规格也没有改变数量，显示提示
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text('未发现任何更改'),
                                        duration: Duration(seconds: 1),
                                      ),
                                    );
                                  }
                                }
                              }).catchError((error) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('选择失败'),
                                    duration: Duration(seconds: 2),
                                  ),
                                );
                                LogManager().debug("showSKU调用失败: $error");
                              });
                            },
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 4),
                              decoration: BoxDecoration(
                                color: const Color(0xFFF5F5F5),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Flexible(
                                    child: Text(
                                      item.skuName?.isNotEmpty == true
                                          ? item.skuName!
                                          : '选择规格',
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  const Icon(Icons.keyboard_arrow_down,
                                      size: 16),
                                ],
                              ),
                            ),
                          ),
                          const SizedBox(height: 16),
                          // 价格和数量
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                '¥ ${(item.sellPrice ?? 0.0).toStringAsFixed(2)}',
                                style: TextStyle(
                                  color: Colors.red[700],
                                  fontWeight: FontWeight.bold,
                                  fontSize: 18,
                                ),
                              ),
                              // 数量控制
                              Row(
                                children: [
                                  InkWell(
                                    onTap: () {
                                      if ((item.quantity ?? 0) > 1) {
                                        _updateItemQuantity(item, false);
                                      }
                                    },
                                    child: Container(
                                      width: 32,
                                      height: 32,
                                      decoration: BoxDecoration(
                                        color: Colors.grey[200],
                                        borderRadius: const BorderRadius.only(
                                          topLeft: Radius.circular(4),
                                          bottomLeft: Radius.circular(4),
                                        ),
                                      ),
                                      alignment: Alignment.center,
                                      child: const Text('-',
                                          style: TextStyle(fontSize: 18)),
                                    ),
                                  ),
                                  Container(
                                    width: 40,
                                    height: 32,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      border: Border.symmetric(
                                        horizontal: BorderSide(
                                            color: Colors.grey[200]!),
                                      ),
                                    ),
                                    child: Text('${item.quantity ?? 1}'),
                                  ),
                                  InkWell(
                                    onTap: () {
                                      _updateItemQuantity(item, true);
                                    },
                                    child: Container(
                                      width: 32,
                                      height: 32,
                                      decoration: BoxDecoration(
                                        color: Colors.grey[200],
                                        borderRadius: const BorderRadius.only(
                                          topRight: Radius.circular(4),
                                          bottomRight: Radius.circular(4),
                                        ),
                                      ),
                                      alignment: Alignment.center,
                                      child: const Text('+',
                                          style: TextStyle(fontSize: 18)),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              // 收货方式
              Padding(
                padding: const EdgeInsets.fromLTRB(56, 0, 16, 16),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('收货方式',
                        style: TextStyle(fontSize: 14, color: Colors.grey)),
                    Flexible(
                      child: Text(
                        item.logisticsTakeModeStr ?? '送货到家',
                        style:
                            const TextStyle(fontSize: 14, color: Colors.grey),
                        textAlign: TextAlign.end,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建推荐商品部分
  Widget _buildRecommendations() {
    return Column(
      children: [
        // 标题分割线
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 80,
                height: 1,
                color: Colors.grey[300],
              ),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 12),
                child: Row(
                  children: const [
                    Icon(Icons.favorite, color: Colors.red, size: 18),
                    SizedBox(width: 8),
                    Text('你可能还喜欢'),
                  ],
                ),
              ),
              Container(
                width: 80,
                height: 1,
                color: Colors.grey[300],
              ),
            ],
          ),
        ),

        // 推荐商品列表
        recommendList.isEmpty
            ? Center(
                child: Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: Text(
                    '暂无推荐商品',
                    style: TextStyle(color: Colors.grey),
                  ),
                ),
              )
            : GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  crossAxisSpacing: 15,
                  mainAxisSpacing: 15,
                  childAspectRatio: 160 / 240,
                ),
                itemCount: recommendList.length,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemBuilder: (context, index) {
                  final item = recommendList[index];
                  return GestureDetector(
                    onTap: () {
                      // 点击跳转到商品详情页
                      // print('点击跳转到商品详情页id:${item.id}');
                      NavigatorAction.init(
                        context,
                        view: StoreDetailPage(
                          id: item.id,
                          code: item.code,
                        ),
                      );
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade200),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 商品图片
                          ClipRRect(
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(8),
                              topRight: Radius.circular(8),
                            ),
                            child: Image.network(
                              item.image,
                              height: 160,
                              width: double.infinity,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Container(
                                  height: 160,
                                  color: Colors.grey[100],
                                  child: Center(
                                    child: Icon(Icons.image_not_supported,
                                        color: Colors.grey[400]),
                                  ),
                                );
                              },
                            ),
                          ),
                          // 商品信息
                          Padding(
                            padding: const EdgeInsets.all(10.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  item.name,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                // 商品价格
                                Row(
                                  children: [
                                    Text(
                                      '¥',
                                      style: TextStyle(
                                        color: Colors.red[700],
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    Text(
                                      '${item.price.toStringAsFixed(2)}',
                                      style: TextStyle(
                                        color: Colors.red[700],
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
      ],
    );
  }

  // 构建失效商品标题
  Widget _buildExpiredItemsTitle() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      color: Colors.white,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            '失效商品',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          GestureDetector(
            onTap: () {
              _showConfirmClearExpiredDialog(context);
            },
            child: Text(
              '清空失效商品',
              style: TextStyle(
                color: Colors.red,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建失效商品项
  Widget _buildExpiredItem(ExpiredCommodityList item, int index) {
    return GestureDetector(
      onTap: () {
        // 点击失效商品跳转到商品详情页
        NavigatorAction.init(
          context,
          view: StoreDetailPage(
            id: item.commodityId ?? 0,
            code: 0, // Assuming default channel code is 0
          ),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          border:
              Border(bottom: BorderSide(color: Color(0xFFEEEEEE), width: 0.5)),
        ),
        padding: EdgeInsets.all(16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // 添加不可选中的按钮
            Icon(
              Icons.check_circle_outline,
              color: Colors.grey.shade300, // 使用浅灰色表示不可选中
              size: 24,
            ),
            const SizedBox(width: 12),
            // 商品图片
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                color: Colors.grey[200],
              ),
              child: Image.network(
                item.commodityImage ?? 'https://via.placeholder.com/80',
                fit: BoxFit.cover,
              ),
            ),
            const SizedBox(width: 12),
            // 商品信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    item.commodityName ?? '',
                    style: TextStyle(fontSize: 14),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                  // 失效原因也需要约束
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: Color(0xFFF5F5F5),
                      borderRadius: BorderRadius.circular(2),
                    ),
                    child: Text(
                      item.expiredReasonStr ?? '商品已下架',
                      style: TextStyle(color: Colors.grey, fontSize: 12),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 添加显示一键清理商品的半模态弹窗方法
  void _showClearItemsDialog(BuildContext context) {
    // 如果没有失效商品或正常商品被选中，显示toast提示
    bool hasItemsToClear = isEditing
        ? (selectedItemCount > 0 || expiredCommodityList.isNotEmpty)
        : expiredCommodityList.isNotEmpty;

    if (!hasItemsToClear) {
      // 显示灰色半透明Toast提示
      OverlayEntry overlayEntry = OverlayEntry(
        builder: (context) => Positioned(
          top: MediaQuery.of(context).size.height / 2 - 30,
          left: 0,
          right: 0,
          child: Center(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              decoration: BoxDecoration(
                color: const Color(0x88666666),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                "没有可以清除的商品～",
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      );

      // 在Overlay中显示Toast
      Overlay.of(context).insert(overlayEntry);

      // 2秒后自动移除Toast
      Future.delayed(const Duration(seconds: 2), () {
        overlayEntry.remove();
      });

      return;
    }

    // 原有的逻辑，当有失效商品时显示模态框
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      builder: (BuildContext context) {
        // 为弹窗创建临时数据和状态
        List<ExpiredCommodityList> tempExpiredItems =
            List.from(expiredCommodityList);
        Map<int, bool> selectedItems = {};

        // 初始化所有项为未选中
        for (var item in tempExpiredItems) {
          selectedItems[item.cartId ?? 0] = false;
        }

        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setModalState) {
            // 计算选中项数量
            int selectedCount =
                selectedItems.values.where((isSelected) => isSelected).length;
            bool allSelected = tempExpiredItems.isNotEmpty &&
                selectedItems.values.every((isSelected) => isSelected);

            return Container(
              height: MediaQuery.of(context).size.height * 0.6,
              child: Column(
                children: [
                  // 顶部标题栏
                  Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 16, horizontal: 16),
                    decoration: BoxDecoration(
                      border: Border(
                        bottom:
                            BorderSide(color: Color(0xFFEEEEEE), width: 0.5),
                      ),
                    ),
                    child: Stack(
                      children: [
                        Align(
                          alignment: Alignment.center,
                          child: Text(
                            '一键清理',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        Positioned(
                          right: 0,
                          top: 0,
                          child: GestureDetector(
                            onTap: () => Navigator.pop(context),
                            child: Icon(
                              Icons.close,
                              size: 24,
                              color: Color(0xFF333333),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // 提示文本
                  Padding(
                    padding: EdgeInsets.all(16),
                    child: Text(
                      '负担过重，把收纳篮，购物车速度更快',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                  ),

                  // 失效商品选项
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    alignment: Alignment.centerLeft,
                    child: GestureDetector(
                      onTap: () {
                        setModalState(() {
                          // 切换全选状态
                          bool newValue = !allSelected;
                          for (var item in tempExpiredItems) {
                            selectedItems[item.cartId ?? 0] = newValue;
                          }
                        });
                      },
                      child: Row(
                        children: [
                          Icon(
                            allSelected
                                ? Icons.check_circle
                                : Icons.check_circle_outline,
                            color: allSelected ? Colors.red : Colors.grey,
                            size: 24,
                          ),
                          SizedBox(width: 8),
                          Text('失效商品'),
                        ],
                      ),
                    ),
                  ),

                  // 失效商品列表
                  Expanded(
                    child: tempExpiredItems.isEmpty
                        ? Center(
                            child: Text(
                              '没有失效商品',
                              style: TextStyle(color: Colors.grey),
                            ),
                          )
                        : ListView.builder(
                            itemCount: tempExpiredItems.length,
                            itemBuilder: (context, index) {
                              final item = tempExpiredItems[index];
                              final isSelected =
                                  selectedItems[item.cartId ?? 0] ?? false;

                              return Container(
                                padding: EdgeInsets.all(16),
                                decoration: BoxDecoration(
                                  border: Border(
                                    bottom: BorderSide(
                                        color: Color(0xFFEEEEEE), width: 0.5),
                                  ),
                                ),
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    GestureDetector(
                                      onTap: () {
                                        setModalState(() {
                                          selectedItems[item.cartId ?? 0] =
                                              !isSelected;
                                        });
                                      },
                                      child: Icon(
                                        isSelected
                                            ? Icons.check_circle
                                            : Icons.check_circle_outline,
                                        color: isSelected
                                            ? Colors.red
                                            : Colors.grey,
                                        size: 24,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(4),
                                      child: Image.network(
                                        item.commodityImage ??
                                            'https://via.placeholder.com/80',
                                        width: 80,
                                        height: 80,
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Text(
                                            item.commodityName ?? '',
                                            style: TextStyle(fontSize: 14),
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          const SizedBox(height: 8),
                                          Container(
                                            padding: EdgeInsets.symmetric(
                                                horizontal: 8, vertical: 2),
                                            decoration: BoxDecoration(
                                              color: Color(0xFFF5F5F5),
                                              borderRadius:
                                                  BorderRadius.circular(2),
                                            ),
                                            child: Text(
                                              item.expiredReasonStr ?? '已失效',
                                              style: TextStyle(
                                                  color: Colors.grey,
                                                  fontSize: 12),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                  ),

                  // 底部删除按钮
                  Container(
                    padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      border: Border(
                        top: BorderSide(color: Color(0xFFEEEEEE), width: 0.5),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Text(
                          '已选 $selectedCount 件',
                          style: TextStyle(fontSize: 14),
                        ),
                        const SizedBox(width: 16),
                        ElevatedButton(
                          onPressed: selectedCount > 0
                              ? () {
                                  // 获取选中的失效商品ID
                                  List<int> selectedIds = [];
                                  selectedItems.forEach((id, isSelected) {
                                    if (isSelected) selectedIds.add(id);
                                  });

                                  if (selectedIds.isNotEmpty) {
                                    // 调用API删除选中的失效商品
                                    storeApi.deleteCartItem(body: {
                                      "channelSourceId": 0,
                                      "cartIds": selectedIds
                                    }).then((_) {
                                      // API调用成功后更新UI
                                    });

                                    // 从本地列表移除选中的失效商品
                                    setState(() {
                                      expiredCommodityList.removeWhere((item) =>
                                          selectedIds.contains(item.cartId));
                                    });
                                    Navigator.pop(context); // 关闭弹窗
                                  }
                                }
                              : null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(20),
                            ),
                            padding: const EdgeInsets.symmetric(
                                horizontal: 24, vertical: 10),
                          ),
                          child: Text('删除'),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  // 添加显示确认清空失效商品对话框的方法
  void _showConfirmClearExpiredDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 24, horizontal: 16),
          width: 280,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Text(
                "确认清空失效商品吗?",
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: TextButton.styleFrom(
                        foregroundColor: const Color(0xFF333333),
                      ),
                      child: const Text(
                        "我再想想",
                        style: TextStyle(fontSize: 16),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();

                        // 调用API删除所有失效商品
                        List<int> expiredIds = expiredCommodityList
                            .map((item) => item.cartId ?? 0)
                            .toList();
                        storeApi.deleteCartItem(body: {
                          "channelSourceId": 0,
                          "cartIds": expiredIds
                        });

                        // 使用真实数据，直接清空本地列表
                        setState(() {
                          expiredCommodityList.clear();
                        });
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.red,
                      ),
                      child: const Text(
                        "删除",
                        style: TextStyle(fontSize: 16),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 在类中添加这个方法，与商品详情页保持一致
  void showNotLoginAlertDialog(BuildContext context) {
    DialogManager().showCustomDialog(
      CustomDialog(
        content: "您还没有登录",
        contentFontSize: 18,
        contentColor: Colors.black,
        buttonHeight: 70,
        horizontalPadding: 20,
        buttons: [
          DialogButton(
              label: "继续逛逛",
              onPressed: () {},
              backgroundColor: Colors.white,
              textColor: const Color(0xFF9B9DA9)),
          DialogButton(
              label: "去登录",
              onPressed: () {
                LoginManager().showLoginModal();
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xffea0029)),
        ],
      ),
    );
  }

  @override
  void dispose() {
    // TODO: implement dispose
    _refreshController?.dispose();
    super.dispose();
  }
}
