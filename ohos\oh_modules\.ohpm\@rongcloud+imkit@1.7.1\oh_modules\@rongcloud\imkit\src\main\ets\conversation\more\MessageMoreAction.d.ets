// @keepTs
// @ts-nocheck
import { Message } from "@rongcloud/imlib";
/**
 * Created on 2024/9/21
 * <AUTHOR>
 */
/**
 * 长按消息点击“更多”之后，在底部显示的按钮
 * @version 1.0.0
 */
export interface MessageMoreAction {
    /**
     * 按钮图标
     */
    icon: string | Resource;
    /**
     * 图标的点击事件
     * return true 继续编辑（消息依旧处于多选状态）,false 退出编辑（消息退出多端状态）
     */
    onClick: (context: Context, data: Message[]) => boolean;
    /**
     * 过滤条件，确定底部是否显示
     * true 显示，false 不显示
     */
    onFilter: (data: Message[]) => boolean;
    /**
     * 动作 Id ，每一个动作都有一个 ID 用来区分行为
     */
    actionId: string;
    /**
     * 按钮将放置在底部，根据 location 的值，按照从小到大的顺序依次向右排列。
     */
    location: number;
}
