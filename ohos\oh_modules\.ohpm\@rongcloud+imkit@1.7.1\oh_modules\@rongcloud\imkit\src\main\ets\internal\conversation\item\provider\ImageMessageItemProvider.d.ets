// @keepTs
// @ts-nocheck
/**
 * Created on 2024/07/09
 * <AUTHOR>
 */
import { ImageMessage } from '@rongcloud/imlib';
import { BaseMessageItemProvider } from '../../../../conversation/item/provider/BaseMessageItemProvider';
import { UiMessage } from '../../../../conversation/model/UiMessage';
export declare class ImageMessageItemProvider extends BaseMessageItemProvider<ImageMessage> {
    getMessageWrapBuilder(): WrappedBuilder<[
        Context,
        UiMessage,
        number
    ]>;
    isShowSummaryName(j182: Context, k182: ImageMessage): boolean;
    getSummaryTextByMessageContent(e182: Context, f182: ImageMessage): Promise<MutableStyledString>;
    private isDestruct;
}
@Builder
export declare function bindImageMessageData(s181: Context, t181: UiMessage, u181: number): void;
@Component
export declare struct ImageMessageView {
    @Require
    @Prop
    context: Context;
    @Require
    @Prop
    index: number;
    @ObjectLink
    uiMessage: UiMessage;
    private imageMessage;
    @State
    imageWidth: number;
    @State
    imageHeight: number;
    aboutToAppear(): void;
    build(): void;
    private imagePreviewData;
    private bubbleBorderRadius;
}
