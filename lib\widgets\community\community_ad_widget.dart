import 'dart:convert';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_uikit/flutter_uikit.dart';
import 'package:wuling_flutter_app/models/community/recommend_ad.dart';
import 'package:wuling_flutter_app/page/community/topic_post_details_page.dart';
import 'package:wuling_flutter_app/page/store/store_detail/store_detail_page.dart';
import 'package:wuling_flutter_app/widgets/common/image_widget.dart';
import 'package:wuling_flutter_app/widgets/webview/webview.dart';

import '../../common/action.dart';
import '../../constant/service_constant.dart';
import '../../constant/web_view_url_tool.dart';
import '../../page/community/img_text_video_post_detail_page.dart';
import '../../page/community/user_info_page.dart';
import '../../page/store/store_baojun/store_baojun_page.dart';
import '../../page/store/store_wuling/store_wuling_page.dart';
import '../../routes/jump_tool.dart';
import '../../utils/manager/log_manager.dart';
import '../common/image_view.dart';

class CommunityAdWidget extends StatefulWidget {
  const CommunityAdWidget({super.key,required this.list});
  final List<RecommendAd> list;

  @override
  State<CommunityAdWidget> createState() => _CommunityAdWidgetState();
}

class _CommunityAdWidgetState extends State<CommunityAdWidget> {
  final CarouselController _carouselController = CarouselController();
  int advertiseId = 0;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 220,
      width: double.maxFinite,
      padding: const EdgeInsets.symmetric(horizontal: 20,vertical: 10),
      child: Stack(
        children: [
          CarouselSlider(
              carouselController: _carouselController,
              items: widget.list.map((ad) => Builder(
                  builder: (context) {
                    return GestureDetector(
                      onTap: () {
                        if(ad.linkType == 3 || ad.linkType == 4 || ad.linkType == 5) {
                          Navigator.of(context).push(MaterialPageRoute(builder: (context) {
                            return WebViewPage(url: ad.linkUrl);
                          }));
                        }else if(ad.linkType == 1) {
                          Navigator.of(context).push(MaterialPageRoute(builder: (context) {
                            return TopicPostDetailsPage(postId: int.parse(ad.linkUrl), postTypeId: 14);
                          }));
                        }else if(ad.linkType == 50) {
                          Navigator.of(context).push(MaterialPageRoute(builder: (context) {
                            return StoreDetailPage(id: int.parse(ad.linkUrl), code: 0);
                          }));
                        } else if(ad.linkType == 7) {
                          // "客服MM";
                          String url = WebViewURLTool.kefuURLStrWithGroup(
                              KefuGroup.mm.value, '', '');
                          JumpTool().openWeb(context, url, true);
                        } else if(ad.linkType == 17) {
                          NavigatorAction.init(context,view: UserInfoPage(nickname: '', userIdStr: ad.linkUrl,));
                        } else if(ad.linkType == 56) {
                          // "指定一二级分类(商品中心)";
                          NavigatorAction.init(context,view: StoreBaoJunPage(id: int.parse(ad.linkUrl),));
                        } else if(ad.linkType == 57) {
                          //  "仅指定二级分类(商品中心)";
                          NavigatorAction.init(context,view: StoreWuLingPage(id: int.parse(ad.linkUrl),));
                        }else if(ad.linkType == 58) {
                          // "话题详情页";
                          int topicId = int.parse(ad.linkUrl);
                          JumpTool().jumpToTopicDetailPage(
                            context: context,
                            topicId: topicId,
                          );
                        } else if(ad.linkType == 25) {
                          // "短视频帖";
                          NavigatorAction.init(context,view: ImgTextVideoPostDetailPage(postId: int.parse(ad.linkUrl),postTypeId: 5,));
                        }else {
                          LogManager().debug("linkType: ${ad.linkType}, linkUrl: ${ad.linkUrl}");
                        }
                      },
                      child: Container(
                          width: MediaQuery.of(context).size.width,
                          margin: const EdgeInsets.symmetric(horizontal: 0),
                          child: ImageWidget(width: double.infinity, height: 220, url: ad.advertiseImage,borderRadius: 10,)
                      ),
                    );
                  }
              )).toList(),
              options: CarouselOptions(
                height: 220,
                  enlargeCenterPage: false,
                  autoPlay: true,
                  // 自动播放仅当广告数量大于1时启用
                  viewportFraction: 1.0,
                  // 每个图片占据整个轮播视口的宽度
                  autoPlayInterval: const Duration(seconds: 5),
                  onPageChanged: (index,season) {
                    advertiseId = widget.list[index].advertiseId;
                    setState(() {

                    });
                  }
              )),
          Positioned(
            bottom: 10,
              left: 0,
              right: 0,
              child:Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: widget.list.map((e) {
                  return Container(
                    margin: const EdgeInsets.symmetric(horizontal: 2),
                    height: 2,
                    width: advertiseId == e.advertiseId ? 10 : 5 ,
                    decoration: BoxDecoration(
                      color: advertiseId == e.advertiseId ? Colors.white : Colors.grey,
                      borderRadius: BorderRadius.circular(2)
                    ),
                  );
                }).toList(),
              )
          )
        ],
      ),
    );
  }

  @override
  void dispose() {
    // TODO: implement dispose
    _carouselController?.stopAutoPlay();
    super.dispose();
  }
}
