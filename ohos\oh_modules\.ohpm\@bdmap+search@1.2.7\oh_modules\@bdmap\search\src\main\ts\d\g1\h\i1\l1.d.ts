import { LatLng } from '@bdmap/base';
import { PoiFilter } from "../p1";
import { LanguageType } from "../../../base/x2";
/**
 * 附近检索参数
 */
export declare class PoiNearbySearchOption {
    /**
     * 检索关键字，必须参数
     * 支持多个关键字并集检索，不同关键字间以$符号分隔，最多支持10个关键字检索。如:”银行$酒店”
     */
    private _keyword;
    set keyword(value: string);
    get keyword(): string;
    /**
     * 周边检索中心点，不支持多个点
     * 必须参数
     */
    private _location;
    set location(value: LatLng);
    get location(): LatLng;
    /**
     * 周边检索半径，单位为米
     * 当半径过大，超过中心点所在城市边界时，会变为城市范围检索，检索范围为中心点所在城市
     * 必须参数
     */
    private _radius;
    set radius(value: number);
    get radius(): number;
    /**
     * 分页页码，默认为0
     * 0代表第一页，1代表第二页，以此类推
     */
    private _pageNum;
    set pageNum(value: number);
    get pageNum(): number;
    /**
     * 单页展示POI数量
     * 默认为10条记录，最大返回20条。多关键字检索时，返回的记录数为关键字个数*mPageNum
     */
    private _pageCapacity;
    set pageCapacity(value: number);
    get pageCapacity(): number;
    /**
     * 检索结果排序策略，默认按综合排序
     */
    private _sortType;
    set sortType(value: PoiSortType);
    get sortType(): PoiSortType;
    /**
     * 检索分类，
     * 多个分类以","分割，默认空
     */
    private _tag;
    set tag(value: string | null);
    get tag(): string | null;
    /**
     * 检索结果详细程度。
     * 取值为1或空，则返回基本信息；取值为2，返回检索POI详细信息。
     * 默认为1
     */
    private _scope;
    set scope(value: number);
    get scope(): number;
    /**
     * 是否严格限定召回结果在设置检索半径范围内
     * true（是），false（否）
     * 默认否
     */
    private _radiusLimit;
    set radiusLimit(value: boolean);
    get radiusLimit(): boolean;
    /**
     * 检索过滤条件。
     * 当scope取值为2时，可以设置filter进行排序
     * 默认空
     */
    private _poiFilter;
    set poiFilter(value: PoiFilter | null);
    get poiFilter(): PoiFilter | null;
    /**
     * 是否召回行政区域编码，默认是
     */
    private _isExtendAdcode;
    set isExtendAdcode(value: boolean);
    get isExtendAdcode(): boolean;
    /**
     * 检索语言类型
     * 默认中文
     */
    private _mLanguageType;
    set languageType(value: LanguageType);
    get languageType(): LanguageType;
    constructor(params: PoiNearbySearchOptionParams);
}
export interface PoiNearbySearchOptionParams {
    /**
     * 检索关键字，必须参数
     * 支持多个关键字并集检索，不同关键字间以$符号分隔，最多支持10个关键字检索。如:”银行$酒店”
     */
    keyword: string;
    /**
     * 周边检索中心点，不支持多个点
     * 必须参数
     */
    location: LatLng;
    /**
     * 周边检索半径，单位为米
     * 当半径过大，超过中心点所在城市边界时，会变为城市范围检索，检索范围为中心点所在城市
     * 必须参数
     */
    radius: number;
    /**
     * 分页页码，默认为0
     * 0代表第一页，1代表第二页，以此类推
     */
    pageNum?: number;
    /**
     * 单页展示POI数量
     * 默认为10条记录，最大返回20条。多关键字检索时，返回的记录数为关键字个数*mPageNum
     */
    pageCapacity?: number;
    /**
     * 检索结果排序策略，默认按综合排序
     */
    sortType?: PoiSortType;
    /**
     * 检索分类，
     * 多个分类以","分割，默认空
     */
    tag?: string;
    /**
     * 检索结果详细程度。
     * 取值为1或空，则返回基本信息；取值为2，返回检索POI详细信息。
     * 默认为1
     */
    scope?: number;
    /**
     * 是否严格限定召回结果在设置检索半径范围内
     * true（是），false（否）
     * 默认否
     */
    radiusLimit?: boolean;
    /**
     * 检索过滤条件。
     * 当scope取值为2时，可以设置filter进行排序
     * 默认空
     */
    poiFilter?: PoiFilter;
    /**
     * 是否召回行政区域编码，默认是
     */
    isExtendAdcode?: boolean;
    /**
     * 检索语言类型
     * 默认中文
     */
    languageType?: LanguageType;
}
/**
 * poi排序枚举类
 */
export declare enum PoiSortType {
    /**
     * 按综合排序
     */
    comprehensive = 0,
    /**
     * 按距离排序
     */
    distance_from_near_to_far = 1
}
