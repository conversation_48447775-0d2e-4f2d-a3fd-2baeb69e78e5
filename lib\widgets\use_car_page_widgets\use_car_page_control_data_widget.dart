import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/models/car/car_service_status_model.dart';
import 'package:wuling_flutter_app/utils/manager/log_manager.dart';
import 'package:wuling_flutter_app/utils/str_util.dart';
import 'package:wuling_flutter_app/widgets/common/image_view.dart';
import 'package:wuling_flutter_app/widgets/common/frame_animation_Image.dart';
import 'package:wuling_flutter_app/models/car/car_info_model.dart';
import 'package:wuling_flutter_app/models/car/car_status_model.dart';
import 'package:wuling_flutter_app/utils/manager/ble_manager.dart';

import '../../models/car/car_service_model.dart';
import '../../utils/manager/colors_manager.dart';
import '../../utils/manager/fonts_manager.dart';

class CarNumberWidget extends StatelessWidget {
  final bool isPhev;
  final String carTotalMeleage;
  final String carBatSoc;
  final String carMeleage;
  final bool oilState;
  final String oilLeftMileage;
  final String avgFuel;
  final int vecChrgStsIndOn;
  final VoidCallback? onChargeButtonClicked;
  const CarNumberWidget(
      {Key? key,
      required this.carTotalMeleage,
      required this.carBatSoc,
      required this.carMeleage,
      required this.isPhev,
      required this.oilState,
      required this.oilLeftMileage,
      required this.avgFuel,
      required this.vecChrgStsIndOn,
      this.onChargeButtonClicked})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    const double batteryWidth = 32;
    const double batteryHeight = 50;
    // //剩余油量显示：红色：≤20  ---- 黄色：21~30 ----- 绿色：≥31
    //             if (progress <= 20) {
    //                 mProgressBgColor = R.color.color_FF5A5A;
    //             } else if (progress >= 21 && progress <= 30) {
    //                 mProgressBgColor = R.color.color_FFCF5A;
    //             } else if (progress >= 31) {
    //                 mProgressBgColor = R.color.color_00c853;
    //             }

    int batteryValue = int.tryParse(carBatSoc) ?? 0;
    Color batteryColor;
    if (batteryValue <= 20) {
      batteryColor = Color(0xFFFF5A5A);
    } else if (batteryValue >= 21 && batteryValue <= 30) {
      batteryColor = Color(0xFFFFCF5A);
    } else if (batteryValue >= 31) {
      batteryColor = Color(0xFF00c853);
    }
    if (isPhev) {
      return Container(
        height: 90,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8), // 设置圆角半径
          boxShadow: const [
            BoxShadow(
              color: Color(0x0a000000), // 阴影颜色
              spreadRadius: 0, // 阴影扩散半径
              blurRadius: 4, // 模糊半径
              offset: Offset(0, 0), // 阴影偏移量
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
                child: _buildPHEVInfoUI(batteryValue, carMeleage, oilState,
                    oilLeftMileage, avgFuel)),
            Container(width: 5),
            //充电按钮
            TextButton(
                onPressed: onChargeButtonClicked,
                child: SizedBox(
                  width: 76,
                  height: 62,
                  child: Stack(
                    alignment: Alignment.centerRight,
                    children: [
                      Positioned(
                          left: 0,
                          top: 0,
                          child: Container(
                            color: const Color(0xffD2D4D8),
                            width: 1,
                            height: 62,
                          )),
                      Image.asset(
                        'assets/images/use_car_page/recharge_light.png',
                        width: 60,
                        height: 62,
                      ),
                      Positioned(
                          bottom: 0,
                          child: Container(
                            width: 58,
                            height: 26,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: const Color(0xff008DFF),
                              borderRadius: BorderRadius.circular(13), // 设置圆角半径
                            ),
                            child: const Text(
                              '充电',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.white,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ))
                    ],
                  ),
                ))
          ],
        ),
      );
    } else {
      return Container(
        height: 90,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8), // 设置圆角半径
          boxShadow: const [
            BoxShadow(
              color: Color(0x0a000000), // 阴影颜色
              spreadRadius: 0, // 阴影扩散半径
              blurRadius: 4, // 模糊半径
              offset: Offset(0, 0), // 阴影偏移量
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            const SizedBox(
              width: 0,
            ),
            //充电图示
            SizedBox(
              width: batteryWidth,
              height: batteryHeight,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  Positioned(
                    left: 0,
                    top: 0,
                    child: Image.asset(
                      'assets/images/use_car_page/battery_bg.png',
                      width: batteryWidth,
                      height: batteryHeight,
                    ),
                  ),
                  Positioned(
                    left: 0,
                    bottom: 0,
                    child: Container(
                      width: batteryWidth,
                      height: 48 * (batteryValue / 100),
                      decoration: BoxDecoration(
                        color: _getBatSocColor(batteryValue), // 设置背景颜色
                        borderRadius: BorderRadius.only(
                            bottomLeft: const Radius.circular(2.5),
                            bottomRight: const Radius.circular(2.5),
                            topLeft: batteryValue >= 100
                                ? const Radius.circular(2.5)
                                : const Radius.circular(0),
                            topRight: batteryValue >= 100
                                ? const Radius.circular(2.5)
                                : const Radius.circular(0)), // 设置圆角半径
                      ),
                    ),
                  ),
                  if (batteryValue >= 100)
                    Positioned(
                      top: 0,
                      child: Container(
                        width: 14,
                        height: 2,
                        decoration: BoxDecoration(
                          color: _getBatSocColor(batteryValue), // 设置背景颜色
                          borderRadius:
                              BorderRadius.all(Radius.circular(1)), // 设置圆角半径
                        ),
                      ),
                    ),
                  if (vecChrgStsIndOn == 1)
                    Image.asset('assets/images/use_car_page/car_recharge.png',
                        width: 10, height: 20),
                ],
              ),
            ),
            //剩余电量
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      carBatSoc,
                      style: const TextStyle(
                          fontSize: 30,
                          color: Color(0xff686B78),
                          fontWeight: FontWeight.w500),
                    ),
                    const Padding(
                      padding: EdgeInsets.only(bottom: 4),
                      child: Text(
                        '%',
                        style:
                            TextStyle(fontSize: 16, color: Color(0xff686B78)),
                      ),
                    )
                  ],
                ),
                Text(
                  vecChrgStsIndOn == 1 ? '充电中' : '剩余电量',
                  style: TextStyle(
                      fontSize: 12,
                      color: vecChrgStsIndOn == 1
                          ? const Color(0xff008DFF)
                          : const Color(0xff686B78)),
                )
              ],
            ),
            //续航里程
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(
                  height: 6,
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      carMeleage,
                      style: const TextStyle(
                          fontSize: 21,
                          color: Color(0xff686B78),
                          fontWeight: FontWeight.w500),
                    ),
                    const Padding(
                      padding: EdgeInsets.only(bottom: 2),
                      child: Text(
                        'km',
                        style:
                            TextStyle(fontSize: 16, color: Color(0xff686B78)),
                      ),
                    )
                  ],
                ),
                const SizedBox(
                  height: 6,
                ),
                Text(
                  '续航里程',
                  style: TextStyle(
                      fontSize: 12,
                      color: vecChrgStsIndOn == 1
                          ? const Color(0xff008DFF)
                          : const Color(0xff686B78)),
                )
              ],
            ),
            //充电按钮
            TextButton(
                onPressed: onChargeButtonClicked,
                child: SizedBox(
                  width: 76,
                  height: 62,
                  child: Stack(
                    alignment: Alignment.centerRight,
                    children: [
                      Positioned(
                          left: 0,
                          top: 0,
                          child: Container(
                            color: const Color(0xffD2D4D8),
                            width: 1,
                            height: 62,
                          )),
                      Image.asset(
                        'assets/images/use_car_page/recharge_light.png',
                        width: 60,
                        height: 62,
                      ),
                      Positioned(
                          bottom: 0,
                          child: Container(
                            width: 58,
                            height: 26,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: const Color(0xff008DFF),
                              borderRadius: BorderRadius.circular(13), // 设置圆角半径
                            ),
                            child: const Text(
                              '充电',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.white,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ))
                    ],
                  ),
                ))
          ],
        ),
      );
    }
  }

  //根据不同的电量获取显示的颜色
  Color _getBatSocColor(int soc) {
    Color socColor = ColorsManager.color_FF7ADA62_FF7ADA62();
    if (soc <= 20) {
      socColor = ColorsManager.color_FFFF5A5A_FFFF5A5A();
    } else if (soc <= 30) {
      socColor = ColorsManager.color_FFFFCF5A_FFFFCF5A();
    }

    return socColor;
  }

  //PHEV车电池、续航显示UI
  Widget _buildPHEVInfoUI(int batSoc, String leftMileage, bool oilState,
      String oilLeftMileage, String avgFuel) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
            child: Container(
                padding: const EdgeInsets.fromLTRB(25, 4, 0, 8),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(
                      height: 20,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Stack(children: [
                            Image.asset(
                                'assets/images/use_car_page/usecar_home_phev_battery_bg.png',
                                width: 20,
                                height: 20),
                            Positioned(
                                left: 5.5,
                                right: 5.5,
                                bottom: 2.5,
                                height: 14 * batSoc / 100.0,
                                child:
                                    Container(color: _getBatSocColor(batSoc)))
                          ]),
                          Container(width: 0.0),
                          Text(vecChrgStsIndOn == 1 ? '充电中' : '剩余电量',
                              textScaleFactor: 1.0,
                              style: FontsManager.regularFont12(
                                  _getBatSocSubTextColor(
                                      vecChrgStsIndOn == 1))),
                          Container(width: 3.0),
                          RichText(
                            textScaleFactor: 1.0,
                            text: TextSpan(
                                text: '$batSoc',
                                style: FontsManager.mediumFont16(
                                    _getBatSocMainTextColor(
                                        vecChrgStsIndOn == 1)),
                                children: <TextSpan>[
                                  TextSpan(
                                      text: '%',
                                      style: FontsManager.regularFont10(
                                          _getBatSocMainTextColor(
                                              vecChrgStsIndOn == 1)))
                                ]),
                          ),
                          Container(width: 3.0),
                          Container(
                            width: 1.0,
                            height: 11.0,
                            color: ColorsManager.color1e000000Dark1e000000(),
                          ),
                          Container(width: 3.0),
                          RichText(
                            textScaleFactor: 1.0,
                            overflow: TextOverflow.ellipsis,
                            text: TextSpan(
                                text: leftMileage.isValidStr()
                                    ? leftMileage
                                    : '--',
                                style: FontsManager.mediumFont16(
                                    ColorsManager.colorFf383A40DarkFf383A40()),
                                children: <TextSpan>[
                                  TextSpan(
                                      text: 'km',
                                      style: FontsManager.regularFont10(
                                          ColorsManager
                                              .colorFf383A40DarkFf383A40()))
                                ]),
                          )
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 9,
                    ),
                    SizedBox(
                      height: 20,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Image.asset(
                              oilState
                                  ? 'assets/images/use_car_page/usecar_home_phev_oil.png'
                                  : 'assets/images/use_car_page/usecar_home_phev_oil_low.png',
                              width: 20,
                              height: 20),
                          Container(width: 0.0),
                          Text('油量续航',
                              textScaleFactor: 1.0,
                              style: FontsManager.regularFont12(
                                  ColorsManager.colorFf686b78DarkFf686b78())),
                          Container(width: 5.0),
                          RichText(
                            textScaleFactor: 1.0,
                            text: TextSpan(
                                text: oilLeftMileage.isValidStr()
                                    ? oilLeftMileage
                                    : '--',
                                style: FontsManager.mediumFont16(
                                    ColorsManager.colorFf383A40DarkFf383A40()),
                                children: <TextSpan>[
                                  TextSpan(
                                      text: 'km',
                                      style: FontsManager.regularFont10(
                                          ColorsManager
                                              .colorFf383A40DarkFf383A40()))
                                ]),
                          ),
                        ],
                      ),
                    ),
                  ],
                ))),
        Container(
          width: 5,
        ),
        SizedBox(
            height: 50,
            width: 55,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                RichText(
                    textScaleFactor: 1.0,
                    textAlign: TextAlign.center,
                    text: TextSpan(
                        text: avgFuel.isValidStr() ? avgFuel : '--',
                        style: FontsManager.mediumFont21(
                            ColorsManager.colorFf383A40DarkFf383A40()),
                        children: <TextSpan>[
                          TextSpan(
                              text: 'L',
                              style: FontsManager.regularFont10(
                                  ColorsManager.colorFf383A40DarkFf383A40()))
                        ])),
                Text('平均油耗',
                    textScaleFactor: 1.0,
                    style: FontsManager.regularFont12(
                        ColorsManager.colorFf686b78DarkFf686b78()))
              ],
            )),
      ],
    );
  }

  Color _getBatSocMainTextColor(bool isCharge) {
    if (isCharge) {
      return ColorsManager.colorFf008DFFDarkFf008DFF();
    } else {
      return ColorsManager.colorFf383A40DarkFf383A40();
    }
  }

  Color _getBatSocSubTextColor(bool isCharge) {
    if (isCharge) {
      return ColorsManager.colorFf008DFFDarkFf008DFF();
    } else {
      return ColorsManager.colorFf686b78DarkFf686b78();
    }
  }
}

class CarTipWidget extends StatelessWidget {
  final BleStatus currentBleStatus;
  final bool isHandleConnectBle;
  final int? bleType;
  final String? maintenanceMsg;
  final VoidCallback? onBleButtonClicked;
  const CarTipWidget(
      {Key? key,
      required this.currentBleStatus,
      required this.isHandleConnectBle,
      this.bleType = 0,
      this.maintenanceMsg = '',
      this.onBleButtonClicked})
      : super(key: key);

  String imageUrlByStatus(BleStatus bleStatus) {
    switch (bleStatus) {
      case BleStatus.bleDefault:
        return 'assets/images/use_car_page/new_ble_no.png';
      case BleStatus.bleSearching:
      case BleStatus.bleAuthorizing:
      case BleStatus.bleAuthHandshaking1:
      case BleStatus.bleAuthHandshaking2:
        return 'assets/images/use_car_page/new_ble_connecting.png';
      case BleStatus.bleAuthorized:
        return 'assets/images/use_car_page/new_ble_connected.png';
      default:
        return 'assets/images/use_car_page/new_ble_no.png';
    }
  }

  String buttonTitleByStatus(BleStatus bleStatus) {
    switch (bleStatus) {
      case BleStatus.bleDefault:
        return '蓝牙钥匙未连接';
      case BleStatus.bleSearching:
      case BleStatus.bleAuthorizing:
      case BleStatus.bleAuthHandshaking1:
      case BleStatus.bleAuthHandshaking2:
        return '蓝牙钥匙连接中';
      case BleStatus.bleAuthorized:
        return '蓝牙钥匙已连接';
      default:
        return '蓝牙钥匙未连接';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8), // 设置圆角半径
        boxShadow: const [
          BoxShadow(
            color: Color(0x0a000000), // 阴影颜色
            spreadRadius: 0, // 阴影扩散半径
            blurRadius: 4, // 模糊半径
            offset: Offset(0, 0), // 阴影偏移量
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 20),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            if (bleType != 0)
              TextButton(
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.all(0),
                  ),
                  onPressed: onBleButtonClicked,
                  child: Row(
                    children: [
                      Image.asset(
                        imageUrlByStatus(currentBleStatus),
                        width: 15,
                        height: 15,
                      ),
                      const SizedBox(
                        width: 4,
                      ),
                      Text(
                        buttonTitleByStatus(currentBleStatus),
                        style:
                            TextStyle(fontSize: 12, color: Color(0xff686B78)),
                      )
                    ],
                  )),
            if (maintenanceMsg != null && maintenanceMsg!.isNotEmpty)
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.all(0),
                      ),
                      onPressed: () {},
                      child: Row(
                        children: [
                          Image.asset(
                            'assets/images/use_car_page/car_baoyang.png',
                            width: 16,
                            height: 16,
                          ),
                          const SizedBox(
                            width: 4,
                          ),
                          const Text(
                            '保养提醒',
                            style: TextStyle(
                                fontSize: 12, color: Color(0xffF2AB62)),
                          )
                        ],
                      )),
                  SizedBox(
                    width: 30,
                    height: 40,
                    child: IconButton(
                      onPressed: () {},
                      icon: Image.asset(
                        'assets/images/use_car_page/maintainClose.png',
                        width: 12,
                        height: 12,
                      ),
                      iconSize: 30,
                    ),
                  )
                ],
              ),
          ],
        ),
      ),
    );
  }
}

class CarImageWidget extends StatelessWidget {
  final String imageUrl;
  final String fallbackUrl;
  final bool isAcTurnOn;
  final bool isWarmMode;
  final bool isNewAir;
  final bool isOfflineMode; // 新增：是否离线模式

  const CarImageWidget({
    Key? key,
    required this.imageUrl,
    this.fallbackUrl = '',
    this.isAcTurnOn = false,
    this.isWarmMode = false,
    this.isNewAir = false,
    this.isOfflineMode = false, // 新增：默认为false
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.transparent,
      child: Center(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: LayoutBuilder(
            builder: (context, constraints) {
              final double maxWidth = constraints.maxWidth;
              return AspectRatio(
                aspectRatio: 345 / 215.0,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    ImageView(
                      imageUrl.isNotEmpty
                          ? imageUrl
                          : 'assets/images/use_car_page/car_image_placeholder_front.png',
                      fit: BoxFit.contain,
                      fallbackUrl: fallbackUrl,
                    ),
                    //非新空调才显示空调开启动画，离线模式下隐藏
                    if (isAcTurnOn && !isNewAir && !isOfflineMode)
                      Positioned(
                        top: 35,
                        child: Container(
                          height: 120,
                          width: 120,
                          color: Colors.transparent,
                          child: FrameAnimationImage(
                            baseImageName: isWarmMode
                                ? "assets/images/animation_frame/ac_animation/warm/accAnimationFrame_warm_"
                                : 'assets/images/animation_frame/ac_animation/normal/accAnimationFrame_',
                            frameCount: 71,
                            imageFormat: ".png",
                            durationSeconds: 5,
                          ),
                        ),
                      ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}

class DataDisplayWidget extends StatelessWidget {
  final BleStatus currentBleStatus;
  final bool isHandleConnectBle;
  final CarStatusModel? carStatusModel;
  final CarInfoModel? carInfoModel;
  final VoidCallback? onChargeButtonClicked;
  final VoidCallback? onBleButtonClicked;
  final VoidCallback? onCarImageClicked;
  final List<CarServiceModel> ctrlServiceList;
  final bool isOfflineMode; // 新增：是否进入离线模式
  const DataDisplayWidget(
      {Key? key,
      required this.currentBleStatus,
      required this.isHandleConnectBle,
      required this.carStatusModel,
      required this.carInfoModel,
      this.onChargeButtonClicked,
      this.onBleButtonClicked,
      this.onCarImageClicked,
      required this.ctrlServiceList,
      this.isOfflineMode = false})
      : super(key: key);

  String getCurrentCarImageCodeWithModel() {
    if (carStatusModel == null || carInfoModel == null) {
      return '000000';
    }
    Map<String, dynamic> statusMap = carStatusModel!.toJson();
    List<String> list = [
      'door1OpenStatus',
      'door2OpenStatus',
      'doorLockStatus',
      'dipHeadLight',
      'tailDoorOpenStatus',
      'windowStatus'
    ];
    StringBuffer currentCarImage = StringBuffer();

    for (String str in list) {
      if (statusMap[str] == '1') {
        currentCarImage.write('1');
      } else {
        currentCarImage.write('0');
      }
    }
    return currentCarImage.toString();
  }

  /// 空调是否开启
  bool _isAcTurnOn() {
    if (carStatusModel == null) {
      return false;
    }
    return carStatusModel!.acStatus == '1' || carStatusModel!.acStatus == '2';
  }

  /// 判断是否是新空调
  bool _isNewAir(
      CarStatusModel? carStatus, List<CarServiceModel>? ctrlServiceList) {
    if (carStatus == null) {
      LogManager().debug('isNewAir() carStatus == null');
      return false;
    }
    if (ctrlServiceList == null) {
      LogManager().debug('isNewAir() ctrlServiceList == null');
      return false;
    }
    //是否有空调车控
    bool hasAcStatus = false;
    for (int i = 0; i < ctrlServiceList.length; i++) {
      CarServiceModel serviceModel = ctrlServiceList[i];
      if (serviceModel.serviceStatusList != null) {
        for (int j = 0; j < serviceModel.serviceStatusList!.length; j++) {
          CarServiceStatusModel carServiceStatusModel =
              serviceModel.serviceStatusList![j];
          if (serviceModel.serviceCode == 'acStatus') {
            hasAcStatus = true;
            if (carServiceStatusModel.serviceStatusValue ==
                carStatus!.acStatus) {
              //服务状态动作，0表示跳转到target（直接调指令接口），其他表示弹出对应level的状态。
              if (carServiceStatusModel.serviceStatusAction == '0') {
              } else {
                List<CarServiceStatusModel> statusList =
                    serviceModel.serviceStatusList!;
                //serviceStatusAction=2是新空调，跳有温度进度调节的空调弹框
                if ((statusList != null && statusList.length > 0) &&
                    (statusList[0].serviceStatusAction == '2' ||
                        statusList[0].serviceStatusAction == '4')) {
                  LogManager().debug('isNewAir() 当前是新空调');
                  return true;
                }
              }
            }
          }
        }
      }
    }
    if (ctrlServiceList.length == 0) {
      LogManager().debug('isNewAir() 车控按钮列表数据还没请求到，默认为新空调，不然车图上会闪现空调动画');
      return true;
    } else if (!hasAcStatus) {
      LogManager().debug('isNewAir() 车控按钮列表没有空调按钮，默认为新空调，不然会出现空调动画');
      return true;
    } else {
      LogManager().debug('isNewAir() 当前是旧空调');
      return false;
    }
  }

  /// 空调开启前提下是否为取暖模式
  bool _isWarmMode() {
    return carStatusModel?.acStatus == '2';
  }

  @override
  Widget build(BuildContext context) {
    String imageUrl = '';
    if (carStatusModel != null && carInfoModel != null) {
      if (carInfoModel!.folderUrl?.isNotEmpty ?? false) {
        String imageCode = getCurrentCarImageCodeWithModel();
        String folderUrl = carInfoModel!.folderUrl!;
        imageUrl = "$folderUrl/$imageCode.png";
      } else {
        imageUrl = carInfoModel!.image ?? "";
      }
    } else if (carInfoModel != null) {
      imageUrl = carInfoModel!.image ?? "";
    }
    return Container(
      color: Colors.transparent,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.only(top: 16),
            child: GestureDetector(
              onTap: isOfflineMode ? null : onCarImageClicked, // 离线模式下禁用点击
              child: CarImageWidget(
                imageUrl: imageUrl,
                isAcTurnOn: _isAcTurnOn(),
                isWarmMode: _isWarmMode(),
                isNewAir: _isNewAir(carStatusModel, ctrlServiceList),
                isOfflineMode: isOfflineMode, // 传递离线模式状态
              ),
            ),
          ),
          if (carInfoModel?.bleType != 0)
            Padding(
              padding: const EdgeInsets.only(top: 8, left: 20, right: 20),
              child: CarTipWidget(
                currentBleStatus: currentBleStatus,
                isHandleConnectBle: isHandleConnectBle,
                bleType: carInfoModel!.bleType,
                onBleButtonClicked: onBleButtonClicked,
              ),
            ),
          if (carStatusModel != null)
            Padding(
              padding: const EdgeInsets.only(top: 8, left: 20, right: 20),
              child: CarNumberWidget(
                carTotalMeleage: carStatusModel?.mileage ?? '--',
                carBatSoc: carStatusModel?.batterySoc ?? '--',
                carMeleage: carStatusModel?.leftMileage ?? '--',
                isPhev: carInfoModel?.controlView == 8,
                oilState:
                    (int.tryParse(carStatusModel?.leftFuel ?? '') ?? 0) > 15,
                oilLeftMileage: carStatusModel?.oilLeftMileage ?? '',
                avgFuel: carStatusModel?.avgFuel?.toString() ?? '',
                vecChrgStsIndOn:
                    int.parse(carStatusModel?.vecChrgStsIndOn ?? '0'),
                onChargeButtonClicked: onChargeButtonClicked,
              ),
            ),
        ],
      ),
    );
  }
}
