// @keepTs
// @ts-nocheck
/**
 * Created on 2024/8/8
 * <AUTHOR>
 */
import { Message, ReadReceiptInfo, SentStatus } from '@rongcloud/imlib';
import { UserInfoModel } from '../../user/model/UserInfoModel';
/**
 * UI 层用到的消息容器类，包含 Message 对象和其他配置
 * @version 1.0.0
 */
@Observed
export declare class UiMessage {
    message: Message;
    senderInfo: UserInfoModel;
    displayTime: boolean;
    selected: boolean;
    progress: number;
    isCanSendReadReceipt: boolean;
    readReceiptInfo: ReadReceiptInfo | null;
    sentStatus: SentStatus;
    resendStatus: ResendStatus;
    uiStatus: UiStatus;
    isConversationEditing: boolean;
    objectName: string;
    extra: string;
    get key(): string;
    constructor(x26: Message, y26: UserInfoModel);
}
/**
 * UiMessage的UI状态
 */
export declare enum UiStatus {
    /**
     * 正常状态
     */
    Normal = 1,
    /**
     * 错误状态
     */
    Error = 2
}
/**
 * UiMessage关联的消息重发状态
 * 备注：
 * 1，仅Message的direction为Send有效；
 * 2，仅Message的sentStatus为Sending时有效，
 */
export declare enum ResendStatus {
    /**
     * 正常状态，不在重发中，也不在等待重发队列中
     */
    None = 1,
    /**
     * 等待重发队列中
     */
    InWaiting = 2,
    /**
     * 正在重发中
     */
    InResending = 3
}
