import premierlibrary from 'libpremierlibrary.so';
import { ObjCreateHelper } from '../utils/ObjCreateHelper';
import { ErrorInfo } from '../player/bean/ErrorInfo';
import { findErrorCodeByValue, PlayerErrorCode } from '../player/bean/PlayerErrorCode';
import { AliPlayerGlobalSettings } from '../player/AliPlayerGlobalSettings';
export class VodMediaLoader {
    constructor() {
        this.objHelper = new ObjCreateHelper();
        this.onPrepared = (u6) => {
            if (this.mOnLoadStatusListener) {
                this.mOnLoadStatusListener.onPrepared(u6);
            }
        };
        this.onError = (q6, r6, s6, t6) => {
            console.log('VodMedialoader onError called, url: ' + q6 + " index " + r6 + " code: " + s6 + " msg " + t6);
            if (this.mOnLoadStatusListener) {
                this.mOnLoadStatusListener.onError(q6, r6, s6, t6);
            }
            if (s6 != PlayerErrorCode.MEDIALOADER_ERROR_ADDED) {
                VodMediaLoader.getInstance().cancel(q6, r6);
            }
        };
        this.onErrorV2 = (k6, l6, m6, n6) => {
            console.log('VodMedialoader onErrorV2 called, url: ' + k6 + " index " + l6 + " code: " + m6 + " msg " + n6);
            if (this.mOnLoadStatusListener != null) {
                let o6 = findErrorCodeByValue(m6);
                let p6 = new ErrorInfo();
                p6.setCode(o6);
                p6.setMsg(n6);
                this.mOnLoadStatusListener.onErrorV2(k6, l6, p6);
            }
            if (m6 != PlayerErrorCode.MEDIALOADER_ERROR_ADDED) {
                VodMediaLoader.getInstance().cancel(k6, l6);
            }
        };
        this.onCompleted = (i6, j6) => {
            if (this.mOnLoadStatusListener) {
                this.mOnLoadStatusListener.onCompleted(i6, j6);
            }
        };
        this.onCanceled = (g6, h6) => {
            if (this.mOnLoadStatusListener) {
                this.mOnLoadStatusListener.onCanceled(g6, h6);
            }
        };
        premierlibrary.nVodMediaLoaderConstruct(this);
    }
    static getInstance() {
        if (!VodMediaLoader.sInstance) {
            VodMediaLoader.sInstance = new VodMediaLoader();
        }
        return VodMediaLoader.sInstance;
    }
    prepareVidStsSource(a6) {
        premierlibrary.nVodMediaLoaderPrepareSts(a6);
    }
    prepareVidAuthSource(z5) {
        premierlibrary.nVodMediaLoaderPrepareAuth(z5);
    }
    removeVidSource(y5) {
        premierlibrary.nVodMediaLoaderRemoveSource(y5);
    }
    load(t5, u5, v5) {
        if (!t5 || u5 < 0) {
            return;
        }
        let w5 = premierlibrary.nVodMediaLoaderGetVodUrl(t5, u5);
        let x5 = AliPlayerGlobalSettings.OnGetUrlHashCallback(w5);
        premierlibrary.nVodMediaLoaderLoad(t5, u5, x5, v5);
    }
    cancel(p5, q5) {
        let r5 = premierlibrary.nVodMediaLoaderGetVodUrl(p5, q5);
        let s5 = AliPlayerGlobalSettings.OnGetUrlHashCallback(r5);
        premierlibrary.nVodMediaLoaderCancel(p5, q5, s5);
    }
    pause(l5, m5) {
        if (!l5 || m5 < 0) {
            return;
        }
        let n5 = premierlibrary.nVodMediaLoaderGetVodUrl(l5, m5);
        let o5 = AliPlayerGlobalSettings.OnGetUrlHashCallback(n5);
        premierlibrary.nVodMediaLoaderPause(true, l5, m5, o5);
    }
    resume(h5, i5) {
        if (!h5 || i5 < 0) {
            return;
        }
        let j5 = premierlibrary.nVodMediaLoaderGetVodUrl(h5, i5);
        let k5 = AliPlayerGlobalSettings.OnGetUrlHashCallback(j5);
        premierlibrary.nVodMediaLoaderPause(false, h5, i5, k5);
    }
    setOnLoadStatusListener(g5) {
        this.mOnLoadStatusListener = g5;
    }
}
