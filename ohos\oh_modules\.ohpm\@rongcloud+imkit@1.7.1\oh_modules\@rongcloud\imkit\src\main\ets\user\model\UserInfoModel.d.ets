// @keepTs
// @ts-nocheck
/**
 * Created on 2024/8/9
 * <AUTHOR>
 * @version 1.0.0
 */
import { UserType } from '@rongcloud/imlib';
/**
 * 用户信息
 * @version 1.0.0
 */
@Observed
export declare class UserInfoModel {
    /**
     * 用户 id
     */
    userId: string;
    /**
     * 用户名
     */
    name: string;
    /**
     * 用户昵称
     */
    alias: string;
    /**
     * 用户头像
     */
    portraitUri: string | Resource;
    /**
     * 扩展字段
     */
    extra: string;
    /**
     * 用户类型
     * @version 1.7.1
     */
    userType: UserType;
    /**
     * 获取 UI 展示的名字
     *```
     * 优先展示 alias
     * 否则展示 name
     * 否则展示 user<userId>
     *```
     * @returns 名字
     */
    getDisplayName(): string;
    /**
     * 用户信息构造方法
     * @param userId 用户 Id
     * @param name 用户名称
     * @param portraitUri 头像地址
     * @param alias 备注
     * @param extra 附件字段
     */
    constructor(t357: string, u357: string, v357: string | Resource, w357?: string, x357?: string, y357?: UserType);
}
