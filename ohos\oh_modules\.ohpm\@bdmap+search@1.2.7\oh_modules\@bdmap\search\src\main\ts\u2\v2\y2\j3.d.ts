import { BuildingSearchOption } from "../../../d/e2/h2";
import { SearchRequest } from '../../base/base';
import { UrlProvider } from "../../base/d3";
export declare class BuildingSearchRequest extends SearchRequest {
    /**
     * BuildingSearchOption构造函数，用于创建一个BuildingSearchOption对象实例。
     * 该函数接收一个参数option，类型为BuildingSearchOption，表示构建的对象属性。
     * 调用super()函数初始化父类的属性，然后使用传入的option来设置BuildingSearchOption对象的属性。
     *
     * @param option BuildingSearchOption对象的属性，包括buildingId、pageNum和pageSize等。
     */
    constructor(z6: BuildingSearchOption);
    /**
     * @description 构建搜索请求参数，包括经纬度、坐标类型等信息
     *
     * @param option {BuildingSearchOption} - 搜索选项对象，包含latlng（LatLng类型）属性，表示中心点的经纬度坐标
     *
     * @returns {void} - 无返回值
     */
    private buildingSearchRequestBuildParam;
    /**
     * @description 获取URL域名
     * @param {UrlProvider} provider URL提供者对象
     * @returns {string} 返回URL域名字符串
     */
    getUrlDomain(w6: UrlProvider): string;
}
