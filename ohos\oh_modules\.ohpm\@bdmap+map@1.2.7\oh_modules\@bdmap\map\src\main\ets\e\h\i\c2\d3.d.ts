export default class BmObjType { static readonly NONE: number;       static readonly LAYER: number; static readonly DRAW_ITEM: number; static readonly BASE_MARKER: number; static readonly ICON_MARKER: number; static readonly TEXT_MARKER: number; static readonly TEXT_PATH_MARKER: number; static readonly BASE_LINE: number; static readonly POLYLINE: number; static readonly GRADIENT_LINE: number; static readonly POLYGON: number; static readonly CIRCLE: number; static readonly ARC: number; static readonly CLUSTER: number; static readonly CLUSTER_GROUP: number; static readonly CLUSTER_TEMPLATE: number; static readonly CLUSTER_UI: number; static readonly CLUSTER_ICON: number; static readonly CLUSTER_TEXT: number; static readonly POI_LAYOUT: number; static readonly MODEL_3D: number; static readonly MODEL_3D_GROUP: number; static readonly GROUND: number; static readonly PRISM: number; static readonly MULTIPOINT: number; static readonly TRACK: number;       static readonly RICH_VIEW: number; static readonly BASE_UI: number; static readonly GROUP_UI: number; static readonly LABEL_UI: number; static readonly IMAGE_UI: number; static readonly HORIZONTAL_LAYOUT: number; static readonly VERTICAL_LAYOUT: number; static readonly FRAME_LAYOUT: number; static readonly RICHUI_OPTION: number;       static readonly STYLE: number; static readonly LINE_STYLE: number; static readonly LINE_STYLE_OPTION: number; static readonly SURFACE_STYLE: number; static readonly TEXT_STYLE: number; static readonly DRAWABLE_RESOURCE: number; static readonly BITMAP_RESOURCE: number; static readonly FRAME_RESOURCE: number; static readonly GIF_RESOURCE: number; static readonly GUESS_RESOURCE: number; static readonly DRAWABLE_INFLATER: number; static readonly TRACK_STYLE: number;       static readonly GEO_ELEMENT: number; static readonly COORDCHAIN_HANDLE: number;       static readonly ANIMATION: number; static readonly SCALE_ANIMATION: number; static readonly TRANSLATE_ANIMATION: number; static readonly ALPHA_ANIMATION: number; static readonly ROTATE_ANIMATION: number; static readonly TRACK_ANIMATION: number; static readonly ANIMATION_SET: number;       static readonly INTERPOLATOR: number; static readonly LINEAR_INTERPOLATOR: number; static readonly ACCELERATE_DECELERATE_INTERPOLATOR: number; static readonly ACCELERATE_INTERPOLATOR: number; static readonly DECELERATE_INTERPOLATOR: number; static readonly ANTICIPATE_INTERPOLATOR: number; static readonly OVERSHOOT_INTERPOLATOR: number; static readonly ANTICIPATE_OVERSHOOT_INTERPOLATOR: number; static readonly BOUNCE_INTERPOLATOR: number; static readonly CYCLE_INTERPOLATOR: number; } 