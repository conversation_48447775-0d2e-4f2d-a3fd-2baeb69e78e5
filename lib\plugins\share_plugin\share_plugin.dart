import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:flutter_uikit/ui_overlay.dart';
import 'package:wuling_flutter_app/api/store/store_api.dart';

import '../../utils/manager/log_manager.dart';

class BaseConfig{
  /// 基础配置
  late String title;
  late String description;
  late String cover;
}

class WebConfig extends BaseConfig{
  /// 网页地址配置
  late String webpageUrl;
  // factory WebConfig.fromJson(Map<String, dynamic> json){
  //   String shareContent = json["shareContent"] ?? "五菱宝骏车主专属社交平台";
  //   String imgURL = json["imgURL"];
  //   String shareURL = json["shareURL"];
  //   String title = json["title"] ?? "精彩活动，快来收看";
  //   int activityId = json["activityId"] ?? 0;
  //   return WebConfig(
  //       shareURL
  //   );
  // }

}

class MiniConfig extends BaseConfig{
  /// 小程序配置
  String userName = "gh_bd7a2c3f3483";
  late String path;
}

class SharePlugin {

  static const userName = "userName";

  static const MethodChannel _channel =
  MethodChannel('com.sgmw.flutter/share');

  /// 分享  shareContent,imgURL,shareURL,title,shareType,activityId
  static Future<String> shareData(dynamic msg) async {
    LogManager().debug(msg);
    int shareType = msg["shareType"] ?? 0;
    int activityId = msg["activityId"] ?? 0;
    // 1：分享app，2：分享社区,3：分享帖子，4：分享活动，5：分享直播，6：分享话题，7：分享经销商活动，8：分享整车商品，9：分享优品商品，10：分享拼团活动
    if(shareType != 0){
      storeApi.getAccountUserShare(activityId,shareType);
    }

    String shareContent = msg["shareContent"] ?? "五菱宝骏车主专属社交平台";
    String imgURL = msg["path"] ?? msg["imgURL"];
    String shareURL = msg["shareURL"];
    String title = msg["title"] ?? "精彩活动，快来收看";

    return SharePlugin.shareFriend(
      shareURL,
      title:title,
      description:shareContent,
      cover:imgURL,
      userName: msg["username"],
    );
  }

  /// 分享到好友
  static Future<String> shareConfigFriend(dynamic data) async {
    final String message = await _channel.invokeMethod('shareFriend', data).catchError((e){
      UIOverlay.toast(e.toString());
      return "";
    });
    return message;
  }

  /// 分享到朋友圈
  static Future<String> shareConfigCommon(BaseConfig msg) async {
    final String message = await _channel.invokeMethod('shareCommon', msg).catchError((e){
      UIOverlay.toast("分享到朋友圈失败");
      return "";
    });
    return message;
  }


  /// 分享到好友
  /// msg: string           小程序的path，web的uri，文本的content，图片的url
  /// title: String         标题
  /// description: String   介绍
  /// userName: String      小程序id
  /// cover: String         封面
  /// data: dynamic         数据
  static Future<String> shareFriend(String msg,{String? title,String? description,String? userName,String? cover,dynamic d}) async {
    dynamic data = msg;
    if(userName != null){
      assert(description!=null,title!=null);
      data = {
        "title":title,
        "description":description,
        "path":msg,
        "userName": userName,
        "cover":cover,
        "data":d
      };
    }else if(title != null){
      assert(description!=null);
      data = {
        "title":title,
        "description":description,
        "webpageUrl":msg,
        "cover":cover,
        "data":d
      };
    }else{
      data = msg;
    }

    final String message = await _channel.invokeMethod('shareFriend', data).catchError((e){
      UIOverlay.toast("分享到好友失败");
      return "";
    });
    return message;
  }

  /// 分享到朋友圈
  /// msg:string     小程序的path，web的uri，文本的content，图片的url
  static Future<String> shareCommon(String msg,{String? title,String? description,String? userName,String? cover,dynamic data}) async {
    dynamic data = msg;
    if(userName != null){
      assert(description!=null,title!=null);
      data = {
        "title":title,
        "description":description,
        "path":msg,
        "userName": userName,
        "cover":cover,
        "data":data
      };
    }else if(title != null){
      assert(description!=null);
      data = {
        "title":title,
        "description":description,
        "webpageUrl":msg,
        "userName": userName,
        "cover":cover,
        "data":data
      };
    }else{
      data = msg;
    }

    final String message = await _channel.invokeMethod('shareCommon', data).catchError((e){
      UIOverlay.toast("分享到朋友圈失败");
      return "";
    });
    return message;
  }

  /// 打开小程序
  static Future<String> openMini(dynamic msg) async {
    final String message = await _channel.invokeMethod('openMini', msg).catchError((e){
      UIOverlay.toast("打开小程序失败");
      return "";
    });
    return message;
  }

  /// 打开微信
  static Future<String> openWX(dynamic msg) async {
    final String message = await _channel.invokeMethod('openWX', msg);
    return message;
  }

  /// 微信登录
  static Future<dynamic> login(dynamic msg) async {
    final dynamic message = await _channel.invokeMethod('login', msg);
    return message;
  }

  /// 保存图片到相册
  static Future<String> saveImage(dynamic msg) async {
    LogManager().debug(msg);
    final String message = await _channel.invokeMethod('saveImage',base64).catchError((e){
      LogManager().debug(e.toString());
      return "";
    });

    return message;
  }

  /// 获取定位
  static Future<dynamic> getLocation() async {
    final dynamic message = await _channel.invokeMethod('getLocation').catchError((e){
      UIOverlay.toast(e.toString());
    });
    return message;
  }

  static Future<String> image2Base64(String path) async {
    File file =  File(path);
    List<int> imageBytes = await file.readAsBytes();
    return base64Encode(imageBytes);
  }

}
