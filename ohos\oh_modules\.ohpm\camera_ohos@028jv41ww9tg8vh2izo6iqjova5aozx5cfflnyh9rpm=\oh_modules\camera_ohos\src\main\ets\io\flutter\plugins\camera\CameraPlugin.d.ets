import { AbilityAware, AbilityPluginBinding } from '@ohos/flutter_ohos';
import { FlutterPlugin, FlutterPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
import "@ohos/flutter_ohos/src/main/ets/component/FlutterComponent";
export default class CameraPlugin implements FlutterPlugin, AbilityAware {
    private TAG;
    private flutterPluginBinding;
    private methodCallHandler;
    private textureRegistry;
    constructor();
    getUniqueClassName(): string;
    onAttachedToEngine(binding: FlutterPluginBinding): void;
    onDetachedFromEngine(binding: FlutterPluginBinding): void;
    onAttachedToAbility(binding: AbilityPluginBinding): void;
    onDetachedFromAbility(): void;
}
