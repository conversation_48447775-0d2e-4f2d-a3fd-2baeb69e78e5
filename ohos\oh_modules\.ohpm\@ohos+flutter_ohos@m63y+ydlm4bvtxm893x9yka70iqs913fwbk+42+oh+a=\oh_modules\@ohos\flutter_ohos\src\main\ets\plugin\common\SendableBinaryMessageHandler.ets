/*
* Copyright (c) 2023 Hunan OpenValley Digital Industry Development Co., Ltd. All rights reserved.
* Use of this source code is governed by a BSD-style license that can be
* found in the LICENSE_KHZG file.
*/
import { lang } from '@kit.ArkTS';
import { BinaryReply } from './BinaryMessenger';

type ISendable = lang.ISendable;
export default interface SendableBinaryMessageHandler extends ISendable {
  onMessage(message: <PERSON><PERSON>yBuffer, reply: BinaryReply, ...args: Object[]): void;
}
