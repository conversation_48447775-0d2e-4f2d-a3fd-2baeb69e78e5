import { LatLng } from '@bdmap/base';
/**
 * 室内POI信息类
 */
export interface PoiIndoorInfo {
    /**
     * poi地址信息
     */
    address: string;
    /**
     * 该室内poi所在室内ID
     */
    bid: string;
    /**
     * 该室内poi所在城市的city id
     */
    cid: number;
    /**
     * poi折扣信息
     */
    discount: number;
    /**
     * poi点所在层级
     */
    floor: string;
    /**
     * poi名称
     */
    name: string;
    /**
     * 商铺电话
     */
    phone: string;
    /**
     * poi商铺价格
     */
    price: number;
    /**
     * 商铺所在地理坐标
     */
    latLng: LatLng | null;
    /**
     * 星级 (0-50), 50 表示5星
     */
    starLevel: number;
    /**
     * 是否有团购信息
     */
    isGroup: boolean;
    /**
     * 是否有外卖
     */
    isTakeOut: boolean;
    /**
     * 是否需要排队
     */
    isWaited: boolean;
    /**
     * poi点的uid
     */
    uid: string;
    /**
     * 商铺的类型
     */
    tag: string;
    /**
     * 团购数量
     */
    groupNum: number;
}
