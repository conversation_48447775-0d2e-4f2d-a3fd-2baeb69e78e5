import { AVPBase } from './AVPBase';
import { OhosUrlPlayer } from './nativeclass/OhosUrlPlayer';
import { UrlSource } from './source/UrlSource';
export class UrlVideoPlayer extends AVPBase {
    constructor(t41, u41) {
        super(t41, u41);
    }
    createAlivcMediaPlayer(r41) {
        let s41 = new OhosUrlPlayer(r41);
        return s41;
    }
    setUrlDataSource(p41) {
        let q41 = this.getCorePlayer();
        if (q41 instanceof OhosUrlPlayer) {
            if (p41 instanceof UrlSource) {
                q41.setUrlDataSource(p41);
            }
        }
    }
}
