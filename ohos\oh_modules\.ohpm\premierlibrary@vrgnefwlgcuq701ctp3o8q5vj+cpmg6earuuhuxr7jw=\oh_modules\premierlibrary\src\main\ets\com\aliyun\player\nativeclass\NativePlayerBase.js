import premierlibrary from 'libpremierlibrary.so';
import { Log } from '../../utils/Log';
import { Option, AudioStatus, } from '../IPlayer';
import { InfoBean } from '../bean/InfoBean';
import { findInfoCodeByValue, InfoCode } from '../bean/InfoCode';
import { findErrorCodeByValue } from '../bean/PlayerErrorCode';
import { ErrorInfo } from '../bean/ErrorInfo';
import { ObjCreateHelper } from '../../utils/ObjCreateHelper';
const log = new Log('NativePlayerBase');
export class NativePlayerBase {
    constructor(i26) {
        this.mNativeContext = 0;
        this.objHelper = new ObjCreateHelper();
        this.getNativeContext = () => {
            log.info(`getNativeContext, address: ${this.mNativeContext}`);
            return this.mNativeContext;
        };
        this.setNativeContext = (u29) => {
            log.info(`setNativeContext, address: ${u29}`);
            this.mNativeContext = u29;
        };
        this.getHelper = () => {
            log.info(`getHelper`);
            return this.objHelper;
        };
        this.getContext = () => {
            log.info(`getNativeContext, address: ${this.mNativeContext}`);
            return this.mContext;
        };
        this.onPrepared = () => {
            console.log('onPrepared called at nativePlayerBase.');
            if (this.mOnPreparedListener != null) {
                this.mOnPreparedListener.onPrepared();
            }
        };
        this.onCircleStart = () => {
            if (this.mOnInfoListener != null) {
                let t29 = new InfoBean();
                t29.setCode(InfoCode.LoopingStart);
                this.mOnInfoListener.onInfo(t29);
            }
        };
        this.onAutoPlayStart = () => {
            if (this.mOnInfoListener != null) {
                let s29 = new InfoBean();
                s29.setCode(InfoCode.AutoPlayStart);
                this.mOnInfoListener.onInfo(s29);
            }
        };
        this.onEvent = (o29, p29) => {
            if (this.mOnInfoListener != null) {
                let q29 = findInfoCodeByValue(o29);
                let r29 = new InfoBean();
                r29.setCode(q29);
                r29.setExtraMsg(p29);
                this.mOnInfoListener.onInfo(r29);
            }
        };
        this.onCurrentDownloadSpeed = (m29) => {
            if (this.mOnInfoListener != null) {
                let n29 = new InfoBean();
                n29.setCode(InfoCode.CurrentDownloadSpeed);
                n29.setExtraValue(m29);
                this.mOnInfoListener.onInfo(n29);
            }
        };
        this.onUtcTimeUpdate = (k29) => {
            if (this.mOnInfoListener != null) {
                let l29 = new InfoBean();
                l29.setCode(InfoCode.UtcTime);
                l29.setExtraValue(k29);
                this.mOnInfoListener.onInfo(l29);
            }
        };
        this.onLocalCacheLoad = (i29) => {
            if (this.mOnInfoListener != null) {
                let j29 = new InfoBean();
                j29.setCode(InfoCode.LocalCacheLoaded);
                j29.setExtraValue(i29);
                this.mOnInfoListener.onInfo(j29);
            }
        };
        this.onBufferedPositionUpdate = (g29) => {
            if (this.mOnInfoListener != null) {
                let h29 = new InfoBean();
                h29.setCode(InfoCode.BufferedPosition);
                h29.setExtraValue(g29);
                this.mOnInfoListener.onInfo(h29);
            }
        };
        this.onCurrentPositionUpdate = (e29) => {
            if (this.mOnInfoListener != null) {
                let f29 = new InfoBean();
                f29.setCode(InfoCode.CurrentPosition);
                f29.setExtraValue(e29);
                this.mOnInfoListener.onInfo(f29);
            }
        };
        this.onFirstFrameShow = () => {
            if (this.mOnRenderingStartListener != null) {
                this.mOnRenderingStartListener.onRenderingStart();
            }
        };
        this.onVideoSizeChanged = (c29, d29) => {
            if (this.mOnVideoSizeChangedListener != null) {
                this.mOnVideoSizeChangedListener.onVideoSizeChanged(c29, d29);
            }
        };
        this.onAudioInterruptEvent = (b29) => {
            if (this.mOnAudioInterruptEventListener != null) {
                if (b29 < AudioStatus.AUDIO_STATUS_DEFAULT || b29 > AudioStatus.AUDIO_STATUS_UNDUCK) {
                    log.info('onAudioInterruptEvent exception audioStatus: ' + b29);
                    this.mOnAudioInterruptEventListener.onAudioInterruptEvent(AudioStatus.AUDIO_STATUS_DEFAULT);
                    return;
                }
                this.mOnAudioInterruptEventListener.onAudioInterruptEvent(b29);
            }
        };
        this.onLoadingStart = () => {
            if (this.mOnLoadingStatusListener != null) {
                this.mOnLoadingStatusListener.onLoadingBegin();
            }
        };
        this.onLoadingProgress = (a29) => {
            if (this.mOnLoadingStatusListener != null) {
                this.mOnLoadingStatusListener.onLoadingProgress(a29, 0);
            }
        };
        this.onLoadingEnd = () => {
            if (this.mOnLoadingStatusListener != null) {
                this.mOnLoadingStatusListener.onLoadingEnd();
            }
        };
        this.onAVNotSyncStart = (z28) => {
            if (this.mOnAVNotSyncStatusListener != null) {
                this.mOnAVNotSyncStatusListener.onAVNotSyncStart(z28);
            }
        };
        this.onAVNotSyncEnd = (y28) => {
            if (this.mOnAVNotSyncStatusListener != null) {
                this.mOnAVNotSyncStatusListener.onAVNotSyncEnd();
            }
        };
        this.onSeekEnd = () => {
            if (this.mOnSeekCompleteListener != null) {
                this.mOnSeekCompleteListener.onSeekComplete();
            }
        };
        this.onStatusChanged = (w28, x28) => {
            log.info('onStatusChanged, newState: ' + w28 + ' oldState: ' + x28);
            if (this.mOnStateChangedListener != null) {
                this.mOnStateChangedListener.onStateChanged(w28);
            }
        };
        this.onCompletion = () => {
            if (this.mOnCompletionListener != null) {
                this.mOnCompletionListener.onCompletion();
            }
        };
        this.onVideoRendered = (u28, v28) => {
            if (this.mOnVideoRenderedListener != null) {
                this.mOnVideoRenderedListener.onVideoRendered(u28, v28);
            }
        };
        this.onShowSubtitle = (r28, s28, t28) => {
            if (this.mOnSubtitleDisplayListener != null) {
                this.mOnSubtitleDisplayListener.onSubtitleShow(r28, s28, t28);
            }
        };
        this.onSubtitleExtAdded = (p28, q28) => {
            if (this.mOnSubtitleDisplayListener != null) {
                this.mOnSubtitleDisplayListener.onSubtitleExtAdded(p28, q28);
            }
        };
        this.onHideSubtitle = (n28, o28) => {
            if (this.mOnSubtitleDisplayListener != null) {
                this.mOnSubtitleDisplayListener.onSubtitleHide(n28, o28);
            }
        };
        this.onSubtitleHeader = (l28, m28) => {
            if (this.mOnSubtitleDisplayListener != null) {
                this.mOnSubtitleDisplayListener.onSubtitleHeader(l28, m28);
            }
        };
        this.onError = (g28, h28, i28) => {
            let j28 = findErrorCodeByValue(g28);
            if (this.mOnErrorListener != null) {
                let k28 = new ErrorInfo();
                k28.setCode(j28);
                k28.setMsg(h28);
                k28.setExtra(i28);
                this.mOnErrorListener.onError(k28);
            }
        };
        this.onStreamSwitchSuc = (f28) => {
            if (this.mOnTrackChangedListener != null) {
                this.mOnTrackChangedListener.onChangedSuccess(f28);
            }
        };
        this.onStreamInfoGet = (e28) => {
            if (this.mOnTrackReadyListener != null) {
                this.mOnTrackReadyListener.onTrackReady(e28);
            }
        };
        this.onSubStreamInfoGet = (d28) => {
            if (this.mOnSubTrackReadyListener != null) {
                this.mOnSubTrackReadyListener.onSubTrackReady(d28);
            }
        };
        this.onSwitchStreamUrlResult = (y27, z27, a28) => {
            if (this.mOnStreamSwitchedListener == null) {
                return;
            }
            if (z27 == 0) {
                this.mOnStreamSwitchedListener.onSwitchedSuccess(y27);
            }
            else {
                let b28 = findErrorCodeByValue(z27);
                let c28 = new ErrorInfo();
                c28.setCode(b28);
                c28.setMsg(a28);
                this.mOnStreamSwitchedListener.onSwitchedFail(y27, c28);
            }
        };
        this.onCaptureScreen = (v27, w27, x27) => {
            if (this.mOnSnapShotListener != null) {
                this.mOnSnapShotListener.onSnapShot(x27, v27, w27);
            }
        };
        this.onSeiDataCallback = (t27, u27) => {
            if (this.mOnSeiDataListener != null) {
                this.mOnSeiDataListener.onSeiData(t27, new Uint8Array(u27));
            }
        };
        log.info('constructor_');
        this.mContext = i26;
        premierlibrary.nConstruct(this);
    }
    setOnPreparedListener(h26) {
        this.mOnPreparedListener = h26;
    }
    setOnInfoListener(g26) {
        this.mOnInfoListener = g26;
    }
    setOnRenderingStartListener(f26) {
        this.mOnRenderingStartListener = f26;
    }
    setOnStateChangedListener(e26) {
        this.mOnStateChangedListener = e26;
    }
    setOnCompletionListener(d26) {
        this.mOnCompletionListener = d26;
    }
    setOnLoadingStatusListener(c26) {
        this.mOnLoadingStatusListener = c26;
    }
    setOnErrorListener(b26) {
        this.mOnErrorListener = b26;
    }
    setOnVideoSizeChangedListener(a26) {
        this.mOnVideoSizeChangedListener = a26;
    }
    setOnSeekCompleteListener(z25) {
        this.mOnSeekCompleteListener = z25;
    }
    setOnSubtitleDisplayListener(y25) {
        this.mOnSubtitleDisplayListener = y25;
    }
    setOnVideoRenderedListener(x25) {
        this.mOnVideoRenderedListener = x25;
    }
    setOnAudioInterruptEventListener(w25) {
        this.mOnAudioInterruptEventListener = w25;
    }
    setOnTrackReadyListener(v25) {
        this.mOnTrackReadyListener = v25;
    }
    setOnSubTrackReadyListener(u25) {
        this.mOnSubTrackReadyListener = u25;
    }
    setOnStreamSwitchedListener(t25) {
        this.mOnStreamSwitchedListener = t25;
    }
    setOnAVNotSyncStatusListener(s25) {
        this.mOnAVNotSyncStatusListener = s25;
    }
    setOnTrackChangedListener(r25) {
        this.mOnTrackChangedListener = r25;
    }
    setOnSnapShotListener(q25) {
        this.mOnSnapShotListener = q25;
    }
    setOnSeiDataListener(p25) {
        this.mOnSeiDataListener = p25;
    }
    start() {
        log.info('nStart');
        premierlibrary.nStart(this);
    }
    pause() {
        log.info('nPause');
        premierlibrary.nPause(this);
    }
    stop() {
        log.info('nStop');
        premierlibrary.nStop(this);
    }
    prepare() {
        log.info('nPrepare');
        premierlibrary.nPrepare(this);
    }
    setAutoPlay(o25) {
        log.info('nPrepare');
        premierlibrary.nSetAutoPlay(this, o25);
    }
    setSurfaceId(n25) {
        log.info('nSetSurfaceId');
        premierlibrary.nSetSurfaceId(this, n25);
    }
    setSpeed(m25) {
        log.info('setSpeed');
        premierlibrary.nSetSpeed(this, m25);
    }
    switchStream(l25) {
        log.info('switchStream');
        premierlibrary.nSwitchStream(this, l25);
    }
    setAlphaRenderMode(k25) {
        log.info('setAlphaRenderMode');
        premierlibrary.nSetAlphaRenderMode(this, k25);
    }
    getAlphaRenderMode() {
        log.info('getAlphaRenderMode');
        return premierlibrary.nGetAlphaRenderMode(this);
    }
    selectTrack(j25) {
        log.info('selectTrack');
        premierlibrary.nSelectTrack(this, j25);
    }
    getCurrentTrack(h25) {
        log.info('getCurrentTrack, type: ' + h25);
        let i25 = premierlibrary.nGetCurrentStreamInfo(this, h25);
        return i25;
    }
    addExtSubtitle(g25) {
        log.info(`addExtSubtitle`);
        premierlibrary.nAddExtSubtitle(this, g25);
    }
    selectExtSubtitle(e25, f25) {
        log.info(`selectExtSubtitle`);
        premierlibrary.nSelectExtSubtitle(this, e25, f25);
    }
    setVolume(d25) {
        log.info('setVolume');
        premierlibrary.nSetVolume(this, d25);
    }
    getVolume() {
        log.info('getVolume');
        return premierlibrary.nGetVolume(this);
    }
    seekTo(b25, c25) {
        log.info('seekTo');
        premierlibrary.nSeekTo(this, b25, c25);
    }
    setStartTime(z24, a25) {
        log.info('setStartTime');
        premierlibrary.nSetStartTime(this, z24, a25);
    }
    getDuration() {
        log.info('getDuration');
        return premierlibrary.nGetDuration(this);
    }
    setGlobalTime(y24) {
        premierlibrary.nSetGlobalTime(this, y24);
    }
    getPlayedDuration() {
        log.info('getPlayedDuration');
        return premierlibrary.nGetPlayedDuration(this);
    }
    getCurrentPosition() {
        log.info('getCurrentPosition');
        return premierlibrary.nGetCurrentPosition(this);
    }
    getBufferedPosition() {
        log.info('getBufferedPosition');
        return premierlibrary.nGetBufferedPosition(this);
    }
    getPlayerStatus() {
        log.info('getPlayerStatus');
        return premierlibrary.nGetPlayerStatus(this);
    }
    setTraceId(x24) {
        log.info('setTraceId');
        premierlibrary.nSetTraceId(this, x24);
    }
    enableHardwareDecoder(w24) {
        log.info('enableHardwareDecoder');
        premierlibrary.nEnableHardwareDecoder(this, w24);
    }
    release() {
        log.info('release');
        premierlibrary.nRelease(this);
    }
    setMute(v24) {
        log.info('setMute');
        premierlibrary.nSetMute(this, v24);
    }
    isMuted() {
        log.info('isMuted');
        return premierlibrary.nIsMuted(this) === 1;
    }
    setScaleMode(u24) {
        log.info('setScaleMode');
        premierlibrary.nSetScaleMode(this, u24);
    }
    getScaleMode() {
        log.info('getScaleMode');
        return premierlibrary.nGetScaleMode(this);
    }
    setLoop(t24) {
        log.info('setLoop');
        premierlibrary.nSetLoop(this, t24);
    }
    isLoop() {
        log.info('isLoop');
        return premierlibrary.nIsLoop(this) === 1;
    }
    getVideoWidth() {
        log.info('getVideoWidth');
        return premierlibrary.nGetVideoWidth(this);
    }
    getVideoHeight() {
        log.info('getVideoHeight');
        return premierlibrary.nGetVideoHeight(this);
    }
    getVideoRotation() {
        log.info('getVideoRotation');
        return premierlibrary.nGetVideoRotation(this);
    }
    reload() {
        log.info('reload');
        premierlibrary.nReload(this);
    }
    setRotateMode(s24) {
        log.info('setRotateMode');
        premierlibrary.nSetRotateMode(this, s24);
    }
    getRotateMode() {
        log.info('getRotateMode');
        return premierlibrary.nGetRotateMode(this);
    }
    setMirrorMode(r24) {
        log.info('setMirrorMode');
        premierlibrary.nSetMirrorMode(this, r24);
    }
    getMirrorMode() {
        log.info('getMirrorMode');
        return premierlibrary.nGetMirrorMode(this);
    }
    setVideoBackgroundColor(q24) {
        log.info('setVideoBackgroundColor');
        premierlibrary.nSetVideoBackgroundColor(this, q24);
    }
    getSpeed() {
        log.info('getSpeed');
        return premierlibrary.nGetSpeed(this);
    }
    isAutoPlay() {
        log.info('isAutoPlay');
        return premierlibrary.nIsAutoPlay(this) === 1;
    }
    setConfig(p24) {
        log.info('setConfig');
        premierlibrary.nSetConfig(this, p24);
        this.mPlayerConfig = p24;
    }
    getConfig() {
        log.info('getConfig');
        let o24 = premierlibrary.nGetConfig(this);
        return o24;
    }
    setOption(m24, n24) {
        log.info('setOption');
        premierlibrary.nSetOption(this, m24, n24);
    }
    getOption(j24) {
        let k24 = premierlibrary.nGetOption(this, j24);
        if (j24 == Option.RenderFPS ||
            j24 == Option.DownloadBitrate ||
            j24 == Option.VideoBitrate ||
            j24 == Option.AudioBitrate) {
            let l24 = parseFloat(k24);
            return l24;
        }
        return k24;
    }
    setStreamDelay(h24, i24) {
        log.info('setStreamDelay');
        premierlibrary.nSetStreamDelay(this, h24, i24);
    }
    setMaxAccurateSeekDelta(g24) {
        log.info('setMaxAccurateSeekDelta');
        premierlibrary.nSetMaxAccurateSeekDelta(this, g24);
    }
    setCacheConfig(f24) {
        log.info('setCacheConfig');
        premierlibrary.nSetCacheConfig(this, f24);
    }
    setIPResolveType(e24) {
        log.info('setIPResolveType');
        premierlibrary.nSetIPResolveType(this, e24);
    }
    setFastStart(d24) {
        log.info('setFastStart');
        premierlibrary.nSetFastStart(this, d24);
    }
    snapShot() {
        log.info('snapShot');
        premierlibrary.nSnapShot(this);
    }
    clearScreen() {
        log.info('clearScreen');
        premierlibrary.nClearScreen(this);
    }
    getSdkVersion() {
        log.info('getSdkVersion');
        return premierlibrary.nGetSdkVersion();
    }
    getCacheFilePathByUrl(c24) {
        log.info('getCacheFilePathByUrl');
        return premierlibrary.nGetCacheFilePathByUrl(this, c24);
    }
    getCacheFilePathByVid(y23, z23, a24, b24) {
        log.info('getCacheFilePathByVid');
        return premierlibrary.nGetCacheFilePathByVid(this, y23, z23, a24, b24);
    }
    getPropertyString(x23) {
        log.info('getPropertyString');
        return premierlibrary.nGetPropertyString(this, x23);
    }
    setDefaultBandWidth(w23) {
        log.info('setDefaultBandWidth');
        premierlibrary.nSetDefaultBandWidth(this, w23);
    }
    sendCustomEvent(v23) {
        log.info('sendCustomEvent');
        premierlibrary.nSendCustomEvent(this, v23);
    }
    setVideoTag(u23) {
        log.info('setVideoTag');
        premierlibrary.nSetVideoTag(this, u23);
    }
    setUserData(t23) {
        log.info('setUserData');
        premierlibrary.nSetUserData(this, t23);
    }
    getUserData() {
        log.info('getUserData');
        return premierlibrary.nGetUserData(this);
    }
    getNativeContextAddr() {
        return this.mNativeContext;
    }
}
