export class CacheConfig {
    constructor() {
        this.mEnable = false;
        this.mDir = "";
        this.mMaxSizeMB = 0;
        this.mMaxDurationS = 0;
    }
    isCacheEnabled() {
        return this.mEnable;
    }
    setCacheEnabled(x21) {
        this.mEnable = x21;
    }
    getCacheDir() {
        return this.mDir;
    }
    setCacheDir(w21) {
        this.mDir = w21;
    }
    getMaxCacheSizeMB() {
        return this.mMaxSizeMB;
    }
    setMaxCacheSizeMB(v21) {
        this.mMaxSizeMB = v21;
    }
    getMaxDurationS() {
        return this.mMaxDurationS;
    }
    setMaxDurationS(u21) {
        this.mMaxDurationS = u21;
    }
}
