    import BmObject from "../u2"; import BmAnimationListener from "./v2"; import BmInterpolator from "./w2"; export default abstract class BmAnimation extends BmObject {       static readonly INFINITE: number;         static readonly RESTART: number;         static readonly REVERSE: number;           private static readonly START_ON_FIRST_FRAME;       private static readonly ABSOLUTE;         private static readonly RELATIVE_TO_SELF;              static readonly FILL_BEFORE: number;       static readonly FILL_FIRST: number;       static readonly FILL_AFTER: number;         mFillMode: number;         mStartDelay: number;       mRepeatDelay: number;       mDuration: number;         mRepeatCount: number;           mRepeatMode: number;       private mInterpolator; private mListener;       private mExtParam; private static readonly START; private static readonly END; private static readonly REPEAT;       private static wkListenerAnimations; constructor(y8: number, z8: number);       reset(): void; pause(): void; resume(): void;                     cancel(): void;           setInterpolator(x8: BmInterpolator): void;                 setStartDelay(w8: number): void; setRepeatDelay(v8: number): void; setDuration(u8: number): void;               setStartTime(t8: number): void;       start(): void;               setRepeatMode(repeatMode: number): void; setRepeatCount(repeatCount: number): void;               setFillMode(fillMode: number): void; setAnimationListener(listener: BmAnimationListener): void;       setExtParam(s8: string): void; getExtParam(): string; static dispatchAnimationListener(p8: number, action: number): void; static addAnimation(animation: BmAnimation): void;   static removeAnimation(animation: BmAnimation): void; } 