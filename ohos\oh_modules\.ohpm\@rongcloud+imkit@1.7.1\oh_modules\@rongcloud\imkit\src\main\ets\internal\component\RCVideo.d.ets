// @keepTs
// @ts-nocheck
import { Message } from '@rongcloud/imlib';
/**
 * 自定义 Video 组件
 * # 特点
 *```
 * 1. 除了系统 Video 支持的类型之外，还支持传入 Message 对象（必须是小视频消息）
 * 2. 下载失败后，尝试拿 msgId 或 http 地址手动下载
 * 3. 使用方式和 Video 基本一致
 *```
 * # 示例代码
 *```
 * Video({ src: this.uri, previewUri: 'data:image/jpeg;base64,' + this.sightMessage.base64 })
 * RCVideo({ src: this.uri, previewUri: 'data:image/jpeg;base64,' + this.sightMessage.base64 })
 *```
 */
@Component
export declare struct RCVideo {
    @Prop
    src?: ResourceStr | Message;
    @Prop
    currentProgressRate?: number | string | PlaybackSpeed;
    @Prop
    previewUri?: string | PixelMap | Resource;
    @Prop
    autoPlay: boolean;
    @State
    private duration;
    controller?: VideoController;
    imageAIOptions?: ImageAIOptions;
    onPlayEnd?: () => void;
    onPrepared?: (duration: number) => void;
    onCancelClick?: () => void;
    @State
    private systemSrc?;
    @State
    private systemProgress?;
    @State
    private systemPreviewUri?;
    @State
    private currentTime;
    @State
    private isPlaying;
    @State
    private showOperationBar;
    @State
    private showPause;
    private formatTime;
    aboutToAppear(): void;
    build(): void;
    private updateAfterDownload;
    private onSrcChange;
    private onProgressChange;
    private onPreviewUriChange;
    /**
     * 返回下载 key
     * @param src 数据源
     * @returns 返回下载链接或者消息 Id
     */
    private getDownloadKey;
    private getSystemSrc;
}
