// @keepTs
// @ts-nocheck
/**
 * 自定义聊天设置详情，左侧标题，右侧开关组件
 */
@Component
export declare struct CustomItemToggleView {
    @Link
    toggleOpen: boolean;
    bgColor: Resource;
    showTopMargin: boolean;
    leftContent?: Resource;
    leftFontSize: number;
    leftFontColor: Resource;
    showLine: boolean;
    lineColor: Resource;
    toggleColor: Resource;
    /**
     * 开关状态改变的事件, 返回true: 更新成功, false: 更新失败，需要重置开关状态
     */
    onToggleChange?: (isOn: boolean) => Promise<boolean>;
    build(): void;
}
