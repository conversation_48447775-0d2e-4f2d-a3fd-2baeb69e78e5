import type { NativeCallback } from "./h1"; import type { LayerTag } from "../util/b1/c1"; import type { MapStatusBundle, NativeMapStatusBundle, TextImage } from "./i1"; import type { AnyObject, INearOptions } from "./a2"; import type { BmLayerTag } from "../i/c2/d2"; interface j { x: number; y: number; lat: number; lng: number; } export type LayerAddrBundle = { [tag in LayerTag as string]?: string; }; export interface MapOverlay { } export interface LayerMsgData { layerID: string; } export interface LayerOptions { type?: number; id?: string; layerId?: string; visible?: boolean; show?: number; } export interface RemoveOverlayOptions { id: string; type: string; layerAddr: string; } export interface TransformInput { point: [ number, number ]; type: number; } export interface BackForce { type: number; } export interface MapBundle { } export interface IndoorBundle { uuid?: string; floor?: string; } export interface IndoorFloorBundles { uid: string; curfloor: string; floorlist: Array<string>; } export interface CustomStyleOptions { path?: string; enable?: number; } export interface Result { status: boolean; } export interface DefaultMsg { handle?: string; msg?: any; arg1?: any; arg2?: any; } export interface TextMsgData extends AnyObject { text: string; font_style: number; font_size: number; text_color: number; stroke_color: number; back_color?: number; } export interface MsgBundle { what: string; data: DefaultMsg | LayerMsgData | TextMsgData; } export interface NearOverlays { type: number; id: string; } export interface AddSDKTileBundle { sdktileaddr?: string; datasource: number; sdktiletmpmax: number; url: string; } export interface UpdateSDKTileBundle { sdktileaddr?: string; maxlevel: number; minlevel: number; rectb: number; rectl: number; rectt: number; rectr: number; } export type CallbackBundle = MsgBundle | TextImage | AnyObject | number | null; export interface NativeContext { removeBmLayer: (mapViewId: string, instance: number) => void; addBmLayer: (mapViewId: string, instance: number) => void; changeMapStatusByObj: (mapViewId: string, bundle: MapStatusBundle) => void; getMapStatusObj: (mapViewId: string) => NativeMapStatusBundle; drawOneItem: (mapViewId: string, bundle: MapOverlay) => void; createLayers: (mapViewId: string) => LayerAddrBundle; updateLayer: (mapViewId: string, bundle: LayerOptions) => void; updateOneItem: (mapViewId: string, bundle: MapOverlay) => void; removeOneItem: (mapViewId: string, bundle: MapOverlay) => void; removeItems: (mapViewId: string, ids: Array<RemoveOverlayOptions>) => void; getNearlyRadius: (mapViewId: string, bundle: INearOptions) => Array<NearOverlays>; transformScreenCallback: (mapViewId: string, bundle: TransformInput, callback: (point: j) => void) => void; transformScreen: (mapViewId: string, bundle: TransformInput) => null | j; setMapOnBackOrFore: (mapViewId: string, bundle: BackForce) => void; setShowCustomLayer: (mapViewId: string, bundle: MapBundle) => void; switchIndoorMapFloor: (mapViewId: string, bundle: IndoorBundle) => void; getFocuseIndoorMapInfo: (mapViewId: string, bundle: IndoorBundle) => IndoorFloorBundles; initCustomStyle: (mapViewId: string, bundle: CustomStyleOptions) => Result; setCustomStyleEnable: (mapViewId: string, bundle: CustomStyleOptions) => Result; registerJSMethod: (mapViewId: string, key: string, callback: NativeCallback<string, CallbackBundle>) => void; onceDraw: (mapViewId: string) => void; getDPI: (mapViewId: string) => null | number; addLayer: (mapViewId: string, eUpdataType: number, ulTimerEscap: number, tag: string) => null | number; addSDKTile: (mapViewId: string, bundle: AddSDKTileBundle) => null | number; updateSDKTile: (mapViewId: string, bundle: UpdateSDKTileBundle) => null | number; removeLayer: (mapViewId: string, obj: any) => null | number; cleanSDKTileCache: (mapViewId: string, obj: any) => null | number; setMapStatusLimits: (mapViewId: string, obj: any) => null | number; getLayerIDByTag: (mapViewId: string, layerTag: string) => number; showLayers: (mapViewId: string, layerId: string, isShow: boolean) => number; layersIsShow(mapViewId: string, layerTag: BmLayerTag): number; switchLayer(mapViewId: string, srcLayerId: number, destLayerId: number): boolean; setGestureEnable(mapViewId: string, canMoving: boolean, canZoom: boolean, canRotate: boolean, canOverLook: boolean): any; setGestureConfig(mapViewId: string, zoomWithFingerCenter: boolean, useCenter: AnyObject): any; setMinZoomLevel: (mapViewId: string, level: number) => void; setMaxZoomLevel: (mapViewId: string, level: number) => void; setDBClickEnable: (mapViewId: string, enable: boolean) => void; } export {}; 