import { BikingRoutePlanOption, BikingRouteResult } from "../../../d/e/h/k";
import { DrivingRoutePlanOption, DrivingRouteResult } from "../../../d/e/h/l";
import { MassTransitRoutePlanOption, MassTransitRouteResult } from "../../../d/e/h/i";
import { TransitRoutePlanOption, TransitRouteResult } from "../../../d/e/h/n";
import { WalkingRoutePlanOption, WalkingRouteResult } from "../../../d/e/h/m";
import { BaseSearch } from '../../base/base';
/**
 * @internal
 */
export declare class RoutePlanSearchImp extends BaseSearch implements IRoutePlanSearch {
    /**
     * @description 公交车路线搜索方法，返回一个Promise对象，包含了解析结果。
     * @param {TransitRoutePlanOption} option - 公交车路线计划选项，包含起点、终点和途经点等信息。
     * @param useMultiThread - 接口内部是否使用多线程请求，默认为false，启用后需要手动调用destroy
     * @returns {Promise<TransitRouteResult>} - 返回一个Promise对象，resolve状态下包含了解析结果，reject状态下包含错误信息。
     */
    transitSearch(y27: TransitRoutePlanOption, z27: boolean): Promise<TransitRouteResult>;
    /**
     * @description
     * 步行路线查询，返回一个Promise对象，包含了步行路线的结果。
     *
     * @param option {WalkingRoutePlanOption} - 步行路线计算参数，包含起点、终点和途经点等信息。
     *
     * @returns {Promise<WalkingRouteResult>} - 返回一个Promise对象，resolve状态下包含了步行路线的结果，reject状态下包含错误信息。
     */
    walkingSearch(u27: WalkingRoutePlanOption, v27: boolean): Promise<WalkingRouteResult>;
    /**
     * @deprecated
     * 驾车路线搜索，返回一个Promise对象，包含驾车路线的结果。
     *
     * @param option {DrivingRoutePlanOption} - 驾车路线计算参数，包含起点、终点和可选参数等信息。
     *
     * @returns {Promise<DrivingRouteResult>} - 返回一个Promise对象，resolve状态下包含驾车路线的结果，reject状态下包含错误信息。
     */
    drivingSearch(q27: DrivingRoutePlanOption, r27: boolean): Promise<DrivingRouteResult>;
    /**
     * @description
     * 根据轨道交通路线计划选项进行搜索，返回一个Promise对象，该对象包含了轨道交通路线的结果。
     *
     * @param option MassTransitRoutePlanOption - 轨道交通路线计划选项，包含起点、终点和其他可选参数。
     *
     * @returns Promise<MassTransitRouteResult> - 返回一个Promise对象，该对象包含了轨道交通路线的结果，包括轨道交通路线列表和详情信息等。
     *
     * @throws 无异常抛出。
     */
    masstransitSearch(m27: MassTransitRoutePlanOption, n27: boolean): Promise<MassTransitRouteResult>;
    /**
     * @description
     * 使用自行车路线计划来进行搜索，返回一个Promise对象，该对象包含了一个BikingRouteResult类型的值。
     *
     * @param option - BikingRoutePlanOption类型，表示搜索选项，包括起始点、目标点和可选参数等。
     *
     * @returns Promise<BikingRouteResult> - 返回一个Promise对象，当请求完成时会解析为一个BikingRouteResult类型的值。
     *
     * @throws {Error} 如果发生错误，则抛出一个Error对象。
     */
    bikingSearch(i27: BikingRoutePlanOption, j27: boolean): Promise<BikingRouteResult>;
    destroy(): void;
}
/**
 * @internal
 */
export interface IRoutePlanSearch {
    transitSearch(option: TransitRoutePlanOption, useMultiThread: boolean): Promise<TransitRouteResult>;
    masstransitSearch(option: MassTransitRoutePlanOption, useMultiThread: boolean): Promise<MassTransitRouteResult>;
    walkingSearch(option: WalkingRoutePlanOption, useMultiThread: boolean): Promise<WalkingRouteResult>;
    drivingSearch(option: DrivingRoutePlanOption, useMultiThread: boolean): Promise<DrivingRouteResult>;
    bikingSearch(option: BikingRoutePlanOption, useMultiThread: boolean): Promise<BikingRouteResult>;
    destroy(): void;
}
