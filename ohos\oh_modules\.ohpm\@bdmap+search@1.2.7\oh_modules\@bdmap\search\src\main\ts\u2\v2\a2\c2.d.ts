import { AoiResult } from "../../../d/a2/b2";
import { AoiSearchOption } from "../../../d/a2/d2";
import { BaseSearch } from '../../base/base';
export interface IAoiSearch {
    searchAoi(option: AoiSearchOption): Promise<AoiResult>;
}
export declare class AoiSearchImp extends BaseSearch implements IAoiSearch {
    /**
     * @description 搜索AOI，返回一个Promise对象，包含了AOI结果。
     * @param {AoiSearchOption} option - AOI搜索选项，包含了关键字、类型等信息。
     * @returns {Promise<AoiResult>} Promise对象，resolve的值为AoiResult类型，reject的值为Error类型。
     */
    searchAoi(z5: AoiSearchOption): Promise<AoiResult>;
}
