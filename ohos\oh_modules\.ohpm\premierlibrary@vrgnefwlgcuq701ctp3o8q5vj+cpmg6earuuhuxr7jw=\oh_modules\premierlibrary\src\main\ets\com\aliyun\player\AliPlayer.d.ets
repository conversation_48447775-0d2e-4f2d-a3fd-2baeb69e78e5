import { UrlPlayer } from './UrlPlayer';
import { VidAuth } from './source/VidAuth';
import { VidMps } from './source/VidMps';
import { VidSts } from './source/VidSts';
import { LiveSts } from './source/LiveSts';
import { StsInfo } from './source/StsInfo';
export interface AliPlayer extends UrlPlayer {
    /**
     * 设置vidAuth数据源
     *
     * @param vidAuth auth数据源。见{@link VidAuth}。
     */
    /****
     * Set a vidAuth source.
     *
     * @param vidAuth The specified auth source. See {@link VidAuth}.
     */
    setVidAuthDataSource: (vidAuthSource: VidAuth) => void;
    /**
     * 设置vidSts数据源
     *
     * @param vidSts sts数据源。见{@link VidSts}。
     */
    /****
     * Set a vidSts source.
     *
     * @param vidSts The specified STS source. See {@link VidSts}.
     */
    setVidStsDataSource: (vidStsSource: VidSts) => void;
    /**
     * 设置mps数据源
     *
     * @param vidMps mps数据源。见{@link VidMps}。
     */
    /****
     * Set a vidMps source.
     *
     * @param vidMps The specified MPS source. See {@link VidMps}.
     */
    setVidMpsDataSource: (vidMpsSource: VidMps) => void;
    /**
     * 设置直播sts
     * @param liveSts
     */
    /****
     * set sts for live
     * @param liveSts
     */
    setLiveStsDataSource: (liveStsSource: LiveSts) => void;
    /**
     * 更新StsInfo
     * @param stsInfo
     */
    /****
     * update StsInfo
     * @param stsInfo
     */
    updateStsInfo: (stsInfo: StsInfo) => void;
    /**
     * 更新VidAuth
     * @param vidAuth
     */
    /****
     * update VidAuth
     * @param vidAuth
     */
    updateVidAuth: (vidAuth: VidAuth) => void;
}
