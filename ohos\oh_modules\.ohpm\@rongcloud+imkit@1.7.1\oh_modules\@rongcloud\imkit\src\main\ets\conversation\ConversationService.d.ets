// @keepTs
// @ts-nocheck
import { IBoardPlugin } from './inputbar/component/plugin/IBoardPlugin';
import { IMessageItemProvider } from './item/provider/IMessageItemProvider';
import { UiMessage } from './model/UiMessage';
import { ItemLongClickAction } from '../base/click/ItemLongClickAction';
import { Conversation, ConversationIdentifier, ConversationType, IAsyncResult, ICountOption, ISetConversationTopOption, Message, MessageFlag, PushNotificationLevel, SyncConversationReadStatusListener, ReceivedStatus, IQuietHoursOption } from '@rongcloud/imlib';
import List from '@ohos.util.List';
import { ConversationConfig } from './config/ConversationConfig';
import { MessageClickListener } from './listener/MessageClickListener';
import { ConversationEventListener } from './listener/ConversationEventListener';
import { MessageMoreAction } from './more/MessageMoreAction';
import { HashMap } from '@kit.ArkTS';
import { IExtensionConfig } from './extension/IExtensionConfig';
import { IExtensionModule } from './extension/IExtensionModule';
import { ConversationContentComponentConfig, InputAreaComponentConfig, WatermarkComponentConfig } from './config/ComponentConfig';
import { IEmoticonTab } from './inputbar/component/emoticon/IEmoticonTab';
/**
 * 会话服务，主要处理聊天页面的各种业务
 * @version 1.0.0
 */
export interface ConversationService {
    /**
     * 设置会话配置
     * @param config 配置
     */
    getConversationConfig(): ConversationConfig;
    /**
     * 设置会话配置 会完全覆盖sdk配置，建议通过getConversationConfig获取配置，修更改需要修改的配置
     * @param config 配置
     * @warning 配置不支持动态刷新 UI，必须在聊天页面展示前设置
     */
    setConversationConfig(config: ConversationConfig): void;
    /**
     * 增加聊天页面事件监听
     * @param listener 监听
     * @warning addConversationEventListener & removeConversationEventListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     */
    addConversationEventListener(listener: ConversationEventListener): void;
    /**
     * 移除聊天页面事件监听
     * @param listener 监听
     * @discussion 配合 addConversationEventListener 使用，否则会出现内存泄露
     */
    removeConversationEventListener(listener: ConversationEventListener): void;
    /**
     * 增加消息点击事件监听
     * @param listener 监听
     * @warning addMessageClickListener & removeMessageClickListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     */
    addMessageClickListener(listener: MessageClickListener): void;
    /**
     * 移除消息点击事件监听
     * @param listener 监听
     * @warning addMessageClickListener & removeMessageClickListener 配合使用，避免内存泄露
     */
    removeMessageClickListener(listener: MessageClickListener): void;
    /**
     * 增加消息 item，用消息的 ObjectName 和对应的 provider 绑定
     * # 重要
     *```
     * 自定义消息注册分为两部分
     * 1. 将消息注册给 IMLib，保证消息能够正常的收发：调用 IMEngine registerMessageType
     * 2. 将消息和 provider 进行绑定，保证该消息能在 UI 上正常展示：调用该方法
     *```
     *
     * # 说明
     *```
     * 自定义消息可以通过此方法绑定对应的 provider，如果自定义消息没有绑定，会使用 UnknownMessageObjectName 的 Provider
     * 如果 objectName 是内置消息，会覆盖内置消息的 provider
     *```
     * @param objectName 消息的 objectName
     * @param provider 消息的 provider
     */
    addMessageItemProvider(objectName: string, provider: IMessageItemProvider<object>): void;
    /**
     * 移除消息对应的 provider
     *```
     * UnknownMessageObjectName 的 Provider 是当做占位使用的，可以被覆盖，但是不允许被移除
     * 移除 objectName 对应的 provider 之后，如果 sdk 做消息展示时发现 objectName 没有对应的 provider，会使用 UnknownMessageObjectName 的 Provider
     *```
     * @param objectName 消息的 objectName
     */
    removeMessageItemProvider(objectName: string): void;
    /**
     * 获取消息对应的 provider，如果 objectName 对应的 Provider 不存在，则返回 UnknownMessageObjectName 的 Provider
     * @param objectName
     * @returns
     */
    getMessageItemProvider(objectName: string): IMessageItemProvider<UiMessage>;
    /**
     * 增加消息长按事件，长按事件会增加到每个消息上，如果特定类型的消息不需要某个长按事件，可以通过 ItemLongClickAction.onFilter 过滤
     * @param actionInfo 长按事件
     */
    addMessageItemLongClickAction(actionInfo: ItemLongClickAction<Message>): void;
    /**
     * 在指定索引上增加消息长按事件
     * @param index 索引
     * @param actionInfo  长按事件
     */
    addMessageItemLongClickActionByIndex(index: number, actionInfo: ItemLongClickAction<Message>): void;
    /**
     * 移除指定的消息长按事件
     * @param actionId
     */
    removeMessageItemLongClickAction(actionId: string): void;
    /**
     * 清空所有的消息长按事件
     */
    clearMessageItemLongClickAction(): void;
    /**
     * 找到对应的长按事件索引，找不到对应索引会返回 -1
     * @param actionId 事件 id
     * @returns
     */
    findMessageItemLongClickActionIndex(actionId: string): number;
    /**
     * 获取消息长按事件列表
     * @returns 长按事件列表
     */
    getMessageItemLongClickActionArray(): Array<ItemLongClickAction<Message>>;
    /**
     * 长按消息，点击“更多”之后，增加聊天页面底部的按钮
     * @param actionInfo
     * @discussion 长按消息，点击“更多”之后，sdk 在聊天页面底部内置了删除按钮
     */
    addMessageMoreAction(actionInfo: MessageMoreAction): void;
    /**
     * 长按消息，点击“更多”之后，移除聊天页面底部的按钮
     * @param actionId
     */
    removeMessageMoreAction(actionId: string): void;
    /**
     * 清空所有的按钮，清空 sdk 内置的，同时清空用户自定义的
     */
    clearMessageMoreAction(): void;
    /**
     * 增加扩展面板插件
     * @param plugin
     */
    addBoardPlugin(plugin: IBoardPlugin): void;
    /**
     * 插件插入扩展面板到指定位置
     * @param index
     * @param plugin
     */
    insertBoardPlugin(index: number, plugin: IBoardPlugin): void;
    /**
     * 替换指定位置的插件，如果没有对应的索引，会将插件放到最后
     * @param index
     * @param plugin
     */
    replaceBoardPlugin(index: number, plugin: IBoardPlugin): void;
    /**
     * 移除特定的插件
     * @param plugin
     */
    removeBoardPlugin(plugin: IBoardPlugin): void;
    /**
     * 根据插件名移除特定的插件
     * @param pluginName 插件名
     */
    removeBoardPluginByName(pluginName: string): void;
    /**
     * 清空当前的插件
     *```
     * 调用该方法 sdk 内置插件和自定义的插件都会被清空
     * 如果只需要清空 sdk 内置插件，可以先调用该方法，清空 sdk 内置插件后再调用 add insert 方法插入自定义插件
     *```
     */
    clearBoardPlugin(): void;
    /**
     * 根据插件名，设置插件展示的UI组件
     *
     *```
     * 注意：通过该接口设置指定插件的UI组件，则会优先展示。此接口可以设置内置插件的UI组件。
     *
     * 插件UI展示的优先级排序说明：
     * 1. 通过 `RongIM.getInstance().conversationService().setBoardPluginView` 设置的插件UI组件。
     * 2. 使用SDK的UI组件，插件标题是 `IBoardPlugin` 的 `obtainTitle` ，插件图标是 `IBoardPlugin` 的 `obtainImage` 。
     *```
     *
     * @param pluginName 插件名。如果插件名为空，或者插件面板没有此插件则设置无效。此接口可以设置内置插件的UI组件。
     * @param builder 插件UI组件。如果为空，则代表清除该插件的UI组件
     * @since 1.5.1
     */
    setBoardPluginView(pluginName: string, builder: WrappedBuilder<[
        Context,
        ConversationIdentifier
    ]> | null): void;
    /**
     * 在表情面板添加 IEmoticonTab 组件，添加后会立即刷新表情面板。
     *
     *
     * #### 重要
     * IEmoticonTab 编写示例，接口说明参照 IEmoticonTab
     * ```
     * &nbsp;
     * 1. IEmoticonTab 示例
     *
     * class CustomStickerTab extends IEmoticonTab {
     *   obtainTabName(): string {
     *     return "RCCustomStickerTab"
     *   }
     *
     *   obtainTabDrawable(context: Context): ResourceStr {
     *     return $r("app.media.rc_custom_emoji_icon")
     *   }
     *
     *   obtainTabPager(context: Context): WrappedBuilder<[Context, ConversationIdentifier, IEmoticonTab]> {
     *     return wrapBuilder(buildCustomEmoticonSwiperPageView)
     *   }
     *
     *   onTableSelected(context: Context, index: number): void {
     *     console.log("index：" + index)
     *   }
     * }
     *
     * 2. obtainTabPager返回的组件示例
     *
     * &nbsp;@Builder
     * export function buildCustomEmoticonSwiperPageView(context: Context, convId: ConversationIdentifier, tab: IEmoticonTab) {
     *   CustomEmoticonSwiperPage({ convId: convId, tab: tab })
     * }
     *
     * class CustomDataSource implements IDataSource {
     *   private list: string[][] = []
     *
     *   constructor(list: string[][]) {
     *     this.list = list
     *   }
     *
     *   totalCount(): number {
     *     return this.list.length
     *   }
     *
     *   getData(index: number): string[] {
     *     return this.list[index]
     *   }
     *
     *   registerDataChangeListener(listener: DataChangeListener): void {
     *   }
     *
     *   unregisterDataChangeListener() {
     *   }
     * }
     *
     * 3. Swiper 组件示例，downloadPaths 是业务侧已下载好的路径地址集合，塞到 CustomDataSource 中进行渲染
     *
     * &nbsp;@Component
     * export struct CustomEmoticonSwiperPage {
     *  &nbsp;@Require @Prop tab: IEmoticonTab;
     *  &nbsp;@Require @Prop convId: ConversationIdentifier;
     *   private customDataSource: CustomDataSource = new CustomDataSource([])
     *
     *   aboutToAppear(): void {
     *     let downloadPaths: string[] = []
     *     let dataList: string[][] = []
     *     for (let i = 0; i <= 8; i += 8) {
     *       const emojisSubset = rawPaths.slice(i, i + 8);
     *       dataList.push(emojisSubset)
     *     }
     *     this.customDataSource = new CustomDataSource(dataList)
     *   }
     *
     *   aboutToDisappear(): void {
     *   }
     *
     *   build() {
     *     this.emojiView()
     *   }
     *
     *  &nbsp;@Builder
     *   emojiView() {
     *     Swiper() {
     *       LazyForEach(this.customDataSource, (item: string[]) => {
     *         this.emojiPageView(item)
     *       }, (item: string[]) => item.toString())
     *     }
     *     .loop(false)
     *     .indicator(
     *       new DotIndicator().itemWidth(8)
     *         .itemHeight(8)
     *         .selectedItemWidth(9)
     *         .selectedItemHeight(9)
     *         .color(Color.Gray)
     *         .selectedColor(Color.Black)
     *     )
     *     .onChange((index: number) => {
     *       console.info(index.toString())
     *     })
     *     .width('100%')
     *     .layoutWeight(1)
     *     // Swiper必须设置Mode为SELF_FIRST。否则Swiper滑动表现异常。
     *     .nestedScroll(SwiperNestedScrollMode.SELF_FIRST)
     *   }
     *
     *  &nbsp;@Builder
     *   emojiPageView(pageData: string[]) {
     *     Column() {
     *       Grid() {
     *         ForEach(pageData, (item: string, index: number) => {
     *           GridItem() {
     *             Column() {
     *               Image(item).width(40).height(40)
     *               Text("表情名" + index).fontSize(12)
     *             }
     *           }.onClick(() => {
     *             // 可以调用 `this.tab.onEmoticonItemClick()` 回传输入框要输入的内容，也可以做发送消息的操作。
     *           })
     *         })
     *       }.columnsTemplate('1fr 1fr 1fr 1fr').rowsTemplate('1fr 1fr').width('100%').height('100%')
     *     }.width('100%').height('100%').padding({ bottom: 20 })
     *   }
     * ```
     * @warning 注意：SDK 仅在内存中维护已添加的 IEmoticonTab 组件，App重启后需要重新添加，建议连接后添加即可。
     * @param tab 添加的 `IEmoticonTab` 组件，注意：`obtainTabName` 返回空则添加失败、重复添加会添加失败。
     * @since 1.5.1
     */
    addEmoticonTab(tab: IEmoticonTab): void;
    /**
     * 根据`IEmoticonTab` 组件名移除 Tab 组件，移除后会立即刷新表情面板。
     *
     * @param tabName 移除的 `IEmoticonTab` 组件的名字，返回空则移除失败。
     * @since 1.5.1
     */
    removeEmoticonTabByName(tabName: string): void;
    /**
     * 清除所有添加的 `IEmoticonTab` 组件，清除后会立即刷新表情面板。
     *
     * @param tabName 移除的 `IEmoticonTab` 组件的名字，返回空则移除失败。
     * @since 1.5.1
     */
    clearAllEmoticonTab(): void;
    /**
     * 返回所有添加的 `IEmoticonTab` 组件。
     *
     * @returns 所有添加的 `IEmoticonTab` 组件
     * @since 1.5.1
     */
    getEmoticonTabList(): List<IEmoticonTab>;
    /**
     * 添加自定义的 {@link IExtensionModule},添加后，可以通过 {@link #getExtensionModules()} 获取已注册的 module
     *
     * <pre>
     * 注意：
     * 1. 此方法只是把自定义IExtensionModule加入到IExtensionModule列表,不会调用{@link IExtensionModule#onInit(Context, String)}}
     * 2. 注册请使用{@link #registerExtensionModule(IExtensionModule)}
     * </pre>
     *
     * @param extensionModule 自定义模块。
     */
    addExtensionModule(extensionModule: IExtensionModule): void;
    /**
     * 注册自定义的 {@link IExtensionModule},注册后，可以通过 {@link #getExtensionModules()} 获取已注册的 module
     *
     * <pre>
     * 注意：
     * 1. 请在 SDK 初始化后，调用此方法注册自定义 {@link IExtensionModule}
     * 2. 一定要在进入会话界面之前调此方法
     * </pre>
     *
     * @param extensionModule 自定义模块。
     */
    registerExtensionModule(extensionModule: IExtensionModule): void;
    /**
     * 注销 {@link IExtensionModule} 模块
     *
     * <pre>
     * 注意：
     * 1. 请在 SDK 初始化后 调用此方法反注册注册 {@link IExtensionModule}
     * 2. 一定要在进入会话界面之前调次方法
     * </pre>
     *
     * @param extensionModule 已注册的 IExtensionModule 模块
     */
    unregisterExtensionModule(extensionModule: IExtensionModule): void;
    /**
     * 获取已注册的模块。
     *
     * @return 已注册的模块列表
     */
    getExtensionModules(): List<IExtensionModule>;
    /**
     * 设置 Kit 输入栏插件配置
     *
     *```
     * 如果想调整 sdk 默认展示的插件以及插件的顺序，可以调用该方法设置插件配置，
     * 则 sdk 会使用该 `IExtensionConfig` 的 `getPluginModules` 返回的插件列表。
     *```
     */
    setExtensionConfig(config: IExtensionConfig): void;
    /**
     * 获取 Kit 输入栏插件配置
     *
     */
    getExtensionConfig(): IExtensionConfig;
    /**
     * 设置自定义的群聊已读人数组件(私有云专属)
     * @param builder 组件
     */
    setMessageGroupReceiptCountView(builder: WrappedBuilder<[
        Context,
        UiMessage
    ]>): void;
    /**
     * 设置自定义的群聊已读请求组件(私有云专属)
     * @param builder 组件
     */
    setMessageGroupReceiptStatusView(builder: WrappedBuilder<[
        Context,
        UiMessage
    ]>): void;
    /**
     * 设置自定义的单聊已读状态组件(私有云专属)
     * @param builder 组件
     */
    setMessagePrivateReceiptStatusView(builder: WrappedBuilder<[
        Context,
        UiMessage
    ]>): void;
    /**
     * 设置自定义的单聊发送状态组件(私有云专属)
     * @param builder 组件
     */
    setMessagePrivateSentStatusView(builder: WrappedBuilder<[
        Context,
        UiMessage
    ]>): void;
    /**
     * 设置输入框组件的自定义配置
     *
     * # 重要
     *```
     * InputAreaComponentConfig 参数说明
     * 1. 组件标识 `identifier: ComponentIdentifier,`
     *    支持的类型参照 `ComponentIdentifier`。
     * 2. 组件UI `component: WrappedBuilder<[InputAreaComponentData]> | null`，
     *    InputAreaComponentData代表透传给自定义组件的数据，使用说明见其注释。
     *```
     *
     * # 示例
     * ```
     * // 设置接口
     * let InputBarPluginButton: InputAreaComponentConfig = {
     *   identifier: ComponentIdentifier.InputBarPluginButton,
     *   component: wrapBuilder(buildCustomInputBarPluginView),
     * }
     * RongIM.getInstance().conversationService().setInputAreaComponentConfig(InputBarPluginButton)
     *
     * // 组件
     * &nbsp;@Builder
     * export function buildCustomInputBarPluginView(componentData:InputAreaComponentData) {
     *   CustomInputBarPluginView({ context: componentData.context, convId: componentData.convId, type: componentData.data })
     * }
     *
     * &nbsp;@Component
     * export struct CustomInputBarPluginView {
     * @Prop context: Context;
     * @Prop convId: ConversationIdentifier;
     * @Prop type: string;
     *
     *   aboutToAppear(): void {
     *     console.log("CustomInputBarPluginView  aboutToAppear " + this.type)
     *   }
     *
     *   build() {
     *     Image($r('app.media.default_ic'))
     *       .size({ width: 30, height: 30 })
     *       .onTouch(() => {
     *         console.log("CustomInputBarPluginView  点击了Plugin按钮")
     *       })
     *   }
     * }
     *```
     *
     * @param componentConfig 组件配置，参照 `InputAreaComponentConfig`
     * @since 1.5.1
     */
    setInputAreaComponentConfig(componentConfig: InputAreaComponentConfig): void;
    /**
     * 设置会话页面内容组件的自定义配置
     *
     * # 重要
     *```
     * 一. ConversationContentComponentConfig 参数说明
     *  1. 组件标识 `identifier: ComponentIdentifier`
     *     支持的类型参照 `ComponentIdentifier`。
     *     对应 IConversationViewModel 的点击事件：
     *       - onClickUnreadMessageButton
     *       - onClickUnreadMentionedMessageButton
     *       - onClickNewReceivedUnreadMessageButton
     *  2. 组件UI `component: WrappedBuilder<[ConversationContentComponentData]> | null`，
     *     ConversationContentComponentData代表透传给自定义组件的数据，使用说明见其注释。
     * 二. `component` 组件的父布局是 RelativeContainer，可以使用 alignRules 控制自定义组件排放的位置。
     *```
     *
     * # 示例
     * ```
     * // 设置接口
     * let ConversationNewReceivedUnreadMessageButton: ConversationContentComponentConfig = {
     *  identifier: ComponentIdentifier.ConversationNewReceivedUnreadMessageButton,
     *  component: wrapBuilder(buildCustomConversationBottomUnreadMessageButton),
     * }
     * RongIM.getInstance().conversationService().setConversationContentComponentConfig(ConversationNewReceivedUnreadMessageButton)
     *
     * // 组件
     * &nbsp;@Builder
     * export function buildCustomConversationBottomUnreadMessageButton(data: ConversationContentComponentData) {
     *   CustomConversationBottomUnreadMessageButton({ context: data.context, convId: data.convId, unreadNewMsgLength: data.data, viewModel:data.conversationViewModel })
     * }
     * &nbsp;@Component
     * export struct CustomConversationBottomUnreadMessageButton {
     *  &nbsp;@Prop context: Context;
     *  &nbsp;@Prop convId: ConversationIdentifier;
     *  &nbsp;@Prop unreadNewMsgLength: number;
     *  &nbsp;@Prop viewModel: IConversationViewModel;
     *
     *   build() {
     *     if (this.unreadNewMsgLength > 0) {
     *       Stack({ alignContent: Alignment.Center }) {
     *         Text(this.unreadNewMsgLength > 99 ? '99+' : `${this.unreadNewMsgLength}`)
     *           .fontSize(12)
     *           .fontColor($r('app.color.rc_color_FFFFFF'))
     *       }.alignRules({
     *         top: { anchor: "__container__", align: VerticalAlign.Top },
     *         left: { anchor: "__container__", align: HorizontalAlign.Start }
     *       }).onClick(() => {
     *         this.viewModel.onClickNewReceivedUnreadMessageButton()
     *       })
     *     }
     *   }
     * }
     *```
     *
     * @param componentConfig 组件配置，参照 `ConversationContentComponentConfig`
     * @since 1.6.0
     */
    setConversationContentComponentConfig(componentConfig: ConversationContentComponentConfig): void;
    /**
     * 设置水印组件的自定义配置
     *
     * # 重要
     *```
     * 一. WatermarkComponentConfig 参数说明
     *  1. 组件标识 `identifier: ComponentIdentifier`
     *     支持的类型参照 `ComponentIdentifier`。
     *  2. 组件UI `component: WrappedBuilder<[WatermarkComponentData]> | null`，
     *     WatermarkComponentData代表透传给自定义组件的数据，使用说明见其注释。
     *  3.水印生效的页面 `page: WatermarkPageIdentifier[]`
     *     支持的类型参照 `WatermarkPageIdentifier`，如果为空则设置失败。
     *```
     *
     * # 示例
     * ```
     * // 设置接口
     * let WatermarkComponent: WatermarkComponentConfig = {
     *   page: [WatermarkPageIdentifier.CombinePage],
     *   identifier: ComponentIdentifier.WatermarkComponent,
     *   component:wrapBuilder(buildCustomWatermarkComponent)
     * }
     * RongIM.getInstance().conversationService().setWatermarkComponentConfig(WatermarkComponent)
     *
     * // 组件
     * &nbsp;@Builder
     * export function buildCustomWatermarkComponent(data: WatermarkComponentData) {
     *   CustomWatermarkComponent({ context: data.context, convId: data.convId})
     * }
     *
     * &nbsp;@Component
     * export struct CustomWatermarkComponent {
     *  &nbsp;@Prop context: Context;
     *  &nbsp;@Prop convId: ConversationIdentifier | undefined;
     *  &nbsp;@State textArray: Array<number> = new Array(50).fill(0).map((_: number, index) => index + 1);
     *
     *   build() {
     *     Flex({ wrap: FlexWrap.Wrap }) {
     *       ForEach(this.textArray, (item: number, index: number) => {
     *         Text("默认水印文本")
     *           .fontSize(14)
     *           .fontColor("#999999")
     *           .opacity(0.15)
     *           .rotate({ angle: -15 })
     *           .margin({
     *             top: 40,
     *             bottom: 40,
     *             left: 20 + (index % 4 === 0 ? 0 : 40),
     *             right: 20
     *           })
     *       }, (item: number) => item.toString())
     *     }.size({ width: '100%', height: '100%' }).backgroundColor(Color.Transparent)
     *   }
     * }
     *```
     *
     * @param componentConfig 组件配置，参照 `WatermarkComponentConfig`.如果 `page` 为空则设置失败。
     * @since 1.7.0
     */
    setWatermarkComponentConfig(componentConfig: WatermarkComponentConfig): void;
    /**
     * 获取单个会话
     * @param conId 会话标识
     * @returns 会话数据
     * @see ConversationIdentifier
     * @version 1.0.0
     */
    getConversation(conId: ConversationIdentifier): Promise<IAsyncResult<Conversation>>;
    /**
     * 批量删除会话
     */
    removeConversations(conversationIds: List<ConversationIdentifier>): Promise<IAsyncResult<void>>;
    /**
     * 设置会话置顶
     * @param conversationIds 会话 id 标识列表
     * @param option 配置
     * @returns
     */
    setConversationsToTop(conversationIds: List<ConversationIdentifier>, option: ISetConversationTopOption): Promise<IAsyncResult<void>>;
    /**
     * 获取会话置顶状态
     * @param conId 会话标识
     * @returns 是否置顶
     */
    getConversationTopStatus(conId: ConversationIdentifier): Promise<IAsyncResult<boolean>>;
    /**
     * 设置会话免打扰级别
     * @param conversationIds 会话 id 标识列表
     * @param level 免打扰级别
     * @returns
     */
    setConversationsNotificationLevel(conversationIds: List<ConversationIdentifier>, level: PushNotificationLevel): Promise<IAsyncResult<void>>;
    /**
     * 获取单个会话免打扰状态
     * @param conId 会话标识
     * @returns 会话免打扰级别
     */
    getConversationNotificationLevel(conId: ConversationIdentifier): Promise<IAsyncResult<PushNotificationLevel>>;
    setNotificationQuietHoursLevel(option: IQuietHoursOption): Promise<IAsyncResult<void>>;
    /**
     * 查询已设置的时间段消息提醒屏蔽
     * # 示例代码
     *```
     *  IMEngine.getInstance().getNotificationQuietHoursLevel().then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取免打扰失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 免打扰数据为空
     *      return;
     *    }
     *    let info = result.data as IQuietHoursOption;
     *  })
     *```
     * @returns 具体的配置
     * @version 1.0.0
     */
    getNotificationQuietHoursLevel(): Promise<IAsyncResult<IQuietHoursOption>>;
    /**
     * 删除已设置的全局时间段消息提醒屏蔽
     * # 示例代码
     *```
     *  IMEngine.getInstance().removeNotificationQuietHours().then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 移除免打扰失败
     *      return;
     *    }
     *    // 移除免打扰成功
     *  });
     *```
     * @returns 结果
     * @version 1.0.0
     */
    removeNotificationQuietHours(): Promise<IAsyncResult<void>>;
    /**
     * 获取本地会话中 @ 自己的未读消息列表
     * @param conId 会话标识
     * @param option 配置
     * @returns 返回本地消息结果
     * @see ConversationIdentifier
     * @see ICountOption
     * @version 1.0.0
     */
    getUnreadMentionedMessages(conId: ConversationIdentifier, option: ICountOption): Promise<IAsyncResult<List<Message>>>;
    /**
     * 获取会话里第一条未读消息
     * @param conId 会话标识
     * @returns 消息，如果该会话没有未读，返回 null
     * @see ConversationIdentifier
     * @version 1.0.0
     */
    getFirstUnreadMessage(conId: ConversationIdentifier): Promise<IAsyncResult<Message>>;
    /**
     * 会话未读数，是否包含免打扰会话的未读数
     * @param typeList 会话类型数组
     * @param isContainBlocked 是否包含免打扰；true 代表获取所有会话未读数之和； false 代表获取不包含免打扰会话的正常会话未读数之和
     * @returns 未读数
     * @discussion 正常单聊会话 A 的未读数为1，免打扰单聊会话 B 的未读数为 2。true 代表获取两个单聊会话的未读数之和，其结果为 3。false 代表获取正常会话 A 的未读数，结果为 1
     * @version 1.0.0
     */
    getUnreadCountByTypes(typeList: List<ConversationType>, isContainBlocked: boolean): Promise<IAsyncResult<number>>;
    /**
     * 获取单个会话的未读数
     * @param conId 会话标识
     * @returns 该会话的未读数
     * @version 1.0.0
     */
    getUnreadCount(conId: ConversationIdentifier): Promise<IAsyncResult<number>>;
    /**
     * 清理会话未读数
     * @param conversationId
     * @returns
     */
    clearMessagesUnreadStatus(conversationId: ConversationIdentifier): Promise<IAsyncResult<void>>;
    /**
     * 清理会话未读数
     * @param conversationId
     * @returns
     */
    clearMessagesUnreadStatusByTime(conId: ConversationIdentifier, time: number): Promise<IAsyncResult<void>>;
    /**
     * 保存文本消息草稿
     * @param conversationId 会话标识
     * @param draft 草稿，空字符串代表清空草稿
     * @returns
     */
    saveTextMessageDraft(conversationId: ConversationIdentifier, draft: string): Promise<IAsyncResult<void>>;
    /**
     * 获取草稿
     */
    getTextMessageDraft(conversationId: ConversationIdentifier): Promise<IAsyncResult<string>>;
    /**
     * 获取 SDK 中所有的消息 objectName 和存储标识的映射关系
     * @returns 映射关系集合。 key ：objectName 、 value ： MessageFlag
     */
    getMessageTypeMap(): HashMap<string, MessageFlag>;
    /**
     * 下载媒体消息，含下载进度
     * @param messageId 消息 Id
     * @param progressListener 下载进度监听，取值范围 [0 , 100]
     * @returns 媒体消息下载成功的本地路径，存储路径见 InitOption.mediaSavePath
     * @note 调用该接口下载成功后，消息的本地路径会保存数据库中；相同的消息重复下载，会直接返回本地路径
     * @note SDK 下载媒体过程中，调用了 cancelDownloadMediaMessage() ，该方法会返回 EngineError.RequestCanceled
     * @version 1.0.0
     */
    downloadMediaMessageWithProgress(messageId: number, progressListener: (messageId: number, progress: number) => void): Promise<IAsyncResult<string>>;
    cancelDownloadMediaMessage(messageId: number): Promise<IAsyncResult<void>>;
    /**
     * 用于设置 isListened 来标记高清语音消息是否已被听过
     * @param messageId 消息 Id
     * @param receiveStatus 接收状态
     * @returns 结果
     * @version 1.3.0
     */
    setMessageReceivedStatus(messageId: number, receiveStatus: ReceivedStatus): Promise<IAsyncResult<void>>;
    /**
     * 同步会话已读状态
     */
    syncConversationReadStatus(conId: ConversationIdentifier, timestamp: number): Promise<IAsyncResult<void>>;
    /**
     * 增加会话已读状态监听
     * @param listener 监听
     * @warning addSyncConversationReadStatusListener & removeSyncConversationReadStatusListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     */
    addSyncConversationReadStatusListener(listener: SyncConversationReadStatusListener): void;
    /**
     * 移除会话已读状态监听
     * @param listener 监听
     * @warning addSyncConversationReadStatusListener & removeSyncConversationReadStatusListener 配合使用，避免内存泄露
     */
    removeSyncConversationReadStatusListener(listener: SyncConversationReadStatusListener): void;
    /**
     * 单聊，发送某个会话中的消息阅读回执，由原始消息的接收方调用
     *```
     * 单聊调用该方法后，原始消息发送方会触发 MessageReadReceiptListener.onMessageReadReceiptReceived
     * 原始消息发送方本地对应单聊会话的消息全变为已读
     *```
     * @param conId 会话标识
     * @param timestamp 会话中已读的最后一条消息的发送时间戳， Message.sentTime
     */
    sendReadReceiptMessage(conId: ConversationIdentifier, timestamp: number): Promise<IAsyncResult<void>>;
    /**
     * 群聊，发送某个会话的消息已读请求，由原始消息的发送方调用
     *```
     * 群聊调用该方法后，原始消息的接收方会触发 MessageReadReceiptListener.onMessageReceiptRequest
     * 原始消息接收方就知道需要对该消息做已读响应。调用 sendReadReceiptResponse
     *```
     * @param message 消息体，messageUid 必须有效
     * @returns 消息已读请求的结果
     */
    sendReadReceiptRequest(message: Message): Promise<IAsyncResult<void>>;
    /**
     * 群聊，发送某个会话已读响应，由原始消息的接收方调用
     *```
     * 群聊调用该方法后，原始消息的发送方会触发 MessageReadReceiptListener.onMessageReceiptResponse
     * 原始消息的发送方就知道自己该消息有哪些人已读了
     *```
     * @param conId 会话标识
     * @param messageArray 消息体数组，消息体必须存在有效的 senderId 和 messageUid
     * @returns 消息已读响应结果，返回的 Message 集合可以通过 Message.readReceiptInfo.hasRespond 确认该消息是否已经发送了响应
     */
    sendReadReceiptResponse(conId: ConversationIdentifier, messageArray: Array<Message>): Promise<IAsyncResult<List<Message>>>;
}
