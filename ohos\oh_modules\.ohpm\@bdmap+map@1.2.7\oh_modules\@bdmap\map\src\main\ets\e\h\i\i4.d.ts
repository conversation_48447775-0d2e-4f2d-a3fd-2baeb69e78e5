import emitter from '@ohos.events.emitter'; import { Callback } from '@ohos.base'; type r27 = Array<Callback<emitter.EventData>>; export default class EventEmitter { static _maps: Map<string, Map<number, r27>>; static emit(v27: string, event: emitter.InnerEvent, data?: emitter.EventData): void; static on(t27: string, event: emitter.InnerEvent, callback: Callback<emitter.EventData>): void; static destroy(s27: string): void; } export {}; 