// @keepTs
// @ts-nocheck
/**
 * Created on 2025/04/25
 * <AUTHOR>
 */
import { PushNotificationLevel } from '@rongcloud/imlib';
/**
 * 推送通知级别工具类，提供与推送通知级别相关的工具方法。
 */
export declare class PushNotificationLevelUtils {
    /**
     * 判断给定的推送通知级别是否表示免打扰状态。
     *
     * 在融云即时通讯库中，不同的推送通知级别代表不同的消息接收策略。
     * 当推送通知级别为以下几种时，被认为处于免打扰状态：
     * - `Blocked`：所有消息都被阻止，不会收到任何推送通知。
     * - `Mention`：仅在被 @ 时收到推送通知，其他消息不会推送。
     * - `MentionUsers`：仅在被指定用户 @ 时收到推送通知。
     * - `MentionAll`：仅在被 @全体成员 时收到推送通知。
     *
     * @param level - 要检查的推送通知级别
     * @returns 如果推送通知级别表示免打扰状态，则返回 `true`；否则返回 `false`。
     */
    static isNoDisturb(n348: PushNotificationLevel): boolean;
}
