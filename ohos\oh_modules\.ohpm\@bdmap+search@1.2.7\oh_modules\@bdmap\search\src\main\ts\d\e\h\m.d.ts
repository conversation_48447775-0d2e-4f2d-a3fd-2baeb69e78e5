import { LatLng } from '@bdmap/base';
import type { PlanNode, RouteLine, RouteNode, RouteStep, SearchResult, SuggestAddrInfo, TaxiInfo } from "./f1";
/**
 * 步行路线规划结果
 */
export interface WalkingRouteResult extends SearchResult {
    /**
     * 所有步行规划路线
     */
    routeLines?: WalkingRouteLine[];
    /**
     * 打车信息
     */
    taxiInfo?: TaxiInfo;
    /**
     * 建议信息
     */
    suggestAddrInfo?: SuggestAddrInfo;
}
/**
 * 表示一条步行路线
 */
export interface WalkingRouteLine extends RouteLine<WalkingStep> {
}
/**
 * 描述一个步行路段
 */
export interface WalkingStep extends RouteStep {
    /**
     * 该路段起点方向值 单位：度。 正北方向为0度，顺时针
     */
    direction: number;
    /**
     * 路段起点信息
     */
    entrance: RouteNode;
    /**
     * 路段所经过的地理坐标集合
     */
    wayPoints: LatLng[];
    /**
     * 路段终点信息
     */
    exit: RouteNode;
    /**
     * pathString 需要时可以解析
     */
    pathString: string;
    /**
     * 路段入口提示信息
     */
    entranceInstructions: string;
    /**
     * 路段出口指示信息
     */
    exitInstructions: string;
    /**
     * 路段整体指示信息
     */
    instructions: string;
}
/**
 *  步行路线规划参数
 */
export interface WalkingRoutePlanOption {
    /**
     * 起点
     */
    from: PlanNode;
    /**
     * 终点
     */
    to: PlanNode;
}
