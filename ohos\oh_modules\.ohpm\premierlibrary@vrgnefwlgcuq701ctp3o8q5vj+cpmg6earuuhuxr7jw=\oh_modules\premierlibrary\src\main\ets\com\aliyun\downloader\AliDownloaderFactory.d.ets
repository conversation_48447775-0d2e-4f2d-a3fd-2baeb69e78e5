import { Context } from '@kit.AbilityKit';
import { AliMediaDownloader } from './AliMediaDownloader';
export declare class AliDownloaderFactory {
    /**
     * 创建下载
     *
     * @param context 上下文
     * @return 下载对象. 见{@linkplain AliMediaDownloader}
     */
    /****
     * Create a download task.
     *
     * @param context Context.
     * @return The object that you want to download. See {@linkplain AliMediaDownloader}.
     */
    static create(f: Context): AliMediaDownloader;
    /**
     * 直接删除文件。注意：需要保证其他下载不会操作对应的文件。
     *
     * @param saveDir 保存的文件夹位置
     * @param vid     视频id
     * @param format  视频格式
     * @param index   下载的视频索引
     * @return 成功返回0。
     */
    /****
     * Delete a specified video file. Note: Make sure that the video file is not used by other download tasks.
     *
     * @param saveDir The directory where the video file is saved.
     * @param vid     The ID of the video.
     * @param format  The format of the video.
     * @param index   The index of the video.
     * @return If the video file is deleted, value 0 is returned.
     */
    static deleteFile(b: string, c: string, d: string, e: number): number;
}
