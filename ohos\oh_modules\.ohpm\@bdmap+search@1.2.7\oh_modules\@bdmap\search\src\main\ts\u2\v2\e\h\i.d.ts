import { LatLng } from '@bdmap/base';
import { SdkInnerError } from "../../../base/f1";
/**
 * 从服务端获取的json解析,该接口不对用户开放
 */
export interface JSONResult {
    status?: number;
    type?: number;
    result?: Result;
    transit_type?: number;
    message?: string;
    SDK_InnerError?: SdkInnerError;
}
export interface Result {
    origin?: Destination;
    destination?: Destination;
    total?: number;
    routes?: Route[];
    taxi?: string;
}
export interface Destination {
    city_id?: number;
    city_name?: string;
    location?: LatLng;
    wd?: string;
    city_code?: number;
}
export interface Route {
    distance?: number;
    duration?: number;
    arrive_time?: string;
    price?: number;
    price_detail?: any[];
    steps?: Array<Step[]>;
}
export interface Step {
    distance?: number;
    duration?: number;
    instructions?: string;
    path?: string;
    traffic_condition?: any[];
    start_location?: LatLng;
    end_location?: LatLng;
    vehicle_info?: VehicleInfo;
}
export interface VehicleInfo {
    type?: number;
    detail?: Detail | null;
}
export interface Detail {
    name?: string;
    line_id?: string;
    type?: number;
    line_color?: string;
    stop_num?: number;
    on_station?: string;
    off_station?: string;
    first_time?: string;
    last_time?: string;
    bus_type?: number;
    shuttle_time_table?: any[];
    stop_info?: StopInfo[];
    start_info?: string;
    end_info?: EndInfo;
    price?: number;
    departure_station?: string;
    arrive_station?: string;
    departure_time?: string;
    arrive_time?: string;
    booking?: string;
    discount?: number;
    airlines?: string;
    provider_name?: string;
    provider_url?: string;
}
export interface EndInfo {
    end_time?: string;
    end_uid?: string;
    end_name?: string;
    end_location?: LatLng;
    end_city?: string;
}
export interface StopInfo {
    stop_name?: string;
    stop_location?: LatLng;
}
