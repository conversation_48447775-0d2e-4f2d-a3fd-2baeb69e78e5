// @keepTs
// @ts-nocheck
import media from '@ohos.multimedia.media';
/**
 * 高清语音消息的录制
 */
declare class AudioRecorder {
    private static instance;
    private avRecorder;
    private micPhonePermission;
    private audioFile;
    private audioPath;
    private onAVRecorderStateCallBack?;
    setOnAVRecorderStateCallBack(o339?: (state: media.AVRecorderState) => void): void;
    static getInstance(): AudioRecorder;
    private setAudioRecorderCallback;
    startRecordingProcess(): Promise<void>;
    pauseRecordingProcess(): Promise<void>;
    resumeRecordingProcess(): Promise<void>;
    stopRecordingProcess(): Promise<string>;
    private getAvConfig;
    private getAudioPath;
    private getAudioFileFd;
    /**
     * 获取avRecorder
     * @returns
     */
    getAvRecorder(): media.AVRecorder | null;
}
export { AudioRecorder };
