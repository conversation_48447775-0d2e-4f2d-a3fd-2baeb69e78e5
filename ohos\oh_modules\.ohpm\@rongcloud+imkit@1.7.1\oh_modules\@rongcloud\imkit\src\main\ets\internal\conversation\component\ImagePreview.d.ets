// @keepTs
// @ts-nocheck
import { Message } from '@rongcloud/imlib';
@Component
export declare struct ImagePreview {
    @State
    allMessage: Message[];
    @Prop
    optionIndex: number;
    @Prop
    isShowSaveButton: boolean;
    @Prop
    isDestructMessage: boolean;
    @Provide
    windowWidth: number;
    @Provide
    windowHeight: number;
    private DISPLAY_COUNT;
    private MIN_SCALE;
    private swiperController;
    private phoneDialogController?;
    onChange: (index: number) => void;
    onBackClick?: (isPlayEnd: boolean) => void;
    @State
    private opacityList;
    @State
    private scaleList;
    @State
    private translateList;
    @State
    private zIndexList;
    private DestructRecallMessageEventCallback;
    aboutToAppear(): Promise<void>;
    aboutToDisappear(): void;
    loadingDialog(): void;
    build(): void;
    private getFileDownloadInfo;
    private openDialog;
    private getFileDownloadPath;
    private saveImage;
    /**
     * 根据已下载路径保存媒体文件
     */
    private saveMedia;
    private saveGifImage;
    private saveOriginalImage;
    private saveSightWithPath;
    private isImageMessage;
    private getImageRemoteUrl;
    private getImageBase64;
    private isGifByMimeType;
    private imageTypeForFilePath;
    private imageFormatForType;
    private isSightMessage;
    private getSightMessage;
}
