// @keepTs
// @ts-nocheck
/**
 * Created on 2025/06/06
 * <AUTHOR>
 */
import { Context } from "@kit.AbilityKit";
import { ConversationIdentifier } from "@rongcloud/imlib";
/**
 * 表情扩展的基类
 * @version 1.5.1
 */
declare abstract class IEmoticonTab {
    /**
     * 构造的 tab 的标识，如果返回空则添加此Tab会失败。
     *
     */
    abstract obtainTabName(): string;
    /**
     * 构造 tab 的小图标，用于显示在表情面板底部 tab bar中。
     *
     * @param context 应用上下文。
     * @return 图标的 drawable，不能为 null。
     */
    abstract obtainTabDrawable(z25: Context): ResourceStr;
    /**
     * 构造 table 页面。
     *
     * ```
     * 1. obtainTabPager返回准备添加的组件
     *   obtainTabPager(context: Context): WrappedBuilder<[Context, ConversationIdentifier, IEmoticonTab]> {
     *     return wrapBuilder(buildCustomEmoticonSwiperPageView)
     *   }
     *
     *  2. obtainTabPager返回的组件示例
     *
     *  &nbsp;@Builder
     *  export function buildCustomEmoticonSwiperPageView(context: Context, convId: ConversationIdentifier, tab: IEmoticonTab) {
     *    CustomEmoticonSwiperPage({ convId: convId, tab: tab })
     *  }
     *
     *  class CustomDataSource implements IDataSource {
     *    private list: string[][] = []
     *
     *    constructor(list: string[][]) {
     *      this.list = list
     *    }
     *
     *    totalCount(): number {
     *      return this.list.length
     *    }
     *
     *    getData(index: number): string[] {
     *      return this.list[index]
     *    }
     *
     *    registerDataChangeListener(listener: DataChangeListener): void {
     *    }
     *
     *    unregisterDataChangeListener() {
     *    }
     *  }
     *
     *  3. Swiper 组件示例，downloadPaths 是业务侧已下载好的路径地址集合，塞到 CustomDataSource 中进行渲染
     *
     *  &nbsp;@Component
     *  export struct CustomEmoticonSwiperPage {
     *   &nbsp;@Require @Prop tab: IEmoticonTab;
     *   &nbsp;@Require @Prop convId: ConversationIdentifier;
     *    private customDataSource: CustomDataSource = new CustomDataSource([])
     *
     *    aboutToAppear(): void {
     *      let downloadPaths: string[] = []
     *      let dataList: string[][] = []
     *      for (let i = 0; i <= 8; i += 8) {
     *        const emojisSubset = rawPaths.slice(i, i + 8);
     *        dataList.push(emojisSubset)
     *      }
     *      this.customDataSource = new CustomDataSource(dataList)
     *    }
     *
     *    aboutToDisappear(): void {
     *    }
     *
     *    build() {
     *      this.emojiView()
     *    }
     *
     *   &nbsp;@Builder
     *    emojiView() {
     *      Swiper() {
     *        LazyForEach(this.customDataSource, (item: string[]) => {
     *          this.emojiPageView(item)
     *        }, (item: string[]) => item.toString())
     *      }
     *      .loop(false)
     *      .indicator(
     *        new DotIndicator().itemWidth(8)
     *          .itemHeight(8)
     *          .selectedItemWidth(9)
     *          .selectedItemHeight(9)
     *          .color(Color.Gray)
     *          .selectedColor(Color.Black)
     *      )
     *      .onChange((index: number) => {
     *        console.info(index.toString())
     *      })
     *      .width('100%')
     *      .layoutWeight(1)
     *       // Swiper必须设置Mode为SELF_FIRST。否则Swiper滑动表现异常。
     *      .nestedScroll(SwiperNestedScrollMode.SELF_FIRST)
     *    }
     *
     *   &nbsp;@Builder
     *    emojiPageView(pageData: string[]) {
     *      Column() {
     *        Grid() {
     *          ForEach(pageData, (item: string, index: number) => {
     *            GridItem() {
     *              Column() {
     *                Image(item).width(40).height(40)
     *                Text("表情名" + index).fontSize(12)
     *              }
     *            }.onClick(() => {
     *              // 可以调用 `this.tab.onEmoticonItemClick()` 回传输入框要输入的内容，也可以做发送消息的操作。
     *            })
     *          })
     *        }.columnsTemplate('1fr 1fr 1fr 1fr').rowsTemplate('1fr 1fr').width('100%').height('100%')
     *      }.width('100%').height('100%').padding({ bottom: 20 })
     *    }
     *  ```
     * @param context 应用上下文。
     * @return 构造后的 table view，用来展示到底部表情面板中，不能为 null。
     */
    abstract obtainTabPager(y25: Context): WrappedBuilder<[
        Context,
        ConversationIdentifier,
        IEmoticonTab
    ]>;
    /**
     * 表情面板左右滑动时，回调此方法。
     *
     * @param context 应用上下文。
     * @param position 当前 table 的位置。
     */
    abstract onTableSelected(w25: Context, x25: number): void;
    /**
     * Emoticon Item 的点击回调
     * @param data 点击表情或者贴图，需要传递给输入框的内容
     * @param isDelete 是否是删除按钮
     */
    onEmoticonItemClick(u25: string, v25?: boolean): void;
}
export { IEmoticonTab };
