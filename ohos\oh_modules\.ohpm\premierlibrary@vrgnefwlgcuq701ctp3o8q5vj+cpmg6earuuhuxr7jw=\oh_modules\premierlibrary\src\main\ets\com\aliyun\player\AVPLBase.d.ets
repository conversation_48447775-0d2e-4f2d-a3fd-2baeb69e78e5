import { IListPlayer, MultiBitratesMode, SceneType, StrategyType } from './IListPlayer';
import { AlphaRenderMode, IPlayer, IPResolveType, OnAudioInterruptEventListener, OnAVNotSyncStatusListener, OnCompletionListener, OnErrorListener, OnInfoListener, OnLoadingStatusListener, OnPreparedListener, OnRenderingStartListener, OnSeekCompleteListener, OnSeiDataListener, OnSnapShotListener, OnStateChangedListener, OnStreamSwitchedListener, OnSubtitleDisplayListener, OnSubTrackReadyListener, OnTrackChangedListener, OnTrackReadyListener, OnVideoRenderedListener, OnVideoSizeChangedListener, PropertyKey } from './IPlayer';
import { ListPlayerBase } from './nativeclass/ListPlayerBase';
import { Context } from '@ohos.abilityAccessCtrl';
import { PlayerConfig } from './nativeclass/PlayerConfig';
import { CacheConfig } from './nativeclass/CacheConfig';
import { MediaInfo } from './nativeclass/MediaInfo';
import { TrackType, TrackInfo } from './nativeclass/TrackInfo';
export declare abstract class AVPLBase implements IPlayer, IListPlayer {
    private mListPlayer?;
    private mNativePlayer?;
    private mPreRenderPlayer?;
    constructor(d20: Context, e20: string);
    protected abstract createListPlayer(d46: Context, e46: string, f46: number, g46: number): ListPlayerBase;
    protected abstract getNativePlayerWithContext(b46: Context, c46: string): IPlayer;
    protected abstract getPrerenderPlayerWithContext(z45: Context, a46: string): IPlayer;
    protected abstract getCurrentPlayerIndex(): number;
    protected getCurrentPlayer(): IPlayer;
    protected getCurrentPrerenderPlayer(b20: number): IPlayer | undefined;
    protected getCorePlayer(): ListPlayerBase;
    protected getNativePlayer(): IPlayer;
    protected getPrerenderPlayer(): IPlayer;
    removeSource(a20: string): void;
    clear(): void;
    getCurrentUid(): string;
    setMaxPreloadMemorySizeMB(z19: number): void;
    getMaxPreloadMemorySizeMB(): number;
    SetMultiBitratesMode(y19: MultiBitratesMode): void;
    GetMultiBitratesMode(): MultiBitratesMode;
    setPreloadCount(x19: number): void;
    setPreloadScene(w19: SceneType): void;
    enablePreloadStrategy(u19: StrategyType, v19: boolean): void;
    setPreloadStrategy(s19: StrategyType, t19: string): void;
    setPreloadCountWithPrevAndNext(q19: number, r19: number): void;
    getNativeContextAddr(): number;
    prepare(): void;
    start(): void;
    pause(): void;
    stop(): void;
    setAutoPlay(p19: boolean): void;
    setSurfaceId(o19: string): void;
    setSpeed(n19: number): void;
    setVolume(m19: number): void;
    getVolume(): number;
    seekTo(k19: number, l19: number): void;
    setStartTime(i19: number, j19: number): void;
    getDuration(): number;
    getPlayedDuration(): number;
    getCurrentPosition(): number;
    getBufferedPosition(): number;
    getPlayerStatus(): number;
    enableHardwareDecoder(h19: boolean): void;
    release(): void;
    releaseAsync(): void;
    setMute(g19: boolean): void;
    isMuted(): boolean;
    setScaleMode(f19: number): void;
    getScaleMode(): number;
    setLoop(e19: boolean): void;
    isLoop(): boolean;
    getVideoWidth(): number;
    getVideoHeight(): number;
    getVideoRotation(): number;
    reload(): void;
    setRotateMode(d19: number): void;
    getRotateMode(): number;
    setMirrorMode(c19: number): void;
    getMirrorMode(): number;
    setAlphaRenderMode(b19: AlphaRenderMode): void;
    getAlphaRenderMode(): number;
    setVideoBackgroundColor(a19: number): void;
    getSpeed(): number;
    isAutoPlay(): boolean;
    setConfig(z18: PlayerConfig): void;
    getConfig(): PlayerConfig | undefined;
    setOption(x18: string, y18: string): void;
    setOptionNum(v18: number, w18: number): void;
    getOption(u18: string): string | number | undefined;
    selectTrack(t18: number): void;
    switchStream(s18: string): void;
    getMediaInfo(): MediaInfo | null;
    getSubMediaInfo(): MediaInfo | null;
    currentTrack(r18: TrackType): TrackInfo | null;
    addExtSubtitle(q18: string): void;
    selectExtSubtitle(o18: number, p18: boolean): void;
    setStreamDelay(m18: number, n18: number): void;
    setMaxAccurateSeekDelta(l18: number): void;
    setCacheConfig(k18: CacheConfig): void;
    setIPResolveType(j18: IPResolveType): void;
    setFastStart(i18: boolean): void;
    snapShot(): void;
    clearScreen(): void;
    getCacheFilePathByUrl(h18: string): string;
    getCacheFilePathByVid(d18: string, e18: string, f18: string, g18: number): string;
    getPropertyString(c18: PropertyKey): string;
    setDefaultBandWidth(b18: number): void;
    sendCustomEvent(a18: string): void;
    setVideoTag(z17: number[]): void;
    setUserData(y17: string): void;
    setTraceId(x17: string): void;
    getUserData(): string;
    setOnPreparedListener(w17: OnPreparedListener): void;
    setOnInfoListener(v17: OnInfoListener): void;
    setOnRenderingStartListener(u17: OnRenderingStartListener): void;
    setOnStateChangedListener(t17: OnStateChangedListener): void;
    setOnCompletionListener(s17: OnCompletionListener): void;
    setOnLoadingStatusListener(r17: OnLoadingStatusListener): void;
    setOnAVNotSyncStatusListener(q17: OnAVNotSyncStatusListener): void;
    setOnErrorListener(p17: OnErrorListener): void;
    setOnVideoSizeChangedListener(o17: OnVideoSizeChangedListener): void;
    setOnSeekCompleteListener(n17: OnSeekCompleteListener): void;
    setOnSubtitleDisplayListener(m17: OnSubtitleDisplayListener): void;
    setOnVideoRenderedListener(l17: OnVideoRenderedListener): void;
    setOnAudioInterruptEventListener(k17: OnAudioInterruptEventListener): void;
    setOnSubTrackReadyListener(j17: OnSubTrackReadyListener): void;
    setOnTrackReadyListener(i17: OnTrackReadyListener): void;
    setOnTrackChangedListener(h17: OnTrackChangedListener): void;
    setOnSnapShotListener(g17: OnSnapShotListener): void;
    setOnSeiDataListener(f17: OnSeiDataListener): void;
    setOnStreamSwitchedListener(e17: OnStreamSwitchedListener): void;
}
