// @keepTs
// @ts-nocheck
/**
 * 录制普通语音消息
 * @warning 现状是普通语音的录制播放，鸿蒙自身没有问题，但是普通语音鸿蒙无法和 iOS Android 互通
 * @deprecated 使用 AudioRecorder 替代
 */
declare class AudioManager {
    private static instance;
    private currentPlayingCount;
    private audioCapturer;
    private isRecording;
    /** 数据包大小 **/
    private PCM_PACKET_SIZE;
    /** 数据传输间隔 **/
    private PCM_SEND_DELAY;
    /** 数据队列，保存1280字节一个的packet **/
    private mDataQueue;
    /** 上次未满 **/
    private mRemainingData;
    /** 播放ID，用来判断消息的播放状态 **/
    private playId;
    /** 播放间隔ID **/
    private timerId;
    private enqueueData;
    /** 录音初始化 **/
    private isInitRecord;
    private initRecord;
    static getInstance(): AudioManager;
    /** 采样配置 **/
    private audioStreamCapturerInfo;
    /** 声源配置 **/
    private audioCapturerInfo;
    /** 录音配置 **/
    private audioCapturerOptions;
    /**
     * 开始录音
     *
     * @param context 上下文
     * @param key 关键key
     * @param resolve 成功回调
     * @param reject 失败回调
     */
    startPcmRecord(z337: Context, a338: (result: string) => void): Promise<void>;
    getPcmRecordPath(): string;
    /**
     * 获取音频录制振幅
     */
    getPcmRecordMaxAmplitude(): Promise<number>;
    /**
     * 关闭录音
     */
    stopPcmRecord(): void;
    private audioStreamRendererInfo;
    /** 播放音频配置 **/
    private audioRendererInfo;
    /** 播放配置 **/
    private audioRendererOptions;
    private audioRenderer;
    /** 播放器初始化 **/
    private isInitAudio;
    private initAudio;
    /**
     * 播放PCM音频
     */
    playPcmRecord(a337: number, y336: number, z336?: (state: string, a337: number) => void): Promise<void>;
    /**
     * 根据消息ID跟当前播放的消息ID对比，获取当前播放状态
     * @param msgId 消息ID
     * @returns 消息ID对应的播放状态。如果当前没有消息在播放或者播放的不是当前消息，则返回 initialized 。
     */
    getMessagePlayState(w336: number): string;
    /**
     * 开始播放，开始计时器，设置playId。播放完毕后进行callback
     */
    private startPlayInterval;
    /**
     * 停止播放，停止计时器，重置playId
     */
    private stopPlayInterval;
    /**
     * 停止播放PCM音频
     */
    stopPlayPcmRecord(): void;
    /**
     * 释放资源
     */
    release(): void;
}
export { AudioManager };
