import { TrackInfo } from "./TrackInfo";
import { Thumbnail } from "./Thumbnail";
export declare class MediaInfo {
    mVideoId: string;
    mTitle: string;
    mDuration: number;
    mStatus: string;
    mCoverUrl: string;
    mMediaType: string;
    mTransCodeMode: string;
    mTotalBitrate: number;
    mTrackInfos: TrackInfo[];
    mThumbnailList: Thumbnail[];
    private nativeGetVideoId;
    private nativeSetVideoId;
    private nativeGetTitle;
    private nativeSetTitle;
    private nativeGetDuration;
    private nativeSetDuration;
    private nativeGetStatus;
    private nativeSetStatus;
    private nativeGetCoverUrl;
    private nativeSetCoverUrl;
    private nativeGetMediaType;
    private nativeSetMediaType;
    private nativeGetTransCodeMode;
    private nativeSetTransCodeMode;
    private nativeGetTotalBitrate;
    private nativeSetTotalBitrate;
    private nativeSetTrackInfos;
    private nativeSetThumbnailList;
    /**
     * 获取标题。如果用户有自定义标题，那么值为用户定义的标题。
     *
     * @return 标题
     */
    /****
     * Query the title information. If a custom title is assigned, then the custom title is returned.
     *
     * @return The title.
     */
    getTitle(): string;
    /**
     * 获取时长
     *
     * @return 时长。单位ms。
     */
    /****
     * Query the duration of the media.
     *
     * @return The media duration in milliseconds.
     */
    getDuration(): number;
    /**
     * 设置时长
     *
     * @param duration 单位ms
     */
    /****
     * Set the duration of the media.
     *
     * @param duration The media duration in milliseconds.
     */
    setDuration(q22: number): void;
    /**
     * 获取媒体状态。
     *
     * @return 媒体状态。
     */
    /****
     * Query the status of the media.
     *
     * @return The status of the media.
     */
    getStatus(): string;
    /**
     * 获取媒体id
     *
     * @return 媒体id
     */
    /****
     * Query the ID of the media.
     *
     * @return The ID of the media.
     */
    getVideoId(): string;
    /**
     * 获取封面地址。如果用户有自定义封面，那么值为用户定义的封面地址。
     *
     * @return 封面地址。
     */
    /****
     * Query the album cover of the media. If a custom album cover is set, then the custom album cover is returned.
     *
     * @return The album cover of the media.
     */
    getCoverUrl(): string;
    /**
     * 获取媒体类型。video,audio.
     *
     * @return 媒体类型
     */
    /****
     * Query the type of the media: video or audio.
     *
     * @return The type of the media.
     */
    getMediaType(): string;
    /**
     * 获取转码模式。 FastTranscode（上传完成即转码，且转码完成才能播放），
     * NoTranscode（上传完成不转码，且立即能播放），默认取值FastTranscode
     *
     * @return 转码模式
     */
    /****
     * Query the transcoding mode. FastTranscode: transcode the media after it is uploaded. The media can only be played after it is transcoded.
     * NoTranscode: do not transcode the media after it is uploaded. The media can be played immediately after it is uploaded. Default: FastTranscode.
     *
     * @return The transcoding mode.
     */
    getTransCodeMode(): string;
    /**
     * 获取码率(vod和多码率值为0，应该使用TrackInfo中的videoBitrate)
     *
     * @return
     */
    /****
     * Retrievable bit rate (VOD and multi-bit rate values are 0; Should use TrackInfo's videoBitrate value. )
     *
     * @return
     */
    getTotalBitrate(): number;
    /**
     * 获取媒体所有的流信息
     *
     * @return 流信息。见{@link TrackInfo}
     */
    /****
     * Query all stream information of the media.
     *
     * @return The stream information. See {@link TrackInfo}.
     */
    getTrackInfos(): TrackInfo[];
    /**
     * 获取缩略图地址
     *
     * @return 缩略图地址
     */
    /****
     * Query the URL of the thumbnail image.
     *
     * @return The URL of the thumbnail image.
     */
    getThumbnailList(): Thumbnail[];
}
