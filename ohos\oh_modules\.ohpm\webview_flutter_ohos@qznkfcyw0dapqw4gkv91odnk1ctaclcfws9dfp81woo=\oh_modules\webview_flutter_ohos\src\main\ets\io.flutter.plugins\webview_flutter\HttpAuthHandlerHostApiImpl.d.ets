import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import { HttpAuthHandlerHostApi, HttpAuthHandler } from './GeneratedOhosWebView';
import { InstanceManager } from './InstanceManager';
/**
 * Host api implementation for {@link HttpAuthHandler}.
 *
 * <p><PERSON><PERSON> creating {@link HttpAuthHandler}s that intercommunicate with a paired Dart object.
 */
export declare class HttpAuthHandlerHostApiImpl extends HttpAuthHandlerHostApi {
    binaryMessenger: BinaryMessenger;
    instanceManager: InstanceManager;
    /**
     * Constructs a {@link HttpAuthHandlerHostApiImpl}.
     *
     * @param binaryMessenger used to communicate with <PERSON><PERSON> over asynchronous messages
     * @param instanceManager maintains instances stored to communicate with attached Dart objects
     */
    constructor(binaryMessenger: BinaryMessenger, instanceManager: InstanceManager);
    useHttpAuthUsernamePassword(instanceId: number): boolean;
    cancel(instanceId: number): void;
    proceed(instanceId: number, username: string, password: string): void;
    getHttpAuthHandlerInstance(instanceId: number): HttpAuthHandler;
}
