import { NativeDownloaderBase } from './nativeclass/NativeDownloaderBase';
const onCompletionListenerMap = new WeakMap();
const onPreparedListenerMap = new WeakMap();
const onProgressListenerMap = new WeakMap();
const onErrorListenerMap = new WeakMap();
export class ApsaraDownloader {
    constructor(f1) {
        this.mOutOnCompletionListener = null;
        this.mInnerOnCompletionListener = new InnerCompletionListener(this);
        this.mOutOnPreparedListener = null;
        this.mInnerOnPreparedListener = new InnerPreparedListener(this);
        this.mOutOnProgressListener = null;
        this.mInnerOnProgressListener = new InnerProgressListener(this);
        this.mOutOnErrorListener = null;
        this.mInnerOnErrorListener = new InnerErrorListener(this);
        this.mCoreDownloader = new NativeDownloaderBase(f1);
        this.mContext = f1;
        onCompletionListenerMap.set(this, this.mInnerOnCompletionListener);
        onPreparedListenerMap.set(this, this.mInnerOnPreparedListener);
        onProgressListenerMap.set(this, this.mInnerOnProgressListener);
        onErrorListenerMap.set(this, this.mInnerOnErrorListener);
        this.bindListener();
    }
    start() {
        this.mCoreDownloader?.start();
    }
    stop() {
        this.mCoreDownloader?.stop();
    }
    release() {
        this.mCoreDownloader?.release();
    }
    setSaveDir(e1) {
        this.mCoreDownloader?.setSaveDir(e1);
    }
    getFilePath() {
        return this.mCoreDownloader?.getFilePath() || '';
    }
    selectItem(d1) {
        this.mCoreDownloader?.selectItem(d1);
    }
    prepareVidAuth(c1) {
        this.mCoreDownloader?.prepareVidAuth(c1);
    }
    prepareVidSts(b1) {
        this.mCoreDownloader?.prepareVidSts(b1);
    }
    updateVidStsSource(a1) {
        this.mCoreDownloader?.updateVidStsSource(a1);
    }
    updateVidAuthSource(z) {
        this.mCoreDownloader?.updateVidAuthSource(z);
    }
    deleteFile() {
        this.mCoreDownloader?.deleteFile();
    }
    static setConvertURLCallback(y) {
        NativeDownloaderBase.setConvertURLCallback(y);
    }
    setDownloaderConfig(x) {
        this.mCoreDownloader?.setDownloaderConfig(x);
    }
    setOnPreparedListener(w) {
        this.mOutOnPreparedListener = w;
    }
    setOnProgressListener(v) {
        this.mOutOnProgressListener = v;
    }
    setOnCompletionListener(u) {
        this.mOutOnCompletionListener = u;
    }
    setOnErrorListener(t) {
        this.mOutOnErrorListener = t;
    }
    bindListener() {
        this.mCoreDownloader?.setOnCompletionListener(this.mInnerOnCompletionListener);
        this.mCoreDownloader?.setOnPreparedListener(this.mInnerOnPreparedListener);
        this.mCoreDownloader?.setOnProgressListener(this.mInnerOnProgressListener);
        this.mCoreDownloader?.setOnErrorListener(this.mInnerOnErrorListener);
    }
    onCompletion() {
        if (this.mOutOnCompletionListener) {
            this.mOutOnCompletionListener.onCompletion();
        }
    }
    onPrepared(s) {
        if (this.mOutOnPreparedListener) {
            this.mOutOnPreparedListener.onPrepared(s);
        }
    }
    onProgress(q, r) {
        if (this.mOutOnProgressListener) {
            if (q === 0) {
                this.mOutOnProgressListener.onDownloadingProgress(r);
            }
            else {
                this.mOutOnProgressListener.onProcessingProgress(r);
            }
        }
    }
    onError(p) {
        if (this.mOutOnErrorListener) {
            this.mOutOnErrorListener.onError(p);
        }
    }
}
class InnerCompletionListener {
    constructor(o) {
        this.downloaderRef = o;
    }
    onCompletion() {
        this.downloaderRef.onCompletion();
    }
}
class InnerProgressListener {
    constructor(n) {
        this.downloaderRef = n;
    }
    onDownloadingProgress(m) {
        this.downloaderRef.onProgress(0, m);
    }
    onProcessingProgress(l) {
        this.downloaderRef.onProgress(1, l);
    }
}
class InnerPreparedListener {
    constructor(k) {
        this.downloaderRef = k;
    }
    onPrepared(j) {
        this.downloaderRef.onPrepared(j);
    }
}
class InnerErrorListener {
    constructor(i) {
        this.downloaderRef = i;
    }
    onError(h) {
        this.downloaderRef.onError(h);
    }
}
