import BmObject from "./u2"; import { ArrayList } from '@kit.ArkTS'; import type BmDrawItem from "./a3"; import type BmOnClickListener from "./n3"; import BmBaseMarker from "./c3"; export default class Bm<PERSON>ayer extends BmObject { private mDrawItems; private mOnClickListener; private mLayerId;           constructor(f12?: boolean); getLayerId(): number; fetchDrawItems(): ArrayList<BmDrawItem>;           addDrawItem(c12: BmDrawItem, zIndex?: number): void;                      removeDrawItem(b12: BmDrawItem): void; removeDrawItemType(type: number): void;       clearDrawItems(): void;                             show(x11: boolean): void;           setClickable(w11: boolean): void;             setShowLevel(from: number, to: number): void;       commitUpdate(): void;                 isItemClick(r11: number, s11: number, radius: number): { item: BmBaseMarker; t: import("./ui/BmBaseUI").default; } | { item: BmDrawItem; t: any; };                  setOnClickListener(q11: BmOnClickListener): void;           private findDrawItemByInstance; } 