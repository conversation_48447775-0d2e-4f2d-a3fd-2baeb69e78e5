// @keepTs
// @ts-nocheck
import { BaseMessageItemProvider } from '../../../../conversation/item/provider/BaseMessageItemProvider';
import { UiMessage } from '../../../../conversation/model/UiMessage';
import { CombineMessage } from '../../../../message/content/CombineMessage';
export declare class CombineMessageItemProvider extends BaseMessageItemProvider<CombineMessage> {
    getMessageWrapBuilder(): WrappedBuilder<[
        Context,
        UiMessage,
        number
    ]>;
    isShowSummaryName(v157: Context, w157: CombineMessage): boolean;
    getSummaryTextByMessageContent(r157: Context, s157: CombineMessage): Promise<MutableStyledString>;
}
@Builder
export declare function bindCombineMessageData(g157: Context, h157: UiMessage, i157: number): void;
@Component
export declare struct CombineMessageView {
    @Require
    @Prop
    context: Context;
    @Require
    @Prop
    index: number;
    @ObjectLink
    uiMessage: UiMessage;
    private messageContent;
    aboutToAppear(): void;
    build(): void;
    private getTitle;
    private getSummary;
}
