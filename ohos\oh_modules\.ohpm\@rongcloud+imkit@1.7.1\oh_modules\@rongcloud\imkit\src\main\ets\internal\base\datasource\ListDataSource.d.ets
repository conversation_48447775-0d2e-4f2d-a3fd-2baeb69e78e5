// @keepTs
// @ts-nocheck
/**
 * LazyLoad Class
 */
import { ArrayList } from '@kit.ArkTS';
import { ListDataSourceListener } from './ListDataSourceListener';
/**
 * List 数据源，会话列表数据源和聊天页面数据源的基类
 * @version 1.0.0
 */
@Observed
export declare class ListDataSource<T> implements IDataSource {
    private listeners;
    private listData;
    private dataLengthChangeListener;
    totalCount(): number;
    getData(s53: number): T | undefined;
    getDataList(): ArrayList<T>;
    addDataByIndex(q53: number, r53: T): void;
    addData(p53: T): void;
    addArray(n53: T[]): void;
    removeData(m53: T): void;
    clearDataAndNotify(): void;
    replaceData(k53: ArrayList<T>): void;
    notifyDataLengthChange(): void;
    registerDataLengthChangeListener(h53: ListDataSourceListener): void;
    unregisterDataLengthChangeListener(f53: ListDataSourceListener): void;
    registerDataChangeListener(e53: DataChangeListener): void;
    unregisterDataChangeListener(c53: DataChangeListener): void;
    notifyDataReload(): void;
    notifyDataAdd(x52: number): void;
    notifyDataChange(u52: number): void;
    notifyDataDelete(r52: number): void;
    notifyDataMove(n52: number, o52: number): void;
    notifyDatasetChange(k52: DataOperation[]): void;
}
