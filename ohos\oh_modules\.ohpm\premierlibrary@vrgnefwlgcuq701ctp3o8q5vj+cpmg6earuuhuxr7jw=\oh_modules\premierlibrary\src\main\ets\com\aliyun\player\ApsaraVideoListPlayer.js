import { UrlVideoListPlayer } from './UrlVideoListPlayer';
import { Log } from '../utils/Log';
import { ApsaraVideoPlayer } from './ApsaraVideoPlayer';
import { OhosUrlListPlayer } from './nativeclass/OhosUrlListPlayer';
import { LiveSts } from './source/LiveSts';
import { VidSts } from './source/VidSts';
import { VidAuth } from './source/VidAuth';
import { VidMps } from './source/VidMps';
import { StsInfo } from './source/StsInfo';
import { OhosSaasListPlayer } from './nativeclass/OhosSaasListPlayer';
export class ApsaraVideoListPlayer extends UrlVideoListPlayer {
    constructor(o10, p10) {
        super(o10, p10);
        this.mmLog = new Log("NativePlayerBase_ApsaraVideListPlayer");
        this.mSaasVidePlayer = null;
        this.mSaaSPrerenderPlayer = null;
    }
    createListPlayer(k10, l10, m10, n10) {
        return new OhosSaasListPlayer(k10, m10, n10, true);
    }
    getNativePlayerWithContext(i10, j10) {
        if (this.mSaasVidePlayer == null) {
            this.mSaasVidePlayer = new ApsaraVideoPlayer(i10, j10);
        }
        return this.mSaasVidePlayer;
    }
    getPrerenderPlayerWithContext(g10, h10) {
        if (this.mSaaSPrerenderPlayer == null) {
            this.mSaaSPrerenderPlayer = new ApsaraVideoPlayer(g10, h10);
        }
        return this.mSaaSPrerenderPlayer;
    }
    getCurrentPlayerIndex() {
        let f10 = super.getCorePlayer();
        if (f10 instanceof OhosUrlListPlayer) {
            this.mmLog.info("getCurrentPlayerIndex");
            return f10.getCurrentPlayerIndex();
        }
        return 0;
    }
    stop() {
        super.stop();
        let e10 = super.getCorePlayer();
        if (e10 instanceof OhosUrlListPlayer) {
            this.mmLog.info("stop");
            e10.stop();
        }
    }
    getPreRenderPlayer() {
        let c10 = super.getCorePlayer();
        if (c10 instanceof OhosUrlListPlayer) {
            this.mmLog.info("getPreRenderPlayer");
            let d10 = c10.getPreRenderPlayerIndex();
            return this.getCurrentPrerenderPlayer(d10);
        }
        return undefined;
    }
    setVidAuthDataSource(a10) {
        let b10 = super.getCorePlayer();
        if (b10 instanceof ApsaraVideoPlayer) {
            if (a10 instanceof VidAuth) {
                b10.setVidAuthDataSource(a10);
            }
        }
    }
    setVidStsDataSource(y9) {
        let z9 = super.getCorePlayer();
        if (z9 instanceof ApsaraVideoPlayer) {
            if (y9 instanceof VidSts) {
                z9.setVidStsDataSource(y9);
            }
        }
    }
    setVidMpsDataSource(w9) {
        let x9 = super.getCorePlayer();
        if (x9 instanceof ApsaraVideoPlayer) {
            if (w9 instanceof VidMps) {
                x9.setVidMpsDataSource(w9);
            }
        }
    }
    setLiveStsDataSource(u9) {
        let v9 = super.getCorePlayer();
        if (v9 instanceof ApsaraVideoPlayer) {
            if (u9 instanceof LiveSts) {
                v9.setLiveStsDataSource(u9);
            }
        }
    }
    updateVidAuth(s9) {
        let t9 = super.getCorePlayer();
        if (t9 instanceof ApsaraVideoPlayer) {
            if (s9 instanceof VidAuth) {
                t9.updateVidAuth(s9);
            }
        }
    }
    updateStsInfo(q9) {
        let r9 = super.getCorePlayer();
        if (r9 instanceof ApsaraVideoPlayer) {
            if (q9 instanceof StsInfo) {
                r9.updateStsInfo(q9);
            }
        }
    }
}
