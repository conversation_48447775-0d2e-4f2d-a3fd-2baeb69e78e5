import { FlutterPlugin, FlutterPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
import { <PERSON><PERSON>all<PERSON>andler, MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import MethodCall from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodCall';
export declare class PackageInfoPlugin implements FlutterPlugin, MethodCallHandler {
    getUniqueClassName(): string;
    private methodChannel;
    private applicationContext;
    onAttachedToEngine(binding: FlutterPluginBinding): void;
    onDetachedFromEngine(binding: FlutterPluginBinding): void;
    onMethodCall(call: Method<PERSON>all, result: MethodResult): void;
}
