import { VidSourceBase } from './VidSourceBase';
export declare class VidMps extends VidSourceBase {
    private mMediaId;
    private mAccessKeyId;
    private mAccessKeySecret;
    private mSecurityToken;
    private mRegion;
    private mPlayDomain;
    private mAuthInfo;
    private mHlsUriToken;
    constructor();
    protected nativeGetMediaId: Function;
    protected nativeSetMediaId: Function;
    protected nativeGetAccessKeyId: Function;
    protected nativeSetAccessKeyId: Function;
    protected nativeGetAccessKeySecret: Function;
    protected nativeSetAccessKeySecret: Function;
    protected nativeGetSecurityToken: Function;
    protected nativeSetSecurityToken: Function;
    protected nativeGetRegion: Function;
    protected nativeSetRegion: Function;
    protected nativeGetPlayDomain: Function;
    protected nativeSetPlayDomain: Function;
    protected nativeGetAuthInfo: Function;
    protected nativeSetAuthInfo: Function;
    protected nativeGetHlsUriToken: Function;
    protected nativeSetHlsUriToken: Function;
    /**
     * 设置清晰度相关信息
     *
     * @param quality      期望播放的清晰度
     * @param forceQuality 是否强制使用此清晰度。如果强制，则在没有对应清晰度的情况下播放不了。
     */
    /****
     * Definition settings
     *
     * @param quality      Specify a definition for playback.
     * @param forceQuality Indicate whether to force the player to play the media with the specified definition. However, if the media does not support the specified definition, then it cannot be played.
     */
    setQuality(v37: string, w37: boolean): void;
    /**
     * 获取媒体id
     *
     * @return 媒体id
     */
    /****
     * Query the ID of the media.
     *
     * @return The ID of the media.
     */
    getMediaId(): string;
    /**
     * 设置媒体id
     *
     * @param mMediaId 媒体id
     */
    /****
     * Specify a media ID.
     *
     * @param mMediaId The ID of the media.
     */
    setMediaId(u37: string): void;
    /**
     * 获取鉴权id
     *
     * @return 鉴权id
     */
    /****
     * Query the AccessKey ID for authentication.
     *
     * @return The AccessKey ID for authentication.
     */
    getAccessKeyId(): string;
    /**
     * 设置鉴权id
     *
     * @param mAccessKeyId 鉴权id
     */
    /****
     * Set the AccessKey ID for authentication.
     *
     * @param mAccessKeyId The AccessKey ID for authentication
     */
    setAccessKeyId(t37: string): void;
    /**
     * 获取鉴权秘钥
     *
     * @return 鉴权秘钥
     */
    /****
     * Query the AccessKey Secret for authentication.
     *
     * @return The AccessKey Secret for authentication.
     */
    getAccessKeySecret(): string;
    /**
     * 设置鉴权秘钥
     *
     * @param mAccessKeySecret 鉴权秘钥
     */
    /****
     * Set the AccessKey Secret for authentication.
     *
     * @param mAccessKeySecret The AccessKey Secret for authentication.
     */
    setAccessKeySecret(s37: string): void;
    /**
     * 获取安全token
     *
     * @return 安全token
     */
    /****
     * Query the token.
     *
     * @return The token.
     */
    getSecurityToken(): string;
    /**
     * 设置安全token
     *
     * @param mSecurityToken 安全token
     */
    /****
     * Set a token.
     *
     * @param mSecurityToken The specified token.
     */
    setSecurityToken(r37: string): void;
    /**
     * 获取地域
     *
     * @return 地域
     */
    /****
     * Query region information.
     *
     * @return The region information.
     */
    getRegion(): string;
    /**
     * 设置地域
     *
     * @param mRegion 地域
     */
    /****
     * Specify regions.
     *
     * @param mRegion The specified regions.
     */
    setRegion(q37: string): void;
    /**
     * 获取播放domain
     *
     * @return 播放domain
     */
    /****
     * Query the playback domain.
     *
     * @return The playback domain.
     */
    getPlayDomain(): string;
    /**
     * 设置播放domain
     *
     * @param mPlayDomain 播放domain
     */
    /****
     * Specify a playback domain.
     *
     * @param mPlayDomain The playback domain.
     */
    setPlayDomain(p37: string): void;
    /**
     * 获取认证信息
     *
     * @return 认证信息
     */
    /****
     * Query authentication information.
     *
     * @return The authentication information.
     */
    getAuthInfo(): string;
    /**
     * 设置认证信息
     *
     * @param mAuthInfo 认证信息
     */
    /****
     * Specify authentication information.
     *
     * @param mAuthInfo The authentication information.
     */
    setAuthInfo(o37: string): void;
    /**
     * 获取HlsUriToken
     *
     * @return HlsUriToken
     */
    /****
     * Query HlsUriToken.
     *
     * @return HlsUriToken.
     */
    getHlsUriToken(): string;
    /**
     * 设置HlsUriToken
     *
     * @param mHlsUriToken HlsUriToken
     */
    /****
     * Set HlsUriToken.
     *
     * @param mHlsUriToken HlsUriToken.
     */
    setHlsUriToken(n37: string): void;
}
