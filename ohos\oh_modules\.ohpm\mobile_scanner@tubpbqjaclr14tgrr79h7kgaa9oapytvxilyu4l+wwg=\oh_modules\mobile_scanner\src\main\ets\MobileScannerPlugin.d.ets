import { FlutterPlugin, FlutterPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
import { <PERSON><PERSON><PERSON><PERSON>andler, MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import MethodCall from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodCall';
import { AbilityAware, AbilityPluginBinding } from '@ohos/flutter_ohos/index';
import customScan from "@hms.core.scan.customScan";
import { Barcode } from './Barcode';
import { BusinessError as BusinessError } from "@ohos.base";
export declare class MobileScannerPlugin implements FlutterPlugin, MethodCallHandler, AbilityAware {
    getUniqueClassName(): string;
    onAttachedToAbility(binding: AbilityPluginBinding): void;
    onDetachedFromAbility(): void;
    private methodChannel;
    private eventChannel;
    private eventSink;
    private applicationContext;
    private ability;
    private textureId;
    private surfaceId;
    private binding;
    private isStart;
    private cameraWidth;
    private cameraHeight;
    private scanWidth;
    private scanHeight;
    private scanCodeRect;
    private scanBottom;
    private displayHeight;
    private displayWidth;
    private scanWindow;
    private imageBuffer;
    publishEvent(event: ESObject): void;
    onAttachedToEngine(binding: FlutterPluginBinding): void;
    onDetachedFromEngine(binding: FlutterPluginBinding): void;
    onMethodCall(call: MethodCall, result: MethodResult): void;
    start(call: MethodCall, result: MethodResult): Promise<void>;
    private scanCallback;
    private frameCallback;
    changeToXComponent(frameResult: customScan.ScanFrame): void;
    startScan(): void;
    setDisplay(): void;
    toFixedNumber(no: number): number;
    stop(result: MethodResult): Promise<void>;
    toggleTorch(call: MethodCall, result: MethodResult): void;
    analyzeImage(call: MethodCall, result: MethodResult): void;
    setScale(call: MethodCall, result: MethodResult): void;
    resetScale(result: MethodResult): void;
    updateScanWindow(call: MethodCall, result: MethodResult): void;
    callback(barcodes: Barcode[], image: Uint8Array | null, width: number, height: number): void;
    private isBarcodeInScanWindow;
    errorCallback(error: string): void;
    torchCallback(error: BusinessError, bool: boolean): void;
}
