import { Log } from '../utils/Log';
const log = new Log('AVPBase');
const preparedListenerMap = new WeakMap();
const onInfoListenerMap = new WeakMap();
const onRenderingStartListenerMap = new WeakMap();
const onStateChangedListenerMap = new WeakMap();
const onCompletionListenerMap = new WeakMap();
const onLoadingStatusListenerMap = new WeakMap();
const onErrorListenerMap = new WeakMap();
const onVideoSizeChangedListenerMap = new WeakMap();
const onSeekCompleteListenerMap = new WeakMap();
const onSubtitleDisplayListenerMap = new WeakMap();
const onVideoRenderedListenerMap = new WeakMap();
const onAudioInterruptEventListenerMap = new WeakMap();
const onTrackReadyListenerMap = new WeakMap();
const onSubTrackReadyListenerMap = new WeakMap();
const onSwichStreamListenerMap = new WeakMap();
const onAVNotSyncStatusListenerMap = new WeakMap();
const onTrackChangedListenerMap = new WeakMap();
const onSnapShotListenerMap = new WeakMap();
const onSeiDataListenerMap = new WeakMap();
export class AVPBase {
    constructor(c17, d17) {
        this.mOutMediaInfo = null;
        this.mOutSubMediaInfo = null;
        this.mOutOnPreparedListener = null;
        this.mInnerOnPreparedListener = new InnerPrepareListener(this);
        this.mOutOnInfoListener = null;
        this.mInnerOnInfoListener = new InnerInfoListener(this);
        this.mOutOnRenderingStartListener = null;
        this.mInnerOnRenderingStartListener = new InnerRenderingStartListener(this);
        this.mOutOnStateChangedListener = null;
        this.mInnerOnStateChangedListener = new InnerStateChangedListener(this);
        this.mOutOnVideoSizeChangedListener = null;
        this.mInnerOnVideoSizeChangedListener = new InnerVideoSizeChangedListener(this);
        this.mOutOnLoadingStatusListener = null;
        this.mInnerOnLoadingStatusListener = new InnerLoadingStatusListener(this);
        this.mOutOnSeekCompleteListener = null;
        this.mInnerOnSeekCompleteListener = new InnerSeekCompleteListener(this);
        this.mOutOnCompletionListener = null;
        this.mInnerOnCompletionListener = new InnerCompletionListener(this);
        this.mOutOnVideoRenderedListener = null;
        this.mInnerOnVideoRenderedListener = new InnerVideoRenderedListener(this);
        this.mOutOnSubtitleDisplayListener = null;
        this.mInnerOnSubtitleDisplayListener = new InnerSubtitleDisplayListener(this);
        this.mOutOnErrorListener = null;
        this.mInnerOnErrorListener = new InnerErrorListener(this);
        this.mOutOnAudioInterruptEventListener = null;
        this.mInnerOnAudioInterruptEventListener = new InnerAudioInterruptEventListener(this);
        this.mOutOnTrackReadyListener = null;
        this.mInnerOnTrackReadyListener = new InnerTrackReadyListener(this);
        this.mOutOnSubTrackReadyListener = null;
        this.mInnerOnSubTrackReadyListener = new InnerSubTrackReadyListener(this);
        this.mOutOnStreamSwitchedListener = null;
        this.mInnerOnStreamSwitchedListener = new InnerStreamSwitchedListener(this);
        this.mOutOnAVNotSyncStatusListener = null;
        this.mInnerOnAVNotSyncStatusListener = new InnerOnAVNotSyncStatusListener(this);
        this.mOutOnTrackChangedListener = null;
        this.mInnerOnTrackChangedListener = new InnerTrackChangedListener(this);
        this.mOutOnSnapShotListener = null;
        this.mInnerOnSnapShotListener = new InnerSnapShotListener(this);
        this.mOutOnSeiDataListener = null;
        this.mInnerOnSeiDataListener = new InnerSeiDataListener(this);
        this.mContext = c17;
        this.mTraceId = d17;
        this.mCorePlayer = this.createAlivcMediaPlayer(c17);
        this.mCorePlayer.setTraceId(this.mTraceId);
        preparedListenerMap.set(this, this.mInnerOnPreparedListener);
        onInfoListenerMap.set(this, this.mInnerOnInfoListener);
        onRenderingStartListenerMap.set(this, this.mInnerOnRenderingStartListener);
        onStateChangedListenerMap.set(this, this.mInnerOnStateChangedListener);
        onCompletionListenerMap.set(this, this.mInnerOnCompletionListener);
        onLoadingStatusListenerMap.set(this, this.mInnerOnLoadingStatusListener);
        onErrorListenerMap.set(this, this.mInnerOnErrorListener);
        onVideoSizeChangedListenerMap.set(this, this.mInnerOnVideoSizeChangedListener);
        onSeekCompleteListenerMap.set(this, this.mInnerOnSeekCompleteListener);
        onSubtitleDisplayListenerMap.set(this, this.mInnerOnSubtitleDisplayListener);
        onVideoRenderedListenerMap.set(this, this.mInnerOnVideoRenderedListener);
        onAudioInterruptEventListenerMap.set(this, this.mInnerOnAudioInterruptEventListener);
        onTrackReadyListenerMap.set(this, this.mInnerOnTrackReadyListener);
        onSubTrackReadyListenerMap.set(this, this.mInnerOnSubTrackReadyListener);
        onSwichStreamListenerMap.set(this, this.mInnerOnStreamSwitchedListener);
        onAVNotSyncStatusListenerMap.set(this, this.mInnerOnAVNotSyncStatusListener);
        onTrackChangedListenerMap.set(this, this.mInnerOnTrackChangedListener);
        onSnapShotListenerMap.set(this, this.mInnerOnSnapShotListener);
        onSeiDataListenerMap.set(this, this.mInnerOnSeiDataListener);
        this.bindListener();
    }
    getNativeContextAddr() {
        return this.mCorePlayer?.getNativeContextAddr();
    }
    prepare() {
        this.prepareInner();
    }
    prepareInner() {
        this.mCorePlayer?.prepare();
    }
    getCorePlayer() {
        return this.mCorePlayer;
    }
    start() {
        this.startInner();
    }
    startInner() {
        this.mCorePlayer?.start();
    }
    pause() {
        this.pauseInner();
    }
    pauseInner() {
        this.mCorePlayer?.pause();
    }
    stop() {
        let b17 = "protected.playEvent.user.stop";
        this.setGlobalTime(b17);
        this.stopInner();
    }
    stopInner() {
        this.mCorePlayer?.stop();
    }
    setAutoPlay(a17) {
        this.mCorePlayer?.setAutoPlay(a17);
    }
    setSurfaceId(z16) {
        this.mCorePlayer?.setSurfaceId(z16);
    }
    setSpeed(y16) {
        this.mCorePlayer?.setSpeed(y16);
    }
    setVolume(x16) {
        this.mCorePlayer?.setVolume(x16);
    }
    getVolume() {
        return this.mCorePlayer?.getVolume();
    }
    seekTo(v16, w16) {
        this.mCorePlayer?.seekTo(v16, w16);
    }
    setStartTime(t16, u16) {
        this.mCorePlayer?.setStartTime(t16, u16);
    }
    getDuration() {
        return this.mCorePlayer?.getDuration();
    }
    getPlayedDuration() {
        return this.mCorePlayer?.getPlayedDuration();
    }
    getCurrentPosition() {
        return this.mCorePlayer?.getCurrentPosition();
    }
    getBufferedPosition() {
        return this.mCorePlayer?.getBufferedPosition();
    }
    getPlayerStatus() {
        return this.mCorePlayer?.getPlayerStatus();
    }
    enableHardwareDecoder(s16) {
        this.mCorePlayer?.enableHardwareDecoder(s16);
    }
    release() {
        let r16 = "protected.playEvent.user.destroy";
        this.setGlobalTime(r16);
        this.releaseInner();
    }
    setGlobalTime(q16) {
        this.mCorePlayer?.setGlobalTime(q16);
    }
    releaseInner() {
        this.mCorePlayer?.release();
    }
    releaseAsync() {
        let o16 = "protected.playEvent.user.destroy";
        this.setGlobalTime(o16);
        log.info('releaseAsync');
        this.pause();
        setTimeout(() => {
            this.stopInner();
            this.releaseInner();
        }, 0);
    }
    setMute(n16) {
        this.mCorePlayer?.setMute(n16);
    }
    isMuted() {
        return this.mCorePlayer?.isMuted();
    }
    setScaleMode(m16) {
        this.mCorePlayer?.setScaleMode(m16);
    }
    getScaleMode() {
        return this.mCorePlayer?.getScaleMode();
    }
    setLoop(l16) {
        this.mCorePlayer?.setLoop(l16);
    }
    isLoop() {
        return this.mCorePlayer?.isLoop();
    }
    getVideoWidth() {
        return this.mCorePlayer?.getVideoWidth();
    }
    getVideoHeight() {
        return this.mCorePlayer?.getVideoHeight();
    }
    getVideoRotation() {
        return this.mCorePlayer?.getVideoRotation();
    }
    reload() {
        this.mCorePlayer?.reload();
    }
    setRotateMode(k16) {
        this.mCorePlayer?.setRotateMode(k16);
    }
    getRotateMode() {
        return this.mCorePlayer?.getRotateMode();
    }
    setMirrorMode(j16) {
        this.mCorePlayer?.setMirrorMode(j16);
    }
    getMirrorMode() {
        return this.mCorePlayer?.getMirrorMode();
    }
    setAlphaRenderMode(i16) {
        this.mCorePlayer?.setAlphaRenderMode(i16);
    }
    getAlphaRenderMode() {
        return this.mCorePlayer?.getAlphaRenderMode();
    }
    setVideoBackgroundColor(h16) {
        this.mCorePlayer?.setVideoBackgroundColor(h16);
    }
    getSpeed() {
        return this.mCorePlayer?.getSpeed();
    }
    isAutoPlay() {
        return this.mCorePlayer?.isAutoPlay();
    }
    setConfig(g16) {
        this.mCorePlayer?.setConfig(g16);
    }
    getConfig() {
        return this.mCorePlayer?.getConfig();
    }
    setOption(d16, e16) {
        let f16 = "player_option_" + d16 + "_str";
        this.mCorePlayer?.setOption(f16, e16);
    }
    setOptionNum(a16, b16) {
        let c16 = "player_option_" + a16.toString() + "_str";
        this.mCorePlayer?.setOption(c16, b16.toString());
    }
    getOption(z15) {
        return this.mCorePlayer?.getOption(z15);
    }
    selectTrack(y15) {
        this.mCorePlayer?.selectTrack(y15);
    }
    switchStream(x15) {
        this.mCorePlayer?.switchStream(x15);
    }
    getMediaInfo() {
        return this.mOutMediaInfo;
    }
    getSubMediaInfo() {
        return this.mOutSubMediaInfo;
    }
    currentTrack(w15) {
        if (this.mCorePlayer) {
            return this.mCorePlayer.getCurrentTrack(w15);
        }
        else {
            return null;
        }
    }
    addExtSubtitle(v15) {
        this.mCorePlayer?.addExtSubtitle(v15);
    }
    ;
    selectExtSubtitle(t15, u15) {
        this.mCorePlayer?.selectExtSubtitle(t15, u15);
    }
    setStreamDelay(r15, s15) {
        this.mCorePlayer?.setStreamDelay(r15, s15);
    }
    setMaxAccurateSeekDelta(q15) {
        this.mCorePlayer?.setMaxAccurateSeekDelta(q15);
    }
    setCacheConfig(p15) {
        this.mCorePlayer?.setCacheConfig(p15);
    }
    setIPResolveType(o15) {
        this.mCorePlayer?.setIPResolveType(o15);
    }
    setFastStart(n15) {
        this.mCorePlayer?.setFastStart(n15);
    }
    snapShot() {
        this.mCorePlayer?.snapShot();
    }
    clearScreen() {
        this.mCorePlayer?.clearScreen();
    }
    getCacheFilePathByUrl(m15) {
        return this.mCorePlayer?.getCacheFilePathByUrl(m15) ?? '';
    }
    getCacheFilePathByVid(i15, j15, k15, l15) {
        return this.mCorePlayer?.getCacheFilePathByVid(i15, j15, k15, l15) ?? '';
    }
    getPropertyString(h15) {
        return this.mCorePlayer?.getPropertyString(h15) ?? '';
    }
    setDefaultBandWidth(g15) {
        this.mCorePlayer?.setDefaultBandWidth(g15);
    }
    sendCustomEvent(f15) {
        this.mCorePlayer?.sendCustomEvent(f15);
    }
    setVideoTag(e15) {
        this.mCorePlayer?.setVideoTag(e15);
    }
    setUserData(d15) {
        this.mCorePlayer?.setUserData(d15);
    }
    setTraceId(c15) {
        this.mCorePlayer?.setTraceId(c15);
    }
    getUserData() {
        return this.mCorePlayer?.getUserData() ?? '';
    }
    bindListener() {
        this.mCorePlayer?.setOnPreparedListener(this.mInnerOnPreparedListener);
        this.mCorePlayer?.setOnInfoListener(this.mInnerOnInfoListener);
        this.mCorePlayer?.setOnRenderingStartListener(this.mInnerOnRenderingStartListener);
        this.mCorePlayer?.setOnStateChangedListener(this.mInnerOnStateChangedListener);
        this.mCorePlayer?.setOnVideoSizeChangedListener(this.mInnerOnVideoSizeChangedListener);
        this.mCorePlayer?.setOnLoadingStatusListener(this.mInnerOnLoadingStatusListener);
        this.mCorePlayer?.setOnSeekCompleteListener(this.mInnerOnSeekCompleteListener);
        this.mCorePlayer?.setOnCompletionListener(this.mInnerOnCompletionListener);
        this.mCorePlayer?.setOnVideoRenderedListener(this.mInnerOnVideoRenderedListener);
        this.mCorePlayer?.setOnSubtitleDisplayListener(this.mInnerOnSubtitleDisplayListener);
        this.mCorePlayer?.setOnErrorListener(this.mInnerOnErrorListener);
        this.mCorePlayer?.setOnAudioInterruptEventListener(this.mInnerOnAudioInterruptEventListener);
        this.mCorePlayer?.setOnTrackReadyListener(this.mInnerOnTrackReadyListener);
        this.mCorePlayer?.setOnSubTrackReadyListener(this.mInnerOnSubTrackReadyListener);
        this.mCorePlayer?.setOnStreamSwitchedListener(this.mInnerOnStreamSwitchedListener);
        this.mCorePlayer?.setOnAVNotSyncStatusListener(this.mInnerOnAVNotSyncStatusListener);
        this.mCorePlayer?.setOnTrackChangedListener(this.mInnerOnTrackChangedListener);
        this.mCorePlayer?.setOnSnapShotListener(this.mInnerOnSnapShotListener);
        this.mCorePlayer?.setOnSeiDataListener(this.mInnerOnSeiDataListener);
    }
    setOnPreparedListener(b15) {
        this.mOutOnPreparedListener = b15;
    }
    setOnInfoListener(a15) {
        this.mOutOnInfoListener = a15;
    }
    setOnRenderingStartListener(z14) {
        this.mOutOnRenderingStartListener = z14;
    }
    setOnStateChangedListener(y14) {
        this.mOutOnStateChangedListener = y14;
    }
    setOnCompletionListener(x14) {
        this.mOutOnCompletionListener = x14;
    }
    setOnAVNotSyncStatusListener(w14) {
        this.mOutOnAVNotSyncStatusListener = w14;
    }
    setOnStreamSwitchedListener(v14) {
        this.mOutOnStreamSwitchedListener = v14;
    }
    setOnLoadingStatusListener(u14) {
        this.mOutOnLoadingStatusListener = u14;
    }
    setOnErrorListener(t14) {
        this.mOutOnErrorListener = t14;
    }
    setOnVideoSizeChangedListener(s14) {
        this.mOutOnVideoSizeChangedListener = s14;
    }
    setOnSeekCompleteListener(r14) {
        this.mOutOnSeekCompleteListener = r14;
    }
    setOnSubtitleDisplayListener(q14) {
        this.mOutOnSubtitleDisplayListener = q14;
    }
    setOnVideoRenderedListener(p14) {
        this.mOutOnVideoRenderedListener = p14;
    }
    setOnAudioInterruptEventListener(o14) {
        this.mOutOnAudioInterruptEventListener = o14;
    }
    setOnTrackReadyListener(n14) {
        this.mOutOnTrackReadyListener = n14;
    }
    setOnSubTrackReadyListener(m14) {
        this.mOutOnSubTrackReadyListener = m14;
    }
    setOnTrackChangedListener(l14) {
        this.mOutOnTrackChangedListener = l14;
    }
    setOnSnapShotListener(k14) {
        this.mOutOnSnapShotListener = k14;
    }
    setOnSeiDataListener(j14) {
        this.mOutOnSeiDataListener = j14;
    }
    onPrepared() {
        if (this.mOutOnPreparedListener != null) {
            this.mOutOnPreparedListener.onPrepared();
        }
    }
    onInfo(i14) {
        if (this.mOutOnInfoListener != null) {
            this.mOutOnInfoListener.onInfo(i14);
        }
    }
    onRenderingStart() {
        if (this.mOutOnRenderingStartListener != null) {
            this.mOutOnRenderingStartListener.onRenderingStart();
        }
    }
    onStateChanged(h14) {
        if (this.mOutOnStateChangedListener != null) {
            this.mOutOnStateChangedListener.onStateChanged(h14);
        }
    }
    onVideoSizeChanged(f14, g14) {
        if (this.mOutOnVideoSizeChangedListener != null) {
            this.mOutOnVideoSizeChangedListener.onVideoSizeChanged(f14, g14);
        }
    }
    onLoadingBegin() {
        if (this.mOutOnLoadingStatusListener != null) {
            this.mOutOnLoadingStatusListener.onLoadingBegin();
        }
    }
    onLoadingProgress(d14, e14) {
        if (this.mOutOnLoadingStatusListener != null) {
            this.mOutOnLoadingStatusListener.onLoadingProgress(d14, e14);
        }
    }
    onLoadingEnd() {
        if (this.mOutOnLoadingStatusListener != null) {
            this.mOutOnLoadingStatusListener.onLoadingEnd();
        }
    }
    onSeekEnd() {
        if (this.mOutOnSeekCompleteListener != null) {
            this.mOutOnSeekCompleteListener.onSeekComplete();
        }
    }
    onCompletion() {
        if (this.mOutOnCompletionListener != null) {
            this.mOutOnCompletionListener.onCompletion();
        }
    }
    onVideoRendered(b14, c14) {
        if (this.mOutOnVideoRenderedListener != null) {
            this.mOutOnVideoRenderedListener.onVideoRendered(b14, c14);
        }
    }
    onSubtitleShow(y13, z13, a14) {
        if (this.mOutOnSubtitleDisplayListener != null) {
            this.mOutOnSubtitleDisplayListener.onSubtitleShow(y13, z13, a14);
        }
    }
    onSubtitleExtAdded(w13, x13) {
        if (this.mOutOnSubtitleDisplayListener != null) {
            this.mOutOnSubtitleDisplayListener.onSubtitleExtAdded(w13, x13);
        }
    }
    onSubtitleHide(u13, v13) {
        if (this.mOutOnSubtitleDisplayListener != null) {
            this.mOutOnSubtitleDisplayListener.onSubtitleHide(u13, v13);
        }
    }
    onSubtitleHeader(s13, t13) {
        if (this.mOutOnSubtitleDisplayListener != null) {
            this.mOutOnSubtitleDisplayListener.onSubtitleHeader(s13, t13);
        }
    }
    onError(r13) {
        if (this.mOutOnErrorListener != null) {
            this.mOutOnErrorListener.onError(r13);
        }
    }
    onAudioInterruptEvent(q13) {
        if (this.mOutOnAudioInterruptEventListener != null) {
            this.mOutOnAudioInterruptEventListener.onAudioInterruptEvent(q13);
        }
    }
    onTrackReady(p13) {
        this.mOutMediaInfo = p13;
        if (this.mOutOnTrackReadyListener != null) {
            this.mOutOnTrackReadyListener.onTrackReady(p13);
        }
    }
    onSubTrackReady(o13) {
        this.mOutSubMediaInfo = o13;
        if (this.mOutOnSubTrackReadyListener != null) {
            this.mOutOnSubTrackReadyListener.onSubTrackReady(o13);
        }
    }
    onSwitchedSuccess(n13) {
        if (this.mOutOnStreamSwitchedListener != null) {
            this.mOutOnStreamSwitchedListener.onSwitchedSuccess(n13);
        }
    }
    onSwitchedFail(l13, m13) {
        if (this.mOutOnStreamSwitchedListener != null) {
            this.mOutOnStreamSwitchedListener.onSwitchedFail(l13, m13);
        }
    }
    onAVNotSyncStart(k13) {
        if (this.mOutOnAVNotSyncStatusListener != null) {
            this.mOutOnAVNotSyncStatusListener.onAVNotSyncStart(k13);
        }
    }
    onAVNotSyncEnd() {
        if (this.mOutOnAVNotSyncStatusListener != null) {
            this.mOutOnAVNotSyncStatusListener.onAVNotSyncEnd();
        }
    }
    onChangedSuccess(j13) {
        if (this.mOutOnTrackChangedListener != null) {
            this.mOutOnTrackChangedListener.onChangedSuccess(j13);
        }
    }
    onSnapShot(g13, h13, i13) {
        if (this.mOutOnSnapShotListener != null) {
            this.mOutOnSnapShotListener.onSnapShot(g13, h13, i13);
        }
    }
    onSeiData(e13, f13) {
        if (this.mOutOnSeiDataListener != null) {
            this.mOutOnSeiDataListener.onSeiData(e13, f13);
        }
    }
}
class InnerPrepareListener {
    constructor(d13) {
        this.avbBaseWR = d13;
    }
    onPrepared() {
        if (this.avbBaseWR != null) {
            this.avbBaseWR.onPrepared();
        }
    }
}
class InnerInfoListener {
    constructor(c13) {
        this.avpBaseRef = c13;
    }
    onInfo(b13) {
        if (this.avpBaseRef != null) {
            this.avpBaseRef.onInfo(b13);
        }
    }
}
class InnerRenderingStartListener {
    constructor(a13) {
        this.avpBaseRef = a13;
    }
    onRenderingStart() {
        if (this.avpBaseRef != null) {
            this.avpBaseRef.onRenderingStart();
        }
    }
}
class InnerStateChangedListener {
    constructor(z12) {
        this.avpBaseRef = z12;
    }
    onStateChanged(y12) {
        this.avpBaseRef.onStateChanged(y12);
    }
}
class InnerVideoSizeChangedListener {
    constructor(x12) {
        this.avpBaseRef = x12;
    }
    onVideoSizeChanged(v12, w12) {
        this.avpBaseRef.onVideoSizeChanged(v12, w12);
    }
}
class InnerLoadingStatusListener {
    constructor(u12) {
        this.avpBaseRef = u12;
    }
    onLoadingBegin() {
        this.avpBaseRef.onLoadingBegin();
    }
    onLoadingProgress(s12, t12) {
        this.avpBaseRef.onLoadingProgress(s12, t12);
    }
    onLoadingEnd() {
        this.avpBaseRef.onLoadingEnd();
    }
}
class InnerSeekCompleteListener {
    constructor(r12) {
        this.avpBaseRef = r12;
    }
    onSeekComplete() {
        this.avpBaseRef.onSeekEnd();
    }
}
class InnerCompletionListener {
    constructor(q12) {
        this.avpBaseRef = q12;
    }
    onCompletion() {
        this.avpBaseRef.onCompletion();
    }
}
class InnerVideoRenderedListener {
    constructor(p12) {
        this.avpBaseRef = p12;
    }
    onVideoRendered(n12, o12) {
        this.avpBaseRef.onVideoRendered(n12, o12);
    }
}
class InnerSubtitleDisplayListener {
    constructor(m12) {
        this.avpBaseRef = m12;
    }
    onSubtitleShow(j12, k12, l12) {
        this.avpBaseRef.onSubtitleShow(j12, k12, l12);
    }
    onSubtitleExtAdded(h12, i12) {
        this.avpBaseRef.onSubtitleExtAdded(h12, i12);
    }
    onSubtitleHide(f12, g12) {
        this.avpBaseRef.onSubtitleHide(f12, g12);
    }
    onSubtitleHeader(d12, e12) {
        this.avpBaseRef.onSubtitleHeader(d12, e12);
    }
}
class InnerErrorListener {
    constructor(c12) {
        this.avpBaseRef = c12;
    }
    onError(b12) {
        this.avpBaseRef.onError(b12);
    }
}
class InnerAudioInterruptEventListener {
    constructor(a12) {
        this.avpBaseRef = a12;
    }
    onAudioInterruptEvent(z11) {
        this.avpBaseRef.onAudioInterruptEvent(z11);
    }
}
class InnerTrackReadyListener {
    constructor(y11) {
        this.avpBaseRef = y11;
    }
    onTrackReady(x11) {
        this.avpBaseRef.onTrackReady(x11);
    }
}
class InnerSubTrackReadyListener {
    constructor(w11) {
        this.avpBaseRef = w11;
    }
    onSubTrackReady(v11) {
        this.avpBaseRef.onSubTrackReady(v11);
    }
}
class InnerStreamSwitchedListener {
    constructor(u11) {
        this.avpBaseRef = u11;
    }
    onSwitchedSuccess(t11) {
        this.avpBaseRef.onSwitchedSuccess(t11);
    }
    onSwitchedFail(r11, s11) {
        this.avpBaseRef.onSwitchedFail(r11, s11);
    }
}
class InnerOnAVNotSyncStatusListener {
    constructor(q11) {
        this.avpBaseRef = q11;
    }
    onAVNotSyncStart(p11) {
        this.avpBaseRef.onAVNotSyncStart(p11);
    }
    onAVNotSyncEnd() {
        this.avpBaseRef.onAVNotSyncEnd();
    }
}
class InnerTrackChangedListener {
    constructor(o11) {
        this.avpBaseRef = o11;
    }
    onChangedSuccess(n11) {
        this.avpBaseRef.onChangedSuccess(n11);
    }
}
class InnerSnapShotListener {
    constructor(m11) {
        this.avpBaseRef = m11;
    }
    onSnapShot(j11, k11, l11) {
        this.avpBaseRef.onSnapShot(j11, k11, l11);
    }
}
class InnerSeiDataListener {
    constructor(i11) {
        this.avpBaseRef = i11;
    }
    onSeiData(g11, h11) {
        this.avpBaseRef.onSeiData(g11, h11);
    }
}
