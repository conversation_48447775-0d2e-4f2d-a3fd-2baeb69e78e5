import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wuling_flutter_app/constant/service_constant.dart';
import 'package:wuling_flutter_app/constant/web_view_url_tool.dart';
import 'package:wuling_flutter_app/models/common/advertise_commodify.dart';
import 'package:wuling_flutter_app/models/global_data.dart';
import 'package:wuling_flutter_app/routes/jump_tool.dart';
import 'package:wuling_flutter_app/utils/app_config_util.dart';
import 'package:wuling_flutter_app/utils/manager/dialog_manager.dart';
import 'package:wuling_flutter_app/utils/manager/loading_manager.dart';
import 'package:wuling_flutter_app/utils/manager/login_manager.dart';
import 'package:wuling_flutter_app/widgets/common/custom_dialog.dart';
import 'package:wuling_flutter_app/widgets/common/image_view.dart';

import '../../utils/manager/log_manager.dart';
class CarAdvertiseDetailBottomNav extends StatelessWidget {
  final AdvertiseCommodify? advertise;
  final VoidCallback onBuy;

  const CarAdvertiseDetailBottomNav({super.key,required this.advertise,required this.onBuy});

  @override
  Widget build(BuildContext context) {
    // bool isImageUrlValid =
    //     (advertise != null) && ((advertise?.advertiseImage ?? "").isNotEmpty);
    return Container(
      color: Colors.white,
      // child: Padding(
      // padding: const EdgeInsets.only(bottom: 3.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: <Widget>[
          SizedBox(width: 20,height: 10,),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              GestureDetector(
                child:
                Container(
                  // color: Colors.red,
                  child: Image.asset(
                    'assets/images/profile_page/my_customer_service.png',
                    width: 25,
                    fit:BoxFit.fitHeight,
                  ),
                  margin:EdgeInsets.only(left: 10) ,

                ),
                onTap: (){
                  String url = WebViewURLTool.kefuURLStrWithGroup(KefuGroup.mm.value, '', '');
                  JumpTool().openWeb(context, url, true);
                  // LoadingManager.showToast('页面尚未完成 敬请期待');
                },
              ),
              SizedBox(height: 5,width: 10,),
              Container(
                margin:EdgeInsets.only(left: 10,bottom: 20),
                child: GestureDetector(
                  child: Text(
                    '客服',
                  ),
                  onTap: (){
                    String url = WebViewURLTool.kefuURLStrWithGroup(KefuGroup.mm.value, '', '');
                    JumpTool().openWeb(context, url, true);
                    // LoadingManager.showToast('页面尚未完成 敬请期待');
                  },
                ),
              ),
            ],
          ),

          Expanded(child:

          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Container(
                width: 110,
                height: 40,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(30.0)),
                  color: Colors.yellow,
                ),
                margin:EdgeInsets.only(left: 30,bottom: 20) ,
                child: TextButton(
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.white, // 设置按钮前景颜色为蓝色
                    ),
                    child: const Text('联系我试驾'),
                    onPressed: () {
                      if(!GlobalData().isLogin){
                        showNotLoginAlertDialog(context);
                        return;
                      }
                      String url = WebViewURLTool.newAskPriceOrAppointmentDriveWithPageAskPriceOrAppointmentDrive(PageType.testDrive.value, 0, advertise!.commodityId);
                      JumpTool().openWeb(context, url, false);
                    }
                ),
              ),
              SizedBox(width: 10,),
              Container(
                width: 110,
                height: 40,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(30.0)),
                  color: Colors.red,
                ),
                margin:EdgeInsets.only(right: 20,bottom: 20) ,
                // color: Colors.red,
                child: TextButton(
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('立即购买'),
                    onPressed: onBuy,
                ),
              ),
            ],
          )
          )
        ],
      ),
      // ),
      // ),
    );

  }

  void showNotLoginAlertDialog(BuildContext context) {
    DialogManager().showCustomDialog(
      CustomDialog(
        content: "您还没有登录",
        contentFontSize: 18,
        contentColor: Colors.black,
        buttonHeight: 70,
        horizontalPadding: 20,
        buttons: [
          DialogButton(
              label: "继续逛逛",
              onPressed: () {
                LogManager().debug("取消按钮被点击");
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xFF9B9DA9)),
          DialogButton(
              label: "去登录",
              onPressed: () {
                LoginManager().showLoginModal();
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xffea0029)),
        ],
      ),
    );
  }
}

