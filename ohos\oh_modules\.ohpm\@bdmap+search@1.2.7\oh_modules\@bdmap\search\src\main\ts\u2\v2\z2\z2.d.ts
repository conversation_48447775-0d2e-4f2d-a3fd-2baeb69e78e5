import { GeoCodeOption } from "../../../d/u1/i1/w1";
import { ReverseGeoCodeOption } from "../../../d/u1/i1/z1";
import { GeoCodeResult } from "../../../d/u1/n1/x1";
import { ReverseGeoCodeResult } from "../../../d/u1/n1/y1";
import { BaseSearch } from '../../base/base';
/**
 * 地理编码查询接口
 */
export interface IGeoCoder {
    geocode(option: GeoCodeOption): Promise<GeoCodeResult>;
    reverseGeoCode(option: ReverseGeoCodeOption): Promise<ReverseGeoCodeResult>;
}
export declare class GeoCoderImp extends BaseSearch implements IGeoCoder {
    geocode(z10: GeoCodeOption): Promise<GeoCodeResult>;
    reverseGeoCode(w10: ReverseGeoCodeOption): Promise<ReverseGeoCodeResult>;
}
