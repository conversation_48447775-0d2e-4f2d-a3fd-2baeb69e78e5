import { IPlayer } from './IPlayer';
export interface IListPlayer extends IPlayer {
    /**
     * 根据uid移除播放源
     * @param uid
     */
    /****
     * Remove source by uid
     * @param uid
     */
    removeSource: (uid: string) => void;
    /**
     * 清除播放源
     */
    /****
     * Clear all source
     */
    clear: () => void;
    /**
     * 获取当前播放的uid
     * @return
     */
    /****
     * get current playing uid.
     * @return
     */
    getCurrentUid: () => string;
    /**
     * 设置预加载个数。
     * @param count  预加载个数。比如设置为3，则预加载3*2 + 1。最大为5。
     */
    /****
     * Set the number of preloads.
     * @param count the number of preloads. If 3, preload 3*2 + 1. The maximum value is 5.
     */
    setPreloadCount: (count: number) => void;
    /**
     * 设置两个方向上的预加载个数
     * @param prevCount 前面url的预加载个数
     * @param nextCount 后面url的预加载个数
     * 总的预加载个数为prevCount + nextCount
     */
    /****
     * Set the number of preloads in two directions.
     * @param prevCount the number of preloads in previous url.
     * @param nextCount the number of preloads in previous url.
     * total preload count is prevCount + nextCount
     */
    setPreloadCountWithPrevAndNext: (prevCount: number, nextCount: number) => void;
    /**
     * 设置预加载场景。
     * @param type 场景类型
     */
    /****
     * Set preload scene.
     * @param type scene type
     */
    setPreloadScene: (type: SceneType) => void;
    /**
     * 启用预加载策略。
     * @param type 策略类型
     * @param enable 是否启用
     */
    /****
     * enable preload strategy.
     * @param type strategy type
     * @param enable enable strategy
     */
    enablePreloadStrategy: (type: StrategyType, enable: boolean) => void;
    /**
     * 设置策略参数。
     * @param type 策略类型
     * @param strategyParam 策略参数，格式为json字符串
     *                      支持参数：algorithm, offset, scale
     *                      配置参数举例：
     *                      动态预加载时长递减策略
     *                      默认配置
     *                      {
     *                         "algorithm":"sub",
     *                         "offset":"500"
     *                      }
     *                      转换成字符串为 "{\"algorithm\": \"div\",\"scale\": \"0.75\"}"
     *                      或者
     *                      {
     *                          "algorithm":"div",
     *                          "scale":"0.75"
     *                      }
     */
    /****
     * set strategy parameter.
     * @param type strategy type
     * @param strategyParam strategy parameter, type is json string
     *                      support param: algorithm, offset, scale
     *                      for example:
     *                      dynamic preload duration decrease strategy
     *                      default strategy
     *                      {
     *                         "algorithm":"sub",
     *                         "offset":"500"
     *                      }
     *                      transfer to string: "{\"algorithm\": \"div\",\"scale\": \"0.75\"}"
     *                      or
     *                      {
     *                          "algorithm":"div",
     *                          "scale":"0.75"
     *                      }
     */
    setPreloadStrategy: (type: StrategyType, strategyParam: string) => void;
    /**
     * 设置预加载的最大内存大小。
     * @param size
     */
    /****
     * Set the preload maximum memory size.
     * @param size
     */
    setMaxPreloadMemorySizeMB: (size: number) => void;
    /**
     * 获取预加载的最大内存大小。
     * @return
     */
    /****
     * Get the preload maximum memory size.
     * @return
     */
    /**
     * 设置多码率列表播放预加载模式
     * @param mode
     */
    /****
     * Set the multi-rate list playback preload mode.
     * @param size
     */
    SetMultiBitratesMode: (mode: MultiBitratesMode) => void;
    /**
     * 获取多码率列表播放预加载模式
     * @return
     */
    /****
     * Get the multi-rate list playback preloading mode
     * @return
     */
    GetMultiBitratesMode: () => MultiBitratesMode;
}
/**
 * 场景类型
 */
/****
 * scene type
 */
export declare enum SceneType {
    /**
     * 场景：无
     */
    /****
     * scene none
     */
    SCENE_NONE = -1,
    /**
     * 超短视频场景：适用于30s以下
     */
    /****
     * very short scene: apply to less 30s
     */
    SCENE_VERY_SHORT = 0,
    /**
     * 短视频场景：适用于30s-5min
     */
    /****
     * short scene: apply to 30-5min
     */
    SCENE_SHORT = 1,
    /**
     * 中视频场景：适用于5min-30min
     */
    /****
     * middle scene: apply to 5min-30min
     */
    SCENE_MIDDLE = 2,
    /**
     * 长视频场景：适用于30min以上
     */
    /****
     * long scene: apply to more than 30min
     */
    SCENE_LONG = 3
}
/**
 * 策略类型
 */
/****
 * strategy type
 */
export declare enum StrategyType {
    /**
     * 动态预加载时长
     */
    /****
     * dynamic preload duration.
     */
    STRATEGY_DYNAMIC_PRELOAD_DURATION = -1
}
/**
 * 多码率预加载类型，只对多码率HLS流生效
 */
/****
 * multiBitrates preload mode, effect only on multiBitrates hls stream.
 */
export declare enum MultiBitratesMode {
    /**
     * 默认配置，播放和预加载默认码率
     */
    /****
     * default mode, play and preload default bitrate of a stream
     */
    MultiBitratesMode_Default = 0,
    /**
     * 首帧优先配置，起播视频默认播放已完成预加载的码率
     */
    /****
     * First frame cost (FC) priority, decrease first frame cost. only play bitrate of the hls stream which has been preloaded.
     */
    MultiBitratesMode_FCPrio = 1,
    /**
     * 兼顾首帧和播放平滑，切换前后（moveToNext）的视频码率一致，且兼顾首帧性能
     */
    /****
     * First frame and play smooth, play the same bitrate before and after moveToNext
     */
    MultiBitratesMode_FC_AND_SMOOTH = 2,
    /**
     * 播放平滑优先配置，起播视频默认播放前一个视频的码率
     */
    /****
     * Play Smooth priority, play the same bitrate before and after moveToNext.
     */
    MultiBitratesMode_SmoothPrio = 3
}
