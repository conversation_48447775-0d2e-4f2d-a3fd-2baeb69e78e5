{"version": "2.0", "ppid": 15572, "events": [{"head": {"id": "890d2425-518a-4c19-9b0a-7129c44020d7", "name": "env: nodejsVersion=v18.20.1", "description": "", "type": "log"}, "body": {"pid": 12772, "tid": "Main Thread", "startTime": 7737254281900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc29b390-81cc-44a4-a97e-91aaff6c64a3", "name": "env: hvigor-config.json5 content = {\n  modelVersion: '5.0.0',\n  dependencies: {},\n  execution: { daemon: true, incremental: true, parallel: true },\n  logging: {},\n  debugging: { stacktrace: true },\n  properties: { 'ohos.nativeResolver': false },\n  nodeOptions: {}\n}", "description": "", "type": "log"}, "body": {"pid": 12772, "tid": "Main Thread", "startTime": 7737264480700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82dde683-260c-4fc6-80a5-e217886cf09b", "name": "env: daemon=false", "description": "", "type": "log"}, "body": {"pid": 12772, "tid": "Main Thread", "startTime": 7737270125400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6f89f5d-8b28-4ee1-99ab-c9e88b7e8b96", "name": "no-daemon, use the parent process.execArgv --max-old-space-size=8192,--max-semi-space-size=16,--expose-gc", "description": "", "type": "log"}, "body": {"pid": 12772, "tid": "Main Thread", "startTime": 7737270301500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea0039da-3552-4e92-adde-291a58367262", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 12772, "tid": "Main Thread", "startTime": 7738862346600}, "additional": {"children": ["a3df9e91-bec4-4f96-bbef-705f824a5b08", "d4edd15b-f78f-408f-bdcb-73d831700dba", "bb03b223-7cbc-428c-a139-ccc4ec303fe8", "265c17fc-7dfc-420d-a44b-b8e325b32d8c"], "state": "running", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": []}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a3df9e91-bec4-4f96-bbef-705f824a5b08", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 12772, "tid": "Main Thread", "startTime": 7738862372600}, "additional": {"children": [], "state": "running", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ea0039da-3552-4e92-adde-291a58367262"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4daefbe7-151f-42ef-a3cb-777713f22701", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 12772, "tid": "Main Thread", "startTime": 7738884475000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5d6ea6a-854c-4a41-a4a2-9f86d0059dbf", "name": "\u001b[31m00303149 Configuration Error\r\nError Message: Path not found. At file: D:\\pub\\git\\flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0\\ohos\r\n\r\n* Try the following:\r\n  > Please check field: modules in file: D:\\wulingFlutter\\wuling-flutter-app\\ohos\\build-profile.json5.\r\n\u001b[39m", "description": "", "type": "log"}, "body": {"pid": 12772, "tid": "Main Thread", "startTime": 7738885201000}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "53429a0f-e5bb-4b03-910c-4b4910e281ac", "name": "AdaptorError: \u001b[31m00303149 Configuration Error\r\nError Message: Path not found. At file: D:\\pub\\git\\flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0\\ohos\r\n\r\n* Try the following:\r\n  > Please check field: modules in file: D:\\wulingFlutter\\wuling-flutter-app\\ohos\\build-profile.json5.\r\n\u001b[39m\n    at HvigorLogger.printErrorExit (D:\\Huawei\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\log\\hvigor-log.js:1:4794)\n    at exitIfNotExists (D:\\Huawei\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\util\\file-util.js:1:1253)\n    at D:\\Huawei\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\external\\core\\hvigor-config.js:1:1742\n    at Array.forEach (<anonymous>)\n    at new HvigorConfig (D:\\Huawei\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\external\\core\\hvigor-config.js:1:1529)\n    at hvigorConfigInit (D:\\Huawei\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\external\\core\\hvigor-config.js:1:3716)\n    at init (D:\\Huawei\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\internal\\lifecycle\\init.js:1:2867)\n    at start (D:\\Huawei\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\boot\\index.js:1:2518)\n    at boot (D:\\Huawei\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\base\\boot\\index.js:1:1818)\n    at startHvigorBuild (D:\\Huawei\\DevEco Studio\\tools\\hvigor\\hvigor\\src\\cli\\main\\cli.js:1:7509)", "description": "", "type": "log"}, "body": {"pid": 12772, "tid": "Main Thread", "startTime": 7738892517400}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "0909d9f2-4d50-45d6-ac2a-833835a10cd5", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 12772, "tid": "Main Thread", "startTime": 7738913629000, "endTime": 7738914177700}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cd767f61-1217-4836-b45b-4812ddad65f5", "logId": "66fcd415-0c50-41bc-bc93-48e7b17871fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "66fcd415-0c50-41bc-bc93-48e7b17871fe", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 12772, "tid": "Main Thread", "startTime": 7738913629000, "endTime": 7738914177700}, "additional": {"logType": "info", "children": [], "durationId": "0909d9f2-4d50-45d6-ac2a-833835a10cd5"}}, {"head": {"id": "0433d117-80bc-46b6-8ef8-448f8df4787c", "name": "assembleHar", "description": "", "type": "mark"}, "body": {"pid": 12772, "tid": "Main Thread", "startTime": 7738843286300, "endTime": 7738919512300}, "additional": {"time": {"year": 2025, "month": 7, "day": 28, "hour": 15, "minute": 51, "second": 34}, "completeCommand": "{\"prop\":[\"module=camera_ohos@default,flutter_blue_plus@default,iamgeqr_flutter_plugin@default,image_picker_ohos@default,path_provider_ohos@default,shared_preferences_ohos@default,url_launcher_ohos@default,webview_flutter_ohos@default,permission_handler_ohos@default,package_info_plus@default,connectivity_plus@default,device_info_plus@default,mobile_scanner@default,open_app_settings@default,fluwx@default\",\"product=default\"],\"mode\":\"module\",\"daemon\":false,\"_\":[\"assembleHar\"]};--mode module -p module=camera_ohos@default,flutter_blue_plus@default,iamgeqr_flutter_plugin@default,image_picker_ohos@default,path_provider_ohos@default,shared_preferences_ohos@default,url_launcher_ohos@default,webview_flutter_ohos@default,permission_handler_ohos@default,package_info_plus@default,connectivity_plus@default,device_info_plus@default,mobile_scanner@default,open_app_settings@default,fluwx@default -p product=default assembleHar --no-daemon", "hvigorVersion": "5.19.0", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "488acf20-1d41-4991-a824-df4165228ecb", "name": "BUILD FAILED in 72 ms ", "description": "", "type": "log"}, "body": {"pid": 12772, "tid": "Main Thread", "startTime": 7738920071400}, "additional": {"logType": "error", "children": []}}]}