import { WeatherResult } from "../../../d/u/b1";
import { WeatherSearchOption } from "../../../d/u/e1";
import { BaseSearch } from '../../base/base';
export interface IWeatherSearch {
    searchWeather(option: WeatherSearchOption): Promise<WeatherResult>;
}
export declare class WeatherSearchImp extends BaseSearch implements IWeatherSearch {
    searchWeather(a35: WeatherSearchOption): Promise<WeatherResult>;
}
