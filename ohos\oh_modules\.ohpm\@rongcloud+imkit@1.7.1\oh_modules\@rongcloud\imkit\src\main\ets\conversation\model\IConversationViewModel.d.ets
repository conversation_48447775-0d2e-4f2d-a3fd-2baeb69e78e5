// @keepTs
// @ts-nocheck
import { Message, UiMessage } from "../../../../../Index";
/**
 * ConversationViewModel的能力接口
 * @since 1.6.0
 */
export interface IConversationViewModel {
    /**
     * 进入会话时展示的未读消息的组件点击事件
     */
    onClickUnreadMessageButton(): void;
    /**
     * 进入会话时展示的未读@我的消息的组件点击事件
     */
    onClickUnreadMentionedMessageButton(): void;
    /**
     * 在进入会话后收到未读新消息的组件点击事件
     */
    onClickNewReceivedUnreadMessageButton(): void;
    /**
     * 更新输入框的文本组件的内容
     * @since 1.7.0
     */
    onChangeInputTextAreaContent?(text: string): void;
    /**
     * 获取输入框当前的文本输入组件的内容
     * @since 1.7.0
     */
    getInputTextAreaContent?(): string;
    /**
     * 获取消息列表
     *
     * 注意：改返回列表仅用于读取，不建议修改。
     *
     * @returns 消息列表
     * @since 1.7.1
     */
    getMessageList?(): UiMessage[];
    /**
     * 刷新某条消息
     * @since 1.7.1
     */
    refreshUiMessage?(message: Message): void;
}
