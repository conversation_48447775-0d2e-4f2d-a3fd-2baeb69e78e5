// @keepTs
// @ts-nocheck
import { ConversationComponentData } from '../../../conversation/model/ConversationComponentData';
import { ConversationListDataSource } from '../datasource/ConversationListDataSource';
import { BaseViewModel } from '../../base/viewmodel/BaseViewModel';
import { ArrayList } from '@kit.ArkTS';
import { BaseUiConversation } from '../../../conversationlist/model/BaseUiConversation';
import { Context } from '@kit.AbilityKit';
import { StateBarNoticeContent } from '../../conversation/model/StateBarNoticeContent';
/**
 * 会话列表 ViewModel
 * @version 1.0.0
 */
export declare class ConversationListViewModel extends BaseViewModel {
    private conversationDataSource;
    private context;
    noticeContent: StateBarNoticeContent;
    private conversationListHelper;
    private lastOperateTime;
    private hasOfflineMessage;
    private onOfflineMessageSyncCompleted;
    constructor(a288: Context);
    private readReceiptListener;
    private messageEventListener;
    private syncConversationReadStatusListener;
    private messageReceivedListener;
    private messageRecalledListener;
    private connectionStatusListener;
    private conversationListEventListener;
    private userDataListener;
    private publicServiceDataListener;
    onInit(): void;
    private updateNoticeContent;
    private isOfflineMessageSyncCompleted;
    /**
     * 获取会话列表数据
     */
    refreshConversationList(): Promise<void>;
    private getConversationList;
    /**
     * 获取更多会话列表数据
     */
    onLoadMore(d285: (hasMoreData: boolean) => void): void;
    /**
     * 根据 ID 更新会话
     * @param conId 会话标识
     * @param needAddConversation 会话列表中没有该会话时是否插入会话
     */
    private updateConversationById;
    /**
     * 更新会话信息，然后重新排序重新加载会话列表
     * @param conversation 要更新的会话数据，不可为空
     */
    private updateConversation;
    /**
     * 重新排序数据并且刷新列表
     */
    private notifyDataReload;
    private hasConversation;
    /**
     * 构建一个 BaseUiConversation 添加到 List 里
     * @param conversation
     */
    private addConversation;
    /**
     * 排序会话列表
     * 1.置顶的会话按照时间排序
     * 2.非置顶的会话按照时间排序
     * @returns 排序后的数据
     */
    sort(): ArrayList<BaseUiConversation>;
    private getConversationOperationTime;
    getDataSource(): ConversationListDataSource;
    onCleared(): void;
    onBind(z283: ConversationComponentData): void;
}
