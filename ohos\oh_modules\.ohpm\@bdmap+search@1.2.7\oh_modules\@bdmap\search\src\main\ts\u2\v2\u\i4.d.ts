import { SearchResult } from "../../../d/e/h/f1";
import { SearchParser } from '../../base/base';
export declare class WeatherParser extends SearchParser {
    /**
     * 获取解析结果，由子类实现
     *
     * @param json
     * @return SearchResult
     */
    parseSearchResult(n34: string): SearchResult;
    private parseWeatherResult;
    private parseLocation;
    private setWeatherSearchLocation;
    private parseNow;
    private setRealTimeWeather;
    private parseForecasts;
    private parseAlert;
    private parseIndexs;
    private parseForecastForHours;
}
