//
//  Generated code. Do not modify.
//  source: sgmw_app_control_result.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

/// 车辆状态，车联网后端给APP的消息
class SgmwAppControlResult extends $pb.GeneratedMessage {
  factory SgmwAppControlResult({
    $core.String? code,
    $core.String? message,
    $core.String? serviceCode,
    $fixnum.Int64? collectTime,
  }) {
    final $result = create();
    if (code != null) {
      $result.code = code;
    }
    if (message != null) {
      $result.message = message;
    }
    if (serviceCode != null) {
      $result.serviceCode = serviceCode;
    }
    if (collectTime != null) {
      $result.collectTime = collectTime;
    }
    return $result;
  }
  SgmwAppControlResult._() : super();
  factory SgmwAppControlResult.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SgmwAppControlResult.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'SgmwAppControlResult', createEmptyInstance: create)
    ..aOS(1, _omitFieldNames ? '' : 'code')
    ..aOS(2, _omitFieldNames ? '' : 'message')
    ..aOS(3, _omitFieldNames ? '' : 'serviceCode', protoName: 'serviceCode')
    ..aInt64(4, _omitFieldNames ? '' : 'collectTime', protoName: 'collectTime')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SgmwAppControlResult clone() => SgmwAppControlResult()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SgmwAppControlResult copyWith(void Function(SgmwAppControlResult) updates) => super.copyWith((message) => updates(message as SgmwAppControlResult)) as SgmwAppControlResult;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static SgmwAppControlResult create() => SgmwAppControlResult._();
  SgmwAppControlResult createEmptyInstance() => create();
  static $pb.PbList<SgmwAppControlResult> createRepeated() => $pb.PbList<SgmwAppControlResult>();
  @$core.pragma('dart2js:noInline')
  static SgmwAppControlResult getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SgmwAppControlResult>(create);
  static SgmwAppControlResult? _defaultInstance;

  @$pb.TagNumber(1)
  $core.String get code => $_getSZ(0);
  @$pb.TagNumber(1)
  set code($core.String v) { $_setString(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCode() => $_has(0);
  @$pb.TagNumber(1)
  void clearCode() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get message => $_getSZ(1);
  @$pb.TagNumber(2)
  set message($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasMessage() => $_has(1);
  @$pb.TagNumber(2)
  void clearMessage() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get serviceCode => $_getSZ(2);
  @$pb.TagNumber(3)
  set serviceCode($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasServiceCode() => $_has(2);
  @$pb.TagNumber(3)
  void clearServiceCode() => clearField(3);

  @$pb.TagNumber(4)
  $fixnum.Int64 get collectTime => $_getI64(3);
  @$pb.TagNumber(4)
  set collectTime($fixnum.Int64 v) { $_setInt64(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasCollectTime() => $_has(3);
  @$pb.TagNumber(4)
  void clearCollectTime() => clearField(4);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
