import image from '@ohos.multimedia.image';
import { CameraCaptureProperties } from '../types/CameraCaptureProperties';
import ArrayList from "@ohos.util.ArrayList";
import HashMap from "@ohos.util.HashMap";
import { Size as Size } from "@ohos.arkui.node";
import { EventSink } from '@ohos/flutter_ohos/src/main/ets/plugin/common/EventChannel';
export declare class ImageStreamReader {
    private readonly dartImageFormat;
    private readonly imageReceiver;
    private readonly imageStreamReaderUtils;
    constructor(size: Size, imageFormat: number, maxImages: number);
    onImageAvailable(_image: image.Image, imgComponent: image.Component, captureProps: CameraCaptureProperties, imageStreamSink: EventSink): Promise<void>;
    parsePlanes(_image: image.Image, imgComponent: image.Component): ArrayList<HashMap<string, object>>;
    getReceivingSurfaceId(): Promise<string>;
    subscribeListener(captureProps: CameraCaptureProperties, imageStreamSink: EventSink): Promise<void>;
    removeListener(): void;
    close(): void;
}
