export declare class PlayerConfig {
    /**
     * http代理。例如："http://ip:port"
     */
    /****
     * HTTP proxy. Example: "http://ip:port".
     */
    mHttpProxy: string;
    /**
     * referrer
     */
    /****
     * Referer.
     */
    mReferrer: string;
    /**
     * 网络超时时间。单位ms。
     */
    /****
     * Network connection timeout time. Unit: milliseconds.
     */
    mNetworkTimeout: number;
    /**
     * 最大延迟。注意：直播有效。单位ms。
     */
    /****
     * Maximum delay time. Note: This parameter is only valid for broadcasting. Unit: milliseconds.
     */
    mMaxDelayTime: number;
    /**
     * 最大缓冲区时长。单位ms
     */
    /****
     * Maximum buffer size. Unit: milliseconds.
     */
    mMaxBufferDuration: number;
    /**
     * 高缓冲时长。单位ms
     */
    /****
     * Maximum buffer time. Unit: milliseconds.
     */
    mHighBufferDuration: number;
    /**
     * 起播缓冲区时长。单位ms。
     */
    /****
     * The size of the data that the player must buffer before it can start playing the media. Unit: milliseconds.
     */
    mStartBufferDuration: number;
    /**
     * 最大probe大小。单位 byte 。 -1：默认。
     */
    /****
     * Maximum probe size. Unit: bytes. Default: -1.
     */
    mMaxProbeSize: number;
    /**
     * 停止后是否清空画面。默认为false。
     */
    /****
     * Indicate whether to clear the player surface when playback is stopped. Default: false.
     */
    mClearFrameWhenStop: boolean;
    /**
     * 是否启用TunnelRender
     */
    /****
     * Indicate whether TunnelRender is enabled.
     */
    mEnableVideoTunnelRender: boolean;
    /**
     * 是否启用MediaCodec Buffer Render
     */
    /****
     * Indicate whether MediaCodec Buffer Render is enabled.
     */
    mEnableVideoBufferRender: boolean;
    /**
     * 是否启用SEI
     */
    /****
     * Indicate whether SEI is enabled.
     */
    mEnableSEI: boolean;
    /**
     * 设置请求的ua
     */
    /****
     * Set a request UserAgent.
     */
    mUserAgent: string;
    /**
     * 网络重试次数，每次间隔networkTimeout，networkRetryCount=0则表示不重试，重试策略app决定，默认值为2
     */
    /****
     * The maximum network reconnection attempts. networkTimeout specifies the reconnection interval. networkRetryCount=0 indicates that automatic reconnection is disabled. The reconnection policy depends on the app. Default: 2.
     */
    mNetworkRetryCount: number;
    /**
     * HLS直播时，起播分片位置。
     */
    /****
     * The start playing index of fragments, when HLS is live.
     */
    mLiveStartIndex: number;
    /**
     * 禁用Audio。
     */
    /****
     * Disable audio track.
     */
    mDisableAudio: boolean;
    /**
     * 禁用Video。
     */
    /****
     * Disable video track.
     */
    mDisableVideo: boolean;
    /**
     * 进度跟新的频率。包括当前位置和缓冲位置。
     */
    /****
     * Set the frequencies of Progress. Includes the current position and the
     * buffer position.
     */
    mPositionTimerIntervalMs: number;
    /**
     * 往前缓存的最大buffer时长。
     */
    /****
     * max backward buffer duration.
     */
    mMaxBackwardBufferDurationMs: number;
    /**
     * 优先保证音频播放；在网络带宽不足的情况下，优先保障音频的播放，目前只在dash直播流中有效（视频已经切换到了最低码率）
     */
    /****
     * prefer audio playback; prefer audio playback when under insufficient network bandwidth. At present, it is only effective in dash live stream (the video has been switched to the lowest bit rate)
     */
    mPreferAudio: boolean;
    /**
     * 当通过GlobalSettings API打开本地缓存功能后，此处可设置当前播放器实例是否允许被缓存，默认允许。
     */
    /****
     * When local cached is enabled in GlobalSettings API, this config can be used to enable or disable local cache in
     * current player instance, by default is ON.
     */
    mEnableLocalCache: boolean;
    /**
     * 播放器实例是否可以使用http dns进行解析，-1 表示跟随全局设置，0 禁用
     */
    /****
     * whether enable http dns, -1 : as global setting, 0: disable
     */
    mEnableHttpDns: number;
    /**
     * 播放器实例是否可以使用增强型http dns进行解析，-1 表示跟随全局设置，0 禁用
     */
    /****
     * whether enable enhanced http dns, -1 : as global setting, 0: disable
     */
    mEnableEnhancedHttpDns: number;
    /**
     * 使用http3进行请求，支持标准：RFC 9114（HTTP3）和RFC 9000（QUIC v1），默认值关
     * 如果http3请求失败，自动降级至普通http
     */
    /****
     * Use http3 to request, supported standards:RFC 9114(HTTP3) and RFC 9000(QUIC v1), false by default
     * If request failed, it will automatically downgrade to normal http.
     */
    mEnableHttp3: boolean;
    /**
     * 用于纯音频或纯视频的RTMP/FLV直播流起播优化策略，当流的header声明只有音频或只有视频时，且实际流的内容跟header声明一致时，此选项打开可以达到快速起播的效果
     * 默认值关
     */
    /****
     * Used to fast stream start when playing pure audio or pure video in RTMP/FLV live stream. If live stream header
     * states only audio or only video and the stream content really contains the same single stream, enable this option
     * can fast start the stream.
     * Default value is false
     */
    mEnableStrictFlvHeader: boolean;
    /**
     * 给特定支持的芯片设置（如海思）硬解码low-latency模式，以降低解码延时，最终降低端到端延时
     * 默认值开
     */
    /****
     * Used to set specific chip low-latency mode on its hardware decoder, to lower decoder latency and finally lower client-to-client latency.
     * Default is true.
     */
    mEnableLowLatencyMode: boolean;
    /**
     * 允许当前播放器实例进行投屏
     * 你需要集成投屏SDK来完成投屏功能
     * 默认值关
     */
    /****
     * Enable projection for current player
     * You need to integrate projection SDK to do this
     * Default is false.
     */
    mEnableProjection: boolean;
    /**
     * 针对打开了点播URL鉴权的媒体资源（HLS协议），开启本地缓存后，可选择不同的鉴权模式：
     * 非严格鉴权(false)：鉴权也缓存，若上一次只缓存了部分媒体，下次播放至非缓存部分时，播放器会用缓存的鉴权发起请求，如果URL鉴权设置的有效很短的话，会导致播放异常。
     * 严格鉴权(true)：鉴权不缓存，每次起播都进行鉴权，无网络下会导致起播失败。
     * 默认值：true
     */
    /****
     * For media that enabled URL authorization(HLS protocol), when local cache is enabled, we can choose different auth mode:
     * Non Strict Mode(false): Auth is cached. If last play cached part of the media, and next play to non-cache part,
     *                         player will use old auth to request, which may fail if the auth timeout configuration in the server is very short.
     * Strict Mode(true): Auth is not cached. Every play will do the auth verification, so when there is no network, media cannot be played.
     * Default is true.
     */
    mEnableStrictAuthMode: boolean;
    /**
     * 开始预加载/下载的阈值。单位ms
     */
    /****
     * start preload or download limit. Unit: milliseconds.
     */
    mStartBufferLimit: number;
    /**
     * 停止预加载/下载阈值。单位ms
     */
    /****
     * stop preload or download limit. Unit: milliseconds.
     */
    mStopBufferLimit: number;
    /**
     * 调用SelectTrack是否清除buffer，无论是高清晰度还是低清晰度， 0 不清除，1 清除
     */
    /****
     * Whether to clear the buffer when calling SelectTrack, no matter it is high-definition or low-definition, 0 is not cleared, and 1 is cleared.
     */
    mSelectTrackBufferMode: number;
    /**
     * 清晰度切换至AUTO档位时，允许升路的清晰度视频对应的最大像素数量。
     * 例如将值设置为1280 * 720 = 921600，那么最高升路到该对应清晰度，而不会升路到 1920 * 1080
     * 不同清晰度对应值可以参考 PixelNumber
     */
    /****
     * When switching the resolution to AUTO mode, the maximum pixel count corresponding to the allowable resolution upgrade for the video.
     * for example, if the value is set to 1280 * 720 = 921600, then the highest resolution upgrade corresponds to this resolution, and it will not upgrade to 1920 * 1080.
     * Can refer to PixelNumber
     */
    mMaxAllowedAbrVideoPixelNumber: number;
    /**
     * 设置音频打断类型
     * 设置为0：设置为共享焦点模式（SHARE_MODE），同一应用创建的多个音频流，音频打断策略不会介入。
     * 设置为1：设置为独立焦点模式（INDEPENDENT_MODE）：同一应用创建的每一个音频流均会独立拥有一个音频焦点，当多个音频流并发播放时，会触发音频打断策略的管控。
     * 默认设置为0，即共享焦点模式。
     */
    /**
     * Sets the audio interruption type.
     *
     * Set to 0: Set to shared focus mode (SHARE_MODE),
     * where multiple audio streams created by the same application will not be interrupted by audio interruption policies.
     *
     * Set to 1: Set to independent focus mode (INDEPENDENT_MODE),
     * where each audio stream created by the same application will have its own independent audio focus.
     * When multiple audio streams are played concurrently, audio interruption policies will be enforced.
     *
     * The default setting is 0, which is the shared focus mode.
     */
    mAudioInterruptMode: number;
    private mCustomHeaders;
    /**
     * 获取用户自定义header
     * @return
     */
    /****
     * Get custom set headers.
     * @return
     */
    getCustomHeaders(): string[] | undefined;
    getCustomHeadersArray(): Array<Record<string, string>>;
    /**
     * 设置用户自定义header
     * @param headers
     */
    /****
     * Set custom headers
     * @param headers
     */
    setCustomHeaders(i31: string[]): void;
    private nativeGetHttpProxy;
    private nativeGetReferrer;
    private nativeGetNetworkTimeout;
    private nativeGetMaxDelayTime;
    private nativeGetMaxBufferDuration;
    private nativeGetHighBufferDuration;
    private nativeGetStartBufferDuration;
    private nativeGetMaxProbeSize;
    private nativeGetClearFrameWhenStop;
    private nativeGetEnableVideoTunnelRender;
    private nativeGetEnableVideoBufferRender;
    private nativeGetEnableSEI;
    private nativeGetUserAgent;
    private nativeGetNetworkRetryCount;
    private nativeGetLiveStartIndex;
    private nativeGetDisableAudio;
    private nativeGetDisableVideo;
    private nativeGetPositionTimerIntervalMs;
    private nativeGetMaxBackwardBufferDurationMs;
    private nativeGetPreferAudio;
    private nativeGetEnableLocalCache;
    private nativeGetEnableHttpDns;
    private nativeGetEnableHttp3;
    private nativeGetEnableStrictFlvHeader;
    private nativeGetEnableLowLatencyMode;
    private nativeGetEnableProjection;
    private nativeGetEnableStrictAuthMode;
    private nativeGetStartBufferLimit;
    private nativeGetStopBufferLimit;
    private nativeGetAudioInterruptMode;
    private nativeGetCustomHeaders;
    private nativeGetSelectTrackBufferMode;
    private nativeGetMaxAllowedAbrVideoPixelNumber;
    private nativeGetEnableEnhancedHttpDns;
    private nativeGetCustomHeadersNumber;
    private nativeGetCustomHeader;
}
export declare enum PixelNumber {
    Resolution_360P = 172800,
    Resolution_480P = 345600,
    Resolution_540P = 518400,
    Resolution_720P = 921600,
    Resolution_1080P = 2073600,
    Resolution_2K = 3686400,
    Resolution_4K = 8847360,
    Resolution_NoLimit = 2147483647
}
