// @keepTs
// @ts-nocheck
import notificationManager from '@ohos.notificationManager';
import common from '@ohos.app.ability.common';
import { WantAgent } from '@ohos.wantAgent';
/**
 * 通知工具类
 * Created on 2024/07/11
 * <AUTHOR>
 */
export declare class NotificationUtil {
    /**
     * 通知渠道类型：社交通讯渠道
     */
    static slotType: notificationManager.SlotType;
    /**
     * 检查当前是否存在通知权限
     * @returns Promise<boolean>
     */
    static checkNotificationEnabled(): Promise<boolean>;
    /**
     * 请求用户授权通知权限
     * @param uiAbilityContext
     * @returns Promise<boolean>
     */
    static requestEnableNotification(l326: common.UIAbilityContext): Promise<boolean>;
    /**
     * 设置当前应用角标数量
     * @param badgeNumber num
     * @returns Promise<boolean>
     */
    static setBadgeNum(e326: number): Promise<boolean>;
    /**
     * 发布通知
     * @param notificationId 通知 ID ，如果为消息场景应该为 messageId
     * @param title 标题
     * @param content 通知内容
     * @param groupName 分组标识
     * @param wantAgent 点击通知的意图
     * @returns Promise<boolean> 通知是否发送成功
     */
    static publishNotification(v325: number, w325: string, x325: string, y325: string, z325?: WantAgent): Promise<boolean>;
    /**
     * 取消已经发布的通知
     * @param notificationId 通知 ID ，如果为消息场景应该为 messageId
     * @param label label
     * @returns Promise<boolean>
     */
    private static cancelNotification;
}
