{"license": "Apache-2.0", "author": "", "name": "@ohos/flutter_ohos", "description": "The embedder of flutter in ohos.", "main": "index.ets", "version": "1.0.0-a9e521ff88", "dependencies": {}, "devDependencies": {"@types/libflutter.so": "file:./src/main/cpp/types/libflutter"}, "metadata": {"workers": ["./src/main/ets/embedding/engine/workers/PlatformChannelWorker.ets"], "sourceRoots": ["./src/main"], "debug": true, "nativeDebugSymbol": false}, "compatibleSdkVersion": 12, "compatibleSdkType": "HarmonyOS", "obfuscated": false}