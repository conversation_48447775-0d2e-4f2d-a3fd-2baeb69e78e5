import { BmVisibility } from "../d2"; import BmObject from "../u2"; import type BmDrawableResource from "../f2/l3"; import type Base<PERSON> from "../../n1/q1"; import BmGroup<PERSON> from "./b4"; export default class BmBaseUI extends BmObject {         static readonly MATCH_PARENT: number;           static readonly WRAP_CONTENT = -2; mVisibility: BmVisibility; mClickAction: string; mDescription: string; mHasBackgroundResource: boolean; mHasBackgroundColor: boolean; owner: BaseUI; constructor(y26?: number, z26?: number); setOwner(owner: BaseUI): void;           findViewByName(name: string): this;           findViewByShell(x26: number): BmGroupUI | BmBaseUI | null;           setBackground(w26: BmDrawableResource): any;           setBackgroundResourceId(v26: number): any; hasBackgroundResource(): boolean;           setBackgroundColor(u26: number): any;           setBkColorOfLeft(t26: number): any;           setBkColorOfRight(s26: number): any; hasBackgroundColor(): boolean;       setWidth(width: number): any;       setHeight(height: number): any;               setGravity(gravity: number): any;                 setAlignParent(r26: number): any;             setLayoutWeight(layoutWeight: number): any;           setPadding(left: number, top: number, right: number, bottom: number): any;           setMargin(left: number, top: number, right: number, bottom: number): any;           setVisibility(visibility: number): any;           setPBVisibility(q26: number): any;           setClickable(clickable: boolean): any; setClickAction(clickAction: string): void; getClickAction(): string;           setDescription(description: string): void; getDescription(): string; } 