import LatLng from "../../../e"; import type Point from "../../../f";       export declare class CoordTrans {             static baiduToGcj(latLng: LatLng): LatLng | null;       static wgsToGcj(latLng: LatLng): LatLng | null;             static gcjToBaidu(latLng: LatLng | null): LatLng | null;             static wgsToBaidu(latLng: LatLng): LatLng | null; }       export declare class CoordUtil {             static decodeLocation(y4: string): LatLng | null;           static ll2point(latLng: LatLng): Point;           static point2ll(point: Point, x4?: boolean): LatLng | null;             static decodeLocationList2D(w4: string): LatLng[][];             static getDistanceByLL(v4: LatLng | null, p2: LatLng | null): number; }         export declare function getAppIdentifier(): Promise<string>; export declare function getMCDistanceByOneLatLngAndRadius(n4: LatLng, radius: number): number; 