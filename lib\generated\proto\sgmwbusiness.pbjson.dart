//
//  Generated code. Do not modify.
//  source: sgmwbusiness.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use sgmwBusinessInfoDescriptor instead')
const SgmwBusinessInfo$json = {
  '1': 'SgmwBusinessInfo',
  '2': [
    {'1': 'expireAtTimestamp', '3': 1, '4': 1, '5': 3, '10': 'expireAtTimestamp'},
    {'1': 'vin', '3': 2, '4': 1, '5': 9, '10': 'vin'},
    {'1': 'msgId', '3': 3, '4': 1, '5': 9, '10': 'msgId'},
    {'1': 'businessType', '3': 4, '4': 1, '5': 9, '10': 'businessType'},
    {'1': 'belongUserId', '3': 5, '4': 1, '5': 9, '10': 'belongUserId'},
    {'1': 'businessValue', '3': 6, '4': 1, '5': 9, '10': 'businessValue'},
  ],
};

/// Descriptor for `SgmwBusinessInfo`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List sgmwBusinessInfoDescriptor = $convert.base64Decode(
    'ChBTZ213QnVzaW5lc3NJbmZvEiwKEWV4cGlyZUF0VGltZXN0YW1wGAEgASgDUhFleHBpcmVBdF'
    'RpbWVzdGFtcBIQCgN2aW4YAiABKAlSA3ZpbhIUCgVtc2dJZBgDIAEoCVIFbXNnSWQSIgoMYnVz'
    'aW5lc3NUeXBlGAQgASgJUgxidXNpbmVzc1R5cGUSIgoMYmVsb25nVXNlcklkGAUgASgJUgxiZW'
    'xvbmdVc2VySWQSJAoNYnVzaW5lc3NWYWx1ZRgGIAEoCVINYnVzaW5lc3NWYWx1ZQ==');

