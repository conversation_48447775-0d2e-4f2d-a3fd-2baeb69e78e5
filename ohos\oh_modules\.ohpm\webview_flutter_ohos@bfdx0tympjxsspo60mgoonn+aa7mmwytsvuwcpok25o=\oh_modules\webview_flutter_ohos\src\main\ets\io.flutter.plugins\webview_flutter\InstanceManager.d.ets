export declare class InstanceManager {
    private MIN_HOST_CREATED_IDENTIFIER;
    private CLEAR_FINALIZED_WEAK_REFERENCES_INTERVAL;
    private TAG;
    private finalizationListener;
    private identifiers;
    private Instances;
    private firstIdentifier;
    private nextIdentifier;
    private releaseTimer;
    private hasFinalizationListenerStopped;
    create<T>(finalizationListener: FinalizationListener): InstanceManager;
    fHasFinalizationListenerStopped(): boolean;
    constructor(finalizationListener: FinalizationListener);
    remove(identifier: number): ESObject | null;
    getInstance(identifier: number): ESObject;
    clear(): void;
    private releaseAllFinalizedInstances;
    stopFinalizationListener(): void;
    private addInstance;
    getIdentifier(instance: Object): number;
    private logWarningIfFinalizationListenerHasStopped;
    containsInstance(instance: ESObject): boolean;
    addHostCreatedInstance(instance: ESObject): number;
    addDartCreatedInstance(instance: ESObject, identifier: number): void;
}
interface FinalizationListener {
    onFinalize: (identifier: number) => void;
}
export {};
