// @keepTs
// @ts-nocheck
import { ConversationType } from '@rongcloud/imlib';
import { AvatarStyle } from '../../base/enum/AvatarStyle';
/**
 * 会话列表页面配置
 * @version 1.0.0
 */
export declare class ConversationListConfig {
    private supportedTypes;
    private filterTypeArray;
    private conversationAvatarStyle;
    /**
     * 设置会话列表支持的会话类型数组
     *```
     * SDK 内部默认加载单聊和群聊两种会话类型的会话列表
     * SDK 内部会话列表支持加载单聊、群聊、系统会话、公众号类型
     *```
     * @param typeArray 会话类型数组
     * @discussion 需要在加载会话列表前调用
     */
    setSupportedTypes(g45: Array<ConversationType>): void;
    /**
     * 获取当前 SDK 使用的会话类型数组
     * @returns 会话类型数组
     */
    getSupportedTypes(): ConversationType[];
    /**
     * 设置是否显示会话列表头像圆角，消息头像默认为矩形
     * @param style 头像类型
     */
    setConversationAvatarStyle(f45: AvatarStyle): void;
    /**
     * 获取是否显示会话列表头像圆角
     * @returns
     */
    getConversationAvatarStyle(): AvatarStyle;
}
