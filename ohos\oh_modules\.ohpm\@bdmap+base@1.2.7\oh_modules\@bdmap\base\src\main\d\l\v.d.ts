import LatLng from "../e"; import Point from "../f";       export declare class CoordUtilInternal {             static decodeLocation(geo: string): LatLng; static ll2point(latLng: LatLng): Point; static point2ll(point: Point): LatLng;           static decodeLocationList2D(strGeoList: string): LatLng[][] | null; }           export declare class GeoParse { static EXT_CHARS: string[]; static MAX_DELTA: number; static GEO_TYPE_AREA: number; static GEO_TYPE_LINE: number; static GEO_TYPE_POINT: number;             static parseGeo(geo: any): any; static decodeGeoDiff(coded: any): 0 | { geoType: number; geo: any[]; }; static decodeType(c: any): number; static char2num(c: any): number; static decode6byte(code: any, ret: any): number; static decode4byte(code: any, ret: any): number; } 