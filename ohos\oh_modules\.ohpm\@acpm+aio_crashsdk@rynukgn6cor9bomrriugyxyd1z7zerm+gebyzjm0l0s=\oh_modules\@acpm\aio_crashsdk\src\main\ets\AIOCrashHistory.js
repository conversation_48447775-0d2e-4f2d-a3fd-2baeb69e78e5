import faultLogger from '@ohos.faultLogger';
import { AIOCrashLogger } from './utils/AIOCrashLogger';
const TAG = 'AIOCrashHistory';
export class AIOCrashHistory {
    static Query(m, n, o) {
        if (m == faultLogger.FaultType.NO_SPECIFIC) {
            if (o) {
                AIOCrashLogger.error(TAG, `Query error, please specific fault type: ${m}`);
                o(-1, `not support fault type: ${m}`);
            }
            return;
        }
        faultLogger.query(m, (q, r) => {
            if (q) {
                if (o) {
                    AIOCrashLogger.error(TAG, `Query error for type(${m}) with code(${q.code}), detail: ${q}`);
                    o(q.code, 'query end with fail');
                }
                return;
            }
            const s = r.length;
            AIOCrashLogger.info(TAG, `Got history for type(${m}) with logs.length(${s})`);
            let t = 0;
            if (n) {
                for (let w = 0; w < s; ++w) {
                    let x = n(w, s, r[w].pid, r[w].uid, r[w].type, r[w].timestamp, r[w].reason, r[w].module, r[w].summary, r[w].fullLog);
                    if (x) {
                        ++t;
                    }
                }
            }
            if (o) {
                const u = (t == s ? 0 : 1);
                const v = `handle count(${s}), success(${t})`;
                AIOCrashLogger.info(TAG, v);
                o(u, v);
            }
        });
    }
}
