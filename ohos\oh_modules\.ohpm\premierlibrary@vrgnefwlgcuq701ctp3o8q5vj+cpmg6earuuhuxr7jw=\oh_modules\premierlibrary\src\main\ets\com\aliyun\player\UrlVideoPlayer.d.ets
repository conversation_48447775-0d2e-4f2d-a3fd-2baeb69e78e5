import { AVPBase } from './AVPBase';
import { UrlPlayer } from './UrlPlayer';
import { NativePlayerBase } from './nativeclass/NativePlayerBase';
import { Context } from '@ohos.abilityAccessCtrl';
export declare class UrlVideoPlayer extends AVPBase implements UrlPlayer {
    constructor(t41: Context, u41: string);
    protected createAlivcMediaPlayer(r41: Context): NativePlayerBase;
    setUrlDataSource(p41: object): void;
}
