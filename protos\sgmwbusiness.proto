//------------------------------------------------------------------------------
// Copyright (c) 2020-2023 EMQ Technologies Co., Ltd. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//------------------------------------------------------------------------------

syntax = "proto3";


// The exhook proto version should be fixed as `v2` in EMQX v5.x
// to make sure the exhook proto version is compatible
package com.cloudyoung.sgmw.common.car.proto;

message SgmwBusinessInfo {
  //消息过期时间0L，表示不过期，大于0 表示超过时间戳的数据可以不用处理。单位毫秒
  int64 expireAtTimestamp = 1;
  //消息所属vin
  string vin = 2;
  //消息id 可用于客户端去重消息
  string msgId = 3;
  //业务类型
  string businessType = 4;
  //消息所属用户id(经过base64处理) 如果是空，则表示匹配所有用户
  string belongUserId = 5;
  //businessValue 业务值 泊车通知：是通知文案
  string businessValue = 6;

}