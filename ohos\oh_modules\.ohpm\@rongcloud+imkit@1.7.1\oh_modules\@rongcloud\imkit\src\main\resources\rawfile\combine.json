{"baseHead": "<!DOCTYPE html><html lang='en'><head><meta charset='UTF-8'><meta content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0' name='viewport'/><meta http-equiv='X-UA-Compatible' content='ie=edge'><title>Document</title><style>html,body {height: 100%;-webkit-box-sizing: border-box;-moz-box-sizing: border-box;box-sizing: border-box;font-family: 'Hiragino Sans GB', 'Microsoft YaHei', 'WenQuanYi Micro Hei', sans-serif;margin: 0;padding: 0;border: 0;font-size: 14px}.rong-link-site {color: #0579f4;}.rong-pc {width: 30%;min-width: 400px;margin: 0 auto;border-left: 1px solid #DFDFDF;border-right: 1px solid #DFDFDF;min-height: 100%;}.rong-time {width: 100%;height: 40px;text-align: center;color: #999999;position: relative;}.rong-hr {width: 96%;border-bottom: 1px solid #DFDFDF;position: absolute;top: 20px;z-index: -1;margin-left: 2%;}.rong-time-value {text-align: center;height: 40px;line-height: 40px;background: #fff;padding: 0 10px;margin: 0 auto}.rong-message {margin: 10px;padding-bottom: 10px;border-bottom: 1px solid #DFDFDF;color: #999999;margin-left: 49px;position: relative;}.rong-message-user {display: inline-block;vertical-align: top;position: absolute;margin-left: -39px}.rong-message-user-bg {display: inline-block;width: 39px;height: 39px;background: #ccc;text-align: center;line-height: 39px;border-radius: 8px;color: #FFF;object-fit: cover;}.rongcloud-message-body {vertical-align: top;padding-left: 5px;}.rongcloud-message-user-name {font-size: 16px;color: #262626;margin-bottom: 4px;display: flex;justify-content: space-between;}.rongcloud-message-user-name .name {flex: 1;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;}.rongcloud-message-text pre {margin: 0;padding: 0;white-space: pre-wrap;white-space: -moz-pre-wrap;white-space: -pre-wrap;white-space: -o-pre-wrap;word-break: break-all;word-wrap: break-word}.rong-message-time {color: #999999;font-size: 14px;line-height: 20px;}.rongcloud-message-combinemsg {width: 100%;margin-left: 50px;margin-top: 10px;padding-top: 15px;border-top: 1px solid #ccc}.rong-message-file {display: inline-block;vertical-align: top;padding-left: 5px;max-width: calc(100% - 50px);}.rong-message-file div {word-wrap: break-word;}.rongcloud-message-img img {max-width: 230px;max-height: 250px;border: none;vertical-align: middle;border-radius: 5px}.rong-message-file img {width: 32px;height: 32px}.rong-message-file img {border-radius: 4px}.rong-message-combine {border: 1px solid #C4C4C4;border-radius: 8px;padding: 10px 0;width: 224px;word-break: break-all}a {text-decoration: none;color: #999999}.rong-combine-title {padding-left: 10px;padding-bottom: 5px;color: #262626}.rong-combine-body {padding-left: 10px;line-height: 20px;margin-bottom: 10px;max-height: 80px;overflow: hidden;display: -webkit-box;-webkit-box-orient: vertical;-webkit-line-clamp: 4;}.rong-conbine-foot {border-top: 1px solid #DFDFDF;margin: 0 10px;padding-top: 5px}.rong-none-user {margin-left: 49px}.rong-none-user .rong-none-user-img {display: none}.rong-big-img {position: absolute;top: 0;width: 100%;height: 100%;background: #fff;display: none}.rong-big-video {position: absolute;top: 0;width: 100%;height: 100%;background: #fff;display: none}.rong-big-img img {position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);max-width: 100%;max-height: 100%;border: none;vertical-align: middle}video {position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);max-width: 100%;max-height: 100%;border: none;vertical-align: middle}</style><style>{%style%}</style></head><body><div class='rong-main'>", "baseBottom": "</div><div class='rong-big-img' onclick='hide(\"rong-big-img\")'></div><div class='rong-big-video' onclick='hide(\"rong-big-video\")'></div><div class='rong-big-a'></div><script>window.onload = function () {var addrReplace = getUrlParams();if (addrReplace) {var userPortraits = document.getElementsByClassName('rong-message-user-portrait');for (var i = 0; i < userPortraits.length; i++) {if(!isBase64(userPortraits[i].src)) {userPortraits[i].src = urlReplace(userPortraits[i].src);}}}if (document.getElementsByName('RC:TxtMsg').length > 0) {for (var i = 0; i < document.getElementsByName('RC:TxtMsg').length; i++) {var text = document.getElementsByName('RC:TxtMsg')[i].children[1].children[0].innerHTML;var replaceText = '';replaceText = replaceUri(text);var numArr = getNum(replaceText);if (numArr && text === replaceText) {for (var j = 0; j < numArr.length; j++) {var phStr = numArr[j].replace(/[^0-9]/ig, '');if (phStr.length == 7 || phStr.length == 11) {phnumAfter = \"<span  class='rong-link-site' onclick=show({phoneNum:\" + numArr[j] + \",type:'phone'})>\" + numArr[j] + \"</span>\";replaceText = replaceText.replace(numArr[j], phnumAfter);}}}document.getElementsByName('RC:TxtMsg')[i].children[1].children[0].innerHTML = replaceText;}}};var domainArray = ['com', 'net', 'org', 'cn', 'edu', 'gov', 'info', 'biz', 'cc', 'tv', 'me', 'co', 'pro', 'name', 'link', 'online'];function getUrlParams() {if (!window.location.search) {return null;}const params = Object.fromEntries(new URLSearchParams(window.location.search));return params.target;}function isBase64(str){if(str.indexOf('data:image/')!=-1 && str.indexOf('base64')!=-1 ){return true;}else{return false;}}function urlReplace(url) {var target = getUrlParams();if (target) {var origin = new URL(url).origin;return url.replace(origin, target);}return url;}function replaceUri(str) {var mailReg = '[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\\\.[a-zA-Z0-9_-]+)+';var protocolReg = '((?:http|https|ftp)\\:\\/\\/)?';var ipReg = '(?:(?:25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]{1}[0-9]{1}|[0-9])\\\\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]{1}[0-9]{1}|[0-9])';var hostReg = '(?!@)(?:[a-zA-Z0-9-]{1,63}\\\\.)+(?:' + domainArray.join('|') + ')';var portReg = '(?:\\\\:[0-9]{1,5})?';var pathReg = '(?:(?:/[a-zA-Z0-9.,;?\\\\\\'+&%$#=~_\\\\-!()*\\\\/]*)?)';var uriReg = new RegExp('(?:(' + mailReg + ')|(' + protocolReg + '(?:(' + ipReg + ')|(' + hostReg + '))' + portReg + pathReg + '))','ig');return str.replace(uriReg, function(uriStr, _, _, _, protocol) {var link = uriStr;if (!protocol && !(new RegExp(mailReg).test(uriStr))) {link = 'http://' + link;}return \"<a class='rong-link-site' onclick=show({link:'\" + link + \"',type:'link'}) target='_blank' href='\" + link + \"'>\" + uriStr + \"</a>\";});}function getNum(text) {var value = text.match(/\\d+(\\.\\d+)?/g);return value;}var scroll_top = 0;function show(data) {buttonClick(data);var addrReplace = getUrlParams();if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.buttonClick && window.webkit.messageHandlers.buttonClick.postMessage) {window.webkit.messageHandlers.buttonClick.postMessage(data);} else if (window.interface && window.interface.sendInfoToAndroid) {window.interface.sendInfoToAndroid(JSON.stringify(data));} else if (window.RCFlutterInterface && window.RCFlutterInterface.postMessage) {window.RCFlutterInterface.postMessage(JSON.stringify(data));} else if (data.type == 'RC:CombineMsg') {var fileUrl = data.fileUrl;if (addrReplace) {fileUrl = urlReplace(data.fileUrl);}window.open(fileUrl, '_self');} else if (data.type != 'RC:LBSMsg') {if (data.fileUrl) {var url = data.fileUrl;if (addrReplace) {url = urlReplace(data.fileUrl);}data.type === 'RC:SightMsg' &&  showBig('video', url);data.type === 'RC:ImgMsg' &&   showBig('img', url);data.type === 'RC:FileMsg' &&   showBig('a', url);};}};function buttonClick(json) { };function hide(id) {document.getElementsByClassName(id)[0].style.display = 'none';document.getElementsByClassName(id)[0].children[0].remove();document.getElementsByClassName('rong-main')[0].style.display = 'block';window.scrollTo(0, scroll_top)};function isWeb() {var browser = {versions: function () {var u = navigator.userAgent,app = navigator.appVersion;return {android: u.indexOf('Android') > -1 || u.indexOf('Linux') > -1,iPhone: u.indexOf('iPhone') > -1 || u.indexOf('Mac') > -1,iPad: u.indexOf('iPad') > -1,}}()};if (browser.versions.android || browser.versions.iPhone || browser.versions.iPad) {return false} else {return true}};function showBig(type, url) {scroll_top = getScrollTop();event.currentTarget.name = new Date().getTime();var imgNode = document.createElement(type);imgNode.src = url;imgNode.id = event.currentTarget.name;if(type == 'a') {imgNode.href = url;imgNode.click();return;}document.getElementsByClassName('rong-big-' + type)[0].style.display = 'block';if (type == 'video') {imgNode['x-webkit-airplay'] = 'true';imgNode['webkit-playsinline'] = 'true';imgNode.autoplay = 'autoplay';imgNode.controls = 'controls';}document.getElementsByClassName('rong-big-' + type)[0].name = event.currentTarget.name;document.getElementsByClassName('rong-big-' + type)[0].append(imgNode);document.getElementsByClassName('rong-main')[0].style.display = 'none'}function getScrollTop() {var scroll_top = 0;if (document.documentElement && document.documentElement.scrollTop) {scroll_top = document.documentElement.scrollTop} else if (document.body) {scroll_top = document.body.scrollTop}return scroll_top}function fileClickHandler(dom, data) {show({...data,fileName: dom.getElementsByClassName('rong-message-file-name')[0].innerHTML})}var os = function () {var ua = navigator.userAgent,isWindowsPhone = /(?:Windows Phone)/.test(ua),isSymbian = /(?:SymbianOS)/.test(ua) || isWindowsPhone, isAndroid = /(?:Android)/.test(ua),isPhone = /(?:iPhone)/.test(ua) && !isTablet,isPc = !isPhone && !isAndroid && !isSymbian;return { isPc: isPc };}();if (os.isPc) {document.getElementsByClassName('rong-main')[0].classList.add('rong-pc')}</script><script>var commitId = 'd768cf0f490e68d758138a0f2f0f95354bd9752d';</script></body></html>", "time": "<div class='rong-time' name='timeTemplate'><div class='rong-hr'></div><span class='rong-time-value'>{%time%}</span></div>", "CombineMsgBody": "<div><span class='rong-combin-body-text'>{%text%}</span></div>", "RC:TxtMsg": "<div class='rong-message {%showUser%}'><div class='rong-message-user rong-none-user-img'><img src='{%portrait%}' class='rong-message-user-bg rong-message-user-portrait' /></div><div class='rongcloud-message-body'><div name='RC:TxtMsg'><div class='rongcloud-message-user-name'><span name='userName' class='name'>{%userName%}</span><span class='rong-message-time' name='sendTime'>{%sendTime%}</span></div><div class='rongcloud-message-text'><pre class='rongcloud-message-entry'>{%text%}</pre></div></div></div></div>", "RC:SightMsg": "<div class='rong-message {%showUser%}' onClick='show({fileUrl:\"{%fileUrl%}\",duration:\"{%duration%}\",imageBase64:\"{%imageBase64%}\",type:\"RC:SightMsg\",messageUid:\"{%messageUid%}\"})'><div class='rong-message-user rong-none-user-img'><img src='{%portrait%}' class='rong-message-user-bg rong-message-user-portrait' /></div><div class='rongcloud-message-body'><div name='RC:SightMsg'><div class='rongcloud-message-user-name'><span name='userName' class='name'>{%userName%}</span><span class='rong-message-time' name='sendTime'>{%sendTime%}</span></div><div class='rong-message-file'><img class='rong-message-user-bg' src='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAABgCAMAAADVRocKAAABR1BMVEVHcEw4qvc4qvd///86qvg4qvc4qvc4qvc4q/c4qvc4qvf///9PtPeGy/rJ6Pxbufiz3vv8/v47q/dGsPc5qvf5/P70+v7+/v4/rfdjvPhwwvm74vxXt/hrwPnj8/3s9/7I5/xqv/lJsfdtwPny+f7U7f2AyflvwfnN6v1swPlZuPjT7P39/v55xvm/5Pyw3ful2Pvp9f6Oz/pduvj6/f7q9v6X0vrS7P2y3vvU7P31+/5HsPf4/P53xfmc1fvv+P6U0fplvfi03/t+yPlLsvfi8v09rPdtwflBrvdfu/hQtPhVtvj2+/7f8f2Qz/pTtfjB5Pza7/284vyt3Pud1fvp9v77/f7w+P5Os/d0w/lNs/fl9P294/zg8v2i1/vw+f6X0/rb8P08rPfx+f6m2fteuvhUtvjN6fxCrvdovvhFr/fQ6/2r2/uBZ005AAAACnRSTlMAiIcCJ8r+64z8OXPVhAAAAYZJREFUeF7t2sVuM1EQhNE7bFfPGBnCzMzMzJyfGd9/HUdBZz2lSNH9XuDsu0tVsyOOF0XIRT0nYqvbLNMFKde0qoABYoZStgtirq0ioBZRDqg5ygM1T0VBLapA7i0BGtCABjSggZEg+JPiAX5MqtWvsICdnNz1Pk0BMhPy0FkLA7iSp/Lf18MHPsrzvsRDByalpux+2EBCXhS7JgPyqY8MiCz/JgPybokMSPI0xQVE2n6SAdkNyIDI4QwZkIYSGRD5XyADsveDDMjoYoYLiHT6ZEBai2RAvsXJgMTYQK9PBqTEBurIQPKSDByDCyxUuMBYHFSgPw0mMB0ATKDnAEwgOZ8CE6gvAkwgsQkmcDEHMIHGVTCB2WGACRx1gwnkB7+CCQw1AUyg/BehAgO1QK4dCBf4XAP820bYQJ08dT6eQegAph6BD1sAAfDX7oGTAigAmstZEfm1QTxIpTu6Kq93UtOABjSgAQ3QH9b0lzt9NECfPfCHG8oEMVMpZRm88YxhKfr85wbLq39XegjrFgAAAABJRU5ErkJggg==' /></div><div class='rong-message-file'><div>{%fileName%}</div><div>{%size%}</div></div></div></div></div>", "RC:ImgMsg": "<div class='rong-message {%showUser%}'><div class='rong-message-user rong-none-user-img'><img src='{%portrait%}' class='rong-message-user-bg rong-message-user-portrait' /></div><div class='rongcloud-message-body'><div name='RC:ImgMsg'><div class='rongcloud-message-user-name'><span name='userName' class='name'>{%userName%}</span><span class='rong-message-time' name='sendTime'>{%sendTime%}</span></div><div class='rongcloud-message-img' onClick='show({imgUrl:\"{%imgUrl%}\",fileUrl:\"{%fileUrl%}\",type:\"RC:ImgMsg\"})'><img src='{%imgUrl%}'></div></div></div></div>", "RC:CombineMsg": "<div class='rong-message {%showUser%}' onClick='show({fileUrl:\"{%fileUrl%}\",title:\"{%title%}\",type:\"RC:CombineMsg\"})'><div class='rong-message-user rong-none-user-img'><img src='{%portrait%}' class='rong-message-user-bg rong-message-user-portrait' /></div><div class='rongcloud-message-body'><div name='RC:CombineMsg'><div class='rongcloud-message-user-name'><span name='userName' class='name'>{%userName%}</span><span class='rong-message-time' name='sendTime'>{%sendTime%}</span></div><div class='rong-message-combine' ><div class='rong-combine-title'>{%title%}</div><div class='rong-combine-body'>{%combineBody%}</div><div class='rong-conbine-foot'>{%foot%}</div></div></div></div></div>", "RC:FileMsg": "<div class='rong-message {%showUser%}' onClick='fileClickHandler(this,{fileType:\"{%fileType%}\",fileUrl:\"{%fileUrl%}\",fileSize:\"{%fileSize%}\",type:\"RC:FileMsg\"})'><div class='rong-message-user rong-none-user-img'><img src='{%portrait%}' class='rong-message-user-bg rong-message-user-portrait' /></div><div class='rongcloud-message-body'><div name='RC:FileMsg'><div class='rongcloud-message-user-name'><span name='userName' class='name'>{%userName%}</span><span class='rong-message-time' name='sendTime'>{%sendTime%}</span></div><div class='rong-message-file'><img class='rong-message-user-bg' src='{%fileIcon%}' /></div><div class='rong-message-file'><div class='rong-message-file-name'>{%fileName%}</div><div>{%size%}</div></div></div></div></div>", "RC:VcMsg": "<div class='rong-message {%showUser%}'><div class='rong-message-user rong-none-user-img'><img src='{%portrait%}' class='rong-message-user-bg rong-message-user-portrait' /></div><div class='rongcloud-message-body'><div name='RC:VcMsg'><div class='rongcloud-message-user-name'><span name='userName' class='name'>{%userName%}</span><span class='rong-message-time' name='sendTime'>{%sendTime%}</span></div><div class='rongcloud-message-text'><pre class='rongcloud-message-entry'>{%text%}</pre></div></div></div></div>", "RC:CardMsg": "<div class='rong-message {%showUser%}'><div class='rong-message-user rong-none-user-img'><img src='{%portrait%}' class='rong-message-user-bg rong-message-user-portrait' /></div><div class='rongcloud-message-body'><div name='RC:CardMsg'><div class='rongcloud-message-user-name'><span name='userName' class='name'>{%userName%}</span><span class='rong-message-time' name='sendTime'>{%sendTime%}</span></div><div class='rongcloud-message-text'><pre class='rongcloud-message-entry'>{%text%}</pre></div></div></div></div>", "RC:StkMsg": "<div class='rong-message {%showUser%}'><div class='rong-message-user rong-none-user-img'><img src='{%portrait%}' class='rong-message-user-bg rong-message-user-portrait' /></div><div class='rongcloud-message-body'><div name='RC:StkMsg'><div class='rongcloud-message-user-name'><span name='userName' class='name'>{%userName%}</span><span class='rong-message-time' name='sendTime'>{%sendTime%}</span></div><div class='rongcloud-message-text'><pre class='rongcloud-message-entry'>{%text%}</pre></div></div></div></div>", "RC:ImgTextMsg": "<div class='rong-message {%showUser%}'><div class='rong-message-user rong-none-user-img'><img src='{%portrait%}' class='rong-message-user-bg rong-message-user-portrait' /></div><div class='rongcloud-message-body'><div name='RC:ImgTextMsg'><div class='rongcloud-message-user-name'><span name='userName' class='name'>{%userName%}</span><span class='rong-message-time' name='sendTime'>{%sendTime%}</span></div><div class='rongcloud-message-text'><pre class='rongcloud-message-entry'>{%text%}</pre></div></div></div></div>", "RC:LBSMsg": "<div class='rong-message {%showUser%}' onclick='show({locationName:\"{%locationName%}\",latitude:\"{%latitude%}\",longitude:\"{%longitude%}\",type:\"RC:LBSMsg\"})'><div class='rong-message-user rong-none-user-img'><img src='{%portrait%}' class='rong-message-user-bg rong-message-user-portrait' /></div><div class='rongcloud-message-body'><div name='RC:LBSMsg'><div class='rongcloud-message-user-name'><span name='userName' class='name'>{%userName%}</span><span class='rong-message-time' name='sendTime'>{%sendTime%}</span></div><div class='rong-message-file'><img class='rong-message-user-bg' src='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGAAAABgCAMAAADVRocKAAAB2lBMVEVHcEz4yif0xiLzxiLzxSLzxSLzxSL//3/zxiLzxSLzxiPzxyn21mT55qH77br88s/99+T+/fj++u/99dr778T66q/434j0zkTzxib32nL77Ln+/Pb////99t755Jn1zkf32Gz99t/+/v366az0yzv10VL88tH55Jr33Hz+/fr889T0yzz656X9+ev10lb778P66Kj+/PT1z0v43oT9+Ob0yjf201n88s3zxSP89Nf32XD323b889P66rD55Jv65qP++u3zxijzxyv9+en54pH0yzrzxST201r778X33Hv0yjb212n+/PP9+er66Kr1z0jzyTH+/vv778LzxSX55Z3656T9+OX66Kn55Z/0yjX77r/10VP10VH89Nb434b212f++uz99dz32G333X3+/vz88tD21WH44Y7+/Pf32nX9+Ofzxy3545f323j99dv101jzyTP43oL21mP88s721WD0zD/78Mf0yjj99+D0zkX44Y389Nj33Hr0zD322Gr44In0zkb+/fnzyC/88Mj66rL101fzxyz++/LzyDD++/H66Kf44Ir0yznzxyrzyTL43X71z0n88cr54pL0zULzxif434f10E743oP0zUH99+L54pD44ZD0zUP889L92okrAAAAC3RSTlMAJ4zK6/z/Aof+iNki+F4AAAKsSURBVHgBtMEHAYMADADBzx7+/bbggdzxEDWP/Fi4qfCqnjwyXcDmoQWZPDSC5inF8pThecqJPBX82aMH7UqCKArDizu2kx3btm0nY9u2nbH1tuO5Vd19tU7VeL4X+A/wg/1egf+B/4H/gZjYuPiExKTklNS09Azrgcys7BxqcvPyrQYKCulWVFxiLVBaRn+SymPsBCoqGUBVtYVAZg0Dq60zDmTWM5jKWNNAA4NrbDILNDOUllaTQFsRQ2o3CHR0Mgxd8kA3XXp6+/rjB+iUOCgNDA1T1zgyis/GxifokC4NTFI3MQWf6RlqZjuEgVRq5sagmU+mpk4WWFiksrQMh/FFKiuyQDeVpGq4rFJZJwusp7IBbhsrqYyKApuobIbHEpUtosBW+szAaxuV7aLADvrshNcuKrtFgT302QuvfVT2iwIH6JMIr4NU0kWBQ/RZPAyPI1SOigITVArgcYzKcVFglcoE3OqoOSEKnKTmFFxOU6mFKHCGmoGzgT/Ac7LAeeouXITmEnVxsgAuUzdzZRnfXI2nwzVhII9OuddvjAGHT83dpMPiLWFgnB5FtTP0uA1hIHOGYUmXBnCHYckXB+4yHGUQB3CZYThoEKhgaMmDBoF7awypDwYBHGEoi2eNAvcfMISHMArgEYNLemwYeDLLoMphGMBuBrP21DgwtpdBPINxAM8rGdALWAjgJQM58MpK4HUKA3gDKwG8TQpwoDFLAVyhP8NTsBVAMb0W62AvENNJj3ewGMD7Abr0wmoApTl0SH1tOYDYPdRUHYbtAK5Q+biZXrQYN01HdO0n02ZgdhbMfHFajfzOhplPAwsQNjjPoeXYtbHQ3MkjbvR91IJRC0YtoPmENc2n3Gm+aIDmyx5ov3CDgYOWFnCAFs9w0W7xDBc7A82X/wAAl+mUyf7t7YMAAAAASUVORK5CYII='></div><div class='rong-message-file'><div>{%locationName%}</div></div></div></div></div>", "RC:GIFMsg": "<div class='rong-message {%showUser%}'><div class='rong-message-user rong-none-user-img'><img src='{%portrait%}' class='rong-message-user-bg rong-message-user-portrait' /></div><div class='rongcloud-message-body'><div name='RC:ImgMsg'><div class='rongcloud-message-user-name'><span name='userName' class='name'>{%userName%}</span><span class='rong-message-time' name='sendTime'>{%sendTime%}</span></div><div class='rongcloud-message-img'><img style='display: {%gifDisplay%}' src='{%fileUrl%}' onerror='this.src=`data:image/png;base64,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`'><span style='display: {%gifContentDisplay%}' onclick='show({fileUrl:\"{%fileUrl%}\",type:\"RC:GIFMsg\"})'>{%gifContent%}</span></div></div></div></div>", "RCJrmf:RpMsg": "<div class='rong-message {%showUser%}'><div class='rong-message-user rong-none-user-img'><img src='{%portrait%}' class='rong-message-user-bg rong-message-user-portrait' /></div><div class='rongcloud-message-body'><div name='RCJrmf:RpMsg'><div class='rongcloud-message-user-name'><span name='userName' class='name'>{%userName%}</span><span class='rong-message-time' name='sendTime'>{%sendTime%}</span></div><div class='rongcloud-message-text'><pre class='rongcloud-message-entry'>{%text%}</pre></div></div></div></div>", "RC:VCSummary": "<div class='rong-message {%showUser%}'><div class='rong-message-user rong-none-user-img'><img src='{%portrait%}' class='rong-message-user-bg rong-message-user-portrait' /></div><div class='rongcloud-message-body'><div name='RC:VCSummary'><div class='rongcloud-message-user-name'><span name='userName' class='name'>{%userName%}</span><span class='rong-message-time' name='sendTime'>{%sendTime%}</span></div><div class='rongcloud-message-text'><pre class='rongcloud-message-entry'>{%text%}</pre></div></div></div></div>"}