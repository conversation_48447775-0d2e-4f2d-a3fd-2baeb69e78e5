import { MessageContent } from './MessageContent';
/**
 * 媒体消息体基类。SDK 内置的所有媒体类型消息（图片、文件等）均继承此类
 *
 * # 说明
 *```
 * 从 1.4.0 版本开始支持自定义媒体消息
 * 自定义媒体消息上传给融云的媒体服务时，对自定义媒体消息有如下要求
 * 1. encode() 必须返回合法的 json 字符串
 * 2. json 中包含三个固定的 key：name、localPath、remoteUrl
 *  2.1. name ： string 类型，文件名称
 *  2.2. localPath ： string 类型，本地路径
 *  2.3. remoteUrl ： string 类型， 远端地址
 * 3. json 中本地路径的 key 必须是 localPath。原因是 SDK 反解 json 之后，通过 jsonObject.get("localPath") 方法来获取本地路径
 * 4. SDK 上传成功后会把消息远端地址放在 json 字符串的 remoteUrl 中，App 可通过 jsonObject.get("remoteUrl") 来获取远端地址
 *```
 * # 自定义媒体消息示例代码
 *```
 * import { JsonConverter, MediaMessageContent, MessageFlag, MessageTag } from "@rongcloud/imlib";
 * import List from '@ohos.util.List';
 *
 * const CustomFileMessageObjectName = "App:FileMsg";
 * const CustomFileMessageFlag = MessageFlag.Count;
 *
 * // 1. 继承 MediaMessageContent 并实现 MessageTag 注解
 * &#64MessageTag(CustomFileMessageObjectName, CustomFileMessageFlag)
 * export class CustomFileMessage extends MediaMessageContent {
 *  // 2. 按需增加属性
 *
 *  // 文件大小，单位为 Byte
 *  size : number = 0;
 *  // 文件类型
 *  type: string = "";
 *
 *  // 3. 必须声明无参的构造方法，因为注册自定义消息时候，只能用无参构造方法
 *  constructor() {
 *    super();
 *  }
 *
 *  // 4. 将消息对象转为 JSON 字符串
 *  encode(): string {
 *    // 4.1 将基类的数据保存到 map 中
 *    let map = super.encodeBaseData();
 *
 *    // 4.2 将本类的独有属性放到 map 中
 *    // 说明：ts 的 map 必须指定 kv 的类型，所以存多种类型数据，需要转为 Object
 *    if (this.name) {
 *      map.set("name", this.name as Object);
 *    }
 *    // warning ：本地路径必须用 "localPath" ，确保 SDK 能够通过 json 正确获取到本地路径
 *    if (this.localPath) {
 *      let localPath = super.getCorrectedLocalPath(this.localPath);
 *      map.set("localPath", localPath as Object);
 *    }
 *    // waring ：远端地址必须用 "remoteUrl"
 *    if (this.remoteUrl) {
 *      map.set("remoteUrl", this.remoteUrl as Object);
 *    }
 *    map.set("size", this.size as Object);
 *    if (this.type) {
 *      map.set("type", this.type as Object);
 *    }
 *
 *    // 4.3 将 map 转为 字符串
 *    return JsonConverter.stringifyFromHashMap(map);
 *  }
 *
 *  // 5. 将字符串转为消息对象
 *  decode(contentString: string): void {
 *    // 5.1 将字符串转为 map
 *    let map = JsonConverter.parseToHashMap(contentString);
 *    // 5.2 将基类的数据解析出来
 *    super.decodeBaseData(map);
 *
 *    // 5.3 将本类的独有属性解析
 *    // 说明：需要将 Object 转为对应的数据类型
 *    if (map.get("name")) {
 *      this.name = map.get("name") as string;
 *    }
 *    if (map.get("localPath")) {
 *      this.localPath = map.get("localPath") as string;
 *    }
 *    if (map.get("remoteUrl")) {
 *      this.remoteUrl = map.get("remoteUrl") as string;
 *    }
 *    if (map.get("size")) {
 *      this.size = map.get("size") as number;
 *    }
 *    if (map.get("type")) {
 *      this.type = map.get("type") as string;
 *    }
 *  }
 *
 *  // 6. 将当前类名返回：该方法的作用是防止代码混淆或者压缩后无法获取正常的类名
 *  // 直接写字符串可能会出现拼写错误的情况，所以此处建议直接使用 类名.name
 *  getClassName(): string {
 *    return CustomFileMessage.name;
 *  }
 *
 *  // 7. 返回搜索字段
 *  // 1) 不实现该方法，该类消息无法被搜索
 *  // 2) 实现该方法，返回 null 或者 List 长度为 0，无法被搜索
 *  // 3) 实现该方法， List 里面的 空字符串 和 null 会被忽略
 *  // 4) List 中必须包含有效的字符串才能被搜索到
 *  getSearchableWord(): List<string> | null {
 *    if (!this.name) {
 *      return null;
 *    }
 *    let list = new List<string>();
 *    list.add(this.name);
 *    return list;
 *    }
 *  }
 *```
 *
 * @version 1.0.0
 */
declare abstract class MediaMessageContent extends MessageContent {
    /**
     * 媒体资源的名称
     */
    name: string;
    /**
     * 媒体资源的本地路径
     */
    localPath: string;
    /**
     * 媒体资源的远端地址
     */
    remoteUrl: string;
    /**
     * 获取正常的 localPath ，去掉 file://
     *
     * 在鸿蒙系统内部，沙盒路径需要用 file:// 开头才能被 fs 识别
     *
     * 原生层使用不能以 file:// 开头
     */
    getCorrectedLocalPath(localPath: string): string;
}
export { MediaMessageContent };
