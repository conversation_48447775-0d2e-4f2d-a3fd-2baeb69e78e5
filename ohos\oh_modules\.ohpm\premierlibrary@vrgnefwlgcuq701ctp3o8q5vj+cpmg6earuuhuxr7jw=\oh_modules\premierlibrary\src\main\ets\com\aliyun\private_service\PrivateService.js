import premierlibrary from 'libpremierlibrary.so';
import ACPM from "@acpm/acpm_ohos_pc";
export class PrivateService {
    static preInitService(r42) {
        ACPM.setup(r42);
        ACPM.getFunctionsBinder().forEach((t42, u42) => {
            premierlibrary.JSBind.bindFunction(u42, t42);
        });
        premierlibrary.nPreInitService();
    }
    static initService(m42, n42) {
        ACPM.setup(m42);
        ACPM.getFunctionsBinder().forEach((p42, q42) => {
            premierlibrary.JSBind.bindFunction(q42, p42);
        });
        premierlibrary.nInitService(n42);
    }
    static initServiceWithBytes(h42, i42) {
        ACPM.setup(h42);
        ACPM.getFunctionsBinder().forEach((k42, l42) => {
            premierlibrary.JSBind.bindFunction(l42, k42);
        });
        premierlibrary.nInitService_bytes(i42);
    }
}
