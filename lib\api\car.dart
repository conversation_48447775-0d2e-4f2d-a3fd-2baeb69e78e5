/// FileName car
///
/// <AUTHOR>
/// @Date 2024/5/9 17:04
///
/// @Description TODO
import 'package:wuling_flutter_app/models/car/car_service_model.dart';
import 'package:wuling_flutter_app/models/car/car_service_response_model.dart';
import 'package:wuling_flutter_app/models/car/car_status_response_model.dart';
import 'package:wuling_flutter_app/models/car/car_control_response_model.dart';
import 'package:wuling_flutter_app/models/car/car_ac_reservation_model.dart';
import 'package:wuling_flutter_app/models/car/car_ac_reservation_response_model.dart';
import 'package:wuling_flutter_app/api/car_api.dart';
import 'package:wuling_flutter_app/utils/http/api_exception.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';

import '../models/car/remote_park/remote_park_base_model.dart';

class FetchResult<T> {
  final T? data;
  final String message;
  final bool isSuccess;

  FetchResult.success(this.data)
      : isSuccess = true,
        message = '';
  FetchResult.failure(this.message)
      : isSuccess = false,
        data = null;
}

class CarAPI {
  Future<FetchResult<Map<String, dynamic>>>
      fetchDefaultCarInfoAndStatus() async {
    try {
      // var response = await HttpRequest().post(
      //   'https://openapi.baojun.net/junApi/sgmw/userCarRelation/queryDefaultCarStatus',
      // );
      // dynamic data = response.data['data'];
      // if (data is Map) {
      //   CarInfoModel? carInfo;
      //   CarStatusModel? carStatus;
      //
      //   if (data.containsKey('carInfo')) {
      //     carInfo = CarInfoModel.fromJson(data['carInfo']);
      //     String key = '${SP_USER_DEFAULT_CAR_KEY}_${GlobalData().userModel?.userIdStr}';
      //     SpUtil().setJSON(key, carInfo.toJson());
      //   }
      //   if (data.containsKey('carStatus')) {
      //     carStatus = CarStatusModel.fromJson(data['carStatus']);
      //   }
      //
      //   if (carInfo == null && carStatus == null) {
      //     return FetchResult.failure('没有数据');
      //   } else {
      //     return FetchResult.success({
      //       'carInfo': carInfo,
      //       'carStatus': carStatus,
      //     });
      //   }
      // } else {
      //   return FetchResult.failure('数据格式不正确');
      // }
      CarStatusResponseModel carStatusResponseModel =
          await carAPI.getDefaultCarInfoAndStatus();
      return FetchResult.success({
        'carInfo': carStatusResponseModel.carInfo,
        'carStatus': carStatusResponseModel.carStatus,
      });
    } catch (e) {
      return FetchResult.failure(e is Exception ? e.toString() : '发生未知错误');
    }
  }

  Future<FetchResult<Map<String, List<CarServiceModel>>>>
      fetchCarServiceList() async {
    try {
      // final Map<String, List<String>> parameters = {
      //   "servicePositionCodeList": [
      //     'service_control',
      //     'service_tools',
      //     'service_activity',
      //     'service_screen'
      //   ],
      // };
      // var response = await HttpRequest().post(
      //   'https://openapi.baojun.net/junApi/sgmw/service/appQueryService',
      //   data: parameters,
      // );
      // List<dynamic> dataJsonList = response.data['data'];
      // List<CarServiceResponseModel> serviceResponses = dataJsonList
      //     .map((jsonItem) => CarServiceResponseModel.fromJson(jsonItem))
      //     .toList();
      final List<String> servicePositionCodeList = [
        'service_control',
        'service_tools',
        'service_activity',
        'service_screen'
      ];
      List<CarServiceResponseModel> serviceResponses =
          await carAPI.getCarServiceList(servicePositionCodeList);
      List<CarServiceModel> ctrlServiceList = [];
      List<CarServiceModel> toolServiceList = [];
      List<CarServiceModel> eventList = [];
      List<CarServiceModel> screenList = [];
      for (var serviceResponseModel in serviceResponses) {
        switch (serviceResponseModel.positionCode) {
          case 'service_control':
            ctrlServiceList = serviceResponseModel.serviceList ?? [];
            break;
          case 'service_tools':
            toolServiceList = serviceResponseModel.serviceList ?? [];
            break;
          case 'service_activity':
            eventList = serviceResponseModel.serviceList ?? [];
            break;
          case 'service_screen':
            screenList = serviceResponseModel.serviceList ?? [];
            break;
        }
      }
      return FetchResult.success({
        'ctrlServiceList': ctrlServiceList,
        'toolServiceList': toolServiceList,
        'eventList': eventList,
        'screenList': screenList,
      });
    } catch (e) {
      return FetchResult.failure(e is Exception ? e.toString() : '发生未知错误');
    }
  }

  void requestCarControlServiceWithURLStr(
      {required String targetUrl,
      required Map<String, dynamic> params,
      required void Function(
              {required CarControlResponseModel carControlResponseModel})
          onSucceed,
      required void Function(Exception exception) onError}) async {
    try {
      CarControlResponseModel carControlResponseModel =
          await carAPI.requestCarControlServiceWithURLStr(targetUrl, params);
      onSucceed(carControlResponseModel: carControlResponseModel);
    } catch (e) {
      onError(e as Exception);
    }
  }

  void fetchAcReservationList(
      {required String vin,
      required void Function(
              {required List<CarAcReservationModel> reservationList})
          onSucceed,
      required void Function(Exception exception) onError}) async {
    try {
      CarAcReservationResponseModel responseModel =
          await carAPI.getAcReservationListWithVin(vin);
      onSucceed(reservationList: responseModel.list ?? []);
    } catch (e) {
      onError(e as Exception);
    }
  }

  void deleteAcReservationWithIdList(
      {required List<int> deleteList,
      required void Function({required bool isSucceed}) onSucceed,
      required void Function(Exception exception) onError}) async {
    try {
      bool isSucceed = await carAPI.deleteAcReservationWithIdList(deleteList);
      onSucceed(isSucceed: isSucceed);
    } catch (e) {
      onError(e as Exception);
    }
  }

  void getParkingStatusWithVIN(
      {required String vin,
      required void Function({required RemoteParkBaseModel result}) onSucceed,
      required void Function(Exception exception) onError}) async {
    // 先检测网络状态
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        // 无网络时直接返回友好提示，不发起网络请求
        onError(Exception('啊哦，网络似乎出了点问题…'));
        return;
      }
    } catch (e) {
      // 网络检测失败，也认为是无网络
      onError(Exception('啊哦，网络似乎出了点问题…'));
      return;
    }

    // 有网络时才发起请求
    try {
      RemoteParkBaseModel baseModel =
          await carAPI.getParkingStatusFromService({'vin': vin});
      onSucceed(result: baseModel);
    } catch (e) {
      // 有网络但请求失败，使用其他提示
      String friendlyMessage = '服务暂时不可用，请稍后重试';
      if (e is APIException) {
        friendlyMessage = e.userMessage ?? friendlyMessage;
      }
      onError(Exception(friendlyMessage));
    }
  }
}
