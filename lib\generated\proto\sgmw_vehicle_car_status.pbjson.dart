//
//  Generated code. Do not modify.
//  source: sgmw_vehicle_car_status.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use sgmwVehicleCarStatusDescriptor instead')
const SgmwVehicleCarStatus$json = {
  '1': 'SgmwVehicleCarStatus',
  '2': [
    {'1': 'time', '3': 1, '4': 1, '5': 3, '10': 'time'},
    {'1': 'carControlType', '3': 2, '4': 1, '5': 5, '10': 'carControlType'},
    {'1': 'vin', '3': 3, '4': 1, '5': 9, '10': 'vin'},
    {'1': 'carLocation', '3': 4, '4': 3, '5': 11, '6': '.SgmwVehicleCarStatus.CarLocationEntry', '10': 'carLocation'},
    {'1': 'supplementPowerStatus', '3': 5, '4': 1, '5': 5, '10': 'supplementPowerStatus'},
    {'1': 'airConditioningStatus', '3': 6, '4': 1, '5': 5, '10': 'airConditioningStatus'},
    {'1': 'driverDoorLockStatus', '3': 7, '4': 1, '5': 5, '10': 'driverDoorLockStatus'},
    {'1': 'tailgateTouchStatus', '3': 8, '4': 1, '5': 5, '10': 'tailgateTouchStatus'},
    {'1': 'engineStart', '3': 9, '4': 1, '5': 5, '10': 'engineStart'},
    {'1': 'skylightControlStatus', '3': 10, '4': 1, '5': 5, '10': 'skylightControlStatus'},
    {'1': 'driverWindowControlStatus', '3': 11, '4': 1, '5': 5, '10': 'driverWindowControlStatus'},
    {'1': 'copilotWindowControlStatus', '3': 12, '4': 1, '5': 5, '10': 'copilotWindowControlStatus'},
    {'1': 'rearLeftWindowControlStatus', '3': 13, '4': 1, '5': 5, '10': 'rearLeftWindowControlStatus'},
    {'1': 'rearRightWindowControlStatus', '3': 14, '4': 1, '5': 5, '10': 'rearRightWindowControlStatus'},
    {'1': 'driverSeatHeatControlStatus', '3': 15, '4': 1, '5': 5, '10': 'driverSeatHeatControlStatus'},
    {'1': 'engineCoverStatus', '3': 16, '4': 1, '5': 5, '10': 'engineCoverStatus'},
    {'1': 'acTemperature', '3': 17, '4': 1, '5': 5, '10': 'acTemperature'},
  ],
  '3': [SgmwVehicleCarStatus_CarLocationEntry$json],
};

@$core.Deprecated('Use sgmwVehicleCarStatusDescriptor instead')
const SgmwVehicleCarStatus_CarLocationEntry$json = {
  '1': 'CarLocationEntry',
  '2': [
    {'1': 'key', '3': 1, '4': 1, '5': 9, '10': 'key'},
    {'1': 'value', '3': 2, '4': 1, '5': 1, '10': 'value'},
  ],
  '7': {'7': true},
};

/// Descriptor for `SgmwVehicleCarStatus`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List sgmwVehicleCarStatusDescriptor = $convert.base64Decode(
    'ChRTZ213VmVoaWNsZUNhclN0YXR1cxISCgR0aW1lGAEgASgDUgR0aW1lEiYKDmNhckNvbnRyb2'
    'xUeXBlGAIgASgFUg5jYXJDb250cm9sVHlwZRIQCgN2aW4YAyABKAlSA3ZpbhJICgtjYXJMb2Nh'
    'dGlvbhgEIAMoCzImLlNnbXdWZWhpY2xlQ2FyU3RhdHVzLkNhckxvY2F0aW9uRW50cnlSC2Nhck'
    'xvY2F0aW9uEjQKFXN1cHBsZW1lbnRQb3dlclN0YXR1cxgFIAEoBVIVc3VwcGxlbWVudFBvd2Vy'
    'U3RhdHVzEjQKFWFpckNvbmRpdGlvbmluZ1N0YXR1cxgGIAEoBVIVYWlyQ29uZGl0aW9uaW5nU3'
    'RhdHVzEjIKFGRyaXZlckRvb3JMb2NrU3RhdHVzGAcgASgFUhRkcml2ZXJEb29yTG9ja1N0YXR1'
    'cxIwChN0YWlsZ2F0ZVRvdWNoU3RhdHVzGAggASgFUhN0YWlsZ2F0ZVRvdWNoU3RhdHVzEiAKC2'
    'VuZ2luZVN0YXJ0GAkgASgFUgtlbmdpbmVTdGFydBI0ChVza3lsaWdodENvbnRyb2xTdGF0dXMY'
    'CiABKAVSFXNreWxpZ2h0Q29udHJvbFN0YXR1cxI8Chlkcml2ZXJXaW5kb3dDb250cm9sU3RhdH'
    'VzGAsgASgFUhlkcml2ZXJXaW5kb3dDb250cm9sU3RhdHVzEj4KGmNvcGlsb3RXaW5kb3dDb250'
    'cm9sU3RhdHVzGAwgASgFUhpjb3BpbG90V2luZG93Q29udHJvbFN0YXR1cxJAChtyZWFyTGVmdF'
    'dpbmRvd0NvbnRyb2xTdGF0dXMYDSABKAVSG3JlYXJMZWZ0V2luZG93Q29udHJvbFN0YXR1cxJC'
    'ChxyZWFyUmlnaHRXaW5kb3dDb250cm9sU3RhdHVzGA4gASgFUhxyZWFyUmlnaHRXaW5kb3dDb2'
    '50cm9sU3RhdHVzEkAKG2RyaXZlclNlYXRIZWF0Q29udHJvbFN0YXR1cxgPIAEoBVIbZHJpdmVy'
    'U2VhdEhlYXRDb250cm9sU3RhdHVzEiwKEWVuZ2luZUNvdmVyU3RhdHVzGBAgASgFUhFlbmdpbm'
    'VDb3ZlclN0YXR1cxIkCg1hY1RlbXBlcmF0dXJlGBEgASgFUg1hY1RlbXBlcmF0dXJlGj4KEENh'
    'ckxvY2F0aW9uRW50cnkSEAoDa2V5GAEgASgJUgNrZXkSFAoFdmFsdWUYAiABKAFSBXZhbHVlOg'
    'I4AQ==');

