/**
 * 公共交通信息查询结果
 */
import { RouteNode, RouteStep, SearchResult } from "../e/h/f1";
export interface BusLineResult extends SearchResult {
    /** 公交公司名称 */
    busCompany?: string;
    /** 公交线路名称 */
    busLineName?: string;
    /** 公交是线是否有月票 */
    isMonthTicket?: boolean;
    /** 公交路线首班车时间 */
    startTime?: Date;
    /** 公交路线末班车时间 */
    endTime?: Date;
    /** 公交线路uid */
    uid?: string;
    /** 所有公交站点信息 */
    stations?: BusStation[];
    /** 封装分段坐标点，以便扩展 */
    steps?: BusStep[];
    /** 公交起步价 */
    basePrice?: number;
    /** 公交路线的最高票价 */
    maxPrice?: number;
    /** 公交路线方向 */
    lineDirection?: string;
}
/**
 * 公交站点信息
 */
export interface BusStation extends RouteNode {
}
/**
 * 公交路线分段信息
 */
export interface BusStep extends RouteStep {
}
