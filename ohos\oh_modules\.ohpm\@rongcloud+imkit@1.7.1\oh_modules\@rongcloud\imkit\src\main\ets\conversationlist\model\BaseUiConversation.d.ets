// @keepTs
// @ts-nocheck
/**
 * Created on 2024/08/06
 * <AUTHOR>
 */
import { Conversation, Message, PublicServiceInfo, SentStatus } from '@rongcloud/imlib';
import { Context } from '@kit.AbilityKit';
import { UserInfoModel } from '../../user/model/UserInfoModel';
import { GroupInfoModel } from '../../user/model/GroupInfoModel';
import { GroupMemberInfoModel } from '../../user/model/GroupMemberInfoModel';
/**
 * 带 UI 的部分数据
 * @version 1.0.0
 */
@Observed
export declare abstract class BaseUiConversation {
    private portrait;
    private title;
    private context;
    private conversation;
    private messageContent;
    private lastMessageSentStatus;
    setPortrait(d46: string | Resource): void;
    getPortrait(): string | Resource;
    setLastMessageSentStatus(c46: SentStatus): void;
    getLastMessageSentStatus(): SentStatus;
    getTitle(): string | undefined;
    setTitle(b46: string | undefined): void;
    setConversation(a46: Conversation): void;
    getContext(): Context;
    getConversation(): Conversation;
    setMessageContent(z45: MutableStyledString): void;
    getMessageContent(): MutableStyledString;
    constructor(x45: Context, y45: Conversation);
    /**
     * 更新会话数据
     */
    abstract onConversationUpdate(v45: Conversation, w45: boolean): void;
    /**
     * 用户信息更新
     */
    abstract onUserInfoUpdate(u45: UserInfoModel): void;
    /**
     * 群组信息更新
     */
    abstract onGroupInfoUpdate(t45: GroupInfoModel): void;
    /**
     * 群组成员更新
     */
    abstract onGroupMemberUpdate(s45: GroupMemberInfoModel): void;
    /**
     * 公众号信息更新
     */
    onPublicServiceInfoUpdate(r45: PublicServiceInfo): void;
    /**
     * 判断是否是阅后即焚消息
     * @param lastMessage
     * @returns
     */
    protected isDestruct(q45: Message): boolean;
    /**
     * 更新最后一条消息的发送状态
     * @param conversation 会话
     */
    protected updateSentStatus(j45: Conversation): Promise<void>;
}
