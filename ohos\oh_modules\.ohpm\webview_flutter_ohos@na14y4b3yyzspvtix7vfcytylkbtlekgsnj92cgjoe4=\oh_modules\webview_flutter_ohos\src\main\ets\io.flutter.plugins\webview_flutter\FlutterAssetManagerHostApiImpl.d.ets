import { FlutterAssetManager } from './FlutterAssetManager';
import { FlutterAssetManagerHostApi } from './GeneratedOhosWebView';
export declare class FlutterAssetManagerHostApiImpl extends FlutterAssetManagerHostApi {
    private flutterAssetManager;
    constructor(flutterAssetManager: FlutterAssetManager);
    list(path: string): Promise<string[]>;
    getAssetFilePathByName(name: string): string;
}
