import { <PERSON><PERSON><PERSON>, MethodResult } from "@ohos/flutter_ohos";
export declare class FluwxShareHandler {
    share(call: Method<PERSON><PERSON>, result: MethodResult): void;
    shareText(call: Method<PERSON>all, result: MethodResult): Promise<void>;
    shareImage(call: <PERSON><PERSON><PERSON>, result: MethodResult): Promise<void>;
    shareWebPage(call: Method<PERSON><PERSON>, result: MethodResult): Promise<void>;
    shareMiniProgram(call: MethodCall, result: MethodResult): Promise<void>;
}
