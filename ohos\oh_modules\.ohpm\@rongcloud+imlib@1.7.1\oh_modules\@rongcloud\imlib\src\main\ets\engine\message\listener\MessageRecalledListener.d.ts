import { RecallNotificationMessage } from '../content/normal/RecallNotificationMessage';
import { Message } from '../Message';
/**
 * 消息撤回监听
 * @version 1.3.0
 */
interface MessageRecalledListener {
    /**
     * 消息撤回。撤回了之后，原始消息会变成 RecallNotificationMessage 消息
     * @param message 消息体
     * @param recallMessage 撤回小灰条消息
     */
    onMessageRecalled(message: Message, recallMessage: RecallNotificationMessage): any;
}
export { MessageRecalledListener };
