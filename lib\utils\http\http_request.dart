import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:wuling_flutter_app/api/index.dart';
import 'package:wuling_flutter_app/constant/constant.dart';
import 'package:wuling_flutter_app/models/global_data.dart';
import 'package:crypto/crypto.dart';
import 'dart:convert';
import 'package:uuid/uuid.dart';
import 'package:wuling_flutter_app/models/user/oauth_model.dart';
import 'package:wuling_flutter_app/utils/compress_image_utils.dart';
import 'package:wuling_flutter_app/utils/manager/loading_manager.dart';
import 'package:wuling_flutter_app/utils/http/api_exception.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'dart:async';
import 'package:http_parser/http_parser.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';

import '../../constant/storage.dart';
import '../manager/log_manager.dart';
import '../sp_util.dart';

/*
  2402103 -  服务器异常
  2402119 - 检测ucu版本号失败
  2402118 - 请更新车机版本，详情咨询客服
  2401035 - VIN已绑定
  2000001 - VIN已绑定
  160012 - timestamp参数不合法，请检查参数
  160019 - access_token参数不合法，请检查参数
  160023 - access_token已过期，请重新申请
  161036 - 登录的号码尚未注册
*/
List<String> _specialErrorCodes = [
  "2402118",
  "2402119",
  "2402103",
  "2401035",
  "2000001",
  "40000001",
  "1100102",
  "1800047",
  "160012",
  "160019",
  "160023",
  "161036"
];

class HttpRequest {
  static final HttpRequest _instance = HttpRequest._internal();
  late Dio dio;
  final Map<String, Timer> _debouncers = {}; //防抖请求记录
  final int _debounceDuration = 300; //防抖间隔,毫秒

  // 静态变量，用于标识是否禁用错误吐司
  static bool _suppressErrorToast = false;

  // 私有构造函数
  HttpRequest._internal() {
    dio = Dio();
    dio.options.baseUrl = Constant.BASE_URL; // 基础URL
    dio.options.connectTimeout =
        const Duration(milliseconds: 15000); // 连接服务器超时时间，单位是毫秒.
    dio.options.receiveTimeout =
        const Duration(milliseconds: 30000); // 接收数据的最长时限.
    dio.options.headers = {
      'Content-Type': 'application/json',
      // 其他需要的默认header
    };

    // (dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate =
    //     (client) {
    //   // config the http client
    //   //设置该客户端的代理为指定的 ip:端口
    //   client.findProxy = (uri) {
    //     // 不设置代理
    //     // return 'DIRECT';
    //     //设置代理
    //     // return "PROXY localhost:8888";
    //     //设置多个代理
    //     // return "PROXY localhost:8888;PROXY localhost:7777";
    //     // 设置代理与未设置代理均支持  ‘DIRECT’一定要放在最后
    //     return "PROXY *************:8888;";
    //
    //   };
    //   ///解决安卓https抓包问题
    //   client.badCertificateCallback = (X509Certificate cert, String host, int port) => true;
    //   // you can also create a HttpClient to dio
    //   // return HttpClient();
    // };

    // 添加拦截器
    dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) {
        // 请求前的处理
        // 如果登录了，设置OAuth请求头
        if (GlobalData().oauthModel != null) {
          if (options.baseUrl.contains(Constant.SERVICE_BASE_URL_OPEN)) {
            //走统一车控授权
            _setSGMWUnifiedOAuthHeader(options);
          } else if (options.baseUrl.contains(Constant.UPLOAD_FILE_URL)) {
            // 走普通文件上传,不做处理
          } else if (options.baseUrl.contains(Constant.SGMW_FILE_SERVICE_URL)) {
            // 走私密文件上传
            _setFileUploadOAuthHeader(options);
          } else {
            _setOAuthHeader(options);
          }
        }

        return handler.next(options); // 继续执行
      },
      onResponse: (response, handler) {
        // 响应数据处理
        _errCodeHandler(response);
        return handler.next(response); // 继续执行
      },
      onError: (DioException e, handler) {
        // 错误处理,转换为APIException
        APIException apiException = _exceptionHandler(e);
        return handler.next(apiException); // 继续执行
      },
    ));
  }

  // 提供一个静态方法来获取类的实例
  factory HttpRequest() {
    return _instance;
  }

  // 静态方法：禁用错误吐司（用于轮询等场景）
  static void suppressErrorToast() {
    _suppressErrorToast = true;
  }

  // 静态方法：启用错误吐司（恢复正常显示）
  static void enableErrorToast() {
    _suppressErrorToast = false;
  }

  Future<Response> get(String url,
      {Map<String, dynamic>? data, Map<String, dynamic>? headers}) async {
    return debouncedRequest(
      requestFunction: () => dio.get(
        url,
        queryParameters: data,
        options: Options(headers: headers),
      ),
      url: url,
      queryParameters: data,
    );
  }

  Future<Response> post(String url,
      {dynamic data,
      Map<String, dynamic>? query,
      Map<String, dynamic>? headers}) async {
    return debouncedRequest(
      requestFunction: () => dio.post(
        url,
        data: data,
        queryParameters: query,
        options: Options(headers: headers),
      ),
      url: url,
      queryParameters: query,
    );
  }

  Future<Response> debouncedRequest({
    required Future<Response> Function() requestFunction,
    required String url,
    Map<String, dynamic>? queryParameters,
  }) async {
    final String key = _generateCacheKey(url, queryParameters);
    // 检查是否已经有一个计时器在运行
    if (_debouncers.containsKey(key)) {
      // 如果存在，则直接返回一个挂起的Future，不进行操作
      return Future.error('$url 请求执行过于频繁');
    }

    // 设置一个定时器防止在设定时间内重复执行
    _debouncers[key] = Timer(Duration(milliseconds: _debounceDuration), () {
      // 定时器完成后清理自己
      _debouncers.remove(key);
    });

    // 创建一个新的Completer
    Completer<Response> completer = Completer();
    try {
      Response response = await requestFunction();
      completer.complete(response);
    } catch (e) {
      completer.completeError(e);
    } finally {
      if (_debouncers.containsKey(key)) {
        Timer? timer = _debouncers[key];
        if (timer != null) {
          timer.cancel();
        }
        _debouncers.remove(key); // 请求完成后清理定时器
      }
    }

    return completer.future;
  }

  String _generateCacheKey(String url, Map<String, dynamic>? params) {
    return '$url-${params.toString()}';
  }

  // 设置OAuth请求头
  void _setOAuthHeader(RequestOptions options) {
    String? accessToken = GlobalData().oauthModel?.accessToken;
    // 客户端时间
    int timestamp = DateTime.now().millisecondsSinceEpoch;
    // 服务端时间
    int miliTimes = timestamp + GlobalData().miliTimeDifference;
    String miliTimestamp = miliTimes.toString();
    String nonce = const Uuid().v4(); // 使用uuid包生成UUID
    String platformNo = 'iOS';
    String version = '5.2.7';
    // MD5 32位加密(access_token+oauth_consumer_key+timestamp+nonce+client_secret+salt)
    var unencryptedSignature =
        '$accessToken${Constant.APP_CLIENT_ID}$miliTimestamp$nonce${Constant.APP_CLIENT_SECRET}${Constant.APP_SALT}';
    var signatureBytes = md5.convert(utf8.encode(unencryptedSignature));
    String signature = signatureBytes.toString();

    // 设置请求头
    options.headers['accessToken'] = accessToken!;
    options.headers['oauthConsumerKey'] = Constant.APP_CLIENT_ID;
    options.headers['timestamp'] = miliTimestamp;
    options.headers['nonce'] = nonce;
    options.headers['platformNo'] = platformNo;
    options.headers['version'] = version;
    options.headers['signature'] = signature;

    // LogManager().debug("options: ${jsonEncode(options.headers)}");
  }

  // 设置SGMW统一服务授权参数请求头
  void _setSGMWUnifiedOAuthHeader(RequestOptions options) {
    String? accessToken = GlobalData().oauthModel?.accessToken;
    // 客户端时间
    int timestamp = DateTime.now().millisecondsSinceEpoch;
    // 服务端时间
    int miliTimes = timestamp + GlobalData().miliTimeDifference;
    String miliTimestamp = miliTimes.toString();
    String nonce = const Uuid().v4(); // 使用uuid包生成UUID
    // MD5 32位加密(access_token+oauth_consumer_key+timestamp+nonce+client_secret+salt)
    var unencryptedSignature =
        '$accessToken${Constant.APP_CLIENT_ID}$miliTimestamp$nonce${Constant.APP_CLIENT_SECRET}${Constant.APP_SALT}';
    var signatureBytes = md5.convert(utf8.encode(unencryptedSignature));
    String signature = signatureBytes.toString();
    // 设置请求头
    options.headers['sgmwaccesstoken'] = accessToken!;
    options.headers['sgmwclientid'] = Constant.APP_CLIENT_ID;
    options.headers['sgmwclientsecret'] = Constant.APP_CLIENT_SECRET;
    options.headers['sgmwtimestamp'] = miliTimestamp;
    options.headers['sgmwnonce'] = nonce;
    options.headers['sgmwsignature'] = signature;
    options.headers['sgmwappcode'] = 'sgmw_llb';
    options.headers['sgmwappversion'] =
        '5.2.12'; //座椅通风加热IOS最低版本5.2.12，安卓最低版本8.2.12
    options.headers['sgmwsystem'] = 'iOS';
    options.headers['sgmwsystemversion'] = '16.3.1';
    options.headers['sgmwplatformno'] = 'iOS';
    // LogManager().debug('请求URL: ${options.uri}');
    // LogManager().debug('请求参数: ${options.data}');
    // LogManager().debug('请求头: ${options.headers}');
  }

  // 设置上传图片认证请求头
  void _setFileUploadOAuthHeader(RequestOptions options) {
    String appID = Constant.kFeiDouZheAppID;
    String appSecret = Constant.kFeiDouZheAppSecret;

    // 客户端时间
    int timestamp = DateTime.now().millisecondsSinceEpoch;
    String miliTimestamp = timestamp.toString();

    String? accessToken = GlobalData().oauthModel?.accessToken;

    // MD5 32位加密
    var unencryptedSignature = '$appID$appSecret$miliTimestamp';
    var signatureBytes = md5.convert(utf8.encode(unencryptedSignature));
    String signature = signatureBytes.toString();

    String contentType = 'multipart/form-data';

    // 设置请求头
    options.headers["appId"] = appID;
    options.headers["timestamp"] = miliTimestamp;
    options.headers["signature"] = signature;
    options.headers["accessToken"] = accessToken;
    options.headers["appCode"] = "sgmw_llb";
    options.headers['Content-Type'] = contentType;
    // LogManager().debug('请求URL: ${options.uri}');
    // LogManager().debug('请求参数: ${options.data}');
    // LogManager().debug('请求头: ${options.headers}');
  }

  // 内部错误码处理
  void _errCodeHandler(Response<dynamic> response) {
    Map<String, dynamic> data = {};
    // 检查响应的数据类型
    if (response.data is String) {
      try {
        // 尝试手动解析 JSON 字符串
        data = jsonDecode(response.data);
        response.data = data;
      } catch (e) {
        // 处理解析错误
        LogManager().debug("JSON 解析错误: $e");
      }
    } else {
      // 如果 data 已经是 Map 类型，则直接使用
      data = response.data;
    }
    if (response.statusCode == 200 && data['result'] == true) {
      // 正常返回
      response.data = data['data'];
    } else {
      APIException apiException = APIException(
        errorType: APIExceptionType.internalError,
        requestOptions: response.requestOptions,
        response: response,
        userMessage: data['errorMessage'],
        errorCode: data['errorCode'],
        message: data['errorMessage'],
      );
      // 正数错误码需要提示给用户
      if (int.tryParse(data['errorCode'])! != 0 &&
          data['errorMessage'] != null) {
        if (!_specialErrorCodes.contains(data['errorCode'])) {
          // 根据网络状态显示友好的错误提示
          _showFriendlyErrorMessage(data['errorMessage']);
          LogManager().debug("errorMessage:" + jsonEncode(data));
          LogManager().debug("url: ${response.realUri}");
        } else {
          // 特殊错误码处理
          if (data['errorCode'] == "160019" || data['errorCode'] == "160023") {
            // 160019 - access_token参数不合法，请检查参数
            // 160023 - access_token已过期，请重新申请
            if (GlobalData().isLogin) {
              GlobalData().clearUser();
              //_refreshToken();
            }
          }
        }
      }
      throw apiException;
    }
  }

  // 根据网络状态显示友好的错误提示
  void _showFriendlyErrorMessage(String originalMessage) async {
    // 如果禁用了错误吐司，直接返回
    if (_suppressErrorToast) {
      return;
    }

    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult == ConnectivityResult.none) {
        // 无网络时显示友好提示
        LoadingManager.showError('啊哦，网络似乎出了点问题…');
      } else {
        // 有网络但请求失败，显示原始错误信息
        LoadingManager.showError(originalMessage);
      }
    } catch (e) {
      // 网络检测失败，默认显示友好提示
      LoadingManager.showError('啊哦，网络似乎出了点问题…');
    }
  }

  // 网络请求错误处理
  APIException _exceptionHandler(DioException e) {
    if (e is APIException) {
      return e;
    }

    // 处理网络连接异常，显示友好提示
    if (e.type == DioExceptionType.connectionError ||
        e.type == DioExceptionType.connectionTimeout ||
        e.type == DioExceptionType.receiveTimeout ||
        e.type == DioExceptionType.sendTimeout) {
      _showFriendlyErrorMessage('网络连接失败');
    }

    APIException apiException = APIException.createApiExceptionFromDioError(e);
    return apiException;
  }

  Future<OauthModel> _refreshToken() async {
    String refreshToken = GlobalData().oauthModel?.refreshToken ?? "";
    String clientId = Constant.APP_CLIENT_ID;
    String clientSecret = Constant.APP_CLIENT_SECRET;
    String state = DateTime.now().toIso8601String();
    OauthModel model = await userAPI.refreshToken(
        "refresh_token", clientId, clientSecret, state, refreshToken);
    GlobalData().oauthModel = model; //设置到全局变量
    SpUtil().setJSON(SP_USER_OAUTH_KEY, model.toJson()); //保存到本地
    return model;
  }
}

extension FileUpload on HttpRequest {
  /// 上传图片
  /// [files] - 图片文件列表
  /// [progress] - 上传进度回调
  /// [isPrivacy] - 是否为私密图片
  /// [isRandName] - 是否使用随机文件名
  /// [isDeleteFlag] - true：未提交到业务接口时自动删除，false：不处理
  Future<List<String>> uploadImages(
    List<ImageProvider> files, {
    ProgressCallback? progress,
    bool isPrivacy = false,
    bool isRandName = true,
    bool isDeleteFlag = false,
  }) async {
    try {
      // 将AssetImage转换为File
      final images = await Future.wait(files.map((provider) async {
        if (provider is AssetImage) {
          return _assetImageToFile(provider);
        }
        throw Exception('Unsupported image provider type');
      }));
      // 压缩图片
      final imageDataList =
          await Future.wait(images.map((image) => _compressImage(image)));
      // 构建请求参数
      final formData = FormData();

      for (var i = 0; i < imageDataList.length; i++) {
        final fileName = 'test${i + 1}.jpg';
        formData.files.addAll([
          MapEntry(
            isPrivacy ? 'file' : 'files',
            MultipartFile.fromBytes(
              imageDataList[i],
              filename: fileName,
              contentType: MediaType('image', 'jpg'),
            ),
          ),
        ]);
      }

      // 私密图片上传额外参数
      if (isPrivacy) {
        formData.fields.addAll([
          const MapEntry('uploadType', '1'),
          MapEntry('randName', isRandName.toString()),
          MapEntry('deleteFlag', isDeleteFlag.toString()),
        ]);
      }

      // 设置上传请求头
      final headers = isPrivacy
          ? await _getPrivacyUploadHeaders()
          : await _getOAuthHeaders();

      final String url = isPrivacy
          ? '${Constant.SGMW_FILE_SERVICE_URL}file/v2/batch/upload'
          : '${Constant.UPLOAD_FILE_URL}/llb/oss/image/upload';

      final response = await dio.post(
        url,
        data: formData,
        options: Options(headers: headers),
        onSendProgress: progress,
      );

      if (response.statusCode == 200) {
        if (isPrivacy) {
          final List<String> fileUrls =
              List<String>.from(response.data['fileUrls']);
          return fileUrls;
        } else {
          final List<String> imageNames = List<String>.from(response.data);
          return imageNames;
        }
      }
      throw Exception(response.data['errorMessage'] ?? 'Upload failed');
    } catch (e) {
      rethrow;
    }
  }

  /// 上传图片
  /// [files] - 图片文件列表
  /// [progress] - 上传进度回调
  /// [isPrivacy] - 是否为私密图片
  /// [isRandName] - 是否使用随机文件名
  /// [isDeleteFlag] - true：未提交到业务接口时自动删除，false：不处理
  Future<List<String>> uploadFiles(
    List<String> files, {
    ProgressCallback? progress,
    bool isPrivacy = false,
    bool isRandName = true,
    bool isDeleteFlag = false,
  }) async {
    try {
      // 压缩图片
      final imageDataList = await Future.wait(files.map(
          (image) => CompressImageUtils().compressImageIsolate(File(image))));
      // 构建请求参数
      final formData = FormData();

      for (var i = 0; i < imageDataList.length; i++) {
        final fileName = 'test${i + 1}.jpg';
        formData.files.addAll([
          MapEntry(
            isPrivacy ? 'file' : 'files',
            MultipartFile.fromBytes(
              imageDataList[i],
              filename: fileName,
              contentType: MediaType('image', 'jpg'),
            ),
          ),
        ]);
      }

      // 私密图片上传额外参数
      if (isPrivacy) {
        formData.fields.addAll([
          const MapEntry('uploadType', '1'),
          MapEntry('randName', isRandName.toString()),
          MapEntry('deleteFlag', isDeleteFlag.toString()),
        ]);
      }

      // 设置上传请求头
      final headers = isPrivacy
          ? await _getPrivacyUploadHeaders()
          : await _getOAuthHeaders();

      final String url = isPrivacy
          ? '${Constant.SGMW_FILE_SERVICE_URL}file/v2/batch/upload'
          : '${Constant.UPLOAD_FILE_URL}/llb/oss/image/upload';

      final response = await dio.post(
        url,
        data: formData,
        options: Options(headers: headers),
        onSendProgress: progress,
      );

      if (response.statusCode == 200) {
        if (isPrivacy) {
          final List<String> fileUrls =
              List<String>.from(response.data['fileUrls']);
          return fileUrls;
        } else {
          final List<String> imageNames = List<String>.from(response.data);
          return imageNames;
        }
      }
      throw Exception(response.data['errorMessage'] ?? 'Upload failed');
    } catch (e) {
      rethrow;
    }
  }

  /// 上传文件
  Future<String> uploadFile(
    File file,
    String fileName, {
    Map<String, dynamic>? params,
    Map<String, String>? headerDic,
    ProgressCallback? progress,
  }) async {
    try {
      final formData = FormData();
      formData.files.add(
        MapEntry(
          'file',
          await MultipartFile.fromFile(
            file.path,
            filename: fileName,
          ),
        ),
      );

      if (params != null) {
        formData.fields.addAll(
          params.entries.map((e) => MapEntry(e.key, e.value.toString())),
        );
      }

      final headers = headerDic ?? await _getOAuthHeaders();

      final response = await dio.post(
        '${Constant.UPLOAD_FILE_URL}/upload',
        data: formData,
        options: Options(headers: headers),
        onSendProgress: progress,
      );

      if (response.data['result'] == true) {
        return response.data['data'];
      }
      throw Exception(response.data['errorMessage'] ?? 'Upload failed');
    } catch (e) {
      rethrow;
    }
  }

  /// 上传视频
  Future<String> uploadVideo(
    File videoFile, {
    ProgressCallback? progress,
  }) async {
    try {
      final now = DateTime.now();
      final fileName =
          'iOS_${now.year}_${now.month}_${now.day}_${now.hour}_${now.minute}_${now.second}.mp4';

      final formData = FormData();
      formData.files.add(
        MapEntry(
          'file',
          await MultipartFile.fromFile(
            videoFile.path,
            filename: fileName,
            contentType: MediaType('video', 'mp4'),
          ),
        ),
      );

      formData.fields.addAll([
        const MapEntry('uploadType', '1'),
        const MapEntry('randName', 'true'),
        const MapEntry('deleteFlag', 'true'),
      ]);

      final headers = await _getPrivacyUploadHeaders();

      final response = await dio.post(
        '${Constant.SGMW_FILE_SERVICE_URL}file/v2/batch/upload',
        data: formData,
        options: Options(headers: headers),
        onSendProgress: progress,
      );

      if (response.data['result'] == true) {
        final List<String> fileUrls =
            List<String>.from(response.data['data']['fileUrls']);
        return fileUrls.first;
      }
      throw Exception(response.data['errorMessage'] ?? 'Upload failed');
    } catch (e) {
      rethrow;
    }
  }

  // 压缩图片
  Future<List<int>> _compressImage(File imageFile) async {
    final bytes = await imageFile.readAsBytes();
    final image = img.decodeImage(bytes);
    if (image == null) return bytes;

    var quality = 100;
    var compressedBytes = img.encodeJpg(image, quality: quality);

    while (compressedBytes.length > 2000 * 1024 && quality > 50) {
      quality -= 10;
      compressedBytes = img.encodeJpg(image, quality: quality);
    }

    return compressedBytes;
  }

  // 获取OAuth授权头
  Future<Map<String, String>> _getOAuthHeaders() async {
    String? accessToken = GlobalData().oauthModel?.accessToken;
    final timestamp =
        DateTime.now().millisecondsSinceEpoch + GlobalData().miliTimeDifference;
    final nonce = const Uuid().v4();

    // MD5签名
    final signature = md5
        .convert(utf8.encode(
            '$accessToken${Constant.APP_CLIENT_ID}$timestamp$nonce${Constant.APP_CLIENT_SECRET}${Constant.APP_SALT}'))
        .toString();

    return {
      'accessToken': accessToken!,
      'oauthConsumerKey': Constant.APP_CLIENT_ID,
      'timestamp': timestamp.toString(),
      'nonce': nonce,
      'signature': signature,
    };
  }

  // 获取私密上传文件的请求头
  Future<Map<String, String>> _getPrivacyUploadHeaders() async {
    String? accessToken = GlobalData().oauthModel?.accessToken;
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();

    const appId = '20210811142614596979';
    const appSecret = 'ZHGIG2SE1GMVAN8QDFZO8XDMPLJNTKTP';

    // MD5签名
    final signature =
        md5.convert(utf8.encode('$appId$appSecret$timestamp')).toString();

    return {
      'Content-Type': 'multipart/form-data',
      'appId': appId,
      'appcode': 'sgmw_llb',
      'timestamp': timestamp,
      'signature': signature,
      'accessToken': accessToken!,
    };
  }

  /// 将AssetImage转换为File
  Future<File> _assetImageToFile(AssetImage assetImage) async {
    // 获取资源路径
    final String assetPath = assetImage.assetName;

    // 读取资源数据
    final ByteData data = await rootBundle.load(assetPath);
    final List<int> bytes = data.buffer.asUint8List();

    // 创建临时文件
    final tempDir = await getTemporaryDirectory();
    final String fileName = assetPath.split('/').last;
    final File tempFile = File('${tempDir.path}/$fileName');

    // 写入数据
    await tempFile.writeAsBytes(bytes, flush: true);

    return tempFile;
  }
}
