// @keepTs
// @ts-nocheck
import { EngineError } from '@rongcloud/imlib';
import { FileDownloadInfo } from '../model/FileDownloadInfo';
/**
 * 聊天页面的各种事件监听
 * @version 1.4.0
 */
export interface FileDownloadListener {
    /**
     * 下载进度变化监听
     * @param type 下载类型
     * @param downloadId 如果是通过MessageId下载，则是messageId，如果是通过文件地址下载，则是底层下载接口返回的uniqueId
     * @param progress 下载进度
     */
    progress?: (info: FileDownloadInfo, progress: number) => void;
    /**
     * 下载成功
     * @param path 下载结果，下载的路径
     */
    success?: (info: FileDownloadInfo, path: string) => void;
    /**
     * 下载失败
     * @param status 下载状态
     */
    failed?: (info: FileDownloadInfo, code: EngineError) => void;
}
