{"name": "webview_flutter_ohos", "version": "1.0.0", "description": "Please describe the basic information.", "author": "", "license": "Apache-2.0", "dependencies": {"@ohos/flutter_ohos": "file:libs/flutter.har"}, "types": "Index.d.ets", "artifactType": "obfuscation", "metadata": {"byteCodeHar": true, "sourceRoots": ["./src/main"], "debug": true, "dependencyPkgVersion": {"@ohos/flutter_ohos": "1.0.0-a9e521ff88", "@tencent/wechat_open_sdk": "1.0.14", "@free/global": "1.0.3", "@rongcloud/imkit": "1.7.1", "@rongcloud/imlib": "1.7.1", "@ohos/mqtt": "2.0.23"}, "declarationEntry": [], "useNormalizedOHMUrl": true}, "compatibleSdkVersion": 12, "compatibleSdkType": "HarmonyOS", "obfuscated": false}