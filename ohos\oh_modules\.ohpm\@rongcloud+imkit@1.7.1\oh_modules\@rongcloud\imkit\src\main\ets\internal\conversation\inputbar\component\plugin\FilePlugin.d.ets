// @keepTs
// @ts-nocheck
/**
 * Created on 2024/09/06
 * <AUTHOR>
 */
import { ConversationIdentifier } from '@rongcloud/imlib';
import { IBoardPlugin } from '../../../../../conversation/inputbar/component/plugin/IBoardPlugin';
/**
 * 文件插件标识
 * @version 1.0.0
 */
declare const FilePluginName: string;
/**
 * 加号扩展栏的文件插件
 * @version 1.0.0
 */
declare class FilePlugin implements IBoardPlugin {
    private conId;
    pluginName(): string;
    obtainTitle(x144: Context): ResourceStr;
    obtainImage(w144: Context): ResourceStr;
    onClick(u144: Context, v144: ConversationIdentifier): void;
    onFilter(t144: ConversationIdentifier): boolean;
    sendFileAction(q144: string[]): Promise<void>;
    private buildMediaMessage;
    private delay;
    private pushFile;
}
export { FilePlugin, FilePluginName };
