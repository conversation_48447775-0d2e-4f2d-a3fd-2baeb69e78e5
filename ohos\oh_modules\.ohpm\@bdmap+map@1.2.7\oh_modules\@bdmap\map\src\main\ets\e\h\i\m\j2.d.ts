            import { OverlayType } from "../../util/b1/c1"; import LRUCache from "../../util/m2/t4"; import type OverlayLayer from "../d1/k1"; import type OverlayListener from "./i2"; import type BaseMap from "../o2"; import type Overlay from "./m"; import type { Overlays } from "../../g1/i1"; import type { RemoveOverlayOptions } from "../../g1/e2"; import type BmLayer from "../c2/q2"; export default class OverlayMgr { private basemap; private overlayLayer; private mOverlayListener; private mBmLayer; overlays: Overlays; filesDir: string; cacheDir: string; densityDPI: number; imageLRUCache: LRUCache; constructor(map: BaseMap, i41: OverlayLayer, j41: BmLayer); init(): void; registOverlayListener(h41: OverlayListener): void; setAppAbility(filesDir: string, cacheDir: string, densityDPI: number): void; addOverlay(f41: Overlay, update?: boolean): this; layerCommit(): void; removeOverlay(d41: Overlay, e41?: boolean): void; removeOverlays(type?: OverlayType): void; crsUpdateDraw(): void; hasOverlay(y40: Overlay): boolean; oTypeRemoveArray(key: number, u40: Array<object>, v40?: boolean): void; oRemoveArray(key?: number, r40?: boolean): RemoveOverlayOptions[]; clear(model?: OverlayType, m40?: Overlay, n40?: boolean): void; destroy(): void; } 