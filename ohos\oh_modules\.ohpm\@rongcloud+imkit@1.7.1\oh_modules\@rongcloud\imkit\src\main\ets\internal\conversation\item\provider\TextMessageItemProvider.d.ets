// @keepTs
// @ts-nocheck
/**
 * Created on 2024/07/09
 * <AUTHOR>
 */
import { TextMessage } from '@rongcloud/imlib';
import { BaseMessageItemProvider } from '../../../../conversation/item/provider/BaseMessageItemProvider';
import { UiMessage } from '../../../../conversation/model/UiMessage';
export declare class TextMessageItemProvider extends BaseMessageItemProvider<TextMessage> {
    getMessageWrapBuilder(): WrappedBuilder<[
        Context,
        UiMessage,
        number
    ]>;
    isShowSummaryName(a210: Context, b210: TextMessage): boolean;
    getSummaryTextByMessageContent(u209: Context, v209: TextMessage): Promise<MutableStyledString>;
    private isDestruct;
}
@Builder
export declare function bindTextMessageData(i209: Context, j209: UiMessage, k209: number): void;
@Component
export declare struct TextMessageView {
    @Require
    @Prop
    context: Context;
    @Require
    @Prop
    index: number;
    @ObjectLink
    uiMessage: UiMessage;
    private textMessage;
    controller: TextController;
    @State
    isClickLook: boolean;
    private styledString;
    private mentionUserIdList;
    private fontColor;
    private userDataListener;
    aboutToAppear(): void;
    aboutToDisappear(): void;
    private updateMentionInfo;
    build(): void;
    /**
     * 获取图片
     * @param resource
     * @returns
     */
    private getPixmapFromMedia;
}
