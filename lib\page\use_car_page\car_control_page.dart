import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:wuling_flutter_app/constant/service_constant.dart';
import 'package:wuling_flutter_app/utils/sp_util.dart';

import '../../utils/manager/dialog_manager.dart';
import '../../utils/manager/loading_manager.dart';
import '../../utils/manager/location_manager.dart';
import '../../utils/manager/ble_manager.dart';
import '../../utils/manager/log_manager.dart';
import '../../widgets/use_car_page_widgets/menu_item_card.dart';
import '../../widgets/use_car_page_widgets/navigation_list_widget.dart' as nav;
import '../../models/car/car_control_item_model.dart';
import '../../models/car/car_service_status_model.dart';
import '../../models/car/car_service_model.dart';
import '../../models/car/car_status_model.dart';
import '../../widgets/common/image_view.dart';

class MyCarPage extends StatefulWidget {
  final List<CarControlItemModel> controlItemList;
  final List<CarControlItemModel> controlSubItemList;
  final List<CarServiceModel> toolServiceList;
  final List<nav.MenuItemCard> navigationList;
  final CarStatusModel? statusModel;
  final void Function({CarControlItemModel carControlItemModel})?
      onControlButtonClicked;
  final void Function(
      {required CarControlItemModel carControlItemModel,
      CarServiceStatusModel? statusModel})? onACControlButtonClicked;
  final void Function({CarServiceModel serviceModel})? onServiceButtonClicked;
  final VoidCallback? onBatteryLayoutClicked;
  final VoidCallback? onChargeMapButtonClicked;
  final VoidCallback? onCarDetailsClicked;
  final bool isNetworkConnected;
  final bool isOfflineMode; // 新增：是否进入离线模式
  final bool? isBluetoothConnected; // 新增：蓝牙连接状态
  final void Function(
          {required CarControlItemModel carControlItemModel,
          required CarServiceStatusModel statusModel})?
      onBleControlButtonClicked; // 新增：离线模式蓝牙控制回调
  final VoidCallback? onConnectBluetoothClicked; // 新增：连接蓝牙回调

  const MyCarPage({
    super.key,
    required this.controlItemList,
    required this.controlSubItemList,
    required this.toolServiceList,
    required this.navigationList,
    this.statusModel,
    this.onControlButtonClicked,
    this.onACControlButtonClicked,
    this.onServiceButtonClicked,
    this.onBatteryLayoutClicked,
    this.onChargeMapButtonClicked,
    this.onCarDetailsClicked,
    this.isNetworkConnected = true,
    this.isOfflineMode = false,
    this.isBluetoothConnected, // 新增：蓝牙连接状态
    this.onBleControlButtonClicked, // 新增：离线模式蓝牙控制回调
    this.onConnectBluetoothClicked, // 新增：连接蓝牙回调
  });

  @override
  State<MyCarPage> createState() => _MyCarPageState();
}

class _MyCarPageState extends State<MyCarPage> {
  // SharedPreferences 键值常量
  static const String SP_CURRENT_SETTING_ELECTRIC_AC_LEVEL_KEY =
      'current_setting_electric_ac_level';
  static const String SP_CURRENT_SETTING_TEMPERATURE_KEY =
      'current_setting_temperature';

  // 从SP读取的空调设置值
  int _savedACLevel = 3; // 电动空调档位，默认3档
  int _savedTemperature = 26; // 空调温度，默认26度

  @override
  void initState() {
    super.initState();
    // 初始化 PageController
    _pageController = PageController();
    // 初始化时加载空调设置（这会自动初始化temperature显示值）
    _loadACSettings();
  }

  @override
  void didUpdateWidget(MyCarPage oldWidget) {
    super.didUpdateWidget(oldWidget);

    // 检查statusModel是否发生变化
    if (oldWidget.statusModel != widget.statusModel) {
      if (widget.statusModel?.acStatus != oldWidget.statusModel?.acStatus) {
        // 强制重新构建以更新背景图
        setState(() {});
      }
    }
  }

  // 获取控制按钮的当前状态图片URL (与CarControlContainerWidget保持一致)
  String getCurrentStatusImageUrl(CarControlItemModel carControlItemModel) {
    // 在离线模式下，返回固定的图片
    if (widget.isOfflineMode &&
        carControlItemModel.serviceModel.serviceCode == 'doorLockStatus') {
      if (carControlItemModel.status == "0") {
        // 闭锁按钮（修复：status="0"对应闭锁）
        return 'assets/images/use_car_page/new_UI/png/offline_lock_pic_new.png';
      } else if (carControlItemModel.status == "1") {
        // 解锁按钮（修复：status="1"对应解锁）
        return 'assets/images/use_car_page/new_UI/png/offline_unlock_pic_new.png';
      }
    }

    // 正常模式下的逻辑
    String imageUrl = '';
    if (carControlItemModel.serviceModel.serviceStatusList != null)
      for (CarServiceStatusModel statusModel
          in carControlItemModel.serviceModel.serviceStatusList!) {
        if (carControlItemModel.status == statusModel.serviceStatusValue) {
          imageUrl = statusModel.serviceStatusImage;
          break;
        }
      }
    return imageUrl;
  }

  // 获取控制按钮的当前状态名称 (与CarControlContainerWidget保持一致)
  String getCurrentStatusName(CarControlItemModel carControlItemModel) {
    // 在离线模式下，返回固定的文案
    if (widget.isOfflineMode &&
        carControlItemModel.serviceModel.serviceCode == 'doorLockStatus') {
      if (carControlItemModel.status == "1") {
        // 解锁按钮（修复：status="1"对应解锁）
        return '闭锁';
      } else if (carControlItemModel.status == "0") {
        // 闭锁按钮（修复：status="0"对应闭锁）
        return '解锁';
      }
    }

    // 正常模式下的逻辑
    String name = '';
    if (carControlItemModel.serviceModel.serviceStatusList != null)
      for (CarServiceStatusModel statusModel
          in carControlItemModel.serviceModel.serviceStatusList!) {
        if (carControlItemModel.status == statusModel.serviceStatusValue) {
          name = statusModel.serviceStatusName;
          break;
        }
      }
    return name;
  }

  // 从controlItemList生成菜单数据
  List<MenuItemCard> get menuList {
    List<CarControlItemModel> filteredList = widget.controlItemList;

    // 在离线模式下，创建固定的解锁和闭锁按钮
    if (widget.isOfflineMode) {
      // 查找 doorLockStatus 服务作为模板
      CarControlItemModel? doorLockItem;
      try {
        doorLockItem = widget.controlItemList
            .where((item) => item.serviceModel.serviceCode == 'doorLockStatus')
            .first;
      } catch (e) {
        doorLockItem = null;
      }

      if (doorLockItem != null) {
        // 创建解锁按钮
        CarControlItemModel unlockButton = CarControlItemModel(
          serviceModel: doorLockItem.serviceModel,
          index: 1, // 修改：解锁按钮放在第二个位置
          status: "1", // 修复：解锁按钮使用status="1"，对应btParam=1解锁
          isControling: false,
        );

        // 创建闭锁按钮
        CarControlItemModel lockButton = CarControlItemModel(
          serviceModel: doorLockItem.serviceModel,
          index: 0, // 修改：闭锁按钮放在第一个位置
          status: "0", // 修复：闭锁按钮使用status="0"，对应btParam=0闭锁
          isControling: false,
        );

        filteredList = [lockButton, unlockButton]; // 修改：闭锁按钮在前，解锁按钮在后
      } else {
        filteredList = [];
      }
    } else {}

    return filteredList.map((controlItem) {
      return MenuItemCard(
        icon: getCurrentStatusImageUrl(controlItem),
        label: getCurrentStatusName(controlItem),
        controlItem: controlItem, // 保存原始数据用于点击事件
      );
    }).toList();
  }

  // 从controlSubItemList生成小菜单数据
  List<MenuItemCard> get smallMenuCardList {
    // 过滤逻辑：如果有空调布局显示，则排除空调相关的item
    List<CarControlItemModel> filteredList = widget.controlSubItemList;

    if (isACAvailable) {
      // 当显示空调布局时，过滤掉serviceCode为'acStatus'的项
      filteredList = widget.controlSubItemList
          .where((item) => item.serviceModel.serviceCode != 'acStatus')
          .toList();
    }

    return filteredList.map((controlItem) {
      return MenuItemCard(
        icon: getCurrentStatusImageUrl(controlItem),
        label: getCurrentStatusName(controlItem),
        info: getControlItemInfo(controlItem),
        controlItem: controlItem, // 保存原始数据用于点击事件
      );
    }).toList();
  }

  // 从toolServiceList生成服务菜单数据 (与CarControlContainerWidget保持一致)
  List<MenuItemCard> get serviceMune {
    return widget.toolServiceList.map((serviceModel) {
      return MenuItemCard(
        icon: getServiceStatusImageUrl(serviceModel),
        label: getServiceStatusName(serviceModel),
        serviceModel: serviceModel, // 保存原始数据用于点击事件
      );
    }).toList();
  }

  // 获取控制项的信息描述
  String getControlItemInfo(CarControlItemModel controlItem) {
    String currentStatusName = getCurrentStatusName(controlItem);
    if (currentStatusName.isNotEmpty) {
      return currentStatusName;
    }
    return "未知状态";
  }

  // 获取服务的当前状态图片URL (与CarControlContainerWidget保持一致)
  String getServiceStatusImageUrl(CarServiceModel serviceModel) {
    String imageUrl = '';
    if (serviceModel.serviceStatusList != null &&
        serviceModel.serviceStatusList!.isNotEmpty) {
      // 如果有状态列表，使用第一个状态的图片
      imageUrl = serviceModel.serviceStatusList!.first.serviceStatusImage;
    }

    // 如果没有状态图片，根据serviceCode使用默认图标
    if (imageUrl.isEmpty) {
      imageUrl = _getDefaultServiceIcon(serviceModel.serviceCode);
    }

    return imageUrl;
  }

  // 获取服务的当前状态名称 (与CarControlContainerWidget保持一致)
  String getServiceStatusName(CarServiceModel serviceModel) {
    String name = '';
    if (serviceModel.serviceStatusList != null &&
        serviceModel.serviceStatusList!.isNotEmpty) {
      // 如果有状态列表，使用第一个状态的名称
      name = serviceModel.serviceStatusList!.first.serviceStatusName;
    }

    // 如果没有状态名称，使用服务名称
    if (name.isEmpty) {
      name = serviceModel.serviceName;
    }

    return name;
  }

  // 根据服务代码获取默认图标路径
  String _getDefaultServiceIcon(String serviceCode) {
    switch (serviceCode) {
      case 'ChargeMap':
      case 'ChargingMap':
        return 'assets/images/use_car_page/new_UI/svg/ic_charging_navigation.svg';
      case 'VehicleInspection':
        return 'assets/images/use_car_page/new_UI/svg/ic_vehicle_inspection.svg';
      case 'VehicleAuth':
        return 'assets/images/use_car_page/new_UI/svg/ic_vehicle_auth.svg';
      case 'FastCharging':
        return 'assets/images/use_car_page/new_UI/svg/ic_fast_charging.svg';
      case 'VehicleReport':
        return 'assets/images/use_car_page/new_UI/svg/ic_vehicle_report.svg';
      case 'SmartInsulation':
        return 'assets/images/use_car_page/new_UI/svg/ic_smart_insulation.svg';
      case 'TrafficManagement':
        return 'assets/images/use_car_page/new_UI/svg/ic_traffic_management.svg';
      default:
        return 'assets/images/use_car_page/new_UI/svg/ic_charging_navigation.svg'; // 默认图标
    }
  }

  var currentPage = 0;
  int temperature = 28; // 这个值会根据空调类型动态调整
  late PageController _pageController;

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  // 获取控制项对应的状态模型
  CarServiceStatusModel? _getStatusModelForControlItem(
      CarControlItemModel controlItem) {
    if (controlItem.serviceModel.serviceStatusList != null &&
        controlItem.serviceModel.serviceStatusList!.isNotEmpty) {
      // 查找当前状态对应的状态模型
      for (CarServiceStatusModel statusModel
          in controlItem.serviceModel.serviceStatusList!) {
        if (controlItem.status == statusModel.serviceStatusValue) {
          return statusModel;
        }
      }
      // 如果没找到当前状态，返回第一个状态模型
      return controlItem.serviceModel.serviceStatusList!.first;
    }
    return null;
  }

  // 检查蓝牙连接状态并执行操作
  void _checkBluetoothConnectionAndExecute(VoidCallback onConnected) {
    // 使用BleManager检查蓝牙连接状态
    if (BleManager().getCurrentStatus() == BleStatus.bleAuthorized) {
      // 蓝牙已授权连接，执行操作
      onConnected();
    } else {
      // 蓝牙未连接，显示提示
      _showBluetoothConnectionToast();
    }
  }

  // 显示蓝牙连接提示toast
  void _showBluetoothConnectionToast() {
    // 使用项目中的LoadingManager显示toast
    LoadingManager.showToast('请先连接蓝牙后再进行车控操作');

    // 延迟一段时间后，如果用户需要连接蓝牙，可以调用连接回调
    // 这里可以根据需要添加额外的逻辑
  }

  // 根据controlSubItemList中是否包含serviceCode = "acStatus"来动态判断是否有空调
  bool get isACAvailable {
    // 首先检查controlSubItemList中是否有空调
    bool hasACInSubList = widget.controlSubItemList
        .any((item) => item.serviceModel.serviceCode == 'acStatus');

    // 如果controlSubItemList中没有，再检查controlItemList
    bool hasACInMainList = widget.controlItemList
        .any((item) => item.serviceModel.serviceCode == 'acStatus');

    bool hasAC = hasACInSubList || hasACInMainList;
    return hasAC;
  }

  var acWidgetType = 0;

  // 根据statusModel的vecChrgStsIndOn字段动态获取充电状态文本
  String _getChargingStatusText() {
    int? chargingStatusValue =
        int.tryParse(widget.statusModel?.vecChrgStsIndOn ?? '');
    if (chargingStatusValue == 1) {
      return "充电中";
    } else {
      return "剩余电量";
    }
  }

  // 获取电池百分比 (从statusModel中的batterySoc字段获取)
  double get batteryPercent {
    if (widget.statusModel?.batterySoc != null) {
      return double.tryParse(widget.statusModel!.batterySoc!) ?? 0.0;
    }
    return 0.0;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFF7F7FA),
      child: Column(
        mainAxisSize: MainAxisSize.min, // 重要：让Column根据内容调整大小
        children: [
          // 只有当menuList不为空时才显示车控列表和指示器
          if (menuList.isNotEmpty) ...[
            buildMenuPagerWidget(menuList),
            SizedBox(height: 10),
            buildIndicatorWidget(),
            SizedBox(height: 10),
          ],
          buildBatteryAndACWidget(),
          // 完全移除电池和小菜单之间的间距
          // // 导航列表 (爱车体检、座椅通风等) - 放在空调+电量布局下面
          // buildNavigationList(),
          // SizedBox(height: 10),
          // 小菜单卡片的显示逻辑：简化条件，只要有空调就显示
          if (smallMenuCardList.isNotEmpty && isACAvailable) ...[
            Transform.translate(
              offset: Offset(0, -15), // 向上移动15像素
              child: buildSmallMenuCard(smallMenuCardList),
            ),
          ],
          SizedBox(height: isACAvailable ? 5 : 20), // 没有空调时增加间距，避免与电池布局太近
          buildServiceMenu(serviceMune),
          SizedBox(height: 10),
        ],
      ),
    );
  }

  buildMenuPagerWidget(List<MenuItemCard> menuList) {
    return SizedBox(
      height: 78,
      child: PageView.builder(
        controller: _pageController, // 使用显式的 PageController
        itemCount: (menuList.length / 4).ceil(),
        itemBuilder: (context, pageIndex) {
          int start = pageIndex * 4;
          int end =
              (start + 4) > menuList.length ? menuList.length : (start + 4);
          List<MenuItemCard> pageItems = menuList.sublist(start, end);
          return buildPageItemWidget(pageItems, (item) {
            // 处理菜单项点击事件
            if (item.controlItem != null) {
              // 在离线模式下，使用蓝牙控制方法
              if (widget.isOfflineMode &&
                  widget.onBleControlButtonClicked != null) {
                // 先检查蓝牙连接状态
                _checkBluetoothConnectionAndExecute(() {
                  // 获取对应的状态模型
                  CarServiceStatusModel? statusModel =
                      _getStatusModelForControlItem(item.controlItem!);
                  if (statusModel != null) {
                    widget.onBleControlButtonClicked!(
                      carControlItemModel: item.controlItem!,
                      statusModel: statusModel,
                    );
                  } else {}
                });
              } else {
                // 正常模式，使用原有的网络控制方法
                if (widget.onControlButtonClicked != null) {
                  widget.onControlButtonClicked!(
                      carControlItemModel: item.controlItem!);
                }
              }
            }
          });
        },
        onPageChanged: (index) {
          setState(() {
            currentPage = index;
          });
        },
      ),
    );
  }

  Widget buildPageItemWidget(
      List<MenuItemCard> menuList, Function(MenuItemCard) onTap) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20),
      child: Wrap(
        spacing: 12, // 水平间距
        runSpacing: 12, // 垂直间距
        alignment: WrapAlignment.start, // 从左边开始对齐
        children: menuList
            .map(
              (item) => SizedBox(
                width: (MediaQuery.of(context).size.width - 40 - 36) /
                    4, // 计算每个项目的宽度：(屏幕宽度 - 左右边距40 - 3个间距36) / 4
                height:
                    (MediaQuery.of(context).size.width - 40 - 36) / 4, // 保持正方形
                child: buildMenuItemWidget(item.icon, item.label, () {
                  onTap.call(item);
                }),
              ),
            )
            .toList(),
      ),
    );
  }

  Widget buildMenuItemWidget(
      String imageUrl, String label, VoidCallback onTap) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: Color(0x0a000000),
            spreadRadius: 0,
            blurRadius: 4,
            offset: Offset(0, 0),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.all(8),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 图标 - 使用ImageView来显示网络图片或本地图片
                SizedBox(
                  width: 28,
                  height: 28,
                  child: ImageView(
                    imageUrl,
                    fit: BoxFit.contain,
                    width: 28,
                    height: 28,
                  ),
                ),
                const SizedBox(height: 6),
                // 文字
                Flexible(
                  child: Text(
                    label,
                    style: const TextStyle(
                      fontSize: 11,
                      color: Color(0xff383A40),
                      fontWeight: FontWeight.w400,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  buildIndicatorWidget() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        (menuList.length / 4).ceil(),
        (index) => Container(
          margin: EdgeInsets.symmetric(horizontal: 5),
          width: 8,
          height: 4,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(2),
            shape: BoxShape.rectangle,
            color: currentPage == index ? Color(0xFF008DFF) : Color(0xFFD8D8D8),
          ),
        ),
      ),
    );
  }

  buildBatteryAndACWidget() {
    Widget rightWidget;
    // 修改逻辑：只要有空调就显示空调布局，不再依赖网络状态
    if (isACAvailable) {
      // 有空调时，显示空调（离线模式时会在buildACWidget中添加蒙版）
      rightWidget = buildACWidget();
    } else {
      // 无空调时，显示小菜单
      rightWidget = buildSmallMenuCardVertical(smallMenuCardList);
    }
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start, // 让Row中的子组件顶部对齐
      children: [
        SizedBox(width: 20),
        Expanded(child: buildBatteryWidget()), // 充电布局始终显示
        SizedBox(width: 10),
        Expanded(child: rightWidget),
        SizedBox(width: 20),
      ],
    );
  }

  buildBatteryWidget() {
    Widget batteryCard = Card(
      elevation: 2,
      color: Colors.white,
      margin: EdgeInsets.zero, // 移除Card的默认margin，确保左右对齐
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: SizedBox(
        height: 160, // 只约束高度，让宽度由Expanded决定
        child: Column(
          children: [
            Expanded(child: buildBatteryTopWidget()),
            Divider(
                thickness: 1,
                color: Color(0x0D000000),
                indent: 20,
                endIndent: 20,
                height: 0),
            Expanded(child: Center(child: buildBatteryBottomWidget())),
          ],
        ),
      ),
    );

    // 在离线模式时添加蒙版效果并禁用点击
    if (widget.isOfflineMode) {
      return SizedBox(
        height: 160, // 只约束高度，让宽度由Expanded决定
        child: Stack(
          children: [
            batteryCard,
            // 蒙版层 - 精确覆盖Card内容区域
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.8), // 使用白色+透明度的方式
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      // 正常模式，可点击
      return GestureDetector(
        onTap: () {
          // 点击整个电量布局跳转到车辆详情页面
          if (widget.onCarDetailsClicked != null) {
            widget.onCarDetailsClicked!();
          }
        },
        child: batteryCard,
      );
    }
  }

  buildBatteryTopWidget() {
    return Row(
      children: [
        SizedBox(width: 20),
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              textBaseline: TextBaseline.alphabetic,
              crossAxisAlignment: CrossAxisAlignment.baseline,
              children: [
                Text(
                  "${batteryPercent.toInt()}",
                  style: TextStyle(
                      color: widget.statusModel?.vecChrgStsIndOn == "1"
                          ? Color(0xFF007AFF) // 充电中时显示蓝色
                          : Color(0xff383a40), // 其他状态显示原来的深灰色
                      fontSize: 30,
                      fontWeight: FontWeight.bold,
                      height: 1.0),
                ),
                Text("%",
                    style: TextStyle(
                        color: widget.statusModel?.vecChrgStsIndOn == "1"
                            ? Color(0xFF007AFF) // 充电中时显示蓝色
                            : Color(0xff383a40), // 其他状态显示原来的深灰色
                        fontSize: 10)),
              ],
            ),
            Text(
              _getChargingStatusText(),
              style: TextStyle(
                  color: Color(0xff686B78), fontSize: 12, height: 1.0),
            ),
          ],
        ),
        Spacer(),
        buildChargingWidget(),
        SizedBox(width: 20),
      ],
    );
  }

  Widget buildChargingWidget() {
    Color color;
    if (batteryPercent < 20) {
      color = Colors.red;
    } else if (batteryPercent < 30) {
      color = Colors.amber;
    } else {
      color = Colors.blue;
    }
    return Stack(
      alignment: Alignment.center,
      children: [
        // 电池边框 SVG 图
        SvgPicture.asset(
            "assets/images/use_car_page/new_UI/svg/ic_battery_border.svg",
            width: 25,
            height: 25,
            fit: BoxFit.cover),
        Positioned(
          right: 7.5,
          left: 8.5,
          bottom: 3,
          top: 5,
          child: Align(
            alignment: Alignment.bottomCenter,
            child: FractionallySizedBox(
              heightFactor: batteryPercent / 100,
              child: Container(
                decoration: BoxDecoration(
                    color: color, borderRadius: BorderRadius.circular(2)),
              ),
            ),
          ),
        ),
        // 充电中的黄色电池图标 - 只在vecChrgStsIndOn为"1"时显示
        if (widget.statusModel?.vecChrgStsIndOn == "1")
          Image.asset(
            "assets/images/use_car_page/new_UI/png/ic_battery_yellow.png",
            width: 16,
            height: 14,
            fit: BoxFit.contain,
          ),
      ],
    );
  }

  buildBatteryBottomWidget() {
    return ElevatedButton(
      onPressed: () {
        // 点击充电地图按钮的处理逻辑
        if (widget.onChargeMapButtonClicked != null) {
          widget.onChargeMapButtonClicked!();
        }
      },
      style: ElevatedButton.styleFrom(
        padding: EdgeInsets.zero,
        elevation: 0,
        fixedSize: Size(120, 50),
        backgroundColor: Color(0xFFF9F9F9),
        // 背景色
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(25), // 圆角半径
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Image.asset(
              "assets/images/use_car_page/new_UI/png/ic_charging_map.png",
              width: 25,
              height: 25,
              fit: BoxFit.cover),
          SizedBox(width: 6),
          Text('充电地图',
              style: TextStyle(color: Color(0xFF383A40), fontSize: 12)),
        ],
      ),
    );
  }

  int getACType() {
    // 检查数据是否已加载
    if (widget.controlSubItemList.isEmpty && widget.controlItemList.isEmpty) {
      return 0;
    }

    // 首先在controlSubItemList中查找serviceCode为'acStatus'的项
    for (CarControlItemModel item in widget.controlSubItemList) {
      if (item.serviceModel.serviceCode == 'acStatus') {
        // 空调类型应该是固定的，不应该根据当前状态变化
        // 我们查找所有可用的serviceStatusAction，取最大值作为空调类型
        if (item.serviceModel.serviceStatusList != null) {
          int maxACType = 0;
          for (CarServiceStatusModel statusModel
              in item.serviceModel.serviceStatusList!) {
            int acType =
                int.tryParse(statusModel.serviceStatusAction ?? '0') ?? 0;
            if (acType > maxACType) {
              maxACType = acType;
            }
          }

          return maxACType;
        }
        break;
      }
    }

    // 如果在controlSubItemList中没找到，再从controlItemList中查找
    for (CarControlItemModel item in widget.controlItemList) {
      if (item.serviceModel.serviceCode == 'acStatus') {
        if (item.serviceModel.serviceStatusList != null) {
          for (CarServiceStatusModel statusModel
              in item.serviceModel.serviceStatusList!) {
            if (item.status == statusModel.serviceStatusValue) {
              int acType =
                  int.tryParse(statusModel.serviceStatusAction ?? '0') ?? 0;

              return acType;
            }
          }
        }
        break;
      }
    }

    // 默认返回0（关闭状态）
    return 0;
  }

  // 获取不同空调类型的默认温度/档位值
  int getDefaultTemperature() {
    int acType = getACType();

    if (acType == 3 || acType == 5) {
      // 电动空调默认3档
      return 3;
    } else if (acType == 2 || acType == 4) {
      // 自动空调默认26度
      return 26;
    } else {
      // 普通空调默认28度
      return 28;
    }
  }

  // 获取当前显示的温度/档位值（从SharedPreferences获取）
  int getCurrentTemperature() {
    int acType = getACType();

    if (acType == 3 || acType == 5) {
      // 电动空调：从SP_CURRENT_SETTING_ELECTRIC_AC_LEVEL_KEY获取档位
      int savedLevel = _getSavedACLevel();
      // 确保temperature变量也更新为档位值
      if (temperature < 1 || temperature > 6) {
        temperature = savedLevel;
      }
      return savedLevel;
    } else {
      // 自动空调和普通空调：从SP_CURRENT_SETTING_TEMPERATURE_KEY获取温度
      int savedTemp = _getSavedTemperature();
      // 确保temperature变量也更新为温度值
      if (temperature < 17 || temperature > 33) {
        temperature = savedTemp;
      }
      return savedTemp;
    }
  }

  // 从SharedPreferences获取保存的空调档位
  int _getSavedACLevel() {
    return _savedACLevel; // 返回从SP加载的档位值
  }

  // 从SharedPreferences获取保存的温度
  int _getSavedTemperature() {
    return _savedTemperature; // 返回从SP加载的温度值
  }

  // 保存空调档位到SharedPreferences
  void _saveACLevel(int level) {
    _savedACLevel = level; // 立即更新本地变量
    SpUtil()
        .setDouble(SP_CURRENT_SETTING_ELECTRIC_AC_LEVEL_KEY, level.toDouble());
  }

  // 保存温度到SharedPreferences
  void _saveTemperature(int temp) {
    _savedTemperature = temp; // 立即更新本地变量
    SpUtil().setDouble(SP_CURRENT_SETTING_TEMPERATURE_KEY, temp.toDouble());
  }

  // 从SharedPreferences加载空调设置
  void _loadACSettings() {
    setState(() {
      // 使用SpUtil读取缓存值，与弹窗保持一致
      double savedACLevel =
          SpUtil().getDouble(SP_CURRENT_SETTING_ELECTRIC_AC_LEVEL_KEY);
      double savedTemperature =
          SpUtil().getDouble(SP_CURRENT_SETTING_TEMPERATURE_KEY);

      // 如果缓存值为0（未设置），使用默认值
      _savedACLevel = savedACLevel != 0 ? savedACLevel.toInt() : 3;
      _savedTemperature = savedTemperature != 0 ? savedTemperature.toInt() : 26;

      // 根据空调类型初始化temperature显示值
      _initializeTemperatureDisplay();
    });
  }

  // 根据空调类型初始化temperature显示值
  void _initializeTemperatureDisplay() {
    try {
      int acType = getACType();

      if (acType == 3 || acType == 5) {
        // 电动空调：使用档位值
        temperature = _savedACLevel;
      } else if (acType == 2 || acType == 4) {
        // 自动空调：使用温度值
        temperature = _savedTemperature;
      } else {
        // 普通空调：使用默认温度
        temperature = _savedTemperature;
      }
    } catch (e) {
      // 出错时使用默认值
      temperature = 26;
    }
  }

  // 根据acStatus获取空调状态文字
  String getACStatusText() {
    if (widget.statusModel?.acStatus != null) {
      int acStatus = int.tryParse(widget.statusModel!.acStatus.toString()) ?? 0;
      int acType = getACType();

      if (acStatus == 0) {
        return '空调未开';
      } else {
        // 空调开启状态
        if (acType == 2 || acType == 4) {
          // 空调类型2/4：显示车内温度
          if (widget.statusModel?.interiorTemperature != null) {
            String interiorTemperature =
                widget.statusModel!.interiorTemperature.toString();
            String tempText = '车内温度 $interiorTemperature℃';

            return tempText;
          } else {
            // 如果没有温度数据，显示默认文字

            return '空调已开启';
          }
        } else {
          // 其他空调类型：显示制冷中/制暖中
          switch (acStatus) {
            case 1:
              return '制冷中';
            case 2:
              return '制暖中';
            default:
              return '空调已开启';
          }
        }
      }
    }

    return '空调未开';
  }

  // 根据acStatus获取空调背景图片
  String getACBackgroundImage() {
    if (widget.statusModel?.acStatus != null) {
      int acStatus = int.tryParse(widget.statusModel!.acStatus.toString()) ?? 0;
      int acType = getACType();
      LogManager().debug('当前空调状态：  ' + widget.statusModel!.acStatus.toString());
      if (acStatus == 0) {
        return 'assets/images/use_car_page/new_UI/png/ic_ac_close_bg.png';
      } else {
        // 空调开启状态
        if (acType == 0 || acType == 1) {
          // acTypeRegular (普通空调) - 根据具体状态显示不同背景图
          if (acStatus == 1) {
            // 制冷状态

            return 'assets/images/use_car_page/new_UI/png/ic_ac_cool_bg.png';
          } else if (acStatus == 2) {
            // 制暖状态

            return 'assets/images/use_car_page/new_UI/png/ic_ac_heating_bg.png';
          } else {}
        } else if (acType == 3 || acType == 5) {
          // acTypeElectric (电动空调) - 根据acStatus显示不同背景图
          if (acStatus == 2) {
            // 电动空调制暖状态
            print(
                'DEBUG: 返回背景图: ic_ac_heating_bg.png (电动空调制暖状态，acStatus=$acStatus)');
            return 'assets/images/use_car_page/new_UI/png/ic_ac_heating_bg.png';
          } else {
            // 电动空调制冷状态或其他状态
            print(
                'DEBUG: 返回背景图: ic_ac_cool_bg.png (电动空调制冷状态，acStatus=$acStatus)');
            return 'assets/images/use_car_page/new_UI/png/ic_ac_cool_bg.png';
          }
        } else {
          // acTypeAuto 和其他类型 - 开启状态统一显示制冷背景图

          // 自动空调开启时（无论状态1或2）都显示制冷背景

          return 'assets/images/use_car_page/new_UI/png/ic_ac_cool_bg.png';
        }
      }
    }

    return 'assets/images/use_car_page/new_UI/png/ic_ac_close_bg.png';
  }

  // 根据acStatus获取空调开关图标
  String getACIconImage() {
    if (widget.statusModel?.acStatus != null) {
      int acStatus = int.tryParse(widget.statusModel!.acStatus.toString()) ?? 0;

      if (acStatus == 0) {
        String iconPath =
            'assets/images/use_car_page/new_UI/svg/ic_ac_close.svg';

        return iconPath;
      } else {
        String iconPath =
            'assets/images/use_car_page/new_UI/svg/ic_ac_open.svg';

        return iconPath;
      }
    }
    String iconPath = 'assets/images/use_car_page/new_UI/svg/ic_ac_close.svg';

    return iconPath;
  }

  // 根据acStatus获取制暖图标
  String getACHeatingIcon() {
    if (widget.statusModel?.acStatus != null) {
      int acStatus = int.tryParse(widget.statusModel!.acStatus.toString()) ?? 0;
      if (acStatus == 2) {
        // 制暖状态
        return 'assets/images/use_car_page/new_UI/svg/ic_ac_heating_on.svg';
      } else {
        return 'assets/images/use_car_page/new_UI/svg/ic_ac_heating_off.svg';
      }
    }
    return 'assets/images/use_car_page/new_UI/svg/ic_ac_heating_off.svg';
  }

  // 根据acStatus获取制冷图标
  String getACCoolingIcon() {
    if (widget.statusModel?.acStatus != null) {
      int acStatus = int.tryParse(widget.statusModel!.acStatus.toString()) ?? 0;
      if (acStatus == 1) {
        // 制冷状态
        return 'assets/images/use_car_page/new_UI/svg/ic_ac_cooling_on.svg';
      } else {
        return 'assets/images/use_car_page/new_UI/svg/ic_ac_cooling_off.svg';
      }
    }
    return 'assets/images/use_car_page/new_UI/svg/ic_ac_cooling_off.svg';
  }

  buildACWidget() {
    Widget acWidget;
    int acType = getACType(); // 获取空调类型

    if (acType == 3 || acType == 5) {
      // 电动空调

      acWidget = acTypeElectric();
    } else if (acType == 2 || acType == 4) {
      // 自动空调

      acWidget = acTypeAuto();
    } else {
      // 普通空调 (包括0、1及其他值)

      acWidget = acTypeRegular();
    }

    // 在离线模式时添加蒙版效果并禁用点击
    if (widget.isOfflineMode) {
      return SizedBox(
        height: 160, // 只约束高度，让宽度由Expanded决定
        child: Stack(
          children: [
            acWidget,
            // 蒙版层 - 精确覆盖Card内容区域
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.8), // 使用白色+透明度的方式
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      return acWidget;
    }
  }

  acTypeElectric() {
    // 每次构建时重新从SharedPreferences读取最新值
    _loadACSettings();

    return GestureDetector(
      onTap: () {
        // 检查空调类型和状态
        int acType = getACType();
        int currentACStatus = 0;
        if (widget.statusModel?.acStatus != null) {
          currentACStatus =
              int.tryParse(widget.statusModel!.acStatus.toString()) ?? 0;
        }

        if (acType == 5 && currentACStatus != 0) {
          // 空调5开启时，点击布局直接调用空调开关的关闭逻辑
          _turnOffAC();
        } else if (acType == 3 && currentACStatus != 0) {
          // acType=3且空调开启时，点击布局打开弹窗

          if (widget.onControlButtonClicked != null) {
            CarControlItemModel? acControlItem = _getACControlItem();
            if (acControlItem != null) {
              widget.onControlButtonClicked!(
                  carControlItemModel: acControlItem);
            }
          }
        } else {
          // 其他情况使用原有逻辑

          _handleACLayoutClick();
        }
      },
      child: Card(
        elevation: 2,
        color: Colors.white,
        margin: EdgeInsets.zero, // 移除Card的默认margin，确保左右对齐
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            image: DecorationImage(
                image: AssetImage(getACBackgroundImage()), fit: BoxFit.cover),
          ),
          clipBehavior: Clip.antiAlias,
          height: 160, // 只约束高度，让宽度由Expanded决定
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(getACStatusText(),
                  style: TextStyle(color: Color(0xFF686B78), fontSize: 12)),
              SizedBox(height: 20),
              Row(
                children: [
                  // 只有空调5在关闭时隐藏减少按钮，其他空调类型正常显示
                  if (getACType() != 5 ||
                      (int.tryParse(widget.statusModel?.acStatus?.toString() ??
                                  '0') ??
                              0) ==
                          0)
                    InkWell(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 10),
                        child: SvgPicture.asset(
                            "assets/images/use_car_page/new_UI/svg/ic_reduce.svg",
                            width: 25,
                            height: 25,
                            fit: BoxFit.cover),
                      ),
                      onTap: () {
                        print('🔥🔥🔥 【acTypeElectric减少按钮】被点击 🔥🔥🔥');

                        // 检查是否为acType=3且空调开启的情况
                        int acType = getACType();
                        int currentACStatus = 0;
                        if (widget.statusModel?.acStatus != null) {
                          currentACStatus = int.tryParse(
                                  widget.statusModel!.acStatus.toString()) ??
                              0;
                        }

                        print(
                            '【acTypeElectric减少按钮】acType: $acType, currentACStatus: $currentACStatus');

                        if (acType == 3 && currentACStatus != 0) {
                          // acType=3且空调开启时，点击按钮打开弹窗
                          print('【acTypeElectric减少按钮】✅ acType=3且空调开启，打开弹窗');
                          if (widget.onControlButtonClicked != null) {
                            CarControlItemModel? acControlItem =
                                _getACControlItem();
                            if (acControlItem != null) {
                              widget.onControlButtonClicked!(
                                  carControlItemModel: acControlItem);
                            }
                          }
                        } else {
                          // 其他情况使用原有逻辑
                          print('【acTypeElectric减少按钮】使用原有逻辑，调整档位');
                          setState(() {
                            // 电动空调档位限制：最低1档，超出范围不改变状态
                            if (temperature > 1) {
                              temperature = temperature - 1;
                              _saveACLevel(
                                  temperature); // 保存档位到SharedPreferences
                            }
                          });
                        }
                      },
                    ),
                  Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      textBaseline: TextBaseline.alphabetic,
                      crossAxisAlignment: CrossAxisAlignment.baseline,
                      children: [
                        Text(
                          "${getCurrentTemperature()}",
                          style: TextStyle(
                              color: Color(0xFF383A40),
                              fontSize: 30,
                              fontWeight: FontWeight.bold,
                              height: 1.0),
                        ),
                        Text("档",
                            style: TextStyle(
                                color: Color(0xFF383A40), fontSize: 10)),
                      ],
                    ),
                  ),
                  // 只有空调5在关闭时隐藏增加按钮，其他空调类型正常显示
                  if (getACType() != 5 ||
                      (int.tryParse(widget.statusModel?.acStatus?.toString() ??
                                  '0') ??
                              0) ==
                          0)
                    InkWell(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 10),
                        child: SvgPicture.asset(
                            "assets/images/use_car_page/new_UI/svg/ic_increase.svg",
                            width: 25,
                            height: 25,
                            fit: BoxFit.cover),
                      ),
                      onTap: () {
                        print('🔥🔥🔥 【acTypeElectric增加按钮】被点击 🔥🔥🔥');

                        // 检查是否为acType=3且空调开启的情况
                        int acType = getACType();
                        int currentACStatus = 0;
                        if (widget.statusModel?.acStatus != null) {
                          currentACStatus = int.tryParse(
                                  widget.statusModel!.acStatus.toString()) ??
                              0;
                        }

                        if (acType == 3 && currentACStatus != 0) {
                          // acType=3且空调开启时，点击按钮打开弹窗

                          if (widget.onControlButtonClicked != null) {
                            CarControlItemModel? acControlItem =
                                _getACControlItem();
                            if (acControlItem != null) {
                              widget.onControlButtonClicked!(
                                  carControlItemModel: acControlItem);
                            }
                          }
                        } else {
                          // 其他情况使用原有逻辑

                          setState(() {
                            // 电动空调档位限制：最高6档，超出范围不改变状态
                            if (temperature < 6) {
                              temperature = temperature + 1;
                              _saveACLevel(
                                  temperature); // 保存档位到SharedPreferences
                            }
                          });
                        }
                      },
                    ),
                ],
              ),
              SizedBox(height: 20),
              InkWell(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 10),
                  child: SvgPicture.asset(
                    getACIconImage(),
                    width: 25,
                    height: 25,
                    fit: BoxFit.cover,
                  ),
                ),
                onTap: () {
                  // 参考AcSettingDialog开关按钮逻辑，灵活传递temperature参数
                  CarControlItemModel? acControlItem = _getACControlItem();
                  if (acControlItem != null &&
                      widget.onACControlButtonClicked != null) {
                    // 获取当前状态模型
                    CarServiceStatusModel? currentStatus =
                        acControlItem.currentStatus();
                    if (currentStatus != null) {
                      // 根据空调类型更新temperature参数
                      CarServiceStatusModel? updatedStatus =
                          _updateStatusModelWithCurrentTemperature(
                              currentStatus);

                      // 传递更新后的状态模型
                      widget.onACControlButtonClicked!(
                        carControlItemModel: acControlItem,
                        statusModel: updatedStatus ?? currentStatus,
                      );
                    }
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 获取空调控制项
  CarControlItemModel? _getACControlItem() {
    // 先从controlSubItemList查找
    for (CarControlItemModel item in widget.controlSubItemList) {
      if (item.serviceModel.serviceCode == 'acStatus') {
        return item;
      }
    }

    // 如果没找到，从controlItemList查找
    for (CarControlItemModel item in widget.controlItemList) {
      if (item.serviceModel.serviceCode == 'acStatus') {
        return item;
      }
    }

    return null;
  }

  // 参考AcSettingDialog，根据当前温度/档位更新状态模型参数
  CarServiceStatusModel? _updateStatusModelWithCurrentTemperature(
      CarServiceStatusModel statusModel) {
    // 获取空调类型
    int acType = getACType();

    if (acType == 2 || acType == 4) {
      // 自动空调：使用温度参数
      return _updateParamModel(
          serviceStatusModel: statusModel,
          paramName: 'temperature',
          paramValue: temperature.toStringAsFixed(0));
    } else if (acType == 3 || acType == 5) {
      // 电动空调：使用档位参数
      return _updateParamModel(
          serviceStatusModel: statusModel,
          paramName: 'temperature',
          paramValue: temperature.toStringAsFixed(0));
    }

    // 其他类型直接返回原状态模型
    return statusModel;
  }

  // 参考AcSettingDialog的updateParamModel方法
  CarServiceStatusModel? _updateParamModel(
      {required CarServiceStatusModel? serviceStatusModel,
      required String paramName,
      required String? paramValue}) {
    if (serviceStatusModel == null) return null;

    Map<String, dynamic> serviceStatusMap = serviceStatusModel.toJson();
    if (serviceStatusMap.containsKey('serviceStatusParamList')) {
      for (Map<String, dynamic> param
          in serviceStatusMap['serviceStatusParamList']) {
        if (param.containsKey('paramName')) {
          if (param['paramName'] == paramName && paramValue != null) {
            param['paramValue'] = paramValue;
            break;
          }
        }
      }
    }
    return CarServiceStatusModel.fromJson(serviceStatusMap);
  }

  // 根据值查找对应的StatusModel
  CarServiceStatusModel? _findStatusModelByValue(
      CarControlItemModel controlItem, String value) {
    if (controlItem.serviceModel.serviceStatusList != null) {
      for (int i = 0;
          i < controlItem.serviceModel.serviceStatusList!.length;
          i++) {
        CarServiceStatusModel statusModel =
            controlItem.serviceModel.serviceStatusList![i];

        if (statusModel.serviceStatusValue == value) {
          return statusModel;
        }
      }
    } else {}
    return null;
  }

  // 处理空调布局点击事件
  void _handleACLayoutClick() {
    // 打印空调类型信息
    int acType = getACType();

    if (acType == 2 || acType == 4) {
    } else if (acType == 0 || acType == 1) {
    } else {}

    // 检查当前空调状态
    if (widget.statusModel?.acStatus != null) {
      int acStatus = int.tryParse(widget.statusModel!.acStatus.toString()) ?? 0;

      if (acStatus == 0) {
        // 空调关闭时，弹出控制弹窗
        _showACControlDialog();
        return;
      } else {
        // 空调开启状态的处理 - 根据空调类型区分

        if (acType == 3) {
          // acType=3的电动空调：开启时点击布局打开弹窗

          _showACControlDialog();
          return;
        } else {
          // 其他类型空调：开启时点击布局直接关闭空调

          _turnOffAC();
          return;
        }
      }
    } else {
      // acStatus为null时，弹出控制弹窗
      _showACControlDialog();
      return;
    }
  }

  // 弹出空调控制弹窗
  void _showACControlDialog() {
    // 从controlSubItemList中查找serviceCode = "acStatus"的控制项
    for (CarControlItemModel item in widget.controlSubItemList) {
      if (item.serviceModel.serviceCode == 'acStatus') {
        if (widget.onControlButtonClicked != null) {
          widget.onControlButtonClicked!(carControlItemModel: item);
        }
        return;
      }
    }

    // 如果在controlSubItemList中没找到，再从controlItemList中查找
    for (CarControlItemModel item in widget.controlItemList) {
      if (item.serviceModel.serviceCode == 'acStatus') {
        if (widget.onControlButtonClicked != null) {
          widget.onControlButtonClicked!(carControlItemModel: item);
        }
        return;
      }
    }
  }

  // 直接关闭空调 - 调用空调开关按钮的逻辑
  void _turnOffAC() {
    print('【_turnOffAC】开始关闭空调');
    // 参考空调开关按钮逻辑
    CarControlItemModel? acControlItem = _getACControlItem();
    if (acControlItem != null && widget.onACControlButtonClicked != null) {
      // 获取当前状态模型
      CarServiceStatusModel? currentStatus = acControlItem.currentStatus();
      if (currentStatus != null) {
        print('【_turnOffAC】找到当前状态模型: ${currentStatus.serviceStatusValue}');
        // 根据空调类型更新temperature参数
        CarServiceStatusModel? updatedStatus =
            _updateStatusModelWithCurrentTemperature(currentStatus);

        // 传递更新后的状态模型
        widget.onACControlButtonClicked!(
          carControlItemModel: acControlItem,
          statusModel: updatedStatus ?? currentStatus,
        );
        print('【_turnOffAC】调用空调开关按钮逻辑完成');
      } else {
        print('【_turnOffAC】未找到当前状态模型');
      }
    } else {
      print('【_turnOffAC】未找到空调控制项或回调为null');
    }
  }

  // 处理空调按钮点击事件（制暖/制冷按钮）
  void _handleACButtonClick(String buttonType) {
    // 检查当前空调状态
    if (widget.statusModel?.acStatus != null) {
      int acStatus = int.tryParse(widget.statusModel!.acStatus.toString()) ?? 0;

      if (acStatus != 0) {
        // 空调开启状态，需要根据当前状态和点击的按钮类型进行判断
        if (acStatus == 1) {
          // 当前是制冷状态
          if (buttonType == 'cooling') {
            // 点击制冷按钮，直接关闭空调

            _turnOffAC();
            return;
          } else if (buttonType == 'heating') {
            // 点击制暖按钮，提示需要先关闭制冷
            LoadingManager.showToast('请关闭制冷后使用');
            return;
          }
        } else if (acStatus == 2) {
          // 当前是制暖状态
          if (buttonType == 'heating') {
            // 点击制暖按钮，直接关闭空调

            _turnOffAC();
            return;
          } else if (buttonType == 'cooling') {
            // 点击制冷按钮，提示需要先关闭制暖
            LoadingManager.showToast('请关闭制暖后使用');
            return;
          }
        } else {
          // 其他开启状态，直接关闭空调

          _turnOffAC();
          return;
        }
      } else {
        // 空调关闭时，执行对应的制暖或制冷功能
        if (buttonType == 'heating') {
          _toggleACHeating();
        } else if (buttonType == 'cooling') {
          _toggleACCooling();
        }
        return;
      }
    } else {
      // acStatus为null时，执行对应的制暖或制冷功能
      if (buttonType == 'heating') {
        _toggleACHeating();
      } else if (buttonType == 'cooling') {
        _toggleACCooling();
      }
      return;
    }
  }

  acTypeAuto() {
    // 每次构建时重新从SharedPreferences读取最新值
    _loadACSettings();

    return GestureDetector(
      onTap: () {
        // 点击空调布局直接调用doCarControlAction
        if (widget.onControlButtonClicked != null) {
          CarControlItemModel? acControlItem = _getACControlItem();
          if (acControlItem != null) {
            widget.onControlButtonClicked!(carControlItemModel: acControlItem);
          }
        }
      },
      child: Card(
        elevation: 2,
        color: Colors.white,
        margin: EdgeInsets.zero, // 移除Card的默认margin，确保左右对齐
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            image: DecorationImage(
                image: AssetImage(getACBackgroundImage()), fit: BoxFit.cover),
          ),
          clipBehavior: Clip.antiAlias,
          height: 160, // 只约束高度，让宽度由Expanded决定
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(getACStatusText(),
                  style: TextStyle(color: Color(0xFF686B78), fontSize: 12)),
              SizedBox(height: 15), // 减少间距从20到15
              Row(
                mainAxisAlignment: MainAxisAlignment.center, // 添加居中对齐
                children: [
                  InkWell(
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: 8), // 减少水平padding从10到8
                      child: SvgPicture.asset(
                          "assets/images/use_car_page/new_UI/svg/ic_reduce.svg",
                          width: 25,
                          height: 25,
                          fit: BoxFit.cover),
                    ),
                    onTap: () {
                      print('🔥🔥🔥 【acTypeAuto减少按钮】被点击 🔥🔥🔥');

                      // 检查是否为acType=2或4且空调开启的情况
                      int acType = getACType();
                      int currentACStatus = 0;
                      if (widget.statusModel?.acStatus != null) {
                        currentACStatus = int.tryParse(
                                widget.statusModel!.acStatus.toString()) ??
                            0;
                      }

                      if ((acType == 2 || acType == 4) &&
                          currentACStatus != 0) {
                        // acType=2或4且空调开启时，点击按钮打开弹窗
                        print('【acTypeAuto减少按钮】✅ acType=2或4且空调开启，打开弹窗');
                        if (widget.onControlButtonClicked != null) {
                          CarControlItemModel? acControlItem =
                              _getACControlItem();
                          if (acControlItem != null) {
                            widget.onControlButtonClicked!(
                                carControlItemModel: acControlItem);
                          }
                        }
                      } else {
                        // 其他情况使用原有逻辑
                        print('【acTypeAuto减少按钮】使用原有逻辑，调整温度');
                        setState(() {
                          // 自动空调温度限制：最低17度，超出范围不改变状态
                          if (temperature > 17) {
                            temperature = temperature - 1;
                            _saveTemperature(
                                temperature); // 保存温度到SharedPreferences
                          }
                        });
                      }
                    },
                  ),
                  Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      textBaseline: TextBaseline.alphabetic,
                      crossAxisAlignment: CrossAxisAlignment.baseline,
                      children: [
                        Text(
                          "${getCurrentTemperature()}",
                          style: TextStyle(
                              color: Color(0xFF383A40),
                              fontSize: 30,
                              fontWeight: FontWeight.bold,
                              height: 1.0),
                        ),
                        Text("℃",
                            style: TextStyle(
                                color: Color(0xFF383A40), fontSize: 10)),
                      ],
                    ),
                  ),
                  InkWell(
                    child: Padding(
                      padding: EdgeInsets.symmetric(
                          horizontal: 8), // 减少水平padding从10到8
                      child: SvgPicture.asset(
                          "assets/images/use_car_page/new_UI/svg/ic_increase.svg",
                          width: 25,
                          height: 25,
                          fit: BoxFit.cover),
                    ),
                    onTap: () {
                      print('🔥🔥🔥 【acTypeAuto增加按钮】被点击 🔥🔥🔥');

                      // 检查是否为acType=2或4且空调开启的情况
                      int acType = getACType();
                      int currentACStatus = 0;
                      if (widget.statusModel?.acStatus != null) {
                        currentACStatus = int.tryParse(
                                widget.statusModel!.acStatus.toString()) ??
                            0;
                      }

                      if ((acType == 2 || acType == 4) &&
                          currentACStatus != 0) {
                        // acType=2或4且空调开启时，点击按钮打开弹窗
                        print('【acTypeAuto增加按钮】✅ acType=2或4且空调开启，打开弹窗');
                        if (widget.onControlButtonClicked != null) {
                          CarControlItemModel? acControlItem =
                              _getACControlItem();
                          if (acControlItem != null) {
                            widget.onControlButtonClicked!(
                                carControlItemModel: acControlItem);
                          }
                        }
                      } else {
                        // 其他情况使用原有逻辑
                        print('【acTypeAuto增加按钮】使用原有逻辑，调整温度');
                        setState(() {
                          // 自动空调温度限制：最高33度，超出范围不改变状态
                          if (temperature < 33) {
                            temperature = temperature + 1;
                            _saveTemperature(
                                temperature); // 保存温度到SharedPreferences
                          }
                        });
                      }
                    },
                  ),
                ],
              ),
              SizedBox(height: 15), // 减少间距从20到15
              InkWell(
                child: Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 8), // 减少水平padding从10到8
                  child: SvgPicture.asset(
                    getACIconImage(),
                    width: 25,
                    height: 25,
                    fit: BoxFit.cover,
                  ),
                ),
                onTap: () {
                  // 参考AcSettingDialog开关按钮逻辑，灵活传递temperature参数
                  CarControlItemModel? acControlItem = _getACControlItem();
                  if (acControlItem != null &&
                      widget.onACControlButtonClicked != null) {
                    // 获取当前状态模型
                    CarServiceStatusModel? currentStatus =
                        acControlItem.currentStatus();
                    if (currentStatus != null) {
                      // 根据空调类型更新temperature参数
                      CarServiceStatusModel? updatedStatus =
                          _updateStatusModelWithCurrentTemperature(
                              currentStatus);

                      // 传递更新后的状态模型
                      widget.onACControlButtonClicked!(
                        carControlItemModel: acControlItem,
                        statusModel: updatedStatus ?? currentStatus,
                      );
                    }
                  }
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  acTypeRegular() {
    // 每次构建时重新从SharedPreferences读取最新值
    _loadACSettings();

    return GestureDetector(
      onTap: () {
        // 点击空调布局调用handleControlActionWithItem方法
        _handleACLayoutClick();
      },
      child: Card(
        elevation: 2,
        color: Colors.white,
        margin: EdgeInsets.zero, // 移除Card的默认margin，确保左右对齐
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            image: DecorationImage(
                image: AssetImage(getACBackgroundImage()), fit: BoxFit.cover),
          ),
          clipBehavior: Clip.antiAlias,
          height: 160, // 只约束高度，让宽度由Expanded决定
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '空调',
                style: TextStyle(
                    color: Color(0xFF383A40),
                    fontSize: 14,
                    fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 10),
              Text(getACStatusText(),
                  style: TextStyle(color: Color(0xFF686B78), fontSize: 12)),
              SizedBox(height: 23),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  InkWell(
                    child: SvgPicture.asset(getACHeatingIcon(),
                        width: 50, height: 50, fit: BoxFit.cover),
                    onTap: () {
                      _handleACButtonClick('heating');
                    },
                  ),
                  InkWell(
                    child: SvgPicture.asset(getACCoolingIcon(),
                        width: 50, height: 50, fit: BoxFit.cover),
                    onTap: () {
                      _handleACButtonClick('cooling');
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  buildSmallMenuCardVertical(List<MenuItemCard> menuList) {
    var itemCount = 0;
    if (menuList.length >= 2) {
      itemCount = 2;
    } else {
      itemCount = menuList.length;
    }
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start, // 顶部对齐，特别是当只有一个项目时
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          ...List.generate(itemCount, (index) {
            var item = menuList.elementAt(index);
            return Column(
              children: [
                buildSmallMenuItemWithCustomMargin(item, index, itemCount),
                if (index < itemCount - 1) SizedBox(height: 2), // 减少间距从5到2
              ],
            );
          }),
          // 当只有一个项目时，添加额外的底部间距
          if (itemCount == 1) SizedBox(height: 5),
        ],
      ),
    );
  }

  // 判断是否应该显示subtitle
  bool _shouldShowSubtitle(MenuItemCard item) {
    // 只有"导航到车"(Map)和"通风加热"显示subtitle，其他项目只显示title和图标
    String serviceCode = item.controlItem?.serviceModel.serviceCode ?? '';
    String label = item.label;
    String serviceName = item.controlItem?.serviceModel.serviceName ?? '';

    // 只有导航到车和通风加热显示subtitle，其他所有项目都不显示
    bool shouldShow = false;

    // 导航到车：serviceCode是'Map'
    if (serviceCode == ServiceConstant.jumpToMap) {
      shouldShow = true;
    }
    // 通风加热：label是'通风加热'
    else if (label == '通风加热') {
      shouldShow = true;
    }

    return shouldShow;
  }

  // 构建带自定义margin的菜单项（用于垂直布局的对齐优化）
  buildSmallMenuItemWithCustomMargin(
      MenuItemCard item, int index, int itemCount) {
    // 计算margin：第一个项目移除上边距以实现顶部对齐，最后一个项目根据情况调整下边距
    EdgeInsets itemMargin;
    if (index == 0) {
      // 第一个项目：移除上边距，实现与电池布局顶部对齐
      itemMargin = const EdgeInsets.only(bottom: 4, left: 0, right: 0);
    } else if (index == itemCount - 1) {
      // 最后一个项目：正常的上边距，移除下边距（因为外层会添加间距）
      itemMargin = const EdgeInsets.only(top: 4, left: 0, right: 0);
    } else {
      // 中间项目：正常的上下边距
      itemMargin = const EdgeInsets.symmetric(vertical: 4, horizontal: 0);
    }

    return Container(
      width: (MediaQuery.of(context).size.width - 40 - 10) /
          2, // 动态计算宽度：(屏幕宽度 - 左右边距40 - 中间间距10) / 2
      height: 75, // 固定高度
      margin: itemMargin, // 使用计算出的自定义margin
      child: Card(
        elevation: 2,
        color: Colors.white,
        margin: EdgeInsets.zero, // Card本身不设置margin，由外层Container控制
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        child: InkWell(
          borderRadius: BorderRadius.circular(8),
          onTap: () {
            if (widget.onControlButtonClicked != null &&
                item.controlItem != null) {
              widget.onControlButtonClicked!(
                  carControlItemModel: item.controlItem!);
            }
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                // 左侧文本区域
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center, // 始终垂直居中
                    children: [
                      Text(
                        item.label,
                        style: const TextStyle(
                            fontSize: 14,
                            color: Color(0xFF383A40),
                            fontWeight: FontWeight.bold),
                      ),
                      if (_shouldShowSubtitle(item)) ...[
                        const SizedBox(height: 4), // title和subtitle之间的间距
                        item.controlItem!.serviceModel.serviceCode !=
                                ServiceConstant.jumpToMap
                            ? Text(
                                item.info ?? "",
                                style: const TextStyle(
                                    fontSize: 10, color: Color(0xFF686B78)),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              )
                            : FutureBuilder(
                                future: updateCarPosition(
                                    widget.statusModel?.position,
                                    widget.statusModel?.latitude,
                                    widget.statusModel?.longitude),
                                builder: (context, snapshot) {
                                  return Text(
                                    snapshot.data ?? '获取车况位置信息失败',
                                    style: const TextStyle(
                                        fontSize: 10, color: Color(0xFF686B78)),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  );
                                })
                      ],
                    ],
                  ),
                ),
                // 右侧图标
                _buildServiceIcon(item.icon),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 检查座椅通风是否打开（复制UseCarPage的getSeatVentilationIsOpen逻辑）
  bool _checkSeatVentilationIsOpen() {
    try {
      int seat1WindStatus =
          int.tryParse(widget.statusModel?.seat1WindStatus ?? '7') ?? 7;
      int seat2WindStatus =
          int.tryParse(widget.statusModel?.seat2WindStatus ?? '7') ?? 7;
      int seat3WindStatus =
          int.tryParse(widget.statusModel?.seat3WindStatus ?? '7') ?? 7;
      int seat4WindStatus =
          int.tryParse(widget.statusModel?.seat4WindStatus ?? '7') ?? 7;

      if ((seat1WindStatus == 1 ||
              seat1WindStatus == 2 ||
              seat1WindStatus == 3) ||
          (seat2WindStatus == 1 ||
              seat2WindStatus == 2 ||
              seat2WindStatus == 3) ||
          (seat3WindStatus == 1 ||
              seat3WindStatus == 2 ||
              seat3WindStatus == 3) ||
          (seat4WindStatus == 1 ||
              seat4WindStatus == 2 ||
              seat4WindStatus == 3)) {
        //有一个座椅通风是打开的，则座椅通风就是打开的
        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  // 检查座椅加热是否打开（复制UseCarPage的getSeatHeatingIsOpen逻辑）
  bool _checkSeatHeatingIsOpen() {
    try {
      int seat1HotStatus =
          int.tryParse(widget.statusModel?.seat1HotStatus ?? '7') ?? 7;
      int seat2HotStatus =
          int.tryParse(widget.statusModel?.seat2HotStatus ?? '7') ?? 7;
      int seat3HotStatus =
          int.tryParse(widget.statusModel?.seat3HotStatus ?? '7') ?? 7;
      int seat4HotStatus =
          int.tryParse(widget.statusModel?.seat4HotStatus ?? '7') ?? 7;

      if ((seat1HotStatus == 1 || seat1HotStatus == 2 || seat1HotStatus == 3) ||
          (seat2HotStatus == 1 || seat2HotStatus == 2 || seat2HotStatus == 3) ||
          (seat3HotStatus == 1 || seat3HotStatus == 2 || seat3HotStatus == 3) ||
          (seat4HotStatus == 1 || seat4HotStatus == 2 || seat4HotStatus == 3)) {
        //有一个座椅加热是打开的，则座椅加热就是打开的
        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  // 创建通风加热菜单项（空调类型4专用）
  MenuItemCard? _createVentilationHeatingItem() {
    // 检查是否是空调类型4
    int acType = getACType();
    if (acType != 4) {
      return null;
    }

    // 从controlSubItemList中查找acStatus项
    CarControlItemModel? acControlItem;
    for (CarControlItemModel item in widget.controlSubItemList) {
      if (item.serviceModel.serviceCode == 'acStatus') {
        acControlItem = item;
        break;
      }
    }

    if (acControlItem == null) {
      return null;
    }

    // 使用与UseCarPage相同的逻辑判断座椅通风加热状态
    String statusText = '已关闭';
    String iconUrl = '';
    String targetStatusValue = '0'; // 默认关闭状态

    // 移除空调状态判断，让座椅通风/加热可以独立显示
    // 实现getSeatVentilationIsOpen逻辑
    bool seatVentilationIsOpen = _checkSeatVentilationIsOpen();
    // 实现getSeatHeatingIsOpen逻辑
    bool seatHeatingIsOpen = _checkSeatHeatingIsOpen();

    // 根据座椅通风/加热状态设置显示
    if (seatVentilationIsOpen) {
      if (seatHeatingIsOpen) {
        //座椅通风加热同时打开
        targetStatusValue = '21';
        statusText = '通风加热已开';
      } else {
        //座椅通风打开,座椅加热关闭
        targetStatusValue = '19';
        statusText = '通风已开';
      }
    } else {
      if (seatHeatingIsOpen) {
        //座椅通风关闭，座椅加热打开
        targetStatusValue = '20';
        statusText = '加热已开';
      } else {
        //座椅通风和座椅加热都关闭
        targetStatusValue = '0';
        statusText = '已关闭';
      }
    }

    // 根据目标状态值查找对应的图标
    if (targetStatusValue == '0') {
      // 如果是已关闭状态，使用默认的PNG图标
      iconUrl =
          'assets/images/use_car_page/new_UI/png/icon_seat_statelessness.png';
    } else {
      // 其他状态从serviceStatusList中查找对应图标
      if (acControlItem.serviceModel.serviceStatusList != null) {
        for (CarServiceStatusModel statusModel
            in acControlItem.serviceModel.serviceStatusList!) {
          if (statusModel.serviceStatusValue == targetStatusValue) {
            iconUrl = statusModel.serviceStatusImage;

            break;
          }
        }
      }
    }

    return MenuItemCard(
      label: '通风加热',
      info: statusText,
      icon: iconUrl.isNotEmpty
          ? iconUrl
          : 'assets/images/use_car_page/new_UI/png/icon_seat_statelessness.png', // 提供默认图标
      controlItem: acControlItem,
    );
  }

  buildSmallMenuCard(List<MenuItemCard> menuList) {
    for (int i = 0; i < menuList.length; i++) {}

    // 创建最终的菜单列表
    List<MenuItemCard> finalMenuList = List.from(menuList);

    // 如果是空调类型4，添加通风加热项目到第二个位置
    MenuItemCard? ventilationItem = _createVentilationHeatingItem();
    if (ventilationItem != null) {
      if (finalMenuList.length >= 2) {
        finalMenuList.insert(1, ventilationItem); // 插入到第二个位置（索引1）
      } else {
        finalMenuList.add(ventilationItem); // 如果列表长度不足2，直接添加到末尾
      }
    }

    // 如果最终列表为空，返回空的SizedBox，不占用任何空间
    if (finalMenuList.isEmpty) {
      return SizedBox.shrink();
    }

    Widget menuWidget = Container(
      margin:
          EdgeInsets.symmetric(horizontal: 20, vertical: 0), // 明确设置垂直margin为0
      child: GridView.builder(
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          var item = finalMenuList.elementAt(index);
          return buildSmallMenuItem(item);
        },
        itemCount: finalMenuList.length,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          mainAxisSpacing: 1, // 进一步减少行间距到1
          crossAxisSpacing: 10,
          childAspectRatio: 2.13, // 增加宽高比，让组件更扁平
        ),
      ),
    );

    // 在离线模式时添加蒙版效果并禁用点击
    if (widget.isOfflineMode) {
      return Stack(
        children: [
          menuWidget,
          // 蒙版层 - 覆盖整个小菜单区域
          Positioned.fill(
            child: Container(
              margin: EdgeInsets.symmetric(
                  horizontal: 20, vertical: 0), // 与原容器保持相同的margin
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.8), // 使用与电池、空调相同的蒙版颜色
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ],
      );
    } else {
      return menuWidget;
    }
  }

  buildSmallMenuItem(MenuItemCard item) {
    return Card(
      elevation: 2,
      color: Colors.white,
      margin: const EdgeInsets.symmetric(
          vertical: 4, horizontal: 0), // 保留上下margin，移除左右margin确保对齐
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: () {
          if (widget.onControlButtonClicked != null &&
              item.controlItem != null) {
            widget.onControlButtonClicked!(
                carControlItemModel: item.controlItem!);
          }
        },
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              // 左侧文本区域
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center, // 始终垂直居中
                  children: [
                    Text(
                      item.label,
                      style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF383A40),
                          fontWeight: FontWeight.bold),
                    ),
                    if (_shouldShowSubtitle(item)) ...[
                      const SizedBox(height: 4), // title和subtitle之间的间距
                      item.controlItem!.serviceModel.serviceCode !=
                              ServiceConstant.jumpToMap
                          ? Text(
                              item.info ?? "",
                              style: const TextStyle(
                                  fontSize: 10, color: Color(0xFF686B78)),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            )
                          : FutureBuilder(
                              future: updateCarPosition(
                                  widget.statusModel?.position,
                                  widget.statusModel?.latitude,
                                  widget.statusModel?.longitude),
                              builder: (context, snapshot) {
                                return Text(
                                  snapshot.data ?? '获取车况位置信息失败',
                                  style: const TextStyle(
                                      fontSize: 10, color: Color(0xFF686B78)),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                );
                              })
                    ],
                  ],
                ),
              ),
              // 右侧图标
              _buildServiceIcon(item.icon),
            ],
          ),
        ),
      ),
    );
  }

  // 构建导航列表 (爱车体检、座椅通风等按钮)
  buildNavigationList() {
    if (widget.navigationList.isEmpty) {
      return Container(
        height: 50,
        color: Colors.red.withOpacity(0.3),
        child: Center(child: Text('navigationList为空')),
      ); // 显示一个红色容器表示数据为空
    }

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20, vertical: 0),
      child: GridView.builder(
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        padding: EdgeInsets.zero, // 明确设置GridView的padding为0
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          mainAxisSpacing: 5, // 设置为5dp
          crossAxisSpacing: 10,
          childAspectRatio: 2.13, // 使用与buildSmallMenuCard一致的比例
        ),
        itemCount: widget.navigationList.length,
        itemBuilder: (context, index) {
          final nav.MenuItemCard item = widget.navigationList[index];
          return Card(
            elevation: 2,
            color: Colors.white,
            margin: EdgeInsets.zero, // 完全移除Card的margin
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            child: Container(
              padding:
                  EdgeInsets.symmetric(horizontal: 12, vertical: 8), // 精确控制内边距
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          item.label,
                          style: TextStyle(
                            fontSize: 14,
                            color: Color(0xFF383A40),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (item.info != null && item.info!.isNotEmpty) ...[
                          SizedBox(height: 2),
                          Text(
                            item.info!,
                            style: TextStyle(
                              fontSize: 10,
                              color: Color(0xFF686B78),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  Icon(
                    Icons.help_outline,
                    size: 25,
                    color: Color(0xFF008DFF),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  buildServiceMenu(List<MenuItemCard> menuList) {
    return Container(
      margin:
          EdgeInsets.symmetric(horizontal: 20, vertical: 0), // 明确设置垂直margin为0
      child: Card(
        elevation: 2,
        color: Colors.white,
        margin: EdgeInsets.zero, // 移除Card的默认margin，确保左右对齐
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        child: Container(
          margin: EdgeInsets.symmetric(
              horizontal: 16, vertical: 15), // 恢复原来的15dp垂直间距
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '用车服务',
                    style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF383A40),
                        fontWeight: FontWeight.bold),
                  ),
                  Visibility(
                    visible: false, // 隐藏编辑按钮和左边的图标
                    child: Row(
                      children: [
                        Image.asset(
                            "assets/images/use_car_page/new_UI/png/ic_more.png",
                            width: 12,
                            height: 12,
                            fit: BoxFit.cover),
                        SizedBox(width: 5),
                        Text('编辑',
                            style: TextStyle(
                                fontSize: 12, color: Color(0xFF383A40))),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(height: 15),
              Divider(thickness: 1, color: Color(0x0D000000), height: 0),
              SizedBox(height: 15),
              GridView.builder(
                shrinkWrap: true,
                physics: NeverScrollableScrollPhysics(),
                padding: EdgeInsets.zero,
                itemBuilder: (context, index) {
                  var item = menuList.elementAt(index);
                  return buildServiceMenuItem(item);
                },
                itemCount: menuList.length,
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 5,
                  mainAxisSpacing: 8,
                  crossAxisSpacing: 8,
                  childAspectRatio: 1,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  buildServiceMenuItem(MenuItemCard item) {
    return InkWell(
      onTap: () {
        // 检查是否在离线模式下
        if (widget.isOfflineMode) {
          _showOfflineModeDialog();
          return;
        }

        // 处理服务菜单项点击事件 (与CarControlContainerWidget保持一致)

        if (widget.onServiceButtonClicked != null &&
            item.serviceModel != null) {
          widget.onServiceButtonClicked!(serviceModel: item.serviceModel!);
        }
      },
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center, // 添加垂直居中对齐
        children: [
          // 根据图片URL类型选择合适的显示方式
          _buildServiceIcon(item.icon),
          SizedBox(height: 6),
          Text(
            item.label,
            style: TextStyle(fontSize: 10, color: Color(0xff383A40)),
            textAlign: TextAlign.center, // 添加文字居中对齐
          ),
        ],
      ),
    );
  }

  // 显示离线模式提示弹窗
  void _showOfflineModeDialog() {
    DialogManager().showSingleButtonDialog(
      '', // 不显示标题
      '当前APP处于【离线模式】，暂不支持该功能',
      '我知道了',
      () {
        // 点击我知道了按钮的回调
      },
    );
  }

  // 构建服务图标，支持网络图片、本地SVG和本地PNG
  Widget _buildServiceIcon(String iconPath) {
    if (iconPath.startsWith('http://') || iconPath.startsWith('https://')) {
      // 网络图片 - 使用CachedNetworkImage以利用缓存
      return CachedNetworkImage(
        imageUrl: iconPath,
        width: 25,
        height: 25,
        fit: BoxFit.cover,
        placeholder: (context, url) {
          return Container(
            width: 25,
            height: 25,
            color: Colors.transparent,
          );
        },
        errorWidget: (context, url, error) {
          // 网络和缓存都失败时显示透明容器，不显示默认图标
          return Container(
            width: 25,
            height: 25,
            color: Colors.transparent,
          );
        },
      );
    } else if (iconPath.endsWith('.png') ||
        iconPath.endsWith('.jpg') ||
        iconPath.endsWith('.jpeg')) {
      // 本地PNG/JPG图片
      return Image.asset(
        iconPath,
        width: 25,
        height: 25,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: 25,
            height: 25,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(4),
            ),
            child: Icon(Icons.chair, size: 15, color: Colors.grey[600]),
          );
        },
      );
    } else {
      // 本地SVG图片
      try {
        return SvgPicture.asset(
          iconPath,
          width: 25,
          height: 25,
          fit: BoxFit.cover,
          placeholderBuilder: (BuildContext context) {
            return Container(
              width: 25,
              height: 25,
              color: Colors.grey[300],
              child: const Icon(Icons.image, size: 15),
            );
          },
        );
      } catch (e) {
        // SVG加载失败时使用备用图标
        return Container(
          width: 25,
          height: 25,
          decoration: BoxDecoration(
            color: Colors.grey[300],
            borderRadius: BorderRadius.circular(4),
          ),
          child: Icon(Icons.chair, size: 15, color: Colors.grey[600]),
        );
      }
    }
  }

  Future<String> updateCarPosition(
      carPosition, carLatitude, carLongitude) async {
    if (carPosition.isEmpty || carPosition == 'null') {
      if (carLatitude != "null" &&
          carLongitude != "null" &&
          carLatitude.isNotEmpty &&
          carLongitude.isNotEmpty) {
        try {
          String address = await LocationManager.getAddressFromLocation(
            carLatitude,
            carLongitude,
          );
          // 格式化地址：处理门牌号中的点号分隔问题
          return _formatAddress(address);
        } catch (e) {
          return '获取车况位置信息失败';
        }
      } else {
        return '获取车况位置信息失败';
      }
    } else {
      // 对已有的地址也进行格式化处理
      return _formatAddress(carPosition);
    }
  }

  /// 格式化地址字符串，处理门牌号中的点号分隔问题
  /// 例如：将 "河西路18号*******号" 转换为 "河西路18号1234号"
  String _formatAddress(String address) {
    // 使用正则表达式匹配门牌号中的点号分隔数字模式
    // 匹配模式：数字.数字.数字.数字号 或类似的模式
    return address.replaceAllMapped(
      RegExp(r'(\d+(?:\.\d+)+)号'),
      (Match match) {
        String numberPart = match.group(1)!;
        // 去掉所有点号，保留数字和字母
        String cleanedNumber = numberPart.replaceAll('.', '');
        return '${cleanedNumber}号';
      },
    );
  }

  // 切换制暖状态
  void _toggleACHeating() {
    // 从controlSubItemList中查找serviceCode = "acStatus"的控制项
    for (CarControlItemModel item in widget.controlSubItemList) {
      if (item.serviceModel.serviceCode == 'acStatus') {
        // 打印所有可用的状态
        if (item.serviceModel.serviceStatusList != null) {
          for (int i = 0;
              i < item.serviceModel.serviceStatusList!.length;
              i++) {
            CarServiceStatusModel statusModel =
                item.serviceModel.serviceStatusList![i];
          }
        }

        // 查找制暖对应的statusModel
        CarServiceStatusModel? heatingStatusModel;
        if (item.serviceModel.serviceStatusList != null) {
          for (CarServiceStatusModel statusModel
              in item.serviceModel.serviceStatusList!) {
            // 优先查找serviceStatusName为"制暖"的状态
            if (statusModel.serviceStatusName == '制暖') {
              heatingStatusModel = statusModel;

              break;
            }
          }
          // 如果没找到"制暖"，再查找包含"制暖"的状态
          if (heatingStatusModel == null) {
            for (CarServiceStatusModel statusModel
                in item.serviceModel.serviceStatusList!) {
              if (statusModel.serviceStatusName.contains('制暖') ||
                  statusModel.serviceStatusName.contains('暖风')) {
                heatingStatusModel = statusModel;

                break;
              }
            }
          }
        }

        if (heatingStatusModel == null) {}

        // 创建制暖状态的CarControlItemModel
        CarControlItemModel heatingItem = CarControlItemModel(
          serviceModel: item.serviceModel,
          index: item.index,
          status: heatingStatusModel?.serviceStatusValue ?? '2',
          isControling: false,
        );

        // 使用新的回调函数，传递statusModel参数
        if (heatingStatusModel != null &&
            widget.onACControlButtonClicked != null) {
          widget.onACControlButtonClicked!(
            carControlItemModel: heatingItem,
            statusModel: heatingStatusModel,
          );
        } else if (widget.onControlButtonClicked != null) {
          widget.onControlButtonClicked!(carControlItemModel: heatingItem);
        } else {}
        break;
      }
    }
  }

  // 切换制冷状态
  void _toggleACCooling() {
    // 从controlSubItemList中查找serviceCode = "acStatus"的控制项
    for (CarControlItemModel item in widget.controlSubItemList) {
      if (item.serviceModel.serviceCode == 'acStatus') {
        // 打印所有可用的状态
        if (item.serviceModel.serviceStatusList != null) {
          for (int i = 0;
              i < item.serviceModel.serviceStatusList!.length;
              i++) {
            CarServiceStatusModel statusModel =
                item.serviceModel.serviceStatusList![i];
          }
        }

        // 查找制冷对应的statusModel
        CarServiceStatusModel? coolingStatusModel;
        if (item.serviceModel.serviceStatusList != null) {
          // 优先查找serviceStatusName为"制冷"的状态
          for (CarServiceStatusModel statusModel
              in item.serviceModel.serviceStatusList!) {
            if (statusModel.serviceStatusName == '制冷') {
              coolingStatusModel = statusModel;

              break;
            }
          }
          // 如果没找到"制冷"，再查找包含"制冷"的状态，但排除"制冷中"
          if (coolingStatusModel == null) {
            for (CarServiceStatusModel statusModel
                in item.serviceModel.serviceStatusList!) {
              if ((statusModel.serviceStatusName.contains('制冷') ||
                      statusModel.serviceStatusName.contains('冷风')) &&
                  !statusModel.serviceStatusName.contains('制冷中')) {
                coolingStatusModel = statusModel;

                break;
              }
            }
          }
          // 如果还是没找到，打印所有状态信息进行调试
          if (coolingStatusModel == null) {
            for (int i = 0;
                i < item.serviceModel.serviceStatusList!.length;
                i++) {
              CarServiceStatusModel statusModel =
                  item.serviceModel.serviceStatusList![i];
            }
          }
        }

        if (coolingStatusModel == null) {}

        // 创建制冷状态的CarControlItemModel
        CarControlItemModel coolingItem = CarControlItemModel(
          serviceModel: item.serviceModel,
          index: item.index,
          status: coolingStatusModel?.serviceStatusValue ?? '1',
          isControling: false,
        );

        // 使用新的回调函数，传递statusModel参数
        if (coolingStatusModel != null &&
            widget.onACControlButtonClicked != null) {
          widget.onACControlButtonClicked!(
            carControlItemModel: coolingItem,
            statusModel: coolingStatusModel,
          );
        } else if (widget.onControlButtonClicked != null) {
          widget.onControlButtonClicked!(carControlItemModel: coolingItem);
        } else {}
        break;
      }
    }
  }
}
