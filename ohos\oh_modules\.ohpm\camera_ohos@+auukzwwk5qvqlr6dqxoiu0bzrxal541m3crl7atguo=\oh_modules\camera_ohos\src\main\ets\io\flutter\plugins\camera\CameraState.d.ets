export declare enum CameraState {
    /** Idle, showing preview and not capturing anything. */
    STATE_PREVIEW = 0,
    /** Starting and waiting for autofocus to complete. */
    STATE_WAITING_FOCUS = 1,
    /** Start performing autoexposure. */
    STATE_WAITING_PRECAPTURE_START = 2,
    /** waiting for autoexposure to complete. */
    STATE_WAITING_PRECAPTURE_DONE = 3,
    /** Capturing an image. */
    STATE_CAPTURING = 4
}
