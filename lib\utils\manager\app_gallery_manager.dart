import 'package:flutter/services.dart';
import 'package:flutter_platform_utils/flutter_platform_utils.dart';

import 'log_manager.dart';

class AppGalleryManager {
  // 定义MethodChannel，通道名称为 com.sgmw.flutter/app_gallery
  static const MethodChannel _channel =
      MethodChannel('com.sgmw.flutter/app_gallery');
  // 方法：打开指定app的详情页
  static Future<void> openAppDetailPage(String bundleName) async {
    if (bundleName.isEmpty) return;
    if (PlatformUtils.isOhos) {
      try {
        // 调用原生方法 'openAppDetailPage' 并传递 bundleName 参数
        await _channel.invokeMethod('openAppDetailPage', {
          'bundleName': bundleName,
        });
      } on PlatformException catch (e) {
        LogManager().debug("Failed to open app detail page: ${e.message}");
      }
    }
  }
}
