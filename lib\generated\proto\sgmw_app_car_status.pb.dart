//
//  Generated code. Do not modify.
//  source: sgmw_app_car_status.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

///  车辆状态变更，车联网后端给APP的消息
/// 2024-09-05 新增座椅通风和加热 共8个字段
class SgmwAppCarStatus extends $pb.GeneratedMessage {
  factory SgmwAppCarStatus({
    $fixnum.Int64? collectTime,
    $core.String? acStatus,
    $core.String? doorLockStatus,
    $core.String? windowStatus,
    $core.String? engineStatus,
    $core.String? tailDoorLockStatus,
    $core.String? lowBeamLight,
    $core.String? dipHeadLight,
    $core.String? sentinelModeStatus,
    $core.String? tailDoorOpenStatus,
    $core.String? door1LockStatus,
    $core.String? door2LockStatus,
    $core.String? door3LockStatus,
    $core.String? door4LockStatus,
    $core.String? doorOpenStatus,
    $core.String? door1OpenStatus,
    $core.String? door2OpenStatus,
    $core.String? door3OpenStatus,
    $core.String? door4OpenStatus,
    $core.String? window1Status,
    $core.String? window2Status,
    $core.String? window3Status,
    $core.String? window4Status,
    $core.String? topWindowStatus,
    $core.String? autoGearStatus,
    $core.String? manualGearStatus,
    $core.String? keyStatus,
    $core.String? acTemperatureGear,
    $core.String? acWindGear,
    $core.String? leftBatteryPower,
    $core.String? leftFuel,
    $core.String? mileage,
    $core.String? leftMileage,
    $core.String? batterySoc,
    $core.String? current,
    $core.String? voltage,
    $core.String? batAvgTemp,
    $core.String? batMaxTemp,
    $core.String? batMinTemp,
    $core.String? tmActTemp,
    $core.String? invActTemp,
    $core.String? accActPos,
    $core.String? brakPedalPos,
    $core.String? strWhAng,
    $core.String? vehSpdAvgDrvn,
    $core.String? obcOtpCur,
    $core.String? vecChrgingSts,
    $core.String? vecChrgStsIndOn,
    $core.String? obcTemp,
    $core.String? batSOH,
    $core.String? lowBatVol,
    $core.String? leftTurnLight,
    $core.String? rightTurnLight,
    $core.String? positionLight,
    $core.String? frontFogLight,
    $core.String? rearFogLight,
    $core.String? latitude,
    $core.String? longitude,
    $core.String? position,
    $core.String? charging,
    $core.String? wireConnect,
    $core.String? rechargeStatus,
    $core.String? window1OpenDegree,
    $core.String? window2OpenDegree,
    $core.String? window3OpenDegree,
    $core.String? window4OpenDegree,
    $core.String? seat1WindStatus,
    $core.String? seat2WindStatus,
    $core.String? seat3WindStatus,
    $core.String? seat4WindStatus,
    $core.String? seat1HotStatus,
    $core.String? seat2HotStatus,
    $core.String? seat3HotStatus,
    $core.String? seat4HotStatus,
  }) {
    final $result = create();
    if (collectTime != null) {
      $result.collectTime = collectTime;
    }
    if (acStatus != null) {
      $result.acStatus = acStatus;
    }
    if (doorLockStatus != null) {
      $result.doorLockStatus = doorLockStatus;
    }
    if (windowStatus != null) {
      $result.windowStatus = windowStatus;
    }
    if (engineStatus != null) {
      $result.engineStatus = engineStatus;
    }
    if (tailDoorLockStatus != null) {
      $result.tailDoorLockStatus = tailDoorLockStatus;
    }
    if (lowBeamLight != null) {
      $result.lowBeamLight = lowBeamLight;
    }
    if (dipHeadLight != null) {
      $result.dipHeadLight = dipHeadLight;
    }
    if (sentinelModeStatus != null) {
      $result.sentinelModeStatus = sentinelModeStatus;
    }
    if (tailDoorOpenStatus != null) {
      $result.tailDoorOpenStatus = tailDoorOpenStatus;
    }
    if (door1LockStatus != null) {
      $result.door1LockStatus = door1LockStatus;
    }
    if (door2LockStatus != null) {
      $result.door2LockStatus = door2LockStatus;
    }
    if (door3LockStatus != null) {
      $result.door3LockStatus = door3LockStatus;
    }
    if (door4LockStatus != null) {
      $result.door4LockStatus = door4LockStatus;
    }
    if (doorOpenStatus != null) {
      $result.doorOpenStatus = doorOpenStatus;
    }
    if (door1OpenStatus != null) {
      $result.door1OpenStatus = door1OpenStatus;
    }
    if (door2OpenStatus != null) {
      $result.door2OpenStatus = door2OpenStatus;
    }
    if (door3OpenStatus != null) {
      $result.door3OpenStatus = door3OpenStatus;
    }
    if (door4OpenStatus != null) {
      $result.door4OpenStatus = door4OpenStatus;
    }
    if (window1Status != null) {
      $result.window1Status = window1Status;
    }
    if (window2Status != null) {
      $result.window2Status = window2Status;
    }
    if (window3Status != null) {
      $result.window3Status = window3Status;
    }
    if (window4Status != null) {
      $result.window4Status = window4Status;
    }
    if (topWindowStatus != null) {
      $result.topWindowStatus = topWindowStatus;
    }
    if (autoGearStatus != null) {
      $result.autoGearStatus = autoGearStatus;
    }
    if (manualGearStatus != null) {
      $result.manualGearStatus = manualGearStatus;
    }
    if (keyStatus != null) {
      $result.keyStatus = keyStatus;
    }
    if (acTemperatureGear != null) {
      $result.acTemperatureGear = acTemperatureGear;
    }
    if (acWindGear != null) {
      $result.acWindGear = acWindGear;
    }
    if (leftBatteryPower != null) {
      $result.leftBatteryPower = leftBatteryPower;
    }
    if (leftFuel != null) {
      $result.leftFuel = leftFuel;
    }
    if (mileage != null) {
      $result.mileage = mileage;
    }
    if (leftMileage != null) {
      $result.leftMileage = leftMileage;
    }
    if (batterySoc != null) {
      $result.batterySoc = batterySoc;
    }
    if (current != null) {
      $result.current = current;
    }
    if (voltage != null) {
      $result.voltage = voltage;
    }
    if (batAvgTemp != null) {
      $result.batAvgTemp = batAvgTemp;
    }
    if (batMaxTemp != null) {
      $result.batMaxTemp = batMaxTemp;
    }
    if (batMinTemp != null) {
      $result.batMinTemp = batMinTemp;
    }
    if (tmActTemp != null) {
      $result.tmActTemp = tmActTemp;
    }
    if (invActTemp != null) {
      $result.invActTemp = invActTemp;
    }
    if (accActPos != null) {
      $result.accActPos = accActPos;
    }
    if (brakPedalPos != null) {
      $result.brakPedalPos = brakPedalPos;
    }
    if (strWhAng != null) {
      $result.strWhAng = strWhAng;
    }
    if (vehSpdAvgDrvn != null) {
      $result.vehSpdAvgDrvn = vehSpdAvgDrvn;
    }
    if (obcOtpCur != null) {
      $result.obcOtpCur = obcOtpCur;
    }
    if (vecChrgingSts != null) {
      $result.vecChrgingSts = vecChrgingSts;
    }
    if (vecChrgStsIndOn != null) {
      $result.vecChrgStsIndOn = vecChrgStsIndOn;
    }
    if (obcTemp != null) {
      $result.obcTemp = obcTemp;
    }
    if (batSOH != null) {
      $result.batSOH = batSOH;
    }
    if (lowBatVol != null) {
      $result.lowBatVol = lowBatVol;
    }
    if (leftTurnLight != null) {
      $result.leftTurnLight = leftTurnLight;
    }
    if (rightTurnLight != null) {
      $result.rightTurnLight = rightTurnLight;
    }
    if (positionLight != null) {
      $result.positionLight = positionLight;
    }
    if (frontFogLight != null) {
      $result.frontFogLight = frontFogLight;
    }
    if (rearFogLight != null) {
      $result.rearFogLight = rearFogLight;
    }
    if (latitude != null) {
      $result.latitude = latitude;
    }
    if (longitude != null) {
      $result.longitude = longitude;
    }
    if (position != null) {
      $result.position = position;
    }
    if (charging != null) {
      $result.charging = charging;
    }
    if (wireConnect != null) {
      $result.wireConnect = wireConnect;
    }
    if (rechargeStatus != null) {
      $result.rechargeStatus = rechargeStatus;
    }
    if (window1OpenDegree != null) {
      $result.window1OpenDegree = window1OpenDegree;
    }
    if (window2OpenDegree != null) {
      $result.window2OpenDegree = window2OpenDegree;
    }
    if (window3OpenDegree != null) {
      $result.window3OpenDegree = window3OpenDegree;
    }
    if (window4OpenDegree != null) {
      $result.window4OpenDegree = window4OpenDegree;
    }
    if (seat1WindStatus != null) {
      $result.seat1WindStatus = seat1WindStatus;
    }
    if (seat2WindStatus != null) {
      $result.seat2WindStatus = seat2WindStatus;
    }
    if (seat3WindStatus != null) {
      $result.seat3WindStatus = seat3WindStatus;
    }
    if (seat4WindStatus != null) {
      $result.seat4WindStatus = seat4WindStatus;
    }
    if (seat1HotStatus != null) {
      $result.seat1HotStatus = seat1HotStatus;
    }
    if (seat2HotStatus != null) {
      $result.seat2HotStatus = seat2HotStatus;
    }
    if (seat3HotStatus != null) {
      $result.seat3HotStatus = seat3HotStatus;
    }
    if (seat4HotStatus != null) {
      $result.seat4HotStatus = seat4HotStatus;
    }
    return $result;
  }
  SgmwAppCarStatus._() : super();
  factory SgmwAppCarStatus.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SgmwAppCarStatus.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'SgmwAppCarStatus', createEmptyInstance: create)
    ..aInt64(1, _omitFieldNames ? '' : 'collectTime', protoName: 'collectTime')
    ..aOS(2, _omitFieldNames ? '' : 'acStatus', protoName: 'acStatus')
    ..aOS(3, _omitFieldNames ? '' : 'doorLockStatus', protoName: 'doorLockStatus')
    ..aOS(4, _omitFieldNames ? '' : 'windowStatus', protoName: 'windowStatus')
    ..aOS(5, _omitFieldNames ? '' : 'engineStatus', protoName: 'engineStatus')
    ..aOS(6, _omitFieldNames ? '' : 'tailDoorLockStatus', protoName: 'tailDoorLockStatus')
    ..aOS(7, _omitFieldNames ? '' : 'lowBeamLight', protoName: 'lowBeamLight')
    ..aOS(8, _omitFieldNames ? '' : 'dipHeadLight', protoName: 'dipHeadLight')
    ..aOS(9, _omitFieldNames ? '' : 'sentinelModeStatus', protoName: 'sentinelModeStatus')
    ..aOS(10, _omitFieldNames ? '' : 'tailDoorOpenStatus', protoName: 'tailDoorOpenStatus')
    ..aOS(11, _omitFieldNames ? '' : 'door1LockStatus', protoName: 'door1LockStatus')
    ..aOS(12, _omitFieldNames ? '' : 'door2LockStatus', protoName: 'door2LockStatus')
    ..aOS(13, _omitFieldNames ? '' : 'door3LockStatus', protoName: 'door3LockStatus')
    ..aOS(14, _omitFieldNames ? '' : 'door4LockStatus', protoName: 'door4LockStatus')
    ..aOS(15, _omitFieldNames ? '' : 'doorOpenStatus', protoName: 'doorOpenStatus')
    ..aOS(16, _omitFieldNames ? '' : 'door1OpenStatus', protoName: 'door1OpenStatus')
    ..aOS(17, _omitFieldNames ? '' : 'door2OpenStatus', protoName: 'door2OpenStatus')
    ..aOS(18, _omitFieldNames ? '' : 'door3OpenStatus', protoName: 'door3OpenStatus')
    ..aOS(19, _omitFieldNames ? '' : 'door4OpenStatus', protoName: 'door4OpenStatus')
    ..aOS(20, _omitFieldNames ? '' : 'window1Status', protoName: 'window1Status')
    ..aOS(21, _omitFieldNames ? '' : 'window2Status', protoName: 'window2Status')
    ..aOS(22, _omitFieldNames ? '' : 'window3Status', protoName: 'window3Status')
    ..aOS(23, _omitFieldNames ? '' : 'window4Status', protoName: 'window4Status')
    ..aOS(24, _omitFieldNames ? '' : 'topWindowStatus', protoName: 'topWindowStatus')
    ..aOS(25, _omitFieldNames ? '' : 'autoGearStatus', protoName: 'autoGearStatus')
    ..aOS(26, _omitFieldNames ? '' : 'manualGearStatus', protoName: 'manualGearStatus')
    ..aOS(27, _omitFieldNames ? '' : 'keyStatus', protoName: 'keyStatus')
    ..aOS(28, _omitFieldNames ? '' : 'acTemperatureGear', protoName: 'acTemperatureGear')
    ..aOS(29, _omitFieldNames ? '' : 'acWindGear', protoName: 'acWindGear')
    ..aOS(30, _omitFieldNames ? '' : 'leftBatteryPower', protoName: 'leftBatteryPower')
    ..aOS(31, _omitFieldNames ? '' : 'leftFuel', protoName: 'leftFuel')
    ..aOS(32, _omitFieldNames ? '' : 'mileage')
    ..aOS(33, _omitFieldNames ? '' : 'leftMileage', protoName: 'leftMileage')
    ..aOS(34, _omitFieldNames ? '' : 'batterySoc', protoName: 'batterySoc')
    ..aOS(35, _omitFieldNames ? '' : 'current')
    ..aOS(36, _omitFieldNames ? '' : 'voltage')
    ..aOS(37, _omitFieldNames ? '' : 'batAvgTemp', protoName: 'batAvgTemp')
    ..aOS(38, _omitFieldNames ? '' : 'batMaxTemp', protoName: 'batMaxTemp')
    ..aOS(39, _omitFieldNames ? '' : 'batMinTemp', protoName: 'batMinTemp')
    ..aOS(40, _omitFieldNames ? '' : 'tmActTemp', protoName: 'tmActTemp')
    ..aOS(41, _omitFieldNames ? '' : 'invActTemp', protoName: 'invActTemp')
    ..aOS(42, _omitFieldNames ? '' : 'accActPos', protoName: 'accActPos')
    ..aOS(43, _omitFieldNames ? '' : 'brakPedalPos', protoName: 'brakPedalPos')
    ..aOS(44, _omitFieldNames ? '' : 'strWhAng', protoName: 'strWhAng')
    ..aOS(45, _omitFieldNames ? '' : 'vehSpdAvgDrvn', protoName: 'vehSpdAvgDrvn')
    ..aOS(46, _omitFieldNames ? '' : 'obcOtpCur', protoName: 'obcOtpCur')
    ..aOS(47, _omitFieldNames ? '' : 'vecChrgingSts', protoName: 'vecChrgingSts')
    ..aOS(48, _omitFieldNames ? '' : 'vecChrgStsIndOn', protoName: 'vecChrgStsIndOn')
    ..aOS(49, _omitFieldNames ? '' : 'obcTemp', protoName: 'obcTemp')
    ..aOS(50, _omitFieldNames ? '' : 'batSOH', protoName: 'batSOH')
    ..aOS(51, _omitFieldNames ? '' : 'lowBatVol', protoName: 'lowBatVol')
    ..aOS(52, _omitFieldNames ? '' : 'leftTurnLight', protoName: 'leftTurnLight')
    ..aOS(53, _omitFieldNames ? '' : 'rightTurnLight', protoName: 'rightTurnLight')
    ..aOS(54, _omitFieldNames ? '' : 'positionLight', protoName: 'positionLight')
    ..aOS(55, _omitFieldNames ? '' : 'frontFogLight', protoName: 'frontFogLight')
    ..aOS(56, _omitFieldNames ? '' : 'rearFogLight', protoName: 'rearFogLight')
    ..aOS(57, _omitFieldNames ? '' : 'latitude')
    ..aOS(58, _omitFieldNames ? '' : 'longitude')
    ..aOS(59, _omitFieldNames ? '' : 'position')
    ..aOS(60, _omitFieldNames ? '' : 'charging')
    ..aOS(61, _omitFieldNames ? '' : 'wireConnect', protoName: 'wireConnect')
    ..aOS(62, _omitFieldNames ? '' : 'rechargeStatus', protoName: 'rechargeStatus')
    ..aOS(63, _omitFieldNames ? '' : 'window1OpenDegree', protoName: 'window1OpenDegree')
    ..aOS(64, _omitFieldNames ? '' : 'window2OpenDegree', protoName: 'window2OpenDegree')
    ..aOS(65, _omitFieldNames ? '' : 'window3OpenDegree', protoName: 'window3OpenDegree')
    ..aOS(66, _omitFieldNames ? '' : 'window4OpenDegree', protoName: 'window4OpenDegree')
    ..aOS(67, _omitFieldNames ? '' : 'seat1WindStatus', protoName: 'seat1WindStatus')
    ..aOS(68, _omitFieldNames ? '' : 'seat2WindStatus', protoName: 'seat2WindStatus')
    ..aOS(69, _omitFieldNames ? '' : 'seat3WindStatus', protoName: 'seat3WindStatus')
    ..aOS(70, _omitFieldNames ? '' : 'seat4WindStatus', protoName: 'seat4WindStatus')
    ..aOS(71, _omitFieldNames ? '' : 'seat1HotStatus', protoName: 'seat1HotStatus')
    ..aOS(72, _omitFieldNames ? '' : 'seat2HotStatus', protoName: 'seat2HotStatus')
    ..aOS(73, _omitFieldNames ? '' : 'seat3HotStatus', protoName: 'seat3HotStatus')
    ..aOS(74, _omitFieldNames ? '' : 'seat4HotStatus', protoName: 'seat4HotStatus')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SgmwAppCarStatus clone() => SgmwAppCarStatus()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SgmwAppCarStatus copyWith(void Function(SgmwAppCarStatus) updates) => super.copyWith((message) => updates(message as SgmwAppCarStatus)) as SgmwAppCarStatus;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static SgmwAppCarStatus create() => SgmwAppCarStatus._();
  SgmwAppCarStatus createEmptyInstance() => create();
  static $pb.PbList<SgmwAppCarStatus> createRepeated() => $pb.PbList<SgmwAppCarStatus>();
  @$core.pragma('dart2js:noInline')
  static SgmwAppCarStatus getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SgmwAppCarStatus>(create);
  static SgmwAppCarStatus? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get collectTime => $_getI64(0);
  @$pb.TagNumber(1)
  set collectTime($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCollectTime() => $_has(0);
  @$pb.TagNumber(1)
  void clearCollectTime() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get acStatus => $_getSZ(1);
  @$pb.TagNumber(2)
  set acStatus($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasAcStatus() => $_has(1);
  @$pb.TagNumber(2)
  void clearAcStatus() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get doorLockStatus => $_getSZ(2);
  @$pb.TagNumber(3)
  set doorLockStatus($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasDoorLockStatus() => $_has(2);
  @$pb.TagNumber(3)
  void clearDoorLockStatus() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get windowStatus => $_getSZ(3);
  @$pb.TagNumber(4)
  set windowStatus($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasWindowStatus() => $_has(3);
  @$pb.TagNumber(4)
  void clearWindowStatus() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get engineStatus => $_getSZ(4);
  @$pb.TagNumber(5)
  set engineStatus($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasEngineStatus() => $_has(4);
  @$pb.TagNumber(5)
  void clearEngineStatus() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get tailDoorLockStatus => $_getSZ(5);
  @$pb.TagNumber(6)
  set tailDoorLockStatus($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasTailDoorLockStatus() => $_has(5);
  @$pb.TagNumber(6)
  void clearTailDoorLockStatus() => clearField(6);

  @$pb.TagNumber(7)
  $core.String get lowBeamLight => $_getSZ(6);
  @$pb.TagNumber(7)
  set lowBeamLight($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasLowBeamLight() => $_has(6);
  @$pb.TagNumber(7)
  void clearLowBeamLight() => clearField(7);

  @$pb.TagNumber(8)
  $core.String get dipHeadLight => $_getSZ(7);
  @$pb.TagNumber(8)
  set dipHeadLight($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasDipHeadLight() => $_has(7);
  @$pb.TagNumber(8)
  void clearDipHeadLight() => clearField(8);

  @$pb.TagNumber(9)
  $core.String get sentinelModeStatus => $_getSZ(8);
  @$pb.TagNumber(9)
  set sentinelModeStatus($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasSentinelModeStatus() => $_has(8);
  @$pb.TagNumber(9)
  void clearSentinelModeStatus() => clearField(9);

  @$pb.TagNumber(10)
  $core.String get tailDoorOpenStatus => $_getSZ(9);
  @$pb.TagNumber(10)
  set tailDoorOpenStatus($core.String v) { $_setString(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasTailDoorOpenStatus() => $_has(9);
  @$pb.TagNumber(10)
  void clearTailDoorOpenStatus() => clearField(10);

  @$pb.TagNumber(11)
  $core.String get door1LockStatus => $_getSZ(10);
  @$pb.TagNumber(11)
  set door1LockStatus($core.String v) { $_setString(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasDoor1LockStatus() => $_has(10);
  @$pb.TagNumber(11)
  void clearDoor1LockStatus() => clearField(11);

  @$pb.TagNumber(12)
  $core.String get door2LockStatus => $_getSZ(11);
  @$pb.TagNumber(12)
  set door2LockStatus($core.String v) { $_setString(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasDoor2LockStatus() => $_has(11);
  @$pb.TagNumber(12)
  void clearDoor2LockStatus() => clearField(12);

  @$pb.TagNumber(13)
  $core.String get door3LockStatus => $_getSZ(12);
  @$pb.TagNumber(13)
  set door3LockStatus($core.String v) { $_setString(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasDoor3LockStatus() => $_has(12);
  @$pb.TagNumber(13)
  void clearDoor3LockStatus() => clearField(13);

  @$pb.TagNumber(14)
  $core.String get door4LockStatus => $_getSZ(13);
  @$pb.TagNumber(14)
  set door4LockStatus($core.String v) { $_setString(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasDoor4LockStatus() => $_has(13);
  @$pb.TagNumber(14)
  void clearDoor4LockStatus() => clearField(14);

  @$pb.TagNumber(15)
  $core.String get doorOpenStatus => $_getSZ(14);
  @$pb.TagNumber(15)
  set doorOpenStatus($core.String v) { $_setString(14, v); }
  @$pb.TagNumber(15)
  $core.bool hasDoorOpenStatus() => $_has(14);
  @$pb.TagNumber(15)
  void clearDoorOpenStatus() => clearField(15);

  @$pb.TagNumber(16)
  $core.String get door1OpenStatus => $_getSZ(15);
  @$pb.TagNumber(16)
  set door1OpenStatus($core.String v) { $_setString(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasDoor1OpenStatus() => $_has(15);
  @$pb.TagNumber(16)
  void clearDoor1OpenStatus() => clearField(16);

  @$pb.TagNumber(17)
  $core.String get door2OpenStatus => $_getSZ(16);
  @$pb.TagNumber(17)
  set door2OpenStatus($core.String v) { $_setString(16, v); }
  @$pb.TagNumber(17)
  $core.bool hasDoor2OpenStatus() => $_has(16);
  @$pb.TagNumber(17)
  void clearDoor2OpenStatus() => clearField(17);

  @$pb.TagNumber(18)
  $core.String get door3OpenStatus => $_getSZ(17);
  @$pb.TagNumber(18)
  set door3OpenStatus($core.String v) { $_setString(17, v); }
  @$pb.TagNumber(18)
  $core.bool hasDoor3OpenStatus() => $_has(17);
  @$pb.TagNumber(18)
  void clearDoor3OpenStatus() => clearField(18);

  @$pb.TagNumber(19)
  $core.String get door4OpenStatus => $_getSZ(18);
  @$pb.TagNumber(19)
  set door4OpenStatus($core.String v) { $_setString(18, v); }
  @$pb.TagNumber(19)
  $core.bool hasDoor4OpenStatus() => $_has(18);
  @$pb.TagNumber(19)
  void clearDoor4OpenStatus() => clearField(19);

  @$pb.TagNumber(20)
  $core.String get window1Status => $_getSZ(19);
  @$pb.TagNumber(20)
  set window1Status($core.String v) { $_setString(19, v); }
  @$pb.TagNumber(20)
  $core.bool hasWindow1Status() => $_has(19);
  @$pb.TagNumber(20)
  void clearWindow1Status() => clearField(20);

  @$pb.TagNumber(21)
  $core.String get window2Status => $_getSZ(20);
  @$pb.TagNumber(21)
  set window2Status($core.String v) { $_setString(20, v); }
  @$pb.TagNumber(21)
  $core.bool hasWindow2Status() => $_has(20);
  @$pb.TagNumber(21)
  void clearWindow2Status() => clearField(21);

  @$pb.TagNumber(22)
  $core.String get window3Status => $_getSZ(21);
  @$pb.TagNumber(22)
  set window3Status($core.String v) { $_setString(21, v); }
  @$pb.TagNumber(22)
  $core.bool hasWindow3Status() => $_has(21);
  @$pb.TagNumber(22)
  void clearWindow3Status() => clearField(22);

  @$pb.TagNumber(23)
  $core.String get window4Status => $_getSZ(22);
  @$pb.TagNumber(23)
  set window4Status($core.String v) { $_setString(22, v); }
  @$pb.TagNumber(23)
  $core.bool hasWindow4Status() => $_has(22);
  @$pb.TagNumber(23)
  void clearWindow4Status() => clearField(23);

  @$pb.TagNumber(24)
  $core.String get topWindowStatus => $_getSZ(23);
  @$pb.TagNumber(24)
  set topWindowStatus($core.String v) { $_setString(23, v); }
  @$pb.TagNumber(24)
  $core.bool hasTopWindowStatus() => $_has(23);
  @$pb.TagNumber(24)
  void clearTopWindowStatus() => clearField(24);

  @$pb.TagNumber(25)
  $core.String get autoGearStatus => $_getSZ(24);
  @$pb.TagNumber(25)
  set autoGearStatus($core.String v) { $_setString(24, v); }
  @$pb.TagNumber(25)
  $core.bool hasAutoGearStatus() => $_has(24);
  @$pb.TagNumber(25)
  void clearAutoGearStatus() => clearField(25);

  @$pb.TagNumber(26)
  $core.String get manualGearStatus => $_getSZ(25);
  @$pb.TagNumber(26)
  set manualGearStatus($core.String v) { $_setString(25, v); }
  @$pb.TagNumber(26)
  $core.bool hasManualGearStatus() => $_has(25);
  @$pb.TagNumber(26)
  void clearManualGearStatus() => clearField(26);

  @$pb.TagNumber(27)
  $core.String get keyStatus => $_getSZ(26);
  @$pb.TagNumber(27)
  set keyStatus($core.String v) { $_setString(26, v); }
  @$pb.TagNumber(27)
  $core.bool hasKeyStatus() => $_has(26);
  @$pb.TagNumber(27)
  void clearKeyStatus() => clearField(27);

  @$pb.TagNumber(28)
  $core.String get acTemperatureGear => $_getSZ(27);
  @$pb.TagNumber(28)
  set acTemperatureGear($core.String v) { $_setString(27, v); }
  @$pb.TagNumber(28)
  $core.bool hasAcTemperatureGear() => $_has(27);
  @$pb.TagNumber(28)
  void clearAcTemperatureGear() => clearField(28);

  @$pb.TagNumber(29)
  $core.String get acWindGear => $_getSZ(28);
  @$pb.TagNumber(29)
  set acWindGear($core.String v) { $_setString(28, v); }
  @$pb.TagNumber(29)
  $core.bool hasAcWindGear() => $_has(28);
  @$pb.TagNumber(29)
  void clearAcWindGear() => clearField(29);

  @$pb.TagNumber(30)
  $core.String get leftBatteryPower => $_getSZ(29);
  @$pb.TagNumber(30)
  set leftBatteryPower($core.String v) { $_setString(29, v); }
  @$pb.TagNumber(30)
  $core.bool hasLeftBatteryPower() => $_has(29);
  @$pb.TagNumber(30)
  void clearLeftBatteryPower() => clearField(30);

  @$pb.TagNumber(31)
  $core.String get leftFuel => $_getSZ(30);
  @$pb.TagNumber(31)
  set leftFuel($core.String v) { $_setString(30, v); }
  @$pb.TagNumber(31)
  $core.bool hasLeftFuel() => $_has(30);
  @$pb.TagNumber(31)
  void clearLeftFuel() => clearField(31);

  @$pb.TagNumber(32)
  $core.String get mileage => $_getSZ(31);
  @$pb.TagNumber(32)
  set mileage($core.String v) { $_setString(31, v); }
  @$pb.TagNumber(32)
  $core.bool hasMileage() => $_has(31);
  @$pb.TagNumber(32)
  void clearMileage() => clearField(32);

  @$pb.TagNumber(33)
  $core.String get leftMileage => $_getSZ(32);
  @$pb.TagNumber(33)
  set leftMileage($core.String v) { $_setString(32, v); }
  @$pb.TagNumber(33)
  $core.bool hasLeftMileage() => $_has(32);
  @$pb.TagNumber(33)
  void clearLeftMileage() => clearField(33);

  @$pb.TagNumber(34)
  $core.String get batterySoc => $_getSZ(33);
  @$pb.TagNumber(34)
  set batterySoc($core.String v) { $_setString(33, v); }
  @$pb.TagNumber(34)
  $core.bool hasBatterySoc() => $_has(33);
  @$pb.TagNumber(34)
  void clearBatterySoc() => clearField(34);

  @$pb.TagNumber(35)
  $core.String get current => $_getSZ(34);
  @$pb.TagNumber(35)
  set current($core.String v) { $_setString(34, v); }
  @$pb.TagNumber(35)
  $core.bool hasCurrent() => $_has(34);
  @$pb.TagNumber(35)
  void clearCurrent() => clearField(35);

  @$pb.TagNumber(36)
  $core.String get voltage => $_getSZ(35);
  @$pb.TagNumber(36)
  set voltage($core.String v) { $_setString(35, v); }
  @$pb.TagNumber(36)
  $core.bool hasVoltage() => $_has(35);
  @$pb.TagNumber(36)
  void clearVoltage() => clearField(36);

  @$pb.TagNumber(37)
  $core.String get batAvgTemp => $_getSZ(36);
  @$pb.TagNumber(37)
  set batAvgTemp($core.String v) { $_setString(36, v); }
  @$pb.TagNumber(37)
  $core.bool hasBatAvgTemp() => $_has(36);
  @$pb.TagNumber(37)
  void clearBatAvgTemp() => clearField(37);

  @$pb.TagNumber(38)
  $core.String get batMaxTemp => $_getSZ(37);
  @$pb.TagNumber(38)
  set batMaxTemp($core.String v) { $_setString(37, v); }
  @$pb.TagNumber(38)
  $core.bool hasBatMaxTemp() => $_has(37);
  @$pb.TagNumber(38)
  void clearBatMaxTemp() => clearField(38);

  @$pb.TagNumber(39)
  $core.String get batMinTemp => $_getSZ(38);
  @$pb.TagNumber(39)
  set batMinTemp($core.String v) { $_setString(38, v); }
  @$pb.TagNumber(39)
  $core.bool hasBatMinTemp() => $_has(38);
  @$pb.TagNumber(39)
  void clearBatMinTemp() => clearField(39);

  @$pb.TagNumber(40)
  $core.String get tmActTemp => $_getSZ(39);
  @$pb.TagNumber(40)
  set tmActTemp($core.String v) { $_setString(39, v); }
  @$pb.TagNumber(40)
  $core.bool hasTmActTemp() => $_has(39);
  @$pb.TagNumber(40)
  void clearTmActTemp() => clearField(40);

  @$pb.TagNumber(41)
  $core.String get invActTemp => $_getSZ(40);
  @$pb.TagNumber(41)
  set invActTemp($core.String v) { $_setString(40, v); }
  @$pb.TagNumber(41)
  $core.bool hasInvActTemp() => $_has(40);
  @$pb.TagNumber(41)
  void clearInvActTemp() => clearField(41);

  @$pb.TagNumber(42)
  $core.String get accActPos => $_getSZ(41);
  @$pb.TagNumber(42)
  set accActPos($core.String v) { $_setString(41, v); }
  @$pb.TagNumber(42)
  $core.bool hasAccActPos() => $_has(41);
  @$pb.TagNumber(42)
  void clearAccActPos() => clearField(42);

  @$pb.TagNumber(43)
  $core.String get brakPedalPos => $_getSZ(42);
  @$pb.TagNumber(43)
  set brakPedalPos($core.String v) { $_setString(42, v); }
  @$pb.TagNumber(43)
  $core.bool hasBrakPedalPos() => $_has(42);
  @$pb.TagNumber(43)
  void clearBrakPedalPos() => clearField(43);

  @$pb.TagNumber(44)
  $core.String get strWhAng => $_getSZ(43);
  @$pb.TagNumber(44)
  set strWhAng($core.String v) { $_setString(43, v); }
  @$pb.TagNumber(44)
  $core.bool hasStrWhAng() => $_has(43);
  @$pb.TagNumber(44)
  void clearStrWhAng() => clearField(44);

  @$pb.TagNumber(45)
  $core.String get vehSpdAvgDrvn => $_getSZ(44);
  @$pb.TagNumber(45)
  set vehSpdAvgDrvn($core.String v) { $_setString(44, v); }
  @$pb.TagNumber(45)
  $core.bool hasVehSpdAvgDrvn() => $_has(44);
  @$pb.TagNumber(45)
  void clearVehSpdAvgDrvn() => clearField(45);

  @$pb.TagNumber(46)
  $core.String get obcOtpCur => $_getSZ(45);
  @$pb.TagNumber(46)
  set obcOtpCur($core.String v) { $_setString(45, v); }
  @$pb.TagNumber(46)
  $core.bool hasObcOtpCur() => $_has(45);
  @$pb.TagNumber(46)
  void clearObcOtpCur() => clearField(46);

  @$pb.TagNumber(47)
  $core.String get vecChrgingSts => $_getSZ(46);
  @$pb.TagNumber(47)
  set vecChrgingSts($core.String v) { $_setString(46, v); }
  @$pb.TagNumber(47)
  $core.bool hasVecChrgingSts() => $_has(46);
  @$pb.TagNumber(47)
  void clearVecChrgingSts() => clearField(47);

  @$pb.TagNumber(48)
  $core.String get vecChrgStsIndOn => $_getSZ(47);
  @$pb.TagNumber(48)
  set vecChrgStsIndOn($core.String v) { $_setString(47, v); }
  @$pb.TagNumber(48)
  $core.bool hasVecChrgStsIndOn() => $_has(47);
  @$pb.TagNumber(48)
  void clearVecChrgStsIndOn() => clearField(48);

  @$pb.TagNumber(49)
  $core.String get obcTemp => $_getSZ(48);
  @$pb.TagNumber(49)
  set obcTemp($core.String v) { $_setString(48, v); }
  @$pb.TagNumber(49)
  $core.bool hasObcTemp() => $_has(48);
  @$pb.TagNumber(49)
  void clearObcTemp() => clearField(49);

  @$pb.TagNumber(50)
  $core.String get batSOH => $_getSZ(49);
  @$pb.TagNumber(50)
  set batSOH($core.String v) { $_setString(49, v); }
  @$pb.TagNumber(50)
  $core.bool hasBatSOH() => $_has(49);
  @$pb.TagNumber(50)
  void clearBatSOH() => clearField(50);

  @$pb.TagNumber(51)
  $core.String get lowBatVol => $_getSZ(50);
  @$pb.TagNumber(51)
  set lowBatVol($core.String v) { $_setString(50, v); }
  @$pb.TagNumber(51)
  $core.bool hasLowBatVol() => $_has(50);
  @$pb.TagNumber(51)
  void clearLowBatVol() => clearField(51);

  @$pb.TagNumber(52)
  $core.String get leftTurnLight => $_getSZ(51);
  @$pb.TagNumber(52)
  set leftTurnLight($core.String v) { $_setString(51, v); }
  @$pb.TagNumber(52)
  $core.bool hasLeftTurnLight() => $_has(51);
  @$pb.TagNumber(52)
  void clearLeftTurnLight() => clearField(52);

  @$pb.TagNumber(53)
  $core.String get rightTurnLight => $_getSZ(52);
  @$pb.TagNumber(53)
  set rightTurnLight($core.String v) { $_setString(52, v); }
  @$pb.TagNumber(53)
  $core.bool hasRightTurnLight() => $_has(52);
  @$pb.TagNumber(53)
  void clearRightTurnLight() => clearField(53);

  @$pb.TagNumber(54)
  $core.String get positionLight => $_getSZ(53);
  @$pb.TagNumber(54)
  set positionLight($core.String v) { $_setString(53, v); }
  @$pb.TagNumber(54)
  $core.bool hasPositionLight() => $_has(53);
  @$pb.TagNumber(54)
  void clearPositionLight() => clearField(54);

  @$pb.TagNumber(55)
  $core.String get frontFogLight => $_getSZ(54);
  @$pb.TagNumber(55)
  set frontFogLight($core.String v) { $_setString(54, v); }
  @$pb.TagNumber(55)
  $core.bool hasFrontFogLight() => $_has(54);
  @$pb.TagNumber(55)
  void clearFrontFogLight() => clearField(55);

  @$pb.TagNumber(56)
  $core.String get rearFogLight => $_getSZ(55);
  @$pb.TagNumber(56)
  set rearFogLight($core.String v) { $_setString(55, v); }
  @$pb.TagNumber(56)
  $core.bool hasRearFogLight() => $_has(55);
  @$pb.TagNumber(56)
  void clearRearFogLight() => clearField(56);

  @$pb.TagNumber(57)
  $core.String get latitude => $_getSZ(56);
  @$pb.TagNumber(57)
  set latitude($core.String v) { $_setString(56, v); }
  @$pb.TagNumber(57)
  $core.bool hasLatitude() => $_has(56);
  @$pb.TagNumber(57)
  void clearLatitude() => clearField(57);

  @$pb.TagNumber(58)
  $core.String get longitude => $_getSZ(57);
  @$pb.TagNumber(58)
  set longitude($core.String v) { $_setString(57, v); }
  @$pb.TagNumber(58)
  $core.bool hasLongitude() => $_has(57);
  @$pb.TagNumber(58)
  void clearLongitude() => clearField(58);

  @$pb.TagNumber(59)
  $core.String get position => $_getSZ(58);
  @$pb.TagNumber(59)
  set position($core.String v) { $_setString(58, v); }
  @$pb.TagNumber(59)
  $core.bool hasPosition() => $_has(58);
  @$pb.TagNumber(59)
  void clearPosition() => clearField(59);

  @$pb.TagNumber(60)
  $core.String get charging => $_getSZ(59);
  @$pb.TagNumber(60)
  set charging($core.String v) { $_setString(59, v); }
  @$pb.TagNumber(60)
  $core.bool hasCharging() => $_has(59);
  @$pb.TagNumber(60)
  void clearCharging() => clearField(60);

  @$pb.TagNumber(61)
  $core.String get wireConnect => $_getSZ(60);
  @$pb.TagNumber(61)
  set wireConnect($core.String v) { $_setString(60, v); }
  @$pb.TagNumber(61)
  $core.bool hasWireConnect() => $_has(60);
  @$pb.TagNumber(61)
  void clearWireConnect() => clearField(61);

  @$pb.TagNumber(62)
  $core.String get rechargeStatus => $_getSZ(61);
  @$pb.TagNumber(62)
  set rechargeStatus($core.String v) { $_setString(61, v); }
  @$pb.TagNumber(62)
  $core.bool hasRechargeStatus() => $_has(61);
  @$pb.TagNumber(62)
  void clearRechargeStatus() => clearField(62);

  @$pb.TagNumber(63)
  $core.String get window1OpenDegree => $_getSZ(62);
  @$pb.TagNumber(63)
  set window1OpenDegree($core.String v) { $_setString(62, v); }
  @$pb.TagNumber(63)
  $core.bool hasWindow1OpenDegree() => $_has(62);
  @$pb.TagNumber(63)
  void clearWindow1OpenDegree() => clearField(63);

  @$pb.TagNumber(64)
  $core.String get window2OpenDegree => $_getSZ(63);
  @$pb.TagNumber(64)
  set window2OpenDegree($core.String v) { $_setString(63, v); }
  @$pb.TagNumber(64)
  $core.bool hasWindow2OpenDegree() => $_has(63);
  @$pb.TagNumber(64)
  void clearWindow2OpenDegree() => clearField(64);

  @$pb.TagNumber(65)
  $core.String get window3OpenDegree => $_getSZ(64);
  @$pb.TagNumber(65)
  set window3OpenDegree($core.String v) { $_setString(64, v); }
  @$pb.TagNumber(65)
  $core.bool hasWindow3OpenDegree() => $_has(64);
  @$pb.TagNumber(65)
  void clearWindow3OpenDegree() => clearField(65);

  @$pb.TagNumber(66)
  $core.String get window4OpenDegree => $_getSZ(65);
  @$pb.TagNumber(66)
  set window4OpenDegree($core.String v) { $_setString(65, v); }
  @$pb.TagNumber(66)
  $core.bool hasWindow4OpenDegree() => $_has(65);
  @$pb.TagNumber(66)
  void clearWindow4OpenDegree() => clearField(66);

  @$pb.TagNumber(67)
  $core.String get seat1WindStatus => $_getSZ(66);
  @$pb.TagNumber(67)
  set seat1WindStatus($core.String v) { $_setString(66, v); }
  @$pb.TagNumber(67)
  $core.bool hasSeat1WindStatus() => $_has(66);
  @$pb.TagNumber(67)
  void clearSeat1WindStatus() => clearField(67);

  @$pb.TagNumber(68)
  $core.String get seat2WindStatus => $_getSZ(67);
  @$pb.TagNumber(68)
  set seat2WindStatus($core.String v) { $_setString(67, v); }
  @$pb.TagNumber(68)
  $core.bool hasSeat2WindStatus() => $_has(67);
  @$pb.TagNumber(68)
  void clearSeat2WindStatus() => clearField(68);

  @$pb.TagNumber(69)
  $core.String get seat3WindStatus => $_getSZ(68);
  @$pb.TagNumber(69)
  set seat3WindStatus($core.String v) { $_setString(68, v); }
  @$pb.TagNumber(69)
  $core.bool hasSeat3WindStatus() => $_has(68);
  @$pb.TagNumber(69)
  void clearSeat3WindStatus() => clearField(69);

  @$pb.TagNumber(70)
  $core.String get seat4WindStatus => $_getSZ(69);
  @$pb.TagNumber(70)
  set seat4WindStatus($core.String v) { $_setString(69, v); }
  @$pb.TagNumber(70)
  $core.bool hasSeat4WindStatus() => $_has(69);
  @$pb.TagNumber(70)
  void clearSeat4WindStatus() => clearField(70);

  @$pb.TagNumber(71)
  $core.String get seat1HotStatus => $_getSZ(70);
  @$pb.TagNumber(71)
  set seat1HotStatus($core.String v) { $_setString(70, v); }
  @$pb.TagNumber(71)
  $core.bool hasSeat1HotStatus() => $_has(70);
  @$pb.TagNumber(71)
  void clearSeat1HotStatus() => clearField(71);

  @$pb.TagNumber(72)
  $core.String get seat2HotStatus => $_getSZ(71);
  @$pb.TagNumber(72)
  set seat2HotStatus($core.String v) { $_setString(71, v); }
  @$pb.TagNumber(72)
  $core.bool hasSeat2HotStatus() => $_has(71);
  @$pb.TagNumber(72)
  void clearSeat2HotStatus() => clearField(72);

  @$pb.TagNumber(73)
  $core.String get seat3HotStatus => $_getSZ(72);
  @$pb.TagNumber(73)
  set seat3HotStatus($core.String v) { $_setString(72, v); }
  @$pb.TagNumber(73)
  $core.bool hasSeat3HotStatus() => $_has(72);
  @$pb.TagNumber(73)
  void clearSeat3HotStatus() => clearField(73);

  @$pb.TagNumber(74)
  $core.String get seat4HotStatus => $_getSZ(73);
  @$pb.TagNumber(74)
  set seat4HotStatus($core.String v) { $_setString(73, v); }
  @$pb.TagNumber(74)
  $core.bool hasSeat4HotStatus() => $_has(73);
  @$pb.TagNumber(74)
  void clearSeat4HotStatus() => clearField(74);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
