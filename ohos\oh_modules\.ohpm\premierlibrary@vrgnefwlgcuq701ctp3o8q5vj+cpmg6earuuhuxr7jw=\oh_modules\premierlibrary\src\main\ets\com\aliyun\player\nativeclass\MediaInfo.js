export class MediaInfo {
    constructor() {
        this.mVideoId = "";
        this.mTitle = "";
        this.mDuration = 0;
        this.mStatus = "";
        this.mCoverUrl = "";
        this.mMediaType = "";
        this.mTransCodeMode = "FastTranscode";
        this.mTotalBitrate = 0;
        this.mTrackInfos = [];
        this.mThumbnailList = [];
        this.nativeGetVideoId = () => {
            return this.mVideoId;
        };
        this.nativeSetVideoId = (s23) => {
            this.mVideoId = s23;
        };
        this.nativeGetTitle = () => {
            return this.mTitle;
        };
        this.nativeSetTitle = (r23) => {
            this.mTitle = r23;
        };
        this.nativeGetDuration = () => {
            return this.mDuration;
        };
        this.nativeSetDuration = (q23) => {
            this.mDuration = q23;
        };
        this.nativeGetStatus = () => {
            return this.mStatus;
        };
        this.nativeSetStatus = (p23) => {
            this.mStatus = p23;
        };
        this.nativeGetCoverUrl = () => {
            return this.mCoverUrl;
        };
        this.nativeSetCoverUrl = (o23) => {
            this.mCoverUrl = o23;
        };
        this.nativeGetMediaType = () => {
            return this.mMediaType;
        };
        this.nativeSetMediaType = (n23) => {
            this.mMediaType = n23;
        };
        this.nativeGetTransCodeMode = () => {
            return this.mTransCodeMode;
        };
        this.nativeSetTransCodeMode = (m23) => {
            this.mTransCodeMode = m23;
        };
        this.nativeGetTotalBitrate = () => {
            return this.mTotalBitrate;
        };
        this.nativeSetTotalBitrate = (l23) => {
            this.mTotalBitrate = l23;
        };
        this.nativeSetTrackInfos = (k23) => {
            if (k23 != null && k23.length > 0) {
                this.mTrackInfos = this.mTrackInfos.concat(k23);
            }
        };
        this.nativeSetThumbnailList = (j23) => {
            if (j23 != null && j23.length > 0) {
                this.mThumbnailList = this.mThumbnailList.concat(j23);
            }
        };
    }
    getTitle() {
        return this.nativeGetTitle();
    }
    getDuration() {
        return this.nativeGetDuration();
    }
    setDuration(q22) {
        this.nativeSetDuration(q22);
    }
    getStatus() {
        return this.nativeGetStatus();
    }
    getVideoId() {
        return this.nativeGetVideoId();
    }
    getCoverUrl() {
        return this.nativeGetCoverUrl();
    }
    getMediaType() {
        return this.nativeGetMediaType();
    }
    getTransCodeMode() {
        return this.nativeGetTransCodeMode();
    }
    getTotalBitrate() {
        return this.nativeGetTotalBitrate();
    }
    getTrackInfos() {
        return this.mTrackInfos;
    }
    getThumbnailList() {
        return this.mThumbnailList;
    }
}
