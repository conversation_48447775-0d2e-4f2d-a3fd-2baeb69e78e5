import premierlibrary from 'libpremierlibrary.so';
export class Hls<PERSON>eyGenerator {
    static getInstance() {
        if (HlsKeyGenerator.instance == undefined) {
            HlsKeyGenerator.instance = new HlsKeyGenerator();
        }
        return HlsKeyGenerator.instance;
    }
    static setOnKeyGenerateListener(i21) {
        HlsKeyGenerator.getInstance().mOnKeyGenerateListener = i21;
    }
    constructor() {
        this.mOutKey = "default-key";
        this.getHlsOutKey = () => {
            return HlsKeyGenerator.getInstance().mOutKey;
        };
        this.onHlsKeyInfoInit = (f21, g21, h21) => {
            console.log('onHlsKeyInfoInit called.');
            if (this.mOnKeyGenerateListener != null) {
                this.mOnKeyGenerateListener.onHlsKeyInfoInit(f21, g21, h21);
            }
        };
        this.getHlsKey = (c21, d21) => {
            console.log('ongetHlsK<PERSON> called.');
            if (this.mOnKeyGenerateListener != null) {
                let e21 = this.mOnKeyGenerateListener.getHlsKey(c21, d21);
                this.mOutKey = e21;
                console.log("get hlsKey, key: " + e21);
                return e21;
            }
            return "";
        };
        this.getHlsKeyInternal = () => {
            if (this.mOutKey) {
                return this.mOutKey;
            }
            return "";
        };
        premierlibrary.nHlsGenConstruct(this);
    }
}
