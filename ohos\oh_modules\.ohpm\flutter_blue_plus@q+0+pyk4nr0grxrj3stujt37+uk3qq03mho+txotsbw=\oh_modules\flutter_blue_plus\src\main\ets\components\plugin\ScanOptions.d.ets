import ble from "@ohos.bluetooth.ble";
export declare class ScanOptions implements ble.ScanOptions {
    interval?: number;
    dutyMode?: ble.ScanDuty;
    matchMode?: ble.MatchMode;
    phyType?: ble.PhyType;
}
export declare class ScanOptionBuilder extends ScanOptions {
    setInterval(interval: number): void;
    setDutyMode(dutyMode: ble.ScanDuty): void;
    setMatchMode(matchMode: ble.MatchMode): void;
    setPhyType(phyType: ble.PhyType): void;
}
