/*
* Copyright (c) 2023 Hunan OpenValley Digital Industry Development Co., Ltd. All rights reserved.
* Use of this source code is governed by a BSD-style license that can be
* found in the LICENSE_KHZG file.
*
* Based on FlutterCallbackInformation.java originally written by
* Copyright (C) 2013 The Flutter Authors.
*
*/

import FlutterNapi from '../embedding/engine/FlutterNapi';

export class FlutterCallbackInformation {
  callbackName?: string;
  callbackClassName?: string;
  callbackLibraryPath?: string;

  /**
   * Get callback information for a given handle.
   *
   * @param handle the handle for the callback, generated by `PluginUtilities.getCallbackHandle` in
   *     `dart:ui`.
   * @return an instance of FlutterCallbackInformation for the provided handle.
   */
  static lookupCallbackInformation(handle: number): FlutterCallbackInformation | null {
    return FlutterNapi.nativeLookupCallbackInformation(handle);
  }

  constructor(callbackName?: string, callbackClassName?: string, callbackLibraryPath?: string) {
    this.callbackName = callbackName;
    this.callbackClassName = callbackClassName;
    this.callbackLibraryPath = callbackLibraryPath;
  }

  init(callbackName: string, callbackClassName: string, callbackLibraryPath: string) {
    this.callbackName = callbackName;
    this.callbackClassName = callbackClassName;
    this.callbackLibraryPath = callbackLibraryPath;
  }
}
