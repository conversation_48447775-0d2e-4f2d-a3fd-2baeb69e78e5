import { AbilityAware, AbilityPluginBinding, FlutterPlugin, FlutterPluginBinding, MethodCall, MethodCallHandler, MethodResult, NewWantListener } from '@ohos/flutter_ohos';
import * as wechatSDK from "@tencent/wechat_open_sdk";
import AbilityConstant from "@ohos.app.ability.AbilityConstant";
import Want from "@ohos.app.ability.Want";
/** FluwxPlugin **/
export default class FluwxPlugin implements FlutterPlugin, MethodCallHandler, AbilityAware, NewWantListener, wechatSDK.WXApiEventHandler {
    private channel;
    private appContext;
    private uiContext;
    private binding;
    private authHandler;
    private shareHandler;
    private extMsg;
    getUniqueClassName(): string;
    onAttachedToEngine(binding: FlutterPluginBinding): void;
    onDetachedFromEngine(binding: FlutterPluginBinding): void;
    onAttachedToAbility(binding: AbilityPluginBinding): void;
    onDetachedFromAbility(): void;
    onMethodCall(call: Method<PERSON>all, result: MethodResult): void;
    onNewWant(want: Want, launchParams: AbilityConstant.LaunchParam): void;
    onReq: (req: wechatSDK.BaseReq) => void;
    onResp: (resp: wechatSDK.BaseResp) => void;
    onAuthResponse(resp: wechatSDK.SendAuthResp): void;
    onSendMessageToWXResp(resp: wechatSDK.SendMessageToWXResp): void;
    onPayResp(resp: wechatSDK.PayResp): void;
    onLaunchMiniProgramResp(resp: wechatSDK.LaunchMiniProgramResp): void;
    handlePay(call: MethodCall, result: MethodResult): Promise<void>;
    attemptToResumeMsgFromWx(result: MethodResult): void;
    launchMiniProgram(call: MethodCall, result: MethodResult): Promise<void>;
}
