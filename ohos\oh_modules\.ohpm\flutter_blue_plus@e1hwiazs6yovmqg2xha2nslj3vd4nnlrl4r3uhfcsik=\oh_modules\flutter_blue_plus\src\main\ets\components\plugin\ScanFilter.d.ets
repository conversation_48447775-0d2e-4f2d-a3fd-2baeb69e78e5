type UUID = string;
declare class BleScanRuleConfigBuilder {
    private mDeviceNames;
    private mDeviceMac;
    private mAutoConnect;
    private mFuzzy;
    private mTimeOut;
    private mServiceUuids;
    constructor();
    setServiceUuids(uuids: UUID[]): this;
    setDeviceName(fuzzy: boolean, name: string[]): this;
    setDeviceMac(mac: string): this;
    setAutoConnect(autoConnect: boolean): this;
    setScanTimeOut(timeOut: number): this;
    applyConfig(config: BleScanRuleConfig): void;
    build(): BleScanRuleConfig;
}
export default class BleScanRuleConfig {
    mServiceUuids: UUID[];
    mDeviceNames: string[];
    mDeviceMac: string;
    mAutoConnect: boolean;
    mFuzzy: boolean;
    mScanTimeOut: number;
    getServiceUuids(): UUID[];
    getDeviceNames(): string[];
    getDeviceMac(): string;
    isAutoConnect(): boolean;
    isFuzzy(): boolean;
    getScanTimeOut(): number;
    static Builder: BleScanRuleConfigBuilder;
}
export {};
