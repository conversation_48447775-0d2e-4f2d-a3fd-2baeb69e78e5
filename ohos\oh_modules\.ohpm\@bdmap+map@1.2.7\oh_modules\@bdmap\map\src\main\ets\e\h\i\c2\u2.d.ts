export default abstract class BmObject { private static readonly DEBUG; protected readonly nativeInstance: number; protected readonly mObjType: number; private mName; private mTag; isDestroyed: boolean; constructor(h12: number, i12: number); setName(name: string): void; getName(): string; setTag(tag: string): void; setLayerTag(g12: string): void; getTag(): string; getObjType(): number; getNativeInstance(): number; destroy(delay?: boolean): void; private printDebugNew; private printDebugFinalize; } 