// @keepTs
// @ts-nocheck
/**
 * Created on 2024/07/09
 * <AUTHOR>
 */
import { BaseConversationItemProvider } from '../../../../conversationlist/item/provider/BaseConversationItemProvider';
import { BaseUiConversation } from '../../../../conversationlist/model/BaseUiConversation';
export declare class GroupConversationItemProvider extends BaseConversationItemProvider {
    getConversationWrapBuilder(): WrappedBuilder<[
        Context,
        BaseUiConversation,
        number
    ]>;
}
@Builder
export declare function bindGroupConversationMessageData(k273: Context, l273: BaseUiConversation, m273: number): void;
@Component
export declare struct GroupConversationItemView {
    @ObjectLink
    conversation: BaseUiConversation;
    build(): void;
}
