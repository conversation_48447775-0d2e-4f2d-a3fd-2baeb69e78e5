// @keepTs
// @ts-nocheck
/**
 * Created on 2024/9/20
 * <AUTHOR>
 */
import { Message } from '@rongcloud/imlib';
import { MessageMoreAction } from '../../../../conversation/more/MessageMoreAction';
@Component
export declare struct MessageMoreComponent {
    onClickAction?: (action: MessageMoreAction, isEdit: boolean) => void;
    @Link
    allSelectedMessages: Message[];
    @State
    itemActions: MessageMoreAction[];
    aboutToAppear(): void;
    build(): void;
}
