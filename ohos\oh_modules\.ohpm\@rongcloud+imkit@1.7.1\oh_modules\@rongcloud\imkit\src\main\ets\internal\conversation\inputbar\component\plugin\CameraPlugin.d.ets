// @keepTs
// @ts-nocheck
/**
 * Created on 2024/11/04
 * <AUTHOR>
 */
import { ConversationIdentifier, Message } from '@rongcloud/imlib';
import { IBoardPlugin } from '../../../../../conversation/inputbar/component/plugin/IBoardPlugin';
/**
 * 相机插件标识
 * @version 1.0.0
 */
declare const CameraPluginName: string;
/**
 * 加号扩展栏的小视频插件
 * @version 1.0.0
 */
declare class CameraPlugin implements IBoardPlugin {
    private conId;
    pluginName(): string;
    obtainTitle(q143: Context): ResourceStr;
    obtainImage(p143: Context): ResourceStr;
    onClick(n143: Context, o143: ConversationIdentifier): void;
    onFilter(m143: ConversationIdentifier): boolean;
    private buildMediaMessage;
    private sendImageAction;
    private sendSightAction;
    private isConnected;
    checkConnectionStatus(l142: Message): Promise<void>;
    private sendMediaMessageWithRetry;
    private openCamera;
}
export { CameraPlugin, CameraPluginName };
