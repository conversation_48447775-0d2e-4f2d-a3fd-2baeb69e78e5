import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wuling_flutter_app/api/index.dart';
import 'package:wuling_flutter_app/models/post/post_detail_img_text_model.dart';
import 'package:wuling_flutter_app/models/post/post_detail_model.dart';
import 'package:wuling_flutter_app/page/base_page/base_page.dart';
import 'package:wuling_flutter_app/page/post/banner_image_view.dart';
import 'package:wuling_flutter_app/routes/jump_tool.dart';
import 'package:wuling_flutter_app/utils/date_time_util.dart';
import 'package:wuling_flutter_app/widgets/common/custom_refresher.dart';
import 'package:wuling_flutter_app/widgets/common/image_view.dart';
import 'package:wuling_flutter_app/widgets/post/post_detail_author_header_widget.dart';
import 'package:wuling_flutter_app/widgets/post/post_detail_banner_widget.dart';
import 'package:wuling_flutter_app/widgets/post/post_detail_title_widget.dart';

import '../../utils/manager/log_manager.dart';

class PostNormalDetailPage extends BasePage {
  final int postId;
  PostNormalDetailPage({
    required this.postId,
    Key? key,
    bool hideAppBar = false,
    bool isWithinSafeArea = false,
    String appBarTitle = '帖子详情',
    Color appBarColor = Colors.transparent,
    Color pageBackgroundColor = Colors.white,
  }) : super(
            key: key,
            hideAppBar: hideAppBar,
            isWithinSafeArea: isWithinSafeArea,
            appBarTitle: appBarTitle,
            appBarColor: appBarColor,
            pageBackgroundColor: pageBackgroundColor,
            enablePrivacyMode: true);

  @override
  BasePageState<BasePage> createState() => _PostDetailPageState();
}

class _PostDetailPageState extends BasePageState<PostNormalDetailPage> {
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  PostDetailModel? postDetailModel;
  final ScrollController _scrollController = ScrollController();
  bool _showTopButton = false;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _refreshController.requestRefresh();
    });

    _scrollController.addListener(() {
      setState(() {
        _showTopButton = _scrollController.offset > 60;
      });
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget? setAppBarLeading() {
    return IconButton(
      onPressed: () {
        Navigator.pop(context);
      },
      icon: const Icon(Icons.arrow_back_ios, color: Colors.black),
    );
  }

  @override
  Widget buildPageContent(BuildContext context) {
    return Stack(
      children: [
        Column(
          children: [
            Expanded(
              child: CustomSmartRefresher(
                controller: _refreshController,
                onRefresh: _onRefresh,
                onLoading: null,
                child: CustomScrollView(
                  controller: _scrollController,
                  slivers: [
                    getAuthorHeaderWidget(),
                    getBannerWidget(),
                    getTitleWidget(),
                    getTopicWidget(),
                    getImagesAndTextWidget(),
                    getTimeTitleRowWidget(),
                  ],
                ),
              ),
            ),
            postDetailModel == null
                ? Container()
                : Container(
                    //底部评论工具栏
                    padding: EdgeInsets.only(
                        bottom: MediaQuery.of(context).padding.bottom),
                    height: (MediaQuery.of(context).padding.bottom), //+ 50),
                    child: Row(
                      children: [
                        Expanded(
                            child: Container(
                          color: Colors.white,
                        ))
                      ],
                    ),
                  )
          ],
        ),
        if (_showTopButton)
          Positioned(
              bottom: (MediaQuery.of(context).padding.bottom) + 80,
              right: 0,
              child: InkWell(
                onTap: () {
                  _scrollToTop();
                },
                child: Container(
                  decoration: const BoxDecoration(
                    color: Color(0xff87CEFA),
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(4),
                        bottomLeft: Radius.circular(4)),
                  ),
                  width: 20,
                  height: 80,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: const [
                      Text(
                        '回到顶部',
                        textAlign: TextAlign.center,
                        style: TextStyle(fontSize: 11, color: Colors.white),
                      ),
                      Icon(
                        Icons.arrow_circle_up,
                        color: Colors.white,
                        size: 18,
                      )
                    ],
                  ),
                ),
              )),
      ],
    );
  }

  void _onRefresh() async {
    postAPI.requestDetailPostWithPostId(widget.postId.toString()).then((model) {
      setState(() {
        postDetailModel = model;
        if (_refreshController.isRefresh) {
          _refreshController.refreshCompleted();
        }
      });
    }).catchError((onError) {
      _refreshController.refreshCompleted();
    });
  }

  Widget getAuthorHeaderWidget() {
    return SliverToBoxAdapter(
      child: PostDetailAuthorHeader(
        postModel: postDetailModel,
        onHeaderTap: () {
          LogManager().debug('------点击头像');
        },
        onAttentionTap: () {
          LogManager().debug('------点击关注');
        },
      ),
    );
  }

  Widget getBannerWidget() {
    if (postDetailModel != null && postDetailModel!.postTypeId == 14) {
      return SliverToBoxAdapter(
        child: PostDetailBanner(
          postModel: postDetailModel,
        ),
      );
    }
    return SliverToBoxAdapter(child: Container());
  }

  Widget getTitleWidget() {
    return SliverToBoxAdapter(
        child: PostDetailTitle(postModel: postDetailModel));
  }

  Widget getTopicWidget() {
    if (postDetailModel != null &&
        (postDetailModel?.topicName ?? '').isNotEmpty &&
        (postDetailModel!.postTypeId == 14)) {
      return SliverToBoxAdapter(
        child: GestureDetector(
          onTap: () {
            LogManager().debug('--------点击话题#${postDetailModel?.topicName}#---------');
          },
          child: Padding(
            padding:
                const EdgeInsets.only(top: 15, bottom: 15, left: 20, right: 20),
            child: Text(
              '#${postDetailModel?.topicName}#',
              style: const TextStyle(
                color: Color(0xff4682B4),
                fontSize: 14,
                height: 1.5,
              ),
            ),
          ),
        ),
      );
    }
    return SliverToBoxAdapter(child: Container());
  }

  Widget getImagesAndTextWidget() {
    if (postDetailModel != null &&
        (postDetailModel?.imgTexts ?? []).isNotEmpty) {
      return SliverList(
        delegate: SliverChildBuilderDelegate(
          (context, index) {
            return buildContentItem(postDetailModel!.imgTexts![index]);
          },
          childCount: postDetailModel?.imgTexts?.length,
        ),
      );
    }
    return SliverToBoxAdapter(child: Container());
  }

  Widget buildContentItem(PostDetailImgTextModel imgTextModel) {
    if ((imgTextModel.img ?? '').isNotEmpty &&
        (postDetailModel!.postTypeId != 14)) {
      return Padding(
        padding: const EdgeInsets.only(left: 20, right: 20),
        child: Stack(
          children: [
            InkWell(
              onTap: () {
                if ((imgTextModel.linkUrl ?? '').isNotEmpty &&
                    imgTextModel.linkType != null) {
                  JumpTool().jumpToAdvertisePage(context,
                      linkType: imgTextModel.linkType!,
                      linkUrl: imgTextModel.linkUrl!,
                      eventPage: '图片');
                  return;
                }
                if (getAllImageList().isNotEmpty) {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => BannerImageViewer(
                          imageUrls: getAllImageList(),
                          initialIndex:
                              getAllImageList().indexOf(imgTextModel.img!)),
                    ),
                  );
                }
              },
              child: IntrinsicHeight(
                child: ImageView(
                  imgTextModel.img ?? '',
                  fit: BoxFit.fill,
                ),
              ),
            ),
            ((imgTextModel.linkUrl ?? '').isNotEmpty &&
                    imgTextModel.linkType != null)
                ? Positioned(
                    top: 10,
                    right: 15,
                    child: Container(
                        width: 20,
                        height: 20,
                        decoration: const BoxDecoration(
                          color: Color(0x7F000000),
                          borderRadius: BorderRadius.all(Radius.circular(10)),
                        ),
                        child: const Icon(
                          Icons.arrow_circle_right_outlined,
                          color: Colors.white,
                          size: 14,
                        )),
                  )
                : Container(),
          ],
        ),
      );
    } else if ((imgTextModel.text ?? '').isNotEmpty) {
      return Padding(
        padding:
            const EdgeInsets.only(top: 15, bottom: 15, left: 20, right: 20),
        child: Text(
          imgTextModel.text ?? '',
          style: const TextStyle(
            fontSize: 15,
            height: 1.5,
          ),
        ),
      );
    }
    return Container();
  }

  Widget getTimeTitleRowWidget() {
    TextStyle textStyle =
        const TextStyle(fontSize: 12, color: Color(0xff8B8989));
    if (postDetailModel != null &&
        (postDetailModel?.postTitle ?? '').isNotEmpty) {
      return SliverToBoxAdapter(
        child: Padding(
          padding:
              const EdgeInsets.only(top: 15, bottom: 15, left: 20, right: 20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                getCreateDateStr(),
                style: textStyle,
              ),
              Row(
                children: [
                  Text(
                    '围观： ${formatNumberToK(postDetailModel?.postShowCount ?? 0)}',
                    style: textStyle,
                  ),
                  const SizedBox(
                    width: 20,
                  ),
                  Text(
                    '点赞： ${formatNumberToK(postDetailModel?.praiseCount ?? 0)}',
                    style: textStyle,
                  )
                ],
              )
            ],
          ),
        ),
      );
    }
    return SliverToBoxAdapter(child: Container());
  }

  String getCreateDateStr() {
    String createDateStr = '';
    double? timeInterval = postDetailModel?.creationDate?.toDouble();
    DateTime? createDate =
        DateTimeUtil.dateWithMillisecondsSince1970(timeInterval);
    createDateStr =
        DateTimeUtil.stringFromDate(createDate, format: 'yyyy年MM月dd日 HH:mm');
    return createDateStr;
  }

  void _scrollToTop() {
    _scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 1),
      curve: Curves.easeInOut,
    );
  }

  String formatNumberToK(int number) {
    if (number >= 1000) {
      if (number >= 10000) {
        return "${(number / 1000).truncate()}K";
      } else {
        if (number == 1000) {
          return '1K';
        }
        double formatted = number / 1000;
        int integerPart = formatted.truncate();
        int decimalPart = ((formatted - integerPart) * 10).truncate();
        return "$integerPart.${decimalPart == 0 ? '' : decimalPart}K";
      }
    } else {
      return "$number";
    }
  }

  List<String> getAllImageList() {
    if (postDetailModel != null && postDetailModel!.imgTexts != null) {
      List<String> stringList = postDetailModel!.imgTexts!
          .where((model) => model.img != null && model.img!.isNotEmpty)
          .map((model) => model.img!)
          .toList();
      if (stringList.isNotEmpty) return stringList;
    }
    return [];
  }
}
