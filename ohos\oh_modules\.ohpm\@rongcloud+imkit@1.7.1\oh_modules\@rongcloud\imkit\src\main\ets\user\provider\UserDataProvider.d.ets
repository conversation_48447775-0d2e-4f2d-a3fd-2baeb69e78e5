// @keepTs
// @ts-nocheck
import { GroupInfoModel } from '../model/GroupInfoModel';
import { GroupMemberInfoModel } from '../model/GroupMemberInfoModel';
import { UserInfoModel } from '../model/UserInfoModel';
/**
 * 用户信息提供者
 * 当 SDK 需要展示用户信息（比如会话列表头像名称、聊天页面消息的头像名称）时
 * SDK 优先从本地读取，如果本地读取不到就触发 UserDataProvider 向 app 索取用户信息
 * app 将用户信息返给 SDK 之后，SDK 就会一直使用
 * 如果 app 需要更新该信息，请调用 UserDataService 相关的 update 方法
 * @version 1.0.0
 */
export interface UserDataProvider {
    /**
     * 获取用户信息数据，当需要展示用户头像、昵称时，如果 SDK 没有对应的信息时触发该方法
     *```
     * 获取到用户信息后，SDK 会缓存该数据，并触发监听
     * SDK 缓存之后就会一直使用，不会自动更新。如果用户信息发生变化，需要调用 UserDataService.updateUserInfo 主动刷新 SDK 信息
     *```
     * @param userId 用户 Id
     * @returns 用户信息。注意：如果返回 undefined ，SDK则不会更新缓存。
     */
    fetchUserInfo?: (userId: string) => Promise<UserInfoModel | undefined>;
    /**
     * 是否持久化存储用户信息到 SDK 的本地数据库, 默认为 true,
     * @since 1.6.0
     */
    isCacheUserInfo?: boolean;
    /**
     * 获取群组信息数据，当需要展示群组头像、名称时，如果 SDK 没有对应的信息时触发该方法
     *```
     * 获取到群组信息后，SDK 会缓存该数据，并触发监听
     * SDK 缓存之后就会一直使用，不会自动更新。如果群组信息发生变化，需要调用 UserDataService.updateGroupInfo 主动刷新 SDK 信息
     *```
     * @param groupId 群 id
     * @returns 群组信息。注意：如果返回 undefined ，SDK则不会更新缓存。
     */
    fetchGroupInfo?: (groupId: string) => Promise<GroupInfoModel | undefined>;
    /**
     * 是否持久化存储群组信息到 SDK 的本地数据库, 默认为 true,
     * @since 1.6.0
     */
    isCacheGroupInfo?: boolean;
    /**
     * 获取群组成员信息数据，当需要展示群组成员头像、名称时，如果 SDK 没有对应的信息时触发该方法
     *```
     * 获取到群组成员信息后，SDK 会缓存该数据，并触发监听
     * SDK 缓存之后就会一直使用，不会自动更新。如果群组成员信息发生变化，需要调用 UserDataService.updateGroupMemberInfo 主动刷新 SDK 信息
     *```
     * @param groupId 群 id
     * @param userId 用户 Id
     * @returns 群组成员信息。注意：如果返回 undefined ，SDK则不会更新缓存。
     */
    fetchGroupMemberInfo?: (groupId: string, userId: string) => Promise<GroupMemberInfoModel | undefined>;
    /**
     * 是否持久化存储群成员信息到 SDK 的本地数据库, 默认为 true,
     * @since 1.6.0
     */
    isCacheGroupMemberInfo?: boolean;
    /**
     * 获取群成员信息数据列表，当需要展示群成员头像、名称时，如果 SDK 没有对应的信息时触发该方法
     *```
     * 获取到群组成员信息后，SDK 会缓存该数据，并触发监听
     * SDK 缓存之后就会一直使用，不会自动更新。如果群组成员信息发生变化，需要调用 UserDataService.updateGroupMemberInfo 主动刷新 SDK 信息
     *```
     * @param groupId 群 id
     * @returns 群成员信息列表
     */
    fetchGroupMemberInfos?: (groupId: string) => Promise<Array<GroupMemberInfoModel> | undefined>;
}
