import { MirrorMode, OnCompletionListener, OnErrorListener, OnInfoListener, OnLoadingStatusListener, OnPreparedListener, OnRenderingStartListener, OnSeekCompleteListener, OnStateChangedListener, OnSubtitleDisplayListener, OnVideoRenderedListener, OnVideoSizeChangedListener, ScaleMode, OnAudioInterruptEventListener, OnTrackChangedListener, OnTrackReadyListener, OnSnapShotListener, OnSeiDataListener, OnSubTrackReadyListener, OnStreamSwitchedListener, OnAVNotSyncStatusListener, AlphaRenderMode } from '../IPlayer';
import { Context } from '@ohos.abilityAccessCtrl';
import { PlayerConfig } from './PlayerConfig';
import { TrackInfo } from '../nativeclass/TrackInfo';
import { CacheConfig } from './CacheConfig';
export declare class NativePlayerBase {
    private mOnPreparedListener?;
    private mOnInfoListener?;
    private mOnRenderingStartListener?;
    private mOnStateChangedListener?;
    private mOnCompletionListener?;
    private mOnLoadingStatusListener?;
    private mOnAVNotSyncStatusListener?;
    private mOnErrorListener?;
    private mOnVideoSizeChangedListener?;
    private mOnAudioInterruptEventListener?;
    private mOnSeekCompleteListener?;
    private mOnSubtitleDisplayListener?;
    private mOnVideoRenderedListener?;
    private mOnTrackChangedListener?;
    private mOnTrackReadyListener?;
    private mOnSubTrackReadyListener?;
    private mOnStreamSwitchedListener?;
    private mOnSnapShotListener?;
    private mOnSeiDataListener?;
    private mNativeContext;
    private mContext?;
    private mPlayerConfig?;
    private objHelper;
    private getNativeContext;
    private setNativeContext;
    private getHelper;
    private getContext;
    constructor(i26: Context);
    private onPrepared;
    protected onCircleStart: Function;
    protected onAutoPlayStart: Function;
    protected onEvent: Function;
    protected onCurrentDownloadSpeed: Function;
    protected onUtcTimeUpdate: Function;
    protected onLocalCacheLoad: Function;
    protected onBufferedPositionUpdate: Function;
    protected onCurrentPositionUpdate: Function;
    protected onFirstFrameShow: Function;
    protected onVideoSizeChanged: Function;
    protected onAudioInterruptEvent: Function;
    protected onLoadingStart: Function;
    protected onLoadingProgress: Function;
    protected onLoadingEnd: Function;
    protected onAVNotSyncStart: Function;
    protected onAVNotSyncEnd: Function;
    protected onSeekEnd: Function;
    onStatusChanged: Function;
    protected onCompletion: Function;
    protected onVideoRendered: Function;
    protected onShowSubtitle: Function;
    protected onSubtitleExtAdded: Function;
    protected onHideSubtitle: Function;
    protected onSubtitleHeader: Function;
    protected onError: Function;
    protected onStreamSwitchSuc: Function;
    protected onStreamInfoGet: Function;
    protected onSubStreamInfoGet: Function;
    protected onSwitchStreamUrlResult: Function;
    protected onCaptureScreen: Function;
    protected onSeiDataCallback: Function;
    setOnPreparedListener(h26: OnPreparedListener): void;
    setOnInfoListener(g26: OnInfoListener): void;
    setOnRenderingStartListener(f26: OnRenderingStartListener): void;
    setOnStateChangedListener(e26: OnStateChangedListener): void;
    setOnCompletionListener(d26: OnCompletionListener): void;
    setOnLoadingStatusListener(c26: OnLoadingStatusListener): void;
    setOnErrorListener(b26: OnErrorListener): void;
    setOnVideoSizeChangedListener(a26: OnVideoSizeChangedListener): void;
    setOnSeekCompleteListener(z25: OnSeekCompleteListener): void;
    setOnSubtitleDisplayListener(y25: OnSubtitleDisplayListener): void;
    setOnVideoRenderedListener(x25: OnVideoRenderedListener): void;
    setOnAudioInterruptEventListener(w25: OnAudioInterruptEventListener): void;
    setOnTrackReadyListener(v25: OnTrackReadyListener): void;
    setOnSubTrackReadyListener(u25: OnSubTrackReadyListener): void;
    setOnStreamSwitchedListener(t25: OnStreamSwitchedListener): void;
    setOnAVNotSyncStatusListener(s25: OnAVNotSyncStatusListener): void;
    setOnTrackChangedListener(r25: OnTrackChangedListener): void;
    setOnSnapShotListener(q25: OnSnapShotListener): void;
    setOnSeiDataListener(p25: OnSeiDataListener): void;
    start(): void;
    pause(): void;
    stop(): void;
    prepare(): void;
    setAutoPlay(o25: boolean): void;
    setSurfaceId(n25: string): void;
    setSpeed(m25: number): void;
    switchStream(l25: string): void;
    setAlphaRenderMode(k25: AlphaRenderMode): void;
    getAlphaRenderMode(): AlphaRenderMode;
    selectTrack(j25: number): void;
    getCurrentTrack(h25: number): TrackInfo;
    addExtSubtitle(g25: string): void;
    selectExtSubtitle(e25: number, f25: boolean): void;
    setVolume(d25: number): void;
    getVolume(): number;
    seekTo(b25: number, c25: number): void;
    setStartTime(z24: number, a25: number): void;
    getDuration(): number;
    setGlobalTime(y24: string): void;
    getPlayedDuration(): number;
    getCurrentPosition(): number;
    getBufferedPosition(): number;
    getPlayerStatus(): number;
    setTraceId(x24: string): void;
    enableHardwareDecoder(w24: boolean): void;
    release(): void;
    setMute(v24: boolean): void;
    isMuted(): boolean;
    setScaleMode(u24: ScaleMode): void;
    getScaleMode(): ScaleMode;
    setLoop(t24: boolean): void;
    isLoop(): boolean;
    getVideoWidth(): number;
    getVideoHeight(): number;
    getVideoRotation(): number;
    reload(): void;
    setRotateMode(s24: number): void;
    getRotateMode(): number;
    setMirrorMode(r24: MirrorMode): void;
    getMirrorMode(): MirrorMode;
    setVideoBackgroundColor(q24: number): void;
    getSpeed(): number;
    isAutoPlay(): boolean;
    setConfig(p24: PlayerConfig): void;
    getConfig(): PlayerConfig | undefined;
    setOption(m24: string, n24: string): void;
    getOption(j24: string): string | number;
    setStreamDelay(h24: number, i24: number): void;
    setMaxAccurateSeekDelta(g24: number): void;
    setCacheConfig(f24: CacheConfig): void;
    setIPResolveType(e24: number): void;
    setFastStart(d24: boolean): void;
    snapShot(): void;
    clearScreen(): void;
    getSdkVersion(): string;
    getCacheFilePathByUrl(c24: string): string;
    getCacheFilePathByVid(y23: string, z23: string, a24: string, b24: number): string;
    getPropertyString(x23: number): string;
    setDefaultBandWidth(w23: number): void;
    sendCustomEvent(v23: string): void;
    setVideoTag(u23: number[]): void;
    setUserData(t23: string): void;
    getUserData(): string;
    getNativeContextAddr(): number;
}
