import { ErrorInfo } from '../player/bean/ErrorInfo';
import { MediaInfo } from '../player/nativeclass/MediaInfo';
import { VidAuth } from '../player/source/VidAuth';
import { VidSts } from '../player/source/VidSts';
import { DownloaderConfig } from './DownloaderConfig';
export interface AliMediaDownloader {
    /**
     * 开始下载。
     */
    /****
     * Start downloading.
     */
    start: () => void;
    /**
     * 停止下载。
     */
    /****
     * Stop downloading.
     */
    stop: () => void;
    /**
     * 释放下载。
     */
    /****
     * Release downloading.
     */
    release: () => void;
    /**
     * 设置下载保存的文件夹路径
     *
     * @param absPath 绝对路径
     */
    /****
     * Specify the directory where the downloaded file is saved.
     *
     * @param absPath The absolute directory.
     */
    setSaveDir: (absPath: string) => void;
    /**
     * 获取下载成功后的文件地址。
     *
     * @return 文件保存的地址
     */
    /****
     * Query the path of the downloaded file.
     *
     * @return The path of the downloaded file.
     */
    getFilePath: () => string;
    /**
     * 选择要下载的项
     *
     * @param index 下载项的索引
     */
    /****
     * Specify a track to download.
     *
     * @param index The index of the track.
     */
    selectItem: (index: number) => void;
    /**
     * 通过VidAuth准备下载资源
     *
     * @param vidAuth 见{@linkplain VidAuth}
     */
    /****
     * Download resources through VidAuth.
     *
     * @param vidAuth See {@linkplain VidAuth}.
     */
    prepareVidAuth: (vidAuth: VidAuth) => void;
    /**
     * 通过VidSts准备下载资源
     *
     * @param vidSts 见{@linkplain VidSts}
     */
    /****
     * Download resources through VidSts
     *
     * @param vidSts  See {@linkplain VidSts}.
     */
    prepareVidSts: (vidSts: VidSts) => void;
    /**
     * 更新Vidsts信息。
     *
     * @param vidSts 见{@linkplain VidSts}
     */
    /****
     * Update VidSts information.
     *
     * @param vidSts  See {@linkplain VidSts}.
     */
    updateVidStsSource: (vidSts: VidSts) => void;
    /**
     * 更新VidAuth信息
     *
     * @param vidAuth 见{@linkplain VidAuth}
     */
    /****
     * Update VidAuth information.
     *
     * @param vidAuth  See {@linkplain VidAuth}.
     */
    updateVidAuthSource: (vidAuth: VidAuth) => void;
    /**
     * 删除文件
     */
    /****
     * Delete a specified file.
     */
    deleteFile: () => void;
    setDownloaderConfig: (config: DownloaderConfig) => void;
    /**
     * 设置准备完成监听
     *
     * @param l 准备完成监听
     */
    /****
     * Set a download preparation completion callback.
     *
     * @param l The download preparation completion callback.
     */
    setOnPreparedListener: (listener: OnPreparedListener) => void;
    /**
     * 设置下载进度监听
     *
     * @param l 下载进度监听
     */
    /****
     * Set a download progress callback.
     *
     * @param l The download progress callback.
     */
    setOnProgressListener: (listener: OnProgressListener) => void;
    /**
     * 设置下载成功监听
     *
     * @param l 下载成功监听
     */
    /****
     * Set a download success callback.
     *
     * @param l The download success callback.
     */
    setOnCompletionListener: (listener: OnCompletionListener) => void;
    /**
     * 设置下载失败监听
     *
     * @param l 下载失败监听
     */
    /****
     * Set a download failure callback.
     *
     * @param l The download failure callback.
     */
    setOnErrorListener: (listener: OnErrorListener) => void;
}
/**
 * 下载准备成功回调.成功后，通过{@linkplain #selectItem(int)} 方法选择需要下载的Track.
 */
/****
 * The download preparation success callback. After the download is prepared, call the {@linkplain #selectItem(int)} method to specify the track that you want to download.
 */
export interface OnPreparedListener {
    /**
     * 回调
     *
     * @param mediaInfo 媒体信息。 见{@link MediaInfo}
     */
    /****
     * Callback.
     *
     * @param mediaInfo Media information. See {@link MediaInfo}.
     */
    onPrepared: (mediaInfo: MediaInfo) => void;
}
/**
 * 进度回调
 */
/****
 * The download progress callback.
 */
export interface OnProgressListener {
    /**
     * 下载进度百分比。
     *
     * @param percent 百分比
     */
    /****
     * The download progress in percentage.
     *
     * @param percent The returned percentage value.
     */
    onDownloadingProgress: (percent: number) => void;
    /**
     * 处理进度百分比
     *
     * @param percent 百分比
     */
    /****
     * The processing progress in percentage.
     *
     * @param percent The returned percentage value.
     */
    onProcessingProgress: (percent: number) => void;
}
/**
 * 下载完成消息回调
 */
/****
 * The download completion callback.
 */
export interface OnCompletionListener {
    /**
     * 完成消息
     */
    /****
     * Download completion message.
     */
    onCompletion: () => void;
}
/**
 * 错误消息回调
 */
/****
 * Error callback.
 */
export interface OnErrorListener {
    /**
     * 下载错误的消息
     *
     * @param errorInfo 错误信息。 见{@link ErrorInfo}
     */
    /****
     * Download error message.
     *
     * @param errorInfo The error message. See {@link ErrorInfo}.
     */
    onError: (errorInfo: ErrorInfo) => void;
}
/**
 * 下载之前转化下载地址
 */
/****
 * Convert a specified URL before downloading the relevant media file.
 */
export interface ConvertURLCallback {
    /**
     * @param srcURL    原始地址
     * @param srcFormat 原始格式
     * @return 转化后的地址。如果返回null，则使用原来的地址
     */
    /****
     * @param srcURL    The original URL.
     * @param srcFormat The original format.
     * @return The converted URL. If null is returned, the original URL is used.
     */
    convertURL: (srcURL: string, srcFormat: string) => string;
}
