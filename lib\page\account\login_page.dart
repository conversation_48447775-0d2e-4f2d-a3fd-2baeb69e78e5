// login_screen.dart
import 'dart:convert';

import 'package:flutter_uikit/flutter_uikit.dart';
import 'package:flutter_uikit/ui_button.dart';
import 'package:wuling_flutter_app/common/action.dart';
import 'package:wuling_flutter_app/constant/constant.dart';
import 'package:wuling_flutter_app/utils/manager/get_quick_login_info_manager.dart';
import 'package:wuling_flutter_app/utils/manager/log_manager.dart';
import 'package:wuling_flutter_app/widgets/account/huawei_login_widgets.dart';
import 'package:wuling_flutter_app/widgets/common/image_view.dart';
import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/page/account/login_password_page.dart';
import 'package:wuling_flutter_app/page/account/login_verification_code_page.dart';
import 'package:wuling_flutter_app/widgets/common/infinite_scroll_background.dart';

import '../../api/user_api.dart';
import '../../constant/storage.dart';
import '../../models/global_data.dart';
import '../../models/user/oauth_model.dart';
import '../../plugins/share_plugin/share_plugin.dart';
import '../../utils/manager/login_manager.dart';
import '../../utils/manager/notification_manager.dart';
import '../../utils/sp_util.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen>
    with SingleTickerProviderStateMixin {
  // 定义状态变量

  late AnimationController _controller;
  late Animation<double> _animation;
  double screenHeight = 0.0; //屏幕高度
  final double bgImageHeight = 2950; // 背景图片的实际高度
  double endPosition = 0; // 结束位置
  bool _isHuaweiLogin = false;//是否是华为一键登录
  String phoneNum = "";

  @override
  void initState() {
    super.initState();
    Constant.printAllConstants();
    GetQuickLoginInfoManager.getQuickLoginInfo().then((value){
      if(value != null){
        setState(() {
          LogManager().debug('getQuickLoginInfo-----:$value');
          _isHuaweiLogin = true;
          Map<String, dynamic> data = jsonDecode(value);
          phoneNum = data['anonymousPhone'];
        });
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _isHuaweiLogin ? HuaweiLoginWidget(phoneNum: phoneNum,otherClicked: (){
        setState(() {
          _isHuaweiLogin = false;
        });
      }) : Stack(
        children: [
          // 背景图片
          Positioned.fill(
            child: InfiniteScrollBackground(imagePath: 'assets/images/login/login_back_bg.jpg',),
          ),
          // 欢迎文字
          Positioned(
            top: 125,
            left: 28,
            child: SizedBox(
              width: 305,
              height: 112,
              child: Image.asset(
                'assets/images/login/login_welcome.png',
                fit: BoxFit.cover,
              ),
            ),
          ),
          // 按钮
          Positioned(
            bottom: 80,
            left: 28,
            right: 28,
            child: Column(
              children: [
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFFea0029), // 背景色
                    minimumSize: const Size(double.infinity, 50),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(4), // 圆角半径
                    ),
                  ),
                  onPressed: () {
                    // 处理密码登录
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => LoginPasswordScreen(),
                      ),
                    );
                  },
                  child: const Text('密码登录',
                      style: TextStyle(color: Colors.white, fontSize: 16)),
                ),
                const SizedBox(height: 12),
                Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      flex: 1,
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent, // 背景色
                          side: const BorderSide(color: Colors.white, width: 1),
                          minimumSize: const Size(0, 43),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4), // 圆角半径
                          ),
                        ),
                        onPressed: () {
                          // 处理验证码登录
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  LoginVerificationCodePage(
                                      type: LoginVerificationCodeType.SMSLogin),
                            ),
                          );
                        },
                        child: const Text(
                          '验证码登录',
                          style: TextStyle(color: Colors.white, fontSize: 14),
                        ),
                      ),
                    ),
                    const SizedBox(width: 20),
                    Expanded(
                      flex: 1,
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.transparent, // 背景色
                          side: const BorderSide(color: Colors.white, width: 1),
                          minimumSize: const Size(0, 43),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4), // 圆角半径
                          ),
                        ),
                        onPressed: () {
                          // 处理注册
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  LoginVerificationCodePage(
                                      type:
                                          LoginVerificationCodeType.Register),
                            ),
                          );
                        },
                        child: const Text(
                          '注册',
                          style: TextStyle(color: Colors.white, fontSize: 14),
                        ),
                      ),
                    )
                  ],
                ),
                const SizedBox(height: 18),
                TextButton(
                  onPressed: () {
                    // 处理随便看看
                    Navigator.pop(context);
                  },
                  child: const Text(
                    '先随便看看',
                    style: TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        decoration: TextDecoration.underline),
                  ),
                ),
                const SizedBox(height: 158),
                // UIButton(onPressed: (){
                //   SharePlugin.login({}).then((value) async {
                //     OauthModel model = await userAPI.oauthLoginWithWX(value['code'],Constant.APP_CLIENT_ID , Constant.APP_CLIENT_SECRET);
                //     print(model.toJson());
                //     if(model.wxUnionId.length > 5){
                //       NavigatorAction.init(context,view: LoginVerificationCodePage(type: LoginVerificationCodeType.Binding,code: value['code'],unionId: model.wxUnionId,));
                //     }else{
                //       GlobalData().oauthModel = model; //设置到全局变量
                //       SpUtil().setJSON(SP_USER_OAUTH_KEY, model.toJson()); //保存到本地
                //       NavigatorAction.init(context);
                //       await LoginManager().getSelfInfo();
                //       //通知登录成功
                //       NotificationManager()
                //           .postNotification(Constant.NOTIFICATION_LOGIN_SUCCEED);
                //     }
                //   });
                // },color: 0x00FFFFFF, buttonState: UIButtonState(
                //   buttonType: UIButtonType.top,
                //   title: "微信登录",
                //   fontSize: 13,
                //   spacing: 10,
                // ),buttonImage: UIImage(imgStr: "assets/images/store/weixin.png",width: 40,height: 40,imgColor: 0xFFFFFFFF,),)
              ],
            ),
          ),
        ],
      ),
    );
  }
}
