import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import MessageCodec from '@ohos/flutter_ohos/src/main/ets/plugin/common/MessageCodec';
export declare enum StorageDirectory {
    ROOT = 0,
    MUSIC = 1,
    PODCASTS = 2,
    RINGTONES = 3,
    ALARMS = 4,
    NOTIFICATIONS = 5,
    PICTURES = 6,
    MOVIES = 7,
    DOWNLOADS = 8,
    DCIM = 9,
    DOCUMENTS = 10
}
export default class Messages {
    static wrapError(exception: Error): Array<ESObject>;
}
export declare class FlutterError extends Error {
    code: string;
    details: ESObject;
    constructor(code: string, message: string, details: ESObject);
}
export declare abstract class PathProviderApi {
    abstract getTemporaryPath(): string;
    abstract getApplicationSupportPath(): string;
    abstract getApplicationDocumentsPath(): string;
    abstract getApplicationCachePath(): string;
    abstract getExternalStoragePath(): string;
    abstract getExternalCachePaths(): Array<string>;
    abstract getExternalStoragePaths(directory: StorageDirectory): Array<string>;
    static getCodec(): MessageCodec<ESObject>;
    static setup(binaryMessenger: BinaryMessenger, api: PathProviderApi): void;
}
