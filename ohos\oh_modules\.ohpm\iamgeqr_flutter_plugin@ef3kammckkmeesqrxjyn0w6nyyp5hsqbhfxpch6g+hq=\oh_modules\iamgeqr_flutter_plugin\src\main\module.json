{"app": {"bundleName": "com.sgmw.wuling", "debug": true, "versionCode": 59, "versionName": "1.1.1", "minAPIVersion": 50000012, "targetAPIVersion": 50101019, "apiReleaseType": "Beta1", "compileSdkVersion": "5.1.1.66", "compileSdkType": "HarmonyOS", "appEnvironments": [], "bundleType": "app", "buildMode": "debug"}, "module": {"name": "iamgeqr_flutter_plugin", "type": "har", "deviceTypes": ["default", "tablet"], "packageName": "iamgeqr_flutter_plugin", "installationFree": false, "virtualMachine": "ark12.0.2.0", "compileMode": "esmodule", "dependencies": []}}