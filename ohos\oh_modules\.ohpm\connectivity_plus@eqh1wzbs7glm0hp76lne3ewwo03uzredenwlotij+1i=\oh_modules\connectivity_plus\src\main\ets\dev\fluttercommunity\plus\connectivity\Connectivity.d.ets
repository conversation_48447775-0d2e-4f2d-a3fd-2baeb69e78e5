import connection from '@ohos.net.connection';
export declare class Connectivity {
    static CONNECTIVITY_NONE: string;
    private static CONNECTIVITY_WIFI;
    private static CONNECTIVITY_MOBILE;
    private static CONNECTIVITY_ETHERNET;
    private static CONNECTIVITY_BLUETOOTH;
    private static CONNECTIVITY_VPN;
    static netConnection: connection.NetConnection;
    networkType: String;
    constructor();
    getNetworkType(): Promise<String>;
    hasTransport(capabilities: Array<number>): String;
}
