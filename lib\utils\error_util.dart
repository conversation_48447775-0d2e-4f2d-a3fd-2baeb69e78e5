/*
 * @Author: 余邦锦（ <PERSON><PERSON><PERSON> ）
 * @Email: <EMAIL>
 * @Address: 浙江省杭州市余杭区西溪绿境
 * @project-group: 阿里巴巴-智慧建筑
 * @Date 2021-08-04 10:27 上午 星期三
 * @slogan: 每行代码的开始都意味着新的征程
 * @description: error_util
 */

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/common/action.dart';

import 'manager/log_manager.dart';

class ErrorUtil {
  /// 初始化对象
  ErrorUtil();

  /// 实例类
  static ErrorUtil? _instance;

  /// 工厂方法
  factory ErrorUtil.install() {
    if (_instance == null) {
      LogManager().debug("instance");
      _instance = ErrorUtil();
    }
    return _instance!.._init();
  }

  // 初始化
  void _init() {
    FlutterError.onError = (FlutterErrorDetails details) async {
      LogManager().debug("onError");
      Zone.current.handleUncaughtError(details.exception, details.stack!);
      FlutterError.dumpErrorToConsole(details);
    };
    ErrorWidget.builder = (FlutterErrorDetails details) {
      Zone.current.handleUncaughtError(details.exception, details.stack!);
      return details.context!.value.toString().contains("Page")
          ? Scaffold(
        appBar: AppBar(
          title: const Text("500"),
        ),
        body: const Center(
          child: Text(
            "加载异常...",
            style: TextStyle(color: Colors.red),
          ),
        ),
      )
          : const Center(
        child: Text(
          "加载异常...",
          style: TextStyle(color: Colors.red),
        ),
      );
    };
  }
}
