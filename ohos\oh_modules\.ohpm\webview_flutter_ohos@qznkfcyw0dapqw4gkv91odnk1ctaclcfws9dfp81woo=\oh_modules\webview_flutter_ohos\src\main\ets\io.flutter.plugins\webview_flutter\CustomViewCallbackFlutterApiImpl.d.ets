import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import { CustomViewCallback, CustomViewCallbackFlutterApi, Reply } from './GeneratedOhosWebView';
import { InstanceManager } from './InstanceManager';
export declare class CustomViewCallbackFlutterApiImpl {
    binaryMessenger: BinaryMessenger;
    instanceManager: InstanceManager;
    api: CustomViewCallbackFlutterApi;
    constructor(binaryMessenger: BinaryMessenger, instanceManager: InstanceManager);
    create(instance: CustomViewCallback, callback: Reply<void>): void;
    setApi(api: CustomViewCallbackFlutterApi): void;
}
