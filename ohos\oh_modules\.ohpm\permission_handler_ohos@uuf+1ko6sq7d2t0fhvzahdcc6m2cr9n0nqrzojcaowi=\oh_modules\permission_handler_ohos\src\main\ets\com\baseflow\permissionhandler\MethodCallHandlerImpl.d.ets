import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import common from '@ohos.app.ability.common';
import MethodCall from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodCall';
import { AppSettingManager } from './AppSettingManager';
import { PermissionManager } from './PermissionManager';
import { ServiceManager } from './ServiceManager';
import UIAbility from '@ohos.app.ability.UIAbility';
export declare class MethodCallHandlerImpl implements MethodCallHandler {
    private applicationContext;
    private permissionManager;
    private appSettingManager;
    private serviceManager;
    private ability;
    constructor(applicationContext: common.Context, appSettingManager: AppSettingManager, permissionManager: PermissionManager, serviceManager: ServiceManager);
    setAbility(ability: UIAbility | null): void;
    onMethodCall(call: Method<PERSON>all, result: MethodResult): void;
}
