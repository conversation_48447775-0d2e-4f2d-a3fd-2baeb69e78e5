import { ListPlayerBase } from './ListPlayerBase';
import premierlibrary from 'libpremierlibrary.so';
export class OhosUrlListPlayer extends ListPlayerBase {
    constructor(b31, c31, d31, e31) {
        super(b31, c31, d31, e31);
    }
    addUrl(z30, a31) {
        premierlibrary.nListAddUrl(this, z30, a31);
    }
    getPreRenderPlayerIndex() {
        return premierlibrary.nListGetPreRenderPlayerIndex(this);
    }
    getCurrentPlayerIndex() {
        return premierlibrary.nListGetCurrentPlayerIndex(this);
    }
    moveToNext(x30) {
        let y30 = premierlibrary.nListMoveToNext(this, x30);
        if (y30 == 1) {
            return true;
        }
        else {
            return false;
        }
    }
    moveToPrev() {
        let w30 = premierlibrary.nListMoveToPrev(this);
        if (w30 == 1) {
            return true;
        }
        else {
            return false;
        }
    }
    moveTo(u30) {
        let v30 = premierlibrary.nListMoveTo(this, u30);
        if (v30 == 1) {
            return true;
        }
        else {
            return false;
        }
    }
}
