import { Context } from '@kit.AbilityKit'; import { CoordTypeInfoManager } from "./m1/p1"; import { IdentityInfoManager } from "./m1/q1"; export declare enum InfoManagerKeys { Identity = "identity", Hardware = "hardware", CoordType = "coordType" } export declare class DeviceMetadataManager { private static instance; private managers; private constructor(); static getInstance(): DeviceMetadataManager; private registerManagers;               initialize(context?: Context): Promise<void>;         getIdentityInfoManager(): IdentityInfoManager;         getCoordTypeInfoManager(): CoordTypeInfoManager; private getManager; } 