// @keepTs
// @ts-nocheck
/**
 * Created on 2024/7/29
 * <AUTHOR>
 */
import { MessageContent } from '@rongcloud/imlib';
import { BaseNotificationMessageItemProvider } from '../../../../conversation/item/provider/BaseNotificationMessageItemProvider';
import { UiMessage } from '../../../../conversation/model/UiMessage';
export declare class UnknownMessageItemProvider extends BaseNotificationMessageItemProvider<MessageContent> {
    getMessageWrapBuilder(): WrappedBuilder<[
        Context,
        UiMessage,
        number
    ]>;
    getSummaryTextByMessageContent(n210: Context, o210: MessageContent): Promise<MutableStyledString>;
}
@Builder
export declare function bindUnknownMessageData(d210: Context, e210: UiMessage, f210: number): void;
