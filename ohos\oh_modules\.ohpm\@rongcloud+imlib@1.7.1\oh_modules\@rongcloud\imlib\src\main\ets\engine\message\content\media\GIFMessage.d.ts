import { MediaMessageContent } from '../MediaMessageContent';
/**
 * 消息标识
 * @version 1.2.0
 */
declare const GIFMessageObjectName = "RC:GIFMsg";
/**
 * gif 消息
 * # 示例代码
 *```
 *  // 设置宽高
 *  import image from '@ohos.multimedia.image';
 *
 *  let imgSource = image.createImageSource(gifMsg.localPath);
 *  let imgInfo = imgSource.getImageInfoSync();
 *  gifMsg.width = imgInfo.size.width;
 *  gifMsg.height = imgInfo.size.height;
 *
 *  // 设置 gifDataSize
 *  import fs from '@ohos.file.fs';
 *
 *  gifMsg.gifDataSize = fs.statSync(gifMsg.localPath).size;
 *```
 * @version 1.2.0
 * @warning gif 大小受导航控制
 */
declare class GIFMessage extends MediaMessageContent {
    /**
     * GIF 图片大小，单位字节
     */
    gifDataSize: number;
    /**
     * GIF 图片宽度
     */
    width: number;
    /**
     * GIF 图片高度
     */
    height: number;
    /**
     * 无参构造方法
     */
    constructor();
    /**
     * 编码方法，将消息转为 json 字符串
     * @returns json 字符串
     */
    encode(): string;
    /**
     * 解码方法，将 json 字符串转为消息
     * @param contentString json 字符串
     */
    decode(contentString: string): void;
    /**
     * 获取类名
     * @returns 类名
     */
    getClassName(): string;
    private correctDataIfNeed;
}
export { GIFMessage, GIFMessageObjectName };
