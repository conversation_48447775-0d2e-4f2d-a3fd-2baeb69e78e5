import camera from '@ohos.multimedia.camera';
import { Rect } from '@ohos.application.AccessibilityExtensionAbility';
export interface CameraProperties {
    getCameraName(): string;
    getControlAutoExposureCompensationRange(captureSession: camera.PhotoSession | camera.VideoSession): Array<number>;
    getControlAutoExposureCompensationStep(): number;
    getControlAutoFocusAvailableModes(): number[];
    getControlMaxRegionsAutoExposure(): number;
    getControlMaxRegionsAutoFocus(): number;
    getDistortionCorrectionAvailableModes(): number[];
    getFlashInfoAvailable(): boolean;
    getLensFacing(): number;
    getLensInfoMinimumFocusDistance(): number;
    getScalerAvailableMaxDigitalZoom(): number;
    getSensorInfoActiveArraySize(): Rect;
    getSensorOrientation(): number;
    getHardwareLevel(): number;
    getAvailableNoiseReductionModes(): number[];
}
