import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/services.dart';
import 'package:wuling_flutter_app/models/car/car_service_param_model.dart';
import 'package:wuling_flutter_app/models/index.dart';
import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/utils/manager/loading_manager.dart';
// import 'package:wuling_flutter_app/utils/app_config_util.dart';
import 'dart:ui';
import 'package:wuling_flutter_app/widgets/common/custom_slider.dart';
import 'package:wuling_flutter_app/widgets/common/image_view.dart';
import 'package:wuling_flutter_app/widgets/use_car_page_widgets/duration_picker.dart';
import 'package:wuling_flutter_app/models/car/car_service_status_model.dart';
import 'package:wuling_flutter_app/models/car/car_control_item_model.dart';
import 'package:wuling_flutter_app/models/global_data.dart';
import 'package:wuling_flutter_app/utils/sp_util.dart';
import 'package:wuling_flutter_app/utils/str_util.dart';
import 'package:wuling_flutter_app/utils/manager/notification_manager.dart';
import 'package:wuling_flutter_app/constant/storage.dart';
import 'package:wuling_flutter_app/constant/constant.dart';

import '../../constant/car_constant.dart';
import '../../utils/click_counter.dart';
import '../../utils/manager/log_manager.dart';
import 'package:wuling_flutter_app/widgets/use_car_page_widgets/fan_temperature_control.dart';

enum TopSettingButtonType { text, icon }
enum AcButtonActionType{
  cancel,
  switchTurnStatus,
  quickAction,
  changeTemperature,
  turnOnAppointment,
}
class TopSettingButton extends StatelessWidget {
  final CarServiceStatusModel? statusModel;
  final VoidCallback? onPressed; // 添加点击事件处理回调
  final TopSettingButtonType buttonType;
  final String buttonTitle;
  final Color titleColor;
  final String buttonContent;
  final Color contentColor;
  final double contentFontSize;
  final Color? imageTinColor;
  const TopSettingButton({
    Key? key,
    this.statusModel,
    this.buttonContent = '--',
    required this.buttonTitle,
    this.titleColor = const Color(0xff383A40),
    this.buttonType = TopSettingButtonType.text,
    this.contentColor = const Color(0xFF383A40),
    this.contentFontSize = 24,
    this.onPressed, // 允许传入点击事件处理器
    this.imageTinColor
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        padding: EdgeInsets.zero, // 去除默认的padding
        shadowColor: Colors.transparent, // 阴影透明
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.zero, // 去除圆角
        ),
      ),
      onPressed: onPressed, // 绑定传入的事件处理器
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: Colors.transparent, // 边框颜色
            width: 3, // 边框宽度
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              decoration: BoxDecoration(
                  border: Border.all(
                    color: Colors.transparent, // 边框颜色
                    width: 3, // 边框宽度
                  ),
                  color: Colors.transparent),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  const SizedBox(
                    height: 5,
                  ),
                  buttonType == TopSettingButtonType.icon
                      ? Container(
                    height: 30,
                    color: Colors.transparent,
                    child: Center(
                      child: Container(
                        height: 21,
                        width: 21,
                        color: Colors.transparent,
                        child: statusModel != null
                            ? ImageView(
                          statusModel!.serviceStatusImage,
                          fit: BoxFit.cover,
                          imageTinColor: imageTinColor,
                        )
                            : const Placeholder(),
                      ),
                    ),
                  )
                      : Container(
                      height: 30,
                      color: Colors.transparent,
                      child: Center(
                        child: RichText(
                          text: TextSpan(
                            text: buttonContent,
                            style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: contentFontSize,
                                height: 1,
                                color: contentColor,
                                backgroundColor: Colors.transparent),
                          ),
                        ),
                      )),
                  // const SizedBox(height: 5),
                  Container(
                      color: Colors.transparent,
                      child: Text(
                        buttonTitle,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: titleColor,
                        ),
                      )
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}

class SettingButton extends StatelessWidget {
  final CarServiceStatusModel statusModel;
  final VoidCallback? onPressed; // 添加点击事件处理回调
  const SettingButton({
    Key? key,
    required this.statusModel,
    this.onPressed, // 允许传入点击事件处理器
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        padding: EdgeInsets.zero, // 去除默认的padding
        shadowColor: Colors.transparent, // 阴影透明
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.zero, // 去除圆角
        ),
      ),
      onPressed: onPressed, // 绑定传入的事件处理器
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: Colors.transparent, // 边框颜色
            width: 3, // 边框宽度
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: Colors.transparent, // 边框颜色
                  width: 3, // 边框宽度
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Container(
                    height: 70,
                    width: 70,
                    decoration:
                    BoxDecoration(borderRadius: BorderRadius.circular(20)),
                    child: ImageView(
                      statusModel.serviceStatusImage,
                      fit: BoxFit.cover,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    statusModel.serviceStatusName,
                    style: const TextStyle(
                        fontSize: 13,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF373C51)),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}

class AcSettingDialog extends StatefulWidget {
  final CarControlItemModel carControlItemModel;
  final CarStatusModel? carStatusModel;
  final bool isCarOwner;
  final bool keyStatus;
  final String? interiorTemperature;
  final int serviceStatusAction;
  final VoidCallback? onDismiss;
  final void Function({required CarControlItemModel? controlItemModel, required CarServiceStatusModel? statusModel, required AcButtonActionType actionType,required int serviceStatusAction, required String? accParameter})? onControlButtonClicked;
  final void Function({required Map<String, dynamic> paramJsonMap,required String serviceCode})? onSeatBtnClicked;
  const AcSettingDialog({super.key,
    required this.carControlItemModel,
    required this.carStatusModel,
    required this.isCarOwner,
    required this.keyStatus,
    required this.interiorTemperature,
    required this.serviceStatusAction,
    this.onControlButtonClicked,
    this.onSeatBtnClicked,
    this.onDismiss
  });

  @override
  _AcSettingDialogState createState() => _AcSettingDialogState();
}

class _AcSettingDialogState extends State<AcSettingDialog> {
  double _acSettedTemperature = 26; // 初始化自动空调滑动条的值
  double _electricAcLevel = 3; // 初始化电动空调滑动条的值
  late double? _acSettedTime; // 设定的开启时间
  late CarControlItemModel _carControlItemModel;
  late CarServiceStatusModel? _acTurnOnTimeStatusModel;
  late CarServiceStatusModel? _acBookingStatusModel;
  late CarServiceStatusModel? _warmmingStatusModel;
  late CarServiceStatusModel? _coolingStatusModel;
  late CarServiceStatusModel? _tempSettingStatusModel;
  late CarServiceStatusModel? _turnOnStatusModel;
  late CarServiceStatusModel? _turnOffStatusModel;

  late CarServiceStatusModel? _steeringWheelHeatingStatusModel;//方向盘加热
  late CarServiceStatusModel? _driverSeatVentilateStatusModel;//主驾座椅通风
  late CarServiceStatusModel? _driverSeatHeatingStatusModel;//主驾座椅加热
  late CarServiceStatusModel? _copilotSeatVentilateStatusModel;//副驾座椅通风
  late CarServiceStatusModel? _copilotSeatHeatingStatusModel;//副驾座椅加热
  late CarServiceStatusModel? _leftRearSeatVentilateStatusModel;//左后座椅通风
  late CarServiceStatusModel? _leftRearSeatHeatingStatusModel;//左后座椅加热
  late CarServiceStatusModel? _rightRearSeatVentilateStatusModel;//右后座椅通风
  late CarServiceStatusModel? _rightRearSeatHeatingStatusModel;//右后座椅加热
  late CarServiceStatusModel? _carImgStatusModel;//车模图

  late String? _steeringWheelHeatingStatus;
  late String? _seat1WindStatus;
  late String? _seat2WindStatus;
  late String? _seat3WindStatus;
  late String? _seat4WindStatus;
  late String? _seat1HotStatus;
  late String? _seat2HotStatus;
  late String? _seat3HotStatus;
  late String? _seat4HotStatus;

  int seatWorkLevel = 0 ;
  bool _isTurnOn = false;
  bool _isPlusType = false;
  bool _showInteriorTemp = false;
  String interiorTempStr = '--℃';
  String fanTemperatureControlLeftIcon = 'assets/images/use_car_page/air_flow_warm_flow0.png';
  String fanTemperatureControlRightIcon = 'assets/images/use_car_page/air_flow_warm_warm0.png';
  String _vin = '';
  @override
  void initState() {
    setupNotification();
    if(widget.serviceStatusAction == 2 || widget.serviceStatusAction == 4) {
      // 初始化温度
      double savedTemp = SpUtil().getDouble(SP_CURRENT_SETTING_TEMPERATURE_KEY);
      if(savedTemp != 0){
        _acSettedTemperature = savedTemp;
      }else{
        _acSettedTemperature = 26;
        SpUtil().setDouble(SP_CURRENT_SETTING_TEMPERATURE_KEY, _acSettedTemperature);//若没有手动设置过温度，则默认26度
      }
      GlobalData().preTemperature = _acSettedTemperature;
    }else {
      // 初始化档位
      double savedAcLevel = SpUtil().getDouble(SP_CURRENT_SETTING_ELECTRIC_AC_LEVEL_KEY);
      if(savedAcLevel != 0){
        _electricAcLevel = savedAcLevel;
      }else{
        _electricAcLevel = 3;
        SpUtil().setDouble(SP_CURRENT_SETTING_ELECTRIC_AC_LEVEL_KEY, _electricAcLevel);//若没有手动设置过空调档位，则默认3档
      }
      GlobalData().preElectricAcLevel = _electricAcLevel;
    }

    _carControlItemModel = widget.carControlItemModel;
    CarInfoModel? carInfoModel = GlobalData().carInfoModel;
    if(carInfoModel != null){
      String key =
          '${SP_CURRENT_AC_SETTING_TIME_KEY}_${carInfoModel.vin}';
      double savedTime = SpUtil().getDouble(key);
      _acSettedTime = savedTime;
      _vin = carInfoModel.vin!;
    }
    _acTurnOnTimeStatusModel =
        _carControlItemModel.getStatusModelWithCarStatusValue('8');
    if(_acSettedTime == 0.0) {
      if (_acTurnOnTimeStatusModel != null) {
        for (CarServiceParamModel paramModel in _acTurnOnTimeStatusModel!
            .serviceStatusParamList ?? []) {
          if (paramModel.paramName == 'duration') {
            setAcSettedTime(double.tryParse(paramModel.paramValue));
          }
        }
      }else{
        _acSettedTime = 5.0;
      }
    }
    _acBookingStatusModel =
        _carControlItemModel.getStatusModelWithCarStatusValue('9');
    _turnOnStatusModel =
        _carControlItemModel.getStatusModelWithCarStatusValue('7');
    _turnOffStatusModel =
        _carControlItemModel.getStatusModelWithCarStatusValue('6');
    _tempSettingStatusModel =
        _carControlItemModel.getStatusModelWithCarStatusValue('5');
    _coolingStatusModel =
        _carControlItemModel.getStatusModelWithCarStatusValue('4');
    _warmmingStatusModel =
        _carControlItemModel.getStatusModelWithCarStatusValue('3');
    _isTurnOn = _carControlItemModel.status != '0' && _carControlItemModel.status != '19'&& _carControlItemModel.status != '20' && _carControlItemModel.status != '21';
    _isPlusType = checkPlusType();
    _steeringWheelHeatingStatusModel =
        _carControlItemModel.getStatusModelWithCarStatusValue('999999');
    _driverSeatHeatingStatusModel =
        _carControlItemModel.getStatusModelWithCarStatusValue('10');
    _copilotSeatHeatingStatusModel =
        _carControlItemModel.getStatusModelWithCarStatusValue('11');
    _driverSeatVentilateStatusModel =
        _carControlItemModel.getStatusModelWithCarStatusValue('12');
    _copilotSeatVentilateStatusModel =
        _carControlItemModel.getStatusModelWithCarStatusValue('13');
    _leftRearSeatHeatingStatusModel =
        _carControlItemModel.getStatusModelWithCarStatusValue('14');
    _rightRearSeatHeatingStatusModel =
        _carControlItemModel.getStatusModelWithCarStatusValue('15');
    _leftRearSeatVentilateStatusModel =
        _carControlItemModel.getStatusModelWithCarStatusValue('16');
    _rightRearSeatVentilateStatusModel =
        _carControlItemModel.getStatusModelWithCarStatusValue('17');
    _carImgStatusModel =
    _carControlItemModel.getStatusModelWithCarStatusValue('18');
    _seat1WindStatus = widget.carStatusModel?.seat1WindStatus;
    _seat2WindStatus = widget.carStatusModel?.seat2WindStatus;
    _seat3WindStatus = widget.carStatusModel?.seat3WindStatus;
    _seat4WindStatus = widget.carStatusModel?.seat4WindStatus;
    _seat1HotStatus = widget.carStatusModel?.seat1HotStatus;
    _seat2HotStatus = widget.carStatusModel?.seat2HotStatus;
    _seat3HotStatus = widget.carStatusModel?.seat3HotStatus;
    _seat4HotStatus = widget.carStatusModel?.seat4HotStatus;
    int _serviceStatusAction = widget.serviceStatusAction;
    setInteriorTemp();



    super.initState();
  }



  void setupNotification() {
    NotificationManager().subscribe(
        Constant.NOTIFICATION_AC_STATUS_CHANGED, _receivedAcStatusChangedNotification);
    NotificationManager().subscribe(
        Constant.NOTIFICATION_SLIDER_CHANGE_SEND_INSTRUCT_RESULT, _receivedSliderChangeSendInstructResultNotification);
    NotificationManager().subscribe(
        Constant.NOTIFICATION_SEAT_STATUS_CHANGED, _receivedSeatStatusChangedNotification);
  }

  void removeNotification() {
    NotificationManager().unsubscribe(
        Constant.NOTIFICATION_AC_STATUS_CHANGED, _receivedAcStatusChangedNotification);
    NotificationManager().unsubscribe(
        Constant.NOTIFICATION_SLIDER_CHANGE_SEND_INSTRUCT_RESULT, _receivedSliderChangeSendInstructResultNotification);
    NotificationManager().unsubscribe(
        Constant.NOTIFICATION_SEAT_STATUS_CHANGED, _receivedSeatStatusChangedNotification);
  }

  void _receivedAcStatusChangedNotification(CustomNotification notification) {
    Map<String, dynamic> userInfo = notification.userInfo ?? {};
    setState(() {
      if(userInfo.containsKey('acStatus')) {
        String acStatus =  notification.userInfo!['acStatus'];
        _isTurnOn = acStatus != '0';
      }
      if(userInfo.containsKey('interiorTemperature')) {
        String interiorTemperature =  notification.userInfo!['interiorTemperature'];
        interiorTempStr = '$interiorTemperature℃';
      }
      if(userInfo.containsKey('keyStatus')) {
        String keyStatus =  notification.userInfo!['keyStatus'];

      }
    });
  }

  void _receivedSliderChangeSendInstructResultNotification(CustomNotification notification) {
    if(widget.serviceStatusAction == 2 || widget.serviceStatusAction == 4) {//自动空调
      // 收到请求回复则重置当前请求温度为0
      GlobalData().currentTemperature = 0;
      Map<String, dynamic> userInfo = notification.userInfo ?? {};
      setState(() {
        if(userInfo.containsKey('success')) {
          bool success = userInfo['success'];
          if(success){
            // 请求成功则把当前的温度记录下来作为请求前温度
            GlobalData().preTemperature = _acSettedTemperature;
            SpUtil().setDouble(SP_CURRENT_SETTING_TEMPERATURE_KEY, _acSettedTemperature);
          }else{
            // 请求失败则把当前的温度回复为请求前温度

            GlobalData().preTemperature = SpUtil().getDouble(SP_CURRENT_SETTING_TEMPERATURE_KEY);
            setState(() {
              _acSettedTemperature = GlobalData().preTemperature;
            });
          }
        }
      });
    }else {//电动空调
      // 收到请求回复则重置当前请求空调档位为0
      GlobalData().currentElectricAcLevel = 0;
      Map<String, dynamic> userInfo = notification.userInfo ?? {};
      setState(() {
        if(userInfo.containsKey('success')) {
          bool success = userInfo['success'];
          if(success){

            // 请求成功则把当前的空调档位记录下来作为请求前空调档位
            GlobalData().preElectricAcLevel = _electricAcLevel;
            SpUtil().setDouble(SP_CURRENT_SETTING_ELECTRIC_AC_LEVEL_KEY, _electricAcLevel);
            if(widget.serviceStatusAction == 5) {
              Navigator.of(context).pop();
            }
          }else{
            // 请求失败则把当前的空调档位回恢复为请求前空调档位

            GlobalData().preElectricAcLevel = SpUtil().getDouble(SP_CURRENT_SETTING_ELECTRIC_AC_LEVEL_KEY);
            setState(() {
              _electricAcLevel = GlobalData().preElectricAcLevel;
            });
          }
        }
      });
    }
  }

  void _receivedSeatStatusChangedNotification(CustomNotification notification) {
    Map<String, dynamic> userInfo = notification.userInfo ?? {};

    setState(() {
      if(userInfo.containsKey(STATUS_MODEL)) {
        CarStatusModel statusModel = userInfo[STATUS_MODEL];

        _seat1HotStatus = statusModel.seat1HotStatus;
        _seat2HotStatus = statusModel.seat2HotStatus;
        _seat3HotStatus = statusModel.seat3HotStatus;
        _seat4HotStatus = statusModel.seat4HotStatus;
        _seat1WindStatus = statusModel.seat1WindStatus;
        _seat2WindStatus = statusModel.seat2WindStatus;
        _seat3WindStatus = statusModel.seat3WindStatus;
        _seat4WindStatus = statusModel.seat4WindStatus;
      }
    });
  }

  /// 检查是否为PLUS车型
  bool checkPlusType() {
    return _carControlItemModel.serviceModel.serviceStatusList?.any(
          (model) => model.serviceStatusValue == '1000',
    ) ?? false;
  }

  /// 检查服务中是否含有预约空调功能
  bool checkContainAcAppointmentService(){
    return _carControlItemModel.serviceModel.serviceStatusList?.any(
          (model) => model.serviceStatusValue == '9',
    ) ?? false;
  }

  bool checkContainTimeSetService(){
    return _carControlItemModel.serviceModel.serviceStatusList?.any(
          (model) => model.serviceStatusValue == '8',
    ) ?? false;
  }

  /// 返回根据当前设定的温度值和开启时间修改参数后的状态模型
  CarServiceStatusModel? getResponseStatusModel(CarServiceStatusModel? serviceStatusModel){
    if(widget.serviceStatusAction == 2 || widget.serviceStatusAction == 4) {
      CarServiceStatusModel? afterUpdateTemp = updateParamModelWithTemperature(serviceStatusModel);
      if(checkContainTimeSetService()){
        return updateParamModelWithTimeDuration(afterUpdateTemp);
      }
      return afterUpdateTemp;
    }else {
      CarServiceStatusModel? afterUpdateTemp = updateElectricAcParamModelWithTemperature(serviceStatusModel);
      Map<String, dynamic> statusMap = afterUpdateTemp!.toJson();
      statusMap['backStatusValue'] = _electricAcLevel > 3 ? '2':'1';
      CarServiceStatusModel? afterUpdateTemp2 = CarServiceStatusModel.fromJson(statusMap);
      if(checkContainTimeSetService()){
        return updateParamModelWithTimeDuration(afterUpdateTemp2);
      }
      return afterUpdateTemp2;
    }

  }
  /// 返回根据当前开启时间修改参数后的状态模型
  CarServiceStatusModel? updateParamModelWithTimeDuration(CarServiceStatusModel? serviceStatusModel){
    return updateParamModel(serviceStatusModel: serviceStatusModel, paramName: 'duration', paramValue: _acSettedTime?.toStringAsFixed(0));
  }
  /// 返回根据当前设定温度修改参数后的状态模型（自动空调）
  CarServiceStatusModel? updateParamModelWithTemperature(CarServiceStatusModel? serviceStatusModel){
    return updateParamModel(serviceStatusModel: serviceStatusModel, paramName: 'temperature', paramValue: _acSettedTemperature.toStringAsFixed(0));
  }
  /// 返回根据当前设定温度修改参数后的状态模型（电动空调）
  CarServiceStatusModel? updateElectricAcParamModelWithTemperature(CarServiceStatusModel? serviceStatusModel){
    return updateParamModel(serviceStatusModel: serviceStatusModel, paramName: 'temperature', paramValue: _electricAcLevel.toStringAsFixed(0));
  }
  /// 根据参数名称和参数值修改状态模型中的请求参数，返回一个其他值不变的新生成模型
  /// [serviceStatusModel] 需要修改的状态模型
  /// [paramName] 参数名称
  /// [paramValue] 参数值
  CarServiceStatusModel? updateParamModel({required CarServiceStatusModel? serviceStatusModel, required String paramName, required String? paramValue}){
    if(serviceStatusModel == null)
      return null;
    Map<String, dynamic> serviceStatusMap = serviceStatusModel.toJson();
    if(serviceStatusMap.containsKey('serviceStatusParamList')){
      for(Map<String, dynamic> param in serviceStatusMap['serviceStatusParamList']){
        if(param.containsKey('paramName')){
          if(param['paramName'] == paramName && paramValue != null) {
            param['paramValue'] = paramValue;
            break;
          }
        }
      }
    }
    CarServiceStatusModel newServiceStatusModel = CarServiceStatusModel.fromJson(serviceStatusMap);
    return newServiceStatusModel;
  }
  /// 将状态模型中的温度参数设置为当前请求温度
  /// [statusModel] 输入的状态模型
  void setCurrentTemperatureWithStatusModel({CarServiceStatusModel? statusModel}){
    if(statusModel != null){
      List<CarServiceParamModel> serviceStatusParamList = statusModel.serviceStatusParamList ?? [];
      if(serviceStatusParamList.isNotEmpty){
        for(CarServiceParamModel paramModel in serviceStatusParamList){
          if(paramModel.paramName == 'temperature'){
            double? paramValue = StrUtil.toDouble(paramModel.paramValue);
            if(widget.serviceStatusAction == 2 || widget.serviceStatusAction == 4) {

              GlobalData().currentTemperature = paramValue ?? 0;
              setState(() {
                _acSettedTemperature = paramValue ?? 26;
              });
            }else {

              GlobalData().currentElectricAcLevel = paramValue ?? 0;
              setState(() {
                _electricAcLevel = paramValue ?? 3;
              });
            }
            break;
          }
        }
      }
    }
  }

  void doSliderValueChangedAction(double value)  {
    // 首先更新本地状态
    _acSettedTemperature = value;

    if(!_isTurnOn){
      SpUtil().setDouble(SP_CURRENT_SETTING_TEMPERATURE_KEY, value);
    }else{
      GlobalData().currentTemperature = value;
      widget.onControlButtonClicked?.call(
          controlItemModel:_carControlItemModel,
          statusModel: updateParamModelWithTemperature(_tempSettingStatusModel),
          actionType:AcButtonActionType.changeTemperature,
          serviceStatusAction:widget.serviceStatusAction,
          accParameter:_tempSettingStatusModel?.serviceStatusValue
      );
    }


  }

  void doElectricAcSliderValueChangedAction(double value)  {
    // 首先更新本地状态
    _electricAcLevel = value;

    if(!_isTurnOn){
      SpUtil().setDouble(SP_CURRENT_SETTING_ELECTRIC_AC_LEVEL_KEY, value);
    }else{
      GlobalData().currentElectricAcLevel = value;
      widget.onControlButtonClicked?.call(
          controlItemModel:_carControlItemModel,
          statusModel: updateElectricAcParamModelWithTemperature(_tempSettingStatusModel),
          actionType:AcButtonActionType.changeTemperature,
          serviceStatusAction:widget.serviceStatusAction,
          accParameter:_tempSettingStatusModel?.serviceStatusValue
      );
    }


  }

  void setInteriorTemp(){
    if(_isPlusType){
      String? temp = widget.interiorTemperature;
      String title = (temp == null || temp.isEmpty || temp == 'null') ? '--' : temp;
      _showInteriorTemp = widget.keyStatus || _isTurnOn;
      interiorTempStr = _showInteriorTemp ? (title == '-40' ? '加载中' : '$title℃') : '空调未开';
    }else{
      interiorTempStr = '${widget.interiorTemperature ?? '--'}℃';
    }
  }

  void setAcSettedTime(double? newTime){
    CarInfoModel? carInfoModel = GlobalData().carInfoModel;
    if(carInfoModel != null) {
      _acSettedTime = newTime;
      String key =
          '${SP_CURRENT_AC_SETTING_TIME_KEY}_${carInfoModel.vin}';
      SpUtil().setDouble(key, _acSettedTime ?? 5.0);
    }
  }

  void _showDurationPicker(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return DurationPicker(
          onSelected: (int selectedMinute) {
            setState(() {
              setAcSettedTime(selectedMinute.toDouble());
            });
          },
          currentSelectTime: _acSettedTime?.toInt(),
        );
      },
    );
  }

  //获取座椅通风的图标：level：座椅通风的档位
  String _getVentilationBtnIcon(String? level) {
    if(level == null) {
      return 'assets/images/use_car_page/air_flow_warm_flow0.png';
    }else {
      if(level == '1') {
        return 'assets/images/use_car_page/air_flow_warm_flow1.png';
      }else if(level == '2') {
        return 'assets/images/use_car_page/air_flow_warm_flow2.png';
      }else if(level == '3') {
        return 'assets/images/use_car_page/air_flow_warm_flow3.png';
      }else {
        return 'assets/images/use_car_page/air_flow_warm_flow0.png';
      }
    }
  }

  //获取座椅加热的图标：level：座椅加热的档位
  String _getHeatingBtnIcon(String? level) {
    if(level == null) {
      return 'assets/images/use_car_page/air_flow_warm_warm0.png';
    }else {
      if(level == '1') {
        return 'assets/images/use_car_page/air_flow_warm_warm1.png';
      }else if(level == '2') {
        return 'assets/images/use_car_page/air_flow_warm_warm2.png';
      }else if(level == '3') {
        return 'assets/images/use_car_page/air_flow_warm_warm3.png';
      }else {
        return 'assets/images/use_car_page/air_flow_warm_warm0.png';
      }
    }
  }

  void setSeatHeating(String seatStatusKey,String? statusStr) {
    LogManager().log('$seatStatusKey 获取的状态是: $statusStr');
    int status = 0;
    try {
      status = int.parse(statusStr!);
      const validStatuses = {1, 2, 3, 7};
      if (!validStatuses.contains(status)) {
        status = SpUtil().getInt(_vin+seatStatusKey);
        LogManager().log('$seatStatusKey 状态异常了，取本地状态，当前本地状态是: $status');
        switch (seatStatusKey) {
          case SEAT1_HOT_STATUS:
            SEAT1_HOT_LEVEL = status;
            break;
          case SEAT2_HOT_STATUS:
            SEAT2_HOT_LEVEL = status;
            break;
          case SEAT3_HOT_STATUS:
            SEAT3_HOT_LEVEL = status;
            break;
          case SEAT4_HOT_STATUS:
            SEAT4_HOT_LEVEL = status;
            break;
        };
      }else {
        final hotLevel = status == 7 ? 0 : status;

        switch (seatStatusKey) {
          case SEAT1_HOT_STATUS:
            SEAT1_HOT_LEVEL = hotLevel;
            SpUtil().setInt(_vin+SEAT1_HOT_STATUS, hotLevel);
            break;
          case SEAT2_HOT_STATUS:
            SEAT2_HOT_LEVEL = hotLevel;
            SpUtil().setInt(_vin+SEAT2_HOT_STATUS, hotLevel);
            break;
          case SEAT3_HOT_STATUS:
            SEAT3_HOT_LEVEL = hotLevel;
            SpUtil().setInt(_vin+SEAT3_HOT_STATUS, hotLevel);
            break;
          case SEAT4_HOT_STATUS:
            SEAT4_HOT_LEVEL = hotLevel;
            SpUtil().setInt(_vin+SEAT4_HOT_STATUS, hotLevel);
            break;
        };
      }
    } on FormatException catch (e) {
      status = SpUtil().getInt(_vin+seatStatusKey);
      switch (seatStatusKey) {
        case SEAT1_HOT_STATUS:
          SEAT1_HOT_LEVEL = status;
          break;
        case SEAT2_HOT_STATUS:
          SEAT2_HOT_LEVEL = status;
          break;
        case SEAT3_HOT_STATUS:
          SEAT3_HOT_LEVEL = status;
          break;
        case SEAT4_HOT_STATUS:
          SEAT4_HOT_LEVEL = status;
          break;
      };
      LogManager().log('格式错误: $seatStatusKey 状态异常了，取本地状态，当前本地状态是: $status');
    } catch (e, s) {
      status = SpUtil().getInt(_vin+seatStatusKey);
      switch (seatStatusKey) {
        case SEAT1_HOT_STATUS:
          SEAT1_HOT_LEVEL = status;
          break;
        case SEAT2_HOT_STATUS:
          SEAT2_HOT_LEVEL = status;
          break;
        case SEAT3_HOT_STATUS:
          SEAT3_HOT_LEVEL = status;
          break;
        case SEAT4_HOT_STATUS:
          SEAT4_HOT_LEVEL = status;
          break;
      };

    }

  }

  void setSeatVentilation(String seatStatusKey,String? statusStr) {
    LogManager().log('$seatStatusKey 获取的状态是: $statusStr');
    int status = 0;
    try {
      status = int.parse(statusStr!);
      const validStatuses = {1, 2, 3, 7};
      if (!validStatuses.contains(status)) {
        status = SpUtil().getInt(_vin+seatStatusKey);
        LogManager().log('$seatStatusKey 状态异常了，取本地状态，当前本地状态是: $status');
        switch (seatStatusKey) {
          case SEAT1_WIND_STATUS:
            SEAT1_WIND_LEVEL = status;
            break;
          case SEAT2_WIND_STATUS:
            SEAT2_WIND_LEVEL = status;
            break;
          case SEAT3_WIND_STATUS:
            SEAT3_WIND_LEVEL = status;
            break;
          case SEAT4_WIND_STATUS:
            SEAT4_WIND_LEVEL = status;
            break;
        };
      }else {
        final hotLevel = status == 7 ? 0 : status;

        switch (seatStatusKey) {
          case SEAT1_WIND_STATUS:
            SEAT1_WIND_LEVEL = hotLevel;
            SpUtil().setInt(_vin+SEAT1_WIND_STATUS, hotLevel);
            break;
          case SEAT2_WIND_STATUS:
            SEAT2_WIND_LEVEL = hotLevel;
            SpUtil().setInt(_vin+SEAT2_WIND_STATUS, hotLevel);
            break;
          case SEAT3_WIND_STATUS:
            SEAT3_WIND_LEVEL = hotLevel;
            SpUtil().setInt(_vin+SEAT3_WIND_STATUS, hotLevel);
            break;
          case SEAT4_WIND_STATUS:
            SEAT4_WIND_LEVEL = hotLevel;
            SpUtil().setInt(_vin+SEAT4_WIND_STATUS, hotLevel);
            break;
        };
      }
    } on FormatException catch (e) {
      status = SpUtil().getInt(_vin+seatStatusKey);
      switch (seatStatusKey) {
        case SEAT1_WIND_STATUS:
          SEAT1_WIND_LEVEL = status;
          break;
        case SEAT2_WIND_STATUS:
          SEAT2_WIND_LEVEL = status;
          break;
        case SEAT3_WIND_STATUS:
          SEAT3_WIND_LEVEL = status;
          break;
        case SEAT4_WIND_STATUS:
          SEAT4_WIND_LEVEL = status;
          break;
      };
      LogManager().log('格式错误: $seatStatusKey 状态异常了，取本地状态，当前本地状态是: $status');
    } catch (e, s) {
      status = SpUtil().getInt(_vin+seatStatusKey);
      switch (seatStatusKey) {
        case SEAT1_WIND_STATUS:
          SEAT1_WIND_LEVEL = status;
          break;
        case SEAT2_WIND_STATUS:
          SEAT2_WIND_LEVEL = status;
          break;
        case SEAT3_WIND_STATUS:
          SEAT3_WIND_LEVEL = status;
          break;
        case SEAT4_WIND_STATUS:
          SEAT4_WIND_LEVEL = status;
          break;
      };

    }

  }

  List<Widget> _getTopSettingWidgetList() {
    List<Widget> result = [];
    double topSettingPaddingHeight = 45;
    double topSettingPaddingWidth = MediaQuery.of(context).size.width/4;
    if(widget.serviceStatusAction == 2 || widget.serviceStatusAction == 4) {
      result.add(
        Expanded(
            child: TopSettingButton(
              buttonTitle: '车内温度',
              buttonContent: interiorTempStr,
              contentColor: (interiorTempStr == '加载中' ||interiorTempStr == '空调未开') ? Color(0xff9b9da9):Color(0xff383A40),
              contentFontSize: (_showInteriorTemp && interiorTempStr != '加载中') ? 24 : 18,
              onPressed: () {},
            )),
      );
    }
    if(widget.serviceStatusAction == 2 || widget.serviceStatusAction == 4) {
      result.add(
        Container(
          width: 1,
          height: topSettingPaddingHeight,
          color: const Color(0xFFEDEDED),
        ),
      );
    }

    if(widget.serviceStatusAction == 2 || widget.serviceStatusAction == 4) {
      result.add(
        Expanded(
            child: TopSettingButton(
              buttonTitle: '常用温度设置',
              buttonContent: '${_acSettedTemperature.toStringAsFixed(0)}℃',
              contentColor: const Color(0xFF5493DB),
              onPressed: () {},
            )),
      );
    }

    if(widget.serviceStatusAction == 3 || widget.serviceStatusAction == 5) {
      //空调3或者空调5添加占位，使空调档位和时间居中显示
      result.add(
        GestureDetector(
            onTap: null,
            child: Container(
              width: topSettingPaddingWidth,
              height: topSettingPaddingHeight,
              color: Colors.transparent,
            )
        ),
      );
    }

    if(widget.serviceStatusAction == 3 || widget.serviceStatusAction == 5) {
      result.add(
        Expanded(
            child: TopSettingButton(
              buttonTitle: '空调档位',
              buttonContent: '${_electricAcLevel.toStringAsFixed(0)}',
              contentColor: const Color(0xFF5493DB),
              onPressed: () {},
            )),
      );
    }
    if(_acTurnOnTimeStatusModel != null) {
      result.add(
        Container(
          width: 1,
          height: topSettingPaddingHeight,
          color: const Color(0xFFEDEDED),
        ),
      );
    }

    if(_acTurnOnTimeStatusModel != null) {
      result.add(
        Expanded(
            child: TopSettingButton(
              buttonTitle: _acSettedTime != null ? '${_acSettedTime!
                  .toStringAsFixed(0)}分钟' : '开启时间',
              titleColor: _isTurnOn ? Color(0xffE5E5E5) : Color(0xff383A40),
              statusModel: _acTurnOnTimeStatusModel,
              buttonType: TopSettingButtonType.icon,
              onPressed: _isTurnOn ? () {

              } : () {
                _showDurationPicker(context);
              },
              imageTinColor:  _isTurnOn ? const Color(0xffE5E5E5) : null,
            )),
      );
    }
    if(widget.serviceStatusAction == 3 || widget.serviceStatusAction == 5) {
      result.add(
        GestureDetector(
            onTap: null,
            child: Container(
              width: topSettingPaddingWidth,
              height: topSettingPaddingHeight,
              color: Colors.transparent,
            )
        ),
      );
    }
    // if(widget.isCarOwner) {
    // result.add(
    //   Container(
    //     width: 1,
    //     height: topSettingPaddingHeight,
    //     color: const Color(0xFFEDEDED),
    //   ),
    // );
    // result.add(
    //   Expanded(
    //       child: TopSettingButton(
    //         buttonTitle: '预约空调',
    //         statusModel: _acBookingStatusModel,
    //         buttonType: TopSettingButtonType.icon,
    //         onPressed: () {
    //           Navigator.of(context).pop();
    //           widget.onControlButtonClicked?.call(
    //               controlItemModel:_carControlItemModel,
    //               statusModel: _acBookingStatusModel,
    //               actionType:AcButtonActionType.turnOnAppointment
    //           );
    //         },
    //       )),
    // );
    // }
    return result;
  }

  List<Widget> _getSettingWidgetList() {
    List<Widget> result = [];
    if (_coolingStatusModel != null) {
      result.add(
        SettingButton(
            statusModel: _coolingStatusModel!,
            onPressed: () {
              setCurrentTemperatureWithStatusModel(statusModel: _coolingStatusModel);
              widget.onControlButtonClicked?.call(
                  controlItemModel:_carControlItemModel,
                  statusModel: updateParamModelWithTimeDuration(_coolingStatusModel),
                  actionType:AcButtonActionType.quickAction,
                  serviceStatusAction:widget.serviceStatusAction,
                  accParameter:_coolingStatusModel?.serviceStatusValue
              );
            }),
      );
    }
    if (_warmmingStatusModel != null) {
      result.add(
        SettingButton(
          statusModel: _warmmingStatusModel!,
          onPressed: () {
            setCurrentTemperatureWithStatusModel(statusModel: _warmmingStatusModel);
            widget.onControlButtonClicked?.call(
                controlItemModel:_carControlItemModel,
                statusModel: updateParamModelWithTimeDuration(_warmmingStatusModel),
                actionType:AcButtonActionType.quickAction,
                serviceStatusAction:widget.serviceStatusAction,
                accParameter:_warmmingStatusModel?.serviceStatusValue
            );
          },
        ),
      );
    }
    if (_turnOnStatusModel != null && _turnOffStatusModel != null) {
      result.add(
        SettingButton(
            statusModel: _isTurnOn ? _turnOnStatusModel! : _turnOffStatusModel!,
            onPressed: () {
              widget.onControlButtonClicked?.call(
                  controlItemModel:_carControlItemModel,
                  statusModel: getResponseStatusModel(_carControlItemModel.currentStatus()),
                  actionType:AcButtonActionType.switchTurnStatus,
                  serviceStatusAction:widget.serviceStatusAction,
                  accParameter:_turnOffStatusModel?.serviceStatusValue
              );
            }
        ),
      );
    }
    return result;
  }

  @override
  Widget build(BuildContext context) {

    setSeatHeating(SEAT1_HOT_STATUS, _seat1HotStatus);
    setSeatHeating(SEAT2_HOT_STATUS, _seat2HotStatus);
    setSeatHeating(SEAT3_HOT_STATUS, _seat3HotStatus);
    setSeatHeating(SEAT4_HOT_STATUS, _seat4HotStatus);
    setSeatVentilation(SEAT1_WIND_STATUS,_seat1WindStatus);
    setSeatVentilation(SEAT2_WIND_STATUS,_seat2WindStatus);
    setSeatVentilation(SEAT3_WIND_STATUS,_seat3WindStatus);
    setSeatVentilation(SEAT4_WIND_STATUS,_seat4WindStatus);

    bool isShowSeat = widget.serviceStatusAction == 4 ;//是否展示座椅
    double bottomPadding = MediaQueryData.fromWindow(window).padding.bottom;
    double screenWidth = MediaQueryData.fromWindow(window).size.width;
    return Container(
      height: isShowSeat ? MediaQuery.of(context).size.height : 425,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(isShowSeat ? 0 :16.0),
          topLeft: Radius.circular(isShowSeat ? 0 :16.0),
        ),
        color: Colors.white,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          if(isShowSeat)
          // 座椅控制区域
            Container(
              width: MediaQuery.of(context).size.width,
              height: 368,
              child: Stack(
                children: [
                  ImageView(
                    _carImgStatusModel?.serviceStatusImage ?? '',
                    width: MediaQuery.of(context).size.width,
                    height: 368,
                    fit: BoxFit.cover,
                  ),
                  /*CachedNetworkImage(
                  imageUrl: carImageUrl == null ? '':carImageUrl,
                  width: MediaQuery.of(context).size.width,
                  height: 368,
                  fit: BoxFit.cover,
                ),*/

                  Container(
                    margin:EdgeInsets.fromLTRB(0, 342, 0, 0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          '提示：开启空调车辆即将上电',
                          style: TextStyle(
                            fontSize: 13,
                            color: Color(0xff414141),
                          ),
                        ),
                      ],
                    ),
                  ),

                  if(_steeringWheelHeatingStatusModel != null)
                  //方向盘加热
                    Container(
                      margin:EdgeInsets.fromLTRB(89, 57, 0, 0),
                      child: Row(
                        children: [
                          FanTemperatureControl(
                            isShowVentilationBtn: false,
                            isShowHeatingBtn: _steeringWheelHeatingStatusModel != null,
                            ventilationBtnIcon:'',
                            heatingBtnIcon:_getHeatingBtnIcon(''),
                            ventilationBtnWidgetId: '',
                            heatingBtnWidgetId: STEERING_WHEEL_HEATING_CLICK_COUNT,
                            onVentilationBtnClick: (clickCount) => {

                            },
                            onHeatingBtnClick: (clickCount) => {

                            },
                          ),
                        ],
                      ),
                    ),
                  if((_driverSeatVentilateStatusModel != null || _driverSeatHeatingStatusModel != null))
                  //主驾
                    Container(
                      margin:EdgeInsets.fromLTRB((_driverSeatVentilateStatusModel != null &&_driverSeatHeatingStatusModel != null)?65:91, 138, 0, 0),
                      child: Row(
                        children: [
                          FanTemperatureControl(
                            isShowVentilationBtn: _driverSeatVentilateStatusModel != null,
                            isShowHeatingBtn: _driverSeatHeatingStatusModel != null,
                            ventilationBtnIcon:_getVentilationBtnIcon(_seat1WindStatus),
                            heatingBtnIcon:_getHeatingBtnIcon(_seat1HotStatus),
                            ventilationBtnWidgetId: DRIVER_SEAT_VENTILATE_CLICK_COUNT,
                            heatingBtnWidgetId: DRIVER_SEAT_HEATING_CLICK_COUNT,
                            onVentilationBtnClick: (clickCount) {
                              setSeatBtnClicked(SEAT1_WIND_STATUS,1,SEAT1_WIND_LEVEL,clickCount);
                            },
                            onHeatingBtnClick: (clickCount) {
                              setSeatBtnClicked(SEAT1_HOT_STATUS,1,SEAT1_HOT_LEVEL,clickCount);
                            },
                          ),
                        ],
                      ),
                    ),

                  if((_copilotSeatVentilateStatusModel != null || _copilotSeatHeatingStatusModel != null ))
                  //副驾
                    Container(
                      margin:EdgeInsets.fromLTRB((_copilotSeatVentilateStatusModel != null &&_copilotSeatHeatingStatusModel != null )?213:239, 138, 0, 0),
                      child: Row(
                        children: [
                          FanTemperatureControl(
                            isShowVentilationBtn: _copilotSeatVentilateStatusModel != null,
                            isShowHeatingBtn: _copilotSeatHeatingStatusModel != null,
                            ventilationBtnIcon:_getVentilationBtnIcon(_seat2WindStatus),
                            heatingBtnIcon:_getHeatingBtnIcon(_seat2HotStatus),
                            ventilationBtnWidgetId: COPILOT_SEAT_VENTILATE_CLICK_COUNT,
                            heatingBtnWidgetId: COPILOT_SEAT_HEATING_CLICK_COUNT,
                            onVentilationBtnClick: (clickCount) {
                              setSeatBtnClicked(SEAT2_WIND_STATUS,2,SEAT2_WIND_LEVEL,clickCount);
                            },
                            onHeatingBtnClick: (clickCount) {
                              setSeatBtnClicked(SEAT2_HOT_STATUS,2,SEAT2_HOT_LEVEL,clickCount);
                            },
                          ),
                        ],
                      ),
                    ),
                  if((_leftRearSeatVentilateStatusModel != null || _leftRearSeatHeatingStatusModel != null))
                  //左后
                    Container(
                      margin:EdgeInsets.fromLTRB((_leftRearSeatVentilateStatusModel != null&&_leftRearSeatHeatingStatusModel != null)?65:91, 219, 0, 0),
                      child: Row(
                        children: [
                          FanTemperatureControl(
                            isShowVentilationBtn: _leftRearSeatVentilateStatusModel != null,
                            isShowHeatingBtn: _leftRearSeatHeatingStatusModel != null,
                            ventilationBtnIcon:_getVentilationBtnIcon(_seat3WindStatus),
                            heatingBtnIcon:_getHeatingBtnIcon(_seat3HotStatus),
                            ventilationBtnWidgetId: LEFT_REAR_SEAT_VENTILATE_CLICK_COUNT,
                            heatingBtnWidgetId: LEFT_REAR_SEAT_HEATING_CLICK_COUNT,
                            onVentilationBtnClick: (clickCount) {
                              setSeatBtnClicked(SEAT3_WIND_STATUS,3,SEAT3_WIND_LEVEL,clickCount);
                            },
                            onHeatingBtnClick: (clickCount) {
                              setSeatBtnClicked(SEAT3_HOT_STATUS,3,SEAT3_HOT_LEVEL,clickCount);
                            },
                          ),
                        ],
                      ),
                    ),
                  if((_rightRearSeatVentilateStatusModel != null || _rightRearSeatHeatingStatusModel != null))
                  //右后
                    Container(
                      margin:EdgeInsets.fromLTRB((_rightRearSeatVentilateStatusModel != null&&_rightRearSeatHeatingStatusModel != null)?213:239, 219, 0, 0),
                      child: Row(
                        children: [
                          FanTemperatureControl(
                            isShowVentilationBtn: _rightRearSeatVentilateStatusModel != null,
                            isShowHeatingBtn: _rightRearSeatHeatingStatusModel != null,
                            ventilationBtnIcon:_getVentilationBtnIcon(_seat4WindStatus),
                            heatingBtnIcon:_getHeatingBtnIcon(_seat4HotStatus),
                            ventilationBtnWidgetId: RIGHT_REAR_SEAT_VENTILATE_CLICK_COUNT,
                            heatingBtnWidgetId: RIGHT_REAR_SEAT_HEATING_CLICK_COUNT,
                            onVentilationBtnClick: (clickCount) {
                              setSeatBtnClicked(SEAT4_WIND_STATUS,4,SEAT4_WIND_LEVEL,clickCount);
                            },
                            onHeatingBtnClick: (clickCount) {
                              setSeatBtnClicked(SEAT4_HOT_STATUS,4,SEAT4_HOT_LEVEL,clickCount);
                            },
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          if(!isShowSeat)
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                const SizedBox(width: 40),
                const Text(
                  '提示：开启空调车辆即将上电',
                  style: TextStyle(
                    fontSize: 13.0,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),

          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: _getTopSettingWidgetList(),
          ),
          const SizedBox(height: 25),
          if(widget.serviceStatusAction == 2 || widget.serviceStatusAction == 4)
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '17℃',
                style: TextStyle(fontSize: 16),
              ),
              SizedBox(
                width: 10,
              ),
              CustomSlider(
                height: 25,
                width: 250,
                value: _acSettedTemperature,
                min: 17,
                max: 33,
                divisions: 16,
                label: '',
                onChanged: (double value) {
                  setState(() {

                    _acSettedTemperature = value;
                  });
                },
                onChangeEnd: (double value){

                  doSliderValueChangedAction(value);
                },
                backgroundImagePath:
                'assets/images/use_car_page/use_car_page_control_ac_slider_bg.png', // 替换为你自己的背景图片路径
              ),
              SizedBox(
                width: 10,
              ),
              Text(
                '33℃',
                style: TextStyle(fontSize: 16),
              ),
            ],
          ),
          if(widget.serviceStatusAction == 3 || widget.serviceStatusAction == 5)
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                '1档',
                style: TextStyle(fontSize: 16),
              ),
              SizedBox(
                width: 10,
              ),
              CustomSlider(
                height: 25,
                width: 250,
                value: _electricAcLevel,
                min: 1,
                max: 6,
                divisions: 5,
                label: '',
                onChanged: (double value) {
                  setState(() {

                    _electricAcLevel = value;
                  });
                },
                onChangeEnd: (double value){

                  doElectricAcSliderValueChangedAction(value);
                },
                backgroundImagePath:
                'assets/images/use_car_page/use_car_page_control_ac_slider_bg.png', // 替换为你自己的背景图片路径
              ),
              SizedBox(
                width: 10,
              ),
              Text(
                '6档',
                style: TextStyle(fontSize: 16),
              ),
            ],
          ),
          const SizedBox(
            height: 30,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                height: 1,
                width: screenWidth - 40,
                color: Color(0xFFEDEDED),
              )
            ],
          ),
          const SizedBox(
            height: 20,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: _getSettingWidgetList(),
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                height: 1,
                width: screenWidth - 40,
                color: Color(0xFFEDEDED),
              )
            ],
          ),
          Container(
            color: Colors.transparent,
            padding: EdgeInsets.only(left: 20, right: 20),
            child: ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                '取消',
                style: TextStyle(fontSize: 17),
              ),
              style: ButtonStyle(
                minimumSize:
                MaterialStateProperty.all<Size>(Size(double.infinity, 44)),
                elevation: MaterialStateProperty.resolveWith<double>(
                      (Set<MaterialState> states) {
                    // 为所有状态设置阴影为0
                    return 0;
                  },
                ),
              ),
            ),
          ),
          SizedBox(height: bottomPadding),
        ],
      ),
    );
  }

  @override
  void dispose() {
    removeNotification();
    super.dispose();
  }

  void setSeatBtnClicked(String serviceCode,int seatPosition,int currentWindLevel,int clickCount) {

    // 处理点击事件
    if (clickCount % 4 == 0){
      return;
    }
    //clickCount 是点击次数， currentWindLevel 是当前风量
    if (currentWindLevel - clickCount % 4 > 0){
      seatWorkLevel = currentWindLevel - clickCount % 4 ;
    }else if (currentWindLevel - clickCount % 4 == 0){
      seatWorkLevel = 7;
    }else {
      seatWorkLevel = 4 + (currentWindLevel - clickCount % 4);
    }
    Map<String, dynamic> paramJsonMap = {};
    paramJsonMap['vin'] = _vin;
    paramJsonMap['seatPosition'] = seatPosition;
    paramJsonMap['workLevel'] = seatWorkLevel;
    paramJsonMap['duration'] = AC_TIME_CACHE;

    widget.onSeatBtnClicked?.call(paramJsonMap: paramJsonMap,serviceCode:serviceCode);
  }
}

class NormalAcSettingDialog extends StatefulWidget {
  final CarControlItemModel carControlItemModel;
  //是否是新空调
  final bool isNewAir;
  final void Function({CarControlItemModel? controlItemModel, CarServiceStatusModel? statusModel})? onControlButtonClicked;
  final void Function({CarControlItemModel? controlItemModel, CarServiceStatusModel? statusModel})? onControlButtonClickedNew;
  const NormalAcSettingDialog({super.key, required this.carControlItemModel, this.onControlButtonClicked, this.onControlButtonClickedNew, required this.isNewAir});

  @override
  _NormalAcSettingDialog createState() => _NormalAcSettingDialog();
}

class _NormalAcSettingDialog extends State<NormalAcSettingDialog> {
  late CarServiceModel serviceModel;
  late List<CarServiceStatusModel> statusList;

  List<Widget> _getButtonList() {
    List<Widget> result = [];
    for (CarServiceStatusModel statusModel in statusList) {
      
      if(widget.isNewAir){
        //这个版本空调3和空调5暂时只处理 快速升温 和 快速降温
        if(statusModel.serviceStatusValue == '3' || statusModel.serviceStatusValue == '4'){
          if(statusModel.serviceStatusValue == '3'){
            //将 快速升温 手动改成 制暖
            statusModel.serviceStatusImage = 'https://cdn-df.00bang.cn/images/T1EIATByAT1RCvBVdK.png';
            statusModel.serviceStatusName = '制暖';
          }
          if(statusModel.serviceStatusValue == '4'){
            //将 快速降温 手动改成 制冷
            statusModel.serviceStatusImage = 'https://cdn-df.00bang.cn/images/T1LTbTByWT1RCvBVdK.png';
            statusModel.serviceStatusName = '制冷';
          }
          _addSettingButton(result, statusModel);
        }
      }else{
        _addSettingButton(result, statusModel);
      }
    }
    return result;
  }

  void _addSettingButton(List<Widget> result, CarServiceStatusModel statusModel) {
    result.add(
        SettingButton(
          statusModel: statusModel,
          onPressed: (){
            if(widget.isNewAir){
              widget.onControlButtonClickedNew?.call(
                controlItemModel:widget.carControlItemModel,
                statusModel: statusModel,
              );
            }else{
              widget.onControlButtonClicked?.call(
                controlItemModel:widget.carControlItemModel,
                statusModel: statusModel,
              );
            }
            Navigator.of(context).pop();
          },
        )
    );
  }

  @override
  void initState() {
    serviceModel = widget.carControlItemModel.serviceModel;
    CarServiceStatusModel? currentStatusModel =
    widget.carControlItemModel.currentStatus();
    String serviceStatusAction = currentStatusModel!.serviceStatusAction;
    statusList = CarControlItemModel.getStatusListByLevel(serviceModel: serviceModel, level: serviceStatusAction);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double bottomPadding = MediaQueryData.fromWindow(window).padding.bottom;
    double screenWidth = MediaQueryData.fromWindow(window).size.width;
    return Container(
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(16.0),
          topLeft: Radius.circular(16.0),
        ),
        color: Colors.white,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          const SizedBox(height: 25),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              const SizedBox(width: 40),
              const Text(
                '提示：开启空调车辆即将上电',
                style: TextStyle(
                  fontSize: 13.0,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: _getButtonList(),
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                height: 1,
                width: screenWidth - 40,
                color: Color(0xFFEDEDED),
              )
            ],
          ),
          Container(
            color: Colors.transparent,
            padding: EdgeInsets.only(left: 20, right: 20),
            child: ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                '取消',
                style: TextStyle(fontSize: 17),
              ),
              style: ButtonStyle(
                minimumSize:
                MaterialStateProperty.all<Size>(Size(double.infinity, 44)),
                elevation: MaterialStateProperty.resolveWith<double>(
                      (Set<MaterialState> states) {
                    // 为所有状态设置阴影为0
                    return 0;
                  },
                ),
              ),
            ),
          ),
          SizedBox(height: bottomPadding),
        ],
      ),
    );
  }
}


class RemoteParkActionDialog extends StatefulWidget {
  final CarControlItemModel carControlItemModel;
  final void Function(
      {CarControlItemModel? controlItemModel,
      CarServiceStatusModel? statusModel})? onControlButtonClicked;
  const RemoteParkActionDialog(
      {super.key,
      required this.carControlItemModel,
      this.onControlButtonClicked});

  @override
  State<RemoteParkActionDialog> createState() => _RemoteParkActionDialogState();
}

class _RemoteParkActionDialogState extends State<RemoteParkActionDialog> {
  late CarServiceModel serviceModel;
  late List<CarServiceStatusModel> statusList;

  List<Widget> _getButtonList() {
    List<Widget> result = [];
    for (CarServiceStatusModel statusModel in statusList) {
      result.add(SettingButton(
        statusModel: statusModel,
        onPressed: () {
          widget.onControlButtonClicked?.call(
            controlItemModel: widget.carControlItemModel,
            statusModel: statusModel,
          );
        },
      ));
    }
    return result;
  }

  @override
  void initState() {
    List<CarServiceStatusModel> result = [];
    List<String> nameLsit = ['泊入', '泊出'];
    List<String> imgLsit = [
      'assets/images/use_car_page/remote_park/remote_park_intel_in.png',
      'assets/images/use_car_page/remote_park/remote_park_intel_out.png'
    ];
    for (var i = 0; i < nameLsit.length; i++) {
      CarServiceStatusModel model = CarServiceStatusModel(
          serviceStatusName: nameLsit[i],
          serviceStatusValue: '',
          serviceStatusImage: imgLsit[i],
          serviceStatusLevel: '',
          serviceStatusAction: '',
          serviceStatusResultType: '',
          backStatusValue: '',
          serviceStatusSkipType: '',
          serviceStatusSkipTarget: '',
          appPointId: '');
      result.add(model);
    }
    statusList = result;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double bottomPadding = MediaQueryData.fromWindow(window).padding.bottom;
    double screenWidth = MediaQueryData.fromWindow(window).size.width;
    return Container(
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(16.0),
          topLeft: Radius.circular(16.0),
        ),
        color: Colors.white,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          const SizedBox(height: 25),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children:const [
               SizedBox(width: 40),
                 Text(
                  '',
                  style: TextStyle(
                    fontSize: 13.0,
                    color: Colors.grey,
                  ),
                ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: _getButtonList(),
          ),
          const SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                height: 1,
                width: screenWidth - 40,
                color: Color(0xFFEDEDED),
              )
            ],
          ),
          Container(
            color: Colors.transparent,
            padding: EdgeInsets.only(left: 20, right: 20),
            child: ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                '取消',
                style: TextStyle(fontSize: 17),
              ),
              style: ButtonStyle(
                minimumSize:
                    MaterialStateProperty.all<Size>(Size(double.infinity, 44)),
                elevation: MaterialStateProperty.resolveWith<double>(
                  (Set<MaterialState> states) {
                    // 为所有状态设置阴影为0
                    return 0;
                  },
                ),
              ),
            ),
          ),
          SizedBox(height: bottomPadding),
        ],
      ),
    );
  }
}
