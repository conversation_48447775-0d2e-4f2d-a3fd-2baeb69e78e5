{"app": {"bundleName": "com.sgmw.wuling", "debug": true, "versionCode": 59, "versionName": "1.1.1", "minAPIVersion": 50000012, "targetAPIVersion": 50101019, "apiReleaseType": "Beta1", "compileSdkVersion": "5.1.1.66", "compileSdkType": "HarmonyOS", "appEnvironments": [], "bundleType": "app", "buildMode": "debug"}, "module": {"name": "mobile_scanner", "type": "har", "deviceTypes": ["default"], "requestPermissions": [{"name": "ohos.permission.CAMERA", "reason": "$string:request_perm", "usedScene": {"when": "inuse"}}], "packageName": "mobile_scanner", "installationFree": false, "virtualMachine": "ark12.0.2.0", "compileMode": "esmodule", "dependencies": []}}