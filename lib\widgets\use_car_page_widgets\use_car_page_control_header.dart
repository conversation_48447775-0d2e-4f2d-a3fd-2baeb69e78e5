/// FileName use_car_page_control_header
///
/// <AUTHOR>
/// @Date 2024/5/10 15:47
///
/// @Description TODO

import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/extensions/car/car_status_model_extensions.dart';
import 'package:wuling_flutter_app/models/car/car_info_model.dart';
import 'package:wuling_flutter_app/models/car/car_mini_life_model.dart';
import 'package:wuling_flutter_app/models/car/car_status_model.dart';
import 'package:wuling_flutter_app/models/car/car_control_item_model.dart';
import 'package:wuling_flutter_app/models/car/car_service_model.dart';
import 'package:wuling_flutter_app/models/car/car_service_status_model.dart';
import 'package:wuling_flutter_app/utils/manager/location_manager.dart';
import 'package:wuling_flutter_app/widgets/common/image_view.dart';
import 'package:wuling_flutter_app/widgets/use_car_page_widgets/mini_life_widget.dart';
import 'package:wuling_flutter_app/widgets/use_car_page_widgets/use_car_page_control_title_widget.dart';
import 'package:wuling_flutter_app/widgets/use_car_page_widgets/use_car_page_control_data_widget.dart';
import 'package:wuling_flutter_app/models/user/user_handle_model.dart';
import 'package:wuling_flutter_app/utils/manager/ble_manager.dart';
import '../../models/car/car_maintenance_model.dart';
import 'package:wuling_flutter_app/widgets/use_car_page_widgets/navigation_list_widget.dart'
    as nav;
import 'package:wuling_flutter_app/page/use_car_page/car_control_page.dart';

import 'use_car_page_control_container_widget.dart';

class UseCarPageControlHeader extends StatelessWidget {
  final BleStatus currentBleStatus;
  final bool isHandleConnectBle;
  final List<CarControlItemModel> controlItemList;
  final List<CarServiceModel> ctrlServiceList;
  final List<CarServiceModel> toolServiceList;
  final List<CarServiceModel>? notifiList;
  final CarMaintenanceModel? mainModel;
  final bool isRefresh;
  final CarStatusModel? statusModel;
  final CarInfoModel? carInfoModel;
  final void Function({CarControlItemModel carControlItemModel})?
      onControlButtonClicked;
  final void Function({CarServiceModel serviceModel})? onServiceButtonClicked;
  final VoidCallback onMyCarButtonClicked;
  final VoidCallback? onSwitchButtonClicked;
  final VoidCallback? onChargeButtonClicked;
  final VoidCallback? onSettingButtonClicked;
  final VoidCallback? onBleButtonClicked;
  final VoidCallback? onCarImageClicked;
  final List<UserHandleModel> useCarServiceList;
  final void Function({required UserHandleModel userHandleModel})?
      onUseCarServiceButtonClicked;
  const UseCarPageControlHeader(
      {Key? key,
      required this.currentBleStatus,
      required this.isHandleConnectBle,
      required this.carInfoModel,
      required this.statusModel,
      required this.controlItemList,
      required this.ctrlServiceList,
      required this.toolServiceList,
      required this.notifiList,
      required this.mainModel,
      required this.isRefresh,
      this.onControlButtonClicked,
      this.onServiceButtonClicked,
      this.onChargeButtonClicked,
      required this.onMyCarButtonClicked,
      this.onSwitchButtonClicked,
      required this.useCarServiceList,
      this.onUseCarServiceButtonClicked,
      this.onSettingButtonClicked,
      this.onBleButtonClicked,
      this.onCarImageClicked})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
        color: Color(0xFFF7F9FB),
        // height: height,
        child: Stack(
          children: <Widget>[
            Container(
              color: Colors.transparent,
              child: ImageView(
                'assets/images/use_car_page/car_background.png',
                fit: BoxFit.cover,
                noExpend: true,
              ),
            ),
            Container(
              color: Colors.transparent,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: <Widget>[
                  ControlTitleWidget(
                    carName: carInfoModel?.carName ??
                        carInfoModel?.carTypeName ??
                        '----',
                    collectTime: statusModel?.collectTime ?? '',
                    carPosition: statusModel?.position ?? '',
                    carLatitude: statusModel?.latitude ?? '',
                    carLongitude: statusModel?.longitude ?? '',
                    onMyCarButtonClicked: onMyCarButtonClicked,
                    onSwitchButtonClicked: onSwitchButtonClicked,
                    onSettingButtonClicked: onSettingButtonClicked,
                    hideSettingButton: false,
                    isCarOwner: carInfoModel?.relation == 1,
                    hasMoreCar: carInfoModel?.hasMoreCar == 1,
                    carInfoModel: carInfoModel,
                    statusModel: statusModel,
                    notifiList: notifiList,
                    mainModel: mainModel,
                    isRefresh: isRefresh,
                  ),
                  DataDisplayWidget(
                    currentBleStatus: currentBleStatus,
                    isHandleConnectBle: isHandleConnectBle,
                    carStatusModel: statusModel,
                    carInfoModel: carInfoModel,
                    onChargeButtonClicked: onChargeButtonClicked,
                    onBleButtonClicked: onBleButtonClicked,
                    onCarImageClicked: onCarImageClicked,
                    ctrlServiceList: ctrlServiceList,
                  ),
                  CarControlContainerWidget(
                      ctrlItemList: controlItemList,
                      onControlButtonClicked: onControlButtonClicked,
                      toolServiceList: toolServiceList,
                      onServiceButtonClicked: onServiceButtonClicked,
                      useCarServiceList: useCarServiceList,
                      onUseCarServiceButtonClicked:
                          onUseCarServiceButtonClicked),
                  // 移除重复组件，只保留车辆信息和车图
                  const SizedBox(
                    height: 20,
                  ),
                ],
              ),
            )
          ],
        ));
  }
}

class UseCarPageControlHeaderEV extends StatelessWidget {
  final BleStatus currentBleStatus;
  final bool isHandleConnectBle;
  final List<CarControlItemModel> controlItemList; //车控位置按钮List
  final List<CarControlItemModel> controlSubItemList; //新UI空调位置List
  final List<CarServiceModel> ctrlServiceList;
  final List<CarServiceModel> toolServiceList; //工具服务List
  final CarStatusModel? statusModel;
  final CarInfoModel? carInfoModel;
  final void Function({CarControlItemModel carControlItemModel})?
      onControlButtonClicked;
  final void Function(
          {required CarControlItemModel carControlItemModel,
          required CarServiceStatusModel? statusModel})?
      onDirectACControlButtonClicked;
  final void Function({CarServiceModel serviceModel})? onServiceButtonClicked;
  final void Function(
          {required CarControlItemModel carControlItemModel,
          required CarServiceStatusModel statusModel})?
      onBleControlButtonClicked; // 新增：离线模式蓝牙控制回调
  final VoidCallback onMyCarButtonClicked;
  final VoidCallback? onSwitchButtonClicked;
  final VoidCallback? onChargeButtonClicked;
  final VoidCallback? onSettingButtonClicked;
  final VoidCallback? onBleButtonClicked;
  final List<UserHandleModel> useCarServiceList;
  final void Function({required UserHandleModel userHandleModel})?
      onUseCarServiceButtonClicked;
  final List<CarServiceModel>? notifiList;
  final CarMaintenanceModel? mainModel;
  final bool isRefresh;
  final VoidCallback? onCarImageClicked;
  final List<nav.MenuItemCard> navigationList;
  final VoidCallback? onBatteryLayoutClicked;
  final VoidCallback? onCarDetailsClicked;
  final bool showNetworkBanner;
  final bool isNetworkConnected;
  final bool isOfflineMode; // 新增：是否进入离线模式
  final VoidCallback? onEnterOfflineModeClicked; // 新增：进入离线模式回调
  final List<CarServiceModel> activityList;
  final CarMiniLifeModel? miniLifeModel;
  final void Function(String? url) onMiniLifeClicked;
  const UseCarPageControlHeaderEV({
    Key? key,
    required this.currentBleStatus,
    required this.isHandleConnectBle,
    required this.carInfoModel,
    required this.statusModel,
    required this.controlItemList,
    required this.controlSubItemList,
    required this.ctrlServiceList,
    required this.toolServiceList,
    this.onControlButtonClicked,
    this.onDirectACControlButtonClicked,
    this.onServiceButtonClicked,
    this.onChargeButtonClicked,
    required this.onMyCarButtonClicked,
    this.onSwitchButtonClicked,
    required this.useCarServiceList,
    this.onUseCarServiceButtonClicked,
    this.onSettingButtonClicked,
    this.onBleButtonClicked,
    required this.notifiList,
    required this.mainModel,
    required this.isRefresh,
    required this.navigationList,
    this.onCarImageClicked,
    this.onBatteryLayoutClicked,
    this.onCarDetailsClicked,
    this.showNetworkBanner = false,
    this.isNetworkConnected = true,
    this.isOfflineMode = false,
    this.onEnterOfflineModeClicked,
    this.onBleControlButtonClicked, // 新增：离线模式蓝牙控制回调
    required this.activityList,
    required this.miniLifeModel,
    required this.onMiniLifeClicked,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
        color: const Color(0xFFF7F9FB),
        // height: height,
        child: Stack(
          children: <Widget>[
            Container(
              color: Colors.transparent,
              child: ImageView(
                'assets/images/use_car_page/new_UI/car_background_new.png',
                fit: BoxFit.cover,
                noExpend: true,
              ),
            ),
            Container(
              color: Colors.transparent,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: <Widget>[
                  ControlTopCarInfoEVWidget(
                    carName: carInfoModel?.carName ??
                        carInfoModel?.carTypeName ??
                        '----',
                    collectTime: statusModel?.collectTime ?? '',
                    onMyCarButtonClicked: onMyCarButtonClicked,
                    onSettingButtonClicked: onSettingButtonClicked,
                    hideSettingButton: false,
                    isCarOwner: carInfoModel?.relation == 1,
                    hasMoreCar: carInfoModel?.hasMoreCar == 1,
                    batSoc: statusModel?.batterySoc ?? '--',
                    leftMileage: statusModel?.leftMileage ?? '--',
                    leftChargeTime: statusModel?.leftChargeTime,
                    hasBLE: carInfoModel?.bleType == 2,
                    currentBleStatus: currentBleStatus,
                    isHandleConnectBle: isHandleConnectBle,
                    carInfoModel: carInfoModel,
                    statusModel: statusModel,
                    ctrlServiceList: ctrlServiceList,
                    isCharging: statusModel?.isCharging() ?? false,
                    onBleButtonClicked: onBleButtonClicked,
                    notifiList: notifiList,
                    mainModel: mainModel,
                    isRefresh: isRefresh,
                    onCarImageClicked: onCarImageClicked,
                    showNetworkBanner: showNetworkBanner,
                    isNetworkConnected: isNetworkConnected,
                    isOfflineMode: isOfflineMode,
                    onEnterOfflineModeClicked: onEnterOfflineModeClicked,
                  ),
                  // 集成MyCarPage组件
                  MyCarPage(
                    controlItemList: controlItemList,
                    controlSubItemList: controlSubItemList,
                    toolServiceList: toolServiceList,
                    navigationList: navigationList,
                    statusModel: statusModel,
                    onControlButtonClicked: onControlButtonClicked,
                    onACControlButtonClicked: (
                        {required CarControlItemModel carControlItemModel,
                        CarServiceStatusModel? statusModel}) {
                      // 调用新的直接空调控制回调
                      if (onDirectACControlButtonClicked != null) {
                        onDirectACControlButtonClicked!(
                            carControlItemModel: carControlItemModel,
                            statusModel: statusModel);
                      } else if (onControlButtonClicked != null) {
                        // 回退到原来的回调
                        onControlButtonClicked!(
                            carControlItemModel: carControlItemModel);
                      }
                    },
                    onServiceButtonClicked: onServiceButtonClicked,
                    onBatteryLayoutClicked: onBatteryLayoutClicked, // 充电地图按钮点击
                    onChargeMapButtonClicked:
                        onBatteryLayoutClicked, // 充电地图按钮点击
                    onCarDetailsClicked: onCarImageClicked, // 电池布局点击跳转到车辆详情
                    isNetworkConnected: isNetworkConnected, // 传递网络连接状态
                    isOfflineMode: isOfflineMode, // 传递离线模式状态
                    onBleControlButtonClicked:
                        onBleControlButtonClicked, // 传递蓝牙控制回调
                  ),
                  if (activityList.isNotEmpty || miniLifeModel != null)
                    MiniLifeWidget(
                        activityList: activityList,
                        miniLifeModel: miniLifeModel,
                        onServiceButtonClicked: onServiceButtonClicked,
                        onMiniLifeClicked: onMiniLifeClicked),
                  Container(height: 40),
                ],
              ),
            )
          ],
        ));
  }
}
