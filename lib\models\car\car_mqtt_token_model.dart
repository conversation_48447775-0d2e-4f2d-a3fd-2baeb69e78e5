import 'package:flutter/foundation.dart';
import 'package:quiver/core.dart';

import '../index.dart';

@immutable
class CarMqttTokenModel {

  const CarMqttTokenModel({
    this.token,
    this.expireIn,
    this.refreshToken,
    this.refreshExpireIn,
  });

  final String? token;
  final int? expireIn;
  final String? refreshToken;
  final int? refreshExpireIn;

  factory CarMqttTokenModel.fromJson(Map<String,dynamic> json) => CarMqttTokenModel(
    token: json['token']?.toString(),
    expireIn: json['expire_in'] != null ? json['expire_in'] as int : null,
    refreshToken: json['refresh_token']?.toString(),
    refreshExpireIn: json['refresh_expire_in'] != null ? json['refresh_expire_in'] as int : null
  );
  
  Map<String, dynamic> toJson() => {
    'token': token,
    'expire_in': expireIn,
    'refresh_token': refreshToken,
    'refresh_expire_in': refreshExpireIn
  };

  CarMqttTokenModel clone() => CarMqttTokenModel(
    token: token,
    expireIn: expireIn,
    refreshToken: refreshToken,
    refreshExpireIn: refreshExpireIn
  );


  CarMqttTokenModel copyWith({
    Optional<String?>? token,
    Optional<int?>? expireIn,
    Optional<String?>? refreshToken,
    Optional<int?>? refreshExpireIn
  }) => CarMqttTokenModel(
    token: checkOptional(token, () => this.token),
    expireIn: checkOptional(expireIn, () => this.expireIn),
    refreshToken: checkOptional(refreshToken, () => this.refreshToken),
    refreshExpireIn: checkOptional(refreshExpireIn, () => this.refreshExpireIn),
  );

  @override
  bool operator ==(Object other) => identical(this, other)
    || other is CarMqttTokenModel && token == other.token && expireIn == other.expireIn && refreshToken == other.refreshToken && refreshExpireIn == other.refreshExpireIn;

  @override
  int get hashCode => token.hashCode ^ expireIn.hashCode ^ refreshToken.hashCode ^ refreshExpireIn.hashCode;
}
