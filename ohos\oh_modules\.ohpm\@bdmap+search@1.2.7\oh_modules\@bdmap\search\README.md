# @bdmap/search
## 简介
- 百度地图鸿蒙API11版
- 支持开发者快速使用地图服务能力
- 此版本提供地图正逆地址解析、POI检索
- 此版本提供驾车路线规划、步行路线规划、骑行路线规划、公交路线规划

## 下载安装

```
ohpm install @bdmap/search
```
## 使用说明
参考：[在线文档](https://lbsyun.baidu.com/faq/api?title=harmonynextsdk)

## 使用示例
```typescript
import { PoiSearch } from "@bdmap/search";

const search: PoiSearch = new PoiSearch();
search.searchInCity(this.keyword, (results:Array<result>) => {
    for (let i = 0; i < results.length; i++) {
        let location: LatLng = new LatLng(results[i].location.lat, results[i].location.lng);
        let image:ImageEntity = new ImageEntity('rawfile://poicity.png', 50, 81);
        let marker:Marker = new Marker({
            position: location,
            icon: image,
            yOffset: 0,
            isFlat: false,
            isDraggable: true,
            rotate: 0,
            alpha: 0.9,
            scaleX: 2,
            scaleY: 2,
            isTop: true
        });
        this.mapController?.addOverlay(marker);
    }
}, {});

```
## 需要权限

```
"ohos.permission.GET_WIFI_INFO",
"ohos.permission.GET_NETWORK_INFO",
"ohos.permission.GET_BUNDLE_INFO",
"ohos.permission.INTERNET",
```
## 依赖
1.百度鸿蒙地图基础包:@bdmap/base

## 约束与限制
1.仅支持标准系统上运行，支持设备：华为手机。

2.HarmonyOS系统：HarmonyOS NEXT Developer Preview1及以上。

3.DevEco Studio版本：DevEco Studio NEXT Developer Preview1及以上。

4.HarmonyOS SDK版本：HarmonyOS NEXT Developer Preview1 SDK及以上。