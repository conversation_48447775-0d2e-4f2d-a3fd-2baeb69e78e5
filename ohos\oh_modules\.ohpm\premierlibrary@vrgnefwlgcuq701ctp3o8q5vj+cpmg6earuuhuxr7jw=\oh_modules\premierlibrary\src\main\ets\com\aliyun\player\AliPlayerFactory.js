import { ApsaraVideoPlayer } from './ApsaraVideoPlayer';
import { ApsaraVideoListPlayer } from './ApsaraVideoListPlayer';
import { PrivateService } from '../private_service/PrivateService';
import { ApsaraLiveShiftPlayer } from './ApsaraLiveShiftPlayer';
export class AliPlayerFactory {
    static createAliPlayer(z6, a7) {
        PrivateService.preInitService(z6);
        if (a7) {
            return new ApsaraVideoPlayer(z6, a7);
        }
        else {
            return new ApsaraVideoPlayer(z6, "");
        }
    }
    static createAliListPlayer(x6, y6) {
        PrivateService.preInitService(x6);
        if (y6) {
            return new ApsaraVideoListPlayer(x6, y6);
        }
        else {
            return new ApsaraVideoListPlayer(x6, "");
        }
    }
    static createAliLiveShiftPlayer(v6, w6) {
        PrivateService.preInitService(v6);
        if (w6) {
            return new ApsaraLiveShiftPlayer(v6, w6);
        }
        else {
            return new ApsaraLiveShiftPlayer(v6, "");
        }
    }
}
