import deviceinfo from '@ohos.deviceInfo';
import bundleManager from '@ohos.bundle.bundleManager';
export class AIOCrashDeviceInfo {
    static getVersionId() {
        return deviceinfo.versionId;
    }
    static getProductModel() {
        return deviceinfo.productModel;
    }
    static getSoftwareModel() {
        return deviceinfo.softwareModel;
    }
    static getHardwareModel() {
        return deviceinfo.hardwareModel;
    }
    static getDistributionOSVersion() {
        return deviceinfo.distributionOSVersion;
    }
    static getDistributionOSApiVersion() {
        return deviceinfo.distributionOSApiVersion;
    }
    static getSDKApiVersion() {
        return deviceinfo.sdkApiVersion;
    }
    static getODID() {
        return deviceinfo.ODID;
    }
    static getABIList() {
        return deviceinfo.abiList;
    }
    static getBrand() {
        return deviceinfo.brand;
    }
    static getModel() {
        return deviceinfo.productModel;
    }
    static getOSName() {
        return 'harmonyos';
    }
    static getOSVersion() {
        return deviceinfo.osFullName;
    }
    static getOSSDKApiVersion() {
        return deviceinfo.sdkApiVersion;
    }
    static getApplicationBundleName() {
        const g6 = bundleManager.getBundleInfoForSelfSync(bundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_APPLICATION | bundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_SIGNATURE_INFO);
        return g6.appInfo.name;
    }
}
