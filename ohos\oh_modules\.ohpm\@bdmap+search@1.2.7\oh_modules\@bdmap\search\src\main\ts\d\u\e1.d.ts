import { LatLng } from '@bdmap/base';
import { WeatherServerType } from "./c3";
import { WeatherDataType } from "./v";
import { LanguageType } from "../base/x2";
/**
 * 天气请求数据
 * 开通高级权限： http://lbsyun.baidu.com/apiconsole/fankui#?typeOne=产品需求&typeTwo=高级服务
 * 国内行政区域编码表：https://mapopen-website-wiki.cdn.bcebos.com/cityList/weather_district_id.csv
 * 海外行政区域编码表：https://mapopen-website-wiki.cdn.bcebos.com/cityList/weather_aboard_district_id.xlsx
 * 天气取值对照表：https://mapopen-website-wiki.cdn.bcebos.com/cityList/百度地图天气取值对照表(0410).xlsx
 *
 */
export interface WeatherSearchOption {
    /** 天气服务类型，默认国内 */
    serverType?: WeatherServerType;
    /** 区县的行政区划编码，和location二选一 */
    districtID?: string;
    /** 经纬度，高级字段，需要申请高级权限 */
    location?: LatLng;
    /** 请求数据类型，默认：WEATHER_DATA_TYPE_REAL_TIME */
    dataType?: WeatherDataType;
    /** 语言类型，默认中文。目前仅支持海外天气服务行政区划显示英文。*/
    languageType?: LanguageType;
}
