import { PoiBoundSearchOption } from "../../../d/g1/h/i1/j1";
import { PoiCitySearchOption } from "../../../d/g1/h/i1/k1";
import { PoiNearbySearchOption } from "../../../d/g1/h/i1/l1";
import { SearchRequest } from '../../base/base';
import { UrlProvider } from "../../base/d3";
export declare class PoiSearchRequest extends SearchRequest {
    constructor(s16: PoiCitySearchOption | PoiBoundSearchOption | PoiNearbySearchOption);
    private boundSearchBuildParams;
    private nearBySearchBuildParams;
    getUrlDomain(m16: UrlProvider): string;
    private inCitySearchBuildParams;
}
