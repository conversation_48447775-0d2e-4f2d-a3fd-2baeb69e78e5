import { IPlayer } from './IPlayer';
import { UrlSource } from './source/UrlSource';
export interface UrlPlayer extends IPlayer {
    /**
     * 设置UrlSource数据源
     *
     * @param urlSource 本机地址或网络地址。见{@link UrlSource}。
     */
    /****
     * Set a UrlSource source.
     *
     * @param urlSource The specified UrlSource source: a local address or a URL. See {@link UrlSource}.
     */
    setUrlDataSource: (urlSource: UrlSource) => void;
}
