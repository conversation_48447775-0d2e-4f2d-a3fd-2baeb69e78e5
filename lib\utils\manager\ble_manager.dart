import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:wuling_flutter_app/api/car_api.dart';
import 'package:wuling_flutter_app/models/car/car_ble_key_model.dart';
import 'package:wuling_flutter_app/utils/manager/control_code_manager.dart';
import 'package:wuling_flutter_app/utils/exceptions.dart';
import 'package:wuling_flutter_app/extensions/car/car_ble_key_model_extensions.dart';
import 'package:wuling_flutter_app/models/ble/app_authorization_request_frame.dart';
import 'package:wuling_flutter_app/models/ble/ucu_authorization_request_frame.dart';
import 'package:wuling_flutter_app/models/ble/app_control_request_frame.dart';
import 'package:wuling_flutter_app/models/ble/ucu_control_response_frame.dart';
import 'package:wuling_flutter_app/utils/sp_util.dart';
import 'package:wuling_flutter_app/utils/str_util.dart';

import '../../constant/car_constant.dart';
import 'log_manager.dart';

/// 回调类型定义，用于蓝牙控制响应。
typedef BleCarControlResponseCallback = void Function(Exception? error, bool isSuccess);
/// 回调类型定义，用于蓝牙泊车控制响应。
typedef BleRemoteParkCtrlResponseCallback = void Function(
    Exception? error, UcuControlResponseFrame response);

const String kBtCarLock = 'btCarLock'; // 请替换为实际值

// 蓝牙连接状态枚举类型
enum BleStatus {
  // 未连接
  bleDefault(0),
  // 搜索中
  bleSearching(1),
  // 开始授权
  bleAuthorizing(2),
  // 第一次握手授权中
  bleAuthHandshaking1(3),
  // 第二次握手授权中
  bleAuthHandshaking2(4),
  // 鉴权成功
  bleAuthorized(5);

  final int value;

  const BleStatus(this.value);
}

/// 蓝牙管理器类
class BleManager {
  static final BleManager _instance = BleManager._internal();

  /// 获取 BleManager 的单例实例
  factory BleManager() {
    return _instance;
  }

  /// 私有构造函数，用于初始化 BleManager
  BleManager._internal() {
    // 初始化时开始监听蓝牙状态
    FlutterBluePlus.adapterState.listen((state) {
      _updateSytemBleStatus(state);
    });
  }

  // <editor-fold desc="Properties">
  bool _isScanning = false;
  bool _hasBlueKey = false;
  bool _hasRefreshedBlueKey = false;
  bool _targetFound = false;
  bool _isConnecting = false;
  bool _isConnected = false;
  bool _isBleResponsePushed = false;
  bool _isHandleConnect = true;//是否是用户手动连接蓝牙，true表示手动连接，手动连接就显示那些loading和权限提示
  BluetoothDevice? _targetDevice;
  BluetoothDevice? _connectedDevice;
  Timer? _scanTimer;
  Timer? bleResponseLoadingTimer;
  Map<String, dynamic> _bleKeyModelData = {
    "userId": "",
    "bleMac": "",
    "keyId": "",
    "vin": "",
    "keyMasterRandom": "",
    "masterKey": "",
    "keyType": "",
    "endTime": "",
  };
  CarBleKeyModel? _bleKeyModel;

  // 当前的蓝牙状态
  BleStatus _currentStatus = BleStatus.bleDefault;
  BluetoothAdapterState _currentSystemBluetoothStatus = BluetoothAdapterState.off;
  // 创建一个广播流控制器，允许多个监听者订阅
  final StreamController<BleStatus> _statusController = StreamController<BleStatus>.broadcast();
  // 扫描结果监听订阅
  StreamSubscription? _scanSubscription;
  // 提供一个公开的只读的状态流
  Stream<BleStatus> get statusStream => _statusController.stream;
  // 获取当前的蓝牙状态
  BleStatus get currentStatus => _currentStatus;
  // 一对一指令类型的回调
  BleCarControlResponseCallback? _callbackForBleCarControlResponse;

  BluetoothCharacteristic? _authRequestCharacteristic;
  BluetoothCharacteristic? _authResponseCharacteristic;

  BluetoothCharacteristic? _controlRequestCharacteristic;
  BluetoothCharacteristic? _controlResponseCharacteristic;

  // 泊车指令类型的回调
  BleRemoteParkCtrlResponseCallback? _callbackForRemoteParkCtrlResponse;
  String _responseCallbackKey = '';
  bool get isHandleConnect => _isHandleConnect;
  /// 设备状态更新流
  StreamSubscription? _deviceStateSubscription;
  // </editor-fold>

  void setDefaultStatus () {
    _isScanning = false;
    _targetFound = false;
    _isConnected = false;
    _connectedDevice = null;
  }

  /// 更新系统蓝牙状态
  ///
  /// [state] 当前的蓝牙适配器状态
  void _updateSytemBleStatus(BluetoothAdapterState state) {
    _currentSystemBluetoothStatus = state;
    switch (state) {
      case BluetoothAdapterState.unavailable:
      case BluetoothAdapterState.unauthorized:
      case BluetoothAdapterState.off:
        LogManager().debug('[BleKey]===系统蓝牙关闭，恢复到未连接状态中---');
        if(_currentStatus == BleStatus.bleSearching){
          stopScan(false);
        }else{
          updateStatus(BleStatus.bleDefault);
        }
        setDefaultStatus();
        break;
      case BluetoothAdapterState.on:
        LogManager().debug('[BleKey]===系统蓝牙打开---');
        updateStatus(BleStatus.bleDefault);
        break;
      case BluetoothAdapterState.unknown:
        // TODO: Handle this case.
        break;
      case BluetoothAdapterState.turningOn:
        // TODO: Handle this case.
        break;
      case BluetoothAdapterState.turningOff:
        // TODO: Handle this case.
        break;
    }
  }

  // 方法：更新蓝牙状态
  void updateStatus(BleStatus newStatus) {
    if (_currentStatus != newStatus) {
      _currentStatus = newStatus;
      // 向流中添加新的状态
      _statusController.add(_currentStatus);
      LogManager().debug('蓝牙状态已更新为: $_currentStatus');
    }
  }

  /// 蓝牙连接入口方法,isHandle为null则默认是手动连接
  ///开始扫描附近的蓝牙设备,默认30s后停止扫描
  /// 返回一个 Future，表示扫描操作已开始
  Future<void> startScan({bool? isHandle,required Function(bool) onScanComplete}) async {
    if(isHandle == null ) {
      startScanWithTimeout(true,30, onScanComplete);
    }else {
      startScanWithTimeout(isHandle,30, onScanComplete);
    }

  }

  /// 开始扫描附近的蓝牙设备，并在指定时间后自动停止
  /// [isHandle] 是否是手动连接蓝牙
  /// [duration] 扫描持续的时间，单位为秒
  /// [onScanComplete] 扫描完成时的回调函数，参数为是否找到目标设备
  Future<void> startScanWithTimeout(bool isHandle, double duration, Function(bool) onScanComplete) async {
    _isHandleConnect = true;
    LogManager().debug('开始扫描附近的蓝牙设备，_isHandleConnect:$_isHandleConnect ，duration:$duration ，_isScanning:$_isScanning');
    if (!_isScanning) {
      //将进行自动连接
      // if(!_isHandleConnect){
      //   //蓝牙自动连接开关为关闭状态
      //   if(!canAutoConnectBle()){
      //     LogManager().debug('蓝牙自动连接开关为关闭状态，将不执行蓝牙自动连接 startScanWithTimeout()');
      //     return;
      //   }
      // }
      _isScanning = true;
      _targetFound = false;
      _targetDevice = null;
      _bleKeyModel = CarBleKeyModel.fromJson(_bleKeyModelData);
      updateStatus(BleStatus.bleSearching);

      // 设置扫描超时
      _scanTimer = Timer(Duration(milliseconds: (duration * 1000).round()), () {
        stopScan(_isHandleConnect);
        onScanComplete(_targetFound);
      });

      _scanSubscription = FlutterBluePlus.scanResults.listen((results) {
        if (_isScanning) {
          for (ScanResult r in results) {
            if (_processAdvertisementData(r.advertisementData, r.device)) {
              _targetDevice = r.device;
              _targetFound = true;
              LogManager().debug('发现目标设备${r.device.platformName}');
              stopScan(_isHandleConnect);
              onScanComplete(_targetFound);
              connectToDevice(_isHandleConnect,_targetDevice!);
              break;
            }
          }
        }
      });

      await FlutterBluePlus.startScan(
        timeout: Duration(milliseconds: (duration * 1000).round())
      );
    }
  }

  /// 处理广播数据
  ///
  /// [advertisementData] 广播数据
  /// [device] 蓝牙设备
  /// 返回 true 如果找到目标设备，否则返回 false
  bool _processAdvertisementData(AdvertisementData advertisementData, BluetoothDevice device) {
    String deviceName = device.platformName.isNotEmpty ? device.platformName : "未命名设备";
    LogManager().debug('$deviceName的advertisementData: ${advertisementData.toString()}');
    if (advertisementData.manufacturerData.isNotEmpty) {
      List<int> data = advertisementData.manufacturerData.values.first;
      if (data.length >= 6) {
        List<int> lastSixBytes = data.sublist(data.length - 6);
        String macAddress = lastSixBytes.map((e) => e.toRadixString(16).padLeft(2, '0')).join('');
        // LogManager().debug('$deviceName的ManufacturerData: ${data.map((e) => e.toRadixString(16).padLeft(2, '0')).join(' ')}');
        LogManager().debug('设备MAC地址: ${macAddress.toUpperCase()}');
        LogManager().debug('钥匙MAC地址: ${_bleKeyModel!.bleMacStr.toUpperCase()}');
        if (_bleKeyModel != null && _bleKeyModel!.bleMacStr.toLowerCase() == macAddress.toLowerCase()) {
          return true;
        }
      }
    }
    return false;
    // if(device.platformName.isNotEmpty){
    //   LogManager().debug('搜索到设备:${device.platformName}');
    //   if(device.platformName == 'MyVehicleSimulator'){
    //     return true;
    //   }
    // }
    return false;
  }

  /// 停止正在进行的蓝牙设备扫描
  Future<void> stopScan(bool isHandle) async {
    _isHandleConnect = true;
    LogManager().debug('[BleKey]===蓝牙连接——停止正在进行的蓝牙设备扫描===_isHandleConnect:$_isHandleConnect , _isScanning:$_isScanning');
    if (_isScanning) {
      _isScanning = false;
      _scanTimer?.cancel();
      await FlutterBluePlus.stopScan();
      if(_scanSubscription != null){
        _scanSubscription!.cancel();
        _scanSubscription = null;
        LogManager().debug('[BleKey]===蓝牙连接——取消扫描结果流订阅===');
      }
      if(!_targetFound) {
        updateStatus(BleStatus.bleDefault);
      }
      LogManager().debug('[BleKey]===蓝牙连接——停止扫描设备===');
    }
  }

  /// 获取目标设备
  BluetoothDevice? getTargetDevice() {
    return _targetDevice;
  }

  /// 获取蓝牙钥匙信息
  ///
  /// [vin] 车辆的VIN码
  /// [mobile] 用户的手机号
  /// [onResult] 回调函数，当钥匙获取成功或失败时调用，返回 [CarBleKeyModel?] 和错误信息（如果有）
  Future<void> getBluetoothKey({
    required String vin,
    required String mobile,
    required Function(CarBleKeyModel? bleKeyModel, String? error) onResult,
  }) async {
    try {
      CarBleKeyModel bleKeyModel = await carAPI.getCarBleKey(vin, mobile);
      // _bleKeyModel = bleKeyModel;
      // 存储网络获取的钥匙数据，避免直接影响当前正在使用的钥匙
      // 假如云端钥匙更新，重新连接的时候再使用网络钥匙数据
      _bleKeyModelData = bleKeyModel.toJson();
      _hasBlueKey = true;
      onResult(bleKeyModel, null); // 成功时回调钥匙模型和无错误信息
    } catch (e) {
      _hasBlueKey = false;
      onResult(null, e.toString()); // 失败时回调null和错误信息
    }
  }

  /// 刷新蓝牙钥匙
  ///
  /// 返回一个 Future，表示刷新蓝牙钥匙操作已完成
  Future<void> refreshBluetoothKey() async {
    // 实现刷新蓝牙钥匙的逻辑
    _hasRefreshedBlueKey = true;
  }

  /// 检查是否已获得蓝牙钥匙
  ///
  /// 返回 true 如果已成功获取蓝牙钥匙，否则返回 false
  bool hasBluetoothKey() {
    return _hasBlueKey;
  }

  void resetHasBluetoothKey(bool hasBluetoothKey){
    LogManager().debug('[BleKey]===重设蓝牙钥匙获取标记===hasBluetoothKey:$hasBluetoothKey');
    _hasBlueKey = hasBluetoothKey;
  }

  /// 获取当前蓝牙连接状态
  ///
  /// 返回当前的 [BleStatus]
  BleStatus getCurrentStatus() {
    return _currentStatus;
  }

  /// 获取当前系统蓝牙连接状态
  ///
  /// 返回当前的 [BluetoothAdapterState]
  BluetoothAdapterState getCurrentSystemBluetoothStatus() {
    return _currentSystemBluetoothStatus;
  }

  /// 获取蓝牙适配器的当前状态
  ///
  /// 返回一个 Future，解析为当前的 [BluetoothAdapterState]
  Future<BluetoothAdapterState> getBluetoothState() async {
    return await FlutterBluePlus.adapterState.first;
  }

  /// 检查蓝牙钥匙是否已刷新
  ///
  /// 返回 true 如果蓝牙钥匙已被刷新，否则返回 false
  bool isBluetoothKeyRefreshed() {
    return _hasRefreshedBlueKey;
  }

  /// 提供给定错误代码的描述
  ///
  /// [errorCode] 要描述的错误代码
  /// 返回一个包含错误描述的字符串
  String getErrorDescription(String errorCode) {
    // 实现错误代码描述逻辑
    return "错误: $errorCode";
  }

  /// 从本地存储加载蓝牙钥匙数据
  void loadLocalBluetoothKeyData() {
    // 实现加载本地蓝牙钥匙数据的逻辑
  }

  /// 监听设备连接状态
  void _listenToDeviceState() {
    if (_connectedDevice != null && _deviceStateSubscription == null) {
      _deviceStateSubscription = _connectedDevice!.connectionState.listen((state) async {
        if(_connectedDevice == null){
          updateStatus(BleStatus.bleDefault);
          return;
        }
        switch (state) {
          case BluetoothConnectionState.connected:
            LogManager().debug('[BleKey]===设备 ${_connectedDevice!.platformName} 已连接');
            _isConnected = true;
            break;
          case BluetoothConnectionState.disconnected:
            LogManager().debug('[BleKey]===设备 ${_connectedDevice!.platformName} 已断开连接');
            _isConnected = false;
            _connectedDevice = null;
            updateStatus(BleStatus.bleDefault);
            break;
          default:
            break;
        }
      });
    }
  }

  /// 停止监听设备连接状态
  void _stopListeningToDeviceState() {
    _deviceStateSubscription?.cancel();
    _deviceStateSubscription = null;
  }

  /// 连接到指定的蓝牙设备
  ///
  /// [device] 要连接的 BluetoothDevice
  /// 返回一个 Future<bool>，表示连接是否成功
  Future<bool> connectToDevice(bool isHandle,BluetoothDevice device) async {
    LogManager().debug('[BleKey]===开始连接connectToDevice,isHandle:$isHandle ，_isConnecting:$_isConnecting ，_isConnected:$_isConnected');
    if (_isConnecting || _isConnected) {
      LogManager().debug('[BleKey]===已经在连接或已连接到设备,跳过此次连接');
      _isHandleConnect = true;//连接中、已连接等状态,切换到手动连接
      return false;
    }

    try {
      _isHandleConnect = true;//未连接直接赋值
      _isConnecting = true;
      LogManager().debug('[BleKey]===开始连接${device.platformName}');
      await device.connect(timeout: Duration(seconds: 15), mtu: null);
      _connectedDevice = device;
      _isConnected = true;
      _isConnecting = false;
      _listenToDeviceState();  // 开始监听设备状态
      LogManager().debug('连接${device.platformName}成功');
      updateStatus(BleStatus.bleAuthorizing);
      readAndPrintServicesAndCharacteristics();
      return true;
    } catch (e) {
      _isConnecting = false;
      LogManager().debug('[BleKey]===连接设备失败_isHandleConnect:$_isHandleConnect ，失败原因：: ${e.toString()}');
      updateStatus(BleStatus.bleDefault);
      return false;
    }
  }

  /// 断开当前连接的蓝牙设备,isHandle: true用户主动断开  false被动断开
  Future<void> disconnectDevice(bool isHandle) async {
    _isHandleConnect = true;
    LogManager().debug('[BleKey]===断开当前连接的蓝牙设备，disconnectDevice ：_isHandleConnect:$_isHandleConnect');
    if (_connectedDevice != null) {
      await _connectedDevice!.disconnect();
      LogManager().debug('[BleKey]===断开当前连接的蓝牙设备---成功');
      _stopListeningToDeviceState();
      _connectedDevice = null;
      _isConnected = false;
      updateStatus(BleStatus.bleDefault);
    }
  }

  Future<void> setIsHandleConnect(bool isHandle) async {
    _isHandleConnect = isHandle;
    LogManager().debug('[BleKey]===设置是手动连接还是自动连接，setIsHandleConnect ：_isHandleConnect:$_isHandleConnect');
  }

  /// 检查是否是连接中
  bool isConnecting() {
    return _isConnecting;
  }

  /// 检查是否已连接到设备
  bool isConnected() {
    return _isConnected;
  }

  /// 提供蓝牙适配器状态变化的流
  ///
  /// 返回一个 [BluetoothAdapterState] 的 Stream
  Stream<BluetoothAdapterState> get bluetoothStateStream => FlutterBluePlus.adapterState;

  /// 提供过滤后的扫描结果的流
  ///
  /// 只返回可以连接的设备的 [ScanResult] 列表的 Stream
  Stream<List<ScanResult>> get filteredScanResultsStream async* {
    await for (final results in FlutterBluePlus.scanResults) {
      // 过滤出可以连接的设备
      final connectableDevices = results.where((result) {
        // 判断条件：设备必须有名称，且设备不是已连接状态
        return result.advertisementData.connectable;
      }).toList();

      // 如果有可连接设备，返回过滤后的结果
      if (connectableDevices.isNotEmpty) {
        yield connectableDevices;
      }
    }
  }

  /// 读取并打印当前连接设备的服务和特征
  Future<void> readAndPrintServicesAndCharacteristics() async {
    if (_connectedDevice == null) {
      LogManager().debug('[BleKey]===没有连接的设备');
      return;
    }

    LogManager().debug('[BleKey]===正在读取设备 ${_connectedDevice!.platformName} 的服务和特征...');
    try {
      List<BluetoothService> services = await _connectedDevice!.discoverServices();
      for (BluetoothService service in services) {
        // LogManager().debug('服务: ${service.uuid}');
        if(service.uuid.str.toUpperCase() == BleServiceConstants.authServiceUUID) {
          LogManager().debug('[BleKey]===发现鉴权服务：${service.uuid}');
          for (BluetoothCharacteristic characteristic in service
              .characteristics) {
            LogManager().debug('  特征: ${characteristic.uuid}');
            LogManager().debug('    属性: ${_getCharacteristicProperties(characteristic)}');
            if(characteristic.uuid.str.toUpperCase() == BleServiceConstants.authWriteCharacteristicUUID){
              LogManager().debug('     发现鉴权写入特征：${characteristic.uuid}');
              _authRequestCharacteristic = characteristic;
            }
            if(characteristic.uuid.str.toUpperCase() == BleServiceConstants.authReadCharacteristicUUID){
              LogManager().debug('     发现鉴权读取特征：${characteristic.uuid}');
              _authResponseCharacteristic = characteristic;
            }
          }
          LogManager().debug(''); // 为了更好的可读性添加空行
        } else if (service.uuid.str.toUpperCase() == BleServiceConstants.controlServiceUUID){
          LogManager().debug('[BleKey]===发现控制服务：${service.uuid}');
          for (BluetoothCharacteristic characteristic in service
              .characteristics) {
            LogManager().debug('  特征: ${characteristic.uuid}');
            LogManager().debug('    属性: ${_getCharacteristicProperties(characteristic)}');
            if(characteristic.uuid.str.toUpperCase() == BleServiceConstants.controlWriteCharacteristicUUID){
              LogManager().debug('     发现控制写入特征：${characteristic.uuid}');
              _controlRequestCharacteristic = characteristic;
            }
            if(characteristic.uuid.str.toUpperCase() == BleServiceConstants.controlReadCharacteristicUUID){
              LogManager().debug('     发现控制读取特征：${characteristic.uuid}');
              _controlResponseCharacteristic = characteristic;
            }
          }
          LogManager().debug(''); // 为了更好的可读性添加空行
        }
      }
      if(_authRequestCharacteristic != null && _authResponseCharacteristic != null){
        // 如果鉴权的两个特征都已经读取到，则开始订阅回复特征，并向ucu发送keyid（第一次鉴权请求）
        await getUcuAuthorizationRequest();
        await sendAppAuthorizationRequestWithBleKey();
      }
      if(_controlResponseCharacteristic != null){
        await getUcuControlResponse();
      }
    } catch (e) {
      LogManager().debug('[BleKey]===读取服务和特征时出错: $e');
    }
  }

  // 监听鉴权回复
  Future<void> getUcuAuthorizationRequest() async {
    if(_connectedDevice == null || _authResponseCharacteristic == null){
      LogManager().debug("[BleKey]===目标设备未连接或鉴权回复特征未发现");
      return;
    }
    LogManager().debug('[BleKey]===订阅特征${_authResponseCharacteristic!.uuid.str}');
    await _authResponseCharacteristic!.setNotifyValue(true);
    _authResponseCharacteristic!.onValueReceived
        .distinct((previous, next) => listEquals(previous, next))
        .listen((value) async {
      // 处理收到的特征值
      await onAuthResponseValueReceived(value);
    });
    LogManager().debug('[BleKey]===特征${_authResponseCharacteristic!.uuid.str}订阅完毕');
  }

  onAuthResponseValueReceived(List<int> value) async {
    if(value.isEmpty) return;

    String receivedDataHex = StrUtil.bytesToHexString(Uint8List.fromList(value));
    LogManager().debug('[BleKey]===特征${_authResponseCharacteristic!.uuid.str}接收到数据: $value, 长度：${value.length}, hex形式：0x$receivedDataHex');
    Uint8List ucuRequestData = Uint8List.fromList(value);
    String key = _bleKeyModel!.aes128Key;
    try {
      UcuAuthorizationRequestFrame ucuAuthorizationRequestFrame = UcuAuthorizationRequestFrame
          .fromDataFrame(ucuRequestData, key);
      // Service ID符合协议,则进行进一步处理
      if (ucuAuthorizationRequestFrame.isUcuAuthorizeRequest()) {
        // 如果是ucu发过来的鉴权请求，并且处于第一次握手的阶段（ucu第一次回复）
        if(_currentStatus == BleStatus.bleAuthHandshaking1) {
          _bleKeyModel!.randomData1 = ucuAuthorizationRequestFrame.randomData;
          // 将ucu第一次回复的随机值random1发送给ucu进行验证
          await sendAppAuthorizationRequestWithRandom1(
              random1: _bleKeyModel!.randomData1);
        }
      } else if (ucuAuthorizationRequestFrame.isUcuAuthorizeResponse()) {
        // 如果是ucu发过来的鉴权请求,并且处于第二次握手阶段（ucu第二次回复）
        if(_currentStatus == BleStatus.bleAuthHandshaking2) {
          String random2Local = _bleKeyModel!.randomData2 ?? '';
          String random2Ucu = ucuAuthorizationRequestFrame.getRandomData();
          LogManager().debug('[BleKey]===正在进行随机数校验---');
          LogManager().debug('[BleKey]===random2Local = $random2Local---');
          LogManager().debug('[BleKey]===random2Ucu   = $random2Ucu---');
          if (random2Local == random2Ucu) {
            LogManager().debug('[BleKey]===双向验证通过，可以开始进行控制---');
            updateStatus(BleStatus.bleAuthorized);
          } else {
            LogManager().debug(
                '[BleKey]===本地random2与UCU报文random2不相等，授权校验未通过---');
            disconnectDevice(_isHandleConnect);
          }
        }
      } else {
        LogManager().debug('[BleKey]===功能类型：${ucuAuthorizationRequestFrame
            .getSubFunction()}，未接收到功能类型正确的服务数据');
        disconnectDevice(_isHandleConnect);
      }
    } on ParsingException catch (e) {
      // 处理解析异常，例如提示用户或记录日志
      LogManager().debug("[BleKey]===解析失败：$e");
    } catch (e) {
      // 处理其他未预料的异常
      LogManager().debug("[BleKey]===发生未知错误：$e");
    }
  }

  // 发送鉴权请求的方法
  Future<void> sendAppAuthorizationRequestWithBleKey() async {
    // 检查外围设备和特征是否已初始化
    if (_connectedDevice == null || _authRequestCharacteristic == null) {
      LogManager().debug("[BleKey]===目标设备未连接或鉴权请求特征未发现, App无法发送鉴权请求");
      return;
    }
    //状态更新为开始第一次握手
    updateStatus(BleStatus.bleAuthHandshaking1);
    // 日志记录
    LogManager().debug("[BleKey]===正在进行蓝牙授权验证---");
    LogManager().debug("[BleKey]===App准备发送BLEkey给UCU模块---");
    if(!Platform.isIOS) {
      // 连接成功后请求 MTU 为 100
      int negotiatedMtu = await _connectedDevice!.requestMtu(100);
      LogManager().debug('协商的 MTU 大小：$negotiatedMtu');
    }
    // 获取当前时间戳（秒级）
    int currentTime = DateTime.now().millisecondsSinceEpoch ~/ 1000;
    String currentTimeHex = currentTime.toRadixString(16).toUpperCase();
    LogManager().debug("[BleKey]===当前时间戳：$currentTime, 十六进制形式：$currentTimeHex---");

    // 创建鉴权请求帧
    AppAuthorizationRequestFrame appRequest = AppAuthorizationRequestFrame.withCurrentTime(
      currentTime: currentTimeHex,
      bleKey: _bleKeyModel?.keyIdHex ?? '',
    );
    int delay = 200;
    LogManager().debug("[BleKey]===延时${delay}ms后发送---");
    await Future.delayed(Duration(milliseconds: delay));
    LogManager().debug("[BleKey]===延时发送---");
    // 发送加密数据到指定的 BLE 特征
    try {
      await _authRequestCharacteristic!.write(
        appRequest.encryptedData,
        withoutResponse: _authRequestCharacteristic!.properties.writeWithoutResponse,
        allowLongWrite: true
      );
      LogManager().debug("[BleKey]===App已发送第一次鉴权请求数据---");
    } catch (e) {
      LogManager().debug("[BleKey]===发送数据失败：$e---");
    }
  }

  // 发送鉴权请求的方法
  Future<void> sendAppAuthorizationRequestWithRandom1({String? random1}) async {
    if(random1 == null){
      LogManager().debug('[BleKey]===random1为空，请检查数据---');
      return;
    }
    //状态更新为开始第一次握手
    updateStatus(BleStatus.bleAuthHandshaking2);
    String random1Str = random1 ?? '';
    // 日志记录
    LogManager().debug("[BleKey]===App准备发送鉴权回复给UCU模块---");
    // 使用安全随机数生成器生成随机的32位整数
    int randomData = Random.secure().nextInt(1 << 32);
    // 将随机整数转换为大写的十六进制字符串
    String random2Str = randomData.toRadixString(16).toUpperCase();
    _bleKeyModel!.randomData2 = _addZero(random2Str, 8);
    LogManager().debug('[BleKey]===组件随机数2：0x${_bleKeyModel!.randomData2}---');
    LogManager().debug('[BleKey]===组件随机数1：0x$random1---');
    LogManager().debug('[BleKey]===组件BleKey：0x${_bleKeyModel!.keyIdHex}---');

    // 创建鉴权请求帧
    AppAuthorizationRequestFrame appRequest = AppAuthorizationRequestFrame.withRandomData(
      randomData1: random1Str,
      randomData2: random2Str,
      bleKey: _bleKeyModel!.keyIdHex,
      key: _bleKeyModel!.aes128Key
    );
    // 检查外围设备和特征是否已初始化
    if (_connectedDevice == null || _authRequestCharacteristic == null) {
      LogManager().debug("[BleKey]===目标设备未连接或鉴权请求特征未发现---");
      return;
    }

    // 发送加密数据到指定的 BLE 特征
    try {
      await _authRequestCharacteristic!.write(
        appRequest.encryptedData,
        withoutResponse: false,
        allowLongWrite: true
      );
      LogManager().debug("[BleKey]===App已发送第二次鉴权请求数据---");
    } catch (e) {
      LogManager().debug("发送数据失败：$e");
    }
  }

  // 监听控制回复
  Future<void> getUcuControlResponse() async {
    if(_connectedDevice == null || _controlResponseCharacteristic == null){
      LogManager().debug("目标设备未连接或控制回复特征未发现");
      return;
    }
    LogManager().debug('订阅特征${_controlResponseCharacteristic!.uuid.str}');
    await _controlResponseCharacteristic!.setNotifyValue(true);
    _controlResponseCharacteristic!.onValueReceived.listen((value) async {
      // 处理收到的特征值
      await onUcuControlResponseValueReceived(value);
    });
    LogManager().debug('特征${_controlResponseCharacteristic!.uuid.str}订阅完毕');
  }

  onUcuControlResponseValueReceived(List<int> value) async {
    if (value.isEmpty) return;
    _isBleResponsePushed = true;
    String receivedDataHex =
        StrUtil.bytesToHexString(Uint8List.fromList(value));
    LogManager().debug(
        '[BleKey]===特征${_authResponseCharacteristic!.uuid.str}接收到数据: $value, 长度：${value.length}, hex形式：0x$receivedDataHex');
    Uint8List ucuRequestData = Uint8List.fromList(value);
    String key = _bleKeyModel!.controlAes128Key ?? '';
    try {
      UcuControlResponseFrame ucuControlResponseFrame =
          UcuControlResponseFrame.fromDataFrame(ucuRequestData, key);
      String errorCode = ucuControlResponseFrame.getErrorCode() ?? '';
      String serviceId = ucuControlResponseFrame.getServiceId() ?? '';
      String subFunction = ucuControlResponseFrame.getSubfunction() ?? '';
      if (_callbackForRemoteParkCtrlResponse != null) {
        _callbackForRemoteParkCtrlResponse!(null,ucuControlResponseFrame);
      } else {
        ///非泊车状态（即没有泊车的代理）时，忽略泊车信号（服务类型="C065"）
        if (serviceId == 'C065') {
          return;
        }
        if (errorCode.length == 8) {
        if (errorCode == '00000000') {
          if (serviceId.toUpperCase() == 'A956' &&
              subFunction.toUpperCase() == '0001') {
            setBleCarControlResponse(true, '', '');
          } else {
            setBleCarControlResponse(false, '指令执行出错 请重试', '指令执行出错');
          }
        } else if (serviceId.toUpperCase() == 'FFFF' &&
            subFunction.toUpperCase() == '39D6') {
          String errorDescription =
              ucuControlResponseFrame.getErrorDescription();
          if (errorDescription.isNotEmpty) {
            setBleCarControlResponse(false, errorDescription, '指令执行出错');
          }
        } else {
          String errorDescription =
              ucuControlResponseFrame.getErrorDescription();
          if (errorDescription.isNotEmpty) {
            setBleCarControlResponse(false, errorDescription, '指令执行出错');
          }
        }
      } else {
        setBleCarControlResponse(false, '错误码数据有误', '指令执行出错');
      }
      }
    } on ParsingException catch (e) {
      // 处理解析异常，例如提示用户或记录日志
      LogManager().debug("解析失败：$e");
      setBleCarControlResponse(false, '控制返回报文解析失败', '指令执行出错');
    } catch (e) {
      // 处理其他未预料的异常
      LogManager().debug("发生未知错误：$e");
      setBleCarControlResponse(false, "发生未知错误：$e", '指令执行出错');
    }
  }

  /// 蓝牙近控控制方法。
  ///
  /// 该方法根据 [serviceBleSkipTarget] 指定的控制方法标识，使用 [params] 参数执行相应的控制操作，
  /// 并在完成后通过 [callback] 返回结果。
  ///
  /// [serviceBleSkipTarget]：控制方法标识。
  /// [params]：方法参数。
  /// [callback]：结果回调函数。
  void sendCommandWithServiceBleSkipTarget(
      String serviceBleSkipTarget,
      Map<String, dynamic> params,
      BleCarControlResponseCallback callback,
      ) {

    _callbackForBleCarControlResponse = callback;
    LogManager().debug('[BLEKEY]===serviceBleSkipTarget:$serviceBleSkipTarget params:$params===');
    if (serviceBleSkipTarget == kBtCarLock) {
      if (params.containsKey('btParam')) {
        int? btParamValue = int.tryParse(params['btParam']);
        if (btParamValue == 0) {
          sendLockAllRequest();
          bleResponseLoading();
        } else if (btParamValue == 1) {
          sendUnlockAllRequest();
          bleResponseLoading();
        } else {
          setBleCarControlResponse(
            false,
            '解闭锁控制参数有误',
            '参数有误',
          );
        }
      } else {
        setBleCarControlResponse(
          false,
          '解闭锁控制参数为空',
          '缺少参数',
        );
      }
    } else {
      setBleCarControlResponse(
        false,
        'App版本过低 暂不支持该功能',
        '未知功能',
      );
    }
  }

  /// 发送开锁指令。
  ///
  /// 该方法生成解锁所需的指令，并通过 BLE 写入到设备，实现解锁操作。
  void sendUnlockAllRequest() async {

    // 生成随机数。
    int randomData = Random.secure().nextInt(1 << 32);
    String randomDataHex = randomData.toRadixString(16).toUpperCase();

    // 获取控制码。
    String controlData = ControlCodeManager.getUnlockAllCode();

    // 创建控制请求帧。
    AppControlRequestFrame controlRequest = AppControlRequestFrame.withRollData(
      rollData: _bleKeyModel!.rollData,
      randomData: randomDataHex,
      bleKey: _bleKeyModel!.keyIdHex,
      controlData: controlData,
      key: _bleKeyModel!.controlAes128Key ?? '',
    );

    Uint8List? encryptedData = controlRequest.encryptedData;
    if (encryptedData == null || _controlRequestCharacteristic == null) return;

    // 写入数据到特征。
    await _controlRequestCharacteristic!.write(
      encryptedData,
      withoutResponse: false,
      allowLongWrite: true,
    );
  }

  /// 发送闭锁指令。
  ///
  /// 该方法生成锁车所需的指令，并通过 BLE 写入到设备，实现锁车操作。
  void sendLockAllRequest() async {
    // 生成随机数。
    int randomData = Random.secure().nextInt(1 << 32);
    String randomDataHex = randomData.toRadixString(16).toUpperCase();

    // 获取控制码。
    String controlData = ControlCodeManager.getLockAllCode();

    // 创建控制请求帧。
    AppControlRequestFrame controlRequest = AppControlRequestFrame.withRollData(
      rollData: _bleKeyModel!.rollData,
      randomData: randomDataHex,
      bleKey: _bleKeyModel!.keyIdHex,
      controlData: controlData,
      key: _bleKeyModel!.controlAes128Key ?? '',
    );

    Uint8List? encryptedData = controlRequest.encryptedData;
    if (encryptedData == null || _controlRequestCharacteristic == null) return;

    // 写入数据到特征。
    await _controlRequestCharacteristic!.write(
      encryptedData,
      withoutResponse: false,
      allowLongWrite: true,
    );
  }

  /// 设置泊车信息回调。
  /// 
  void setRemoteParkCtrlResponseCallBack (BleRemoteParkCtrlResponseCallback? callback , String key) {
    _callbackForRemoteParkCtrlResponse = callback;
    _responseCallbackKey = key;
  }

  /// 释放泊车信息回调。
  void releaseRemoteParkCtrlResponseCallBack (String key) {
    if (_responseCallbackKey == key) {
      _callbackForRemoteParkCtrlResponse = null;
    }
  }


  /// 记忆泊车指令。
  ///
  /// 该方法生成记忆泊车控制所需的指令，并通过 BLE 写入到设备，实现记忆泊车
  void sendMemoryParkControlRequest(MemoryParkControl controlType) async {
    // 生成随机数。
    int randomData = Random.secure().nextInt(1 << 32);
    String randomDataHex = randomData.toRadixString(16).toUpperCase();

    // 获取控制码。
    String controlData =
        ControlCodeManager.getMemoryParkControlCode(controlType);

    // 创建控制请求帧。
    AppControlRequestFrame controlRequest =
        AppControlRequestFrame.withServiceId(
            serviceId: '40E5',
            subfunction: '0001',
            randomData: randomDataHex,
            bleKey: _bleKeyModel!.keyIdHex,
            controlData: controlData,
            key: _bleKeyModel!.controlAes128Key ?? '');

    Uint8List? encryptedData = controlRequest.encryptedData;
    if (encryptedData == null || _controlRequestCharacteristic == null) return;

    // 写入数据到特征。
    await _controlRequestCharacteristic!.write(
      encryptedData,
      withoutResponse: false,
      allowLongWrite: true,
    );
  }

  /// 一键泊出选择方向指令
  ///
  /// 该方法生成一键泊出选择方向所需的指令，并通过 BLE 写入到设备，实现出库选择方向。
  void sendParkOutDirectionControlRequest(ParkOutDirectionControl controlType) async {
    // 生成随机数。
    int randomData = Random.secure().nextInt(1 << 32);
    String randomDataHex = randomData.toRadixString(16).toUpperCase();

    // 获取控制码。
    String controlData =
        ControlCodeManager.getRemoteParkOutDirectionControlCode(controlType);

    // 创建控制请求帧。
    AppControlRequestFrame controlRequest =
        AppControlRequestFrame.withServiceId(
            serviceId: '40E5',
            subfunction: '0001',
            randomData: randomDataHex,
            bleKey: _bleKeyModel!.keyIdHex,
            controlData: controlData,
            key: _bleKeyModel!.controlAes128Key ?? '');

    Uint8List? encryptedData = controlRequest.encryptedData;
    if (encryptedData == null || _controlRequestCharacteristic == null) return;

    // 写入数据到特征。
    await _controlRequestCharacteristic!.write(
      encryptedData,
      withoutResponse: false,
      allowLongWrite: true,
    );
  }

  /// 新手动泊车(直线行驶)操作指令
  ///
  /// 该方法生成手动泊车(直线行驶)所需的指令，并通过 BLE 写入到设备，实现手动泊车。
  void sendStraightControlRequest(ManualDirection controlType) async {
    // 生成随机数。
    int randomData = Random.secure().nextInt(1 << 32);
    String randomDataHex = randomData.toRadixString(16).toUpperCase();

    // 获取控制码。
    String controlData =
        ControlCodeManager.getManualParkControlCode(controlType);

    // 创建控制请求帧。
    AppControlRequestFrame controlRequest =
        AppControlRequestFrame.withServiceId(
            serviceId: '40E5',
            subfunction: '0001',
            randomData: randomDataHex,
            bleKey: _bleKeyModel!.keyIdHex,
            controlData: controlData,
            key: _bleKeyModel!.controlAes128Key ?? '');

    Uint8List? encryptedData = controlRequest.encryptedData;
    if (encryptedData == null || _controlRequestCharacteristic == null) return;

    // 写入数据到特征。
    await _controlRequestCharacteristic!.write(
      encryptedData,
      withoutResponse: false,
      allowLongWrite: true,
    );
  }
  
  /// 启动 BLE 响应加载计时器。
  ///
  /// 该方法在发送指令后启动一个 15 秒的定时器，如果在时间内没有收到 BLE 响应，
  /// 则认为操作超时，并返回错误信息。
  void bleResponseLoading() {
    _isBleResponsePushed = false;

    // 如果已有定时器，先取消。
    bleResponseLoadingTimer?.cancel();

    // 启动一个 15 秒的定时器。
    bleResponseLoadingTimer = Timer(Duration(seconds: 15), () {
      if (!_isBleResponsePushed) {
        setBleCarControlResponse(
          false,
          '蓝牙指令执行超时 请稍候重试',
          '指令执行超时',
        );
      }
    });
  }

  /// 设置蓝牙控制响应结果。
  ///
  /// 该方法在操作完成后调用，通知调用者操作是否成功，并传递错误信息（如果有）。
  ///
  /// [isSuccess]：操作是否成功。
  /// [responseErrorDomain]：错误域，描述错误类型。
  /// [responseErrorDescription]：错误描述，详细说明错误信息。
  void setBleCarControlResponse(
      bool isSuccess,
      String responseErrorDomain,
      String responseErrorDescription,
      ) {
    if (_callbackForBleCarControlResponse != null) {
      if (!isSuccess) {
        // 创建错误对象。
        Exception responseError = BleControlException(responseErrorDomain);
        _callbackForBleCarControlResponse!(responseError, isSuccess);
      } else {
        _callbackForBleCarControlResponse!(null, isSuccess);
      }
    }
  }

  /// 获取特征的属性字符串
  String _getCharacteristicProperties(BluetoothCharacteristic characteristic) {
    List<String> properties = [];
    if (characteristic.properties.broadcast) properties.add('广播');
    if (characteristic.properties.read) properties.add('读');
    if (characteristic.properties.writeWithoutResponse) properties.add('无响应写');
    if (characteristic.properties.write) properties.add('写');
    if (characteristic.properties.notify) properties.add('通知');
    if (characteristic.properties.indicate) properties.add('指示');
    if (characteristic.properties.authenticatedSignedWrites) properties.add('认证签名写');
    if (characteristic.properties.extendedProperties) properties.add('扩展属性');
    if (characteristic.properties.notifyEncryptionRequired) properties.add('加密通知');
    if (characteristic.properties.indicateEncryptionRequired) properties.add('加密指示');
    return properties.join(', ');
  }

  /// 格式化特征值
  String _formatCharacteristicValue(List<int> value) {
    if (value.isEmpty) return '空';

    // 尝试将值解释为 UTF-8 字符串
    try {
      String utf8String = utf8.decode(value);
      return 'UTF-8: $utf8String';
    } catch (_) {
      // 如果不是有效的 UTF-8，则显示十六进制
      return 'HEX: ${value.map((e) => e.toRadixString(16).padLeft(2, '0')).join(' ')}';
    }
  }


  /// 检查蓝牙是否已启用
  ///
  /// 返回一个 Future<bool>，表示蓝牙是否已启用
  Future<bool> isBluetoothEnabled() async {
    return await FlutterBluePlus.adapterState.first == BluetoothAdapterState.on;
  }

  /// 请求启用蓝牙
  ///
  /// 注意：此方法在iOS上不起作用，iOS需要用户手动开启蓝牙
  /// 返回一个 Future，表示请求操作已完成
  Future<void> requestBluetoothEnable() async {
    if (await isBluetoothEnabled()) return;
    await FlutterBluePlus.turnOn();
  }

  /// 释放资源
  ///
  /// 在不再需要 BleManager 时调用此方法
  void dispose() {
    _statusController.close();
    _stopListeningToDeviceState();
  }


  // 辅助方法：在字符串前补零至指定长度
  String _addZero(String str, int length) {
    return str.padLeft(length, '0');
  }

  ///获取蓝牙自动连接开关状态,true表示自动连接是开启着的
  bool canAutoConnectBle() {
    String bluetoothKeyConnectMark = SpUtil().getString(BLUE_TOOTH_KEY_CONNECT_MARK);
    LogManager().debug('蓝牙自动连接开关状态,bluetoothKeyConnectMark:$bluetoothKeyConnectMark');
    return bluetoothKeyConnectMark =='1';
  }
}

class BleServiceConstants {
  // 鉴权服务的UUID
  static const String authServiceUUID = "181A";
  // 用于写入数据的鉴权特征UUID
  static const String authWriteCharacteristicUUID = "2A6E";
  // 用于读取数据的鉴权特征UUID
  static const String authReadCharacteristicUUID = "2A6F";

  // 控制服务的UUID
  static const String controlServiceUUID = "182A";
  // 用于写入控制指令的特征UUID
  static const String controlWriteCharacteristicUUID = "2A7E";
  // 用于读取控制反馈的特征UUID
  static const String controlReadCharacteristicUUID = "2A7F";
}