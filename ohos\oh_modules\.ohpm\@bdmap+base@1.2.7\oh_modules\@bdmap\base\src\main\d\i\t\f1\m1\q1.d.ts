import { DeviceMetadata } from "../../h1/o1"; import { PreferencesManager } from "../j1"; import { RuntimeDataManager } from "../k1"; import { BaseInfoManager } from "./n1"; export declare class IdentityInfoManager extends BaseInfoManager<DeviceMetadata.IdentityInfo> { protected _preferencesManager: PreferencesManager<DeviceMetadata.IdentityInfo>; protected _runtimeDataManager: RuntimeDataManager<DeviceMetadata.IdentityInfo>; } 