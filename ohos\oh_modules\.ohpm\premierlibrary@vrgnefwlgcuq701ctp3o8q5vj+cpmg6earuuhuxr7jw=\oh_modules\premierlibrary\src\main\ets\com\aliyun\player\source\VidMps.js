import { VidSourceBase } from './VidSourceBase';
export class VidMps extends VidSourceBase {
    constructor() {
        super();
        this.nativeGetMediaId = () => {
            return this.mMediaId;
        };
        this.nativeSetMediaId = (u38) => {
            this.mMediaId = u38;
        };
        this.nativeGetAccessKeyId = () => {
            return this.mAccessKeyId;
        };
        this.nativeSetAccessKeyId = (t38) => {
            this.mAccessKeyId = t38;
        };
        this.nativeGetAccessKeySecret = () => {
            return this.mAccessKeySecret;
        };
        this.nativeSetAccessKeySecret = (s38) => {
            this.mAccessKeySecret = s38;
        };
        this.nativeGetSecurityToken = () => {
            return this.mSecurityToken;
        };
        this.nativeSetSecurityToken = (r38) => {
            this.mSecurityToken = r38;
        };
        this.nativeGetRegion = () => {
            return this.mRegion;
        };
        this.nativeSetRegion = (q38) => {
            this.mRegion = q38;
        };
        this.nativeGetPlayDomain = () => {
            return this.mPlayDomain;
        };
        this.nativeSetPlayDomain = (p38) => {
            this.mPlayDomain = p38;
        };
        this.nativeGetAuthInfo = () => {
            return this.mAuthInfo;
        };
        this.nativeSetAuthInfo = (o38) => {
            this.mAuthInfo = o38;
        };
        this.nativeGetHlsUriToken = () => {
            return this.mHlsUriToken;
        };
        this.nativeSetHlsUriToken = (n38) => {
            this.mHlsUriToken = n38;
        };
        this.mMediaId = "";
        this.mAccessKeyId = "";
        this.mAccessKeySecret = "";
        this.mSecurityToken = "";
        this.mRegion = "";
        this.mPlayDomain = "";
        this.mAuthInfo = "";
        this.mHlsUriToken = "";
    }
    setQuality(v37, w37) {
        this.mQuality = v37;
        this.mForceQuality = w37;
    }
    getMediaId() {
        return this.mMediaId;
    }
    setMediaId(u37) {
        this.mMediaId = u37;
    }
    getAccessKeyId() {
        return this.mAccessKeyId;
    }
    setAccessKeyId(t37) {
        this.mAccessKeyId = t37;
    }
    getAccessKeySecret() {
        return this.mAccessKeySecret;
    }
    setAccessKeySecret(s37) {
        this.mAccessKeySecret = s37;
    }
    getSecurityToken() {
        return this.mSecurityToken;
    }
    setSecurityToken(r37) {
        this.mSecurityToken = r37;
    }
    getRegion() {
        return this.mRegion;
    }
    setRegion(q37) {
        this.mRegion = q37;
    }
    getPlayDomain() {
        return this.mPlayDomain;
    }
    setPlayDomain(p37) {
        this.mPlayDomain = p37;
    }
    getAuthInfo() {
        return this.mAuthInfo;
    }
    setAuthInfo(o37) {
        this.mAuthInfo = o37;
    }
    getHlsUriToken() {
        return this.mHlsUriToken;
    }
    setHlsUriToken(n37) {
        this.mHlsUriToken = n37;
    }
}
