import { CameraProperties } from '../../CameraProperties';
import { CameraFeature } from '../CameraFeature';
import { NoiseReductionMode } from './NoiseReductionMode';
export declare class NoiseReductionFeature extends CameraFeature<NoiseReductionMode> {
    private currentSetting;
    constructor(cameraProperties: CameraProperties);
    getDebugName(): string;
    getValue(): NoiseReductionMode;
    setValue(value: NoiseReductionMode): void;
    checkIsSupported(): boolean;
}
