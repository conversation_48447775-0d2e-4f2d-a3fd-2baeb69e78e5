{"lockVersion": "1.0", "settings": {"resolveConflict": true, "resolveConflictStrict": false, "installAll": true}, "overrides": {"@ohos/flutter_ohos": "file:D:\\wulingFlutter\\wuling-flutter-app\\ohos\\har\\flutter.har", "shared_preferences_ohos": "file:D:\\wulingFlutter\\wuling-flutter-app\\ohos\\har\\shared_preferences_ohos.har", "url_launcher_ohos": "file:D:\\wulingFlutter\\wuling-flutter-app\\ohos\\har\\url_launcher_ohos.har", "webview_flutter_ohos": "file:D:\\wulingFlutter\\wuling-flutter-app\\ohos\\har\\webview_flutter_ohos.har", "path_provider_ohos": "file:D:\\wulingFlutter\\wuling-flutter-app\\ohos\\har\\path_provider_ohos.har", "@ohos/flutter_module": "file:D:\\wulingFlutter\\wuling-flutter-app\\ohos\\entry", "image_picker_ohos": "file:D:\\wulingFlutter\\wuling-flutter-app\\ohos\\har\\image_picker_ohos.har", "permission_handler_ohos": "file:D:\\wulingFlutter\\wuling-flutter-app\\ohos\\har\\permission_handler_ohos.har", "device_info_plus": "file:D:\\wulingFlutter\\wuling-flutter-app\\ohos\\har\\device_info_plus.har", "package_info_plus": "file:D:\\wulingFlutter\\wuling-flutter-app\\ohos\\har\\package_info_plus.har", "flutter_blue_plus_ohos": "file:D:\\wulingFlutter\\wuling-flutter-app\\ohos\\har\\flutter_blue_plus_ohos.har", "iamgeqr_flutter_plugin": "file:D:\\wulingFlutter\\wuling-flutter-app\\ohos\\har\\iamgeqr_flutter_plugin.har", "connectivity_plus": "file:D:\\wulingFlutter\\wuling-flutter-app\\ohos\\har\\connectivity_plus.har", "mobile_scanner": "file:D:\\wulingFlutter\\wuling-flutter-app\\ohos\\har\\mobile_scanner.har", "fluwx": "file:D:\\wulingFlutter\\wuling-flutter-app\\ohos\\har\\fluwx.har", "open_app_settings": "file:D:\\wulingFlutter\\wuling-flutter-app\\ohos\\har\\open_app_settings.har", "flutter_blue_plus": "file:D:\\wulingFlutter\\wuling-flutter-app\\ohos\\har\\flutter_blue_plus.har", "camera_ohos": "file:D:\\wulingFlutter\\wuling-flutter-app\\ohos\\har\\camera_ohos.har", "video_compress": "file:D:\\wulingFlutter\\wuling-flutter-app\\ohos\\har\\video_compress.har"}, "overrideDependencyMap": {}, "modules": {".": {"name": "", "dependencies": {"@ohos/flutter_ohos": {"specifier": "file:har/flutter.har", "version": "file:har/flutter.har"}, "@tencent/wechat_open_sdk": {"specifier": "1.0.14", "version": "1.0.14"}, "@free/global": {"specifier": "1.0.3", "version": "1.0.3"}, "@rongcloud/imkit": {"specifier": "^1.5.0", "version": "1.7.1"}, "@rongcloud/imlib": {"specifier": "^1.5.0", "version": "1.7.1"}, "@ohos/mqtt": {"specifier": "^2.0.22", "version": "2.0.23"}}, "devDependencies": {"@ohos/hypium": {"specifier": "1.0.18", "version": "1.0.18"}}, "dynamicDependencies": {}, "maskedByOverrideDependencyMap": false}, "entry": {"name": "entry", "dependencies": {"premierlibrary": {"specifier": "file:entry/libs/premierlibrary.har", "version": "file:entry/libs/premierlibrary.har"}, "shared_preferences_ohos": {"specifier": "file:har/shared_preferences_ohos.har", "version": "file:har/shared_preferences_ohos.har"}, "webview_flutter_ohos": {"specifier": "file:har/webview_flutter_ohos.har", "version": "file:har/webview_flutter_ohos.har"}, "path_provider_ohos": {"specifier": "file:har/path_provider_ohos.har", "version": "file:har/path_provider_ohos.har"}, "url_launcher_ohos": {"specifier": "file:har/url_launcher_ohos.har", "version": "file:har/url_launcher_ohos.har"}, "image_picker_ohos": {"specifier": "file:har/image_picker_ohos.har", "version": "file:har/image_picker_ohos.har"}, "permission_handler_ohos": {"specifier": "file:har/permission_handler_ohos.har", "version": "file:har/permission_handler_ohos.har"}, "device_info_plus": {"specifier": "file:har/device_info_plus.har", "version": "file:har/device_info_plus.har"}, "package_info_plus": {"specifier": "file:har/package_info_plus.har", "version": "file:har/package_info_plus.har"}, "flutter_blue_plus_ohos": {"specifier": "file:har/flutter_blue_plus_ohos.har", "version": "file:har/flutter_blue_plus_ohos.har"}, "flutter_blue_plus": {"specifier": "file:har/flutter_blue_plus.har", "version": "file:har/flutter_blue_plus.har"}, "iamgeqr_flutter_plugin": {"specifier": "file:har/iamgeqr_flutter_plugin.har", "version": "file:har/iamgeqr_flutter_plugin.har"}, "connectivity_plus": {"specifier": "file:har/connectivity_plus.har", "version": "file:har/connectivity_plus.har"}, "mobile_scanner": {"specifier": "file:har/mobile_scanner.har", "version": "file:har/mobile_scanner.har"}, "@pura/harmony-utils": {"specifier": "1.2.4", "version": "1.2.4"}, "@bdmap/base": {"specifier": "1.2.7", "version": "1.2.7"}, "@bdmap/search": {"specifier": "1.2.7", "version": "1.2.7"}, "@bdmap/map": {"specifier": "1.2.7", "version": "1.2.7"}, "open_app_settings": {"specifier": "file:har/open_app_settings.har", "version": "file:har/open_app_settings.har"}, "fluwx": {"specifier": "file:har/fluwx.har", "version": "file:har/fluwx.har"}, "@tencent/wechat_open_sdk": {"specifier": "^1.0.11", "version": "1.0.14"}, "camera_ohos": {"specifier": "file:har/camera_ohos.har", "version": "file:har/camera_ohos.har"}}, "devDependencies": {}, "dynamicDependencies": {}, "maskedByOverrideDependencyMap": false}}, "packages": {"@tencent/wechat_open_sdk@1.0.14": {"integrity": "sha512-qTA/XkKqBp5Bji6D4ePCnxdGpaDvzcqeI/vK6FRUwdBIlO9T/o0xuO1akCMCEB9c6qshIUAYqyRlHYmkRIkm4g==", "storePath": "oh_modules/.ohpm/@tencent+wechat_open_sdk@1.0.14", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@free/global@1.0.3": {"integrity": "sha512-WBrNWmjFkDhQP4ZfegyFBm5VC5CXmH2tfRMfBG9HtyO5v0t0t80c3cUMNm7Uq9ahYV9jpPt5BTN2IMpoB4wM9w==", "storePath": "oh_modules/.ohpm/@free+global@1.0.3", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@rongcloud/imkit@1.7.1": {"integrity": "sha512-Jr57bo+OInjWMnF6lKq6qbQm5ofdLoaHlwBJFnFO28606tUjMLYUoEzITEwEODenSyJIoUGHpRjwgGpGJqcNlA==", "storePath": "oh_modules/.ohpm/@rongcloud+imkit@1.7.1", "dependencies": {"@rongcloud/imlib": "1.7.1", "@charles/amrnbconverter": "1.0.1"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@ohos/hypium@1.0.18": {"integrity": "sha512-RGe/iLGdeywdQilMWZsHKUoiE9OJ+9QxQsorF92R2ImLNVHVhbpSePNITGpW7TnvLgOIP/jscOqfIOhk6X7XRQ==", "storePath": "oh_modules/.ohpm/@ohos+hypium@1.0.18", "dependencies": {}, "dynamicDependencies": {}, "dev": true, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@ohos/flutter_ohos@file:har/flutter.har": {"storePath": "oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@rongcloud/imlib@1.7.1": {"integrity": "sha512-P4u6gS277VbqAy7mHyd1E7Dd1hTq6vgkNDhfFyVcbjS8OrCqLRRLEUsLfBMyk5CtodR2U7aSF4MyPCPh6BlIOA==", "storePath": "oh_modules/.ohpm/@rongcloud+imlib@1.7.1", "dependencies": {"librongimlib.so": "oh_modules/.ohpm/@rongcloud+imlib@1.7.1/oh_modules/@rongcloud/imlib/src/main/cpp/types/librongimlib"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@ohos/mqtt@2.0.23": {"integrity": "sha512-YtI31ruqc+UMU1k15KEbkPrVCmN2yigjL5a4lJrgBZKM9HObuZTu7n9N0ftoMZm1APoG3Cqt01fozb1SkN34Xg==", "storePath": "oh_modules/.ohpm/@ohos+mqtt@2.0.23", "dependencies": {"libmqttasync.so": "oh_modules/.ohpm/@ohos+mqtt@2.0.23/oh_modules/@ohos/mqtt/src/main/cpp/types/libmqttasync"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@charles/amrnbconverter@1.0.1": {"integrity": "sha512-LIj24/k9BFUm9l7PTuvhTOnELax3GbtOAcJnXMQMCGCKmXmU6HZ/8IVok4je74kAyLYIVvvak2DzUOOdzWoaEw==", "storePath": "oh_modules/.ohpm/@charles+amrnbconverter@1.0.1", "dependencies": {"libamrconverter.so": "oh_modules/.ohpm/@charles+amrnbconverter@1.0.1/oh_modules/@charles/amrnbconverter/src/main/cpp/types/libamrconverter"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "librongimlib.so@file:oh_modules/.ohpm/@rongcloud+imlib@1.7.1/oh_modules/@rongcloud/imlib/src/main/cpp/types/librongimlib": {"storePath": "oh_modules/.ohpm/@rongcloud+imlib@1.7.1/oh_modules/@rongcloud/imlib/src/main/cpp/types/librongimlib", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "libmqttasync.so@file:oh_modules/.ohpm/@ohos+mqtt@2.0.23/oh_modules/@ohos/mqtt/src/main/cpp/types/libmqttasync": {"storePath": "oh_modules/.ohpm/@ohos+mqtt@2.0.23/oh_modules/@ohos/mqtt/src/main/cpp/types/libmqttasync", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "libamrconverter.so@file:oh_modules/.ohpm/@charles+amrnbconverter@1.0.1/oh_modules/@charles/amrnbconverter/src/main/cpp/types/libamrconverter": {"storePath": "oh_modules/.ohpm/@charles+amrnbconverter@1.0.1/oh_modules/@charles/amrnbconverter/src/main/cpp/types/libamrconverter", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@pura/harmony-utils@1.2.4": {"integrity": "sha512-P17M+h5eonalgtEd3Xe+a85PPZilguRc8uoggEt9+AjrBdAasQddf6L/W7NRubSBCiDf4k2vnVRtgeQqKsWrmA==", "storePath": "oh_modules/.ohpm/@pura+harmony-utils@1.2.4", "dependencies": {"class-transformer": "0.5.1"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@bdmap/search@1.2.7": {"integrity": "sha512-aO5sQq1Sv93CQlZt3l0Xq37SZoJLaKJhr6pLQd/kH/zA7VTvZeVILl1N5loyXMvXR9tOFQWqxtY6Lm+B/7gK8Q==", "storePath": "oh_modules/.ohpm/@bdmap+search@1.2.7", "dependencies": {"@bdmap/base": "1.2.7"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "flutter_blue_plus_ohos@file:har/flutter_blue_plus_ohos.har": {"storePath": "oh_modules/.ohpm/flutter_blue_plus_ohos@4df70qmo49oaztang7dsivr+mi1tpjfgwsjngar+e2u=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "class-transformer@0.5.1": {"integrity": "sha512-SQa1Ws6hUbfC98vKGxZH3KFY0Y1lm5Zm0SY8XX9zbK7FJCyVEac3ATW0RIpwzW+oOfmHE5PMPufDG9hCfoEOMw==", "storePath": "oh_modules/.ohpm/class-transformer@0.5.1", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@bdmap/base@1.2.7": {"integrity": "sha512-9NtZHtlzKGweVcYpHJ5RT5dMAjta+z3akqDQr2BHvxM5HI14/G7P6/Q5oNKAjXkM8KLg+p9jYisnCKLM9cdwWg==", "storePath": "oh_modules/.ohpm/@bdmap+base@1.2.7", "dependencies": {"@types/libbaidumapsdk_base_for_js_v1_0_0.so": "oh_modules/.ohpm/@bdmap+base@1.2.7/oh_modules/@bdmap/base/src/main/cpp/type", "@bdmap/verify": "1.0.2"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@bdmap/verify@1.0.2": {"integrity": "sha512-QyOqj/MmAoHuQZQCLAbFl7jYYVRSdqd2mLx4ajM1xI1ZFfZ0tkugf/K7LwdFwDDsCtv+U7bjGOYVUx38zBJrvQ==", "storePath": "oh_modules/.ohpm/@bdmap+verify@1.0.2", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@bdmap/map@1.2.7": {"integrity": "sha512-dxP3vn91ssbKb8shy0JoFSiYHFWyEHFFZXH1fM2z7ZNLu9GT5DeoHGw8bAehtwoEoencK4mmF5eOQ4v8D3b9ew==", "storePath": "oh_modules/.ohpm/@bdmap+map@1.2.7", "dependencies": {"@types/libbaidumapsdk_map_for_js_v1_0_0.so": "oh_modules/.ohpm/@bdmap+map@1.2.7/oh_modules/@bdmap/map/src/main/cpp/type", "@bdmap/base": "1.2.7"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "premierlibrary@file:entry/libs/premierlibrary.har": {"storePath": "oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=", "dependencies": {"libpremierlibrary.so": "oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=/oh_modules/premierlibrary/src/main/cpp/types/libpremierlibrary", "@acpm/acpm_ohos_pc": "oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=/oh_modules/premierlibrary/libs/acpm_ohos_pc.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@acpm/acpm_ohos_pc@file:oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=/oh_modules/premierlibrary/libs/acpm_ohos_pc.har": {"storePath": "oh_modules/.ohpm/@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=", "dependencies": {"@acpm/aio_crashsdk": "oh_modules/.ohpm/@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=/oh_modules/@acpm/acpm_ohos_pc/hars/aio_crashsdk.har", "@acpm/aio_util": "oh_modules/.ohpm/@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=/oh_modules/@acpm/acpm_ohos_pc/hars/aio_util.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "url_launcher_ohos@file:har/url_launcher_ohos.har": {"storePath": "oh_modules/.ohpm/url_launcher_ohos@wlne140wohx1vkd8ccrurbfnnoly1pw5l2t4tlxnw60=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "shared_preferences_ohos@file:har/shared_preferences_ohos.har": {"storePath": "oh_modules/.ohpm/shared_preferences_ohos@nadksbxq1mqjmuixnx2pjjqabwptj2cvju+p83h8xgu=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "image_picker_ohos@file:har/image_picker_ohos.har": {"storePath": "oh_modules/.ohpm/image_picker_ohos@ayxn+24xl+l1dzv0bylaz9kq36nfp36xzdpkk2rtp+a=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "iamgeqr_flutter_plugin@file:har/iamgeqr_flutter_plugin.har": {"storePath": "oh_modules/.ohpm/iamgeqr_flutter_plugin@1vjbuscikzvjpfjjpnjx0g8lpzpk+dylqndaetdipbe=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "fluwx@file:har/fluwx.har": {"storePath": "oh_modules/.ohpm/fluwx@0luy3guwyen9n4kt97wftqotzdsl407ewqtntkbvyq0=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har", "@tencent/wechat_open_sdk": "1.0.14"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "path_provider_ohos@file:har/path_provider_ohos.har": {"storePath": "oh_modules/.ohpm/path_provider_ohos@rz08ypf+jhpfa5j6qopves7vsk91bavu8lb+8wfkzfa=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "permission_handler_ohos@file:har/permission_handler_ohos.har": {"storePath": "oh_modules/.ohpm/permission_handler_ohos@v1f9zvgtzwiwps6urt4cdkc0xmr4k3+ghajbhe99nii=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "flutter_blue_plus@file:har/flutter_blue_plus.har": {"storePath": "oh_modules/.ohpm/flutter_blue_plus@toon5rvyx27suvcikxe8x+l7wbqukliqqpr24doow+o=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "package_info_plus@file:har/package_info_plus.har": {"storePath": "oh_modules/.ohpm/package_info_plus@oddkx3spdpyogivhls6nikfvehqoth7jfcfacihwabg=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "webview_flutter_ohos@file:har/webview_flutter_ohos.har": {"storePath": "oh_modules/.ohpm/webview_flutter_ohos@bfdx0tympjxsspo60mgoonn+aa7mmwytsvuwcpok25o=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "connectivity_plus@file:har/connectivity_plus.har": {"storePath": "oh_modules/.ohpm/connectivity_plus@kqukugwl2nsdoqs00mqdcainj8gozua8ux6peiraxwi=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "device_info_plus@file:har/device_info_plus.har": {"storePath": "oh_modules/.ohpm/device_info_plus@v+2oszykcfti10hnkmmvwgy+solmmbsrwc3c4yk1uuo=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "open_app_settings@file:har/open_app_settings.har": {"storePath": "oh_modules/.ohpm/open_app_settings@yuq0uoolkhzidhegjyos7acnrx5+ddlclpefshq4g0g=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "mobile_scanner@file:har/mobile_scanner.har": {"storePath": "oh_modules/.ohpm/mobile_scanner@fa18s1hwupccovgme0u7zrnan0ixc+q+mhbg2xvg46m=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@acpm/aio_crashsdk@file:oh_modules/.ohpm/@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=/oh_modules/@acpm/acpm_ohos_pc/hars/aio_crashsdk.har": {"storePath": "oh_modules/.ohpm/@acpm+aio_crashsdk@rynukgn6cor9bomrriugyxyd1z7zerm+gebyzjm0l0s=", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@acpm/aio_util@file:oh_modules/.ohpm/@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=/oh_modules/@acpm/acpm_ohos_pc/hars/aio_util.har": {"storePath": "oh_modules/.ohpm/@acpm+aio_util@m7v9iv6jbihoz+rwxvd+y3p2msbrx6lznc1xn3kcclu=", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "camera_ohos@file:har/camera_ohos.har": {"storePath": "oh_modules/.ohpm/camera_ohos@eoyi02pyjbzogdkwrnb0ry+85medqnonnkge8wnwk+0=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@types/libbaidumapsdk_base_for_js_v1_0_0.so@file:oh_modules/.ohpm/@bdmap+base@1.2.7/oh_modules/@bdmap/base/src/main/cpp/type": {"storePath": "oh_modules/.ohpm/@bdmap+base@1.2.7/oh_modules/@bdmap/base/src/main/cpp/type", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@types/libbaidumapsdk_map_for_js_v1_0_0.so@file:oh_modules/.ohpm/@bdmap+map@1.2.7/oh_modules/@bdmap/map/src/main/cpp/type": {"storePath": "oh_modules/.ohpm/@bdmap+map@1.2.7/oh_modules/@bdmap/map/src/main/cpp/type", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "libpremierlibrary.so@file:oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=/oh_modules/premierlibrary/src/main/cpp/types/libpremierlibrary": {"storePath": "oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=/oh_modules/premierlibrary/src/main/cpp/types/libpremierlibrary", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}}}