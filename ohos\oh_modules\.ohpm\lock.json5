{"lockVersion": "1.0", "settings": {"resolveConflict": true, "resolveConflictStrict": false, "installAll": true}, "overrides": {"@ohos/flutter_ohos": "file:D:\\wulingFlutter\\wuling-flutter-app\\ohos\\har\\flutter.har", "shared_preferences_ohos": "file:D:\\PUB\\git\\flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c\\packages\\shared_preferences\\shared_preferences_ohos\\ohos", "url_launcher_ohos": "file:D:\\PUB\\git\\flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c\\packages\\url_launcher\\url_launcher_ohos\\ohos", "webview_flutter_ohos": "file:D:\\PUB\\git\\flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c\\packages\\webview_flutter\\webview_flutter_ohos\\ohos", "path_provider_ohos": "file:D:\\PUB\\git\\flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c\\packages\\path_provider\\path_provider_ohos\\ohos", "@ohos/flutter_module": "file:D:\\wulingFlutter\\wuling-flutter-app\\ohos\\entry", "image_picker_ohos": "file:D:\\PUB\\git\\flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c\\packages\\image_picker\\image_picker_ohos\\ohos", "permission_handler_ohos": "file:D:\\PUB\\git\\flutter_permission_handler-1e7c80f0720aff742d9f1e08017e1753bcad76ef\\permission_handler_ohos\\ohos", "device_info_plus": "file:D:\\PUB\\git\\flutter_plus_plugins-a1347adcca3a46346a6ddd127cebcec9970cad6c\\packages\\device_info_plus\\device_info_plus\\ohos", "package_info_plus": "file:D:\\PUB\\git\\flutter_plus_plugins-452bfb40465722552f31cf78cde60f99cc691e36\\packages\\package_info_plus\\package_info_plus\\ohos", "flutter_blue_plus_ohos": "file:D:\\wulingFlutter\\wuling-flutter-app\\ohos\\har\\flutter_blue_plus_ohos.har", "iamgeqr_flutter_plugin": "file:D:\\PUB\\git\\flutter_image_scanner-b9f20a88ca1c738c700a15655b8fab4c305c4d32\\ohos", "connectivity_plus": "file:D:\\PUB\\git\\flutter_plus_plugins-8720286d1b4b664dbc1d87ba169125242d4f10fb\\packages\\connectivity_plus\\connectivity_plus\\ohos", "mobile_scanner": "file:D:\\PUB\\git\\fluttertpc_mobile_scanner-a50b8f5e377524f79dd143f1297e82d257ad53b1\\ohos", "fluwx": "file:D:\\PUB\\git\\fluwx-9fe4d9f1d3c05de5de18d25cc53852866d9c34d7\\ohos", "open_app_settings": "file:D:\\PUB\\git\\fluttertpc_open_app_settings-038c0dd8ffd45bcf84691d62cfff957748cd493a\\ohos", "flutter_blue_plus": "file:D:\\PUB\\git\\flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0\\ohos", "camera_ohos": "file:D:\\wulingFlutter\\wuling-flutter-app\\plugins\\camera_ohos\\ohos", "video_compress": "file:D:\\wulingFlutter\\wuling-flutter-app\\ohos\\har\\video_compress.har"}, "overrideDependencyMap": {}, "modules": {".": {"name": "", "dependencies": {"@ohos/flutter_ohos": {"specifier": "file:har/flutter.har", "version": "file:har/flutter.har"}, "@tencent/wechat_open_sdk": {"specifier": "1.0.14", "version": "1.0.14"}, "@free/global": {"specifier": "1.0.3", "version": "1.0.3"}, "@rongcloud/imkit": {"specifier": "^1.5.0", "version": "1.7.1"}, "@rongcloud/imlib": {"specifier": "^1.5.0", "version": "1.7.1"}, "@ohos/mqtt": {"specifier": "^2.0.22", "version": "2.0.23"}}, "devDependencies": {"@ohos/hypium": {"specifier": "1.0.18", "version": "1.0.18"}}, "dynamicDependencies": {}, "maskedByOverrideDependencyMap": false}, "../../../PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos": {"name": "flutter_blue_plus", "dependencies": {"@ohos/flutter_ohos": {"specifier": "file:../../../PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/har/flutter.har", "version": "file:har/flutter.har"}}, "devDependencies": {}, "dynamicDependencies": {}, "maskedByOverrideDependencyMap": false}, "../../../PUB/git/flutter_image_scanner-b9f20a88ca1c738c700a15655b8fab4c305c4d32/ohos": {"name": "iamgeqr_flutter_plugin", "dependencies": {"@ohos/flutter_ohos": {"specifier": "file:../../../PUB/git/flutter_image_scanner-b9f20a88ca1c738c700a15655b8fab4c305c4d32/ohos/har/flutter.har", "version": "file:har/flutter.har"}}, "devDependencies": {}, "dynamicDependencies": {}, "maskedByOverrideDependencyMap": false}, "../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/image_picker/image_picker_ohos/ohos": {"name": "image_picker_ohos", "dependencies": {"@ohos/flutter_ohos": {"specifier": "file:../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/image_picker/image_picker_ohos/ohos/libs/flutter.har", "version": "file:har/flutter.har"}}, "devDependencies": {}, "dynamicDependencies": {}, "maskedByOverrideDependencyMap": false}, "../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/path_provider/path_provider_ohos/ohos": {"name": "path_provider_ohos", "dependencies": {"@ohos/flutter_ohos": {"specifier": "file:../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/path_provider/path_provider_ohos/ohos/libs/flutter.har", "version": "file:har/flutter.har"}}, "devDependencies": {}, "dynamicDependencies": {}, "maskedByOverrideDependencyMap": false}, "../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/shared_preferences/shared_preferences_ohos/ohos": {"name": "shared_preferences_ohos", "dependencies": {"@ohos/flutter_ohos": {"specifier": "file:../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/shared_preferences/shared_preferences_ohos/ohos/libs/flutter.har", "version": "file:har/flutter.har"}}, "devDependencies": {}, "dynamicDependencies": {}, "maskedByOverrideDependencyMap": false}, "../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/url_launcher/url_launcher_ohos/ohos": {"name": "url_launcher_ohos", "dependencies": {"@ohos/flutter_ohos": {"specifier": "file:../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/url_launcher/url_launcher_ohos/ohos/har/flutter.har", "version": "file:har/flutter.har"}}, "devDependencies": {}, "dynamicDependencies": {}, "maskedByOverrideDependencyMap": false}, "../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/webview_flutter/webview_flutter_ohos/ohos": {"name": "webview_flutter_ohos", "dependencies": {"@ohos/flutter_ohos": {"specifier": "file:../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/webview_flutter/webview_flutter_ohos/ohos/libs/flutter.har", "version": "file:har/flutter.har"}}, "devDependencies": {}, "dynamicDependencies": {}, "maskedByOverrideDependencyMap": false}, "../../../PUB/git/flutter_permission_handler-1e7c80f0720aff742d9f1e08017e1753bcad76ef/permission_handler_ohos/ohos": {"name": "permission_handler_ohos", "dependencies": {"@ohos/flutter_ohos": {"specifier": "file:../../../PUB/git/flutter_permission_handler-1e7c80f0720aff742d9f1e08017e1753bcad76ef/permission_handler_ohos/ohos/har/flutter.har", "version": "file:har/flutter.har"}}, "devDependencies": {}, "dynamicDependencies": {}, "maskedByOverrideDependencyMap": false}, "../../../PUB/git/flutter_plus_plugins-452bfb40465722552f31cf78cde60f99cc691e36/packages/package_info_plus/package_info_plus/ohos": {"name": "package_info_plus", "dependencies": {"@ohos/flutter_ohos": {"specifier": "file:../../../PUB/git/flutter_plus_plugins-452bfb40465722552f31cf78cde60f99cc691e36/packages/package_info_plus/package_info_plus/ohos/libs/flutter.har", "version": "file:har/flutter.har"}}, "devDependencies": {}, "dynamicDependencies": {}, "maskedByOverrideDependencyMap": false}, "../../../PUB/git/flutter_plus_plugins-8720286d1b4b664dbc1d87ba169125242d4f10fb/packages/connectivity_plus/connectivity_plus/ohos": {"name": "connectivity_plus", "dependencies": {"@ohos/flutter_ohos": {"specifier": "file:../../../PUB/git/flutter_plus_plugins-8720286d1b4b664dbc1d87ba169125242d4f10fb/packages/connectivity_plus/connectivity_plus/ohos/libs/flutter_embedding.har", "version": "file:har/flutter.har"}}, "devDependencies": {}, "dynamicDependencies": {}, "maskedByOverrideDependencyMap": false}, "../../../PUB/git/flutter_plus_plugins-a1347adcca3a46346a6ddd127cebcec9970cad6c/packages/device_info_plus/device_info_plus/ohos": {"name": "device_info_plus", "dependencies": {"@ohos/flutter_ohos": {"specifier": "file:../../../PUB/git/flutter_plus_plugins-a1347adcca3a46346a6ddd127cebcec9970cad6c/packages/device_info_plus/device_info_plus/ohos/har/flutter.har", "version": "file:har/flutter.har"}}, "devDependencies": {}, "dynamicDependencies": {}, "maskedByOverrideDependencyMap": false}, "../../../PUB/git/fluttertpc_mobile_scanner-a50b8f5e377524f79dd143f1297e82d257ad53b1/ohos": {"name": "mobile_scanner", "dependencies": {"@ohos/flutter_ohos": {"specifier": "file:../../../PUB/git/fluttertpc_mobile_scanner-a50b8f5e377524f79dd143f1297e82d257ad53b1/ohos/libs/flutter.har", "version": "file:har/flutter.har"}}, "devDependencies": {}, "dynamicDependencies": {}, "maskedByOverrideDependencyMap": false}, "../../../PUB/git/fluttertpc_open_app_settings-038c0dd8ffd45bcf84691d62cfff957748cd493a/ohos": {"name": "open_app_settings", "dependencies": {"@ohos/flutter_ohos": {"specifier": "file:../../../PUB/git/fluttertpc_open_app_settings-038c0dd8ffd45bcf84691d62cfff957748cd493a/ohos/har/flutter.har", "version": "file:har/flutter.har"}}, "devDependencies": {}, "dynamicDependencies": {}, "maskedByOverrideDependencyMap": false}, "../../../PUB/git/fluwx-9fe4d9f1d3c05de5de18d25cc53852866d9c34d7/ohos": {"name": "fluwx", "dependencies": {"@ohos/flutter_ohos": {"specifier": "file:../../../PUB/git/fluwx-9fe4d9f1d3c05de5de18d25cc53852866d9c34d7/ohos/har/flutter.har", "version": "file:har/flutter.har"}, "@tencent/wechat_open_sdk": {"specifier": "1.0.6", "version": "1.0.14"}}, "devDependencies": {}, "dynamicDependencies": {}, "maskedByOverrideDependencyMap": false}, "../plugins/camera_ohos/ohos": {"name": "camera_ohos", "dependencies": {"@ohos/flutter_ohos": {"specifier": "file:../plugins/camera_ohos/ohos/libs/flutter.har", "version": "file:har/flutter.har"}}, "devDependencies": {}, "dynamicDependencies": {}, "maskedByOverrideDependencyMap": false}, "entry": {"name": "entry", "dependencies": {"premierlibrary": {"specifier": "file:entry/libs/premierlibrary.har", "version": "file:entry/libs/premierlibrary.har"}, "shared_preferences_ohos": {"specifier": "file:har/shared_preferences_ohos.har", "version": "file:../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/shared_preferences/shared_preferences_ohos/ohos"}, "webview_flutter_ohos": {"specifier": "file:har/webview_flutter_ohos.har", "version": "file:../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/webview_flutter/webview_flutter_ohos/ohos"}, "path_provider_ohos": {"specifier": "file:har/path_provider_ohos.har", "version": "file:../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/path_provider/path_provider_ohos/ohos"}, "url_launcher_ohos": {"specifier": "file:har/url_launcher_ohos.har", "version": "file:../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/url_launcher/url_launcher_ohos/ohos"}, "image_picker_ohos": {"specifier": "file:har/image_picker_ohos.har", "version": "file:../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/image_picker/image_picker_ohos/ohos"}, "permission_handler_ohos": {"specifier": "file:har/permission_handler_ohos.har", "version": "file:../../../PUB/git/flutter_permission_handler-1e7c80f0720aff742d9f1e08017e1753bcad76ef/permission_handler_ohos/ohos"}, "device_info_plus": {"specifier": "file:har/device_info_plus.har", "version": "file:../../../PUB/git/flutter_plus_plugins-a1347adcca3a46346a6ddd127cebcec9970cad6c/packages/device_info_plus/device_info_plus/ohos"}, "package_info_plus": {"specifier": "file:har/package_info_plus.har", "version": "file:../../../PUB/git/flutter_plus_plugins-452bfb40465722552f31cf78cde60f99cc691e36/packages/package_info_plus/package_info_plus/ohos"}, "flutter_blue_plus_ohos": {"specifier": "file:har/flutter_blue_plus_ohos.har", "version": "file:har/flutter_blue_plus_ohos.har"}, "flutter_blue_plus": {"specifier": "file:har/flutter_blue_plus.har", "version": "file:../../../PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos"}, "iamgeqr_flutter_plugin": {"specifier": "file:har/iamgeqr_flutter_plugin.har", "version": "file:../../../PUB/git/flutter_image_scanner-b9f20a88ca1c738c700a15655b8fab4c305c4d32/ohos"}, "connectivity_plus": {"specifier": "file:har/connectivity_plus.har", "version": "file:../../../PUB/git/flutter_plus_plugins-8720286d1b4b664dbc1d87ba169125242d4f10fb/packages/connectivity_plus/connectivity_plus/ohos"}, "mobile_scanner": {"specifier": "file:har/mobile_scanner.har", "version": "file:../../../PUB/git/fluttertpc_mobile_scanner-a50b8f5e377524f79dd143f1297e82d257ad53b1/ohos"}, "@pura/harmony-utils": {"specifier": "1.2.4", "version": "1.2.4"}, "@bdmap/base": {"specifier": "1.2.7", "version": "1.2.7"}, "@bdmap/search": {"specifier": "1.2.7", "version": "1.2.7"}, "@bdmap/map": {"specifier": "1.2.7", "version": "1.2.7"}, "open_app_settings": {"specifier": "file:har/open_app_settings.har", "version": "file:../../../PUB/git/fluttertpc_open_app_settings-038c0dd8ffd45bcf84691d62cfff957748cd493a/ohos"}, "fluwx": {"specifier": "file:har/fluwx.har", "version": "file:../../../PUB/git/fluwx-9fe4d9f1d3c05de5de18d25cc53852866d9c34d7/ohos"}, "@tencent/wechat_open_sdk": {"specifier": "^1.0.11", "version": "1.0.14"}, "camera_ohos": {"specifier": "file:har/camera_ohos.har", "version": "file:../plugins/camera_ohos/ohos"}}, "devDependencies": {}, "dynamicDependencies": {}, "maskedByOverrideDependencyMap": false}}, "packages": {"@tencent/wechat_open_sdk@1.0.14": {"integrity": "sha512-qTA/XkKqBp5Bji6D4ePCnxdGpaDvzcqeI/vK6FRUwdBIlO9T/o0xuO1akCMCEB9c6qshIUAYqyRlHYmkRIkm4g==", "storePath": "oh_modules/.ohpm/@tencent+wechat_open_sdk@1.0.14", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@ohos/flutter_ohos@file:har/flutter.har": {"storePath": "oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@free/global@1.0.3": {"integrity": "sha512-WBrNWmjFkDhQP4ZfegyFBm5VC5CXmH2tfRMfBG9HtyO5v0t0t80c3cUMNm7Uq9ahYV9jpPt5BTN2IMpoB4wM9w==", "storePath": "oh_modules/.ohpm/@free+global@1.0.3", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@rongcloud/imkit@1.7.1": {"integrity": "sha512-Jr57bo+OInjWMnF6lKq6qbQm5ofdLoaHlwBJFnFO28606tUjMLYUoEzITEwEODenSyJIoUGHpRjwgGpGJqcNlA==", "storePath": "oh_modules/.ohpm/@rongcloud+imkit@1.7.1", "dependencies": {"@rongcloud/imlib": "1.7.1", "@charles/amrnbconverter": "1.0.1"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@ohos/hypium@1.0.18": {"integrity": "sha512-RGe/iLGdeywdQilMWZsHKUoiE9OJ+9QxQsorF92R2ImLNVHVhbpSePNITGpW7TnvLgOIP/jscOqfIOhk6X7XRQ==", "storePath": "oh_modules/.ohpm/@ohos+hypium@1.0.18", "dependencies": {}, "dynamicDependencies": {}, "dev": true, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@rongcloud/imlib@1.7.1": {"integrity": "sha512-P4u6gS277VbqAy7mHyd1E7Dd1hTq6vgkNDhfFyVcbjS8OrCqLRRLEUsLfBMyk5CtodR2U7aSF4MyPCPh6BlIOA==", "storePath": "oh_modules/.ohpm/@rongcloud+imlib@1.7.1", "dependencies": {"librongimlib.so": "oh_modules/.ohpm/@rongcloud+imlib@1.7.1/oh_modules/@rongcloud/imlib/src/main/cpp/types/librongimlib"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@ohos/mqtt@2.0.23": {"integrity": "sha512-YtI31ruqc+UMU1k15KEbkPrVCmN2yigjL5a4lJrgBZKM9HObuZTu7n9N0ftoMZm1APoG3Cqt01fozb1SkN34Xg==", "storePath": "oh_modules/.ohpm/@ohos+mqtt@2.0.23", "dependencies": {"libmqttasync.so": "oh_modules/.ohpm/@ohos+mqtt@2.0.23/oh_modules/@ohos/mqtt/src/main/cpp/types/libmqttasync"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@charles/amrnbconverter@1.0.1": {"integrity": "sha512-LIj24/k9BFUm9l7PTuvhTOnELax3GbtOAcJnXMQMCGCKmXmU6HZ/8IVok4je74kAyLYIVvvak2DzUOOdzWoaEw==", "storePath": "oh_modules/.ohpm/@charles+amrnbconverter@1.0.1", "dependencies": {"libamrconverter.so": "oh_modules/.ohpm/@charles+amrnbconverter@1.0.1/oh_modules/@charles/amrnbconverter/src/main/cpp/types/libamrconverter"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "librongimlib.so@file:oh_modules/.ohpm/@rongcloud+imlib@1.7.1/oh_modules/@rongcloud/imlib/src/main/cpp/types/librongimlib": {"storePath": "oh_modules/.ohpm/@rongcloud+imlib@1.7.1/oh_modules/@rongcloud/imlib/src/main/cpp/types/librongimlib", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "libmqttasync.so@file:oh_modules/.ohpm/@ohos+mqtt@2.0.23/oh_modules/@ohos/mqtt/src/main/cpp/types/libmqttasync": {"storePath": "oh_modules/.ohpm/@ohos+mqtt@2.0.23/oh_modules/@ohos/mqtt/src/main/cpp/types/libmqttasync", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "libamrconverter.so@file:oh_modules/.ohpm/@charles+amrnbconverter@1.0.1/oh_modules/@charles/amrnbconverter/src/main/cpp/types/libamrconverter": {"storePath": "oh_modules/.ohpm/@charles+amrnbconverter@1.0.1/oh_modules/@charles/amrnbconverter/src/main/cpp/types/libamrconverter", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@pura/harmony-utils@1.2.4": {"integrity": "sha512-P17M+h5eonalgtEd3Xe+a85PPZilguRc8uoggEt9+AjrBdAasQddf6L/W7NRubSBCiDf4k2vnVRtgeQqKsWrmA==", "storePath": "oh_modules/.ohpm/@pura+harmony-utils@1.2.4", "dependencies": {"class-transformer": "0.5.1"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@bdmap/search@1.2.7": {"integrity": "sha512-aO5sQq1Sv93CQlZt3l0Xq37SZoJLaKJhr6pLQd/kH/zA7VTvZeVILl1N5loyXMvXR9tOFQWqxtY6Lm+B/7gK8Q==", "storePath": "oh_modules/.ohpm/@bdmap+search@1.2.7", "dependencies": {"@bdmap/base": "1.2.7"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "flutter_blue_plus_ohos@file:har/flutter_blue_plus_ohos.har": {"storePath": "oh_modules/.ohpm/flutter_blue_plus_ohos@4df70qmo49oaztang7dsivr+mi1tpjfgwsjngar+e2u=", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "class-transformer@0.5.1": {"integrity": "sha512-SQa1Ws6hUbfC98vKGxZH3KFY0Y1lm5Zm0SY8XX9zbK7FJCyVEac3ATW0RIpwzW+oOfmHE5PMPufDG9hCfoEOMw==", "storePath": "oh_modules/.ohpm/class-transformer@0.5.1", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@bdmap/base@1.2.7": {"integrity": "sha512-9NtZHtlzKGweVcYpHJ5RT5dMAjta+z3akqDQr2BHvxM5HI14/G7P6/Q5oNKAjXkM8KLg+p9jYisnCKLM9cdwWg==", "storePath": "oh_modules/.ohpm/@bdmap+base@1.2.7", "dependencies": {"@types/libbaidumapsdk_base_for_js_v1_0_0.so": "oh_modules/.ohpm/@bdmap+base@1.2.7/oh_modules/@bdmap/base/src/main/cpp/type", "@bdmap/verify": "1.0.2"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@bdmap/verify@1.0.2": {"integrity": "sha512-QyOqj/MmAoHuQZQCLAbFl7jYYVRSdqd2mLx4ajM1xI1ZFfZ0tkugf/K7LwdFwDDsCtv+U7bjGOYVUx38zBJrvQ==", "storePath": "oh_modules/.ohpm/@bdmap+verify@1.0.2", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@bdmap/map@1.2.7": {"integrity": "sha512-dxP3vn91ssbKb8shy0JoFSiYHFWyEHFFZXH1fM2z7ZNLu9GT5DeoHGw8bAehtwoEoencK4mmF5eOQ4v8D3b9ew==", "storePath": "oh_modules/.ohpm/@bdmap+map@1.2.7", "dependencies": {"@types/libbaidumapsdk_map_for_js_v1_0_0.so": "oh_modules/.ohpm/@bdmap+map@1.2.7/oh_modules/@bdmap/map/src/main/cpp/type", "@bdmap/base": "1.2.7"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "premierlibrary@file:entry/libs/premierlibrary.har": {"storePath": "oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=", "dependencies": {"libpremierlibrary.so": "oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=/oh_modules/premierlibrary/src/main/cpp/types/libpremierlibrary", "@acpm/acpm_ohos_pc": "oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=/oh_modules/premierlibrary/libs/acpm_ohos_pc.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@acpm/acpm_ohos_pc@file:oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=/oh_modules/premierlibrary/libs/acpm_ohos_pc.har": {"storePath": "oh_modules/.ohpm/@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=", "dependencies": {"@acpm/aio_crashsdk": "oh_modules/.ohpm/@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=/oh_modules/@acpm/acpm_ohos_pc/hars/aio_crashsdk.har", "@acpm/aio_util": "oh_modules/.ohpm/@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=/oh_modules/@acpm/acpm_ohos_pc/hars/aio_util.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "shared_preferences_ohos@file:../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/shared_preferences/shared_preferences_ohos/ohos": {"storePath": "../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/shared_preferences/shared_preferences_ohos/ohos", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "webview_flutter_ohos@file:../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/webview_flutter/webview_flutter_ohos/ohos": {"storePath": "../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/webview_flutter/webview_flutter_ohos/ohos", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "path_provider_ohos@file:../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/path_provider/path_provider_ohos/ohos": {"storePath": "../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/path_provider/path_provider_ohos/ohos", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "url_launcher_ohos@file:../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/url_launcher/url_launcher_ohos/ohos": {"storePath": "../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/url_launcher/url_launcher_ohos/ohos", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "image_picker_ohos@file:../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/image_picker/image_picker_ohos/ohos": {"storePath": "../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/image_picker/image_picker_ohos/ohos", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "permission_handler_ohos@file:../../../PUB/git/flutter_permission_handler-1e7c80f0720aff742d9f1e08017e1753bcad76ef/permission_handler_ohos/ohos": {"storePath": "../../../PUB/git/flutter_permission_handler-1e7c80f0720aff742d9f1e08017e1753bcad76ef/permission_handler_ohos/ohos", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "device_info_plus@file:../../../PUB/git/flutter_plus_plugins-a1347adcca3a46346a6ddd127cebcec9970cad6c/packages/device_info_plus/device_info_plus/ohos": {"storePath": "../../../PUB/git/flutter_plus_plugins-a1347adcca3a46346a6ddd127cebcec9970cad6c/packages/device_info_plus/device_info_plus/ohos", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "package_info_plus@file:../../../PUB/git/flutter_plus_plugins-452bfb40465722552f31cf78cde60f99cc691e36/packages/package_info_plus/package_info_plus/ohos": {"storePath": "../../../PUB/git/flutter_plus_plugins-452bfb40465722552f31cf78cde60f99cc691e36/packages/package_info_plus/package_info_plus/ohos", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "flutter_blue_plus@file:../../../PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos": {"storePath": "../../../PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "iamgeqr_flutter_plugin@file:../../../PUB/git/flutter_image_scanner-b9f20a88ca1c738c700a15655b8fab4c305c4d32/ohos": {"storePath": "../../../PUB/git/flutter_image_scanner-b9f20a88ca1c738c700a15655b8fab4c305c4d32/ohos", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "connectivity_plus@file:../../../PUB/git/flutter_plus_plugins-8720286d1b4b664dbc1d87ba169125242d4f10fb/packages/connectivity_plus/connectivity_plus/ohos": {"storePath": "../../../PUB/git/flutter_plus_plugins-8720286d1b4b664dbc1d87ba169125242d4f10fb/packages/connectivity_plus/connectivity_plus/ohos", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "mobile_scanner@file:../../../PUB/git/fluttertpc_mobile_scanner-a50b8f5e377524f79dd143f1297e82d257ad53b1/ohos": {"storePath": "../../../PUB/git/fluttertpc_mobile_scanner-a50b8f5e377524f79dd143f1297e82d257ad53b1/ohos", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "open_app_settings@file:../../../PUB/git/fluttertpc_open_app_settings-038c0dd8ffd45bcf84691d62cfff957748cd493a/ohos": {"storePath": "../../../PUB/git/fluttertpc_open_app_settings-038c0dd8ffd45bcf84691d62cfff957748cd493a/ohos", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "fluwx@file:../../../PUB/git/fluwx-9fe4d9f1d3c05de5de18d25cc53852866d9c34d7/ohos": {"storePath": "../../../PUB/git/fluwx-9fe4d9f1d3c05de5de18d25cc53852866d9c34d7/ohos", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har", "@tencent/wechat_open_sdk": "1.0.14"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "camera_ohos@file:../plugins/camera_ohos/ohos": {"storePath": "../plugins/camera_ohos/ohos", "dependencies": {"@ohos/flutter_ohos": "har/flutter.har"}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@acpm/aio_crashsdk@file:oh_modules/.ohpm/@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=/oh_modules/@acpm/acpm_ohos_pc/hars/aio_crashsdk.har": {"storePath": "oh_modules/.ohpm/@acpm+aio_crashsdk@rynukgn6cor9bomrriugyxyd1z7zerm+gebyzjm0l0s=", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@acpm/aio_util@file:oh_modules/.ohpm/@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=/oh_modules/@acpm/acpm_ohos_pc/hars/aio_util.har": {"storePath": "oh_modules/.ohpm/@acpm+aio_util@m7v9iv6jbihoz+rwxvd+y3p2msbrx6lznc1xn3kcclu=", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@types/libbaidumapsdk_base_for_js_v1_0_0.so@file:oh_modules/.ohpm/@bdmap+base@1.2.7/oh_modules/@bdmap/base/src/main/cpp/type": {"storePath": "oh_modules/.ohpm/@bdmap+base@1.2.7/oh_modules/@bdmap/base/src/main/cpp/type", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@types/libbaidumapsdk_map_for_js_v1_0_0.so@file:oh_modules/.ohpm/@bdmap+map@1.2.7/oh_modules/@bdmap/map/src/main/cpp/type": {"storePath": "oh_modules/.ohpm/@bdmap+map@1.2.7/oh_modules/@bdmap/map/src/main/cpp/type", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}, "libpremierlibrary.so@file:oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=/oh_modules/premierlibrary/src/main/cpp/types/libpremierlibrary": {"storePath": "oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=/oh_modules/premierlibrary/src/main/cpp/types/libpremierlibrary", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}}}