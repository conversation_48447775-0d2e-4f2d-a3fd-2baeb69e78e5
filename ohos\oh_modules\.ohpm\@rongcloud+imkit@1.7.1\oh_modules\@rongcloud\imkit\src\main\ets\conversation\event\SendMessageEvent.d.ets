// @keepTs
// @ts-nocheck
/**
 * Created on 2024/08/16
 *
 * 消息发送时状态变化的通知事件
 *
 * <AUTHOR>
 */
import { EngineError, Message } from '@rongcloud/imlib';
import { MessageSendState } from '../../base/enum/MessageSendState';
import { MessageOperationEvent } from './MessageOperationEvent';
export declare class SendMessageEvent extends MessageOperationEvent {
    private _state;
    private _error?;
    private _message;
    set message(p25: Message);
    get message(): Message;
    setState(o25: MessageSendState): void;
    getState(): MessageSendState;
    setError(n25: EngineError | undefined): void;
    getError(): EngineError | undefined;
    constructor(k25: MessageSendState, l25: Message);
}
