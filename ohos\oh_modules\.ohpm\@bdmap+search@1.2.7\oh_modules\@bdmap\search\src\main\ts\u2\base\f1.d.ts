/**
 * SDK内部报错代码,用于传递错误信息给各parseSearchResult实现类处理,不传递给用户
 */
export interface SdkInnerError {
    PermissionCheckError?: any;
    httpStateError?: number;
}
/**
* search的类型
*/
export declare enum SearchType {
    POI_NEAR_BY_SEARCH = 0,
    POI_IN_CITY_SEARCH = 1,
    POI_IN_BOUND_SEARCH = 2,
    POI_DETAIL_SEARCH = 3,
    INDOOR_POI_SEARCH = 4,
    SUGGESTION_SEARCH_TYPE = 5,
    GEO_CODER = 6,
    REVERSE_GEO_CODER = 7,
    MASS_TRANSIT_ROUTE = 8,
    TRANSIT_ROUTE = 9,
    DRIVE_ROUTE = 10,
    BIKE_ROUTE = 11,
    WALK_ROUTE = 12,
    INDOOR_ROUTE = 13,
    BUS_LINE_DETAIL = 14,
    DISTRICT_SEARCH = 15,
    POI_DETAIL_SHARE = 16,
    LOCATION_SEARCH_SHARE = 17,
    ROUTE_PLAN_SHARE = 18,
    WEATHER_SEARCH = 19,
    RECOMMEND_STOP = 20,
    BUILDING_SEARCH = 21,
    AOI_SEARCH = 22
}
