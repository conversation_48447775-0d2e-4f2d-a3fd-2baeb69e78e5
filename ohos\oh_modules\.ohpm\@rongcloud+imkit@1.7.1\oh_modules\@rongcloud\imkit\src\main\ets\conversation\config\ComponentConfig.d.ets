// @keepTs
// @ts-nocheck
import { ConversationIdentifier } from "@rongcloud/imlib";
import { ComponentIdentifier, WatermarkPageIdentifier } from "../../base/enum/ComponentIdentifier";
import { IConversationViewModel } from "../model/IConversationViewModel";
import { UiMessage } from "../model/UiMessage";
/**
 * 基础组件配置，包含组件标识、组件WrappedBuilder
 */
export interface BaseComponentConfig<T extends Object[]> {
    /**
     * 组件标识
     *
     *```
     * 注意：
     * 支持的类型参照 ComponentIdentifier
     *```
     */
    identifier: ComponentIdentifier;
    /**
     * 组件WrappedBuilder。
     * @since 1.6.0
     */
    component?: WrappedBuilder<T> | null;
}
/**
 * 输入区域组件配置
 * @since 1.5.1
 */
export interface InputAreaComponentConfig extends BaseComponentConfig<[
    InputAreaComponentData
]> {
    /**
     * 组件WrappedBuilder。
     *
     * `WrappedBuilder` 参数说明：
     *```
     * @param  Context:上下文
     * @param  ConversationIdentifier：会话标识
     * @param  string:透传参数，当 `identifier` 为某些枚举类型的情况下有值，其余情况下为空。详细说明如下
     *      ComponentIdentifier.InputBarVoiceButton: "Voice" 语音类型、"Text" 文本类型。
     *      ComponentIdentifier.InputBarEmoticonButton: "Emoticon" 表情类型、"Text" 文本类型。
     *      ComponentIdentifier.DestructBarVoiceButton: "Voice" 语音类型、"Text" 文本类型。
     *```
     * @deprecated 1.6.0 版本此属性开始废弃，建议使用 component。如同时设置 contentBuilder 与 component，则 component 优先级高。
     */
    contentBuilder?: WrappedBuilder<[
        Context,
        ConversationIdentifier,
        string
    ]> | null;
}
/**
 * 输入区域自定义组件数据，封装必要的参数透传给自定义组件
 * @since 1.6.0
 */
export declare class InputAreaComponentData {
    /**
     * 上下文
     */
    context: Context | undefined;
    /**
     * 会话标识
     */
    convId: ConversationIdentifier | undefined;
    /**
     * 透传参数
     *
     *```
     * 当 `identifier` 为某些枚举类型的情况下有值，其余情况下为空。详细说明如下
     *  1. ComponentIdentifier.InputBarVoiceButton: "Voice" 语音类型、"Text" 文本类型。
     *  2. ComponentIdentifier.InputBarEmoticonButton: "Emoticon" 表情类型、"Text" 文本类型。
     *  3. ComponentIdentifier.DestructBarVoiceButton: "Voice" 语音类型、"Text" 文本类型。
     *```
     */
    data: string;
    /**
     * 会话页面ViewModel，用来执行后续的事件
     *
     *```
     * 与 `InputAreaComponentConfig` 的 identifier` 参数具备对应关系，使用说明如下：
     *  1. 当 `identifier` 是 InputBarExpandTextAreaButton。
     *  - 1.1 调用 `onInputTextAreaContentChange(text:string)` 更新输入框文本内容，text是更新的内容；
     *  - 1.2 调用 `getInputTextAreaContent():string` 获取当前输入框文本内容。
     *```
     * @since 1.7.0
     */
    conversationViewModel?: IConversationViewModel | undefined;
}
/**
 * 会话内容组件配置
 * @since 1.6.0
 */
export interface ConversationContentComponentConfig extends BaseComponentConfig<[
    ConversationContentComponentData
]> {
}
/**
 * 会话自定义组件数据，封装必要的参数透传给自定义组件
 * @since 1.6.0
 */
export declare class ConversationContentComponentData {
    /**
     * 上下文
     */
    context: Context | undefined;
    /**
     * 会话标识
     */
    convId: ConversationIdentifier | undefined;
    /**
     * 透传参数
     *
     *```
     * 与 `ConversationContentComponentConfig` 的 identifier` 参数具备对应关系，使用说明如下：
     *  1. 当 `identifier` 是 ConversationNewReceivedUnreadMessageButton， 代表在会话中新收到的未读消息的数量；
     *  2. 当 `identifier` 是 ConversationUnreadMessageButton， 代表会话中的未读消息数量；
     *  3. 当 `identifier` 是 ConversationUnreadMentionedMessageButton， 代表当前@自己的未读消息的数量；
     *```
     */
    data: number;
    /**
     * 会话页面ViewModel，用来执行后续的事件
     *
     *```
     * 与 `ConversationContentComponentConfig` 的 identifier` 参数具备对应关系，使用说明如下：
     *  1. 当 `identifier` 是 ConversationNewReceivedUnreadMessageButton， 调用 `onClickNewReceivedUnreadMessageButton` 执行新收到未读消息的点击事件；
     *  2. 当 `identifier` 是 ConversationUnreadMessageButton，调用 `onClickUnreadMessageButton` 执行未读消息的点击事件；
     *  3. 当 `identifier` 是 ConversationUnreadMentionedMessageButton， 调用 `onClickUnreadMentionedMessageButton` 执行未读@我的消息的点击事件；
     *```
     *
     */
    conversationViewModel: IConversationViewModel | undefined;
}
/**
 * 消息气泡组件配置
 * @since 1.7.0
 */
export interface MessageBubbleComponentConfig extends BaseComponentConfig<[
    MessageBubbleComponentData
]> {
}
/**
 * 消息气泡自定义组件数据，封装必要的参数透传给自定义组件
 * @since 1.7.0
 */
export declare class MessageBubbleComponentData {
    /**
     * 上下文
     */
    context: Context | undefined;
    /**
     * 消息体
     */
    uiMessage: UiMessage | undefined;
}
/**
 * 水印组件配置
 * @since 1.7.0
 */
export interface WatermarkComponentConfig extends BaseComponentConfig<[
    WatermarkComponentData
]> {
    /**
     * 设置水印生效的页面，水印支持的页面参照 `WatermarkPageIdentifier`。
     */
    page: WatermarkPageIdentifier[];
}
/**
 * 水印自定义组件数据，封装必要的参数透传给自定义组件
 * @since 1.7.0
 */
export declare class WatermarkComponentData {
    /**
     * 上下文
     */
    context: Context | undefined;
    /**
     * 会话标识，可能为undefined
     */
    convId: ConversationIdentifier | undefined;
}
