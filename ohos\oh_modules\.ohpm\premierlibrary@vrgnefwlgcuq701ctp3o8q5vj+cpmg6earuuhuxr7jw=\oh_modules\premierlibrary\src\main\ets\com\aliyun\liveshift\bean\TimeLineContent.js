export class TimeLineContent {
    constructor() {
        this.current = -1;
        this.timeline = [];
    }
    getStartTime() {
        if (this.timeline.length > 0) {
            return this.timeline[0].start;
        }
        return 0;
    }
    getEndTime() {
        if (this.timeline.length > 0) {
            return this.timeline[this.timeline.length - 1].end;
        }
        return 0;
    }
}
