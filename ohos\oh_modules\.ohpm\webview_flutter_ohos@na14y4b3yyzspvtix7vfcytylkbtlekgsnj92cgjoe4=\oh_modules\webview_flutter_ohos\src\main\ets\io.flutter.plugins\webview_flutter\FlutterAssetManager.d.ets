import resourceManager from '@ohos.resourceManager';
import { FlutterAssets } from "@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin";
export declare abstract class FlutterAssetManager {
    private resourceManager;
    constructor(resourceManager: resourceManager.ResourceManager);
    abstract getAssetFilePathByName(name: string): any;
    list(path: string): Promise<string[]>;
}
export declare class PluginBindingFlutterAssetManager extends FlutterAssetManager {
    flutterAssets: FlutterAssets;
    constructor(resourceManager: resourceManager.ResourceManager, flutterAssets: FlutterAssets);
    getAssetFilePathByName(name: string): string;
}
