// @keepTs
// @ts-nocheck
import { Message, PublicServiceMenuItem, PublicServiceInfo } from '@rongcloud/imlib';
import { ConversationComponentData } from '../model/ConversationComponentData';
import { UiMessage } from '../model/UiMessage';
import { InputAreaController } from '../../internal/conversation/inputbar/InputAreaController';
import { ConversationComponentDelegate } from '../listener/ConversationDelegate';
@Component
export declare struct ConversationComponent {
    @Require
    conversationData: ConversationComponentData;
    @BuilderParam
    limitBuilder?: () => void;
    @State
    messageListComponentBackgroundColor: ResourceColor;
    @Link
    pageShow: boolean;
    @State
    inputAreaController: InputAreaController;
    @Link
    isEdit: boolean;
    @State
    referenceMessage: Message | undefined;
    onPublicServiceClickMainAction?: (item: PublicServiceMenuItem, parentIndex: number) => void;
    onPublicServiceClickChildAction?: (item: PublicServiceMenuItem, parentIndex: number, childIndex: number) => void;
    fetchPublicServiceInfo?: (returnData: (publicServiceInfo: PublicServiceInfo) => void) => void;
    onClickMoreAction?: (isEdit: boolean) => void;
    delegate: ConversationComponentDelegate | undefined;
    private viewModel;
    private context;
    @State
    private isDownRefreshing;
    @State
    private isUpRefreshing;
    @State
    private conversation;
    private pullUpAble;
    @State
    private allSelectedMessages;
    @State
    private unreadNewMsgArr;
    @State
    private unreadMentionedMessages;
    @State
    private messageListSize;
    private listener;
    private initTaskTimerId;
    @State
    private firstScreenMessageIsRendered;
    private isScrollAtEnd;
    private isDestructMode;
    private currentDestructMessage;
    private destructPlugin;
    private dialogController;
    private DestructMessageClickEventCallback;
    private AVPlayerEventCallback;
    private IConversationViewModelEventCallback;
    aboutToAppear(): Promise<void>;
    aboutToDisappear(): void;
    @Builder
    componentBuilder(d39: UiMessage, e39: number): void;
    @Builder
    timeLabel(y38: UiMessage): void;
    @Builder
    buildBottomUnreadMessageComponent(): void;
    @Builder
    buildBottomUnreadMessageComponentDefault(): void;
    @Builder
    buildTopUnreadMessageComponent(): void;
    @Builder
    buildTopUnreadMessageComponentDefault(): void;
    @Builder
    buildTopUnreadMentionedMessageComponent(): void;
    @Builder
    buildTopUnreadMentionedMessageComponentDefault(): void;
    @Builder
    buildInputBar(): void;
    @Builder
    buildMessageList(): void;
    build(): void;
    /**
     * 添加引用消息
     */
    private longClickReferenceItem;
    /**
     * 判断是否可引用
     * @param objectName
     * @returns
     */
    private referenceAble;
    /**
     * 判断是否是阅后即焚消息
     * @param message
     * @returns
     */
    private isDestruct;
    /**
     * 展示被引用的消息
     */
    private ReferenceMessageBuilder;
    private clearTopUnreadComponent;
    /**
     * HQ录音事件
     */
    private onSendHQVoiceAction;
    /**
     * 普通录音事件
     */
    private onSendVoiceAction;
    /**
     * 公众号
     */
    private onPublicServiceModeChange;
    /**
     * 阅后即焚
     */
    private onDestructModeChange;
    /**
     * 公众号-主菜单点击事件
     */
    onPublicServiceParentClickAction: (item: PublicServiceMenuItem, index: number) => void;
    /**
     * 公众号-子菜单点击事件
     */
    onPublicServiceChildClickAction: (item: PublicServiceMenuItem, parentIndex: number, index: number) => void;
    private canEdit;
    /**
     * inputBar事件
     */
    private onSendTextAction;
    /**
     * 当键盘类型变化时
     */
    private onKeyboardChange;
    /**
     * 阅后即焚模式点击媒体按钮
     */
    private onClickMediaAction;
    /**
     * 监听消息变化
     */
    private onMessageOperationChange;
    /**
     * 监听键盘状态
     */
    private initMonitorKeyboardStatus;
    /**
     * 添加长按更多
     */
    private longClickMoreItem;
    /**
     * 消息操作事件
     */
    private operationAction;
    /**
     * 阅后即焚插件点击事件
     */
    private onClickDestructPlugin;
    private startScrollBottomTask;
    private onClickBottomUnreadMessageComponent;
    private onClickUnreadMessageComponent;
    private onClickUnreadMentionedMessageComponent;
    private onClickReferencedMessage;
    private getInitialIndex;
    /**
     * 观察 this.pageShow 字段
     */
    onPageShowChange(): Promise<void>;
    displayMessageItemLongClickActionIfNeed(): void;
    /**
     * 设置输入框变化处理器
     */
    private setupInputChangeHandler;
    /**
     * 观察 this.isEdit 字段
     */
    onIsEditChange(): Promise<void>;
    private messageDidUpdate;
}
