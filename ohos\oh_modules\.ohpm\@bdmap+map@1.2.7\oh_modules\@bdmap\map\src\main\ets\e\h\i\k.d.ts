import BaseMap from "./o2"; import { ESatelliteLayerType } from "../util/b1/c1";             import { LatLng } from '@bdmap/base'; import MapStatus from "./l"; import type { IMapOption, Nullable } from "../g1/a2";             export default class MapOptions { private _mapStatus; private _rotateGesturesEnabled; private _scrollGesturesEnabled; private _overlookingGesturesEnabled; private _zoomGesturesEnabled; private _showSatelliteMap; private _showBaseIndoorMap; private _showTrafficMap; private _showMapPoi; private _showMapIndoorPoi; private _baseMap; private _useMapCenterWhenPinch; private _zoomCenter; private _enableDbClickZoom;         _touchNearly: number;                                                                                                                               constructor(v30?: Nullable<IMapOption>);           get mapStatus(): MapStatus;         set mapStatus(u30: MapStatus);           set rotateGesturesEnabled(t30: boolean);         get rotateGesturesEnabled(): boolean;           set moveGesturesEnabled(s30: boolean);         get moveGesturesEnabled(): boolean;           set overlookingGesturesEnabled(r30: boolean);         get overlookingGesturesEnabled(): boolean;           set zoomGesturesEnabled(zoomGesturesEnabled: boolean);         get zoomGesturesEnabled(): boolean;           get showSatelliteMap(): ESatelliteLayerType;         set showSatelliteMap(type: ESatelliteLayerType);           get showBaseIndoorMap(): boolean;         set showBaseIndoorMap(show: boolean);           get showTrafficMap(): boolean;         set showTrafficMap(show: boolean); get showMapPoi(): boolean;         set showMapPoi(isShow: boolean); get showMapIndoorPoi(): boolean;         set showMapIndoorPoi(isShow: boolean);         set touchNearly(q30: number);         get touchNearly(): number;         set baseMap(p30: BaseMap);         get baseMap(): BaseMap;         get useMapCenterWhenPinch(): boolean;         set useMapCenterWhenPinch(o30: boolean);         get zoomCenter(): Nullable<LatLng>;           set zoomCenter(center: Nullable<LatLng>);         get enableDBClickZoom(): boolean;           set enableDBClickZoom(enable: boolean); updateGestureEnable(): void; updateGestureConfig(): void; refresh(l30?: boolean): void;         toBundle(): { showSatelliteMap: ESatelliteLayerType; showBaseIndoorMap: number; showTrafficMap: number; }; destroy(): void; toString(): string; } 