import { CameraProperties } from '../CameraProperties';
import { CameraFeatureFactory } from './CameraFeatureFactory';
import Ability from '@ohos.app.ability.Ability';
import { DartMessenger } from '../DartMessenger';
import { ResolutionPreset } from './resolution/ResolutionPreset';
import { ResolutionFeature } from './resolution/ResolutionFeature';
import { AutoFocusFeature } from './autofocus/AutoFocusFeature';
import { SensorOrientationFeature } from './sensororientation/SensorOrientationFeature';
import { ZoomLevelFeature } from './zoomlevel/ZoomLevelFeature';
import { NoiseReductionFeature } from './noisereduction/NoiseReductionFeature';
import { FpsRangeFeature } from './fpsrange/FpsRangeFeature';
import { FocusPointFeature } from './focuspoint/FocusPointFeature';
import { FlashFeature } from './flash/FlashFeature';
import { ExposurePointFeature } from './exposurepoint/ExposurePointFeature';
import { ExposureOffsetFeature } from './exposureoffset/ExposureOffsetFeature';
import { ExposureLockFeature } from './exposurelock/ExposureLockFeature';
export declare class CameraFeatures {
    static init(cameraFeatureFactory: CameraFeatureFactory, cameraProperties: CameraProperties, ability: Ability, dartMessenger: DartMessenger, resolutionPreset: ResolutionPreset): CameraFeatures;
    private featureMap;
    setAutoFocus(autoFocus: AutoFocusFeature): void;
    getAutoFocus(): AutoFocusFeature;
    getExposureLock(): ExposureLockFeature;
    setExposureLock(exposureLock: ExposureLockFeature): void;
    getExposureOffset(): ExposureOffsetFeature;
    setExposureOffset(exposureOffset: ExposureOffsetFeature): void;
    getExposurePoint(): ExposurePointFeature;
    setExposurePoint(exposurePoint: ExposurePointFeature): void;
    getFlash(): FlashFeature;
    setFlash(flash: FlashFeature): void;
    getFocusPoint(): FocusPointFeature;
    setFocusPoint(focusPoint: FocusPointFeature): void;
    getFpsRange(): FpsRangeFeature;
    setFpsRange(fpsRange: FpsRangeFeature): void;
    getNoiseReduction(): NoiseReductionFeature;
    setNoiseReduction(noiseReduction: NoiseReductionFeature): void;
    getSensorOrientation(): SensorOrientationFeature;
    setSensorOrientation(sensorOrientation: SensorOrientationFeature): void;
    getZoomLevel(): ZoomLevelFeature;
    setZoomLevel(zoomLevel: ZoomLevelFeature): void;
    getResolution(): ResolutionFeature;
    setResolution(resolution: ResolutionFeature): void;
}
