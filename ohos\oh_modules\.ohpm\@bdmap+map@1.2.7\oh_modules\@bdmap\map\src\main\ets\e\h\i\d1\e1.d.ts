import type { Nullable } from "../../g1/a2"; import type BaseMap from "../o2"; import BaseLayer from "./j1";             export default class CompassLayer extends BaseLayer { private _x; private _y; private _harfIcon; private _hideTime;               constructor(name: string, id: string, h29: BaseMap);         get x(): Nullable<number>;         set x(val: Nullable<number>);         get y(): Nullable<number>;         set y(val: Nullable<number>);         get hideTime(): number;         set hideTime(val: number);                 onClick(left: number, top: number): boolean;         reDraw(): { type?: undefined; x?: undefined; y?: undefined; hidetime?: undefined; } | { type: number; x: number; y: number; hidetime: number; }; } 