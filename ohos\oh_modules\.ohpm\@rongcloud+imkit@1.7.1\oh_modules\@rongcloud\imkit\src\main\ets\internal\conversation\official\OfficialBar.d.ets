// @keepTs
// @ts-nocheck
import { InputAreaController } from '../inputbar/InputAreaController';
import { PublicServiceInfo, PublicServiceMenuItem } from '@rongcloud/imlib';
import List from '@ohos.util.List';
/**
 * 公众号底部菜单
 * Created on 2024/10/21
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
export declare struct OfficialBar {
    private windowWidth;
    @Link
    inputAreaController: InputAreaController;
    @State
    publicServiceList: List<PublicServiceMenuItem>;
    private conId;
    onPublicServiceParentClickAction?: (item: PublicServiceMenuItem, index: number) => void;
    onPublicServiceChildClickAction?: (item: PublicServiceMenuItem, parentIndex: number, childIndex: number) => void;
    fetchPublicServiceInfo?: (returnData: (publicServiceInfo: PublicServiceInfo) => void) => void;
    aboutToAppear(): Promise<void>;
    build(): void;
    @Builder
    officialAccountsButton(): void;
    @Builder
    mainItemBuilder(e233: PublicServiceMenuItem, f233: number): void;
    @Builder
    ChildMenu(m232: Array<PublicServiceMenuItem> | null, n232: number): void;
    /**
     * 获取公众号列表
     */
    private getPublicServiceList;
    private isNullData;
}
