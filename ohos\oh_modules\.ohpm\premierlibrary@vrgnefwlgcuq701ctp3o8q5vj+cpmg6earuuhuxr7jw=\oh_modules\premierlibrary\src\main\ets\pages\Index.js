if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
class Index extends ViewPU {
    constructor(q45, r45, s45, t45 = -1, u45 = undefined, v45) {
        super(q45, s45, t45, v45);
        if (typeof u45 === "function") {
            this.paramsGenerator_ = u45;
        }
        this.__message = new ObservedPropertySimplePU('Hello World', this, "message");
        this.setInitiallyProvidedValue(r45);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(p45) {
        if (p45.message !== undefined) {
            this.message = p45.message;
        }
    }
    updateStateVars(o45) {
    }
    purgeVariableDependenciesOnElmtId(n45) {
        this.__message.purgeDependencyOnElmtId(n45);
    }
    aboutToBeDeleted() {
        this.__message.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    get message() {
        return this.__message.get();
    }
    set message(m45) {
        this.__message.set(m45);
    }
    initialRender() {
        this.observeComponentCreation2((k45, l45) => {
            Row.create();
            Row.height('100%');
        }, Row);
        this.observeComponentCreation2((i45, j45) => {
            Column.create();
            Column.width('100%');
        }, Column);
        this.observeComponentCreation2((f45, g45) => {
            Text.create(this.message);
            Text.fontSize(50);
            Text.fontWeight(FontWeight.Bold);
            Text.onClick(() => {
            });
        }, Text);
        Text.pop();
        Column.pop();
        Row.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
    static getEntryName() {
        return "Index";
    }
}
registerNamedRoute(() => new Index(undefined, {}), "", { bundleName: "com.aliyun.player.demo_ohos", moduleName: "premierlibrary", pagePath: "__harDefaultPagePath__", pageFullPath: "", integratedHsp: "__harDefaultIntegratedHspType__" });
export {};
