// @keepTs
// @ts-nocheck
/**
 * Created on 2024/8/13
 * <AUTHOR>
 */
import { UserInfoModel } from './UserInfoModel';
/**
 * 群成员信息
 * @version 1.0.0
 */
export declare class GroupMemberInfoModel extends UserInfoModel {
    /**
     * 所属的群组 id
     */
    groupId: string;
    /**
     * 用户在该群的备注名
     */
    nickname: string;
    /**
     * 获取 UI 展示的名字
     *```
     * 优先展示 nickName
     * 否则展示 UserInfoModel.getDisplayName()
     *```
     * @returns 名字
     */
    getDisplayName(): string;
    /**
     * 群组成员信息
     * @param groupId 群 Id
     * @param userId 用户 Id
     * @param name 用户名称
     * @param portraitUri 用户头像
     * @param alias 用户备注
     * @param extra 附加字段
     */
    constructor(n357: string, o357: string, p357: string, q357: string | Resource, r357?: string, s357?: string);
}
