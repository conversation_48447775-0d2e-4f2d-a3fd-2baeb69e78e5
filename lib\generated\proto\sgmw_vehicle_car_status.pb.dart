//
//  Generated code. Do not modify.
//  source: sgmw_vehicle_car_status.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

/// 燃油车辆状态，从车上传过来的状态
class SgmwVehicleCarStatus extends $pb.GeneratedMessage {
  factory SgmwVehicleCarStatus({
    $fixnum.Int64? time,
    $core.int? carControlType,
    $core.String? vin,
    $core.Map<$core.String, $core.double>? carLocation,
    $core.int? supplementPowerStatus,
    $core.int? airConditioningStatus,
    $core.int? driverDoorLockStatus,
    $core.int? tailgateTouchStatus,
    $core.int? engineStart,
    $core.int? skylightControlStatus,
    $core.int? driverWindowControlStatus,
    $core.int? copilotWindowControlStatus,
    $core.int? rearLeftWindowControlStatus,
    $core.int? rearRightWindowControlStatus,
    $core.int? driverSeatHeatControlStatus,
    $core.int? engineCoverStatus,
    $core.int? acTemperature,
  }) {
    final $result = create();
    if (time != null) {
      $result.time = time;
    }
    if (carControlType != null) {
      $result.carControlType = carControlType;
    }
    if (vin != null) {
      $result.vin = vin;
    }
    if (carLocation != null) {
      $result.carLocation.addAll(carLocation);
    }
    if (supplementPowerStatus != null) {
      $result.supplementPowerStatus = supplementPowerStatus;
    }
    if (airConditioningStatus != null) {
      $result.airConditioningStatus = airConditioningStatus;
    }
    if (driverDoorLockStatus != null) {
      $result.driverDoorLockStatus = driverDoorLockStatus;
    }
    if (tailgateTouchStatus != null) {
      $result.tailgateTouchStatus = tailgateTouchStatus;
    }
    if (engineStart != null) {
      $result.engineStart = engineStart;
    }
    if (skylightControlStatus != null) {
      $result.skylightControlStatus = skylightControlStatus;
    }
    if (driverWindowControlStatus != null) {
      $result.driverWindowControlStatus = driverWindowControlStatus;
    }
    if (copilotWindowControlStatus != null) {
      $result.copilotWindowControlStatus = copilotWindowControlStatus;
    }
    if (rearLeftWindowControlStatus != null) {
      $result.rearLeftWindowControlStatus = rearLeftWindowControlStatus;
    }
    if (rearRightWindowControlStatus != null) {
      $result.rearRightWindowControlStatus = rearRightWindowControlStatus;
    }
    if (driverSeatHeatControlStatus != null) {
      $result.driverSeatHeatControlStatus = driverSeatHeatControlStatus;
    }
    if (engineCoverStatus != null) {
      $result.engineCoverStatus = engineCoverStatus;
    }
    if (acTemperature != null) {
      $result.acTemperature = acTemperature;
    }
    return $result;
  }
  SgmwVehicleCarStatus._() : super();
  factory SgmwVehicleCarStatus.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SgmwVehicleCarStatus.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'SgmwVehicleCarStatus', createEmptyInstance: create)
    ..aInt64(1, _omitFieldNames ? '' : 'time')
    ..a<$core.int>(2, _omitFieldNames ? '' : 'carControlType', $pb.PbFieldType.O3, protoName: 'carControlType')
    ..aOS(3, _omitFieldNames ? '' : 'vin')
    ..m<$core.String, $core.double>(4, _omitFieldNames ? '' : 'carLocation', protoName: 'carLocation', entryClassName: 'SgmwVehicleCarStatus.CarLocationEntry', keyFieldType: $pb.PbFieldType.OS, valueFieldType: $pb.PbFieldType.OD)
    ..a<$core.int>(5, _omitFieldNames ? '' : 'supplementPowerStatus', $pb.PbFieldType.O3, protoName: 'supplementPowerStatus')
    ..a<$core.int>(6, _omitFieldNames ? '' : 'airConditioningStatus', $pb.PbFieldType.O3, protoName: 'airConditioningStatus')
    ..a<$core.int>(7, _omitFieldNames ? '' : 'driverDoorLockStatus', $pb.PbFieldType.O3, protoName: 'driverDoorLockStatus')
    ..a<$core.int>(8, _omitFieldNames ? '' : 'tailgateTouchStatus', $pb.PbFieldType.O3, protoName: 'tailgateTouchStatus')
    ..a<$core.int>(9, _omitFieldNames ? '' : 'engineStart', $pb.PbFieldType.O3, protoName: 'engineStart')
    ..a<$core.int>(10, _omitFieldNames ? '' : 'skylightControlStatus', $pb.PbFieldType.O3, protoName: 'skylightControlStatus')
    ..a<$core.int>(11, _omitFieldNames ? '' : 'driverWindowControlStatus', $pb.PbFieldType.O3, protoName: 'driverWindowControlStatus')
    ..a<$core.int>(12, _omitFieldNames ? '' : 'copilotWindowControlStatus', $pb.PbFieldType.O3, protoName: 'copilotWindowControlStatus')
    ..a<$core.int>(13, _omitFieldNames ? '' : 'rearLeftWindowControlStatus', $pb.PbFieldType.O3, protoName: 'rearLeftWindowControlStatus')
    ..a<$core.int>(14, _omitFieldNames ? '' : 'rearRightWindowControlStatus', $pb.PbFieldType.O3, protoName: 'rearRightWindowControlStatus')
    ..a<$core.int>(15, _omitFieldNames ? '' : 'driverSeatHeatControlStatus', $pb.PbFieldType.O3, protoName: 'driverSeatHeatControlStatus')
    ..a<$core.int>(16, _omitFieldNames ? '' : 'engineCoverStatus', $pb.PbFieldType.O3, protoName: 'engineCoverStatus')
    ..a<$core.int>(17, _omitFieldNames ? '' : 'acTemperature', $pb.PbFieldType.O3, protoName: 'acTemperature')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SgmwVehicleCarStatus clone() => SgmwVehicleCarStatus()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SgmwVehicleCarStatus copyWith(void Function(SgmwVehicleCarStatus) updates) => super.copyWith((message) => updates(message as SgmwVehicleCarStatus)) as SgmwVehicleCarStatus;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static SgmwVehicleCarStatus create() => SgmwVehicleCarStatus._();
  SgmwVehicleCarStatus createEmptyInstance() => create();
  static $pb.PbList<SgmwVehicleCarStatus> createRepeated() => $pb.PbList<SgmwVehicleCarStatus>();
  @$core.pragma('dart2js:noInline')
  static SgmwVehicleCarStatus getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SgmwVehicleCarStatus>(create);
  static SgmwVehicleCarStatus? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get time => $_getI64(0);
  @$pb.TagNumber(1)
  set time($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasTime() => $_has(0);
  @$pb.TagNumber(1)
  void clearTime() => clearField(1);

  @$pb.TagNumber(2)
  $core.int get carControlType => $_getIZ(1);
  @$pb.TagNumber(2)
  set carControlType($core.int v) { $_setSignedInt32(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasCarControlType() => $_has(1);
  @$pb.TagNumber(2)
  void clearCarControlType() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get vin => $_getSZ(2);
  @$pb.TagNumber(3)
  set vin($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasVin() => $_has(2);
  @$pb.TagNumber(3)
  void clearVin() => clearField(3);

  @$pb.TagNumber(4)
  $core.Map<$core.String, $core.double> get carLocation => $_getMap(3);

  /// *
  ///  补电状态
  ///  0：默认
  ///  1：补电中
  ///  2：补电完成
  ///  3：补电失败
  @$pb.TagNumber(5)
  $core.int get supplementPowerStatus => $_getIZ(4);
  @$pb.TagNumber(5)
  set supplementPowerStatus($core.int v) { $_setSignedInt32(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasSupplementPowerStatus() => $_has(4);
  @$pb.TagNumber(5)
  void clearSupplementPowerStatus() => clearField(5);

  @$pb.TagNumber(6)
  $core.int get airConditioningStatus => $_getIZ(5);
  @$pb.TagNumber(6)
  set airConditioningStatus($core.int v) { $_setSignedInt32(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasAirConditioningStatus() => $_has(5);
  @$pb.TagNumber(6)
  void clearAirConditioningStatus() => clearField(6);

  @$pb.TagNumber(7)
  $core.int get driverDoorLockStatus => $_getIZ(6);
  @$pb.TagNumber(7)
  set driverDoorLockStatus($core.int v) { $_setSignedInt32(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasDriverDoorLockStatus() => $_has(6);
  @$pb.TagNumber(7)
  void clearDriverDoorLockStatus() => clearField(7);

  @$pb.TagNumber(8)
  $core.int get tailgateTouchStatus => $_getIZ(7);
  @$pb.TagNumber(8)
  set tailgateTouchStatus($core.int v) { $_setSignedInt32(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasTailgateTouchStatus() => $_has(7);
  @$pb.TagNumber(8)
  void clearTailgateTouchStatus() => clearField(8);

  @$pb.TagNumber(9)
  $core.int get engineStart => $_getIZ(8);
  @$pb.TagNumber(9)
  set engineStart($core.int v) { $_setSignedInt32(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasEngineStart() => $_has(8);
  @$pb.TagNumber(9)
  void clearEngineStart() => clearField(9);

  @$pb.TagNumber(10)
  $core.int get skylightControlStatus => $_getIZ(9);
  @$pb.TagNumber(10)
  set skylightControlStatus($core.int v) { $_setSignedInt32(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasSkylightControlStatus() => $_has(9);
  @$pb.TagNumber(10)
  void clearSkylightControlStatus() => clearField(10);

  @$pb.TagNumber(11)
  $core.int get driverWindowControlStatus => $_getIZ(10);
  @$pb.TagNumber(11)
  set driverWindowControlStatus($core.int v) { $_setSignedInt32(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasDriverWindowControlStatus() => $_has(10);
  @$pb.TagNumber(11)
  void clearDriverWindowControlStatus() => clearField(11);

  @$pb.TagNumber(12)
  $core.int get copilotWindowControlStatus => $_getIZ(11);
  @$pb.TagNumber(12)
  set copilotWindowControlStatus($core.int v) { $_setSignedInt32(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasCopilotWindowControlStatus() => $_has(11);
  @$pb.TagNumber(12)
  void clearCopilotWindowControlStatus() => clearField(12);

  @$pb.TagNumber(13)
  $core.int get rearLeftWindowControlStatus => $_getIZ(12);
  @$pb.TagNumber(13)
  set rearLeftWindowControlStatus($core.int v) { $_setSignedInt32(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasRearLeftWindowControlStatus() => $_has(12);
  @$pb.TagNumber(13)
  void clearRearLeftWindowControlStatus() => clearField(13);

  @$pb.TagNumber(14)
  $core.int get rearRightWindowControlStatus => $_getIZ(13);
  @$pb.TagNumber(14)
  set rearRightWindowControlStatus($core.int v) { $_setSignedInt32(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasRearRightWindowControlStatus() => $_has(13);
  @$pb.TagNumber(14)
  void clearRearRightWindowControlStatus() => clearField(14);

  @$pb.TagNumber(15)
  $core.int get driverSeatHeatControlStatus => $_getIZ(14);
  @$pb.TagNumber(15)
  set driverSeatHeatControlStatus($core.int v) { $_setSignedInt32(14, v); }
  @$pb.TagNumber(15)
  $core.bool hasDriverSeatHeatControlStatus() => $_has(14);
  @$pb.TagNumber(15)
  void clearDriverSeatHeatControlStatus() => clearField(15);

  @$pb.TagNumber(16)
  $core.int get engineCoverStatus => $_getIZ(15);
  @$pb.TagNumber(16)
  set engineCoverStatus($core.int v) { $_setSignedInt32(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasEngineCoverStatus() => $_has(15);
  @$pb.TagNumber(16)
  void clearEngineCoverStatus() => clearField(16);

  @$pb.TagNumber(17)
  $core.int get acTemperature => $_getIZ(16);
  @$pb.TagNumber(17)
  set acTemperature($core.int v) { $_setSignedInt32(16, v); }
  @$pb.TagNumber(17)
  $core.bool hasAcTemperature() => $_has(16);
  @$pb.TagNumber(17)
  void clearAcTemperature() => clearField(17);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
