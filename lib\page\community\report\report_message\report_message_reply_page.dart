import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_uikit/flutter_uikit.dart';
import 'package:wuling_flutter_app/page/store/store_navbar.dart';

import '../../../../api/report_api/report_api.dart';
import '../../../../common/action.dart';
import '../../../../models/community/report/report_reply_item_model.dart';
import '../../../../utils/manager/log_manager.dart';
import '../../topic_post_details_page.dart';
import '../report_navbar.dart';

import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wuling_flutter_app/widgets/common/custom_refresher.dart';

class ReportMessageReplyPage extends StatefulWidget {
  const ReportMessageReplyPage({super.key});

  @override
  State<ReportMessageReplyPage> createState() => _ReportMessageReplyPageState();
}

class _ReportMessageReplyPageState extends State<ReportMessageReplyPage> {
  List<dynamic> dataSource = [];
  final RefreshController _refreshController =
  RefreshController(initialRefresh: false);
  int _pageNo = 1;

  @override
  void initState() {
    _onRefresh();
    super.initState();
  }

  void _onRefresh() async {
    // monitor network fetch
    _pageNo = 1;
    reportApi.getReportCommentsResponses(secondMessageType: "comments_responses",pageNo: _pageNo).then((value) {
      setState(() {
        dataSource = value;
      });
    });
    await Future.delayed(Duration(milliseconds: 1000));
    _refreshController.refreshCompleted();
  }

  void _onLoading() async {
    _pageNo += 1;
    reportApi.getReportCommentsResponses(secondMessageType: "comments_responses",pageNo: _pageNo).then((value) {
      dataSource.addAll(value);
    });
    await Future.delayed(Duration(milliseconds: 1000));
    _refreshController.loadComplete();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: UINavbar(text: "评论、回复和@",),
      backgroundColor: Color(0xFFF8F8F8),
      body: SmartRefresher(
        enablePullDown: true,
        enablePullUp: true,
        controller: _refreshController,
        onRefresh: _onRefresh,
        onLoading: _onLoading,
        header: AnimatedRefreshHeader(),
        footer: AnimatedRefreshFooter(),
        child:dataSource.isEmpty ? const Center(
          child: UIImage(width: 200,imgStr: "assets/images/community/normal.jpg",),
        ) : ListView.builder(itemBuilder: (ctx,i)=>ReportMessageReplyItem(model: dataSource[i],),itemCount: dataSource.length,)
      ),
    );
  }

  @override
  void dispose() {
    // TODO: implement dispose
    _refreshController?.dispose();
    super.dispose();
  }
}

class ReportMessageReplyItem extends StatelessWidget{

  final ReportReplyItemModel model;

  const ReportMessageReplyItem({super.key, required this.model});

  String getTypeStr(int type){
    String typeStr = "";
    if(type == 1){
      typeStr = "评论了您";
    }else if(type == 2){
      typeStr = "回复了你的评论";
    }else{
      typeStr = "提到了您";
    }
    return typeStr;
  }

  String getContent(){
    String template = model.messageContentVo["contentTemplate"];
    Map param = model.messageContentVo["paramMap"];
    if(param.containsKey("comment")){
      return template.replaceAll("{comment}", param['comment']);
    }else if(param.containsKey("postContent")){
      return template.replaceAll("{postContent}", param['postContent']);
    }else{
      return template;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 130,
      decoration: const BoxDecoration(
          color: Colors.white,
          border: Border(bottom: BorderSide(width: 1,color: Color(0xfff3f3f3)))
      ),
      padding: const EdgeInsets.symmetric(vertical: 10,horizontal: 20),
      child: Column(
        children: [
          Expanded(
            flex: 1,
            child: Row(
              children: [
                UIImage(imgStr:model.userBaseInfo['photo'],fit: BoxFit.cover,radius: 10,width: 20,height: 20,margin: EdgeInsets.only(right: 10),),
                Expanded(flex: 1,child: Text(model.userBaseInfo['nickname']),),
                Text('${DateTime.fromMillisecondsSinceEpoch(model.time).toLocal()}'),
              ],
            ),
          ),
          Row(
            children: [
              Expanded(child: GestureDetector(
                onTap: (){
                  LogManager().debug(model.skipTarget);
                  if(model.skipType == 1){
                    if(model.skipTarget != ""){
                      List<String> targets = model.skipTarget.split(",");
                      NavigatorAction.init(context,view: TopicPostDetailsPage(postId: num.parse(targets[0]).toInt(), postTypeId: 1,));
                    }
                  }
                },
                child: Container(
                  height: 80,
                  color: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 5),
                  alignment: Alignment.topLeft,
                  child: Text(getContent(),maxLines: 4,),
                ),
              )),
              UIImage(imgStr:model.image,fit: BoxFit.cover,width: 120,height: 80,),
            ],
          )
        ],
      ),
    );
  }
}