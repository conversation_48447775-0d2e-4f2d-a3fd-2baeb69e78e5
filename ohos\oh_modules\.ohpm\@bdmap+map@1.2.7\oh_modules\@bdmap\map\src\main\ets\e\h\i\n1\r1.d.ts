import type OverlayListener from "../m/i2"; import Base<PERSON> from "./q1"; import type TextStyle from "./p1";         export default class LabelUI extends BaseUI { private mText; private mStyle; private mLabelUI;         constructor();         setText(text: string): void;         getText(): string;         setStyle(style: TextStyle): void;       setListener(listener: OverlayListener): void;         getStyle(): TextStyle;         setMinLines(y46: number): boolean;         setMaxLines(maxLines: number): boolean; } 