import fs from '@ohos.file.fs';
export class FileUtils {
    static mkDirs(j3) {
        fs.mkdirSync(j3);
    }
    static remove(i3) {
        fs.rmdirSync(i3);
    }
    static createFile(f3) {
        try {
            let h3 = fs.createRandomAccessFileSync(f3);
            if (h3 != null) {
                return true;
            }
        }
        catch (g3) {
        }
        return false;
    }
    static deleteFile(e3) {
        fs.unlinkSync(e3);
    }
}
