import { SearchResult } from "../../../d/e/h/f1";
import { SearchParser } from '../../base/base';
export declare class PoiSearchParser extends SearchParser {
    private static TAG;
    private static TYPE_POI_SEARCH_CITYLIST;
    private mPageIndex;
    private mPageCapacity;
    constructor(j16: number, k16: number);
    parseSearchResult(c16: string): SearchResult;
    private parsePoiResult;
    private parsePoiInfo;
    private parsePoiDetailInfoFromJson;
    private getPoiLocation;
}
