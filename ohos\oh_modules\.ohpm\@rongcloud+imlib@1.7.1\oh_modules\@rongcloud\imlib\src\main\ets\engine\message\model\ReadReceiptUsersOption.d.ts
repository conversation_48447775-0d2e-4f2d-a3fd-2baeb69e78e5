import { ReadReceiptStatus } from "./ReadReceiptStatus";
import { Order } from "../../MacroDefine";
/**
 * 已读回执用户选项，用于已读 V5 信息主动获取时设置参数。
 *
 * @version 1.5.0
 */
export declare class ReadReceiptUsersOption {
    /**
     * 当前分页请求 Token，用于下一次请求拉取，首次可以不传
     * 在分页获取已读回执用户列表时有效，全量获取时忽略本字段
     */
    pageToken: string;
    /**
     * 分页请求条数，范围 [1, 100]
     */
    count: number;
    /**
     * 已读状态，参见 ReadReceiptStatus
     */
    readStatus: ReadReceiptStatus;
    /**
     * 排序规则，参见 Order 。默认优先显示被 AT 用户的已读未读状态
     */
    order: Order;
}
