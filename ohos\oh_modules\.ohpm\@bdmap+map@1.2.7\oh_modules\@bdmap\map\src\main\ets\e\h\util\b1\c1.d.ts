            import { BmVisibility, BmScaleMode, BmCollisionBehavior, BmLocated, BmGravity, BmFontOption, BmTextureOption, BmGradientType, BmFloorAnimateType } from "../../i/c2/d2";       export declare const FileDefine: { DOWN_IMAGE: string; SYS_IMAGE: string; TEST_IMAGE: string; };       export declare const DefaultImageSources: string[];       export declare const EventDefine: { CLICK: symbol; DBCLICK: symbol; LONGPRESS: symbol; ROTATIONSTART: symbol; ROTATIONUPDATE: symbol; ROTATIONEND: symbol; SWIPE: symbol; SWIPEDB: symbol; TOUCHSTART: symbol; TOUCHMOVE: symbol; TOUCHCANCEL: symbol; TOUCHEND: symbol; TOUCHDOUBLESTART: symbol; TOUCHDOUBLEMOVE: symbol; TOUCHDOUBLEEND: symbol; PINCHSTART: symbol; PINCHUPDATE: symbol; PINCHEND: symbol; PINCHCANCEL: symbol; MAPSTATUSCHANGE: symbol; MAPSTATUSCHANGESTART: symbol; MAPSTATUSCHANGEFINISH: symbol; MAPRENDERFINISH: symbol; MAPDESTROY: symbol; INDOORSTATUSCHANGE: symbol; TOUCH: symbol; };         export declare const EventDefineCopy: { [key: string]: symbol; };         export declare const ValueDefine: { CLICKSPAN: number; TOUCHMOVESPAN: number; TOUCHDBMOVESPAN: number; PINCHSCALE: number; ROTATION: number; TOUCHMOVETIMES: number; SPEEDMAX: number; SWIPTIME: number; NETWORKLINK: number; };         export declare const OverLayDefine: { NONE: number; LOGO: number; POPUP: number; MARKER: number; GROUND: number; LABEL: number; ARC: number; DOT: number; CIRCLE: number; POLYLINE: number; POLYGON: number; MULTIPOINT: number; PRISM: number; BM3DMODEL: number; GRADIENTLINE: number; TRACK: number; };         export declare const OverLayDefineCopy: { [key: string]: number; };             export declare enum OverlayType { INFO_WINDOW, MARKER, GROUND, LABEL, DOT, CIRCLE, POLYLINE, POLYGON }         export declare const OverlayEventDefine: { [key: string]: symbol; };         export declare enum AnimateDefine { DROP = 1, GROW = 2, JUMP = 3, NONE = 0 }         export declare const BaiduMapDefine: { MAP_TYPE_NORMAL: number; MAP_TYPE_SATELLITE: number; MAP_TYPE_NONE: number; };         export declare const InfoWindowTypeDefine: { NORMAL: number; RECTANGLE: number; };         export declare const InfoWindowLayoutDefine: { ICON_TEXT: number; TEXT_ICON: number; };         export declare const InfoWindowEventDefine: { CLICK: symbol; TOUCH: symbol; };         export declare enum LineJoinType {   BEVEL = 2048,   MITER = 8192,   ROUND = 4096,   BERZIER = 4096 }         export declare enum LineCapType {   BUTT = 2,   ROUND = 4,   SQUARE = 8 }         export declare enum LineDirectionCross {   NONE = 0,   FROM_EAST_TO_WEST = 1,   FROM_WEST_TO_EAST = 2 }         export declare enum ELineBloomType {   NONE = 0,   GRADIENTA = 1,   BLUE = 2 }         export declare enum PolylineDottedLineType {   DOTTED_LINE_SQUARE = 1,   DOTTED_LINE_CIRCLE = 2 }         export declare enum StrokeStyle {   CIRCLE = 2,   SQUARE = 1,   REAL = 0 }         export declare enum FontType { NORMAL = 0, BOLD = 1, ITALIC = 2, BOLD_ITALIC = 3 }         export declare enum FontAlign { ALIGN_LEFT = "align_left", ALIGN_RIGHT = "align_right", ALIGN_CENTER_HORIZONTAL = "align_center_horizontal", ALIGN_TOP = "align_top", ALIGN_BOTTOM = "align_bottom", ALIGN_CENTER_VERTICAL = "align_center_vertical" }         export declare const MapCRS: { BAIDULL: symbol; BAIDUMC: symbol; GCJ02: symbol; WGS84: symbol; };         export declare const MapEntryCRS: { BAIDU: symbol; GCJ02: symbol; };         export declare const MapRestrict: { DEFAULTCRS: symbol; DEFAULTLNG: number; DEFAULTLAT: number; DEFAULTOVERLOOK: number; DEFAULTROTATE: number; MAXZOOM: number; MINZOOM: number; OVERLOOKINGUNIT: number; ROTATEUNIT: number; };         export declare const ResourceRawfile: { TEXTURE_DASH_CIRCLE: string; TEXTURE_DASH_SQUARE: string; TEXTURE_DASH_NORMAL: string; };         export declare enum LayerTag {         OVERLAY = "android_sdk",         IMAGE_TILE = "sdktile",         COMPASS = "compass",         LOCATION = "location" }         export declare enum UpdateMessageType { EUPDATE_NONE = 0, ECOMPULSORY_UPDATE = 1, EUPDATE_MAPSTATUSCHANGE = 2, EUPDATE_MAPSTATUSCHANGELATER = 4, EUPDATE_TIMERESCAP = 8, EUPDATE_WAITTINGCHANGE = 16 }         export declare enum ESatelliteLayerType {         NONE = 0,         NO_ROUTE = 1,         ROUTE = 2 }         export declare enum EMapType {   MAP_TYPE_NORMAL = 1,   MAP_TYPE_SATELLITE = 2,   MAP_TYPE_NONE = 3 }               export { BmCollisionBehavior as CollisionBehavior };               export { BmLocated as Located };         export { BmGravity as Gravity };         export { BmFontOption as FontOption };         export { BmVisibility as Visibility };         export { BmScaleMode as ScaleMode };         export { BmTextureOption as TextureOption };         export declare enum UnitOption {         METER = 1,         PIXEL = 2 }         export { BmGradientType as GradientType };         export { BmFloorAnimateType as FloorAnimateType };         export declare enum SmoothType {   NONE = 1,   Bezier = 32,   Cardinal = 64,   Curve = 128 }         export declare enum MapStatusChangeReason {   REASON_GESTURE = 1,   REASON_TOUCH = 2,   REASON_DEVELOPER = 3 }         export declare enum Copyright {   LICENSE = "GS(2022)460\u53F7-\u7532\u6D4B\u8D44\u5B5711111342",   CORPORATE = "\u767E\u5EA6\u7F51\u8BAF\u79D1\u6280\u6709\u9650\u516C\u53F8" } 