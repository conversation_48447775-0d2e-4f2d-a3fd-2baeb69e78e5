import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/utils/app_config_util.dart';
import 'package:wuling_flutter_app/widgets/common/image_view.dart';
import 'package:wuling_flutter_app/models/car/car_service_model.dart';

import '../../utils/manager/log_manager.dart';
class ServiceToolButton extends StatelessWidget {
  final CarServiceModel serviceModel;
  final VoidCallback? onPressed;  // 添加点击事件处理回调

  const ServiceToolButton({
    Key? key,
    required this.serviceModel,
    this.onPressed,  // 允许传入点击事件处理器
  }) : super(key: key);

  String getImageUrl(CarServiceModel serviceModel){
    String imageUrl = '';
    if(serviceModel.serviceStatusList?.isNotEmpty ?? false){
      imageUrl = serviceModel.serviceStatusList![0].serviceStatusImage;
    }
    return imageUrl;
  }
  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        padding: EdgeInsets.zero,  // 去除默认的padding
        shadowColor: Colors.transparent, // 阴影透明
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.zero, // 去除圆角
        ),
      ),
      onPressed: onPressed,  // 绑定传入的事件处理器
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: Colors.transparent,  // 边框颜色
            width: 3,           // 边框宽度
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: Colors.transparent,  // 边框颜色
                  width: 3,           // 边框宽度
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Container(
                    height: 28,
                    width: 28,
                    child: ImageView(
                      getImageUrl(serviceModel),
                      fit: BoxFit.cover,
                    ),
                  ),
                  SizedBox(height: 3),
                  Text(
                    serviceModel.serviceName,
                    style: TextStyle(
                        fontSize: 12,
                        color: Color(0xff383A40)
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}

class ServiceToolsWidget extends StatefulWidget {
  final List<CarServiceModel> toolServiceList;
  final void Function({CarServiceModel serviceModel})? onServiceButtonClicked;
  const ServiceToolsWidget({Key? key, required this.toolServiceList, this.onServiceButtonClicked}) : super(key: key);

  @override
  _ServiceToolsWidgetState createState() => _ServiceToolsWidgetState();
}

class _ServiceToolsWidgetState extends State<ServiceToolsWidget> {
  bool isExpanded = false;

  @override
  Widget build(BuildContext context) {
    int rowItemNum = 4;
    int itemCount = widget.toolServiceList.length;
    double buttonHeight = 70.0;//按钮高度
    double sidePadding = 20.0;//两侧间距
    double topPadding = 20.0;//顶部间距
    double bottomPadding = 0.0;//底部间距
    int columnCount = (itemCount / rowItemNum).ceil();
    double fullHeight = buttonHeight * columnCount;
    double foldHeight = columnCount > 2 ? (buttonHeight * 2 - 10):buttonHeight * columnCount;
    return Container(
      color: Colors.transparent,
      child: Padding(
        padding: EdgeInsets.only(left: sidePadding, right: sidePadding,bottom: bottomPadding),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: <Widget>[
              SizedBox(height: topPadding,),
              AnimatedContainer(
                duration: Duration(milliseconds: 1000),  // 动画持续时间
                height: isExpanded ? fullHeight : foldHeight,
                curve: Curves.fastLinearToSlowEaseIn,
                child: GridView.builder(
                  padding: EdgeInsets.zero,
                  physics: const NeverScrollableScrollPhysics(), // 禁止GridView滚动
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: rowItemNum,
                    childAspectRatio: (MediaQuery.of(context).size.width - sidePadding * 2) / (rowItemNum * buttonHeight) ,
                  ),
                  itemCount: itemCount,
                  itemBuilder: (context, index) {
                    return ServiceToolButton(
                      serviceModel: widget.toolServiceList[index],
                      onPressed: (){
                        String serviceName = widget.toolServiceList[index].serviceName;
                        LogManager().debug('点击了服务按钮：$serviceName');
                        if(widget.onServiceButtonClicked != null){
                          widget.onServiceButtonClicked!(serviceModel: widget.toolServiceList[index]);
                        }

                      },
                    );
                  },
                ),
              ),
              if (columnCount > 2)
                Container(
                  color: Colors.transparent,
                  height: 35,
                  child: IconButton(
                    padding: const EdgeInsets.only(top: 5,bottom: 5, left: 50, right: 50),
                    icon: SizedBox(
                      width: 16, // 设置IconButton的尺寸
                      height: 8,
                      child: ImageView(isExpanded ? 'assets/images/use_car_page/bj_icon_tool_service_fold.png' : 'assets/images/use_car_page/bj_icon_tool_service_unfold.png'),
                    ),
                    onPressed: () {
                      setState(() {
                        isExpanded = !isExpanded;
                      });
                    },
                  )
                )
              else
                Container(
                  color: Colors.transparent,
                  height: 10,
                  width: 70,
                )
            ],
          ),
        ),
      ),
    );
  }
}