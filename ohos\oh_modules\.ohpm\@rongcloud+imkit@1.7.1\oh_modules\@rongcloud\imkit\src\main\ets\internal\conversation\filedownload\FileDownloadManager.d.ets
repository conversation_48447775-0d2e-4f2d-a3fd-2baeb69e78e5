// @keepTs
// @ts-nocheck
import { List } from '@kit.ArkTS';
import { FileDownloadListener } from './listener/FileDownloadListener';
import { EngineError } from '../../../../../../Index';
import { FileDownloadInfo } from './model/FileDownloadInfo';
export declare class FileDownloadManager {
    private static instance;
    /**
     * 消息的下载队列缓存
     *
     * 注意：
     *
     * key：文件消息或者引用文件消息的消息ID；
     *
     * value：真实的下载的消息ID，如果key是引用文件消息，则value有值，是所引用的文件消息ID。
     */
    private msgDownloadTasks;
    /**
     * 文件Url的下载队列缓存
     *
     * 注意：
     *
     * key：文件下载地址；
     *
     * value：文件下载地址对应的uniqueId。
     */
    private fileUrlDownloadTasks;
    private downloadListeners;
    private constructor();
    private connectionStatusListener;
    static getInstance(): FileDownloadManager;
    init(): void;
    addFileDownloadListener(h102: FileDownloadListener): void;
    removeFileDownloadListener(g102: FileDownloadListener): void;
    getFileDownloadListeners(): List<FileDownloadListener>;
    downloadFile(f102: FileDownloadInfo): void;
    downloadFileWithCallback(d102: FileDownloadInfo, e102: FileDownloadListener): Promise<void>;
    cancelDownloadFile(c102: FileDownloadInfo): void;
    /**
     * 根据媒体消息下载文件
     */
    private downloadMediaMessage;
    /**
     * 根据媒体消息下载文件
     */
    private downloadMediaMessageWithCallback;
    /**
     * 根据消息ID获取真实的待下载媒体消息ID，这么做的原因是该messageId有可能是文件消息、引用文件消息。
     */
    private getDownloadMediaMessage;
    /**
     * 根据消息ID获取真实的待下载媒体消息ID，这么做的原因是该messageId有可能是文件消息、引用文件消息。
     */
    private getDownloadMediaMessageId;
    /**
     * 取消媒体消息ID下载文件
     */
    private cancelDownloadMediaMessage;
    /**
     * 根据文件url下载
     */
    private downloadMediaUrl;
    private downloadMediaUrlWithCallback;
    /**
     * 根据FileDownloadInfo下载文件，下载成功返回下载地址
     * @return 返回下载成功后的路径，如果失败则返回空字符串。
     */
    downloadFileAsync(q99: FileDownloadInfo): Promise<string>;
    /**
     * 根据媒体消息ID下载文件
     * @return 返回下载成功后的路径，如果失败则返回空字符串。
     */
    private downloadMediaMessageByMessageId;
    /**
     * 根据下载地址下载文件
     * @return 返回下载成功后的路径，如果失败则返回空字符串。
     */
    downloadMediaMessageByUrl(a99: FileDownloadInfo): Promise<string>;
    /**
     * 取消文件url下载
     */
    private cancelDownloadMediaUrl;
    /**
     * 检查是否同一个FileDownloadInfo
     */
    checkFileDownloadInfo(w98: FileDownloadInfo, x98: FileDownloadInfo): boolean;
    getFileUrlDownloadPath(s98: Context, t98: FileDownloadInfo): Promise<string>;
    /**
     * 根据消息ID获取媒体消息的localPath。
     * @param messageId 消息ID
     * @returns  消息不存在、非媒体消息、未下载均返回空
     */
    getMediaMessageDownloadLocalPath(j98: number): Promise<string>;
    /**
     * 根据消息UID获取媒体消息的localPath。
     * @param messageUid 消息UID
     * @returns  消息不存在、非媒体消息、未下载均返回空
     */
    getMediaMessageDownloadLocalPathByUId(e98: string): Promise<string>;
    /**
     * 拷贝文件到沙箱
     */
    copyFileToSandbox(z97: string): Promise<string>;
    getReplaceFilePath(y97: string): string;
    isDownloading(x97: FileDownloadInfo): boolean;
    onProgress(t97: FileDownloadInfo, u97: number): void;
    onSuccess(p97: FileDownloadInfo, q97: string): void;
    onFailed(l97: FileDownloadInfo, m97: EngineError): void;
}
