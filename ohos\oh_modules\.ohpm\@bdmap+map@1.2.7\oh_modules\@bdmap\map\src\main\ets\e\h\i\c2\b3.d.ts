import BmDrawItem from "./a3"; import { BmGeoElement } from "./i3"; export default class BmBaseLine extends BmDrawItem { constructor(j9: number, k9: number);           setGeoElement(i9: BmGeoElement): any;           addGeoElement(h9: BmGeoElement): any;       clearGeoElements(): any;           setSmooth(smooth: number): any;           setThin(g9: number): any;           setSmoothFactor(factor: number): any;           setThinFactor(factor: number): any;           setStartCapType(f9: number): any;           setEndCapType(e9: number): any;           setJointType(jointType: number): any;           setCollisionTagId(d9: number): any;           setCollisionBehavior(c9: number): any;           setLineBloomMode(mode: number): any;           setBloomBlurTimes(time: number): any;           setBloomAlpha(alpha: any): any;           setBloomWidth(width: number): any;           setBloomGradientASpeed(speed: number): any;           setLineDirectionCrossType(type: number): any; } 