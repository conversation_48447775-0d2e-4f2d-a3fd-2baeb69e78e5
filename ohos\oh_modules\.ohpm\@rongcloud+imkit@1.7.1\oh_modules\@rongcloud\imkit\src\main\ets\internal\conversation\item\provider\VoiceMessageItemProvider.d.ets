// @keepTs
// @ts-nocheck
/**
 * Created on 2024/10/16
 * <AUTHOR>
 */
import { VoiceMessage } from '@rongcloud/imlib';
import { BaseMessageItemProvider } from '../../../../conversation/item/provider/BaseMessageItemProvider';
import { UiMessage } from '../../../../conversation/model/UiMessage';
export declare class VoiceMessageItemProvider extends BaseMessageItemProvider<VoiceMessage> {
    getMessageWrapBuilder(): WrappedBuilder<[
        Context,
        UiMessage,
        number
    ]>;
    isShowSummaryName(m214: Context, n214: VoiceMessage): boolean;
    getSummaryTextByMessageContent(f214: Context, g214: VoiceMessage): Promise<MutableStyledString>;
    private isDestruct;
}
@Builder
export declare function bindImageMessageData(t213: Context, u213: UiMessage, v213: number): void;
@Component
export declare struct VoiceMessageView {
    @Require
    @Prop
    index: number;
    @Require
    @Prop
    context: Context;
    @ObjectLink
    uiMessage: UiMessage;
    @State
    isShowRedForVoice: boolean;
    @State
    stateAnim: AnimationStatus;
    private voiceMessage;
    private playListener;
    aboutToAppear(): void;
    aboutToDisappear(): void;
    build(): void;
    /**
     * 播放当前语言消息
     */
    private onPlayStart;
    /**
     * 判断是否是本人
     * @returns
     */
    private isSelf;
    /**
     * 设置播放状态动画状态
     * @param state
     */
    private setStateAnim;
}
