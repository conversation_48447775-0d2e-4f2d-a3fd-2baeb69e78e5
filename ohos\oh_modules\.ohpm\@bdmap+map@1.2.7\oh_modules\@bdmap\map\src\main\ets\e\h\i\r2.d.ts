import { image } from '@kit.ImageKit'; export declare enum ImageStatusEnum { NEED_LOAD = 0, LOADING = 1, FAIL = 2 } export default class ImagesCache { private static instance; private caches; private status; private appendCalls; private timeoutT; static useMapControllerInstance: number; private constructor(); static createKey(input: string): string; has(key: string): boolean; updateTag(key: string, status: ImageStatusEnum): void; get(key: string, call?: Function): image.PixelMap | ImageStatusEnum.NEED_LOAD | ImageStatusEnum.LOADING; set(key: string, image: image.PixelMap): void; delete(key: string): void; destroy(): void; static getInstance(): ImagesCache; } 