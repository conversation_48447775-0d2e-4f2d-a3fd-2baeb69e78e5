// @keepTs
// @ts-nocheck
/**
 * Created on 2024/08/06
 * <AUTHOR>
 */
import { MessageContent } from '@rongcloud/imlib';
import { Context } from '@kit.AbilityKit';
import { IMessageItemProvider } from './IMessageItemProvider';
import { UiMessage } from '../../model/UiMessage';
/**
 * 消息 item 基类
 * @version 1.0.0
 */
export declare abstract class BaseMessageItemProvider<T extends MessageContent> implements IMessageItemProvider<T> {
    private wrap;
    constructor();
    isShowSummaryName(h26: Context, i26: T): boolean;
    getWrapBuilder(): WrappedBuilder<[
        Context,
        UiMessage,
        number
    ]>;
    builder(e26: Context, f26: UiMessage, g26: number): void;
    abstract getMessageWrapBuilder(): WrappedBuilder<[
        Context,
        UiMessage,
        number
    ]>;
    getSummaryText(c26: Context, d26: MessageContent | object): Promise<MutableStyledString>;
    /**
     * 获取当前消息类型需要在会话列表展示的数据
     * @returns 自定义样式的字符串
     */
    abstract getSummaryTextByMessageContent(a26: Context, b26: T): Promise<MutableStyledString>;
}
