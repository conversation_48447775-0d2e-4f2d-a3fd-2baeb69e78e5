import type { LatLng } from '@bdmap/base'; import type Overlay from "../i/m/m"; import type { MapStatusBundle } from "./i1"; import type BaseUI from "../i/n1/q1"; import { MapStatusChangeReason } from "../util/b1/c1";         export interface EventBundle { left: number; top: number; geo: LatLng; scale?: number; rotationAngle?: number; }         export type TMapViewEvent = EventBundle | MapStatusBundle | boolean;         export interface EventOverlayBundle extends EventBundle { target?: Overlay | Array<Overlay> | BaseUI; uiClicked?: boolean; }         export interface EventUIBundle { left: number; top: number; geo: LatLng; target: BaseUI; }   type i = 'UPDATE' | 'VISIBLE' | 'CLICK';   export type InfoMapEvent = { [key in i as string]: Array<Function>; };           export declare enum MapEvent { CLICK = "click", DOUBLECLICK = "dbclick", TOUCH = "touch", LONGPRESS = "longpress", TOUCHSTART = "touchstart", TOUCHMOVE = "touchmove", TOUCHEND = "touchend", TOUCHDOUBLESTART = "touchdoublestart", TOUCHDOUBLEMOVE = "touchdoublemove", TOUCHDOUBLEEND = "touchdoubleend", ROTATIONSTART = "rotationstart", ROTATIONUPDATE = "rotationupdate", ROTATIONEND = "rotationend", PINCHSTART = "pinchstart", PINCHUPDATE = "pinchupdate", PINCHEND = "pinchend", MAPSTATUSCHANGE = "mapstatuschange", MAPSTATUSCHANGESTART = "mapstatuschangestart", MAPSTATUSCHANGEFINISH = "mapstatuschangefinish", MAPRENDERFINISH = "MAPRENDERFINISH", INDOORSTATUSCHANGE = "indoorstatuschange", MAPDESTROY = "mapdestroy" }           export declare enum OverlayEvent { CLICK = "click", TOUCH = "touch" }           export declare enum CommonEvent {   CLICK = "click",   TOUCH = "touch" }             export declare enum Event { zoomIn = 4096, zoomOut = 4097, changeGeoLocation = 4098, panelShow = 4099, panelTouch = 4100, updateScale = 4101, MAP_FRONT = 4102, MAP_BACK = 4103, MAP_READY = 4104, ZoomCom = 4105, LocationCom = 4106, ScaleCom = 4107, Copyright = 4108, CoorsTypeChange = 4112 }         export declare enum EMapStatusCode { START = 0, CHANGE = 1, FINISH = 2 }         export type TOverlayListener = { [event_type: string | number | symbol]: Array<Function>; };         export interface TMapListener { [event_type: string | number | symbol]: Array<Function>; }         export interface TMapOverlayListener { [overlay_type: string | number | symbol]: TOverlayListener; }           export declare enum EOverLayTypeName { MARKER = "MARKER", GROUND = "GROUND", LABEL = "LABEL", DOT = "DOT", CIRCLE = "CIRCLE", POLYLINE = "POLYLINE", POLYGON = "POLYGON" }         export interface Callback<T> {           (data: T, reason?: MapStatusChangeReason): void; }         export interface TwoParamsCallback<T, Q> {           (d1: T, d2: Q): void; }               export interface NativeCallback<T, R> {           (msg: T): R; } export {}; 