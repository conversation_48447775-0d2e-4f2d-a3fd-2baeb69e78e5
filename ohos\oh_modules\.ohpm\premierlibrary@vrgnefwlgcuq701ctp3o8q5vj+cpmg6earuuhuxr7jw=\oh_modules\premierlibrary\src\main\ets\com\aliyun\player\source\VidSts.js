import { VidSourceBase } from './VidSourceBase';
export class VidSts extends VidSourceBase {
    constructor() {
        super();
        this.mVid = "";
        this.mAccessKeyId = "";
        this.mAccessKeySecret = "";
        this.mSecurityToken = "";
        this.mRegion = "";
        this.nativeGetVid = () => {
            return this.mVid;
        };
        this.nativeSetVid = (r40) => {
            this.mVid = r40;
        };
        this.nativeGetAccessKeyId = () => {
            return this.mAccessKeyId;
        };
        this.nativeSetAccessKeyId = (q40) => {
            this.mAccessKeyId = q40;
        };
        this.nativeGetAccessKeySecret = () => {
            return this.mAccessKeySecret;
        };
        this.nativeSetAccessKeySecret = (p40) => {
            this.mAccessKeySecret = p40;
        };
        this.nativeGetSecurityToken = () => {
            return this.mSecurityToken;
        };
        this.nativeSetSecurityToken = (o40) => {
            this.mSecurityToken = o40;
        };
        this.nativeGetRegion = () => {
            return this.mRegion;
        };
        this.nativeSetRegion = (n40) => {
            this.mRegion = n40;
        };
    }
    setQuality(b40, c40) {
        this.mQuality = b40;
        this.mForceQuality = c40;
    }
    getVid() {
        return this.mVid;
    }
    setVid(a40) {
        this.mVid = a40;
    }
    getSecurityToken() {
        return this.mSecurityToken;
    }
    setSecurityToken(z39) {
        this.mSecurityToken = z39;
    }
    getAccessKeyId() {
        return this.mAccessKeyId;
    }
    setAccessKeyId(y39) {
        this.mAccessKeyId = y39;
    }
    getAccessKeySecret() {
        return this.mAccessKeySecret;
    }
    setAccessKeySecret(x39) {
        this.mAccessKeySecret = x39;
    }
    getRegion() {
        return this.mRegion;
    }
    setRegion(w39) {
        this.mRegion = w39;
    }
}
