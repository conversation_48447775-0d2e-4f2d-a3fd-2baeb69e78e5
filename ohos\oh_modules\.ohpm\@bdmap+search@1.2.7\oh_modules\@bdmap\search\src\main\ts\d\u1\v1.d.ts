import { GeoCodeOption } from "./i1/w1";
import { ReverseGeoCodeOption } from "./i1/z1";
import { GeoCodeResult } from "./n1/x1";
export declare class GeoCoder {
    private iGeoCoder;
    private constructor();
    /**
     * 新建地理编码查询
     *
     * @return 地理编码查询对象
     */
    static newInstance(): GeoCoder;
    /**
     * 发起地理编码(地址信息->经纬度)请求
     *
     * @param option 请求参数 请求参数的城市、地址不为空，否则抛出异常
     *
     * @return 成功发起检索返回true , 失败返回false
     *
     * @throws Error
     */
    geocode(option: GeoCodeOption): Promise<GeoCodeResult>;
    /**
     * 发起反地理编码请求(经纬度->地址信息)
     *
     * @param option 请求参数,请求参数的坐标不为空，否则抛出异常
     *
     * @return 成功发起检索返回true , 失败返回false
     */
    reverseGeoCode(option: ReverseGeoCodeOption): Promise<import("./result/reverse_geo_code_result").ReverseGeoCodeResult>;
}
