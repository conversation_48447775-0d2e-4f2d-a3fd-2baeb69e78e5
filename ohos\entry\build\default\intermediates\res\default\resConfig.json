{"configPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json", "packageName": "com.sgmw.wuling", "output": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\entry\\build\\default\\intermediates\\res\\default", "moduleNames": "entry", "ResourceTable": ["D:\\wulingFlutter\\wuling-flutter-app\\ohos\\entry\\build\\default\\generated\\r\\default\\ResourceTable.h"], "applicationResource": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\AppScope\\resources", "moduleResources": ["D:\\wulingFlutter\\wuling-flutter-app\\ohos\\entry\\src\\main\\resources"], "dependencies": ["D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=\\oh_modules\\premierlibrary\\src\\main\\resources", "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\permission_handler_ohos@v1f9zvgtzwiwps6urt4cdkc0xmr4k3+ghajbhe99nii=\\oh_modules\\permission_handler_ohos\\src\\main\\resources", "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\device_info_plus@v+2oszykcfti10hnkmmvwgy+solmmbsrwc3c4yk1uuo=\\oh_modules\\device_info_plus\\src\\main\\resources", "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\package_info_plus@oddkx3spdpyogivhls6nikfvehqoth7jfcfacihwabg=\\oh_modules\\package_info_plus\\src\\main\\resources", "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\connectivity_plus@kqukugwl2nsdoqs00mqdcainj8gozua8ux6peiraxwi=\\oh_modules\\connectivity_plus\\src\\main\\resources", "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\mobile_scanner@fa18s1hwupccovgme0u7zrnan0ixc+q+mhbg2xvg46m=\\oh_modules\\mobile_scanner\\src\\main\\resources", "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@pura+harmony-utils@1.2.4\\oh_modules\\@pura\\harmony-utils\\src\\main\\resources", "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@bdmap+base@1.2.7\\oh_modules\\@bdmap\\base\\src\\main\\resources", "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@bdmap+search@1.2.7\\oh_modules\\@bdmap\\search\\src\\main\\resources", "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@bdmap+map@1.2.7\\oh_modules\\@bdmap\\map\\src\\main\\resources", "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\open_app_settings@yuq0uoolkhzidhegjyos7acnrx5+ddlclpefshq4g0g=\\oh_modules\\open_app_settings\\src\\main\\resources", "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\camera_ohos@eoyi02pyjbzogdkwrnb0ry+85medqnonnkge8wnwk+0=\\oh_modules\\camera_ohos\\src\\main\\resources", "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@bdmap+verify@1.0.2\\oh_modules\\@bdmap\\verify\\src\\main\\resources", "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@acpm+aio_crashsdk@rynukgn6cor9bomrriugyxyd1z7zerm+gebyzjm0l0s=\\oh_modules\\@acpm\\aio_crashsdk\\src\\main\\resources", "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@acpm+aio_util@m7v9iv6jbihoz+rwxvd+y3p2msbrx6lznc1xn3kcclu=\\oh_modules\\@acpm\\aio_util\\src\\main\\resources", "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@free+global@1.0.3\\oh_modules\\@free\\global\\src\\main\\resources", "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@rongcloud+imkit@1.7.1\\oh_modules\\@rongcloud\\imkit\\src\\main\\resources", "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@rongcloud+imlib@1.7.1\\oh_modules\\@rongcloud\\imlib\\src\\main\\resources", "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@ohos+mqtt@2.0.23\\oh_modules\\@ohos\\mqtt\\src\\main\\resources", "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\entry\\build\\default\\intermediates\\res\\default\\resource_str"], "iconCheck": true, "compression": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\entry\\build\\default\\intermediates\\res\\default\\opt-compression.json", "ids": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\entry\\build\\default\\intermediates\\res\\default\\ids_map", "definedIds": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\entry\\build\\default\\intermediates\\res\\default\\ids_map\\id_defined.json", "definedSysIds": "D:\\Huawei\\DevEco Studio\\sdk\\default\\hms\\toolchains\\id_defined.json"}