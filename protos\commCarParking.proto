syntax = "proto3";
//主题： comm/{env}/vehicle/parking/status/{vin} ,其中测试环境env为pre，生产环境env为prod
//如若UCU在整车CAN采集不到相关数据，默认填充255上报(string格式，内容为255)
message CarParkingStatus {
  int64  collectTime = 1; 	//状态更新时间，unix时间戳，毫秒级
  string APPInfcSw = 2;   	// 应用程序界面切换
  string VehCntrlSt = 3; 	//整车控制状态，0 本地模式 1 远程启动模式 2 泊车上电模式
  string IPAOperSts = 4; 	//智能泊车辅助运行状态
  string VPAOperSts = 5; 	//记忆泊车运行状态
  string IPAFailrReas = 6; 	//智能泊车辅助IPA失败原因
  string VPAgFailrReas = 7; //记忆泊车失败原因
}