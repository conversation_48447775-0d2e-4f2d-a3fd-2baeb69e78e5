import { TaxiInfo } from "../../../../d/e/h/f1";
import { SdkInnerError } from "../../../base/f1";
export declare namespace WalkJson {
    interface WalkJsonParseResult {
        taxi?: TaxiInfo;
        result?: Result;
        walk?: Walk;
        traffic_pois?: TrafficPois;
        SDK_InnerError?: SdkInnerError;
    }
    interface TrafficPois {
        content: string[];
    }
    interface Result {
        type: number;
        error: number;
    }
    interface Walk {
        routes: Route[];
        option: Option;
    }
    interface Option {
        total: number;
        sy: number;
        start: Start;
        end: Start[];
        start_city: City;
        end_city: City[];
    }
    interface Start {
        pt: string;
        wd: string;
        uid: string;
        bus_stop: number;
    }
    interface City {
        code: number;
        cname: string;
    }
    interface Route {
        legs?: Leg[];
    }
    interface Leg {
        distance: number;
        duration: number;
        end_location: string;
        start_location: string;
        steps: Step[];
    }
    interface Step {
        distance: number;
        duration: number;
        end_location: string;
        start_location: string;
        instructions: string;
        path: string;
        direction: number;
        end_instructions: string;
        start_instructions: StartInstructions;
    }
    enum StartInstructions {
        Empty = ""
    }
}
