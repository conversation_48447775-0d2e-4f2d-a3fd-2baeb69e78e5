import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/utils/manager/loading_manager.dart';
import '../../models/car/car_control_setting_model.dart';
import '../../utils/manager/log_manager.dart';
import '../../utils/manager/temperature_setting_picker_manager.dart';
import '../base_page/base_page.dart';
import '../../api/car_api.dart';

class BanmaTemperatureSettingPage extends BasePage {

  BanmaTemperatureSettingPage({super.key})
      : super(
    appBarTitle: '空调温度设置',
    initialStatusBarBrightness: Brightness.dark,
  );

  @override
  _BanmaTemperatureSettingPageState createState() => _BanmaTemperatureSettingPageState();
}

class _BanmaTemperatureSettingPageState extends BasePageState<BanmaTemperatureSettingPage> {

  final TextEditingController _temperatureController = TextEditingController();

  /// 制冷的温度列表
  List<String> coolTemperatureList = [];
  /// 制热的温度列表
  List<String> hotTemperatureList = [];
  /// 制热的最低温度
  final int MIN_HOT_TEMPERATURE = 23;
  /// 制热的最高温度
  final int MAX_HOT_TEMPERATURE = 28;
  /// 制冷的最低温度
  final int MIN_COOL_TEMPERATURE = 16;
  /// 制冷的最高温度
  final int MAX_COOL_TEMPERATURE = 25;
  ///制热温度
  late int mAcHeatingTemperature = 24;
  ///制冷温度
  late int mAcRefrigerationTemperature = 24;
  ///调整类型（制热）
  final int TYPE_HOT = 1;
  ///调整类型（制冷）
  final int TYPE_COOL = 2;

  @override
  void initState() {
    super.initState();
    _getTemperature();
    _initTemperature();
  }

  void _getTemperature() {
    carAPI.getTemperatureSetting().then((List<CarControlSettingModel> carControlSettings){
      setState(() {
        if (carControlSettings != null) {
          for (CarControlSettingModel carControlSetting in carControlSettings) {
            if ('acRefrigerationTemperature' == carControlSetting.configCode) {
              mAcRefrigerationTemperature = int.parse(carControlSetting.configValue);
            }
            if ('acHeatingTemperature' == carControlSetting.configCode) {
              mAcHeatingTemperature = int.parse(carControlSetting.configValue);
            }
          }
        }
      });
    });
  }

  ///请求设置温度
  void requestSetTemperature(final int type, final int temperature) {
    String ConfigCode = '';
    String Status = '1';
    if (type == TYPE_COOL) {
      ConfigCode = 'acRefrigerationTemperature';
    } else {
      ConfigCode = 'acHeatingTemperature';
    }
    Map<String, dynamic> map = {
      "configCode": ConfigCode,
      "status" : Status,
    };

    carAPI.updateCarSetting(map).then((value){
      LoadingManager.showToast('温度设置成功！');
      //如果是制冷，则设置制冷温度
      setState(() {
        if (type == TYPE_COOL) {
          mAcRefrigerationTemperature = temperature;
        } else {
          mAcHeatingTemperature = temperature;
        }
      });
    });
  }

  /// 初始化温度
  void _initTemperature() {
    for (int i = MIN_COOL_TEMPERATURE; i <= MAX_COOL_TEMPERATURE; i++) {
      coolTemperatureList.add('$i℃');
    }
    for (int i = MIN_HOT_TEMPERATURE; i <= MAX_HOT_TEMPERATURE; i++) {
      hotTemperatureList.add('$i℃');
    }
  }

  @override
  Widget buildPageContent(BuildContext context) {
    return Column(
      children: [
        GestureDetector(
        onTap: () {
          // 点击事件的处理逻辑
          TemperatureSettingPickerManager.showTemperatureSettingPicker(
              context: context, temperatureList: hotTemperatureList,currentTemperature : mAcHeatingTemperature, type: TYPE_HOT,
              onTemperatureSettingSelected:(int? type,int? temperature){
                LogManager().debug('Container clicked!');
              }
          );
        },
        child:Container(
            padding: const EdgeInsets.all(16.0),
            decoration: const BoxDecoration(
              color: Colors.white,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '制热',
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 18.0,
                  ),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '$mAcHeatingTemperature°C',
                      style: const TextStyle(
                        color: Colors.black,
                        fontSize: 18.0,
                        fontWeight: FontWeight.normal,
                      ),
                    ),
                    const SizedBox(width: 10,),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(0, 0, 0, 2),
                      child: Center(
                        child: Image.asset(
                          'assets/images/use_car_page/car_setting_temperature/ic_temperature_edit.png', // 指定图片路径
                          width: 20, // 可选：设置图片宽度
                          height: 20, // 可选：设置图片高度
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        GestureDetector(
          onTap: () {
            // 点击事件的处理逻辑
            TemperatureSettingPickerManager.showTemperatureSettingPicker(
                context: context, temperatureList: coolTemperatureList, currentTemperature : mAcRefrigerationTemperature, type: TYPE_COOL,
                onTemperatureSettingSelected:(int? type,int? temperature){
                  LogManager().debug('Container clicked!');
                }
            );
          },
        child:Container(
            padding: const EdgeInsets.all(16.0),
            decoration: const BoxDecoration(
              color: Colors.white,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  '制冷',
                  style: TextStyle(
                    color: Colors.black,
                    fontSize: 18.0,
                  ),
                ),
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '$mAcRefrigerationTemperature°C',
                      style: const TextStyle(
                        color: Colors.black,
                        fontSize: 18.0,
                        fontWeight: FontWeight.normal,
                      ),
                    ),
                    const SizedBox(width: 10,),
                    Padding(
                      padding: const EdgeInsets.fromLTRB(0, 0, 0, 2),
                      child: Center(
                        child: Image.asset(
                          'assets/images/use_car_page/car_setting_temperature/ic_temperature_edit.png', // 指定图片路径
                          width: 20, // 可选：设置图片宽度
                          height: 20, // 可选：设置图片高度
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

}