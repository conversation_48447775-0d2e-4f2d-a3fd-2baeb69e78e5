import { Timeout } from './Timeout';
export declare class CaptureTimeoutsWrapper {
    private preCaptureFocusing;
    private preCaptureMetering;
    private preCaptureFocusingTimeoutMs;
    private preCaptureMeteringTimeoutMs;
    constructor(preCaptureFocusingTimeoutMs: number, preCaptureMeteringTimeoutMs: number);
    reset(): void;
    getPreCaptureFocusing(): Timeout;
    getPreCaptureMetering(): Timeout;
}
