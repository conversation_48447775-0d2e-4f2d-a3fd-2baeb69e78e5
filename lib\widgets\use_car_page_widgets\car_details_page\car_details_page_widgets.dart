import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/utils/manager/log_manager.dart';
import '../../../models/index.dart';
import '../../../utils/manager/location_manager.dart';
import '../../common/image_view.dart';
import 'package:wuling_flutter_app/extensions/car/car_status_model_extensions.dart';

// 电池充电状态枚举
enum EVChargeStatus {
  normal(0), // 正常
  lacking(10000), // 亏电，亏电蓄
  supplementing(200), // 补电中
  supplemented(300); // 补电结束

  final int value;
  const EVChargeStatus(this.value);
}

// 电池状态展示枚举
enum EVShowBatteryStatus {
  heating(1), // 加热中
  charging(2), // 充电中
  keeping(3), // 保温中
  kept(4), // 保温完成
  supplementing(5); // 补电中

  final int value;
  const EVShowBatteryStatus(this.value);
}

class CarImageWidget extends StatelessWidget {
  final String imageUrl;
  final CarNewEnergyBatteryModel? newEnergyBatteryModel;
  final ValueChanged<String>? onTap; // 点击回调函数，返回字符串数据
  const CarImageWidget(
      {Key? key,
      required this.imageUrl,
      this.newEnergyBatteryModel,
      this.onTap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.transparent,
      padding: const EdgeInsets.only(top: 50, bottom: 25, left: 25, right: 25),
      child: AspectRatio(
        aspectRatio: 325 / 147,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8.0), // 可选：添加圆角
          child: Stack(
            alignment: Alignment.center,
            children: [
              ImageView(
                imageUrl,
                fit: BoxFit.contain,
              ),
              if ((newEnergyBatteryModel?.status ?? '').isNotEmpty &&
                  newEnergyBatteryModel?.status != '0')
                EVBatteryStatusWidget(
                  newEnergyBatteryModel: newEnergyBatteryModel,
                  onTap: onTap,
                )
            ],
          ),
        ),
      ),
    );
  }
}

// ignore: must_be_immutable
class CarAddressWidget extends StatelessWidget {
  final CarStatusModel? statusModel;
  final String? carName;
  String? addressStr;
  CarAddressWidget({Key? key, this.statusModel, this.carName})
      : super(key: key);

  Future<String> updateCarPosition(
      carPosition, carLatitude, carLongitude) async {
    if (carPosition.isEmpty || carPosition == 'null') {
      if (carLatitude != "null" &&
          carLongitude != "null" &&
          carLatitude.isNotEmpty &&
          carLongitude.isNotEmpty) {
        try {
          String address = await LocationManager.getAddressFromLocation(
            carLatitude,
            carLongitude,
          );
          // 格式化地址：处理门牌号中的点号分隔问题
          String formattedAddress = _formatAddress(address);
          addressStr = formattedAddress;
          return formattedAddress; // 更新地址
        } catch (e) {
          return '获取车况位置信息失败';
        }
      } else {
        return '获取车况位置信息失败';
      }
    } else {
      // 对已有的地址也进行格式化处理
      String formattedAddress = _formatAddress(carPosition);
      addressStr = formattedAddress;
      return formattedAddress;
    }
  }

  /// 格式化地址字符串，处理门牌号中的点号分隔问题
  /// 例如：将 "河西路18号*******号" 转换为 "河西路18号1234号"
  String _formatAddress(String address) {
    // 使用正则表达式匹配门牌号中的点号分隔数字模式
    // 匹配模式：数字.数字.数字.数字号 或类似的模式
    return address.replaceAllMapped(
      RegExp(r'(\d+(?:\.\d+)+)号'),
      (Match match) {
        String numberPart = match.group(1)!;
        // 去掉所有点号，保留数字和字母
        String cleanedNumber = numberPart.replaceAll('.', '');
        return '${cleanedNumber}号';
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    double screenW = MediaQueryData.fromWindow(window).size.width;
    double maxW = screenW - 40 - 45;
    return Container(
      margin: const EdgeInsets.only(left: 20, right: 20),
      height: 25,
      color: const Color.fromRGBO(244, 244, 244, 1.0),
      child: GestureDetector(
        onTap: () {
          // LogManager().debug('点击地图');
          // 确保传递格式化后的地址
          String formattedAddress =
              addressStr != null ? _formatAddress(addressStr!) : '';
          LocationManager.jumpMap(carName ?? '', formattedAddress,
              statusModel?.latitude ?? '', statusModel?.longitude ?? '');
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                const SizedBox(width: 8),
                const Image(
                  width: 12,
                  height: 12,
                  image: AssetImage(
                      'assets/images/use_car_page/car_details_address.png'),
                ),
                const SizedBox(width: 3),
                FutureBuilder<String>(
                  future: updateCarPosition(statusModel?.position,
                      statusModel?.latitude, statusModel?.longitude),
                  builder: (context, snapshot) {
                    return Container(
                      width: maxW,
                      child: Text(
                        snapshot.data ?? '车辆位置获取中...',
                        style: const TextStyle(
                          color: Color(0xFF868990),
                          fontSize: 12,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    );
                  },
                )
              ],
            ),
            const Icon(
              Icons.chevron_right,
              size: 20,
              color: Color.fromRGBO(119, 124, 134, 1),
            ),
          ],
        ),
      ),
    );
  }
}

class CarInfoItem extends StatelessWidget {
  final String topText;
  final Color mainValueColor;
  final String mainValue;
  final String unit;
  final bool showProgressBar;
  final bool showCenterImage;
  final double progressValue; // 0.0 到 1.0 之间
  final Color progressColor;
  final Color backgroundColor;

  const CarInfoItem({
    Key? key,
    required this.topText,
    this.mainValueColor = const Color(0xff384967),
    required this.mainValue,
    required this.unit,
    this.showProgressBar = false,
    this.showCenterImage = false,
    this.progressValue = 0.0,
    this.progressColor = const Color(0xff384967),
    this.backgroundColor = Colors.black12,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        // 顶部文本
        Text(
          topText,
          style: TextStyle(fontSize: 11, color: Colors.grey[600]),
        ),
        SizedBox(height: 4), // 添加一些间距

        // 主值和单位
        Row(
          crossAxisAlignment: CrossAxisAlignment.baseline,
          textBaseline: TextBaseline.alphabetic,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              mainValue,
              style: TextStyle(
                  fontSize: 23,
                  fontWeight: FontWeight.w500,
                  color: mainValueColor),
            ),
            SizedBox(width: 2),
            Text(
              unit,
              style: TextStyle(fontSize: 11, color: Colors.grey[600]),
            ),
          ],
        ),
        // 进度条
        if (showProgressBar)
          Stack(
            alignment: Alignment.center,
            children: [
              Container(
                width: 76,
                height: 6,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(3),
                  child: LinearProgressIndicator(
                    value: progressValue,
                    backgroundColor: backgroundColor,
                    valueColor: AlwaysStoppedAnimation<Color>(progressColor),
                  ),
                ),
              ),
              SizedBox(
                width: 12.5,
                height: 15,
                child: showCenterImage
                    ? ImageView(
                        'assets/images/use_car_page/older_ev_page/newEnergy_charge.png',
                        fit: BoxFit.cover,
                      )
                    : null,
              ),
            ],
          ),
      ],
    );
  }
}

class CarDataItem extends StatelessWidget {
  final String name;
  final String numStr;
  final String unit;
  const CarDataItem(
      {Key? key, required this.name, required this.unit, this.numStr = '0'})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(left: 30, right: 30),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.baseline,
        textBaseline: TextBaseline.alphabetic,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            name,
            style: const TextStyle(
              color: Color(0xFF868990),
              fontSize: 11,
            ),
          ),
          const Spacer(),
          Text(
            numStr,
            style: const TextStyle(
                fontSize: 17,
                fontWeight: FontWeight.w500,
                color: Color(0xff384967)),
          ),
          const SizedBox(width: 2),
          Text(
            unit,
            style: const TextStyle(
              color: Color(0xFF868990),
              fontSize: 11,
            ),
          ),
        ],
      ),
    );
  }
}

class CarInfoWidget extends StatelessWidget {
  final CarStatusModel? statusModel;
  final CarInfoModel? infoModel;
  final String yesterdayMileage;
  const CarInfoWidget(
      {Key? key, this.statusModel, this.infoModel, this.yesterdayMileage = '0'})
      : super(key: key);

  Color getColorGradeWithCarStatus(CarStatusModel? status) {
    // 电量颜色 10%: FF5A5A  30%: FFCF5A  30%以上：7ADA62
    Color elecColor = Color(0xFF7ADA62);
    // 新电量颜色显示逻辑
    if (status?.batteryIndicate != null && status?.batteryIndicate == "0") {
      elecColor = Color(0xFF7ADA62);
    } else if (status?.batteryIndicate != null &&
        status?.batteryIndicate == "1") {
      elecColor = Color(0xFFFF5A5A);
    } else {
      // 旧电量颜色显示逻辑
      elecColor = Color(0xFF7ADA62); // 默认30%以上的颜色
      // 将 batterySoc 从 String? 转换为 int?
      int? batterySocInt = int.tryParse(status?.batterySoc ?? "");
      if (batterySocInt != null) {
        if (batterySocInt <= 20) {
          elecColor = Color(0xFFFF5A5A);
        } else if (batterySocInt <= 30) {
          elecColor = Color(0xFFFFCF5A);
        }
      }
    }

    return elecColor;
  }

  @override
  Widget build(BuildContext context) {
    const double padding = 30;
    const double dataPadding = 20;
    double? batterySoc = double.parse(statusModel?.batterySoc ?? '0');
    double avgFuel = statusModel?.avgFuel ?? 0.0;
    String avgFuelStr = avgFuel.toStringAsFixed(1);
    return Padding(
      padding: const EdgeInsets.only(left: 20.0, right: 20),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 4,
              offset: Offset(0, 0),
            ),
          ],
        ),
        child: Column(
          children: [
            const SizedBox(height: padding),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                    child: CarInfoItem(
                  topText: '剩余电量',
                  mainValue: statusModel?.batterySoc ?? '--',
                  unit: '%',
                  mainValueColor: getColorGradeWithCarStatus(statusModel),
                  showProgressBar: true,
                  showCenterImage: statusModel?.isCharging() ?? false,
                  progressValue: batterySoc / 100.0,
                  progressColor: getColorGradeWithCarStatus(statusModel),
                )),
                Expanded(
                    child: CarInfoItem(
                  topText: '剩余里程数',
                  mainValue: statusModel?.leftMileage ?? '--',
                  unit: 'km',
                  showProgressBar: true,
                  progressValue: batterySoc / 100.0,
                )),
              ],
            ),
            const SizedBox(height: padding),
            CarDataItem(
                name: '行驶里程', unit: 'km', numStr: statusModel?.mileage ?? '--'),
            const SizedBox(height: dataPadding),
            CarDataItem(name: '昨日里程', unit: 'km', numStr: yesterdayMileage),
            SizedBox(
                height: ((statusModel?.hybridMileage?.isNotEmpty ?? false) &&
                        (statusModel?.hybridMileage?.length ?? 0) > 0)
                    ? dataPadding
                    : 0),
            Visibility(
              visible: (statusModel?.hybridMileage?.isNotEmpty ?? false) &&
                  (statusModel?.hybridMileage?.length ?? 0) > 0,
              child: CarDataItem(
                  name: '混动里程',
                  unit: 'km',
                  numStr: statusModel?.hybridMileage ?? '--'),
            ),
            SizedBox(
                height: (statusModel != null &&
                        statusModel!.isCharging() &&
                        statusModel!.chargePower.isNotEmpty &&
                        (statusModel?.chargePower.length ?? 0) > 0)
                    ? dataPadding
                    : 0),
            Visibility(
              visible: (statusModel != null &&
                  statusModel!.isCharging() &&
                  statusModel!.chargePower.isNotEmpty &&
                  (statusModel?.chargePower.length ?? 0) > 0),
              child: CarDataItem(
                  name: '充电功率',
                  unit: 'kw',
                  numStr: statusModel?.chargePower ?? '--'),
            ),
            SizedBox(
                height:
                    infoModel?.supportAvgElectronFuel == 1 ? dataPadding : 0),
            Visibility(
              visible: infoModel?.supportAvgElectronFuel == 1,
              child: CarDataItem(
                  name: '平均电耗',
                  unit: 'kW·h/100km',
                  numStr: statusModel?.avgElectronFuel ?? '--'),
            ),
            SizedBox(
                height: (infoModel?.controlView == 8) && avgFuelStr.isNotEmpty
                    ? dataPadding
                    : 0),
            Visibility(
              visible: (infoModel?.controlView == 8) && avgFuelStr.isNotEmpty,
              child: CarDataItem(name: '平均油耗', unit: 'L', numStr: avgFuelStr),
            ),
            const SizedBox(height: padding),
          ],
        ),
      ),
    );
  }
}

enum SettingItemType { toggle, button }

class SettingItemWidget extends StatelessWidget {
  final String leftIconPath;
  final String title;
  final String? rightIconPath;
  final VoidCallback? onRightIconTap;
  final SettingItemType rightType;
  final String? buttonText;
  final Color buttonColor;
  final bool isButtonEnable;
  final String? toggleOnImageUrl;
  final String? toggleOffImageUrl;
  final bool isToggleOn;
  final VoidCallback? onToggle;

  const SettingItemWidget({
    Key? key,
    required this.leftIconPath,
    required this.title,
    this.rightIconPath,
    this.onRightIconTap,
    required this.rightType,
    this.buttonText,
    this.buttonColor = const Color(0xff384967),
    this.isButtonEnable = true,
    this.toggleOnImageUrl,
    this.toggleOffImageUrl,
    this.isToggleOn = false,
    this.onToggle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 53,
      padding: EdgeInsets.symmetric(horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              SizedBox(
                height: 20,
                width: 20,
                child: ImageView(
                  leftIconPath,
                  fit: BoxFit.cover,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(fontSize: 16),
              ),
              if (rightIconPath != null) ...[
                const SizedBox(width: 6),
                GestureDetector(
                  onTap: onRightIconTap,
                  child: SizedBox(
                    height: 18,
                    width: 18,
                    child: ImageView(
                      rightIconPath!,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ],
            ],
          ),
          if (rightType == SettingItemType.button)
            GestureDetector(
              onTap: isButtonEnable ? onToggle : null,
              child: Container(
                width: 71,
                height: 25,
                decoration: BoxDecoration(
                  color: isButtonEnable ? buttonColor : const Color(0xffc3c8d1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Center(
                  child: Text(
                    buttonText ?? '',
                    style: TextStyle(color: Colors.white, fontSize: 12),
                  ),
                ),
              ),
            )
          else if (rightType == SettingItemType.toggle)
            GestureDetector(
              onTap: onToggle,
              child: SizedBox(
                width: 71,
                height: 25,
                child: ImageView(
                  isToggleOn ? toggleOnImageUrl! : toggleOffImageUrl!,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class EVBatteryStatusWidget extends StatelessWidget {
  final CarNewEnergyBatteryModel? newEnergyBatteryModel;
  final ValueChanged<String>? onTap; // 点击回调函数，返回字符串数据

  const EVBatteryStatusWidget(
      {Key? key,
      this.onTap, // 接收回调函数
      this.newEnergyBatteryModel})
      : super(key: key);

  String getBatteryStatusImageUrl() {
    String url = '';
    int statusValue = int.parse(newEnergyBatteryModel?.status ?? '0');
    if (statusValue == EVShowBatteryStatus.heating.value) {
      url =
          'assets/images/use_car_page/older_ev_page/ev_show_battery_status_heating.png';
    } else if (statusValue == EVShowBatteryStatus.charging.value) {
      url =
          'assets/images/use_car_page/older_ev_page/ev_show_battery_status_charging.png';
    } else if (statusValue == EVShowBatteryStatus.keeping.value) {
      url =
          'assets/images/use_car_page/older_ev_page/ev_show_battery_status_keeping.png';
    } else if (statusValue == EVShowBatteryStatus.kept.value) {
      url =
          'assets/images/use_car_page/older_ev_page/ev_show_battery_status_hept.png';
    } else if (statusValue == EVShowBatteryStatus.supplementing.value) {
      url =
          'assets/images/use_car_page/older_ev_page/ev_show_battery_status_supplementing.png';
    }
    return url;
  }

  @override
  Widget build(BuildContext context) {
    String imageUrl = getBatteryStatusImageUrl();
    return GestureDetector(
      onTap: () {
        // 点击时触发回调，并返回字符串数据
        if (onTap != null) {
          onTap!(newEnergyBatteryModel?.statusToast ?? '');
        }
      },
      child: Container(
        width: 58,
        height: 58,
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.38), // 设置背景色为alpha值0.38的黑色
          shape: BoxShape.circle, // 圆形背景
        ),
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                newEnergyBatteryModel?.statusName ?? '',
                style: const TextStyle(
                  color: Colors.white, // 字体颜色为白色
                  fontSize: 11, // 字体大小为11
                ),
              ),
              const SizedBox(height: 4), // 文本与图片之间的间距，可以调整
              SizedBox(
                width: 25, // 图片宽度为25
                height: 16.5, // 图片高度为16.5
                child: imageUrl.isNotEmpty
                    ? ImageView(
                        imageUrl,
                        fit: BoxFit.contain, // 保持图片的宽高比
                      )
                    : null,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
