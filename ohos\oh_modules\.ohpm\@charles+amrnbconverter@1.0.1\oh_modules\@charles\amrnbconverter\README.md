# amrnbConverter
## 简介
amrnbConverter，amrnb音频格式转换库，基于opencore-amr开源库 (<https://sourceforge.net/projects/opencore-amr/>) 移植并封装了api。便于开发者在业务开发中使用。

目前支持的功能：
- amrnb格式音频文件解码为pcm格式音频文件
- 采样位数为16位的pcm格式音频文件编码为amrnb格式


## 下载安装

`ohpm install @charles/amrnbconverter`


## Demo示例
请参考 <https://github.com/619216696/amrConverterDemo>

## API使用

### amrnb音频格式解码为pcm原始格式

```
import { nativeConvertAmrToPcm } from '@charles/amrnbconverter'

// amr文件的路径
const amrFilePath = '沙箱路径/source.amr'
// 需要编码生成的pcm文件路径
const pcmFilePath = '沙箱路径/dest.pcm'
// 调用api进行解码
const result = await nativeConvertAmrToPcm(amrFilePath, pcmFilePath)
```

### pcm原始格式编码为amrnb音频格式 (目前仅支持采样位数为16的pcm格式编码)

```
import { nativeConvertPcmToAmr } from '@charles/amrnbconverter'

// pcm文件路径
const pcmFilePath = '沙箱路径/souce.pcm'
// amr文件的路径
const amrFilePath = '沙箱路径/dest.amr'
// 调用api进行编码
const result = await nativeConvertPcmToAmr(pcmFilePath, amrFilePath)
```

## 反馈建议
联系邮箱: *<EMAIL>*