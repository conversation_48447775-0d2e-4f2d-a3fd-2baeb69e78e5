          import { LineJoinType } from "../../util/b1/c1"; import Stroke from "../o/p"; import Bundle from "../o/i1"; import Overlay from "./m"; import { LatLng } from '@bdmap/base'; import type { ColorString, IPolygonOption, Nullable } from "../../g1/a2"; import type { ImageOverlayData } from "../../g1/i1"; import type OverlayMgr from "./j2"; import type OverlayListener from "./i2";             export default class Polygon extends Overlay { private mPoints; private mHolePoints; private mFillcolor; private mStroke; private mThin; private mThinFactor; private mJointType; private bmPolygon; private bmGeoElements; private bmHoleGeoElements; private bmSurfaceStyle; private mIsHoleClickable;                         constructor(c42: IPolygonOption);       init(): void;       setListener(listener: OverlayListener): void;         fillcolor(color?: ColorString): this;         getFillcolor(): any;         setFillcolor(color: ColorString): void;         points(points: Array<LatLng>): this;       private packBmGeoElement;       private createBmGeoElement;       private setBmStrokeStyle;       preUpdatePoint(): void;         getPoints(): Array<LatLng>;         setPoints(points: Array<LatLng>): void;         holePoints(points: Array<LatLng> | Array<Array<LatLng>>): this;         setHolePoints(points: Array<LatLng> | Array<Array<LatLng>>): void;         getHolePoints(): LatLng[] | LatLng[][];       private createHoleBmGeoElement;         getStroke(): Stroke;         stroke(stroke: Stroke): this;         setStroke(stroke: Stroke): void;         thin(val: boolean): void;         setThin(val: boolean): void;         getThin(): boolean;         thinFactor(val: number): this;         setThinFactor(val: number): void;         getThinFactor(): number;         jointType(val: LineJoinType): this;         setJointType(val: LineJoinType): void;         getJointType(): LineJoinType;         isHoleClickable(val: boolean): this;         setIsHoleClickable(val: boolean): void;         getIsHoleClickable(): boolean; get typeName(): string;           emitByStroke(): void;       dataFormat(p41: Array<ImageOverlayData>): Nullable<Bundle>;       toBundle(k41: OverlayMgr): Promise<Bundle>;       toString(): string; } 