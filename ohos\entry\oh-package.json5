{"name": "entry", "version": "1.0.0", "description": "Please describe the basic information.", "main": "", "author": "", "license": "", "dependencies": {"premierlibrary": "file:./libs/premierlibrary.har", "shared_preferences_ohos": "file:../har/shared_preferences_ohos.har", "webview_flutter_ohos": "file:../har/webview_flutter_ohos.har", "path_provider_ohos": "file:../har/path_provider_ohos.har", "url_launcher_ohos": "file:../har/url_launcher_ohos.har", "image_picker_ohos": "file:../har/image_picker_ohos.har", "permission_handler_ohos": "file:../har/permission_handler_ohos.har", "device_info_plus": "file:../har/device_info_plus.har", "package_info_plus": "file:../har/package_info_plus.har", "flutter_blue_plus_ohos": "file:../har/flutter_blue_plus_ohos.har", "flutter_blue_plus": "file:../har/flutter_blue_plus.har", "iamgeqr_flutter_plugin": "file:../har/iamgeqr_flutter_plugin.har", "connectivity_plus": "file:../har/connectivity_plus.har", "mobile_scanner": "file:../har/mobile_scanner.har", "@pura/harmony-utils": "1.2.4", "@bdmap/base": "1.2.7", "@bdmap/search": "1.2.7", "@bdmap/map": "1.2.7", "open_app_settings": "file:../har/open_app_settings.har", "fluwx": "file:../har/fluwx.har", "@tencent/wechat_open_sdk": "^1.0.11", "camera_ohos": "file:../har/camera_ohos.har"}}