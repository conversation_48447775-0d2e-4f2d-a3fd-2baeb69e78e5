import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/page/base_page/base_page.dart';
import 'package:wuling_flutter_app/widgets/common/empty_widget.dart';

import '../../routes/app_routes.dart';
import '../../utils/manager/loading_manager.dart';
import '../../utils/manager/log_manager.dart';
import '../../widgets/profile_page_widgets/system_setting_page_widgets/setting_cell.dart';
import '../../widgets/profile_page_widgets/system_setting_page_widgets/setting_page_item.dart';
import '../../widgets/webview/webview.dart';

class SettingSearchPage extends BasePage {
  SettingSearchPage({Key? key, Color pageBackgroundColor = Colors.white})
      : super(
    key: key,
    hideAppBar: true, // 保留顶部标题栏
    isWithinSafeArea: true, // 页面内容包含在SafeArea内
    pageBackgroundColor: pageBackgroundColor,
  );

  @override
  BasePageState<SettingSearchPage> createState() => _SettingSearchPageState();
}

class _SettingSearchPageState extends BasePageState<SettingSearchPage> {

  final TextEditingController _searchController = TextEditingController();
  final FocusNode _textFieldFocusNode = FocusNode();
  List<List<SettingItem>> dataSource = [];
  List<SettingItem> itemList = [];

  @override
  void pageInitState() {
    // TODO: implement pageInitState
    super.pageInitState();

    initData();

  }

  initData() {
    dataSource.add(SectionType.account.searchItems());
    LogManager().debug("dataSource: ${dataSource[0].first.itemType.name}");
  }
  @override
  Widget buildPageContent(BuildContext context) {
    // TODO: implement buildPageContent
    return Column(
      children: [
        _title(),
       Expanded(child: _contentWidget())
      ],
    );
  }
  Widget _title() {
    return Container(
      child: Row(
        children: [
          Expanded(
              flex: 1,
              child: Container(
                height: 35,
                margin: const EdgeInsets.only(left: 15,top: 5),
                padding: const EdgeInsets.only(left: 10),
                alignment: Alignment.centerLeft,
                decoration: const BoxDecoration(
                    color: Color(0xFFF1F2F3),
                    borderRadius: BorderRadius.all(Radius.circular(20))
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Icon(CupertinoIcons.search,size: 16,color: Colors.grey,),
                    Expanded(child:TextField(
                      cursorColor: Colors.grey,
                      textInputAction: TextInputAction.search,
                      controller: _searchController,
                      autofocus: _textFieldFocusNode.hasFocus,
                      decoration: const InputDecoration(
                          border: InputBorder.none,
                          isCollapsed: true,
                          contentPadding: EdgeInsets.symmetric(horizontal: 10), // 水平内边距
                          hintText: "请输入你要搜索的内容",
                          hintStyle: TextStyle(
                            color: Colors.grey,
                            fontSize: 12,
                          )
                      ),
                      onSubmitted: (value) {
                        _search(value);
                      },
                    ))
                  ],
                ),
              )
          ),
          GestureDetector(
            onTap: () {
              Navigator.of(context).pop();
            },
            child: Container(
              height: 30,
              width: 60,
              alignment: Alignment.center,
              child: const Text("取消",style: TextStyle(
                  color: Colors.black,
                  fontSize: 16
              ),),
            ),
          )
        ],
      ),
    );
  }

  Widget _contentWidget() {
    if(dataSource.isEmpty) {
      return const EmptyWidget();
    }
    return ListView.builder(itemBuilder: (context,index) {
      return SettingCell(
        item: itemList[index],
        onTap: (item) {
          //  print('点击了 ${item.itemType.description}');
          Widget page = item.itemType.segueWidget()??Container();
          if(item.itemType == ItemType.cleanCache){
          } else if(item.itemType == ItemType.autoSign || item.itemType == ItemType.authorizationStatus) {

          }else {
            if (page is Container) {
              if(item.itemType.description == "认证信息") {
                Navigator.of(context).push(MaterialPageRoute(builder: (context) {
                  return WebViewPage(url: "https://m.baojun.net/lingClub/authentication/car-auth");
                }));
              }else {
                LoadingManager.showToast('页面尚未完成 敬请期待');
              }
            } else {
              Navigator.of(context).push(
                  CustomCupertinoPageRoute(
                    builder: (context) => page,
                    canSwipeBack: true, // 禁用手势返回
                  ));
            }
          }
        },
        switchValueChanged: (value, item) {
          // 处理开关值变化
        },
      );
    },itemCount: itemList.length,);
  }

  _search(String text) {
    itemList.clear();
    final chars1 = text.runes.toSet();
    for(List<SettingItem> items in dataSource) {
      for(SettingItem item in items) {
        if(chars1.intersection(item.itemType.description.runes.toSet()).isNotEmpty) {
          itemList.add(item);
        }
      }
    }
    setState(() {

    });
  }
}
