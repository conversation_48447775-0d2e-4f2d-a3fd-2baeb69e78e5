import Ability from '@ohos.app.ability.Ability';
import { CameraProperties } from '../CameraProperties';
import { AutoFocusFeature } from './autofocus/AutoFocusFeature';
import { CameraFeatureFactory } from './CameraFeatureFactory';
import { ExposureLockFeature } from './exposurelock/ExposureLockFeature';
import { ExposureOffsetFeature } from './exposureoffset/ExposureOffsetFeature';
import { FlashFeature } from './flash/FlashFeature';
import { FocusPointFeature } from './focuspoint/FocusPointFeature';
import { FpsRangeFeature } from './fpsrange/FpsRangeFeature';
import { ResolutionFeature } from './resolution/ResolutionFeature';
import { ResolutionPreset } from './resolution/ResolutionPreset';
import { SensorOrientationFeature } from './sensororientation/SensorOrientationFeature';
import { DartMessenger } from '../DartMessenger';
import { ZoomLevelFeature } from './zoomlevel/ZoomLevelFeature';
import { ExposurePointFeature } from './exposurepoint/ExposurePointFeature';
import { NoiseReductionFeature } from './noisereduction/NoiseReductionFeature';
export declare class CameraFeatureFactoryImpl implements CameraFeatureFactory {
    createAutoFocusFeature(cameraProperties: CameraProperties, recordingVideo: boolean): AutoFocusFeature;
    createExposureLockFeature(cameraProperties: CameraProperties): ExposureLockFeature;
    createExposureOffsetFeature(cameraProperties: CameraProperties): ExposureOffsetFeature;
    createFlashFeature(cameraProperties: CameraProperties): FlashFeature;
    createResolutionFeature(cameraProperties: CameraProperties, initialSetting: ResolutionPreset, cameraName: string): ResolutionFeature;
    createFocusPointFeature(cameraProperties: CameraProperties, sensorOrientationFeature: SensorOrientationFeature): FocusPointFeature;
    createFpsRangeFeature(cameraProperties: CameraProperties): FpsRangeFeature;
    createSensorOrientationFeature(cameraProperties: CameraProperties, ability: Ability, dartMessenger: DartMessenger): SensorOrientationFeature;
    createZoomLevelFeature(cameraProperties: CameraProperties): ZoomLevelFeature;
    createExposurePointFeature(cameraProperties: CameraProperties, sensorOrientationFeature: SensorOrientationFeature): ExposurePointFeature;
    createNoiseReductionFeature(cameraProperties: CameraProperties): NoiseReductionFeature;
}
