# PowerShell script for generating protobuf Dart files on Windows

Write-Host "Starting protobuf generation for Windows..." -ForegroundColor Green
Write-Host ""

# 设置protoc-gen-dart路径
$protoc_gen_dart = "$env:USERPROFILE\.pub-cache\bin\protoc-gen-dart.bat"

# 检查protoc-gen-dart是否存在
if (-not (Test-Path $protoc_gen_dart)) {
    Write-Host "Error: protoc-gen-dart not found at $protoc_gen_dart" -ForegroundColor Red
    Write-Host "Please install protoc_plugin by running:" -ForegroundColor Yellow
    Write-Host "  dart pub global activate protoc_plugin" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "After installation, make sure <PERSON><PERSON>'s pub cache bin directory is in your PATH:" -ForegroundColor Yellow
    Write-Host "  $env:USERPROFILE\.pub-cache\bin" -ForegroundColor Cyan
    Read-Host "Press Enter to exit"
    exit 1
}

# 检查protoc是否安装
try {
    $null = & protoc --version 2>$null
} catch {
    Write-Host "Error: protoc not found. Please install Protocol Buffers compiler." -ForegroundColor Red
    Write-Host ""
    Write-Host "Installation options:" -ForegroundColor Yellow
    Write-Host "1. Using Chocolatey: choco install protoc" -ForegroundColor Cyan
    Write-Host "2. Using Scoop: scoop install protobuf" -ForegroundColor Cyan
    Write-Host "3. Manual download from: https://github.com/protocolbuffers/protobuf/releases" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "After installation, make sure protoc is in your PATH." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# 检查Dart是否安装
try {
    $null = & dart --version 2>$null
} catch {
    Write-Host "Error: Dart not found. Please install Dart SDK." -ForegroundColor Red
    Write-Host "Download from: https://dart.dev/get-dart" -ForegroundColor Cyan
    Read-Host "Press Enter to exit"
    exit 1
}

# 创建输出目录
$outputDir = "lib\generated\proto"
if (-not (Test-Path $outputDir)) {
    New-Item -ItemType Directory -Path $outputDir -Force | Out-Null
    Write-Host "Created output directory: $outputDir" -ForegroundColor Green
}

# 生成Dart文件
Write-Host "Generating protobuf Dart files..." -ForegroundColor Green

try {
    & protoc --plugin="protoc-gen-dart=$protoc_gen_dart" --dart_out=lib/generated/proto --proto_path=protos protos/*.proto
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host ""
        Write-Host "Protobuf files generated successfully!" -ForegroundColor Green
        Write-Host "Output directory: $outputDir" -ForegroundColor Cyan
    } else {
        throw "protoc command failed with exit code $LASTEXITCODE"
    }
} catch {
    Write-Host "Error generating protobuf files: $_" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Host "Generation completed successfully!" -ForegroundColor Green
Read-Host "Press Enter to exit"