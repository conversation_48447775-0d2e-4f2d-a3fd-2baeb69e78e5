              import LatLng from "./e"; import type Point from "./f"; export default class Bounds { private sw; private ne; private minX; private minY; private maxX; private maxY; constructor(sw?: LatLng, d1?: LatLng);           isEmpty(): boolean;           equals(other: Bounds): boolean;             containsBounds(bounds: Bounds): boolean;           getCenter(): LatLng;         toSpan(transfer?: (ll: LatLng) => Point): number[];             intersects(bounds: Bounds): Bounds;           setMinMax(): void;             containsPoint(point?: LatLng): boolean;           extend(point?: LatLng): void;               getMin(): LatLng;               getMax(): LatLng;           getSouthWest(): LatLng;           getNorthEast(): LatLng;           setSouthWest(sw?: LatLng): void;           setNorthEast(i?: LatLng): void;           clone(): Bounds;           toString(): string; } 