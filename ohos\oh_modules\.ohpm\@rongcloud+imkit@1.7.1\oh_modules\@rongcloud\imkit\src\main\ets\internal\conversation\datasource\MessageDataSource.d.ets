// @keepTs
// @ts-nocheck
/**
 * Created on 2024/07/09
 * <AUTHOR>
 */
import { UiMessage } from '../../../conversation/model/UiMessage';
import { ListDataSource } from '../../base/datasource/ListDataSource';
import { Message } from '@rongcloud/imlib';
/**
 * 聊天页面消息数据源
 * @version 1.0.0
 */
export declare class MessageDataSource extends ListDataSource<UiMessage> {
    private _dataArray;
    totalCount(): number;
    getData(e85: number): UiMessage | undefined;
    getLastReceivedMessageSentTime(): number;
    getLastMessageId(): number;
    updateDataUiMessage(w84: UiMessage): void;
    updateDataMessage(q84: Message): void;
    getIndexByMessage(m84?: number): number;
    /**
     * 根据时间戳查找消息索引。
     * 优先返回 sentTime 与 timestamp 完全相等的消息索引；
     * 若无精确匹配，则返回 sentTime 与 timestamp 差值最小的消息索引。
     * @param timestamp 目标时间戳
     * @returns 匹配消息的索引，未找到返回 -1
     */
    getIndexByTimestamp(g84?: number): number;
    getMessageByMessageId(c84: number): UiMessage | undefined;
    getIndexByMessageUid(y83?: string): number;
    getSelectedMessage(): Message[];
    insertArray(v83: UiMessage[]): void;
    pushArray(u83: UiMessage[]): void;
    pushData(t83: UiMessage): void;
    deleteData(s83: number): void;
    clear(): void;
    batchDelete(o83: number[]): void;
    delete(m83: number, n83?: number): void;
    changeData(l83: number): void;
    reloadData(k83?: boolean): void;
    replace(f83: number, g83: UiMessage): void;
    resetData(): void;
    private handleMessageData;
    private batchHandleMessageData;
    sort(): void;
    set dataArray(t82: UiMessage[]);
    get dataArray(): UiMessage[];
}
