import { Point } from '@bdmap/base'; import BmObject from "./u2"; export default class BmCoordChainHandle extends BmObject { private mCoordChainType; private mCoordAlgorithm; private dThreshold; constructor();           setCoordChainType(s10: number): any;           setCoordAlgorithm(r10: number): any;           setThreshold(threshold: number): any;           handle(points: Array<Point>): any;           getIndexs(): any;           getP0Points(): any[]; } 