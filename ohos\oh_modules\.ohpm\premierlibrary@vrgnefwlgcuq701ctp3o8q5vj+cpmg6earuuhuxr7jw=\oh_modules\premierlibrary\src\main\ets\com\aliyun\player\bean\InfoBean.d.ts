import { InfoCode } from './InfoCode';
export declare class InfoBean {
    private mCode;
    private mExtraValue;
    private mExtraMsg;
    /**
     * 获取info的类型
     * @return 类型
     */
    /****
     * Query the type of info.
     * @return The type of info.
     */
    getCode(): InfoCode;
    /**
     * 设置类型
     * @param mCode 设置类型
     */
    /****
     * Set the info type.
     * @param mCode The info type.
     */
    setCode(k20: InfoCode): void;
    /**
     * 获取额外值.
     * @return 额外值。具体参考{@linkplain InfoCode}的说明。
     */
    /****
     * Query additional values.
     * @return Additional values. See {@linkplain InfoCode}.
     */
    getExtraValue(): number;
    /**
     * 设置额外值
     * @param mExtraValue 额外值
     */
    /****
     * Set additional values.
     * @param mExtraValue Additional values.
     */
    setExtraValue(j20: number): void;
    /**
     * 获取额外信息
     * @return 额外信息。具体参考{@linkplain InfoCode}的说明。
     */
    /****
     * Query additional information.
     * @return Additional information. See {@linkplain InfoCode}.
     */
    getExtraMsg(): string;
    /**
     * 设置额外信息
     * @param mExtraMsg 额外信息
     */
    /****
     * Set additional information.
     * @param mExtraMsg Additional information.
     */
    setExtraMsg(i20: string): void;
}
