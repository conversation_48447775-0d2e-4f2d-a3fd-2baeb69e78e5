import { SearchResult, TaxiInfo } from "../../../d/e/h/f1";
import { MassTransitRoutePlanOption, TransitStep } from "../../../d/e/h/i";
import { SearchParser, SearchRequest } from '../../base/base';
import { UrlProvider } from "../../base/d3";
import { Step } from "./h/i";
export declare class MassTransitRouteParser extends SearchParser {
    parseSearchResult(i26: string): SearchResult;
    /**
     * 跨城公共交通结果，解析出现异常，错误码按RESULT_NOT_FOUND处理
     */
    private parseStringToMassTransitRouteResult;
    parseTransSteps(n25: Step[][]): TransitStep[][];
    parseTransSubSteps(w24: Step): TransitStep;
    parseTaxiInfo(t24: string | undefined): TaxiInfo | null;
    private parseTransitResultNode;
}
export declare class MassTransitRouteRequest extends SearchRequest {
    getUrlDomain(k24: UrlProvider): string;
    constructor(j24: MassTransitRoutePlanOption);
    private massTransitBuildParam;
}
