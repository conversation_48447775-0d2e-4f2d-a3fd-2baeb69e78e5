/**
 * Poi检索过滤规则类
 */
export declare class PoiFilter {
    /**
     * 行业类型
     */
    industryType: IndustryType;
    /**
     * 排序字段
     */
    sortName: SortName;
    /**
     * 排序规则: 0（从高到低），1（从低到高）
     */
    sortRule: string;
    /**
     * 是否有团购:1（有），0（无）
     */
    groupOn: string;
    /**
     * 是否有打折:1（有），0（无)
     */
    discount: string;
    toString(): string;
}
/**
 * poi检索过滤条件：行业类型枚举类
 */
export declare enum IndustryType {
    HOTEL = "hotel",
    CATER = "cater",
    LIFE = "life"
}
/**
 * poi检索过滤条件：排序字段
 * 根据{@link IndustryType}的类型有不同取值
 *
 */
export type SortName = HotelSortName | CaterSortName | LifeSortName;
/**
 * poi检索过滤条件：排序字段，
 * {@link IndustryType}类型为HOTEL时的取值
 *
 */
export declare enum HotelSortName {
    DEFAULT = 0,
    HOTEL_PRICE = 1,
    HOTEL_DISTANCE = 2,
    HOTEL_TOTAL_SCORE = 3,
    HOTEL_LEVEL = 4,
    HOTEL_HEALTH_SCORE = 5
}
/**
 * poi检索过滤条件：排序字段，
 * {@link IndustryType}类型为CATER时的取值
 *
 */
export declare enum CaterSortName {
    DEFAULT = 0,
    CATER_PRICE = 1,
    CATER_DISTANCE = 2,
    CATER_TASTE_RATING = 3,
    CATER_OVERALL_RATING = 4,
    CATER_SERVICE_RATING = 5
}
/**
 * poi检索过滤条件：排序字段，
 * {@link IndustryType}类型为LIFE时的取值
 *
 */
export declare enum LifeSortName {
    DEFAULT = 0,
    PRICE = 1,
    DISTANCE = 2,
    LIFE_OVERALL_RATING = 3,
    LIFE_COMMENT_RATING = 4
}
