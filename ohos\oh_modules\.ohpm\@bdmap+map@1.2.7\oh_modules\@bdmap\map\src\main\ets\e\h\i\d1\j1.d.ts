import type BaseMap from "../o2"; declare const y28: unique symbol; declare const z28: unique symbol; declare const a29: unique symbol;             export default class BaseLayer { private [y28]; private [z28]; private [a29]; protected basemap: BaseMap | undefined; protected mUpdateType: number; protected mTimerEscape: number;       listener: Array<any>;               constructor(name: string, id?: string, c29?: BaseMap);         get name(): string;         get id(): string;       set id(b29: string);         get visible(): boolean;         set visible(val: boolean);         setVisible(visible: boolean): void;       update(): void;         reDraw(): {}; } export {}; 