import { LatLng } from '@bdmap/base';
import { SearchResult } from "../../../d/e/h/f1";
import { SearchParser } from '../../base/base';
export declare class DistrictParser extends SearchParser {
    isSecondParser: boolean;
    mCityName: string | null;
    mCityCode: number | null;
    mCenterPt: LatLng | null;
    parseSearchResult(e9: string): SearchResult;
    setSecondParser(d9: boolean): void;
    private parserDistrictResult;
    private parserDistrictResultSecond;
}
