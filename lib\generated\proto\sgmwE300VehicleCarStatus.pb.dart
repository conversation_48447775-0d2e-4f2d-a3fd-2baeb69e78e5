//
//  Generated code. Do not modify.
//  source: sgmwE300VehicleCarStatus.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

/// 主题： F710C/{env}/vehicle/status/{vin} ,其中测试环境env为pre，生产环境env为prod.主题名找对应的产品负责人确认。根据不同车型名会有变更
/// 如若UCU在整车CAN采集不到相关数据，默认填充255上报(string格式，内容为255)
/// 2024-09-05 F710C 增加27-30 座椅通风和加热字段
/// 2024-09-09 F710C 增加31-34 后排座椅通风和加热字段
class SgmwE300VehicleCarStatus extends $pb.GeneratedMessage {
  factory SgmwE300VehicleCarStatus({
    $fixnum.Int64? collectTime,
    $core.String? dipHeadLight,
    $core.String? lowBeamLight,
    $core.String? psWdronDgr,
    $core.String? drWdronDgr,
    $core.String? sntrModSetSts,
    $core.String? vecChrgStsIndOn1,
    $core.String? vecChrgStsIndOn2,
    $core.String? dDAjrSwAtv,
    $core.String? tDAjrSwAtv,
    $core.String? kyPstn,
    $core.String? psDoorOpenSwAct,
    $core.String? pDAjrSwAtv,
    $core.String? drDoorOpenSwAct,
    $core.String? aCACButCntSt,
    $core.String? aCCntTmpOLfCntSt,
    $core.String? aCBLwLvlCntSt,
    $core.String? chargeLoVolBatSts,
    $core.String? rRDoorOpenSwAct,
    $core.String? rSDAjrSwAtv,
    $core.String? rLDoorOpenSwAct,
    $core.String? lSDAjrSwAtv,
    $core.String? aCPwrModCntSt,
    $core.String? secRwLtWdwOpenDgr,
    $core.String? secRwRtWdwOpenDgr,
    $core.String? pLGSysMSt,
    $core.String? drSeatVentRCC,
    $core.String? prSeatVentRCC,
    $core.String? drSeatHeatRCC,
    $core.String? prSeatHeatRcc,
    $core.String? rlSeatVentRCC,
    $core.String? rrSeatVentRCC,
    $core.String? rlSeatHeatRCC,
    $core.String? rrSeatHeatRcc,
  }) {
    final $result = create();
    if (collectTime != null) {
      $result.collectTime = collectTime;
    }
    if (dipHeadLight != null) {
      $result.dipHeadLight = dipHeadLight;
    }
    if (lowBeamLight != null) {
      $result.lowBeamLight = lowBeamLight;
    }
    if (psWdronDgr != null) {
      $result.psWdronDgr = psWdronDgr;
    }
    if (drWdronDgr != null) {
      $result.drWdronDgr = drWdronDgr;
    }
    if (sntrModSetSts != null) {
      $result.sntrModSetSts = sntrModSetSts;
    }
    if (vecChrgStsIndOn1 != null) {
      $result.vecChrgStsIndOn1 = vecChrgStsIndOn1;
    }
    if (vecChrgStsIndOn2 != null) {
      $result.vecChrgStsIndOn2 = vecChrgStsIndOn2;
    }
    if (dDAjrSwAtv != null) {
      $result.dDAjrSwAtv = dDAjrSwAtv;
    }
    if (tDAjrSwAtv != null) {
      $result.tDAjrSwAtv = tDAjrSwAtv;
    }
    if (kyPstn != null) {
      $result.kyPstn = kyPstn;
    }
    if (psDoorOpenSwAct != null) {
      $result.psDoorOpenSwAct = psDoorOpenSwAct;
    }
    if (pDAjrSwAtv != null) {
      $result.pDAjrSwAtv = pDAjrSwAtv;
    }
    if (drDoorOpenSwAct != null) {
      $result.drDoorOpenSwAct = drDoorOpenSwAct;
    }
    if (aCACButCntSt != null) {
      $result.aCACButCntSt = aCACButCntSt;
    }
    if (aCCntTmpOLfCntSt != null) {
      $result.aCCntTmpOLfCntSt = aCCntTmpOLfCntSt;
    }
    if (aCBLwLvlCntSt != null) {
      $result.aCBLwLvlCntSt = aCBLwLvlCntSt;
    }
    if (chargeLoVolBatSts != null) {
      $result.chargeLoVolBatSts = chargeLoVolBatSts;
    }
    if (rRDoorOpenSwAct != null) {
      $result.rRDoorOpenSwAct = rRDoorOpenSwAct;
    }
    if (rSDAjrSwAtv != null) {
      $result.rSDAjrSwAtv = rSDAjrSwAtv;
    }
    if (rLDoorOpenSwAct != null) {
      $result.rLDoorOpenSwAct = rLDoorOpenSwAct;
    }
    if (lSDAjrSwAtv != null) {
      $result.lSDAjrSwAtv = lSDAjrSwAtv;
    }
    if (aCPwrModCntSt != null) {
      $result.aCPwrModCntSt = aCPwrModCntSt;
    }
    if (secRwLtWdwOpenDgr != null) {
      $result.secRwLtWdwOpenDgr = secRwLtWdwOpenDgr;
    }
    if (secRwRtWdwOpenDgr != null) {
      $result.secRwRtWdwOpenDgr = secRwRtWdwOpenDgr;
    }
    if (pLGSysMSt != null) {
      $result.pLGSysMSt = pLGSysMSt;
    }
    if (drSeatVentRCC != null) {
      $result.drSeatVentRCC = drSeatVentRCC;
    }
    if (prSeatVentRCC != null) {
      $result.prSeatVentRCC = prSeatVentRCC;
    }
    if (drSeatHeatRCC != null) {
      $result.drSeatHeatRCC = drSeatHeatRCC;
    }
    if (prSeatHeatRcc != null) {
      $result.prSeatHeatRcc = prSeatHeatRcc;
    }
    if (rlSeatVentRCC != null) {
      $result.rlSeatVentRCC = rlSeatVentRCC;
    }
    if (rrSeatVentRCC != null) {
      $result.rrSeatVentRCC = rrSeatVentRCC;
    }
    if (rlSeatHeatRCC != null) {
      $result.rlSeatHeatRCC = rlSeatHeatRCC;
    }
    if (rrSeatHeatRcc != null) {
      $result.rrSeatHeatRcc = rrSeatHeatRcc;
    }
    return $result;
  }
  SgmwE300VehicleCarStatus._() : super();
  factory SgmwE300VehicleCarStatus.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SgmwE300VehicleCarStatus.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'SgmwE300VehicleCarStatus', createEmptyInstance: create)
    ..aInt64(1, _omitFieldNames ? '' : 'collectTime', protoName: 'collectTime')
    ..aOS(2, _omitFieldNames ? '' : 'dipHeadLight', protoName: 'dipHeadLight')
    ..aOS(3, _omitFieldNames ? '' : 'lowBeamLight', protoName: 'lowBeamLight')
    ..aOS(4, _omitFieldNames ? '' : 'psWdronDgr', protoName: 'psWdronDgr')
    ..aOS(5, _omitFieldNames ? '' : 'drWdronDgr', protoName: 'drWdronDgr')
    ..aOS(6, _omitFieldNames ? '' : 'sntrModSetSts', protoName: 'sntrModSetSts')
    ..aOS(7, _omitFieldNames ? '' : 'VecChrgStsIndOn1', protoName: 'VecChrgStsIndOn1')
    ..aOS(8, _omitFieldNames ? '' : 'VecChrgStsIndOn2', protoName: 'VecChrgStsIndOn2')
    ..aOS(9, _omitFieldNames ? '' : 'DDAjrSwAtv', protoName: 'DDAjrSwAtv')
    ..aOS(10, _omitFieldNames ? '' : 'TDAjrSwAtv', protoName: 'TDAjrSwAtv')
    ..aOS(11, _omitFieldNames ? '' : 'KyPstn', protoName: 'KyPstn')
    ..aOS(12, _omitFieldNames ? '' : 'PsDoorOpenSwAct', protoName: 'PsDoorOpenSwAct')
    ..aOS(13, _omitFieldNames ? '' : 'PDAjrSwAtv', protoName: 'PDAjrSwAtv')
    ..aOS(14, _omitFieldNames ? '' : 'DrDoorOpenSwAct', protoName: 'DrDoorOpenSwAct')
    ..aOS(15, _omitFieldNames ? '' : 'ACACButCntSt', protoName: 'ACACButCntSt')
    ..aOS(16, _omitFieldNames ? '' : 'ACCntTmpOLfCntSt', protoName: 'ACCntTmpOLfCntSt')
    ..aOS(17, _omitFieldNames ? '' : 'ACBLwLvlCntSt', protoName: 'ACBLwLvlCntSt')
    ..aOS(18, _omitFieldNames ? '' : 'chargeLoVolBatSts', protoName: 'chargeLoVolBatSts')
    ..aOS(19, _omitFieldNames ? '' : 'RRDoorOpenSwAct', protoName: 'RRDoorOpenSwAct')
    ..aOS(20, _omitFieldNames ? '' : 'RSDAjrSwAtv', protoName: 'RSDAjrSwAtv')
    ..aOS(21, _omitFieldNames ? '' : 'RLDoorOpenSwAct', protoName: 'RLDoorOpenSwAct')
    ..aOS(22, _omitFieldNames ? '' : 'LSDAjrSwAtv', protoName: 'LSDAjrSwAtv')
    ..aOS(23, _omitFieldNames ? '' : 'ACPwrModCntSt', protoName: 'ACPwrModCntSt')
    ..aOS(24, _omitFieldNames ? '' : 'SecRwLtWdwOpenDgr', protoName: 'SecRwLtWdwOpenDgr')
    ..aOS(25, _omitFieldNames ? '' : 'SecRwRtWdwOpenDgr', protoName: 'SecRwRtWdwOpenDgr')
    ..aOS(26, _omitFieldNames ? '' : 'PLGSysMSt', protoName: 'PLGSysMSt')
    ..aOS(27, _omitFieldNames ? '' : 'drSeatVentRCC', protoName: 'drSeatVentRCC')
    ..aOS(28, _omitFieldNames ? '' : 'prSeatVentRCC', protoName: 'prSeatVentRCC')
    ..aOS(29, _omitFieldNames ? '' : 'drSeatHeatRCC', protoName: 'drSeatHeatRCC')
    ..aOS(30, _omitFieldNames ? '' : 'prSeatHeatRcc', protoName: 'prSeatHeatRcc')
    ..aOS(31, _omitFieldNames ? '' : 'rlSeatVentRCC', protoName: 'rlSeatVentRCC')
    ..aOS(32, _omitFieldNames ? '' : 'rrSeatVentRCC', protoName: 'rrSeatVentRCC')
    ..aOS(33, _omitFieldNames ? '' : 'rlSeatHeatRCC', protoName: 'rlSeatHeatRCC')
    ..aOS(34, _omitFieldNames ? '' : 'rrSeatHeatRcc', protoName: 'rrSeatHeatRcc')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SgmwE300VehicleCarStatus clone() => SgmwE300VehicleCarStatus()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SgmwE300VehicleCarStatus copyWith(void Function(SgmwE300VehicleCarStatus) updates) => super.copyWith((message) => updates(message as SgmwE300VehicleCarStatus)) as SgmwE300VehicleCarStatus;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static SgmwE300VehicleCarStatus create() => SgmwE300VehicleCarStatus._();
  SgmwE300VehicleCarStatus createEmptyInstance() => create();
  static $pb.PbList<SgmwE300VehicleCarStatus> createRepeated() => $pb.PbList<SgmwE300VehicleCarStatus>();
  @$core.pragma('dart2js:noInline')
  static SgmwE300VehicleCarStatus getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SgmwE300VehicleCarStatus>(create);
  static SgmwE300VehicleCarStatus? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get collectTime => $_getI64(0);
  @$pb.TagNumber(1)
  set collectTime($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCollectTime() => $_has(0);
  @$pb.TagNumber(1)
  void clearCollectTime() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get dipHeadLight => $_getSZ(1);
  @$pb.TagNumber(2)
  set dipHeadLight($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasDipHeadLight() => $_has(1);
  @$pb.TagNumber(2)
  void clearDipHeadLight() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get lowBeamLight => $_getSZ(2);
  @$pb.TagNumber(3)
  set lowBeamLight($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasLowBeamLight() => $_has(2);
  @$pb.TagNumber(3)
  void clearLowBeamLight() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get psWdronDgr => $_getSZ(3);
  @$pb.TagNumber(4)
  set psWdronDgr($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasPsWdronDgr() => $_has(3);
  @$pb.TagNumber(4)
  void clearPsWdronDgr() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get drWdronDgr => $_getSZ(4);
  @$pb.TagNumber(5)
  set drWdronDgr($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasDrWdronDgr() => $_has(4);
  @$pb.TagNumber(5)
  void clearDrWdronDgr() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get sntrModSetSts => $_getSZ(5);
  @$pb.TagNumber(6)
  set sntrModSetSts($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasSntrModSetSts() => $_has(5);
  @$pb.TagNumber(6)
  void clearSntrModSetSts() => clearField(6);

  @$pb.TagNumber(7)
  $core.String get vecChrgStsIndOn1 => $_getSZ(6);
  @$pb.TagNumber(7)
  set vecChrgStsIndOn1($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasVecChrgStsIndOn1() => $_has(6);
  @$pb.TagNumber(7)
  void clearVecChrgStsIndOn1() => clearField(7);

  @$pb.TagNumber(8)
  $core.String get vecChrgStsIndOn2 => $_getSZ(7);
  @$pb.TagNumber(8)
  set vecChrgStsIndOn2($core.String v) { $_setString(7, v); }
  @$pb.TagNumber(8)
  $core.bool hasVecChrgStsIndOn2() => $_has(7);
  @$pb.TagNumber(8)
  void clearVecChrgStsIndOn2() => clearField(8);

  @$pb.TagNumber(9)
  $core.String get dDAjrSwAtv => $_getSZ(8);
  @$pb.TagNumber(9)
  set dDAjrSwAtv($core.String v) { $_setString(8, v); }
  @$pb.TagNumber(9)
  $core.bool hasDDAjrSwAtv() => $_has(8);
  @$pb.TagNumber(9)
  void clearDDAjrSwAtv() => clearField(9);

  @$pb.TagNumber(10)
  $core.String get tDAjrSwAtv => $_getSZ(9);
  @$pb.TagNumber(10)
  set tDAjrSwAtv($core.String v) { $_setString(9, v); }
  @$pb.TagNumber(10)
  $core.bool hasTDAjrSwAtv() => $_has(9);
  @$pb.TagNumber(10)
  void clearTDAjrSwAtv() => clearField(10);

  @$pb.TagNumber(11)
  $core.String get kyPstn => $_getSZ(10);
  @$pb.TagNumber(11)
  set kyPstn($core.String v) { $_setString(10, v); }
  @$pb.TagNumber(11)
  $core.bool hasKyPstn() => $_has(10);
  @$pb.TagNumber(11)
  void clearKyPstn() => clearField(11);

  @$pb.TagNumber(12)
  $core.String get psDoorOpenSwAct => $_getSZ(11);
  @$pb.TagNumber(12)
  set psDoorOpenSwAct($core.String v) { $_setString(11, v); }
  @$pb.TagNumber(12)
  $core.bool hasPsDoorOpenSwAct() => $_has(11);
  @$pb.TagNumber(12)
  void clearPsDoorOpenSwAct() => clearField(12);

  @$pb.TagNumber(13)
  $core.String get pDAjrSwAtv => $_getSZ(12);
  @$pb.TagNumber(13)
  set pDAjrSwAtv($core.String v) { $_setString(12, v); }
  @$pb.TagNumber(13)
  $core.bool hasPDAjrSwAtv() => $_has(12);
  @$pb.TagNumber(13)
  void clearPDAjrSwAtv() => clearField(13);

  @$pb.TagNumber(14)
  $core.String get drDoorOpenSwAct => $_getSZ(13);
  @$pb.TagNumber(14)
  set drDoorOpenSwAct($core.String v) { $_setString(13, v); }
  @$pb.TagNumber(14)
  $core.bool hasDrDoorOpenSwAct() => $_has(13);
  @$pb.TagNumber(14)
  void clearDrDoorOpenSwAct() => clearField(14);

  @$pb.TagNumber(15)
  $core.String get aCACButCntSt => $_getSZ(14);
  @$pb.TagNumber(15)
  set aCACButCntSt($core.String v) { $_setString(14, v); }
  @$pb.TagNumber(15)
  $core.bool hasACACButCntSt() => $_has(14);
  @$pb.TagNumber(15)
  void clearACACButCntSt() => clearField(15);

  @$pb.TagNumber(16)
  $core.String get aCCntTmpOLfCntSt => $_getSZ(15);
  @$pb.TagNumber(16)
  set aCCntTmpOLfCntSt($core.String v) { $_setString(15, v); }
  @$pb.TagNumber(16)
  $core.bool hasACCntTmpOLfCntSt() => $_has(15);
  @$pb.TagNumber(16)
  void clearACCntTmpOLfCntSt() => clearField(16);

  @$pb.TagNumber(17)
  $core.String get aCBLwLvlCntSt => $_getSZ(16);
  @$pb.TagNumber(17)
  set aCBLwLvlCntSt($core.String v) { $_setString(16, v); }
  @$pb.TagNumber(17)
  $core.bool hasACBLwLvlCntSt() => $_has(16);
  @$pb.TagNumber(17)
  void clearACBLwLvlCntSt() => clearField(17);

  @$pb.TagNumber(18)
  $core.String get chargeLoVolBatSts => $_getSZ(17);
  @$pb.TagNumber(18)
  set chargeLoVolBatSts($core.String v) { $_setString(17, v); }
  @$pb.TagNumber(18)
  $core.bool hasChargeLoVolBatSts() => $_has(17);
  @$pb.TagNumber(18)
  void clearChargeLoVolBatSts() => clearField(18);

  @$pb.TagNumber(19)
  $core.String get rRDoorOpenSwAct => $_getSZ(18);
  @$pb.TagNumber(19)
  set rRDoorOpenSwAct($core.String v) { $_setString(18, v); }
  @$pb.TagNumber(19)
  $core.bool hasRRDoorOpenSwAct() => $_has(18);
  @$pb.TagNumber(19)
  void clearRRDoorOpenSwAct() => clearField(19);

  @$pb.TagNumber(20)
  $core.String get rSDAjrSwAtv => $_getSZ(19);
  @$pb.TagNumber(20)
  set rSDAjrSwAtv($core.String v) { $_setString(19, v); }
  @$pb.TagNumber(20)
  $core.bool hasRSDAjrSwAtv() => $_has(19);
  @$pb.TagNumber(20)
  void clearRSDAjrSwAtv() => clearField(20);

  @$pb.TagNumber(21)
  $core.String get rLDoorOpenSwAct => $_getSZ(20);
  @$pb.TagNumber(21)
  set rLDoorOpenSwAct($core.String v) { $_setString(20, v); }
  @$pb.TagNumber(21)
  $core.bool hasRLDoorOpenSwAct() => $_has(20);
  @$pb.TagNumber(21)
  void clearRLDoorOpenSwAct() => clearField(21);

  @$pb.TagNumber(22)
  $core.String get lSDAjrSwAtv => $_getSZ(21);
  @$pb.TagNumber(22)
  set lSDAjrSwAtv($core.String v) { $_setString(21, v); }
  @$pb.TagNumber(22)
  $core.bool hasLSDAjrSwAtv() => $_has(21);
  @$pb.TagNumber(22)
  void clearLSDAjrSwAtv() => clearField(22);

  @$pb.TagNumber(23)
  $core.String get aCPwrModCntSt => $_getSZ(22);
  @$pb.TagNumber(23)
  set aCPwrModCntSt($core.String v) { $_setString(22, v); }
  @$pb.TagNumber(23)
  $core.bool hasACPwrModCntSt() => $_has(22);
  @$pb.TagNumber(23)
  void clearACPwrModCntSt() => clearField(23);

  @$pb.TagNumber(24)
  $core.String get secRwLtWdwOpenDgr => $_getSZ(23);
  @$pb.TagNumber(24)
  set secRwLtWdwOpenDgr($core.String v) { $_setString(23, v); }
  @$pb.TagNumber(24)
  $core.bool hasSecRwLtWdwOpenDgr() => $_has(23);
  @$pb.TagNumber(24)
  void clearSecRwLtWdwOpenDgr() => clearField(24);

  @$pb.TagNumber(25)
  $core.String get secRwRtWdwOpenDgr => $_getSZ(24);
  @$pb.TagNumber(25)
  set secRwRtWdwOpenDgr($core.String v) { $_setString(24, v); }
  @$pb.TagNumber(25)
  $core.bool hasSecRwRtWdwOpenDgr() => $_has(24);
  @$pb.TagNumber(25)
  void clearSecRwRtWdwOpenDgr() => clearField(25);

  @$pb.TagNumber(26)
  $core.String get pLGSysMSt => $_getSZ(25);
  @$pb.TagNumber(26)
  set pLGSysMSt($core.String v) { $_setString(25, v); }
  @$pb.TagNumber(26)
  $core.bool hasPLGSysMSt() => $_has(25);
  @$pb.TagNumber(26)
  void clearPLGSysMSt() => clearField(26);

  @$pb.TagNumber(27)
  $core.String get drSeatVentRCC => $_getSZ(26);
  @$pb.TagNumber(27)
  set drSeatVentRCC($core.String v) { $_setString(26, v); }
  @$pb.TagNumber(27)
  $core.bool hasDrSeatVentRCC() => $_has(26);
  @$pb.TagNumber(27)
  void clearDrSeatVentRCC() => clearField(27);

  @$pb.TagNumber(28)
  $core.String get prSeatVentRCC => $_getSZ(27);
  @$pb.TagNumber(28)
  set prSeatVentRCC($core.String v) { $_setString(27, v); }
  @$pb.TagNumber(28)
  $core.bool hasPrSeatVentRCC() => $_has(27);
  @$pb.TagNumber(28)
  void clearPrSeatVentRCC() => clearField(28);

  @$pb.TagNumber(29)
  $core.String get drSeatHeatRCC => $_getSZ(28);
  @$pb.TagNumber(29)
  set drSeatHeatRCC($core.String v) { $_setString(28, v); }
  @$pb.TagNumber(29)
  $core.bool hasDrSeatHeatRCC() => $_has(28);
  @$pb.TagNumber(29)
  void clearDrSeatHeatRCC() => clearField(29);

  @$pb.TagNumber(30)
  $core.String get prSeatHeatRcc => $_getSZ(29);
  @$pb.TagNumber(30)
  set prSeatHeatRcc($core.String v) { $_setString(29, v); }
  @$pb.TagNumber(30)
  $core.bool hasPrSeatHeatRcc() => $_has(29);
  @$pb.TagNumber(30)
  void clearPrSeatHeatRcc() => clearField(30);

  @$pb.TagNumber(31)
  $core.String get rlSeatVentRCC => $_getSZ(30);
  @$pb.TagNumber(31)
  set rlSeatVentRCC($core.String v) { $_setString(30, v); }
  @$pb.TagNumber(31)
  $core.bool hasRlSeatVentRCC() => $_has(30);
  @$pb.TagNumber(31)
  void clearRlSeatVentRCC() => clearField(31);

  @$pb.TagNumber(32)
  $core.String get rrSeatVentRCC => $_getSZ(31);
  @$pb.TagNumber(32)
  set rrSeatVentRCC($core.String v) { $_setString(31, v); }
  @$pb.TagNumber(32)
  $core.bool hasRrSeatVentRCC() => $_has(31);
  @$pb.TagNumber(32)
  void clearRrSeatVentRCC() => clearField(32);

  @$pb.TagNumber(33)
  $core.String get rlSeatHeatRCC => $_getSZ(32);
  @$pb.TagNumber(33)
  set rlSeatHeatRCC($core.String v) { $_setString(32, v); }
  @$pb.TagNumber(33)
  $core.bool hasRlSeatHeatRCC() => $_has(32);
  @$pb.TagNumber(33)
  void clearRlSeatHeatRCC() => clearField(33);

  @$pb.TagNumber(34)
  $core.String get rrSeatHeatRcc => $_getSZ(33);
  @$pb.TagNumber(34)
  set rrSeatHeatRcc($core.String v) { $_setString(33, v); }
  @$pb.TagNumber(34)
  $core.bool hasRrSeatHeatRcc() => $_has(33);
  @$pb.TagNumber(34)
  void clearRrSeatHeatRcc() => clearField(34);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
