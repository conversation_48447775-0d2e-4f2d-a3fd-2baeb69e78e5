// @keepTs
// @ts-nocheck
import { ConversationIdentifier, IAsyncResult, Message, MessageDestructionListener, MessageReadReceiptListener, MessageRecalledListener, MessageReceivedListener, PublicServiceInfo, TypingStatusListener } from '@rongcloud/imlib';
import { MessageService } from '../../message/MessageService';
import ArrayList from '@ohos.util.ArrayList';
import { MessageEventListener } from '../../conversation/listener/MessageEventListener';
import { MessageInterceptor } from '../../conversation/listener/MessageInterceptor';
import List from '@ohos.util.List';
import { CombineMessageEventListener } from '../../conversation/listener/CombineMessageEventListener';
import { CombineMessage } from '../../message/content/CombineMessage';
export declare class InnerMessageServiceImpl implements MessageService {
    private interceptor?;
    private messageEventListeners;
    private combineMessageEventListeners;
    private typingMessageListeners;
    onInit(v323: Context): void;
    /** 初始化输入状态管理器 */
    private initTypingMessageManager;
    addMessageReceiveListener(r323: MessageReceivedListener): void;
    removeMessageReceiveListener(q323: MessageReceivedListener): void;
    addMessageRecalledListener(p323: MessageRecalledListener): void;
    removeMessageRecalledListener(o323: MessageRecalledListener): void;
    addMessageReadReceiptListener(n323: MessageReadReceiptListener): void;
    removeMessageReadReceiptListener(m323: MessageReadReceiptListener): void;
    /**
     * 发送消息之前的内部逻辑处理。
     */
    private handleBeforeSend;
    /**
     * 发送普通消息
     */
    sendMessage(t322: Message): Promise<IAsyncResult<Message>>;
    /**
     * 发送媒体消息
     */
    sendMediaMessage(r322: Message, q322?: (r322: Message, progress: number) => void): Promise<IAsyncResult<Message>>;
    /**
     * 使用自定义上传下载发送媒体消息
     * # 说明
     *```
     * 如果遇到 DatabaseNotOpened = 34301 ，原因是在 IM 连接成功前发送了消息
     * IM 连接成功后 SDK 才会打开对应用户的消息数据库
     *```
     * # 重要
     *```
     * 在使用 IMKit 时，发送消息请使用该方法发送消息，该方法发送的消息可以自动在 UI 上展示
     * 如果使用 IMEngine 发送消息， UI 不会自动更新
     *```
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "会话 id";
     *
     *  let imgMsg = new ImageMessage();
     *  // 使用沙盒路径创建图片消息。系统路径的图片 SDK 无法访问（例如相册的图片路径 SDK 就无法访问）
     *  imgMsg.localPath = localPath;
     *
     *  let msg = new Message(conId, imgMsg);
     *
     *  let option: ISendMsgOption = {};
     *
     *  RongIM.getInstance().messageService().sendMediaMessageWithUploader(msg
     *    , (msg: Message, progress: number) => {
     *      // 媒体上传进度 [1 ~ 100]
     *    }, (transfer: MediaMessageTransfer) => {
     *      // 业务侧上传后，调用 transfer 回传给SDK进度、成功、失败、取消
     *      // 回传进度
     *      // transfer.updateProgress(progress)
     *      // 回传成功，SDK会执行发送
     *      // transfer.success("业务侧上传后的远端地址")
     *      // 回传失败
     *      // transfer.error()
     *      // 回传取消
     *      // transfer.cancel()
     *    }).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      //发送消息失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 消息为空
     *      return;
     *    }
     *    let msg = result.data as Message;
     *  })
     *```
     * @param message 消息体
     * @param progressCallback [可选参数]媒体文件上传进度
     * @param uploadCallback [可选参数]自定义媒体文件上传回调，如果此参数不传则执行SDK默认流程
     * @returns 媒体消息发送结果
     * @discussion 只有入库成功才会走 savedCallback，其他的情况：非法参数、入库失败、发送不入库的消息等都不会走 savedCallback 直接走 resultCallback
     * @warning 调用该接口才能触发消息拦截
     * @version 1.0.0
     */
    private sendMediaMessageWithUploader;
    private sendMediaMessageByCustomTransferInner;
    private onSendMediaMessageResult;
    private isMediaLimitError;
    /**
     * 取消发送媒体消息下载
     */
    cancelSendMediaMessage(c321: number): Promise<IAsyncResult<void>>;
    private invokeOperationMessageEvent;
    /**
     * 删除本地某个会话内的所有消息
     */
    deleteConversationMessages(v320: ConversationIdentifier, w320?: boolean): Promise<IAsyncResult<void>>;
    /**
     * 批量插入消息
     * @param msgList
     * @returns
     */
    batchInsertMessage(u320: List<Message>): Promise<IAsyncResult<void>>;
    /**
     * 批量插入消息
     * @param msgList
     * @returns
     */
    insertMessage(q320: Message): Promise<IAsyncResult<Message>>;
    /**
     * 批量删除本地和远端消息
     * @param targetId
     * @param cType
     * @param messages
     */
    batchDeleteMessage(i320: ConversationIdentifier, j320: Message[], k320?: boolean): Promise<IAsyncResult<void>>;
    recallMessage(d320: Message): Promise<IAsyncResult<void>>;
    setMessageInterceptor(c320: MessageInterceptor): void;
    removeMessageInterceptor(): void;
    getMessageInterceptor(): MessageInterceptor | undefined;
    /**
     * 添加消息操作事件监听
     * @param listener 要添加的事件
     */
    addMessageEventListener(b320: MessageEventListener): void;
    /**
     * 移除消息操作事件监听
     * @param listener 要移除的事件
     */
    removeMessageEventListener(a320: MessageEventListener): void;
    getMessageEventListeners(): ArrayList<MessageEventListener>;
    addCombineMessageEventListener(z319: CombineMessageEventListener): void;
    removeCombineMessageEventListener(y319: CombineMessageEventListener): void;
    getCombineMessageEventListeners(): ArrayList<CombineMessageEventListener>;
    sendTypingStatus(w319: ConversationIdentifier, x319: string): Promise<IAsyncResult<void>>;
    addTypingStatusListener(v319: TypingStatusListener): void;
    removeTypingStatusListener(u319: TypingStatusListener): void;
    addTypingMessageListeners(t319: TypingStatusListener): void;
    removeTypingMessageListeners(s319: TypingStatusListener): void;
    /**
     * 获取批量本地特定类型的消息，基于 time 获取
     */
    getHistoryMessagesByTime(m319: ConversationIdentifier, n319: List<string>, o319?: number, p319?: number, q319?: number): Promise<IAsyncResult<List<Message>>>;
    /**
     * 获取消息
     * @param messageId
     * @returns
     */
    getMessage(l319: number): Promise<IAsyncResult<Message>>;
    /**
     * 获取公众号列表
     */
    getPublicService(k319: ConversationIdentifier): Promise<IAsyncResult<PublicServiceInfo>>;
    addMessageDestructionListener(j319: MessageDestructionListener): void;
    removeMessageDestructionListener(i319: MessageDestructionListener): void;
    messageBeginDestruct(h319: Message): Promise<IAsyncResult<void>>;
    messageStopDestruct(g319: Message): Promise<IAsyncResult<void>>;
    obtainCombineMessage(z318: Message[], a319?: (message: Message) => Promise<string>): Promise<IAsyncResult<CombineMessage>>;
}
