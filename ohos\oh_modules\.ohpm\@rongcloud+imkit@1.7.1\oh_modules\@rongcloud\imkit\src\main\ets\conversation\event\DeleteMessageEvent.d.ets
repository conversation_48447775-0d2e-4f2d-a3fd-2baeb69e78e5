// @keepTs
// @ts-nocheck
/**
 * Created on 2024/9/20
 * <AUTHOR>
 */
import { ConversationIdentifier, Message } from '@rongcloud/imlib';
import { MessageOperationEvent } from './MessageOperationEvent';
export declare class DeleteMessageEvent extends MessageOperationEvent {
    private _messages;
    set messages(u24: Message[]);
    get messages(): Message[];
    constructor(s24: ConversationIdentifier, t24: Message[]);
}
