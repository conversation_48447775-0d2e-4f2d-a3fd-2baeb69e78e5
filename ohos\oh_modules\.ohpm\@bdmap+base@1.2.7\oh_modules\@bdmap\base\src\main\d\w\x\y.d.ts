import LatLng from "../../e"; export declare class LatLngBounds {       private _center;       readonly northeast: LatLng;       readonly southwest: LatLng; constructor(southwest: LatLng, northeast: LatLng);             contain(point: LatLng): boolean;           set center(center: LatLng);           get center(): LatLng | null; }       export declare class LatLngBoundsBuilder { private mMinLat; private mMaxLat; private mMinLng; private mMaxLng; private _mMaxLng; private _mMinLng; private firstPoint; constructor();           build(): LatLngBounds;             include(i2: LatLng[]): LatLngBoundsBuilder;           private buildMaxAndMinLatlng; } 