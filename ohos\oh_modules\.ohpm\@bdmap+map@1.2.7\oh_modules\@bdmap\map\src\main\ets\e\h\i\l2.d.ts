import { MapStatusChangeReason } from "../util/b1/c1"; import type Map from "./j"; import { EventBundle, EventOverlayBundle } from "../g1/h1"; import type { LayerMsgData, NearOverlays } from "../g1/e2"; import type { MapStatusBundle } from "../g1/i1"; import type Overlay from "./m/m"; import type BaseUI from "./n1/q1";               export default class MapViewListener { private baidumap; private mapEventListener; private overLayEventListener; private mapStatusCode; private changeMapStatusCode; private changeTouchMapStatusReason; private mapStatusReason; constructor(map: Map); changeMapStatusReason(f33: MapStatusChangeReason): void; onMapStateChange(status: MapStatusBundle): void; onMapStatusChange(status: MapStatusBundle): void; dispatchMapStateChange(status: MapStatusBundle, eventKey: symbol): void; onMapIndoorChange(status: boolean): void; onMapChange(): void; onMapDestory(): void; onMapClick(location: EventBundle): void; onMapDoubleClick(location: EventBundle): void; onMapLongPressClick(location: EventBundle): void; onMapSingleMove(location?: EventBundle): void; onMapSingleStart(location?: EventBundle): void; onMapSingleMoveEnd(location?: EventBundle): void; onMapRotation(location: EventBundle): void; onMapRotationStart(location: EventBundle): void; onMapRotationEnd(location: EventBundle): void; onMapDoubleMove(location: EventBundle): void; onMapDoubleMoveStart(location: EventBundle): void; onMapDoubleMoveEnd(location: EventBundle): void; onMapPinchScale(location: EventBundle): void; onMapPinchScaleStart(location: EventBundle): void; onMapPinchScaleEnd(location: EventBundle): void; onMapLongClick(p: EventBundle): void; onCompassClick(target: EventBundle): boolean; onMapObjClick(location: EventOverlayBundle, ids: Array<NearOverlays>): void; onOverlayEmitClickEvent(location: EventOverlayBundle, ids: Array<NearOverlays>, c32: number): void; onBmOverlayEmitClickEvent(location: EventOverlayBundle, overlay: Overlay, y31: null | BaseUI): void; onMapLayerRequest(data: LayerMsgData): {}; destroy(): void; } 