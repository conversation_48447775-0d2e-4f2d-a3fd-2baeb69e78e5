import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import { DVModel } from '@ohos/flutter_ohos/src/main/ets/view/DynamicView/dynamicView';
import { ConsoleMessageLevel, CustomViewCallback, Reply, WebChromeClientFlutterApi } from './GeneratedOhosWebView';
import { InstanceManager } from './InstanceManager';
import { WebChromeClient } from './WebChromeClientHostApiImpl';
import { WebViewPlatformView } from './WebViewHostApiImpl';
export declare class WebChromeClientFlutterApiImpl extends WebChromeClientFlutterApi {
    private instanceManager;
    private webViewFlutterApi;
    private emptyReply;
    toConsoleMessageLevel(level: MessageLevel): ConsoleMessageLevel;
    constructor(binaryMessenger: BinaryMessenger, instanceManager: InstanceManager);
    onProgressChangedImpl(webChromeClient: WebChromeClient, webView: WebViewPlatformView, progress: number, callback: Reply<void>): void;
    onShowFileChooserImpl(webChromeClient: WebChromeClient, webView: WebViewPlatformView, fileChooserParams: FileSelectorParam, callback: Reply<Array<string>>): void;
    onGeolocationPermissionsShowPromptImpl(webChromeClient: WebChromeClient, origin: string, callback: JsGeolocation, replyCallback: Reply<void>): void;
    onGeolocationPermissionsHidePromptImpl(instance: WebChromeClient, callback: Reply<void>): void;
    onPermissionRequestImpl(instance: WebChromeClient, request: PermissionRequest, callback: Reply<void>): void;
    onShowCustomViewImpl(instance: WebChromeClient, view: DVModel, customViewCallback: CustomViewCallback, callback: Reply<void>): void;
    onHideCustomViewImpl(instance: WebChromeClient, callback: Reply<void>): void;
    onConsoleMessageImpl(instance: WebChromeClient, message: ConsoleMessage, callback: Reply<void>): void;
    onJsAlertImpl(instance: WebChromeClient, url: string, message: string, callback: Reply<void>): void;
    onJsConfirmImpl(instance: WebChromeClient, url: string, message: string, callback: Reply<boolean | null>): void;
    onJsPromptImpl(instance: WebChromeClient, url: string, message: string, defaultValue: string, callback: Reply<string | null>): void;
    getIdentifierForClient(webChromeClient: WebChromeClient): number;
}
