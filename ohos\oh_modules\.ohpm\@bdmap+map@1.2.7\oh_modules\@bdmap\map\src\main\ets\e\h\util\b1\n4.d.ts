            export declare const MsgDefine: { MSG_GET_TEXT_DATA: string; MSG_GET_MAP_DATA: string; MSG_GET_LAYER_DATA: string; MSG_GET_DEVICE_DATA: string; MSG_CLICK: string; MSG_GET_NAVI_DATA: string; MSG_GET_ADDLOG_FUN: string; MSG_GET_GL_OK: number; MSG_APP_DATA_OK: number; MSG_APP_PB_DATA_OK: number; MSG_CLICK_POPUP: number; MSG_INDOOR_HIDE: number; MSG_SYNC_CLOUDDATA: number; MSG_MSG_CENTER: number; MSG_COMMON_ENGINE: number; MSG_USERINFO_SECURE: number; MSG_LOG_GESTURE: number;       MSG_ONLINE_UPDATA: number; MSG_ONLINE_DOWNLOAD: number; MSG_NETWORK_CHANNEL: number; MSG_APP_SAVESCREEN: number; MSG_FAV_BUS_OLD: number; MSG_BASE_VOICESEARCH_STATE: number; VM_TIMER_ID: number; V_WM_VDATAENGINE: number; V_WM_VOICESEARCH: number; V_WM_VSTREETIDMATCH: number; V_WM_VSTREETCLICKARROW: number; V_WM_VSTREETCLICKBACKGROUND: number; V_WM_GLRENDER: number; V_WM_GLCHANGE: number; V_WM_STATUS_CHANGE: number; V_WM_TRACK_MOVE_PROGRESS: number; V_WM_TRACK_MOVE_POSITION: number; V_WM_BASEINDOORMAPMODE: number; V_WM_LONGLINKNETSTATE: number; V_WM_TOUCH: number; MSG_SENSOR: number; MSG_AUTOHIDE_TIMER: number; MSG_ARMODE_ENTER: number; MSG_ARMODE_UIENTER: number; MSG_ARTOAST_TIMER: number; MSG_ARMODE_RESULT: number; V_WM_BMBAR: number; V_WM_ENGINE_ERROR_DMP: number;       V_WM_MAP_THEMESCENE_CHANGED: number; RENDER_STATE_STOP: number; RENDER_STATE_START: number; RENDER_STATE_STABLE: number; RENDER_STATE_DRAW_FRAME: number; RENDER_STATE_FOREGROUND: number; RENDER_STATE_ANIMATION_STOP: number; RENDER_STATE_ANIMATION_START: number; RENDER_STATE_ANIMATION_RUN: number; RENDER_STATE_ANIMATION_NONE: number; RENDER_STATE_FIRST_FRAME: number; RENDER_STATE_SWAP_DATA_BUFFER: number; MSG_MAP_DATA_NET_RESPONSE: number; }; 