// @keepTs
// @ts-nocheck
/**
 * Created on 2024/08/06
 * <AUTHOR>
 */
import { Context } from '@kit.AbilityKit';
import { ItemProvider } from '../../../base/item/provider/ItemProvider';
import { BaseUiConversation } from '../../model/BaseUiConversation';
/**
 * 会话 item 的 provider 基类
 * @version 1.0.0
 */
export declare abstract class BaseConversationItemProvider implements ItemProvider<BaseUiConversation> {
    private wrap;
    constructor();
    getWrapBuilder(): WrappedBuilder<[
        Context,
        BaseUiConversation,
        number
    ]>;
    abstract getConversationWrapBuilder(): WrappedBuilder<[
        Context,
        BaseUiConversation,
        number
    ]>;
}
