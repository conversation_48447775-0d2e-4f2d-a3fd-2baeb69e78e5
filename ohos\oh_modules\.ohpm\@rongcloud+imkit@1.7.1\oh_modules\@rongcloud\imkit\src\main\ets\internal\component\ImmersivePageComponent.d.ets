// @keepTs
// @ts-nocheck
@Component
export declare struct ImmersivePageComponent {
    pageTag: string;
    isRouter: boolean;
    @State
    statusBarBackgroundColor: ResourceColor;
    @State
    indicatorBackgroundColor: ResourceColor;
    @State
    private statusBarHeight;
    @State
    private indicatorHeight;
    private indicatorHeightValue;
    private currentWindow?;
    @State
    private pageContentHeight;
    private pageContentHeightValue;
    @State
    private isFullScreen;
    /**
     * 页面具体内容展示的 Builder
     */
    @Builder
    pageContentBuilder(): void;
    @BuilderParam
    pageContentBuilderParam: () => void;
    aboutToAppear(): Promise<void>;
    aboutToDisappear(): void;
    /**
     * 软键盘的隐藏和显示监听回调
     */
    private avoidAreaChangeCallback;
    build(): void;
}
