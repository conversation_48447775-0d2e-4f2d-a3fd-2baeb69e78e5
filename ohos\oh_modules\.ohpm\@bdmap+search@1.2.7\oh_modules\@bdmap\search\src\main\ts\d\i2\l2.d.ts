import { DistrictSearchOption } from "./j2";
import { DistrictResult } from "./k2";
/**
 * 行政区域检索接口
 */
export declare class DistrictSearch {
    private readonly iDistrictSearch;
    private constructor();
    /**
     * 获取行政区域检索对象
     * @return
     */
    static newInstance(): DistrictSearch;
    /**
     *  行政区域检索入口
     *
     * @param option 行政区域检索参数
     * @return 行政区域信息查询结果
     */
    searchDistrict(option: DistrictSearchOption): Promise<DistrictResult>;
}
