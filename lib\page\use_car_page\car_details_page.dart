import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../api/index.dart';
import '../../constant/web_view_url_tool.dart';
import '../../models/global_data.dart';
import '../../models/index.dart';
import '../../routes/jump_tool.dart';
import '../../utils/http/api_exception.dart';
import '../../utils/manager/dialog_manager.dart';
import '../../utils/manager/loading_manager.dart';
import '../../utils/manager/log_manager.dart';
import '../../widgets/common/custom_dialog.dart';
import '../../widgets/common/custom_refresher.dart';
import '../../widgets/use_car_page_widgets/car_details_page/car_details_page_widgets.dart';
import '../base_page/base_page.dart';

class CarDetailsPage extends BasePage {
  CarDetailsPage({
    Key? key,
    Color pageBackgroundColor = Colors.white,
  }) : super(
          key: key,
          appBarTitle: '车辆',
          pageBackgroundColor: pageBackgroundColor,
        );

  @override
  CarDetailsPageState createState() => CarDetailsPageState._();
}

class CarDetailsPageState extends BasePageState<CarDetailsPage> {
  //私有构造方法
  CarDetailsPageState._();
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  /// 上层传入参数
  // 默认爱车信息
  CarInfoModel? _carInfoModel;
  // 爱车车况
  CarStatusModel? _carStatusModel;
  CarNewEnergyBatteryModel? _newEnergyBatteryModel;
  CarEnergySpeedBatteryModel? _energySpeedBatteryModel;
  CarServiceModel? _chargeModel;
  CarServiceModel? _batteryModel;
  List<CarServiceModel> _newEnergyServiceList = [];
  String _yesterdayMileage = '0';
  String _chargeStatus = '0';
  bool _isAutoChargeSwitchOn = false;
  bool _isHeatSwitchOn = false;
  void onRefresh() async {
    try {
      CarStatusResponseModel carStatusResponseModel =
          await carAPI.getDefaultCarInfoAndStatus();
      CarInfoModel? carInfoModel = carStatusResponseModel.carInfo;
      CarStatusModel? carStatusModel = carStatusResponseModel.carStatus;
      CarMileageResponseModel? response =
          await carAPI.getYesterdayMileFromSevers({});
      final List<String> servicePositionCodeList = ['service_new_energy'];
      List<CarServiceResponseModel> serviceResponses =
          await carAPI.getCarServiceList(servicePositionCodeList);
      List<CarServiceModel> newEnergyServiceList = [];
      for (var serviceResponseModel in serviceResponses) {
        if (serviceResponseModel.positionCode == 'service_new_energy') {
          newEnergyServiceList = serviceResponseModel.serviceList ?? [];
        }
      }
      CarChargeStatusModel chargeStatusModel =
          await carAPI.getAutoChargeStatusFromSevers(_carInfoModel?.vin ?? '');
      CarNewEnergyBatteryModel batteryModel = await carAPI
          .getNewEnergyCarBatteryStatusWithVin(_carInfoModel?.vin ?? '');
      CarEnergySpeedBatteryModel energySpeedBatteryModel =
          CarEnergySpeedBatteryModel(
              limitFeedback: '',
              batteryStatus: carStatusModel?.batteryStatus,
              status: '',
              collectTime: carStatusModel?.collectTime,
              vin: carInfoModel?.vin);
      setState(() {
        _carInfoModel = carInfoModel;
        _carStatusModel = carStatusModel;
        _yesterdayMileage = response.trip ?? '0';
        _newEnergyServiceList = newEnergyServiceList;
        _energySpeedBatteryModel = energySpeedBatteryModel;
        _newEnergyBatteryModel = batteryModel;
        for (CarServiceModel serviceModel in _newEnergyServiceList) {
          if (serviceModel.serviceCode == 'autoCharge') {
            _chargeModel = serviceModel;
          }
          if (serviceModel.serviceCode == 'autoHeat') {
            _batteryModel = serviceModel;
          }
        }
        _chargeStatus = chargeStatusModel.chargingStatus ?? '0';
        String batteryStatus = _energySpeedBatteryModel?.batteryStatus ?? '1';
        if (int.parse(_chargeStatus) > 0) {
          _isAutoChargeSwitchOn = true;
        } else {
          _isAutoChargeSwitchOn = false;
        }
        if (int.parse(batteryStatus) == 0) {
          _isHeatSwitchOn = true;
        } else {
          _isHeatSwitchOn = false;
        }
      });

      if (_refreshController.isRefresh) {
        _refreshController.refreshCompleted();
      }
      LoadingManager.dismiss();
    } catch (e) {
      if (_refreshController.isRefresh) {
        _refreshController.refreshCompleted();
      }
      LoadingManager.dismiss();
    }
  }

  @override
  void pageInitState() {
    _carInfoModel = GlobalData().carInfoModel;
    _carStatusModel = null;
    String pageTitle = _carInfoModel?.carTypeName ?? '车辆详情';
    if ((_carInfoModel?.carName ?? '').isNotEmpty) {
      pageTitle = _carInfoModel?.carName ?? '车辆详情';
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      updateAppBarTitle(pageTitle);
      // _refreshController.requestRefresh();
      LoadingManager.show();
      onRefresh();
    });
    super.pageInitState();
  }

  List<Widget> getWidgetList() {
    List<Widget> list = [];
    list.add(CarImageWidget(
      imageUrl: _carInfoModel?.image ?? '',
      newEnergyBatteryModel: _newEnergyBatteryModel,
    ));
    list.add(const SizedBox(
      height: 10,
    ));
    list.add(CarAddressWidget(
        statusModel: _carStatusModel,
        carName: _carInfoModel?.carName ?? (_carInfoModel?.carTypeName ?? '')));
    list.add(const SizedBox(
      height: 10,
    ));
    list.add(CarInfoWidget(
      statusModel: _carStatusModel,
      infoModel: _carInfoModel,
      yesterdayMileage: _yesterdayMileage,
    ));
    list.add(const SizedBox(
      height: 20,
    ));
    if (_chargeModel != null) {
      CarServiceStatusModel? serviceStatusModel =
          (_chargeModel!.serviceStatusList ?? []).isNotEmpty
              ? _chargeModel!.serviceStatusList!.first
              : null;
      list.add(SettingItemWidget(
        leftIconPath: serviceStatusModel?.serviceStatusImage ??
            'assets/images/use_car_page/older_ev_page/ev_charge_auto_icon.png',
        title: serviceStatusModel?.serviceStatusName ?? '---',
        rightType: SettingItemType.toggle,
        toggleOffImageUrl:
            'assets/images/use_car_page/older_ev_page/energy_switch_off.png',
        toggleOnImageUrl:
            'assets/images/use_car_page/older_ev_page/energy_switch_on.png',
        isToggleOn: _isAutoChargeSwitchOn,
        onToggle: () {
          if (!_isAutoChargeSwitchOn) {
            showWillOpenSmartChargeAlertDialog(context);
          } else {
            setSmartChargeWithAction(0);
          }
        },
      ));
      if (!_isAutoChargeSwitchOn) {
        list.add(SettingItemWidget(
          leftIconPath:
              'assets/images/use_car_page/older_ev_page/ev_charge_manual_icon.png',
          rightIconPath:
              'assets/images/use_car_page/older_ev_page/car_details_tip.png',
          title: '12V蓄电池手动补电',
          rightType: SettingItemType.button,
          buttonColor: Color(0xff384967),
          buttonText: '一键补电',
          isButtonEnable: _newEnergyBatteryModel?.banRecharge == '0',
          onToggle: () {
            sendManualChargeRequest();
          },
          onRightIconTap: () => showManualRechargeMessage(),
        ));
      }
    }
    if (_batteryModel != null) {
      CarServiceStatusModel? serviceStatusModel =
          (_batteryModel!.serviceStatusList ?? []).isNotEmpty
              ? _batteryModel!.serviceStatusList!.first
              : null;
      list.add(SettingItemWidget(
        leftIconPath: serviceStatusModel?.serviceStatusImage ??
            'assets/images/use_car_page/older_ev_page/newEnergy.warm.icon.png',
        rightIconPath:
            'assets/images/use_car_page/older_ev_page/car_details_tip.png',
        title: serviceStatusModel?.serviceStatusName ?? '---',
        rightType: SettingItemType.toggle,
        toggleOffImageUrl:
            'assets/images/use_car_page/older_ev_page/energy_switch_off.png',
        toggleOnImageUrl:
            'assets/images/use_car_page/older_ev_page/energy_switch_on.png',
        isToggleOn: _isHeatSwitchOn,
        onToggle: () {
          showSureAlertViewWithAction(context, _isHeatSwitchOn ? 0 : 1);
        },
        onRightIconTap: () => showWarmMessage(),
      ));
    }
    return list;
  }

  /// 比较传入的时间和保存时间，判定状态是否过期
  /// [timeStr]：输入的时间字符串，格式为yyyy-MM-dd HH:mm:ss
  /// [oldTime]：保存的时间字符串，格式为yyyy-MM-dd HH:mm:ss
  bool isCarStatusOutOfDate(String timeStr, String? oldTime) {
    if (oldTime == null || oldTime.isEmpty) {
      // 如果没有保存的时间，则直接返回true，表示过期，需要保存当前的时间
      LogManager().log("[Bat_Collect]===保存时间为空，输入时间为$timeStr，保存输入时间为状态时间");
      return true;
    } else {
      // 解析输入时间和保存时间
      DateFormat format = DateFormat('yyyy-MM-dd HH:mm:ss');
      DateTime newTime = format.parse(timeStr);
      DateTime savedTime = format.parse(oldTime);

      // 转换为Unix时间戳（毫秒）
      int newTimeInterval = newTime.millisecondsSinceEpoch;
      int savedTimeInterval = savedTime.millisecondsSinceEpoch;

      // 计算时间差
      int dur = newTimeInterval - savedTimeInterval;

      if (dur < 0) {
        // 输入的时间不比保存的时间晚，状态未过期
        LogManager()
            .log("[TIME_TEST]===保存时间为$oldTime，输入时间为$timeStr，相差$dur毫秒，状态未过期");
        return false;
      } else {
        // 输入的时间比保存的时间晚，状态已过期
        LogManager().log(
            "[TIME_TEST]===保存时间为$oldTime，输入时间为$timeStr，相差$dur毫秒，状态已过期，更新输入时间为状态时间");
        return true;
      }
    }
  }

  void setSmartChargeWithAction(int action) async {
    if (_carInfoModel == null || (_carInfoModel?.vin ?? '').isEmpty) {
      return;
    }
    String vin = _carInfoModel?.vin ?? '';
    try {
      String address = 'junApi/sgmw/car/warning/charging/status/change';
      Map<String, dynamic> params = {};
      params['chargingStatus'] = action;
      params['vin'] = vin;
      String actionDesc = action == 1 ? '开启' : '关闭';
      String requestStatus = '正在$actionDesc 12V 蓄电池自动补电';
      LoadingManager.show(status: requestStatus);
      var response =
          await carAPI.requestCarControlServiceWithURLStr(address, params);
      CarNewEnergyBatteryModel newEnergyBatteryModel =
          await carAPI.getNewEnergyCarBatteryStatusWithVin(vin);
      LoadingManager.showSuccess('设置成功\n12V蓄电池自动补电已$actionDesc');
      setState(() {
        _isAutoChargeSwitchOn = action == 1 ? true : false;
      });
    } catch (e) {
      if (e is APIException) {
        LoadingManager.showError('设置失败\n ${e.message}');
      }
    }
  }

  void sendManualChargeRequest() async {
    if (_carInfoModel == null || (_carInfoModel?.vin ?? '').isEmpty) {
      return;
    }
    String vin = _carInfoModel?.vin ?? '';
    String actionDesc = '手动一键补电';
    try {
      String address = 'junApi/sgmw/car/control/recharge';
      Map<String, dynamic> params = {};
      params['status'] = 1;
      params['vin'] = vin;
      String requestStatus = '$actionDesc...';
      LoadingManager.show(status: requestStatus);
      var response =
          await carAPI.requestCarControlServiceWithURLStr(address, params);
      LoadingManager.showSuccess('$actionDesc成功');
      Map<String, dynamic> jsonMap = _newEnergyBatteryModel?.toJson() ?? {};
      if (jsonMap.containsKey('banRecharge')) {
        jsonMap['banRecharge'] = 1;
      }
      setState(() {
        _newEnergyBatteryModel = CarNewEnergyBatteryModel.fromJson(jsonMap);
      });
    } catch (e) {
      if (e is APIException) {
        LoadingManager.showError('$actionDesc失败\n ${e.message}');
      }
    }
  }

  void setEnergyCarParkHeatWithAction(int action) async {
    if (_carInfoModel == null || (_carInfoModel?.vin ?? '').isEmpty) {
      return;
    }
    String vin = _carInfoModel?.vin ?? '';
    try {
      String address = 'junApi/sgmw/car/option/keepWarn';
      Map<String, dynamic> params = {};
      params['status'] = action;
      params['vin'] = vin;
      String actionDesc = action == 1 ? '开启' : '关闭';
      String requestStatus = '正在发起$actionDesc电池智能保温设置 请稍候...';
      LoadingManager.show(status: requestStatus);
      var response =
          await carAPI.requestCarControlServiceWithURLStr(address, params);
      LoadingManager.showSuccess('设置成功\n电池智能保温功能已$actionDesc');
      setState(() {
        _isHeatSwitchOn = action == 1 ? true : false;
      });
    } catch (e) {
      if (e is APIException) {
        LoadingManager.showError('设置失败\n ${e.message}');
      }
    }
  }

  //智能保温说明
  void showWarmMessage() {
    DialogManager().showCustomDialog(
      CustomDialog(
        title: "电池智能保温说明",
        content:
            "智能保温功能会在插枪充满电后利用外部电源将动力电池温度维持在最佳工作温度，期间会产生额外耗电，请知悉！智能保温功能各阶段说明如下：\n【加热中】：动力电池当前温度低于适宜充电温度，正在利用外部电源给电池加热\n【充电中】：动力电池处于适宜充电温度，正在利用外部电源给电池充电\n【保温中】：动力电池已充满电，正在利用外部电源保温动力电池在最佳工作温度\n【保温完成】：动力电池已充满电。充满电后智能保温功能已保温电池24小时，本次保温结束。如需重新开启，请拔插一次充电枪。",
        buttonHeight: 60,
        buttons: [
          DialogButton(
              label: "我知道了",
              onPressed: () {},
              backgroundColor: Colors.white,
              textColor: const Color(0xffea0029)),
        ],
      ),
    );
  }

  //点击手动补电说明
  void showManualRechargeMessage() {
    DialogManager().showCustomDialog(
      CustomDialog(
        title: "12V蓄电池手动补电说明",
        content:
            "当您收到【蓄电池电量低】弹窗提醒且误关闭弹窗时，可点击【一键补电】按钮下发补电指令（可点击状态下），使用动力电池给12V蓄电池补电，点击后可通过消息通知获取补电状态。该按钮30分钟内只可点击一次，距上次点击未超过30分钟或12V蓄电池正在补电时不可点击。",
        buttonHeight: 60,
        buttons: [
          DialogButton(
              label: "我知道了",
              onPressed: () {},
              backgroundColor: Colors.white,
              textColor: const Color(0xffea0029)),
        ],
      ),
    );
  }

  void showWillOpenSmartChargeAlertDialog(BuildContext context) {
    DialogManager().showCustomDialog(
      CustomDialog(
        title: "12V蓄电池自动补电",
        content:
            "1、开启自动补电后,当您的爱车12V蓄电池电压过低时，车机将自动使用动力蓄电池给12V蓄电池补电，以避免因12V蓄电池亏电影响您的出行。\n2、如果您不想使用自动补电功能，也可以进行手动“一键补电”进行补电。",
        agreementText: '我已阅读并同意《12V蓄电池自动补电免责声明》',
        agreementLinkText: '《12V蓄电池自动补电免责声明》',
        forceAgree: true,
        buttonHeight: 70,
        onLinkPressed: () {
          String url = WebViewURLTool.disclaimerURL();
          JumpTool().openWeb(context, url, false);
        },
        buttons: [
          DialogButton(
              label: "暂不开启",
              onPressed: () {
                LogManager().debug("暂不开启按钮被点击");
              },
              backgroundColor: Colors.white,
              textColor: Colors.black),
          DialogButton(
              label: "立即开启",
              onPressed: () {
                // setState(() {
                //   _isAutoChargeSwitchOn = !_isAutoChargeSwitchOn;
                // });
                setSmartChargeWithAction(1);
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xffea0029)),
        ],
      ),
    );
  }

  void showSureAlertViewWithAction(BuildContext context, int heatAction) {
    String message = heatAction == 1
        ? '开启智能保温功能后，在插枪充满电后会利用外部电源将动力电池温度维持在最佳工作温度，期间会产生额外耗电，请知悉！'
        : '关闭智能保温功能后，在插枪充满电后不再进行保温。严寒天气用车时建议提前打开智能保温功能进行保温操作。';

    String leftTitle = heatAction == 1 ? '暂不开启' : '暂不关闭';
    String rightTitle = heatAction == 1 ? '确认开启' : '确认关闭';
    DialogManager().showCustomDialog(
      CustomDialog(
        title: "电池智能保温提示",
        content: message,
        buttonHeight: 70,
        onLinkPressed: () {
          String url = WebViewURLTool.disclaimerURL();
          JumpTool().openWeb(context, url, false);
        },
        buttons: [
          DialogButton(
              label: leftTitle,
              onPressed: () {},
              backgroundColor: Colors.white,
              textColor: Colors.black),
          DialogButton(
              label: rightTitle,
              onPressed: () {
                setEnergyCarParkHeatWithAction(heatAction);
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xffea0029)),
        ],
      ),
    );
  }

  @override
  Widget buildPageContent(BuildContext context) {
    // 在此处添加页面ui设置，默认添加一个center text组件
    return CustomSmartRefresher(
      controller: _refreshController,
      onRefresh: onRefresh,
      child: ListView(
        children: getWidgetList(),
      ),
    );
  }

  @override
  void dispose() {
    // 在此处添加页面销毁逻辑
    _refreshController.dispose();
    super.dispose();
  }
}
