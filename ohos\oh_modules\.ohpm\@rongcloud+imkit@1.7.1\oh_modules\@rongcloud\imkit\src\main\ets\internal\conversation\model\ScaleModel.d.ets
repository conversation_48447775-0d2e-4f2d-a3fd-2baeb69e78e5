// @keepTs
// @ts-nocheck
/**
 * Created on 2024/08/28
 * <AUTHOR>
 */
@Observed
export declare class ScaleModel {
    /**
     * scaleValue: 本次缩放因子，用于控制图片的大小显示
     * lastValue：记录上次缩放完后的缩放因子
     * maxScaleValue：默认的最大放大值
     * extraScaleValue：额外缩放因子
     * defaultScaleValue：默认缩放值，1
     */
    scaleValue: number;
    lastValue: number;
    maxScaleValue: number;
    extraScaleValue: number;
    readonly defaultScaleValue: number;
    constructor(l227?: number, m227?: number, n227?: number, o227?: number);
    reset(): void;
    stash(): void;
    toString(): string;
}
