// @keepTs
// @ts-nocheck
import { IConversationViewModel } from "../model/IConversationViewModel";
/**
 * ConversationComponent代理接口
 *
 *
 * # 示例
 * ```
 * 使用ConversationComponent方式集成会话页面，设置delegate接口后，可以通过onViewModelBind拿到ViewModel进行后续操作
 *&nbsp;@Component
 * struct ChatPage {
 *  private vm: IConversationViewModel | undefined
 *  private delegate: ConversationComponentDelegate = {
 *    onViewModelBind: (vm: IConversationViewModel) => {
 *      this.vm = vm;
 *    },
 *    onViewModelClear: () => {
 *      this.vm = undefined
 *    }
 *  }
 *  build() {
 *    ConversationComponent({
 *      conversationData: this.conversationComponentData,
 *      pageShow: this.pageShow,
 *      isEdit: this.isEdit,
 *      delegate: this.delegate,
 *    }).layoutWeight(1)
 *  }
 *```
 *
 * @version 1.7.1
 */
export interface ConversationComponentDelegate {
    /**
     * IConversationViewModel的创建回调
     * @version 1.7.1
     */
    onViewModelBind?: (vm: IConversationViewModel) => void;
    /**
     * IConversationViewModel的清理回调
     * @version 1.7.1
     */
    onViewModelClear?: () => void;
}
