import { BikingRoutePlanOption } from "../../../d/e/h/k";
import { SearchRequest } from '../../base/base';
import { UrlProvider } from "../../base/d3";
import { BikeRouteParser } from "./a4";
export declare class BikeRouteRequest extends SearchRequest {
    constructor(m20: BikingRoutePlanOption);
    getUrlDomain(l20: UrlProvider): string;
    private requestBikingRouteBuildParam;
    private addWayPoints;
}
export { BikeRouteParser };
