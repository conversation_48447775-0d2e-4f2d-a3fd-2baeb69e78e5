// @keepTs
// @ts-nocheck
import { ConversationIdentifier, IAsyncResult, Message, MessageDestructionListener, MessageReadReceiptListener, MessageRecalledListener, MessageReceivedListener, TypingStatusListener } from '@rongcloud/imlib';
import List from '@ohos.util.List';
import { MessageEventListener } from '../conversation/listener/MessageEventListener';
import { MessageInterceptor } from '../conversation/listener/MessageInterceptor';
import { CombineMessageEventListener } from '../conversation/listener/CombineMessageEventListener';
import { CombineMessage } from './content/CombineMessage';
/**
 * 消息服务
 * @version 1.0.0
 * @warning 消息相关的方法请调用该类的，而不要调用 IMLib 的，调用该类的方法可以出发监听，而 IMLib 的不会触发监听
 */
export interface MessageService {
    /**
     * 增加收消息监听
     * @param listener 监听
     * @warning addMessageReceiveListener & removeMessageReceiveListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     */
    addMessageReceiveListener(listener: MessageReceivedListener): void;
    /**
     * 移除收消息监听
     * @param listener 监听
     * @warning addMessageReceiveListener & removeMessageReceiveListener 配合使用，避免内存泄露
     */
    removeMessageReceiveListener(listener: MessageReceivedListener): void;
    /**
     * 添加消息撤回监听
     * @param listener 监听
     * @warning addMessageRecalledListener & removeMessageRecalledListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     */
    addMessageRecalledListener(listener: MessageRecalledListener): void;
    /**
     * 移除消息撤回监听
     * @param listener 监听
     * @warning addMessageRecalledListener & removeMessageRecalledListener 配合使用，避免内存泄露
     */
    removeMessageRecalledListener(listener: MessageRecalledListener): void;
    /**
     * 增加消息事件监听
     * @param listener 监听
     * @warning addMessageEventListener & removeMessageEventListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     */
    addMessageEventListener(listener: MessageEventListener): void;
    /**
     * 移除消息事件监听
     * @param listener 监听
     * @discussion 配合 addMessageEventListener 使用，否则会出现内存泄露
     */
    removeMessageEventListener(listener: MessageEventListener): void;
    /**
     * 增加合并转发消息事件监听
     * @param listener 监听
     * @warning addCombineMessageEventListener & removeCombineMessageEventListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     */
    addCombineMessageEventListener(listener: CombineMessageEventListener): void;
    /**
     * 移除合并转发消息事件监听
     * @param listener 监听
     * @discussion 配合 addCombineMessageEventListener 使用，否则会出现内存泄露
     */
    removeCombineMessageEventListener(listener: CombineMessageEventListener): void;
    /**
     * 设置消息拦截器，只能设置一个
     * @param intercept 拦截器
     */
    setMessageInterceptor(intercept: MessageInterceptor): void;
    /**
     * 获取消息拦截器
     * @returns 拦截器
     */
    getMessageInterceptor(): MessageInterceptor | undefined;
    /**
     * 批量消息入库
     * @param msgList 消息列表
     * @returns 入库结果
     */
    batchInsertMessage(msgList: List<Message>): Promise<IAsyncResult<void>>;
    /**
     * 单条消息入库
     *
     * @param Message 需要入库的消息，会话类型不支持聊天室和超级群
     * @returns 入库结果。如果入库成功，会返回Message，并且该Message对象含有效的 messageId。
     */
    insertMessage(msg: Message): Promise<IAsyncResult<Message>>;
    /**
     * 发送普通消息
     * # 重要
     *```
     * 在使用 IMKit 时，发送消息请使用该方法发送消息，该方法发送的消息可以自动在 UI 上展示
     * 如果使用 IMEngine 发送消息， UI 不会自动更新
     *```
     * @param msg 消息体
     * @returns 发送结果
     * @warning 调用该接口才能触发消息拦截
     */
    sendMessage(msg: Message): Promise<IAsyncResult<Message>>;
    /**
     * 发送媒体消息
     * # 重要
     *```
     * 在使用 IMKit 时，发送消息请使用该方法发送消息，该方法发送的消息可以自动在 UI 上展示
     * 如果使用 IMEngine 发送消息， UI 不会自动更新
     *```
     * @param msg 消息体
     * @param progressListener 媒体上传进度监听
     * @returns 发送结果
     * @warning 调用该接口才能触发消息拦截
     */
    sendMediaMessage(msg: Message, progressListener?: (msg: Message, progress: number) => void): Promise<IAsyncResult<Message>>;
    /**
     * 取消发送媒体消息下载
     * # 重要
     *```
     * 1，在使用 IMKit 时，取消发送消息请使用该方法，该方法取消消息可以自动在 UI 上展示取消状态
     * 如果使用 IMEngine 取消发送消息， UI 不会自动更新
     *
     * 2，调用本接口成功后，sendMessage、sendMediaMessage会返回RequestCanceled(33200)错误码。
     *```
     * @param messageId 消息 Id
     * @returns 取消发送媒体消息结果
     * @version 1.4.3
     * */
    cancelSendMediaMessage(messageId: number): Promise<IAsyncResult<void>>;
    /**
     * 批量删除消息
     * @param conId 会话标识
     * @param messages 消息数组
     * @param remoteDelete 是否删除远端
     * @returns 结果
     */
    batchDeleteMessage(conId: ConversationIdentifier, messages: Message[], remoteDelete?: boolean): Promise<IAsyncResult<void>>;
    /**
     * 撤回消息
     * @param message 需要撤回的消息
     * @returns 撤回结果
     */
    recallMessage(message: Message): Promise<IAsyncResult<void>>;
    /**
     * 发送输入状态，仅支持单聊
     * @param msg 消息体
     * @returns 发送结果
     * @warning 调用该接口才能触发消息拦截
     */
    sendTypingStatus(conId: ConversationIdentifier, objectName: string): Promise<IAsyncResult<void>>;
    /**
     * 增加输入状态的监听
     * @param listener 监听
     * @warning addTypingStatusListener & removeTypingStatusListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     */
    addTypingStatusListener(listener: TypingStatusListener): void;
    /**
     * 移除输入状态的监听
     * @param listener 监听
     * @warning addTypingStatusListener & removeTypingStatusListener 配合使用，避免内存泄露
     */
    removeTypingStatusListener(listener: TypingStatusListener): void;
    /**
     * 获取本地一批消息
     * @param conId 回话标识
     * @param objNames objectName 数组
     * @param time 时间，默认 Date.now()
     * @param before 默认 50
     * @param after 默认 50
     * @returns 本地消息
     */
    getHistoryMessagesByTime(conId: ConversationIdentifier, objNames: List<string>, time?: number, before?: number, after?: number): Promise<IAsyncResult<List<Message>>>;
    /**
     * 删除消息
     * @param conId 会话标识
     * @param remoteDelete 是否删除远端，默认为 false
     * @returns 删除结果
     */
    deleteConversationMessages(conId: ConversationIdentifier, remoteDelete?: boolean): Promise<IAsyncResult<void>>;
    /**
     * 增加消息阅后即焚监听，仅支持单聊，仅限阅后即焚消息的接收方调用
     * @param listener 监听
     * @discussion addMessageDestructionListener & removeMessageDestructionListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     */
    addMessageDestructionListener(listener: MessageDestructionListener): void;
    /**
     * 移除消息阅后即焚监听，仅支持单聊，仅限阅后即焚消息的接收方调用
     * @param listener 监听
     * @discussion addMessageDestructionListener & removeMessageDestructionListener 配合使用，避免内存泄露
     */
    removeMessageDestructionListener(listener: MessageDestructionListener): void;
    /**
     * 消息开始阅后即焚倒计时，仅支持单聊，仅限阅后即焚消息的接收方调用
     * @param message 消息体
     * @note 调用之后，阅后即焚消息开始倒计时，触发 MessageDestructionListener.onMessageDestructing
     */
    messageBeginDestruct(message: Message): Promise<IAsyncResult<void>>;
    /**
     * 消息停止阅后即焚倒计时，仅支持单聊，仅限阅后即焚消息的接收方调用
     *```
     * 调用之后，该消息终止阅后即焚
     * 阅后即焚倒计时停止，并触发 MessageDestructionListener.onMessageDestructionStop
     * 可以调用 messageBeginDestruct() 重新进行倒计时
     *```
     * @param message 消息体
     */
    messageStopDestruct(message: Message): Promise<IAsyncResult<void>>;
    /**
     * 增加消息已读回执监听
     * @param listener 监听
     * @discussion addMessageReadReceiptListener & removeMessageReadReceiptListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     */
    addMessageReadReceiptListener(listener: MessageReadReceiptListener): void;
    /**
     * 移除消息已读回执监听
     * @param listener 监听
     * @discussion addMessageReadReceiptListener & removeMessageReadReceiptListener 配合使用，避免内存泄露
     */
    removeMessageReadReceiptListener(listener: MessageReadReceiptListener): void;
    /**
     * 构建合并转发消息
     *
     * 重要说明
     *```
     * 一. 构建合并转发消息的流程：基于合并转发 HTML 模版，把消息数组 `forwardMessages` 逐个转换为 HTML body 插入到模版中，拼接成完整的 HTML 页面。
     * 二. `unsupportedMessageHandler` 参数说明：
     *  1. 仅当 SDK 转换 `forwardMessages` 遇到不支持的消息类型时回调此 function，`message` 是不支持的消息。如开发者准备渲染该消息，则需要返回 body 内容。
     *  2. SDK 不会校验 function 返回的 body 内容，如内容异常会导致合并转发页面加载失败，需开发者保证内容有效。如返回空字符串则不会处理。
     *```
     *
     * 示例
     *
     * 1，定义"CUSTOM:MSG" 类型消息对应的 HTML body 内容，支持使用自定义样式，支持点击事件。
     * - 自定义合并转发样式通过 setCombineHtmlStyle 来设置。使用方式参照 {@link ConversationConfig#setCombineHtmlStyle}。
     * - 必须设置 onClick='show()' 来保证SDK可以接收JS点击事件，例：onClick='show({extra:"自定义透传参数",title:"标题",type:"CUSTOM:MSG"})'
     * - 设置合并转发事件接口，可以拦截JS点击事件，参照 {@link CombineMessageEventListener#onJSCallNativeInterceptor}。
     * - 下面是SDK的示例，一般情况下在这个示例上调整即可。内置标签如：{%portrait%}、{%showUser%}、{%userName%}、{%sendTime%}，与消息类型无关，由SDK进行处理。
     *
     * HTML body 示例：
     *```
     * let customMsgHtmlData:string =
     * "<!-- 1，点击事件：必须统一使用show()来进行鸿蒙原生JS回调，内容是Json格式 -->\n" +
     * "<div class='rong-message {%showUser%}' onClick='show({extra:\"自定义透传参数\",title:\"标题\",type:\"CUSTOM:MSG\"})'>\n" +
     * "        <div class='rong-message-user rong-none-user-img'><img src='{%portrait%}' class='rong-message-user-bg rong-message-user-portrait' /></div>\n" +
     * "        <div class='rongcloud-message-body'>\n" +
     * "                <!-- 2，修改name修改为消息的objectName -->\n" +
     * "                <div name='CUSTOM:MSG'>\n" +
     * "                        <div class='rongcloud-message-user-name'><span name='userName'>{%userName%}</span><span class='rong-message-time' name='sendTime'>{%sendTime%}</span></div>\n" +
     * "                        <!-- 3，摆放 html body 标签 -->\n" +
     * "                        <!-- 这里rongcloud-message-text 是内置的样式 -->\n" +
     * "                        <div class='rongcloud-message-text'>\n" +
     * "                                <pre class='rongcloud-message-entry'>{%customMsgField1_SDKStyle%}</pre>\n" +
     * "                        </div>\n" +
     * "                        <!-- 这里 custom-msg-style1、custom-msg-style2 是自定义的样式 -->\n" +
     * "                        <div class='custom-msg-style1'>\n" +
     * "                                <pre class='rongcloud-message-entry'>{%customMsgField2_CustomStyle%}</pre>\n" +
     * "                        </div>\n" +
     * "                        <div class='custom-msg-style2'>\n" +
     * "                                <pre class='rongcloud-message-entry'>{%customMsgField3_CustomStyle%}</pre>\n" +
     * "                        </div>\n" +
     * "                </div>\n" +
     * "        </div>\n" +
     * "</div>";
     *```
     *
     * 2. 合并转发自定义的样式示例：
     *```
     * let customMsgHtmlStyle: string = ".custom-msg-style1 {\n" +
     * "    font-size: 14px;\n" +
     * "    color: #FF0000;\n" +
     * "    margin: 0;\n" +
     * "    padding: 0;\n" +
     * "  }\n" +
     * "  .custom-msg-style2 {\n" +
     * "    font-size: 16px;\n" +
     * "    color: #CC0066;\n" +
     * "    margin: 0;\n" +
     * "    padding: 0;\n" +
     * "  }";
     *```
     * 3.  `unsupportedMessageHandler` 示例：
     *```
     * let unsupportedMessageHandler = (message: Message) => {
     *      return new Promise<string>((resolve) => {
     *        if (message.objectName === CustomTextMessageObjectName) {
     *          // 示例replaceContent是从Message中取到的字段，准备替换到html模版中。
     *          let field1 = "SDK样式+自定义消息字段1"
     *          let field2 = "自定义样式+自定义消息字段2"
     *          let field3 = "自定义样式+自定义消息字段3"
     *          // 如果有多个内容需要替换，则逐个replace替换.
     *          let html = customMsgHtmlData
     *                     .replaceAll("{%customMsgField1_SDKStyle%}", field1)
     *                     .replaceAll("{%customMsgField2_CustomStyle%}", field2)
     *                     .replaceAll("{%customMsgField3_CustomStyle%}", field3);
     *          resolve(html)
     *        } else {
     *          resolve("")
     *        }
     *      })
     *    }
     * let forwardMessage: Message[] = []
     * RongIM.getInstance().messageService().obtainCombineMessage(forwardMessage, unsupportedMessageHandler)
     *```
     *
     * @param forwardMessages 用来构建合并转发消息的消息数组
     * @param unsupportedMessageHandler 用于开发者处理合并转发不支持类型的消息转换为 HTML 内容的逻辑。
     * @returns 返回 CombineMessage，如果返回 undefined 则代表构建失败。
     */
    obtainCombineMessage(forwardMessages: Message[], unsupportedMessageHandler?: (message: Message) => Promise<string>): Promise<IAsyncResult<CombineMessage>>;
}
