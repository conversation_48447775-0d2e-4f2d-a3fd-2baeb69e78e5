// @keepTs
// @ts-nocheck
import { GroupInfoModel } from '../../../user/model/GroupInfoModel';
import { GroupMemberInfoModel } from '../../../user/model/GroupMemberInfoModel';
import { UserInfoModel } from '../../../user/model/UserInfoModel';
/**
 * 用户模块内存管理
 * 内存缓存，需要做数量限制，例如每类信息最多缓存 100 个，需要做淘汰策略
 */
export declare class UserDataMemCache {
    private enableMemCache;
    private userCache;
    private groupCache;
    private groupMemberCache;
    private currentUserInfo?;
    clearAll(): void;
    setMemCacheEnable(b330: boolean): void;
    getMemCacheEnable(): boolean;
    setCurrentUserInfo(a330?: UserInfoModel): void;
    getCurrentUserInfo(): UserInfoModel | undefined;
    getUserInfoModel(z329: string): UserInfoModel | undefined;
    updateUserInfoModel(y329: UserInfoModel): void;
    clearUserInfoModel(): void;
    getGroupInfoModel(x329: string): GroupInfoModel | undefined;
    updateGroupInfoModel(w329: GroupInfoModel): void;
    clearGroupInfoModel(): void;
    getGroupMemberInfoModel(t329: string, u329: string): GroupMemberInfoModel | undefined;
    updateGroupMemberInfoModel(r329: GroupMemberInfoModel): void;
    batchUpdateGroupMemberInfoModels(n329: GroupMemberInfoModel[]): void;
    clearGroupMemberInfoModel(): void;
    /**
     * 生成群成员缓存的 key ，
     * @param groupId
     * @param userId
     * @returns
     */
    private genGroupMemberKey;
}
