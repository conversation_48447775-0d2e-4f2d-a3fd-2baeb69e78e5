import { Point } from '@bdmap/base'; import { BmCoordChain } from "./d2"; import BmObject from "./u2"; import BmLineStyle from "./f2/w3"; import BmTrackStyle from "./f2/z3"; import BmLineStyleOption from "./f2/x3"; import BmCoordChainHandle from "./g3"; export declare class BmGeoElement extends BmObject { private mCoordChainType; private mLinestyle; private mTrackStyle; private pointList; private mEncodedPoints; private mEncodePointType; constructor(i11?: BmCoordChain); getStyle(): BmLineStyle;           setStyle(style: BmLineStyle): any; getTrackStyle(): BmTrackStyle; setTrackStyle(style: BmTrackStyle): any;           addStyleOption(styleOption: BmLineStyleOption): any;           removeStyleOption(styleOption: BmLineStyleOption): any;           addPoint(point: Point): any;           setPoints(points: Array<Point>): any; setPoints3(points: Array<Point>): any;             setGradientColors(type: number, c11: Array<number>): any;           delGradientColors(type: number): any; clearGradientColors(): any;           setCoordChainType(b11: number): any; setCoordChainHandle(handle: BmCoordChainHandle): any; styleDestroy(delay?: boolean): void; destroy(delay?: boolean): void; } 