// @keepTs
// @ts-nocheck
/**
 * Created on 2024/08/06
 * <AUTHOR>
 */
import { Conversation } from '@rongcloud/imlib';
import { BaseUiConversation } from '../../../conversationlist/model/BaseUiConversation';
import { image } from '@kit.ImageKit';
import { UserInfoModel } from '../../../user/model/UserInfoModel';
import { GroupMemberInfoModel } from '../../../user/model/GroupMemberInfoModel';
import { GroupInfoModel } from '../../../user/model/GroupInfoModel';
@Observed
export declare class GroupUiConversation extends BaseUiConversation {
    private sendName;
    imagePixelMap: image.PixelMap | undefined;
    onUserInfoUpdate(o278: UserInfoModel): Promise<void>;
    onGroupInfoUpdate(m278: GroupInfoModel): void;
    onGroupMemberUpdate(k278: GroupMemberInfoModel): Promise<void>;
    onConversationUpdate(i278: Conversation, j278: boolean): Promise<void>;
    /**
     * 更新会话消息中的发送者名称
     * @param conversation
     * @param userInfo
     * @param groupMember
     */
    /**
     * 更新会话消息中的发送者名称
     * @param conversation 会话对象
     * @param userInfo 用户信息,可选参数
     * @param groupMember 群成员信息,可选参数
     */
    private updateSendName;
    /**
     * 更新最后一条消息显示的内容
     * @param conversation 会话
     */
    private updateMessageContent;
    refreshData(k277: string): void;
    /**
     * 获取图片
     * @param resource
     * @returns
     */
    private getPixmapFromMedia;
    /**
     * 设置语音消息样式
     * @param lastMessage
     * @param value
     */
    private setVoiceMutableStyledString;
}
