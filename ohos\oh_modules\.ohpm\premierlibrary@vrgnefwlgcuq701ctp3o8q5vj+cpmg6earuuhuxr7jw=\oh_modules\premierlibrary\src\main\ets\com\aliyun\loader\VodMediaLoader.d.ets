import { MediaInfo } from '../player/nativeclass/MediaInfo';
import { VidAuth } from '../player/source/VidAuth';
import { VidSts } from '../player/source/VidSts';
import { ErrorInfo } from '../player/bean/ErrorInfo';
export declare class VodMediaLoader {
    private static sInstance;
    private mOnLoadStatusListener?;
    private objHelper;
    private constructor();
    static getInstance(): VodMediaLoader;
    prepareVidStsSource(a6: VidSts): void;
    prepareVidAuthSource(z5: VidAuth): void;
    removeVidSource(y5: string): void;
    load(t5: string, u5: number, v5: number): void;
    cancel(p5: string, q5: number): void;
    pause(l5: string, m5: number): void;
    resume(h5: string, i5: number): void;
    setOnLoadStatusListener(g5: OnVodLoadStatusListener): void;
    protected onPrepared: Function;
    protected onError: Function;
    protected onErrorV2: Function;
    protected onCompleted: Function;
    protected onCanceled: Function;
}
export interface OnVodLoadStatusListener {
    /**
     * 准备成功。成功之后，可以调用load去加载某个清晰度的视频。
     */
    onPrepared: (mediaInfo: MediaInfo) => void;
    /**
     * 加载出错
     */
    onError: (vid: string, index: number, code: number, msg: string) => void;
    /**
     * 加载出错V2，推荐使用
     */
    onErrorV2: (vid: string, index: number, errorInfo: ErrorInfo) => void;
    /**
     * 加载完成
     */
    onCompleted: (vid: string, index: number) => void;
    /**
     * 取消加载
     */
    onCanceled: (vid: string, index: number) => void;
}
