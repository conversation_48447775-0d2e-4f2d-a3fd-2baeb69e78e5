//
//  CYUnifiedMQTTHelper.m
//  LingLingBang
//
//  Created by admin on 2022/2/22.
//  Copyright © 2022 linglingbang. All rights reserved.
//

#import "CYUnifiedMQTTHelper.h"

#import "CYUserModel.h"
#import "CYUnifiedGetTokenHTTPModel.h"
//#import "CYUnifiedStartUpHTTPModel.h"
#import "AFNetworking.h"
#import "HttpUtil.h"
#import "BJLogRecordManager.h"
//#import "CYMemoryParkingOperationVC.h"
//#import "CYMemoryParkingRemoteVC.h"

#import <ReactiveCocoa/ReactiveCocoa.h>
@interface CYUnifiedMQTTHelper()
//是否取消重连（主动断开时使用）
@property (nonatomic, assign) BOOL          isCancelReconnect;

//5秒查询状态
@property (nonatomic, strong) NSTimer *statusTimer;

@end
@implementation CYUnifiedMQTTHelper

- (instancetype)init {
    self = [super init];
    if (self) {
        // 去掉不必要的Log
        ddLogLevel = DDLogLevelError;
        _session = [[MQTTSession alloc] init];
        MQTTCFSocketTransport *transport = [[MQTTCFSocketTransport alloc] init];
//        MQTTSSLSecurityPolicyTransport *transport = [[MQTTSSLSecurityPolicyTransport alloc] init];
        transport.host = @"parkingdata.sgmwcloud.com.cn";
        transport.port = 1883;
//        transport.tls = YES;
//        transport.securityPolicy = [MQTTSSLSecurityPolicy defaultPolicy];
        _session.willQoS = MQTTQosLevelAtLeastOnce;
//        _session.cleanSessionFlag = NO;
        _session.transport = transport;
        _session.delegate = self;
        _session.keepAliveInterval = 5;
        [_session addObserver:self forKeyPath:@"status" options:(NSKeyValueObservingOptionOld) context:nil];
        [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(disConnectServer) name:kLogoutNotfi object:nil];
        _pairs = [NSMutableArray array];
        self.isNetwork = YES;
        
        //[self reachability];
    }
    return self;
}

+ (instancetype)shared {
    static dispatch_once_t onceToken;
    static CYUnifiedMQTTHelper *helper = nil;
    dispatch_once(&onceToken, ^{
        helper = [[self alloc] init];
    });
    return helper;
}

#pragma mark  实现代理方法，监听MQTT的状态
- (void)observeValueForKeyPath:(NSString *)keyPath ofObject:(id)object change:(NSDictionary<NSKeyValueChangeKey,id> *)change context:(void *)context {
    BJLog(@"MQTT的状态:%ld --isCancelReconnect: %d ", self.session.status, self.isCancelReconnect);
//    if (!self.isCancelReconnect && [CYUnifiedMQTTHelper shared].supportMqtt) {
//        //如果不是主动断开的，需要进行重连
//        NSString *network = [HttpUtil getNetconnType];
//        if ((self.session.status == MQTTSessionStatusDisconnecting || self.session.status == MQTTSessionStatusError || self.session.status == MQTTSessionStatusClosed) && ![network isEqualToString:@"no_network"] ) {
//           //重连即可
//            self.isCancelReconnect = YES;
//            [self reConnectMTQQ];
//        }
//    }
}

- (void)reachability {
    AFNetworkReachabilityManager *manage = [AFNetworkReachabilityManager sharedManager];

    [manage setReachabilityStatusChangeBlock:^(AFNetworkReachabilityStatus status) {
        switch (status) {
            case AFNetworkReachabilityStatusUnknown: // 未知网络
                {
                    NSLog(@"未知网络");
                    self.isNetwork = NO;
                }
            break;
            case AFNetworkReachabilityStatusNotReachable: // 没有网络(断网)
                {
                    NSLog(@"没有网络(断网)");
                    self.isNetwork = NO;
                }
            break;
            case AFNetworkReachabilityStatusReachableViaWWAN: case AFNetworkReachabilityStatusReachableViaWiFi:  // 手机自带网络 WIFI
                {
                    NSLog(@"手机自带网络");
                    if (!self.isNetwork && self.session.status != MQTTSessionStatusConnected) {
                        //重连
                        [self reConnectMTQQ];
                    }
                    self.isNetwork = YES;
                }
            break;
        }
    }];
    // 开始监控
    [manage startMonitoring];
}

- (void)reConnectMTQQ {

    if ([CYUserModel isLogin] && self.session.status != MQTTSessionStatusConnected && self.session.status != MQTTSessionStatusConnecting && self.supportMqtt) {
        //进行重连的时候，在未获取到登录密码和token的时候，不再进行请求token
        BJLog(@"MQTT: 重新连接....");
        [CYUnifiedMQTTHelper getTokenWithVin:self.prefix success:^(NSString * _Nonnull username, NSString * _Nonnull password, NSString * _Nonnull clientId, NSString * _Nonnull vin) {
            BJLog(@"MQTT: 重新获取账号密码成功");
            // 获取密码后不再重新连接
            // 登录MQTT
            [self connectWithUsername:username password:password clientId:clientId success:^{
                self.isCancelReconnect = NO;
                BJLog(@"MQTT: 重新登录成功");
            } failure:^{
                self.isCancelReconnect = NO;
                __weak typeof(self) weakself = self;
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [weakself reConnectMTQQ];
                });
                BJLog(@"MQTT: 重新登录失败");
            }];
        } failure:^{
            BJLog(@"MQTT: 重新获取账号密码失败");
        }];
    }
    /*
    __weak typeof(self) weakself = self;
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        if ([CYUserModel isLogin] && self.session.status != MQTTSessionStatusConnected && self.session.status != MQTTSessionStatusConnecting) {
            weakself.isCancelReconnect = YES;
            [weakself.session connectWithConnectHandler:^(NSError *error) {
                if (error) {
                    BJLog(@"MQTT: failure %@", error);
                    //进行重连的时候，在未获取到登录密码和token的时候，再进行请求token
                    [CYUnifiedMQTTHelper getTokenWithVin:weakself.prefix success:^(NSString * _Nonnull username, NSString * _Nonnull password, NSString * _Nonnull clientId, NSString * _Nonnull vin) {
                        BJLog(@"MQTT: 重新获取账号密码成功");
                        // 获取密码后不再重新连接
                        // 登录MQTT
                        [weakself connectWithUsername:username password:password clientId:clientId success:^{
                            weakself.isCancelReconnect = NO;
                            BJLog(@"MQTT: 重新登录成功");
                        } failure:^{
                            weakself.isCancelReconnect = NO;
                            [weakself reConnectMTQQ];
                            BJLog(@"MQTT: 重新登录失败");
                        }];
                    } failure:^{
                        weakself.isCancelReconnect = NO;
                        [weakself reConnectMTQQ];
                        BJLog(@"MQTT: 重新获取账号密码失败");
                    }];
                } else {
                    BJLog(@"MQTT: connect success");
                    [weakself subcribeTopicsWithCompletion:^(BOOL isSuccess) {
                        weakself.isCancelReconnect = NO;
                        if (isSuccess) {
                            BJLog(@"MQTT:  订阅成功");
                        } else {
                            BJLog(@"MQTT:  订阅失败");
                            [weakself reConnectMTQQ];
                        }
                    }];
                }
            }];
        }
    });
     */
}

#pragma mark 用户退出登录，MQTT关闭
- (void)disConnectServer {
//    self.session.delegate = nil;
    [self.session disconnect];
//    self.session = nil;
    [self cancelTimer];
}

#pragma mark 获取token并登录
- (void)getTokenAndLoginWithVin:(NSString *)vin complete:(void(^_Nullable)(BOOL))complete{
    if (self.session.status != MQTTSessionStatusConnected && self.session.status != MQTTSessionStatusConnecting) {
        // 获取MQTT登录账号密码等信息
        [CYUnifiedMQTTHelper getTokenWithVin:vin success:^(NSString * _Nonnull username, NSString * _Nonnull password, NSString * _Nonnull clientId, NSString * _Nonnull vin) {
            BJLog(@"MQTT: 获取账号密码成功");
            // 登录MQTT
            [[CYUnifiedMQTTHelper shared] loginWithUsername:username password:password clientId:clientId vin:vin success:^{
                !complete?: complete(YES);
                BJLog(@"MQTT: 登录成功");
            } failure:^{
                !complete?: complete(NO);
                BJLog(@"MQTT: 登录失败");
            }];
        } failure:^{
            !complete?: complete(NO);
            BJLog(@"MQTT: 获取账号密码失败");
        }];
    }
}

+ (void)getTokenWithVin:(NSString *)vin success:(void (^ _Nullable)(NSString * _Nonnull, NSString * _Nonnull, NSString * _Nonnull, NSString * _Nonnull))success failure:(void (^ _Nullable)(void))failure {
    if (![CYUserModel isLogin] || !vin.length) {
        !failure ?: failure();
        return;
    }
    
    BJLog(@"MQTT: --Vin:%@",vin);
    NSString *phone = [CYUserModel sharedInstance].mobile;
    // 获取 MQTT 的token，生成用户名和密码
    [CYUnifiedGetTokenHTTPModel getTokenWithVIN:vin success:^(CYUnifiedGetTokenHTTPModel * _Nullable model) {
        if (model && model.token.length) {
            // vin + _ + 手机号后4位
            NSString *clientID = [NSString stringWithFormat:@"%@_%@", vin, [phone substringWithRange:NSMakeRange(phone.length-4, 4)]];
            // md5(clientID前6位 + token)
            NSString *username_org = [NSString stringWithFormat:@"%@%@", [clientID substringWithRange:NSMakeRange(0, 6)], model.token];
            NSString *username = [username_org md5String];
            // md5(clientID后6位 + token)
            NSString *password_org = [NSString stringWithFormat:@"%@%@", [clientID substringWithRange:NSMakeRange(clientID.length-6, 6)], model.token];
            NSString *password = [password_org md5String];
            
            !success ?: success(username, password, clientID, vin);
        } else {
            !failure ?: failure();
        }
    } failure:^(NSError * _Nullable error) {
        BJLog(@"MQTT: %@",error.localizedDescription);
        !failure ?: failure();
    }];
}

- (void)loginWithUsername:(NSString *)username password:(nonnull NSString *)password clientId:(nonnull NSString *)clientId vin:(nonnull NSString *)vin success:(void (^ _Nullable)(void))success failure:(void (^ _Nullable)(void))failure {
    // 打印输入参数
    NSLog(@"MQTTHelper: 登录参数");
    NSLog(@"MQTTHelper:   username: %@", username);
    NSLog(@"MQTTHelper:   password: %@", password);
    NSLog(@"MQTTHelper:   clientId: %@", clientId);
    NSLog(@"MQTTHelper:   vin: %@", vin);
    
    self.username = username;
    self.password = password;
    self.clientId = clientId;
    self.prefix = vin;
    // 重新登录MQTT服务器时重新设置需要重连
    self.isCancelReconnect = NO;
    // 登录 MQTT
    [self connectWithUsername:username password:password clientId:clientId success:success failure:^{
        __weak typeof(self) weakself = self;
        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(5.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [weakself reConnectMTQQ];
        });
        failure();
    }];
}

- (void)disconnect {
    // 主动断开连接时，不触发重连
    self.isCancelReconnect = YES;
    [self.session disconnect];
    [self cancelTimer];
}

- (void)addTarget:(id)target action:(SEL)action forTopic:(CYUnifiedTopic)topic {
    // target/action对是否已存在
    __block BOOL existed = NO;
    NSArray<CYUnifiedMQTTHelperPair*> *pairs = [self.pairs copy];
    [pairs enumerateObjectsUsingBlock:^(CYUnifiedMQTTHelperPair * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if (obj.target == nil || obj.action == nil || ![obj.target respondsToSelector:obj.action]) {
            // 移除非法action对
            [self.pairs removeObject:obj];
        } else if (obj.target == target && obj.action == action && obj.topic == topic) {
            // action对已存在
            existed = YES;
        }
    }];
    // 如果action对已存在或非法，不添加
    if (existed == NO && [target respondsToSelector:action]) {
        CYUnifiedMQTTHelperPair *pair = [[CYUnifiedMQTTHelperPair alloc] init];
        pair.target = target;
        pair.action = action;
        pair.topic = topic;
        [self.pairs addObject:pair];
    }
}

- (void)removeTarget:(id)target {
    if (target == nil) {
        [self.pairs removeAllObjects];
        return;
    }
    NSArray<CYUnifiedMQTTHelperPair*> *pairs = [self.pairs copy];
    [pairs enumerateObjectsUsingBlock:^(CYUnifiedMQTTHelperPair * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        if (obj.target == nil || obj.action == nil || ![obj.target respondsToSelector:obj.action]) {
            // 移除非法action对
            [self.pairs removeObject:obj];
        } else if (obj.target == target) {
            // 移除目标action对
            [self.pairs removeObject:obj];
        }
    }];
}

#pragma mark - Private

- (void)connectWithUsername:(NSString *)username password:(NSString *)password clientId:(NSString *)clientId success:(void (^)(void))success failure:(void (^)(void))failure {
    // 打印连接参数
    NSLog(@"MQTTHelper: 连接参数");
    NSLog(@"MQTTHelper:   username: %@", username);
    NSLog(@"MQTTHelper:   password: %@", password);
    NSLog(@"MQTTHelper:   clientId: %@", clientId);
    
    self.session.userName = username;
    self.session.password = password;
    self.session.clientId = clientId;
//    NSLog(@"MQTT: connect %@ %@ %@", username, password, clientId);
    @weakify(self);
    [self.session connectWithConnectHandler:^(NSError *error) {
        @strongify(self);
        if (error) {
            BJLog(@"MQTT: failure %@", error);
            !failure ?: failure();
        } else {
            BJLog(@"MQTT: connect success");
            [self subcribeTopicsWithCompletion:^(BOOL isSuccess) {
                if (isSuccess) {
                    !success ?: success();
                } else {
                    !failure ?: failure();
                }
            }];
            
            [self cancelTimer];
            [self runTimer];
            
        }
    }];
}

- (void)subcribeTopicsWithCompletion:(void (^)(BOOL success))completion {
    NSArray<NSString *> *topics = [[CYUnifiedMQTTHelper shared] topicDictionary].allValues;
    NSMutableDictionary *dictionary = [NSMutableDictionary dictionary];
    
    // 打印订阅主题参数
    NSLog(@"MQTTHelper: 订阅主题参数");
    NSLog(@"MQTTHelper:   prefix: %@", self.prefix);
    NSLog(@"MQTTHelper:   环境前缀: %@", kEnvironmentPrefix);
    NSLog(@"MQTTHelper:   订阅主题数量: %lu", (unsigned long)topics.count);
    
    [topics enumerateObjectsUsingBlock:^(NSString * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
        NSString *topic = [NSString stringWithFormat:@"%@", obj];
        dictionary[topic] = @(MQTTQosLevelAtLeastOnce);
        NSLog(@"MQTTHelper:   主题[%lu]: %@", (unsigned long)idx, topic);
    }];
    
    BJLog(@"MQTT: subscribeToTopics %@",topics);
    [self.session subscribeToTopics:dictionary subscribeHandler:^(NSError *error, NSArray<NSNumber *> *gQoss) {
        if (error) {
            BJLog(@"MQTT: subscribeToTopics error %@", error);
            !completion ?: completion(NO);
        } else {
            BJLog(@"MQTT: subscribeToTopics success");
            !completion ?: completion(YES);
        }
    }];
}

- (NSDictionary<NSNumber*, NSString *> *)topicDictionary {
    return @{
//        @(CYUnifiedTopicCarStatus): @"car/status/v1",
//        @(CYUnifiedTopicCarPortPosition): @"car/carport/position/v1",
//        @(CYUnifiedTopicParkingInStatus): @"car/parking_in/status/v1",
//        @(CYUnifiedTopicParkingOutStatus): @"car/parking_out/status/v1",
//        @(CYUnifiedTopicRemoteControlStatus): @"car/remote_play/status/v1",
//        @(CYUnifiedTopicSummonLocation): @"car/summon/location/v1",
//        @(CYUnifiedTopicSummonStatus): @"car/summon/status/v1",
//        @(CYUnifiedTopicUnblockStatus): @"car/unblock/status/v1",
//        @(CYUnifiedTopicCarControlResult):@"sgmw/control",
        @(CYUnifiedTopicCarRemoteAsyncResult):[NSString stringWithFormat:@"%@/%@/sgmw/vehicle/control", self.prefix, kEnvironmentPrefix],
        @(CYUnifiedTopicCarControlAllStatus):[NSString stringWithFormat:@"%@/%@/sgmw/vehicle/app/status", self.prefix, kEnvironmentPrefix],
        @(CYUnifiedTopicCarCheckAuthorizeBusiness):[NSString stringWithFormat:@"%@/%@/sgmw/vehicle/car_check_authorize/business", self.prefix, kEnvironmentPrefix],
        @(CYUnifiedTopicCarParkingNotifyBusiness):[NSString stringWithFormat:@"%@/%@/sgmw/vehicle/car_parking_notify/business", self.prefix, kEnvironmentPrefix]
    };
}

#pragma mark - MQTTSession Delegate

- (void)newMessage:(MQTTSession *)session data:(NSData *)data onTopic:(NSString *)topic qos:(MQTTQosLevel)qos retained:(BOOL)retained mid:(unsigned int)mid {
    // 打印接收到的消息参数
    NSLog(@"MQTTHelper: 接收消息参数");
    NSLog(@"MQTTHelper:   主题: %@", topic);
    NSLog(@"MQTTHelper:   数据长度: %lu bytes", (unsigned long)data.length);
    NSLog(@"MQTTHelper:   QoS级别: %d", qos);
    NSLog(@"MQTTHelper:   是否保留: %@", retained ? @"是" : @"否");
    NSLog(@"MQTTHelper:   消息ID: %u", mid);
    
    __block CYUnifiedTopic topicEnum = 0;
    [[[CYUnifiedMQTTHelper shared] topicDictionary] enumerateKeysAndObjectsUsingBlock:^(NSNumber * _Nonnull key, NSString * _Nonnull obj, BOOL * _Nonnull stop) {
        if ([topic hasSuffix:obj]) {
            topicEnum = key.integerValue;
            *stop = YES;
        }
    }];
    
    NSLog(@"MQTTHelper:   匹配的主题枚举: %lu", (unsigned long)topicEnum);
    
    id model;
    NSLog(@"MQTT: onTopic %@", topic);
    switch (topicEnum) {
        case CYUnifiedTopicCarControlAllStatus:
        {
            model = [SgmwAppCarStatus parseFromData:data error:nil];
        }
            break;
        case CYUnifiedTopicCarPortPosition:
        {
            model = [CarportPosition parseFromData:data error:nil];
        }
            break;
        case CYUnifiedTopicParkingInStatus:
        {
            model = [ParkingInStatus parseFromData:data error:nil];
        }
            break;
        case CYUnifiedTopicParkingOutStatus:
        {
            model = [ParkingOutStatus parseFromData:data error:nil];
        }
            break;
        case CYUnifiedTopicRemoteControlStatus:
        {
            model = [RemoteControlStatus parseFromData:data error:nil];
        }
            break;
        case CYUnifiedTopicSummonLocation:
        {
            model = [SummonLocation parseFromData:data error:nil];
        }
            break;
        case CYUnifiedTopicSummonStatus:
        {
            model = [SummonStatus parseFromData:data error:nil];
        }
            break;
        case CYUnifiedTopicUnblockStatus:
        {
            model = [UnblockStatus parseFromData:data error:nil];
        }
            break;
        case CYUnifiedTopicCarRemoteAsyncResult:
        {
            model = [SgmwAppControlResult parseFromData:data error:nil];
        }
            break;
        case CYUnifiedTopicCarCheckAuthorizeBusiness:
        {
            model = [SgmwBusinessInfo parseFromData:data error:nil];
        }
            break;
        case CYUnifiedTopicCarParkingNotifyBusiness:
        {
            model = [SgmwBusinessInfo parseFromData:data error:nil];
        }
            break;
        default:
            break;
    }
    if (model) {
        NSArray<CYUnifiedMQTTHelperPair*> *pairs = [self.pairs copy];
        [pairs enumerateObjectsUsingBlock:^(CYUnifiedMQTTHelperPair * _Nonnull obj, NSUInteger idx, BOOL * _Nonnull stop) {
            if (obj.target && obj.action && obj.topic == topicEnum && [obj.target respondsToSelector:obj.action]) {
                [obj.target performSelector:obj.action withObject:model afterDelay:0];
            }
        }];
    }
}

#pragma mark - Timer
- (void)runTimer {
    self.statusTimer = [NSTimer scheduledTimerWithTimeInterval:5.0 target:self selector:@selector(timerRunBlock) userInfo:nil repeats:YES];
    [self.statusTimer fire];
}

- (void)cancelTimer {
    if (self.statusTimer) {
        [self.statusTimer invalidate];
        self.statusTimer = nil;
    }
}

- (void)timerRunBlock {
    BJLog(@"MQTT: 连接状态：%@", self.session.status == MQTTSessionStatusConnected?@"已连接":@"未连接");
    NSString *network = [HttpUtil getNetconnType];
    if ([CYUnifiedMQTTHelper shared].supportMqtt &&(self.session.status == MQTTSessionStatusDisconnecting || self.session.status == MQTTSessionStatusError || self.session.status == MQTTSessionStatusClosed) && ![network isEqualToString:@"no_network"] ) {
       //重连即可
        self.isCancelReconnect = YES;
        [self reConnectMTQQ];
    }
}

#pragma mark - 检查MQTT连接情况并尝试连接
- (void)checkAndConnectMQTT {
    BJLog(@"MQTT: 状态:%ld -- ", [CYUnifiedMQTTHelper shared].session.status);
    //如果是使用新界面的类型
    if ([CYUserModel isLogin] && self.supportMqtt && [CYUnifiedMQTTHelper shared].session.status != MQTTSessionStatusConnected && [CYUnifiedMQTTHelper shared].session.status != MQTTSessionStatusConnecting) {
        // 获取MQTT登录账号密码等信息
        [CYUnifiedMQTTHelper getTokenWithVin:self.prefix success:^(NSString * _Nonnull username, NSString * _Nonnull password, NSString * _Nonnull clientId, NSString * _Nonnull vin) {
            BJLog(@"MQTT: 获取账号密码成功");
            // 登录MQTT
            [[CYUnifiedMQTTHelper shared] loginWithUsername:username password:password clientId:clientId vin:vin success:^{
                BJLog(@"MQTT: 登录成功");
            } failure:^{
                BJLog(@"MQTT: 登录失败");
            }];
        } failure:^{
            BJLog(@"MQTT: 账号密码失败");
        }];
    }
}

#pragma mark - other
- (void)dealloc {
    [self.statusTimer invalidate];
    self.statusTimer = nil;
}


@end


@implementation CYUnifiedMQTTHelperPair



@end
