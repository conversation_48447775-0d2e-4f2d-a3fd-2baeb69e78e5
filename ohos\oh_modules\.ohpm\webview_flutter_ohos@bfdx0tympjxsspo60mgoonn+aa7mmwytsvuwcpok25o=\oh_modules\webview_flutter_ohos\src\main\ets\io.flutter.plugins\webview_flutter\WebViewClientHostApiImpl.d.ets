import { WebViewClient<PERSON>ost<PERSON><PERSON>, HttpAuthHandler } from './GeneratedOhosWebView';
import { InstanceManager } from './InstanceManager';
import { WebViewClientFlutterApiImpl } from './WebViewClientFlutterApiImpl';
import { WebViewPlatformView } from './WebViewHostApiImpl';
export interface WebViewClient {
    onPageStarted(view: WebViewPlatformView, url: string): void;
    onPageFinished(view: WebViewPlatformView, url: string): void;
    onReceivedError(view: WebViewPlatformView, request: WebResourceRequest, error: WebResourceError): void;
    shouldOverrideUrlLoading(view: WebViewPlatformView, request: WebResourceRequest): boolean;
    doUpdateVisitedHistory(view: WebViewPlatformView, url: string, isReload: boolean): void;
    onReceivedHttpAuthRequest(view: WebViewPlatformView, handler: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, host: string, realm: string): void;
}
export declare class WebViewClientImpl implements WebViewClient {
    private flutterApi;
    private returnValueForShouldOverrideUrlLoading;
    private emptyReply;
    constructor(flutterApi: WebViewClientFlutterApiImpl);
    onPageStarted(view: WebViewPlatformView, url: string): void;
    onPageFinished(view: WebViewPlatformView, url: string): void;
    onReceivedError(view: WebViewPlatformView, request: WebResourceRequest, error: WebResourceError): void;
    shouldOverrideUrlLoading(view: WebViewPlatformView, request: WebResourceRequest): boolean;
    doUpdateVisitedHistory(view: WebViewPlatformView, url: string, isReload: boolean): void;
    onReceivedHttpAuthRequest(view: WebViewPlatformView, handler: HttpAuthHandler, host: string, realm: string): void;
    setReturnValueForShouldOverrideUrlLoading(value: boolean): void;
}
export declare class WebViewClientCreator {
    createWebViewClient(flutterApi: WebViewClientFlutterApiImpl): WebViewClient;
}
export declare class WebViewClientHostApiImpl extends WebViewClientHostApi {
    private instanceManager;
    private webViewClientCreator;
    private flutterApi;
    constructor(instanceManager: InstanceManager, webViewClientCreator: WebViewClientCreator, flutterApi: WebViewClientFlutterApiImpl);
    create(instanceId: number): void;
    setSynchronousReturnValueForShouldOverrideUrlLoading(instanceId: number, value: boolean): void;
}
