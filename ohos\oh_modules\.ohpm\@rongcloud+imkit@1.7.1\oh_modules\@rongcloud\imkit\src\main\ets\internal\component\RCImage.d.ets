// @keepTs
// @ts-nocheck
import { Message } from '@rongcloud/imlib';
import { GroupInfoModel } from '../../user/model/GroupInfoModel';
import { GroupMemberInfoModel } from '../../user/model/GroupMemberInfoModel';
import { UserInfoModel } from '../../user/model/UserInfoModel';
import { DrawableDescriptor } from '@ohos.arkui.drawableDescriptor';
/**
 * 系统 Image 组件的 src 类型
 */
type RcSystemImageSrcType = PixelMap | ResourceStr | DrawableDescriptor;
/**
 * 占位图类型
 */
type RcSystemImageAltType = string | Resource | PixelMap;
/**
 * 用户信息、群组信息、群成员信息
 */
type RcInfoModel = UserInfoModel | GroupInfoModel | GroupMemberInfoModel;
/**
 * RCImage 组件的 src 类型
 */
type RcImageSrcType = RcSystemImageSrcType | Message | RcInfoModel;
/**
 * 系统 Image 组件回调onComplete的数据结构
 */
interface Event {
    width: number;
    height: number;
    componentWidth: number;
    componentHeight: number;
    loadingStatus: number;
    contentWidth: number;
    contentHeight: number;
    contentOffsetX: number;
    contentOffsetY: number;
}
/**
 * 自定义 Image 组件
 * # 特点
 *```
 * 1. 除了系统 Image 支持的类型之外，还支持传入 Message | UserInfoModel | GroupInfoModel | GroupMemberInfoModel 对象。Message 必须是图片消息或 Gif 消息
 * 2. 下载失败后，尝试拿 msgId 或 http 地址手动下载
 * 3. 使用方式和 Image 基本一致
 *```
 * # 示例代码
 *```
 * Image(this.uri).alt(this.alt).objectFit(ImageFit.Contain)
 * RCImage({ src: this.uri, alt : this.alt, objectFit : ImageFit.Contain, radius: 3})
 * src 等效于 Image(src)
 * alt 等效于 Image().alt(this.alt)
 * objectFit 等效于 Image().objectFit(ImageFit.Contain)
 * radius 等效于 Image().borderRadius(3)
 * bgColor 等效于 Image().backgroundColor(this.bgColor)
 * maxWidth与maxHeight 等效于 Image().constraintSize({ maxWidth: this.systemMaxWidth, maxHeight: this.systemMaxHeight })
 * syncLoad 等效于 Image().syncLoad(this.syncLoad)
 * autoResize 等效于 Image().autoResize(this.autoResize)
 * completeCallback 等效于 Image().onComplete(this.completeCallback)
 *```
 */
@Component
export declare struct RCImage {
    private cache?;
    @Prop
    src?: RcImageSrcType;
    alt?: RcSystemImageAltType;
    @State
    private altImpl?;
    objectFit?: ImageFit;
    radius: Length | BorderRadiuses | LocalizedBorderRadiuses;
    bgColor?: ResourceColor;
    maxWidth?: Length;
    maxHeight?: Length;
    syncLoad?: boolean;
    autoResize?: boolean;
    completeCallback?: (event?: Event) => void;
    aboutToAppear(): void;
    build(): void;
    private downloadAfterError;
    private updateAfterDownload;
    /**
     * 初始化图片alt，采用懒加载形式，避免图片加载成功前闪烁alt的问题
     */
    private initAlt;
    /**
     * 更新图片alt。仅在设置了alt且图片加载失败时设置alt。
     * 采用懒加载形式，避免图片加载成功前闪烁alt的问题
     */
    private updateAlt;
    private getDownloadCache;
    private putLoadCompleteCache;
    private putDownloadCache;
    /**
     * 返回下载 key
     * @param src 数据源
     * @returns 返回下载链接或者消息 Id
     */
    private getDownloadKey;
    private getSystemSrc;
    private getSystemSrcString;
    private getUrlFromMessage;
    private getStringFromResourceStr;
}
export {};
