import type OverlayListener from "../m/i2"; export default abstract class BmObject { protected readonly nativeInstance: number; protected readonly mObjType: number; private mName;   isDestroyed: boolean; protected mListener: OverlayListener; constructor(a46: number, b46: number);         get listener(): OverlayListener;         setListener(listener: OverlayListener): void;         setName(name: string): void;         getName(): string;         getObjType(): number;         getNativeInstance(): number;         destroy(): void; private printDebugFinalize; } 