{"device_info_plus|device_info_plus|1.0.0|build/default/generated/profile/default/ModuleInfo.js": {"version": 3, "file": "ModuleInfo.ts", "sources": ["ohos/build/default/generated/profile/default/ModuleInfo.ts"], "names": [], "mappings": "AACA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EAAE,CAAA,CAAA;AACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EAAE,CAAA,CAAA;", "entry-package-info": "device_info_plus|1.0.0"}, "device_info_plus|device_info_plus|1.0.0|index.ts": {"version": 3, "file": "index.ets", "sourceRoot": "", "sources": ["ohos/index.ets"], "names": [], "mappings": "OAeO,wBAAwB;AAC/B,eAAe,wBAAwB,CAAC", "entry-package-info": "device_info_plus|1.0.0"}, "device_info_plus|device_info_plus|1.0.0|src/main/ets/dev/fluttercommunity/plus/device_info/DeviceInfoPlusOhosPlugin.ts": {"version": 3, "file": "DeviceInfoPlusOhosPlugin.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/dev/fluttercommunity/plus/device_info/DeviceInfoPlusOhosPlugin.ets"], "names": [], "mappings": "OAeO,GAAG;YACH,YAAY;cAEjB,oBAAoB;cAGpB,aAAa,EACb,oBAAoB;OAEf,aAAa;cACX,iBAAiB,EAAE,YAAY;YACjC,UAAU;OACV,UAAU;AAEjB,MAAM,GAAG,GAAG,0BAA0B,CAAC;AACvC,MAAM,YAAY,GAAG,uCAAuC,CAAC;AAE7D,gCAAgC;AAChC,MAAM,CAAC,OAAO,OAAO,wBAAyB,YAAW,aAAa,EAAE,YAAY,EAAE,iBAAiB;IACrG,OAAO,CAAC,oBAAoB,EAAE,oBAAoB,GAAG,IAAI,GAAG,IAAI,CAAC;IACjE,OAAO,CAAC,OAAO,EAAE,aAAa,GAAG,IAAI,GAAG,IAAI,CAAC;IAE7C,kBAAkB,IAAI,MAAM;QAC1B,OAAO,GAAG,CAAC;IACb,CAAC;IAED,mBAAmB,CAAC,OAAO,EAAE,oBAAoB;QAC/C,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC;QACpC,IAAI,CAAC,OAAO,EAAE,oBAAoB,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC;IAED,qBAAqB;IACrB,CAAC;IAED,kBAAkB,CAAC,OAAO,EAAE,oBAAoB,GAAG,IAAI;QACrD,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,+BAA+B,CAAC,CAAA;QAC3C,IAAI,CAAC,OAAO,GAAG,IAAI,aAAa,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,YAAY,CAAC,CAAC;QAC7E,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED,oBAAoB,CAAC,OAAO,EAAE,oBAAoB,GAAG,IAAI;IACzD,CAAC;IAED,YAAY,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,IAAI;QACxD,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,sBAAsB,CAAC,CAAC;QACnC,QAAQ,IAAI,CAAC,MAAM,EAAE;YACnB,KAAK,eAAe;gBAClB,IAAI,IAAI,EAAE,GAAG,CAAC,MAAM,MAAW,GAAG,IAAI,GAAG,EAAE,CAAC;gBAC5C,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC;gBAC9C,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,UAAU,CAAC,WAAW,CAAC,CAAC;gBAChD,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC,KAAK,CAAC,CAAC;gBACpC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC;gBAC9C,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,UAAU,CAAC,aAAa,CAAC,CAAC;gBACpD,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,UAAU,CAAC,YAAY,CAAC,CAAC;gBAClD,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,UAAU,CAAC,aAAa,CAAC,CAAC;gBACpD,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,UAAU,CAAC,aAAa,CAAC,CAAC;gBACpD,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,UAAU,CAAC,eAAe,CAAC,CAAC;gBACxD,IAAI,CAAC,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC,MAAM,CAAC,CAAC;gBACtC,IAAI,CAAC,GAAG,CAAC,mBAAmB,EAAE,UAAU,CAAC,iBAAiB,CAAC,CAAC;gBAC5D,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC;gBACxC,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,UAAU,CAAC,gBAAgB,CAAC,CAAC;gBAC1D,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,UAAU,CAAC,cAAc,CAAC,CAAC;gBACtD,IAAI,CAAC,GAAG,CAAC,oBAAoB,EAAE,UAAU,CAAC,kBAAkB,CAAC,CAAC;gBAC9D,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,UAAU,CAAC,aAAa,CAAC,CAAC;gBACpD,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,UAAU,CAAC,CAAC;gBAC9C,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,UAAU,CAAC,YAAY,CAAC,CAAC;gBAClD,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,UAAU,CAAC,aAAa,CAAC,CAAC;gBACpD,IAAI,CAAC,GAAG,CAAC,gBAAgB,EAAE,UAAU,CAAC,cAAc,CAAC,CAAC;gBACtD,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,UAAU,CAAC,YAAY,CAAC,CAAC;gBAClD,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,UAAU,CAAC,aAAa,CAAC,CAAC;gBACpD,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,UAAU,CAAC,eAAe,CAAC,CAAC;gBACxD,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC;gBAC5C,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC;gBAC5C,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC;gBAC5C,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC;gBAC5C,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,UAAU,CAAC,SAAS,CAAC,CAAC;gBAC5C,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,UAAU,CAAC,aAAa,CAAC,CAAC;gBACpD,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC,IAAI,CAAC,CAAC;gBAClC,IAAI,CAAC,GAAG,CAAC,oBAAoB,EAAE,UAAU,CAAC,kBAAkB,CAAC,CAAC;gBAC9D,IAAI,CAAC,GAAG,CAAC,uBAAuB,EAAE,UAAU,CAAC,qBAAqB,CAAC,CAAC;gBACpE,IAAI,CAAC,GAAG,CAAC,0BAA0B,EAAE,UAAU,CAAC,wBAAwB,CAAC,CAAC;gBAC1E,IAAI,CAAC,GAAG,CAAC,2BAA2B,EAAE,UAAU,CAAC,yBAAyB,CAAC,CAAC;gBAC5E,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBACrB,MAAM;YACR;gBACE,MAAM,CAAC,cAAc,EAAE,CAAC;gBACxB,MAAM;SACT;IACH,CAAC;CACF", "entry-package-info": "device_info_plus|1.0.0"}}