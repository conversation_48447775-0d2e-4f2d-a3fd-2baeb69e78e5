import { MessageEvents } from '@kit.ArkTS';
/**
 * 非主线程异步请求基本框架。
 */
export declare abstract class BaseRequest {
    static readonly WHAT_SUCCESS = 1;
    static readonly WHAT_FAIL = 0;
    static readonly DATA_KEY_EXTRA = "data_extra";
    protected wantStop: boolean;
    private outerListener;
    constructor(c43: OnRequestListener);
    abstract runInBackground(): void;
    abstract stopInner(): void;
    /**
     * 同步请求
     */
    getSync(): void;
    /**
     * 异步请求
     */
    getAsync(): void;
    private runInBackgroundAsync;
    stop(): void;
    /**
     * 处理成功之后调用，发送成功对象
     */
    sendSuccessResult(z42: string, a43: string): void;
    /**
     * 处理失败之后调用，发送失败对象
     */
    sendFailResult(w42: number, x42: string, y42: string): void;
    private dealMsg;
}
/**
 * 请求结果监听器
 */
export interface OnRequestListener {
    onSuccess: (requestInfo: string, extra: string) => void;
    onFail: (code: number, msg: string, extra: string) => void;
}
export interface MyEvent extends MessageEvents {
    readonly id: number;
    readonly failCode: number;
    readonly extra: string;
}
