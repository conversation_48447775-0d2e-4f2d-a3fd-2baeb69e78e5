import image from '@ohos.multimedia.image';
import fs from '@ohos.file.fs';
export interface ImageSaverCallback {
    onComplete(absolutePath: string): void;
    onError(errorCode: string, errorMessage: string): void;
}
export declare class ImageSaver {
    private readonly image;
    private readonly file;
    private readonly callback;
    constructor(image: image.Image, file: fs.File | null, callback: ImageSaverCallback);
    run(): void;
}
