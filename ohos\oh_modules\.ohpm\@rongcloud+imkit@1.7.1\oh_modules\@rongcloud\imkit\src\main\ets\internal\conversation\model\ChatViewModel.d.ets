// @keepTs
// @ts-nocheck
import { ConversationIdentifier } from '@rongcloud/imlib';
import RCTitleBar from '../../../base/component/RCTitleBar';
import { UserInfoModel } from '../../../user/model/UserInfoModel';
export declare class ChatViewModel {
    chatTitle: string;
    userInfo: UserInfoModel | undefined;
    private titleModel;
    private conId;
    private typingStatusListener;
    onInit(l215: ConversationIdentifier, m215?: RCTitleBar.Model): void;
    addTypingStatusListener(): void;
    getUnReadMessageCount(): void;
    onPageShow(): Promise<object>;
    /**
     * 会话置顶开关状态改变
     * @param isOn
     */
    onConversationSessionTopChange(u214: boolean): Promise<import("@rongcloud/imlib/src/main/ets/engine/IResult").IAsyncResult<void>>;
    /**
     * 消息免打扰开关状态改变
     * @param isOn
     */
    onMessageWithoutInterruptionChange(s214: boolean): Promise<import("@rongcloud/imlib/src/main/ets/engine/IResult").IAsyncResult<void>>;
    /**
     * 销毁
     */
    onDestroy(): void;
}
