import Ability from '@ohos.app.ability.Ability';
import { <PERSON><PERSON><PERSON><PERSON>enger } from '../../DartMessenger';
import { DeviceOrientation } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/systemchannels/PlatformChannel';
export declare class DeviceOrientationManager {
    private readonly ability;
    private readonly messenger;
    private readonly isFrontFacing;
    private readonly sensorOrientation;
    private lastOrientation;
    private displayClass;
    static create(ability: Ability, messenger: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, isFrontFacing: boolean, sensorOrientation: number): DeviceOrientationManager;
    constructor(ability: Ability, messenger: <PERSON>t<PERSON><PERSON>eng<PERSON>, isFrontFacing: boolean, sensorOrientation: number);
    start(): void;
    stop(): void;
    getPhotoOrientation(orientation?: DeviceOrientation): number | DeviceOrientation;
    getVideoOrientation(orientation?: DeviceOrientation): number;
    getLastUIOrientation(): DeviceOrientation;
    handleUIOrientationChange(): void;
    getUIOrientation(): DeviceOrientation;
}
