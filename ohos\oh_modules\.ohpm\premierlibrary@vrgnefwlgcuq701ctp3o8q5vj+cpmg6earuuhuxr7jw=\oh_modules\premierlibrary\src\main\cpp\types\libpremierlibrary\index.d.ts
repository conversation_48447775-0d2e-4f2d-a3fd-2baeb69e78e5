import constant from "@ohos.bluetooth.constant";
export const nConstruct: (nativePlayerBase: object) => void;
export const nStart: (nativePlayerBase: object) => void;
export const nPause: (nativePlayerBase: object) => void;
export const nStop: (nativePlayerBase: object) => void;
export const nPrepare: (nativePlayerBase: object) => void;
export const nSetAutoPlay: (nativePlayerBase: object, isAutoPlay: boolean) => void;
export const nSetUrlDataSource: (nativePlayerBase: object, url: object) => void;
export const nSetSurfaceId: (nativePlayerBase: object, url: string) => void;
export const nSetSpeed: (nativePlayerBase: object, speed: number) => void;
export const nSetVolume: (nativePlayerBase: object, volume: number) => void;
export const nGetVolume: (nativePlayerBase: object) => number;
export const nSetStartTime: (nativePlayerBase: object, position: number, mode: number) => void;
export const nSeekTo: (nativePlayerBase: object, position: number, mode: number) => void;
export const nGetDuration: (nativePlayerBase: object) => number;
export const nGetPlayedDuration: (nativePlayerBase: object) => number;
export const nSetGlobalTime: (nativePlayerBase: object, key: string) => void;
export const nGetCurrentPosition: (nativePlayerBase: object) => number;
export const nGetBufferedPosition: (nativePlayerBase: object) => number;
export const nGetPlayerStatus: (nativePlayerBase: object) => number;
export const nSetTraceId: (nativePlayerBase: object, traceId: string) => number;
export const nEnableHardwareDecoder: (nativePlayerBase: object, enabled: boolean) => void;
export const nRelease: (nativePlayerBase: object) => void;
export const nSetMute: (nativePlayerBase: object, mute: boolean) => void;
export const nIsMuted: (nativePlayerBase: object) => number; // 0 or 1, as your TODO comment suggests
export const nSetScaleMode: (nativePlayerBase: object, mode: number) => void;
export const nGetScaleMode: (nativePlayerBase: object) => number;
export const nSetLoop: (nativePlayerBase: object, on: boolean) => void;
export const nIsLoop: (nativePlayerBase: object) => number; // 0 or 1
export const nGetVideoWidth: (nativePlayerBase: object) => number;
export const nGetVideoHeight: (nativePlayerBase: object) => number;
export const nGetVideoRotation: (nativePlayerBase: object) => number;
export const nReload: (nativePlayerBase: object) => void;
export const nSetRotateMode: (nativePlayerBase: object, mode: number) => void;
export const nGetRotateMode: (nativePlayerBase: object) => number;
export const nSetMirrorMode: (nativePlayerBase: object, mode: number) => void;
export const nGetMirrorMode: (nativePlayerBase: object) => number;
export const nSetVideoBackgroundColor: (nativePlayerBase: object, color: number) => void;
export const nGetSpeed: (nativePlayerBase: object) => number;
export const nIsAutoPlay: (nativePlayerBase: object) => number; // 0 or 1
export const nSetConfig: (nativePlayerBase: object, config: object) => void;
export const nSetOption: (nativePlayerBase: object, key: string, value: string) => void;
export const nGetOption: (nativePlayerBase: object, key: string) => string;
export const nSelectTrack: (nativePlayerBase: object, index: number) => void;
export const nSwitchStream: (nativePlayerBase: object, url: string) => void;
export const nSetAlphaRenderMode: (nativePlayerBase: object, mode: number) => void;
export const nGetAlphaRenderMode: (nativePlayerBase: object) => number;
export const nGetCurrentStreamInfo: (nativePlayerBase: object, streamType: number) => object;
export const nAddExtSubtitle: (nativePlayerBase: object, url: string) => void;
export const nSelectExtSubtitle: (nativePlayerBase: object, index: number, isSelected: boolean) => object;
export const nSetStreamDelay: (nativePlayerBase: object, index: number, time: number) => void;
export const nSetMaxAccurateSeekDelta: (nativePlayerBase: object, delta: number) => void;
export const nSetCacheConfig: (nativePlayerBase: object, config: object) => void;
export const nGetConfig: (nativePlayerBase: object) => object;
export const nSetIPResolveType: (nativePlayerBase: object, type: number) => void;
export const nSetFastStart: (nativePlayerBase: object, enable: boolean) => void;
export const nSnapShot: (nativePlayerBase: object) => void;
export const nClearScreen: (nativePlayerBase: object) => void;
export const nGetSdkVersion: () => string;
export const nGetCacheFilePathByUrl: (nativePlayerBase: object, url: string) => string;
export const nGetCacheFilePathByVid: (nativePlayerBase: object, vid: string, format: string, definition: string, previewTime: number) => string;
export const nGetPropertyString: (nativePlayerBase: object, propertyKey: number) => string;
export const nSetDefaultBandWidth: (nativePlayerBase: object, bandwidth: number) => void;
export const nSendCustomEvent: (nativePlayerBase: object, event: string) => void;
export const nSetVideoTag: (nativePlayerBase: object, tags: number[]) => void;
export const nSetUserData: (nativePlayerBase: object, userData: string) => void;
export const nGetUserData: (nativePlayerBase: object) => string;
/*-------------------ListPlayer-------------------*/
export const nListConstruct: (listPlayerBase: object, playerAddr: number, preRenderPlayerAddr: number, isSaas: boolean) => void;
export const nListRemoveSource: (listPlayerBase: object, uid: string) => void;
export const nListClear: (listPlayerBase: object) => void;
export const nListGetCurrentUid: (listPlayerBase: object) => string;
export const nListStop: (listPlayerBase: object) => void;
export const nListSetPreloadCount: (listPlayerBase: object, count: number) => void;
export const nListSetPreloadCountWithPrevAndNext: (listPlayerBase: object, prevCount: number, nextCount: number) => void;
export const nListSetPreloadScene: (listPlayerBase: object, sceneType: number) => void;
export const nListEnablePreloadStrategy: (listPlayerBase: object, strategyType: number, enable: boolean) => void;
export const nListSetPreloadStrategyParam: (listPlayerBase: object, strategyType: number, strategyParam: string) => void;
export const nListSetMaxPreloadMemorySizeMB: (listPlayerBase: object, size: number) => void;
export const nListGetMaxPreloadMemorySizeMB: (listPlayerBase: object) => number;
export const nListSetMultiBitratesMode: (listPlayerBase: object, mode: number) => void;
export const nListGetMultiBitratesMode: (listPlayerBase: object) => number;
export const nListRelease: (listPlayerBase: object) => void;
export const nListAddUrl: (listPlayerBase: object, url: string, uid: string) => void;
export const nListGetPreRenderPlayerIndex: (listPlayerBase: object) => number;
export const nListGetCurrentPlayerIndex: (listPlayerBase: object) => number;
export const nListMoveToNext: (listPlayerBase: object, preRendered: boolean) => number; // Returns 1 or 0
export const nListMoveToPrev: (listPlayerBase: object) => number; // Returns 1 or 0
export const nListMoveTo: (listPlayerBase: object, uid: string) => number; // Returns 1 or 0
export const nListAddVid: (listPlayerBase: object, videoId: string, uid: string) => void;
export const nListSetDefinition: (listPlayerBase: object, definition: string) => void;
export const nListMoveToNextWithSts: (listPlayerBase: object, StsInfo: object, preRendered: boolean) => boolean;
export const nListMoveToPrevWithSts: (listPlayerBase: object, StsInfo: object) => boolean;
export const nListMoveToWithSts: (listPlayerBase: object, uid: string, StsInfo: object) => boolean;
export const nListMoveToNextWithPlayAuth: (listPlayerBase: object, PlayAuthInfo: object, preRendered: boolean) => boolean;
export const nListMoveToPrevWithPlayAuth: (listPlayerBase: object, PlayAuthInfo: object) => boolean;
export const nListMoveToWithPlayAuth: (listPlayerBase: object, uid: string, PlayAuthInfo: object) => boolean;
/*-------------------GlobalSetting-------------------*/
export const nGlobalSettingSetIPResolveType: (type: number) => void;
export const nSetUseHttp2: (use: boolean) => void;
export const nSetDNSResolve: (host: string, ip: string) => void;
export const nSetAudioStreamType: (type: number) => void;
export const nSetOptionStr: (key: number, value: string) => void;
export const nSetOptionInt: (key: number, value: number) => void;
export const nEnableEnhancedHttpDns: (enable: boolean) => void;
export const nForceAudioRendingFormat: (force: boolean, format: string, channels: number, sampleRate: number) => void;
export const nEnableLocalCache: (enable: boolean, maxBufferMemoryKB: number, localCacheDir: string) => void;
export const nSetCacheFileClearConfig: (expireMin: number, maxCapacityMB: number, freeStorageMB: number) => void;
export const nSetCacheUrlHashCallback: (globalSetting: object, beset: boolean) => void;
// export const nSetNetworkCallback: (beset: boolean) => void;
export const nEnableHttpDns: (enable: boolean) => void;
export const nEnableNetworkBalance: (enable: boolean) => void;
export const nEnableBufferToLocalCache: (enable: boolean) => void;
export const nClearCaches: () => void;
export const nDisableCrashUpload: (disable: boolean) => void;
export const nAddPreResolveDomain: (domain: string) => void;
export const nAddEnhancedHttpDnsDomain: (domain: string) => void;
/*-------------------ACPM-------------------*/
export class JSBind {
    static bindFunction: (name: string, func: Function) => number;
    static bindFunMap: (map: Map<string, Function>) => void;
    static bindAllFunction: (size: Number, index: Array<string>, func: Array<Function>) => void;
    static initTaskRunner: (param: string) => void;
}
/*-------------------privateService-------------------*/
export const nPreInitService: () => void;
export const nInitService_bytes: (verifyFileInfo: string) => void;
export const nInitService: (verifyFilePath: string) => void;
/*-------------------HlsKeyGen-------------------*/
export const nHlsGenConstruct: (hlsKeyGenBase: object) => void;
/*-------------------Saas-------------------*/
export const nSetVidStsDataSource: (nativePlayerBase: object, source: object) => void;
export const nSetVidAuthDataSource: (nativePlayerBase: object, source: object) => void;
export const nSetVidMpsDataSource: (nativePlayerBase: object, source: object) => void;
export const nSetLiveStsDataSource: (nativePlayerBase: object, source: object) => void;
export const nUpdateVidAuth: (nativePlayerBase: object, source: object) => void;
export const nUpdateStsInfo: (nativePlayerBase: object, source: object) => void;
/*-------------------MediaLoader-------------------*/
export const nMediaLoaderConstruct: (mediaLoaderBase: object) => void;
export const nMediaLoaderLoad: (url: string, hashUrl: string, duration: number) => void;
export const nMediaLoaderLoadWithBandWidth: (url: string, hashUrl: string, duration: number, bandWidth: number) => void;
export const nMediaLoaderCancel: (url: string, hashurl: string) => void;
export const nMediaLoaderCancelAll: () => void;
export const nMediaLoaderPause: (pause: boolean, url: string, hashurl: string) => void;
/*-------------------VodMediaLoader-------------------*/
export const nVodMediaLoaderPrepareSts: (vidSts: object) => void;
export const nVodMediaLoaderPrepareAuth: (vidAuth: object) => void;
export const nVodMediaLoaderRemoveSource: (vid: string) => void;
export const nVodMediaLoaderLoad: (vid: string, index: number, hashurl: string, duration: number) => void;
export const nVodMediaLoaderCancel: (vid: string, index: number, hashurl: string) => void;
export const nVodMediaLoaderPause: (pause: boolean, vid: string, index: number, hashurl: string) => void;
export const nVodMediaLoaderGetVodUrl: (vid: string, index: number) => string;
export const nVodMediaLoaderConstruct: (VodMediaLoaderBase: object) => void;
/*-------------------Downloader-------------------*/
export const nDownloaderConstruct: (downloaderBase: object) => void;
export const nDownloaderRelease: (downloaderBase: object) => void;
export const nDownloaderPrepareByVidAuth: (downloaderBase: object, vidAuth: object) => void;
export const nDownloaderPrepareByVidSts: (downloaderBase: object, vidSts: object) => void;
export const nDownloaderSetConnectivityManager: (downloaderBase: object) => void;
export const nDownloaderSetSaveDir: (downloaderBase: object, saveDir: string) => void;
export const nDownloaderSelectItem: (downloaderBase: object, index: number) => void;
export const nDownloaderUpdateSourceByVidSts: (downloaderBase: object, vidSts: object) => void;
export const nDownloaderUpdateSourceByVidAuth: (downloaderBase: object, vidAuth: object) => void;
export const nDownloaderStart: (downloaderBase: object) => void;
export const nDownloaderStop: (downloaderBase: object) => void;
export const nDownloaderDeleteFile: (downloaderBase: object) => void;
export const nDownloaderSDeleteFile: (saveDir: string, vid: string, format: string, index: number) => number;
export const nDownloaderGetFilePath: (downloaderBase: object) => string;
export const nDownloaderSetDownloaderConfig: (downloaderBase: object, config: object) => void;
/*-------------------Logger-------------------*/
export const nLoggerConstruct: (loggerBase: object) => void;
export const nLoggerGetLevel: () => number;
export const nLoggerEnableConsole: (enable: boolean) => void;
export const nLoggerSetLevel: (level: number) => void;
export const nLoggerSetOption: (logOption: string, value: number) => void;
