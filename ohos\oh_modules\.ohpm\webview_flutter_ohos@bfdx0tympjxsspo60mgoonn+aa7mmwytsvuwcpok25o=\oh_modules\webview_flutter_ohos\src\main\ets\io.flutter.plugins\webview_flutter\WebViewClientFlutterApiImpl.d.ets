import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import { Reply, WebResourceErrorData, WebResourceRequestData, WebViewClientFlutterApi, HttpAuthHandler } from './GeneratedOhosWebView';
import { InstanceManager } from './InstanceManager';
import { WebViewClient } from './WebViewClientHostApiImpl';
import { WebViewPlatformView } from './WebViewHostApiImpl';
export declare class WebViewClientFlutterApiImpl extends WebViewClientFlutterApi {
    private instanceManager;
    private webViewFlutterApi;
    private emptyReply;
    static createWebResourceErrorData(error: WebResourceError): WebResourceErrorData;
    static toWebHeadersMap(headers: Array<Header>): Map<string, string>;
    static createWebResourceRequestData(request: WebResourceRequest): WebResourceRequestData;
    constructor(binaryMessenger: BinaryMessenger, instanceManager: InstanceManager);
    onPageStartedImpl(webViewClient: WebViewClient, webView: WebViewPlatformView, urlArg: string, callback: Reply<void>): void;
    onPageFinishedImpl(webViewClient: WebViewClient, webView: WebViewPlatformView, urlArg: string, callback: Reply<void>): void;
    onReceivedRequestErrorImpl(webViewClient: WebViewClient, webView: WebViewPlatformView, request: WebResourceRequest, error: WebResourceError, callback: Reply<void>): void;
    requestLoadingImpl(webViewClient: WebViewClient, webView: WebViewPlatformView, request: WebResourceRequest, callback: Reply<void>): void;
    doUpdateVisitedHistoryImpl(webViewClient: WebViewClient, webView: WebViewPlatformView, url: string, isReload: boolean, callback: Reply<void>): void;
    onReceivedHttpAuthRequestImpl(webViewClient: WebViewClient, webview: WebViewPlatformView, httpAuthHandler: HttpAuthHandler, host: string, realm: string, callback: Reply<void>): void;
    getIdentifierForClient(webViewClient: WebViewClient): number;
}
