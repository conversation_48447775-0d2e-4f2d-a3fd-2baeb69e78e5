import 'package:flutter/material.dart';

import '../utils/manager/log_manager.dart';

class PostPublishPage extends StatefulWidget {
  @override
  _PostPublishPageState createState() => _PostPublishPageState();
}

class _PostPublishPageState extends State<PostPublishPage> {
  // 模拟已选择的图片列表
  List<ImageProvider> _selectedImages = [
    AssetImage('assets/images/use_car_page/older_ev_page/ev_show_battery_status_charging.png'),
    AssetImage('assets/images/use_car_page/older_ev_page/ev_show_battery_status_heating.png'),
    AssetImage('assets/images/use_car_page/older_ev_page/ev_show_battery_status_hept.png'),
    AssetImage('assets/images/use_car_page/older_ev_page/ev_show_battery_status_keeping.png'),
  ];
  String _title = '';
  String _content = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.close),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        actions: [
          TopActionButtons(
            onSaveDraft: saveDraft,
            onPublish: publishPost,
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ImagePreviewGrid(
                images: _selectedImages,
                onAddImage: addImage,
                onImageRemove: (index) {
                  setState(() {
                    _selectedImages.removeAt(index);
                  });
                },
                onReorder: (oldIndex, newIndex) {
                  setState(() {
                    final image = _selectedImages.removeAt(oldIndex);
                    _selectedImages.insert(newIndex, image);
                  });
                },
              ),
              SizedBox(height: 16),
              TitleInputField(
                onChanged: (value) {
                  setState(() {
                    _title = value;
                  });
                },
              ),
              SizedBox(height: 16),
              ContentInputField(
                onChanged: (value) {
                  setState(() {
                    _content = value;
                  });
                },
              ),
              SizedBox(height: 16),
              // 其他功能组件如选择车型、位置、话题、提醒谁看等，可以在这里继续添加
            ],
          ),
        ),
      ),
    );
  }

  void saveDraft() {
    LogManager().debug("Draft saved with title: $_title and content: $_content");
  }

  void publishPost() {
    LogManager().debug("Post published with title: $_title and content: $_content");
  }

  void addImage() {
    setState(() {
      _selectedImages.add(AssetImage('assets/images/sample5.jpg'));
    });
  }
}

// 顶部操作按钮组件
class TopActionButtons extends StatelessWidget {
  final VoidCallback onSaveDraft;
  final VoidCallback onPublish;

  const TopActionButtons({
    Key? key,
    required this.onSaveDraft,
    required this.onPublish,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        TextButton(
          onPressed: onSaveDraft,
          child: Text("存草稿"),
          style: TextButton.styleFrom(
           //   primary: Colors.grey
          ),
        ),
        TextButton(
          onPressed: onPublish,
          child: Text("发布"),
          style: TextButton.styleFrom(
            //  primary: Colors.red
          ),
        ),
      ],
    );
  }
}

// 图片预览组件，支持拖动换位
class ImagePreviewGrid extends StatefulWidget {
  final List<ImageProvider> images;
  final VoidCallback onAddImage;
  final Function(int index) onImageRemove;
  final Function(int oldIndex, int newIndex) onReorder;

  const ImagePreviewGrid({
    Key? key,
    required this.images,
    required this.onAddImage,
    required this.onImageRemove,
    required this.onReorder,
  }) : super(key: key);

  @override
  _ImagePreviewGridState createState() => _ImagePreviewGridState();
}

class _ImagePreviewGridState extends State<ImagePreviewGrid> {
  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1.0,
      ),
      itemCount: widget.images.length + 1,
      itemBuilder: (context, index) {
        if (index < widget.images.length) {
          return buildDraggableGridItem(index);
        } else {
          return buildAddButton();
        }
      },
    );
  }

  // 构建可拖动的网格项
  Widget buildDraggableGridItem(int index) {
    return LongPressDraggable<int>(
      data: index,
      axis: Axis.vertical, // 拖动方向
      feedback: Opacity(
        opacity: 0.7,
        child: buildGridItem(index), // 拖动时的视觉效果
      ),
      childWhenDragging: Container(), // 拖动时原位置显示为空
      onDragStarted: () {
        // 拖动开始时的操作
      },
      onDragCompleted: () {
        // 拖动完成时的操作
      },
      onDragEnd: (details) {
        // 拖动结束时的操作
      },
      onDraggableCanceled: (velocity, offset) {
        // 拖动取消时的操作
      },
      child: DragTarget<int>(
        onWillAccept: (data) => data != index, // 接收除自身外的元素
        onAccept: (data) {
          widget.onReorder(data!, index);
        },
        builder: (context, candidateData, rejectedData) {
          return buildGridItem(index);
        },
      ),
    );
  }

  // 构建网格项（图像与删除按钮）
  Widget buildGridItem(int index) {
    return Stack(
      fit: StackFit.expand,
      children: [
        Image(
          image: widget.images[index],
          fit: BoxFit.cover,
        ),
        Positioned(
          top: 0,
          right: 0,
          child: GestureDetector(
            onTap: () => widget.onImageRemove(index),
            child: Container(
              color: Colors.black54,
              child: Icon(Icons.close, size: 16, color: Colors.white),
            ),
          ),
        ),
      ],
    );
  }

  // 构建添加图片按钮
  Widget buildAddButton() {
    return GestureDetector(
      onTap: widget.onAddImage,
      child: Container(
        color: Colors.grey[300],
        child: Center(
          child: Icon(Icons.add, size: 40, color: Colors.grey),
        ),
      ),
    );
  }
}

// 标题输入框组件
class TitleInputField extends StatelessWidget {
  final ValueChanged<String> onChanged;

  const TitleInputField({
    Key? key,
    required this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextField(
      maxLength: 15,
      decoration: InputDecoration(
        hintText: "标题字数在15字以内更美观哦~（选填）",
        counterText: "",
        border: OutlineInputBorder(),
      ),
      onChanged: onChanged,
    );
  }
}

// 帖子内容输入组件
class ContentInputField extends StatelessWidget {
  final ValueChanged<String> onChanged;

  const ContentInputField({
    Key? key,
    required this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextField(
      maxLines: null,
      decoration: InputDecoration(
        hintText: "创作内容需符合要求，方向围绕五菱&宝骏汽车相关内容",
        border: OutlineInputBorder(),
      ),
      onChanged: onChanged,
    );
  }
}