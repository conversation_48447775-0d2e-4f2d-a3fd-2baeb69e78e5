import image from '@ohos.multimedia.image'; import { resourceManager } from '@kit.LocalizationKit'; export default class NativeAPI { constructor(); static setSysValues(filesDir: string, cacheDir: string, densityDPI: number, resourceManager: resourceManager.ResourceManager, callback?: (result: string) => void): void; static NapiBmObjectFinalizer(v25: number): void; static NapiBmObjectSetLayerTag(t25: number, u25: string): void; static NapiBmDrawItemSetVisibility(s25: number, visibility: number): void; static NapiBmDrawItemSetShowLevel(r25: number, from: number, to: number): void; static NapiBmDrawItemSetOpacity(q25: number, opacity: number): void; static NapiBmDrawItemSetClickable(o25: number, p25: boolean): void; static NapiBmDrawItemSetHoleClickable(m25: number, n25: boolean): void; static NapiBmDrawItemSetAnimation(k25: number, l25: number): void; static NapiBmLayerCreate(): any; static NapiBmLayerGetLayerId(j25: number): any; static NapiBmLayerSetCollisionBaseMap(h25: number, i25: boolean): void; static NapiBmLayerCommitUpdate(g25: number): void; static NapiBmLayerSetVisibility(f25: number, visibility: number): void; static NapiBmLayerSetClickable(e25: number, clickable: boolean): void; static NapiBmLayerSetShowLevel(b25: number, c25: number, d25: number): void; static NapiBmLayerAddDrawItem(z24: number, a25: number): void; static NapiBmLayerAddDrawItemByZIndex(w24: number, x24: number, y24: number): void; static NapiBmLayerAddDrawItemBelow(t24: number, u24: number, v24: number): void; static NapiBmLayerAddDrawItemAbove(q24: number, r24: number, s24: number): void; static NapiBmLayerRemoveDrawItem(o24: number, p24: number): void; static NapiBmLayerClearDrawItems(n24: number): void; static NapiBmLayerGetDrawItemRect(m24: number, left: number, top: number, right: number, bottom: number): void; static NapiBmLayerHandleClick(j24: number, k24: number, l24: number, radius: number): any; static NapiBmCircleCreate(): any; static NapiBmCircleSetCenter(i24: number, x: number, y: number, z: number): void; static NapiBmCircleSetRadius(h24: number, radius: number): void; static NapiBmCircleSetPixelRadius(g24: number, radius: number): any; static NapiBmCircleSetLineStyle(e24: number, f24: number): void; static NapiBmCircleSetSurfaceStyle(c24: number, d24: number): void; static NapiBmCircleSetIsGradientCircle(a24: number, b24: boolean): void; static NapiBmCircleSetGradientRadiusWeight(y23: number, z23: number): void; static NapiBmCircleSetGradientColorWeight(w23: number, x23: number): void; static NapiBmCircleSetGradientColors(v23: number, type: number, colors: Array<number>): void; static NapiBmSurfaceStyleCreate(): any; static NapiBmSurfaceStyleSetColor(u23: number, color: number): void; static NapiBmSurfaceStyleSetBitmapResource(t23: number, bitmap: number): void; static NapiBmSurfaceStyleSetBmpResId(r23: number, s23: number): void; static NapiBmBaseMarker_SetXYZ(q23: number, x: number, y: number, z: number): void; static NapiBmBaseMarker_SetX(p23: number, x: number): void; static NapiBmBaseMarker_SetY(o23: number, y: number): void; static NapiBmBaseMarker_SetZ(n23: number, z: number): void; static NapiBmBaseMarker_SetPerspective(m23: number, perspective: number): void; static NapiBmBaseMarker_SetIsFix(k23: number, l23: number): void; static NapiBmBaseMarker_SetFixX(i23: number, j23: number): void; static NapiBmBaseMarker_SetFixY(g23: number, h23: number): void; static NapiBmBaseMarker_SetWidth(f23: number, width: number): void; static NapiBmBaseMarker_SetHeight(e23: number, height: number): void; static NapiBmBaseMarker_SetLocated(c23: number, d23: number): void; static NapiBmBaseMarker_SetOffsetX(b23: number, offsetX: number, scaleMode: number): void; static NapiBmBaseMarker_SetOffsetY(a23: number, offsetY: number, scaleMode: number): void; static NapiBmBaseMarker_SetScale(z22: number, scale: number): void; static NapiBmBaseMarker_SetScaleX(y22: number, scaleX: number): void; static NapiBmBaseMarker_SetScaleY(x22: number, scaleY: number): void; static NapiBmBaseMarker_SetRotate(w22: number, rotate: number): void; static NapiBmBaseMarker_SetCollisionBehavior(u22: number, v22: number): void; static NapiBmBaseMarker_SetCollisionPriority(t22: number, priority: number): void; static NapiBmBaseMarker_SetTrackBy(r22: number, s22: number): void; static NapiBmBaseMarker_SetRotateFeature(q22: number, feature: number): void; static NapiBmBaseMarker_SetFollowMapRotateAxis(o22: number, p22: number): void; static NapiBmBaseMarker_SetId(m22: number, n22: string): void; static NapiBmBaseMarker_SetBuildingId(k22: number, l22: string): void; static NapiBmBaseMarker_SetFloorId(i22: number, j22: string): void; static NapiBmBaseMarker_AddRichView(g22: number, h22: number): void; static NapiBmBaseMarker_RemoveRichView(e22: number, f22: number): void; static NapiBmBaseMarker_ClearRichViews(d22: number): void; static NapiBmBaseMarker_SetDrawFullscreenMaskFlag(c22: number, flag: boolean): void; static NapiBmIconMarker_Create(): any; static NapiBmIconMarker_SetBmpResId(b22: number, resId: number): void; static NapiBmIconMarker_SetDrawableResource(z21: number, a22: number): void; static NapiBmIconMarker_SetColor(x21: number, y21: number): void; static NapiBmIconMarker_SetAnimationType(w21: number, type: number): void; static NapiBmPrism_Create(): any; static NapiBmPrism_AddGeoElement(u21: number, v21: number): any; static NapiBmPrism_ClearGeoElements(t21: number): any; static NapiBmPrism_SetSurfaceTopStyle(r21: number, s21: number): any; static NapiBmPrism_SetSurfaceSideStyle(p21: number, q21: number): any; static NapiBmPrism_SetSurfaceFloorTopStyle(n21: number, o21: number): any; static NapiBmPrism_SetSurfaceFloorSideStyle(l21: number, m21: number): any; static NapiBmPrism_SetHeight(k21: number, height: number): any; static NapiBmPrism_SetLastFloorHeight(j21: number, height: number): any; static NapiBmPrism_SetFloorHeight(i21: number, height: number): any; static NapiBmPrism_SetBuildingID(g21: number, h21: string): any; static NapiBmPrism_SetIsAnimation(e21: number, f21: boolean): any; static NapiBmPrism_SetHasFloor(c21: number, d21: boolean): any; static NapiBmPrism_SetIsBuilding(a21: number, b21: boolean): any; static NapiBmPrism_SetAnimateType(y20: number, z20: number): any; static NapiBmPrism_SetFloorAnimateType(w20: number, x20: number): any; static NapiBmPrism_SetIsRoundedCorner(u20: number, v20: boolean): any; static NapiBmPrism_SetRoundedCornerRadius(s20: number, t20: number): any; static NapiBmBitmapResource_Create(): any; static NapiBmBitmapResource_SetBitmap(k20: number, l20: string, m20: image.PixelMap, n20: number, o20: number, p20: number, q20: number): void; static NapiBmBitmapResource_SetScaleX(j20: number, scale: number[], size: number): void; static NapiBmBitmapResource_SetScaleY(i20: number, scale: number[], size: number): void; static NapiBmBitmapResource_SetFillArea(h20: number, x1: number, x2: number, y1: number, y2: number): void; static NapiBmTextStyle_Create(): any; static NapiBmTextStyle_SetTextColor(f20: number, g20: number): any; static NapiBmTextStyle_SetTextSize(e20: number, textSize: number): any; static NapiBmTextStyle_SetBorderColor(c20: number, d20: number): any; static NapiBmTextStyle_SetBorderWidth(b20: number, width: number): any; static NapiBmTextStyle_SetFontOption(a20: number, option: number): any; static NapiBmTextStyle_SetTextBackColor(z19: number, option: number): any; static NapiBmBaseUI_SetBackground(x19: number, y19: number): any; static NapiBmBaseUI_SetBackgroundResId(w19: number, resId: number): any; static NapiBmBaseUI_SetBackgroundColor(u19: number, v19: number): any; static NapiBmBaseUI_SetBkColorOfLeft(s19: number, t19: number): any; static NapiBmBaseUI_SetBkColorOfRight(q19: number, r19: number): any; static NapiBmBaseUI_SetWidth(p19: number, width: number): any; static NapiBmBaseUI_SetHeight(o19: number, height: number): any; static NapiBmBaseUI_SetVisibility(n19: number, visibility: number): any; static NapiBmBaseUI_SetGravity(m19: number, gravity: number): any; static NapiBmBaseUI_SetAlignParent(k19: number, l19: number): any; static NapiBmBaseUI_SetLayoutWeight(j19: number, layoutWeight: number): any; static NapiBmBaseUI_SetPadding(i19: number, left: number, top: number, right: number, bottom: number): any; static NapiBmBaseUI_SetMargin(h19: number, left: number, top: number, right: number, bottom: number): any; static NapiBmBaseUI_SetClickable(f19: number, g19: boolean): any; static NapiBmLabelUI_Create(): any; static NapiBmLabelUI_SetText(instance: number, text: string): any; static NapiBmLabelUI_SetStyle(instance: number, e19: number): any; static NapiBmLabelUI_SetMinLines(instance: number, d19: number): any; static NapiBmLabelUI_SetMaxLines(instance: number, maxLines: number): any; static NapiBmRichView_Create(): any; static NapiBmRichView_SetView(b19: number, c19: number): any; static NapiBmRichView_SetAnimation(z18: number, a19: number): any; static NapiBmRichView_SetCollisionBehavior(x18: number, y18: number): any; static NapiBmRichView_SetCollisionPriority(w18: number, priority: number): any; static NapiBmRichView_SetCollisionBorder(v18: number, left: number, top: number, right: number, bottom: number): any; static NapiBmRichView_SetCollisionLineTagId(t18: number, u18: number): any; static NapiBmRichView_SetVisibility(s18: number, visibility: number): any; static NapiBmRichView_SetShowLevel(r18: number, from: number, to: number): any; static NapiBmRichView_SetLocated(p18: number, q18: number): any; static NapiBmRichView_SetOffsetX(o18: number, offsetX: number, scaleMode: number): any; static NapiBmRichView_SetOffsetY(n18: number, offsetY: number, scaleMode: number): any; static NapiBmRichView_SetOpacity(m18: number, opacity: number): any; static NapiBmRichView_SetScale(l18: number, scale: number): any; static NapiBmRichView_SetScaleX(k18: number, scaleX: number): any; static NapiBmRichView_SetScaleY(j18: number, scaleY: number): any; static NapiBmRichView_AddRichUIOption(h18: number, i18: number): any; static NapiBmRichView_DelRichUIOption(f18: number, g18: number): any; static NapiBmRichView_SetDrawFullscreenMaskFlag(instance: number, flag: boolean): any; static NapiBmLineStyle_Create(): any; static NapiBmLineStyle_SetColor(e18: number, color: number): any; static NapiBmLineStyle_SetBitmapResource(c18: number, d18: number): any; static NapiBmLineStyle_SetBmpResId(a18: number, b18: number): any; static NapiBmLineStyle_SetLineResId(y17: number, z17: number): any; static NapiBmLineStyle_SetWidth(x17: number, width: number): any; static NapiBmLineStyle_SetStrokeWidth(w17: number, width: number): any; static NapiBmLineStyle_SetStrokeColor(v17: number, color: number): any; static NapiBmLineStyle_SetTextureOption(u17: number, option: number): any; static NapiBmLineStyle_SetLineType(t17: number, lineType: number): any; static NapiBmGround_Create(): any; static NapiBmGround_SetPosition(s17: number, x: number, y: number, z: number): any; static NapiBmGround_SetWidth(r17: number, width: number): any; static NapiBmGround_SetHeight(q17: number, height: number): any; static NapiBmGround_SetAnchorX(o17: number, p17: number): any; static NapiBmGround_SetAnchorY(m17: number, n17: number): any; static NapiBmGround_SetDrawableResource(k17: number, l17: number): any; static NapiBmGeoElement_Create(j17: number): any; static NapiBmGeoElement_AddPoint(f17: number, g17: number, h17: number, i17: number): any; static NapiBmGeoElement_SetPoints(d17: number, e17: Array<number>, stride: number): any; static NapiBmGeoElement_SetStyle(b17: number, c17: number): any; static NapiBmGeoElement_SetTrackStyle(z16: number, a17: number): any; static NapiBmGeoElement_AddStyleOption(x16: number, y16: number): any; static NapiBmGeoElement_RemoveStyleOption(v16: number, w16: number): any; static NapiBmGeoElement_SetGradientColors(u16: number, type: number, colors: Array<number>): any; static NapiBmGeoElement_DelGradientColors(t16: number, type: number): any; static NapiBmGeoElement_ClearGradientColors(s16: number): any; static NapiBmGeoElement_SetCoordChainType(q16: number, r16: number): any; static NapiBmGeoElement_SetCoordChainHandle(o16: number, p16: number): any; static NapiBmTrackStyle_Create(): any; static NapiBmTrackStyle_SetColor(n16: number, color: number): any; static NapiBmTrackStyle_SetWidth(m16: number, width: number): any; static NapiBmTrackStyle_SetBitmapResource(k16: number, l16: number): any; static NapiBmTrackStyle_SetPaletteBitmapResource(i16: number, j16: number): any; static NapiBmTrackStyle_SetTrackType(h16: number, lineType: number): any; static NapiBmTrackStyle_SetOpacity(g16: number, opacity: number): any; static NapiBmTrackStyle_SetPaletteOpacity(f16: number, opacity: number): any; static NapiBmLineStyleOption_Create(): any; static NapiBmLineStyleOption_BuildStyleOption(d16: number, state: number, e16: number): any; static NapiBmCoordChainHandle_Create(): any; static NapiBmCoordChainHandle_SetCoordChainType(b16: number, c16: number): any; static NapiBmCoordChainHandle_SetCoordAlgorithm(z15: number, a16: number): any; static NapiBmCoordChainHandle_SetThreshold(y15: number, threshold: number): any; static NapiBmCoordChainHandle_Handle(w15: number, x15: Array<number>, stride: number): any; static NapiBmCoordChainHandle_GetIndexs(v15: number): any; static NapiBmCoordChainHandle_GetP0Points(u15: number): any; static NapiBmPolygon_Create(): any; static NapiBmPolygon_AddGeoElement(s15: number, t15: number): any; static NapiBmPolygon_AddHoleGeoElement(q15: number, r15: number): any; static NapiBmPolygon_ClearGeoElements(p15: number): any; static NapiBmPolygon_SetThin(n15: number, o15: number): any; static NapiBmPolygon_SetThinFactor(m15: number, factor: number): any; static NapiBmPolygon_SetJointType(l15: number, jointType: number): any; static NapiBmPolygon_SetSurfaceStyle(j15: number, k15: number): any; static NapiBmPolygon_SetDrawFullscreenMaskFlag(i15: number, flag: boolean): any; static NapiBmBaseLine_SetGeoElement(g15: number, h15: number): any; static NapiBmBaseLine_AddGeoElement(e15: number, f15: number): any; static NapiBmBaseLine_ClearGeoElements(d15: number): any; static NapiBmBaseLine_SetSmooth(c15: number, smooth: number): any; static NapiBmBaseLine_SetThin(a15: number, b15: number): any; static NapiBmBaseLine_SetSmoothFactor(z14: number, factor: number): any; static NapiBmBaseLine_SetThinFactor(y14: number, factor: number): any; static NapiBmBaseLine_SetStartCapType(w14: number, x14: number): any; static NapiBmBaseLine_SetEndCapType(u14: number, v14: number): any; static NapiBmBaseLine_SetJointType(t14: number, jointType: number): any; static NapiBmBaseLine_SetCollisionTagId(r14: number, s14: number): any; static NapiBmBaseLine_SetCollisionBehavior(p14: number, q14: number): any; static NapiBmBaseLine_SetLineBloomMode(o14: number, mode: number): any; static NapiBmBaseLine_SetBloomBlurTimes(n14: number, time: number): any; static NapiBmBaseLine_SetLineDirectionCrossType(m14: number, type: number): any; static NapiBmBaseLine_SetBloomAlpha(l14: number, alpha: number): any; static NapiBmBaseLine_SetBloomWidth(k14: number, width: number): any; static NapiBmBaseLine_SetBloomGradientASpeed(j14: number, speed: number): any; static NapiBmGradientLine_Create(): any; static NapiBmPolyline_Create(): any; static NapiBmPolyline_UseGeodesic(h14: number, i14: boolean): any; static NapiBmTextMarker_Create(): any; static NapiBmTextMarker_SetText(g14: number, text: string): any; static NapiBmTextMarker_SetStyle(e14: number, f14: number): any; static NapiBmImageUI_Create(): any; static NapiBmImageUI_SetBmpResId(d14: number, resId: number): any; static NapiBmImageUI_SetDrawableResource(b14: number, c14: number): any; static NapiBmImageUI_SetMaskResource(z13: number, a14: number): any; static NapiBmImageUI_SetColor(x13: number, y13: number): any; static NapiBmGroupUI_AddView(v13: number, w13: number, index: number): any; static NapiBmGroupUI_RemoveAllViews(u13: number): any; static NapiBmFrameLayout_Create(): any; static NapiBmHorizontalLayout_Create(): any; static NapiBmVerticalLayout_Create(): any; static nativeSetInterpolator(s13: number, t13: number): void; static nativeSetStartTime(q13: number, r13: number): void; static setStartTime(p13: number): void; static nativePause(o13: number): void; static nativeResume(n13: number): void; static nativeSetDuration(l13: number, m13: number): void; static nativeSetStartDelay(j13: number, k13: number): void; static nativeSetRepeatDelay(h13: number, i13: number): void; static nativeSetRepeatCount(g13: number, repeatCount: number): void; static nativeSetRepeatMode(f13: number, repeatMode: number): void; static nativeSetFillMode(e13: number, fillMode: number): void; static nativeReset(d13: number): void; static nativeCancel(c13: number): void; static nativeSetListener(a13: number, b13: boolean): void; } 