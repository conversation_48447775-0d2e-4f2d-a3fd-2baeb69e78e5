export class StsInfo {
    constructor() {
        this.nativeGetAccessKeyId = () => {
            return this.mAccessKeyId;
        };
        this.nativeSetAccessKeyId = (i36) => {
            this.mAccessKeyId = i36;
        };
        this.nativeGetAccessKeySecret = () => {
            return this.mAccessKeySecret;
        };
        this.nativeSetAccessKeySecret = (h36) => {
            this.mAccessKeySecret = h36;
        };
        this.nativeGetSecurityToken = () => {
            return this.mSecurityToken;
        };
        this.nativeSetSecurityToken = (g36) => {
            this.mSecurityToken = g36;
        };
        this.nativeGetRegion = () => {
            return this.mRegion;
        };
        this.nativeSetRegion = (f36) => {
            this.mRegion = f36;
        };
        this.nativeGetFormatStr = () => {
            return this.getFormatStr();
        };
        this.mAccessKeyId = '';
        this.mAccessKeySecret = '';
        this.mSecurityToken = '';
        this.mRegion = '';
        this.mFormats = [];
    }
    setAccessKeyId(v35) {
        this.mAccessKeyId = v35;
    }
    setAccessKeySecret(u35) {
        this.mAccessKeySecret = u35;
    }
    setSecurityToken(t35) {
        this.mSecurityToken = t35;
    }
    setRegion(s35) {
        this.mRegion = s35;
    }
    getAccessKeyId() {
        return this.mAccessKeyId;
    }
    getAccessKeySecret() {
        return this.mAccessKeySecret;
    }
    getSecurityToken() {
        return this.mSecurityToken;
    }
    getRegion() {
        return this.mRegion;
    }
    getFormats() {
        return this.mFormats;
    }
    setFormats(r35) {
        this.mFormats = r35;
    }
    getFormatStr() {
        if (!this.mFormats || this.mFormats.length == 0) {
            return "";
        }
        let p35 = "";
        for (let q35 of this.mFormats) {
            if (q35 != null) {
                p35 += q35 + ",";
            }
        }
        if (p35.length > 0) {
            p35 = p35.slice(0, -1);
        }
        return p35;
    }
}
