            import type { FingerData } from "../g1/i1"; import type { IXYZ } from "../g1/a2"; import type Map from "./j"; export declare class FingerEvent { type: symbol; timestamp: number; dataStr: string; x: number; y: number; spanX: number; spanY: number; scale: number; rotationAngle: number; swipeAngle: number; swipeSpeed: number; swipeGlobalX: number; swipeGlobalY: number; constructor(type: symbol, timestamp: number, t28: string); parseData(): void; } export declare class FingerEventHandle { private map; private open; private delaSpanX; private delaSpanY; private delaScale; private delaRotation; private swipeSpeed; private swipeOn; private fingerEvent; private lastTime; private lastRotation; private lastScale; private x; private y; private moveSpeeds; constructor(map: Map); initData(): void; resetDela(): void; addFingerEvent(type: symbol, timestamp: number, data: FingerData): void; clamp(x: number, min: number, max: number): number; smoothstep(i28: number, j28: number, x: number): number; moveTarget(): { targetX: number; targetY: number; spanX: number; spanY: number; }; addSpeed(x0: number, y0: number, x1: number, y1: number, z27: number): void; getTriangleDis(x27: IXYZ, p2: IXYZ): { deltaX: number; deltaY: number; delta: number; }; } 