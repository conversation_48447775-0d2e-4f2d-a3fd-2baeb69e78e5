import List from '@ohos.util.List';
import { NativeInsertLogInfo } from 'librongimlib.so';
import { ChatroomInfo } from '../chatroom/model/ChatroomInfo';
import { ChatroomJoinedInfo } from '../chatroom/model/ChatroomJoinedInfo';
import { ConversationIdentifier } from '../conversation/ConversationIdentifier';
import { InitOption } from '../InitOption';
import { ConnectionStatus, ConversationType, HardwareResourceType, Order, PushNotificationLevel, ReceivedStatus, SentStatus } from '../MacroDefine';
import { RecallNotificationMessage } from '../message/content/normal/RecallNotificationMessage';
import { Message } from '../message/Message';
import { Conversation } from '../conversation/Conversation';
import { IHistoryMessageOption, ICountOption, IGetLocalMsgByIdOption, IGetLocalMsgByTimeOption, IGetRemoteMsgOption, ISearchMessageInTimeRangeOption, ISendMsgOption } from '../message/option/MessageOption';
import { NetworkMonitorListener } from './monitor/NetworkMonitor';
import common from '@ohos.app.ability.common';
import { MessageContent, MessageContentConstructor, MessageFlag } from '../message/content/MessageContent';
import { EngineError } from '../EngineError';
import { CrashMonitorListener } from './monitor/CrashMonitor';
import { IGetConversationOption, IQuietHoursOption, ISetConversationTopOption } from '../conversation/option/ConversationOption';
import { AppState, NetworkType, RCCallback } from './InternalDefine';
import { ChatroomStatusListener } from '../chatroom/listener/ChatroomStatusListener';
import { LogLevel } from '../log/Log';
import { PublicServiceInfo } from '../publicservice/model/PublicServiceInfo';
import { ChatroomKVStatusListener } from '../chatroom/listener/ChatroomKVStatusListener';
import { ChatroomMemberActionListener } from '../chatroom/listener/ChatroomMemberActionListener';
import { ChatroomNotifyEventListener } from '../chatroom/listener/ChatroomNotifyEventListener';
import { RtcHeartbeatListener } from './rtc/listener/RtcHeartbeatListener';
import { KeyValuePair } from './model/KeyValuePair';
import { RtcRoomEventListener } from './rtc/listener/RtcRoomEventListener';
import { GroupCallSignalListener } from './rtc/listener/GroupCallSignalListener';
import { MessageExpansionListener } from '../message/listener/MessageExpansionListener';
import { HashMap } from '@kit.ArkTS';
import { ConversationStatusListener } from '../conversation/listener/ConversationStatusListener';
import { SyncConversationReadStatusListener } from '../conversation/listener/SyncConversationReadStatusListener';
import { SearchConversationResult } from '../conversation/model/SearchConversationResult';
import { MessageReceivedListener } from '../message/listener/MessageReceivedListener';
import { ConnectionStatusListener } from '../connection/listener/ConnectionStatusListener';
import { MessageDestructionListener } from '../message/listener/MessageDestructionListener';
import { MessageReadReceiptListener } from '../message/listener/MessageReadReceiptListener';
import { DatabaseStatusListener } from '../connection/listener/DatabaseStatusListener';
import { MessageRecalledListener } from '../message/listener/MessageRecalledListener';
import { MessageBlockedListener } from '../message/listener/MessageBlockedListener';
import { TypingStatusListener } from '../message/listener/TypingStatusListener';
import { MediaMessageTransfer } from '../message/listener/MediaMessageTransfer';
import { MessageReadReceiptV2Listener } from '../message/listener/MessageReadReceiptV2Listener';
import { HistoryMessageResult } from '../message/model/HistoryMessageResult';
import { MessageReadReceiptV5Listener } from '../message/listener/MessageReadReceiptV5Listener';
import { ReadReceiptInfoV5 } from '../message/model/ReadReceiptInfoV5';
import { ReadReceiptUsersResult } from '../message/model/ReadReceiptUserByPageResult';
import { ReadReceiptUsersOption } from '../message/model/ReadReceiptUsersOption';
import { MessageIdentifier } from '../message/option/MessageIdentifier';
declare class InnerEngineImpl implements NetworkMonitorListener, CrashMonitorListener {
    private static instance;
    private nativeEngine;
    private networkMonitor;
    private crashMonitor;
    private isInitialized;
    private deviceIDHandler;
    private appVersion;
    private pushToken;
    private reconnectKickEnable;
    private databaseStatusListenerArrayList;
    private messageReceivedListenerArrayList;
    private messageRecalledListenerArrayList;
    private messageBlockedListenerArrayList;
    private connectionStatusListenerArrayList;
    private typingStatusListenerArrayList;
    private messageExpansionListenerArrayList;
    private messageDestructionListenerArrayList;
    private conversationStatusListenerArrayList;
    private syncConversationReadStatusListenerArrayList;
    private messageReadReceiptListenerListenerArrayList;
    private messageReadReceiptV2ListenerArrayList;
    private messageReadReceiptV5ListenerArrayList;
    private chatroomStatusListenerArrayList;
    private chatroomKVStatusListenerArrayList;
    private chatroomMemberActionListenerArrayList;
    private chatroomNotifyEventListenerArrayList;
    private applicationStateChangeCallback;
    private suspendDelayId;
    private intervalId;
    private constructor();
    static getInstance(): InnerEngineImpl;
    /**
     * 是否已经初始化，仅用于 InitGuard 注解
     */
    isInit(): boolean;
    init(context: common.Context, appKey: string, initOption: InitOption): void;
    private clearAllListeners;
    private registerAllNativeListeners;
    private registerNativeChatroomNotifyEventListener;
    private registerNativeChatroomKVStatusListener;
    private registerNativeChatroomStatusListener;
    private registerNativeChatroomMemberListener;
    private registerNativeMessageReadReceiptListener;
    private registerNativeMessageReadReceiptV2Listener;
    private registerNativeMessageReadReceiptV5Listener;
    private registerNativeSyncConversationReadStatusListener;
    private registerNativeConversationStatusListener;
    private registerNativeMessageDestructionListener;
    private registerNativeMessageExpansionListener;
    private registerNativeTypingStatusListener;
    private registerNativeMessageBlockedListener;
    private registerNativeMessageRecalledListener;
    private registerNativeDatabaseStatusListener;
    private registerNativeConnectionStatusListener;
    private registerNativeMessageReceivedListener;
    private registerNativeOfflineMessageSyncCompletedListener;
    private registerNativeMessageSearchableWordsListener;
    private registerNativeVoipCallInfoListener;
    private getInitParam;
    /**
     * 获取媒体存储路径
     */
    private getMediaSavePath;
    onNetworkChanged(type: NetworkType, name: string): void;
    onSdkCrashed(exception: string): void;
    private registerAppStateChange;
    /**
     * 申请短时任务
     * 在应用进入后台时启动，短任务执行完毕后可以认为应用会被挂起
     * */
    private requestSuspendDelay;
    /**
     * 终止正在进行中的短任务
     * 在应用切到前台时，终止正在执行的短任务
     * */
    private cancelSuspendDelay;
    private setDeviceId;
    getVersion(): string;
    getDeltaTime(callback: RCCallback<number>): void;
    setAppVersion(ver: string): void;
    setReconnectKickEnable(enable: boolean): void;
    setPushToken(pushToken: string): void;
    addDatabaseStatusListener(listener: DatabaseStatusListener): void;
    removeDatabaseStatusListener(listener: DatabaseStatusListener): void;
    setLogLevel(level: LogLevel): void;
    writeLogToNative(info: NativeInsertLogInfo): void;
    connect(token: string, timeout: number, callback: RCCallback<string>): void;
    private registerMessageToNative;
    addConnectionStatusListener(listener: ConnectionStatusListener): void;
    removeConnectionStatusListener(listener: ConnectionStatusListener): void;
    getCurrentConnectionStatus(callback: RCCallback<ConnectionStatus>): void;
    getCurrentUserId(callback: RCCallback<string>): void;
    /**
     * 同步获取用户 id
     * @returns 用户 id
     * @note 连接成功后才能获取到用户 id，否则获取空字符串
     * @version 1.6.0
     */
    getCurrentUserIdSync(): string;
    protected notifyAppStateChanged(appState: AppState): void;
    private notifyNetworkChanged;
    disconnect(isReceivePush: boolean): void;
    addMessageReceivedListener(listener: MessageReceivedListener): void;
    removeMessageReceivedListener(listener: MessageReceivedListener): void;
    addMessageRecalledListener(listener: MessageRecalledListener): void;
    removeMessageRecalledListener(listener: MessageRecalledListener): void;
    addMessageBlockedListener(listener: MessageBlockedListener): void;
    removeMessageBlockedListener(listener: MessageBlockedListener): void;
    sendMessage(message: Message, option: ISendMsgOption, userIdArray: Array<string> | null, savedCallback: (msg: Message) => void, resultCallback: RCCallback<Message>): void;
    sendMediaMessage(message: Message, option: ISendMsgOption, userIdArray: Array<string> | null, savedCallback: (msg: Message) => void, progressListener: (msg: Message, progress: number) => void, resultCallback: RCCallback<Message>): void;
    cancelSendMediaMessage(messageId: number, callback: RCCallback<void>): void;
    sendMediaMessageWithUploader(message: Message, option: ISendMsgOption, userIdArray: Array<string> | null, savedCallback: (msg: Message) => void, progressListener: (msg: Message, progress: number) => void, resultCallback: RCCallback<Message>, uploadCallback: (uploader: MediaMessageTransfer) => void): void;
    private sendMediaMessageWithUploaderImpl;
    downloadMediaMessageWithProgress(messageId: number, progressListener: (messageId: number, progress: number) => void, callback: RCCallback<string>): void;
    downloadMediaMessageWithDownloader(messageId: number, progressListener: (messageId: number, progress: number) => void, resultCallback: RCCallback<string>, downloadCallback: (downloader: MediaMessageTransfer) => void): void;
    pauseDownloadMediaMessage(messageId: number, callback: RCCallback<void>): void;
    cancelDownloadMediaMessage(messageId: number, callback: RCCallback<void>): void;
    downloadFileWithProgress(remoteUrl: string, fileName: string, startCallback: (uniqueId: number) => void, progressListener: (uniqueId: number, progress: number) => void, callback: RCCallback<string>): void;
    downloadMediaFile(uniqueId: string, remoteUrl: string, fileName: string, startCallback: (uniqueId: string) => void, progressListener: (uniqueId: string, progress: number) => void, callback: RCCallback<string>): void;
    pauseDownloadMediaFile(uniqueId: string, callback: RCCallback<void>): void;
    cancelDownloadFile(uniqueId: string, callback: RCCallback<void>): void;
    recallMessage(message: Message, callback: RCCallback<RecallNotificationMessage>): void;
    registerMessageType(msgClassList: List<MessageContentConstructor>): void;
    getMessageTypeMap(): HashMap<string, MessageFlag>;
    batchInsertMessage(msgList: List<Message>, callback: RCCallback<void>): void;
    insertMessage(msg: Message, callback: RCCallback<Message>): void;
    getMessages(conId: ConversationIdentifier, option: IHistoryMessageOption, callback: RCCallback<HistoryMessageResult>): void;
    getMessageById(messageId: number, callback: RCCallback<Message>): void;
    getMessageByUid(messageUid: string, callback: RCCallback<Message>): void;
    getHistoryMessagesById(conId: ConversationIdentifier, option: IGetLocalMsgByIdOption, callback: RCCallback<List<Message>>): void;
    getHistoryMessagesByTime(conId: ConversationIdentifier, option: IGetLocalMsgByTimeOption, callback: RCCallback<List<Message>>): void;
    getRemoteHistoryMessages(conId: ConversationIdentifier, option: IGetRemoteMsgOption, callback: RCCallback<List<Message>>): void;
    getUnreadMentionedMessages(conId: ConversationIdentifier, option: ICountOption, callback: RCCallback<List<Message>>): void;
    getFirstUnreadMessage(conId: ConversationIdentifier, callback: RCCallback<Message>): void;
    deleteHistoryMessagesByIds(messageIds: List<number>, callback: RCCallback<void>): void;
    deleteMessages(conId: ConversationIdentifier, callback: RCCallback<void>): void;
    deleteHistoryMessagesByTime(conId: ConversationIdentifier, sentTime: number, callback: RCCallback<void>): void;
    cleanRemoteHistoryMessagesByTime(conId: ConversationIdentifier, sentTime: number, callback: RCCallback<void>): void;
    deleteRemoteMessages(conId: ConversationIdentifier, msgList: List<Message>, isDeleteLocal: boolean, callback: RCCallback<void>): void;
    addTypingStatusListener(listener: TypingStatusListener): void;
    removeTypingStatusListener(listener: TypingStatusListener): void;
    sendTypingStatus(conId: ConversationIdentifier, objectName: string, callback: RCCallback<void>): void;
    setTypingStatusInterval(interval: number, callback: RCCallback<void>): void;
    setMessageReceivedStatus(messageId: number, receiveStatus: ReceivedStatus, callback: RCCallback<void>): void;
    setMessageExtra(messageId: number, extra: string, callback: RCCallback<void>): void;
    /**
     * 修改本地数据库的消息发送状态（本方法不对外暴露）
     * @param messageId 消息 Id，必须 > 0
     * @param sentStatus 发送状态，不能设置 SentStatus.Sending 否则报错
     * @param callback 结果
     * @warning 消息的发送状态会影响消息 UI 展示，原则上只能由 SDK 内部维护，不建议外部进行修改
     * @version 1.2.0
     */
    setMessageSentStatus(messageId: number, sentStatus: SentStatus, callback: RCCallback<void>): void;
    /**
     * 修改本地数据库的消息内容（本方法不对外暴露）
     * @param messageId 消息 Id，必须 > 0
     * @param content 消息内容
     * @param objectName 消息类型。objectName 为空，SDK 会使用 content 对应的值；objectName 为有效值，则必须和 content 匹配，否则会出现解析错误
     * @param searchableWord 搜索的关键字，可以为空。逻辑和 MessageContent 的 getSearchableWord() 一致
     * @warning 消息内容会影响 UI 展示和问题排查，原则上只能由 SDK 内部维护，不建议外部进行修改
     * @version 1.2.0
     */
    setMessageContent(messageId: number, content: MessageContent, objectName: string | null, searchableWord: List<string> | null, callback: RCCallback<void>): void;
    addMessageExpansionListener(listener: MessageExpansionListener): void;
    removeMessageExpansionListener(listener: MessageExpansionListener): void;
    updateMessageExpansion(expansion: Map<string, string>, messageUid: string, callback: RCCallback<void>): void;
    removeMessageExpansion(keyArray: Array<string>, messageUid: string, callback: RCCallback<void>): void;
    addMessageDestructionListener(listener: MessageDestructionListener): void;
    removeMessageDestructionListener(listener: MessageDestructionListener): void;
    messageBeginDestruct(message: Message, callback: RCCallback<void>): void;
    messageStopDestruct(message: Message, callback: RCCallback<void>): void;
    setPushContentShowStatus(showStatus: boolean, callback: RCCallback<void>): void;
    getPushContentShowStatus(callback: RCCallback<boolean>): void;
    setPushReceiveStatus(receiveStatus: boolean, callback: RCCallback<void>): void;
    getPushReceiveStatus(callback: RCCallback<boolean>): void;
    addConversationStatusListener(listener: ConversationStatusListener): void;
    removeConversationStatusListener(listener: ConversationStatusListener): void;
    getConversation(conId: ConversationIdentifier, callback: RCCallback<Conversation>): void;
    getConversationListByPage(conTypeList: List<ConversationType>, option: IGetConversationOption, callback: RCCallback<List<Conversation>>): void;
    getTopConversationListByPage(conTypeList: List<ConversationType>, option: IGetConversationOption, callback: RCCallback<List<Conversation>>): void;
    getBlockedConversationListByPage(conTypeList: List<ConversationType>, option: IGetConversationOption, callback: RCCallback<List<Conversation>>): void;
    getUnreadConversations(conTypeList: List<ConversationType>, callback: RCCallback<List<Conversation>>): void;
    clearConversations(conTypeList: List<ConversationType>, callback: RCCallback<void>): void;
    removeConversations(conIdList: List<ConversationIdentifier>, callback: RCCallback<void>): void;
    removeRemoteConversations(conIdList: Array<ConversationIdentifier>, callback: RCCallback<void>): void;
    setReadTimestamp(conId: ConversationIdentifier, timestamp: number, callback: RCCallback<void>): void;
    setConversationsToTop(conIdList: List<ConversationIdentifier>, option: ISetConversationTopOption, callback: RCCallback<List<Conversation>>): void;
    getConversationTopStatus(conId: ConversationIdentifier, callback: RCCallback<boolean>): void;
    setConversationsNotificationLevel(conIdList: List<ConversationIdentifier>, level: PushNotificationLevel, callback: RCCallback<void>): void;
    getConversationNotificationLevel(conId: ConversationIdentifier, callback: RCCallback<PushNotificationLevel>): void;
    saveTextMessageDraft(conId: ConversationIdentifier, draft: string, callback: RCCallback<void>): void;
    getTextMessageDraft(conId: ConversationIdentifier, callback: RCCallback<string>): void;
    setNotificationQuietHoursLevel(option: IQuietHoursOption, callback: RCCallback<void>): void;
    getNotificationQuietHoursLevel(callback: RCCallback<IQuietHoursOption>): void;
    removeNotificationQuietHours(callback: RCCallback<void>): void;
    getTotalUnreadCount(callback: RCCallback<number>): void;
    getTotalUnreadCountByIds(conIds: List<ConversationIdentifier>, callback: RCCallback<number>): void;
    getUnreadCount(conId: ConversationIdentifier, callback: RCCallback<number>): void;
    clearMessagesUnreadStatus(conId: ConversationIdentifier, callback: RCCallback<void>): void;
    clearMessagesUnreadStatusByTime(conId: ConversationIdentifier, time: number, callback: RCCallback<void>): void;
    getUnreadCountByTypes(typeList: List<ConversationType>, isContainBlocked: boolean, callback: RCCallback<number>): void;
    syncConversationReadStatus(conId: ConversationIdentifier, timestamp: number, callback: RCCallback<void>): void;
    addSyncConversationReadStatusListener(listener: SyncConversationReadStatusListener): void;
    removeSyncConversationReadStatusListener(listener: SyncConversationReadStatusListener): void;
    addMessageReadReceiptListener(listener: MessageReadReceiptListener): void;
    removeMessageReadReceiptListener(listener: MessageReadReceiptListener): void;
    addMessageReadReceiptV2Listener(listener: MessageReadReceiptV2Listener): void;
    removeMessageReadReceiptV2Listener(listener: MessageReadReceiptV2Listener): void;
    addMessageReadReceiptV5Listener(listener: MessageReadReceiptV5Listener): void;
    removeMessageReadReceiptV5Listener(listener: MessageReadReceiptV5Listener): void;
    sendReadReceiptMessage(conId: ConversationIdentifier, timestamp: number, callback: RCCallback<void>): void;
    sendReadReceiptRequest(message: Message, callback: RCCallback<void>): void;
    sendReadReceiptResponse(conId: ConversationIdentifier, messageArray: Array<Message>, callback: RCCallback<List<Message>>): void;
    sendReadReceiptResponseV2(conId: ConversationIdentifier, messageArray: Array<Message>, callback: RCCallback<List<Message>>): void;
    getMessagesReadReceiptByUsersV5(conId: ConversationIdentifier, messageUid: string, userList: Array<string>, callback: RCCallback<ReadReceiptUsersResult>): void;
    getMessagesReadReceiptUsersByPageV5(conId: ConversationIdentifier, messageUid: string, option: ReadReceiptUsersOption, callback: RCCallback<ReadReceiptUsersResult>): void;
    getMessageReadReceiptInfoV5(conId: ConversationIdentifier, messageUidArray: Array<String>, callback: RCCallback<List<ReadReceiptInfoV5>>): void;
    getMessageReadReceiptInfoV5ByIdentifiers(identifiers: Array<MessageIdentifier>, callback: RCCallback<List<ReadReceiptInfoV5>>): void;
    sendReadReceiptResponseV5(conId: ConversationIdentifier, messageUidArray: Array<String>, callback: RCCallback<List<Message>>): void;
    getGroupMessageReaderList(conId: ConversationIdentifier, messageUid: string, successCallback: (totalCount: number, readerList: Map<string, number>) => void, callback: RCCallback<number>): void;
    searchConversationsWithResult(typeList: List<ConversationType>, keyword: string, objNameList: List<string> | null, callback: RCCallback<List<SearchConversationResult>>): void;
    searchMessages(conId: ConversationIdentifier, keyword: string, objNameList: List<string> | null, startTime: number, count: number, callback: RCCallback<List<Message>>): void;
    searchMessagesInTimeRange(conId: ConversationIdentifier, keyword: string, option: ISearchMessageInTimeRangeOption, callback: RCCallback<List<Message>>): void;
    searchMessagesByUser(conId: ConversationIdentifier, userId: string, startTime: number, count: number, callback: RCCallback<List<Message>>): void;
    searchMessagesByConversations(conTypes: Array<ConversationType>, targetIds: Array<string> | null, channelIds: Array<string> | null, objNameArray: Array<string> | null, keyword: string, startTime: number, count: number, order: Order, callback: RCCallback<List<Message>>): void;
    searchMessagesByUsers(conId: ConversationIdentifier, sendIds: Array<string>, objNameArray: Array<string> | null, startTime: number, count: number, order: Order, callback: RCCallback<List<Message>>): void;
    getPublicServiceList(callback: RCCallback<List<PublicServiceInfo>>): void;
    getPublicService(conId: ConversationIdentifier, callback: RCCallback<PublicServiceInfo>): void;
    addChatroomStatusListener(listener: ChatroomStatusListener): void;
    removeChatroomStatusListener(listener: ChatroomStatusListener): void;
    joinChatroom(roomId: string, msgCount: number, callback: RCCallback<ChatroomJoinedInfo>): void;
    joinExistingChatroom(roomId: string, msgCount: number, callback: RCCallback<ChatroomJoinedInfo>): void;
    quitChatroom(roomId: string, callback: RCCallback<void>): void;
    getChatroomInfo(roomId: string, option: ICountOption, callback: RCCallback<ChatroomInfo>): void;
    setChatroomEntries(roomId: string, entries: Map</* key */ string, /* value */ string>, autoDelete: boolean, overWrite: boolean, callback: RCCallback<Map</* key */ string, EngineError>>): void;
    deleteChatroomEntries(roomId: string, keys: List</* key */ string>, isForce: boolean, callback: RCCallback<Map</* key */ string, EngineError>>): void;
    getChatroomEntries(roomId: string, keys: List</* key */ string>, callback: RCCallback<Map<string, string>>): void;
    getAllChatroomEntries(roomId: string, callback: RCCallback<Map<string, string>>): void;
    addChatroomKVStatusListener(listener: ChatroomKVStatusListener): void;
    removeChatroomKVStatusListener(listener: ChatroomKVStatusListener): void;
    addChatroomMemberListener(listener: ChatroomMemberActionListener): void;
    removeChatroomMemberListener(listener: ChatroomMemberActionListener): void;
    addChatroomNotifyEventListener(listener: ChatroomNotifyEventListener): void;
    removeChatroomNotifyEventListener(listener: ChatroomNotifyEventListener): void;
    /**
     * 发送 RTC 信令
     * @version 1.2.0
     */
    sendRtcSignaling(roomId: string, signalingName: string, isQuery: boolean, pbBuffer: Uint8Array, timeout: number, callback: RCCallback<Uint8Array>): number;
    /**
     * 取消 RTC 信令
     * @version 1.2.0
     */
    cancelRtcSignaling(requestIds: Array<number>): void;
    /**
     * 发送 RTC 心跳
     * @version 1.2.0
     */
    sendRtcHeartbeat(roomIds: Array<string>, timeout: number): void;
    /**
     * 设置 RTC 心跳监听
     * @version 1.2.0
     */
    setRtcHeartbeatListener(listener: RtcHeartbeatListener): void;
    /**
     * 设置 RTC 房间事件监听
     * @version 1.2.0
     */
    setRtcRoomEventListener(listener: RtcRoomEventListener): void;
    /**
     * 发送 RTC GroupCall 信令
     * @version 1.2.0
     */
    sendGroupCallSignalInfo(targetId: string, key: string, signalInfo: string, callback: RCCallback<KeyValuePair<string>>): void;
    /**
     * 设置 RTC GroupCall 信令监听
     * @version 1.2.0
     */
    setGroupCallSignalListener(listener: GroupCallSignalListener): void;
    /**
     * 是否有模块在占用音视频资源，仅供其他 ExtensionModule 实现，不建议对外暴露
     * @param type 资源类型
     * @returns true 是否被占用
     * @version 1.7.1
     */
    isOnRequestHardwareResource(type: HardwareResourceType): boolean;
}
export { InnerEngineImpl };
