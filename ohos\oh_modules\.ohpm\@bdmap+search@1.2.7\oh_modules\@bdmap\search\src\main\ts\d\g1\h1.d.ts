import { PoiBoundSearchOption } from "./h/i1/j1";
import { PoiCitySearchOption } from "./h/i1/k1";
import { PoiDetailSearchOption } from "./h/i1/t1";
import { PoiIndoorOption } from "./h/i1/s1";
import { PoiNearbySearchOption } from "./h/i1/l1";
import { PoiDetailSearchResult } from "./h/m1/q1";
import { PoiIndoorResult } from "./h/m1/r1";
import { PoiResult } from "./h/m1/n1";
/**
 * POI检索接口
 */
export declare class PoiSearch {
    static readonly DEFAULT_PAGE_CAPACITY: number;
    private _iPoiSearch;
    /**
     * 创建PoiSearch实例
     *
     * @return PoiSearch实例
     */
    static newInstance(): PoiSearch;
    private constructor();
    /**
     * 城市内检索
     *
     * @param option 请求参数
     * @return 成功发起检索返回true , 失败返回false
     */
    searchInCity(option: PoiCitySearchOption): Promise<PoiResult>;
    /**
     * 周边检索
     *
     */
    searchNearby(option: PoiNearbySearchOption): Promise<PoiResult>;
    /**
     * 范围内检索
     */
    searchInBound(option: PoiBoundSearchOption): Promise<PoiResult>;
    /**
     * POI详情检索
     */
    searchPoiDetail(option: PoiDetailSearchOption): Promise<PoiDetailSearchResult>;
    /**
     * POI 室内检索
     */
    searchPoiIndoor(option: PoiIndoorOption): Promise<PoiIndoorResult>;
}
