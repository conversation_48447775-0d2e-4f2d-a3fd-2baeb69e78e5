import { CameraProperties } from '../../CameraProperties';
import { CameraFeature } from '../CameraFeature';
import camera from '@ohos.multimedia.camera';
export declare class AutoFocusFeature extends CameraFeature<camera.FocusMode> {
    private currentSetting;
    private readonly recordingVideo;
    constructor(cameraProperties: CameraProperties, recordingVideo: boolean);
    getDebugName(): string;
    setValue(value: camera.FocusMode): void;
    getValue(): camera.FocusMode;
    checkIsSupported(): boolean;
}
