import http from '@ohos.net.http'; import { rcp } from '@kit.RemoteCommunicationKit';       export declare class Request { private _connectTimeout; get connectTimeout(): number; private _readTimeout; get readTimeout(): number; private _headers; get headers(): rcp.RequestHeaders; private _method; get method(): rcp.HttpMethod; private _url;           get url(): string; private _requestOp;               get requestOp(): http.HttpRequestOptions;               constructor(build: RequestBuilder);         get Builder(): RequestBuilder; }       export declare class RequestBuilder { private _connectTimeout; get getConnectTimeout(): number; private _readTimeout; get getReadTimeout(): number; private _method; get getMethod(): rcp.HttpMethod; private _headers; get getHeaders(): rcp.RequestHeaders; private _url;         get getUrl(): string; private _requestOp;         get getRequestOp(): http.HttpRequestOptions; constructor(request?: Request); url(url: string): RequestBuilder; method(method: rcp.HttpMethod): RequestBuilder; headers(headers: rcp.RequestHeaders): RequestBuilder; connectTimeout(time: number): this; readTimeout(time: number): this; requestOp(j6: http.HttpRequestOptions): RequestBuilder;           build(): Request; } 