export interface UrlProvider {
    getPoiSearchDomain(): string;
    getPoiDetailSearchDomain(): string;
    getIndoorPoiSearchDomain(): string;
    getSuggestionSearchDomain(): string;
    getReverseGeoCoderDomain(): string;
    getGeoCoderDomain(): string;
    getMassTransitRouteDomain(): string;
    getBusRouteDomain(): string;
    getDriveRouteDomain(): string;
    getWalkRouteDomain(): string;
    getIndoorRouteDomain(): string;
    getBusLineDomain(): string;
    getDistrictFromPoiDomain(): string;
    getDistrictSearchDomain(): string;
    getPoiDetailShareDomain(): string;
    getLocationShareDomain(): string;
    getRouteShareDomain(): string;
    getWeatherSearchDomain(): string;
    getWeatherAbroadDomain(): string;
    /**
     * 推荐上车点
     * @returns
     */
    getRecommendStopDomain(): string;
    getBuildingDomain(): string;
    getAoiDomain(): string;
    getBikingRouteDomain(): string;
}
export declare class HttpsUrlProvider implements UrlProvider {
    constructor();
    getPoiSearchDomain(): string;
    getPoiDetailSearchDomain(): string;
    getIndoorPoiSearchDomain(): string;
    getSuggestionSearchDomain(): string;
    getReverseGeoCoderDomain(): string;
    getGeoCoderDomain(): string;
    getBusRouteDomain(): string;
    getDriveRouteDomain(): string;
    getWalkRouteDomain(): string;
    getIndoorRouteDomain(): string;
    getBusLineDomain(): string;
    getDistrictFromPoiDomain(): string;
    getDistrictSearchDomain(): string;
    getPoiDetailShareDomain(): string;
    getLocationShareDomain(): string;
    getRouteShareDomain(): string;
    getWeatherSearchDomain(): string;
    getWeatherAbroadDomain(): string;
    /**
     * 获取建筑物域名，用于百度地图API的请求
     *
     * @returns {string} 返回一个字符串，表示建筑物域名，格式为：https://api.map.baidu.com/sdkproxy/lbs_harmonyos_sdk/polygon/v1/fuzzy_search
     */
    getBuildingDomain(): string;
    /**
     * 获取AOI域名，用于请求百度地图API的AOI相关接口。
     *
     * @returns {string} AOI域名，格式为字符串，包括协议、主机和路径部分。
     */
    getAoiDomain(): string;
    getMassTransitRouteDomain(): string;
    getRecommendStopDomain(): string;
    getBikingRouteDomain(): string;
}
export declare class UrlProviderFactory {
    static getUrlProvider(): UrlProvider;
}
