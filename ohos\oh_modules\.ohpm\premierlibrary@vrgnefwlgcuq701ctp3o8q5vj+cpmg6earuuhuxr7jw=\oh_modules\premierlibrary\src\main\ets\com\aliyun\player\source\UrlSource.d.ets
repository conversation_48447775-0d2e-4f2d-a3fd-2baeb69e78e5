import { SourceBase } from './SourceBase';
export declare class UrlSource extends SourceBase {
    private mUri;
    private mCacheFilePath;
    private mOriginSize;
    private mExtraInfo;
    constructor();
    private nativeGetUri;
    private nativeSetUri_;
    private nativeGetExtraInfo;
    private nativeSetExtraInfo;
    private nativeGetCacheFilePath;
    private nativeSetCacheFilePath;
    private nativeGetOriginSize;
    private nativeSetOriginSize;
    /**
     * 获取设置的播放地址
     *
     * @return 播放地址
     */
    /****
     * Query the playback URL.
     *
     * @return The playback URL.
     */
    getUri(): String;
    /**
     * 获取设置的播放额外信息
     *
     * @return 播放额外信息
     */
    /****
     * Query the playback ExtraInfo.
     *
     * @return The playback ExtraInfo.
     */
    getExtraInfo(): String;
    /**
     * 设置播放的额外信息
     *
     * @param extraInfo 播放额外信息
     */
    /****
     * Set the playback ExtraInfo.
     *
     * @param mUri The playback ExtraInfo: an extraInfo of this url.
     */
    setExtraInfo(m36: String): void;
    /**
     * 设置播放的地址
     *
     * @param mUri 本地或网络地址
     */
    /****
     * Set the playback URL.
     *
     * @param mUri The playback URL: a local address or a URL.
     */
    setUri(l36: String): void;
    /**
     * 获取本地文件路径
     *
     * @return 本地文件路径
     */
    getCacheFilePath(): String;
    /**
     * 设置本地文件路径
     *
     * @param cacheFilePath 本地文件路径
     */
    setCacheFilePath(k36: String): void;
    /**
     * 获取url文件大小
     * @return
     */
    getOriginSize(): number;
    /**
     * 设置url文件大小
     * @param originSize
     */
    setOriginSize(j36: number): void;
}
