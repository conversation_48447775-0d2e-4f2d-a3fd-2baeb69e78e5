import { InstanceManager } from './InstanceManager';
import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import { GeolocationPermissionsCallbackHostApi } from "./GeneratedOhosWebView";
export declare class GeolocationPermissionsCallbackHostApiImpl extends GeolocationPermissionsCallbackHostApi {
    private binaryMessenger;
    private instanceManager;
    constructor(binaryMessenger: BinaryMessenger, instanceManager: InstanceManager);
    invoke(instanceId: number, origin: string, allow: boolean, retain: boolean): void;
    private getGeolocationPermissionsCallbackInstance;
}
