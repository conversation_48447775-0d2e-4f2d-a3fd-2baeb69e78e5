import { CameraProperties } from '../../CameraProperties';
import { CameraFeature } from '../CameraFeature';
import { Point } from '../Point';
import { SensorOrientationFeature } from '../sensororientation/SensorOrientationFeature';
import { Size as Size } from "@ohos.arkui.node";
import camera from '@ohos.multimedia.camera';
export declare class ExposurePointFeature extends CameraFeature<Point> {
    private cameraBoundaries;
    private exposurePoint;
    private exposureRectangle;
    private readonly sensorOrientationFeature;
    private defaultRegionsHasBeenSet;
    defaultRegions: camera.Rect[] | null;
    constructor(cameraProperties: CameraProperties, sensorOrientationFeature: SensorOrientationFeature);
    setCameraBoundaries(cameraBoundaries: Size): void;
    getDebugName(): string;
    getValue(): Point;
    setValue(value: Point): void;
    checkIsSupported(): boolean;
    updateBuilder(): void;
    private buildExposureRectangle;
    private convertPointToMeteringRectangle;
}
