// @keepTs
// @ts-nocheck
/**
 * Created on 2024/07/09
 * <AUTHOR>
 */
import { Message } from '@rongcloud/imlib';
import { UserInfoModel } from '../../user/model/UserInfoModel';
import { UiMessage } from '../model/UiMessage';
/**
 * 消息气泡 View，包含头像、消息内容
 * @version 1.0.0
 */
@Component
export declare struct MessageBubbleView {
    @Require
    @Prop
    uiMessage: UiMessage;
    @ObjectLink
    userInfo: UserInfoModel;
    @State
    destructDuration: number;
    @State
    isDestructing: boolean;
    @Prop
    isShowRedForVoice: boolean;
    @Prop
    isOnlyShowMessageContent: boolean;
    @State
    isSendReadReceiptRequest: boolean;
    private avatarId;
    private messageAvatarStyle;
    private msgDestructionListener;
    @Builder
    customBuilder(): void;
    @BuilderParam
    customBuilderParam: () => void;
    aboutToAppear(): void;
    aboutToDisappear(): void;
    build(): void;
    @Builder
    StatusView(): void;
    @Builder
    AvatarImage(): void;
    @Builder
    DestructView(): void;
    onAvatarLongClick(n18: Message, o18: GestureEvent): void;
    onMessagePortraitClick(h18: Message, i18: ClickEvent): void;
    private showRedPointView;
    private showVoiceDownloadErrorView;
    private showReceiptStatusView;
    private showGroupReceiptStatusView;
    private showGroupReceiptCountView;
    private getReadNum;
    @Builder
    BubbleView(): void;
    @Builder
    FailedStatusView(): void;
    @Builder
    SendingStatusView(): void;
    @Builder
    GroupReceiptStatusView(): void;
    @Builder
    GroupReceiptCountView(): void;
    @Builder
    ReceiptStatusView(): void;
    @Builder
    VoiceStatusView(): void;
    @Builder
    VoiceDownloadStatusView(): void;
    @Builder
    CancelStatusView(): void;
    @Builder
    Nickname(): void;
    bubbleBorderColorInner(f11: Message): Resource;
    bubbleBorderColor(b11: Message): ResourceColor;
    bubbleColorInner(a11: Message): Resource | Color;
    bubbleColor(w10: Message): ResourceColor;
    bubbleBorderRadius(t10: Message): Length | BorderRadiuses | LocalizedBorderRadiuses;
    private handlerLongClick;
    private onMessageDestructing;
    private onMessageDestructionStop;
    /**
     * 删除当前消息
     */
    private deleteCurrentMessage;
    /**
     * 是否是阅后即焚模式
     * @returns
     */
    private isDestructMode;
}
