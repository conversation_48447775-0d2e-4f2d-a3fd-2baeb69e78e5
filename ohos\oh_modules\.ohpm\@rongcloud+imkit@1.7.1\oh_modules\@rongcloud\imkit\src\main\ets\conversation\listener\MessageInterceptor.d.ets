// @keepTs
// @ts-nocheck
/**
 * Created on 2024/9/4
 * <AUTHOR>
 */
import { MediaMessageTransfer, Message } from '@rongcloud/imlib';
/**
 * 消息拦截器
 * # 说明
 *```
 * 该消息拦截器是属于 IMKit 的，所以所有的拦截方法只有当 RongIM 相关方法才会触发
 * 如果是直接调用 IMLib 的相关方法，不会触发该拦截器方法
 *```
 * @version 1.0.0
 */
export interface MessageInterceptor {
    /**
     * 在发消息前拦截并修改消息体
     * # 示例代码
     * ```ts
     * let interceptor: MessageInterceptor = {
     *   onWillSendMessage: (message: Message) => {
     *     // 以修改 TextMessage的内容为例，在原内容基础上追加内容：
     *     if (message.content instanceof TextMessage) {
     *       let textMessage = message.content as TextMessage
     *       textMessage.content = textMessage.content + "额外修改的内容"
     *       return message
     *     }
     *     return message
     *   },
     * }
     * RongIM.getInstance().messageService().setMessageInterceptor(interceptor)
     * ```
     * @param message 即将发送的消息体
     * @returns 修改之后的消息体
     * @warning 如果返回的 Message 为空或者 Message.content 为空，该消息将不会发送出去
     * @warning SDK内部优先判断 `onWillSendMessageSync` 接口，未实现则再判断此接口。
     */
    onWillSendMessage?: (message: Message) => Message | null;
    /**
     * 在发消息前拦截并修改消息体，同步接口
     * # 示例代码
     *```ts
     * let interceptor: MessageInterceptor = {
     *   onWillSendMessageSync: async (message: Message) => {
     *     // 以修改 TextMessage的内容为例，在原内容基础上追加内容：
     *     if (message.content instanceof TextMessage) {
     *       let textMessage = message.content as TextMessage
     *       textMessage.content = textMessage.content + "额外修改的内容"
     *       return message
     *     }
     *     return message
     *   },
     * }
     * RongIM.getInstance().messageService().setMessageInterceptor(interceptor)
     *```
     * @param message 即将发送的消息体
     * @returns 修改之后的消息体
     * @warning 如果返回的 Message 为空或者 Message.content 为空，该消息将不会发送出去。
     * @warning SDK内部优先判断此接口，未实现则再判断 `onWillSendMessage` 接口。
     */
    onWillSendMessageSync?: (message: Message) => Promise<Message | null>;
    /**
     * 发送媒体消息进行上传媒体资源前拦截，同步接口
     * # 示例代码
     *```ts
     * let interceptor: MessageInterceptor = {
     *     onWillUploadMessageSync: async (message: Message, localPath: string) => {
     *       // 拦截接口，如果决定拦截则返回此接口，在此接口中使用 transfer 做进度回传操作。不拦截返回 null 即可。
     *       let transferCallback = (transfer: MediaMessageTransfer) => {
     *         let progress = 0
     *         // 此处模拟异步回调上传进度与结果，需根据实际业务处理
     *         let timer = setInterval(() => {
     *           progress += 10
     *           if (progress < 100) {
     *             // 模拟回传进度
     *             transfer.updateProgress(progress)
     *           } else {
     *             // 模拟上传成功、失败、取消
     *             // 上传成功，回传地址
     *             transfer.success("业务侧上传后的远端地址")
     *             // 上传失败
     *             // message.sentStatus = SentStatus.Failed
     *             // transfer.error()
     *             // 上传取消
     *             // transfer.cancel()
     *             clearInterval(timer);
     *           }
     *         }, 100);
     *       }
     *       return transferCallback
     *     }
     *   }
     * RongIM.getInstance().messageService().setMessageInterceptor(interceptor)
     *```
     * @param message 即将上传并发送的消息体
     * @param localPath 即将上传的本地路径
     * @returns 拦截接口。返回 `(transfer: MediaMessageTransfer) => void` 代表由开发者控制上传，返回 null 代表由SDK执行上传。
     */
    onWillUploadMessageSync?: (message: Message, localPath: string) => Promise<((transfer: MediaMessageTransfer) => void) | null>;
    /**
     * 下载媒体消息的拦截器，同步接口
     * # 示例代码
     *```
     *let interceptor: MessageInterceptor = {
     *  onWillDownloadMessageSync: async (message: Message, remoteUrl: string) => {
     *    // 拦截接口，如果决定拦截则返回此接口，在此接口中使用 transfer 做进度回传操作。不拦截返回 null 即可。
     *    let transferCallback = (transfer: MediaMessageTransfer) => {
     *      let progress = 0
     *      // 此处模拟异步回调下载进度与结果，需根据实际业务处理
     *      let timer = setInterval(() => {
     *        progress += 10
     *        if (progress < 100) {
     *          // 模拟回传进度
     *          transfer.updateProgress(progress)
     *        } else {
     *          // 模拟下载成功、失败、取消
     *          // 下载成功，回传地址
     *          transfer.success("下载后的本地地址")
     *          // 下载失败
     *          // transfer.error()
     *          // 下载取消
     *          // transfer.cancel()
     *          clearInterval(timer);
     *        }
     *      }, 100);
     *    }
     *    return transferCallback
     *  },
     *}
     *RongIM.getInstance().messageService().setMessageInterceptor(interceptor)
     *```
     * @param message 即将下载的消息体
     * @param remoteUrl 即将下载的地址
     * @returns 拦截接口。返回 `(transfer: MediaMessageTransfer) => void` 代表由开发者控制下载，返回 null 代表由SDK执行下载。
     */
    onWillDownloadMessageSync?: (message: Message, remoteUrl: string) => Promise<((transfer: MediaMessageTransfer) => void) | null>;
    /**
     * 下载文件的拦截器，同步接口
     * # 示例代码
     *```
     * let interceptor: MessageInterceptor = {
     *   onWillDownloadFileSync: async (uniqueId: string, remoteUrl: string, fileName: string) => {
     *     // 拦截接口，如果决定拦截则返回此接口，在此接口中使用 transfer 做进度回传操作。不拦截返回 null 即可。
     *     let transferCallback = (transfer: MediaMessageTransfer) => {
     *       let progress = 0
     *       // 此处模拟异步回调下载进度与结果，需根据实际业务处理
     *       let timer = setInterval(() => {
     *         progress += 10
     *         if (progress < 100) {
     *           // 模拟回传进度
     *           transfer.updateProgress(progress)
     *         } else {
     *           // 模拟下载成功、失败、取消
     *           // 下载成功，回传地址
     *           transfer.success("下载后的本地地址")
     *           // 下载失败
     *           // transfer.error()
     *           // 下载取消
     *           // transfer.cancel()
     *           clearInterval(timer);
     *         }
     *       }, 100);
     *     }
     *     return transferCallback
     *   }
     * }
     * RongIM.getInstance().messageService().setMessageInterceptor(interceptor)
     *```
     * @param uniqueId 下载标识
     * @param remoteUrl 即将下载的文件地址
     * @param fileName 即将下载的文件名
     * @returns 拦截接口。返回 `(transfer: MediaMessageTransfer) => void` 代表由开发者控制下载，返回 null 代表由SDK执行下载。
     */
    onWillDownloadFileSync?: (uniqueId: string, remoteUrl: string, fileName: string) => Promise<((transfer: MediaMessageTransfer) => void) | null>;
}
