{"app": {"bundleName": "io.rong.imkit", "versionCode": 1000000, "versionName": "1.0.0", "minAPIVersion": 50000012, "targetAPIVersion": 50000012, "apiReleaseType": "Release", "compileSdkVersion": "5.0.0.71", "compileSdkType": "HarmonyOS", "appEnvironments": [], "bundleType": "app"}, "module": {"name": "RongIMKit", "type": "har", "deviceTypes": ["default", "tablet"], "requestPermissions": [{"name": "ohos.permission.GET_NETWORK_INFO", "usedScene": {"when": "always"}}, {"name": "ohos.permission.INTERNET", "usedScene": {"when": "always"}}, {"name": "ohos.permission.MICROPHONE", "reason": "$string:rc_permission_reason_microphone", "usedScene": {"when": "always"}}, {"name": "ohos.permission.STORE_PERSISTENT_DATA", "usedScene": {"when": "inuse"}}], "routerMap": "$profile:route_map", "packageName": "@rongcloud/imkit", "installationFree": false, "virtualMachine": "ark12.0.2.0", "compileMode": "esmodule", "dependencies": []}}