import { BuildingResult } from "./f2";
import { BuildingSearchOption } from "./h2";
/**
 * 建筑物检索
 */
export declare class BuildingSearch {
    private readonly iBuildingSearch;
    /**
     * BuildingSearch的构造函数，创建一个新的BuildingSearchImp实例。
     * @private
     * @constructor
     */
    constructor();
    /**
     * 获取检索建筑物对象
     *
     * @return 建筑物检索对象
     */
    static newInstance(): BuildingSearch;
    /**
     * 发起建筑物检索请求
     *
     * @param option 建筑检索参数，经纬度不能为null
     *
     * @return 异步结果
     */
    requestBuilding(option: BuildingSearchOption): Promise<BuildingResult>;
}
