      export declare enum BmVisibility {   VISIBLE = 1,   INVISIBLE = 2,   GONE = 4 }         export declare enum BmScaleMode {   NO_SCALE_DPI = 0,   SCALE_DPI = 1,   AUTO_SCALE = 2 }             export declare enum BmLocated {       CENTER = 1,         TOP = 2,         BOTTOM = 4,         LEFT = 8,         RIGHT = 16,         LEFT_TOP = 10,         LEFT_BOTTOM = 12,         RIGHT_TOP = 18,         RIGHT_BOTTOM = 20,              HALF_TOP = 1024,         HALF_BOTTOM = 2048,         HALF_LEFT = 4096,         HALF_RIGHT = 8192,         HALF_LEFT_TOP = 5120,         HALF_LEFT_BOTTOM = 6144,         HALF_RIGHT_TOP = 9216,         HALF_RIGHT_BOTTOM = 10240,              ALIGN_TOP = 1048576,         ALIGN_BOTTOM = 2097152,         ALIGN_LEFT = 4194304,         ALIGN_RIGHT = 8388608,         ALIGN_LEFT_TOP = 5242880,         ALIGN_LEFT_BOTTOM = 6291456,         ALIGN_RIGHT_TOP = 9437184,         ALIGN_RIGHT_BOTTOM = 10485760 } export declare enum BmAlign {            ALIGN_PARENT_GRAVITY = 0,       ALIGN_PARENT_LEFT = 1,       ALIGN_PARENT_TOP = 2,       ALIGN_PARENT_RIGHT = 4,       ALIGN_PARENT_BOTTOM = 8,       ALIGN_PARENT_LEFTRIGHT = 5,       ALIGN_PARENT_TOPBOTTOM = 10,       ALIGN_PARENT_HCENTER = 16,       ALIGN_PARENT_VCENTER = 32,       ALIGN_PARENT_CENTER = 48 }         export declare enum BmFontOption {   NORMAL = 0,   BOLD = 1,   ITALIC = 2,   BOLD_ITALIC = 3 }         export declare enum BmGravity {   GRAVITY_LEFT = 1,   GRAVITY_TOP = 2,   GRAVITY_RIGHT = 4,   GRAVITY_BOTTOM = 8,   GRAVITY_CENTER_HORIZONTAL = 16,   GRAVITY_CENTER_VERTICAL = 32,   GRAVITY_CENTER = 48,   GRAVITY_BOTTOM_HCENTER = 24,   GRAVITY_FILL_HORIZONTAL = 5,   GRAVITY_FILL_VERTICAL = 10 }               export declare enum BmCollisionBehavior {         NOT_COLLIDE = 0,         ALWAYS_SHOW = 1,         HIDE_BY_PRIORITY = 2,         COLLIDE_WITH_INNER = 4,         COLLIDE_WITH_BASEPOI = 8,         COLLIDE_INNER_AND_BASEPOI = 12, INNER_AND_BASEPOI = 12,         DODGE_WITH_INNER = 16,                        COLLIDE_WITH_ALL_LAYERS = 512 }         export declare enum BmLineType {       SOILD = 0,       NORMAL_DASH = 1,       CIRCLE_DASH = 2 }         export declare enum BmTextureOption {       REPEAT = 0,       ROUND_HEAD = 1,                      SCALE_TEXTURE = 5 }         export declare enum BmGradientType {   GRADIENT_NORMAL = 1,   GRADIENT_FORWARD = 2,   GRADIENT_BACKWARD = 4 }         export declare enum BmCoordChain {         BDMC_ABSOLUTE = 0,       BDMC_RELATIVE_P0 = 1,       BDMC_MIINUS = 2 } export declare enum BMTrackType { SURFACE = 3, DAFAULT3D = 4 }         export declare enum BmState { NORMAL = 0, FOCUS = 2,       PROGRESS_FORWARD = 128,       PROGRESS_BACKWARD = 256 }         export declare enum BmCoordAlgorithm { BmAlgorithmNone = 1, BmDouglasPeucker = 2, BmBezierSmooth = 32, BmCardinalSmooth = 64, BmCurveSmooth = 128 }   export declare enum BmLineCapAndJointType { NoneCap = 1, ButtCap = 2, RoundCap = 4, SquareCap = 8, BevelJoint = 2048, RoundJoint = 4096, MiterJoint = 8192 } export declare enum BmRotateFeature { RotateAnimation = 1, RotateItem = 2, RotateScreenUpper = 4, RotateGeoNorth = 8, RotateFlipNo = 16, RotateFlipUpper = 32, RotateDefalut = 21 }       export declare enum BmRotateAxis { AXIS_NONE = 0, AXIS_PITCH = 1, AXIS_YAW = 2, AXIS_ROLL = 4, AXIS_ALL = 7 }       export declare enum BmLayerTag { POIMARK_LAYER_TAG = "basepoi", MAP_INDOOR_POI_LAYER = "poiindoormarklayer" }       export declare enum BmFloorAnimateType { AnimateSlow = 0, AnimateNormal = 1, AnimateFast = 2 } 