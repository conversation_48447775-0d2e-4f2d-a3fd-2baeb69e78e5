# @bdmap/map
## 简介
- 鸿蒙NEXT API12的百度地图开发包
- 支持开发者快速实现地图业务开发
- 此版本提供地图基础操作控制
- 此版本提供普通地图、卫星地图、个性化地图类型设置
- 此版本提供多种地图覆盖物展示能力等

## 下载安装

```
ohpm install @bdmap/map
```
## 使用说明
参考：[在线文档](https://lbsyun.baidu.com/faq/api?title=harmonynextsdk)

## 使用示例
```javascript
import { MapComponent, MapController, MapOptions } from "@bdmap/map";
// 配置地图初始化参数
mapOptions:MapOptions = new MapOptions();

MapComponent({ onReady: (err, mapController) => {
  // 获取地图操作控件
},mapOptions}).width('100%').height('100%')
```
## 需要权限

```
"ohos.permission.GET_WIFI_INFO",
"ohos.permission.GET_NETWORK_INFO",
"ohos.permission.GET_BUNDLE_INFO",
"ohos.permission.INTERNET",
"ohos.permission.LOCATION",
"ohos.permission.APPROXIMATELY_LOCATION"
```
## 依赖
1.百度鸿蒙地图定位包:@bdmap/locsdk

2.百度鸿蒙地图基础包:@bdmap/base

## 约束与限制
1.仅支持标准系统上运行，支持设备：华为手机。

2.HarmonyOS系统：HarmonyOS NEXT Developer Preview1及以上。

3.DevEco Studio版本：DevEco Studio NEXT Developer Preview1及以上。

4.HarmonyOS SDK版本：HarmonyOS NEXT Developer Preview1 SDK及以上。