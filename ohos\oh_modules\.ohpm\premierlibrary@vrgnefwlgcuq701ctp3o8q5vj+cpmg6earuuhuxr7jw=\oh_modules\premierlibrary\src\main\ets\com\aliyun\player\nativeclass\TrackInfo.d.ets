export declare class TrackInfo {
    static readonly AUTO_SELECT_INDEX: number;
    index: number;
    mType: TrackType;
    description: string;
    videoBitrate: number;
    videoWidth: number;
    videoHeight: number;
    videoHDRType: VideoHDRType;
    audioLang: string;
    audioChannels: number;
    audioSampleRate: number;
    audioSampleFormat: number;
    subtitleLang: string;
    vodDefinition: string;
    vodFileSize: number;
    vodPlayUrl: string;
    vodWaterMarkPlayUrl: string;
    vodFormat: string;
    private nativeSetType;
    private nativeGetType;
    setVideoHDRType(f33: number): void;
    getVideoHDRType(): number;
    /**
     * 获取播放地址。注意：只有在{@link #getType()} = {@link Type#TYPE_VOD} 时值才是正确的。
     * @return 播放地址
     */
    /****
     * Query the playback URL. Note: Only when {@link #getType()} = {@link Type#TYPE_VOD}, this value is valid.
     * @return The playback URL.
     */
    getVodPlayUrl(): string;
    /**
     * 获取格式。注意：只有在{@link #getType()} = {@link Type#TYPE_VOD} 时值才是正确的。
     * @return 格式
     */
    /****
     * Query the format of the media. Note: Only when {@link #getType()} = {@link Type#TYPE_VOD}, this value is valid.
     * @return The format of the media.
     */
    getVodFormat(): string;
    /**
     * 获取带水印的播放地址。注意：只有在{@link #getType()} = {@link Type#TYPE_VOD} 时值才是正确的。
     * @return 带水印的播放地址
     */
    /****
     * Query the playback URL of the media with a watermark added. Note: Only when {@link #getType()} = {@link Type#TYPE_VOD}, this value is valid.
     * @return The playback URL of the media with a watermark added.
     */
    getVodWaterMarkPlayUrl(): string;
    /**
     * 获取流索引
     *
     * @return 流索引
     */
    /****
     * Query the index of the stream.
     *
     * @return The index of the stream.
     */
    getIndex(): number;
    /**
     * 获取流类型
     *
     * @return 流类型
     */
    /****
     * Query the type of the stream.
     *
     * @return The type of the stream.
     */
    getType(): TrackType;
    /**
     * 获取视频流码率。注意：只有在{@link #getType()} = {@link Type#TYPE_VIDEO} 时值才是正确的。
     *
     * @return 视频流码率
     */
    /****
     * Query the bitrate of the video stream. Note: Only when {@link #getType()} = {@link Type#TYPE_VIDEO}, this value is valid.
     *
     * @return The bitrate of the video stream.
     */
    getVideoBitrate(): number;
    /**
     * 获取视频流宽度。注意：只有在{@link #getType()} = {@link Type#TYPE_VIDEO} 时值才是正确的。
     *
     * @return 视频流宽度
     */
    /****
     * Query the width of the video stream. Note: Only when {@link #getType()} = {@link Type#TYPE_VIDEO}, this value is valid.
     *
     * @return The width of the video stream.
     */
    getVideoWidth(): number;
    /**
     * 获取视频流高度。注意：只有在{@link #getType()} = {@link Type#TYPE_VIDEO} 时值才是正确的。
     *
     * @return 视频流高度
     */
    /****
     * Query the height of the video stream. Note: Only when {@link #getType()} = {@link Type#TYPE_VIDEO}, this value is valid.
     *
     * @return The height of the video stream.
     */
    getVideoHeight(): number;
    /**
     * 获取音频流语言。注意：只有在{@link #getType()} = {@link Type#TYPE_AUDIO} 时值才是正确的。
     *
     * @return 音频流语言
     */
    /****
     * Query the language of the audio stream. Note: Only when {@link #getType()} = {@link Type#TYPE_AUDIO}, this value is valid.
     *
     * @return The language of the audio stream.
     */
    getAudioLang(): string;
    /**
     * 获取音频流声道数。注意：只有在{@link #getType()} = {@link Type#TYPE_AUDIO} 时值才是正确的。
     *
     * @return 音频流声道数
     */
    /****
     * Query the number of channels of the audio stream. Note: Only when {@link #getType()} = {@link Type#TYPE_AUDIO}, this value is valid.
     *
     * @return The number of channels of the audio stream.
     */
    getAudioChannels(): number;
    /**
     * 获取音频流采样率。注意：只有在{@link #getType()} = {@link Type#TYPE_AUDIO} 时值才是正确的。
     *
     * @return 音频流采样率
     */
    /****
     * Query the sampling rate of the audio stream. Note: Only when {@link #getType()} = {@link Type#TYPE_AUDIO}, this value is valid.
     *
     * @return The sampling rate of the audio stream.
     */
    getAudioSampleRate(): number;
    /**
     * 获取音频流采样格式。注意：只有在{@link #getType()} = {@link Type#TYPE_AUDIO} 时值才是正确的。
     *
     * @return 音频流采样格式
     */
    /****
     * Query the sampling format of the audio stream. Note: Only when {@link #getType()} = {@link Type#TYPE_AUDIO}, this value is valid.
     *
     * @return The sampling format of the audio stream.
     */
    getAudioSampleFormat(): number;
    /**
     * 获取字幕流语言。注意：只有在{@link #getType()} = {@link Type#TYPE_SUBTITLE} 时值才是正确的。
     *
     * @return 字幕流语言
     */
    /****
     * Query the language of the subtitle stream. Note: Only when {@link #getType()} = {@link Type#TYPE_SUBTITLE}, this value is valid.
     *
     * @return The language of the subtitle stream.
     */
    getSubtitleLang(): string;
    /**
     * 获取流描述
     *
     * @return 流描述
     */
    /****
     * Query the description of the stream.
     *
     * @return The stream description.
     */
    getDescription(): string;
    /**
     * 获取点播流清晰度。注意：只有在{@link #getType()} = {@link Type#TYPE_VOD} 时值才是正确的。
     *
     * @return 点播流清晰度
     */
    /****
     * Query the definition of the VOD stream. Note: Only when {@link #getType()} = {@link Type#TYPE_VOD}, this value is valid.
     *
     * @return The definition of the VOD stream.
     */
    getVodDefinition(): string;
    /**
     * 获取点播流文件大小。注意：只有在{@link #getType()} = {@link Type#TYPE_VOD} 时值才是正确的。
     *
     * @return 点播流文件大小
     */
    /****
     * Query the size of the VOD file. Note: Only when {@link #getType()} = {@link Type#TYPE_VOD}, this value is valid.
     *
     * @return The size of the VOD file.
     */
    getVodFileSize(): number;
}
export declare enum VideoHDRType {
    VideoHDRType_SDR = 0,
    VideoHDRType_HDR10 = 1
}
/**
 * Track类型
 */
/****
 * Track types
 */
export declare enum TrackType {
    /**
     * 视频流
     */
    /****
     * Video stream.
     */
    TYPE_VIDEO = 0,
    /**
     * 音频流
     */
    /****
     * Audio stream.
     */
    TYPE_AUDIO = 1,
    /**
     * 字幕流
     */
    /****
     * Subtitle stream.
     */
    TYPE_SUBTITLE = 2,
    /**
     * 点播流
     */
    /****
     * VOD stream.
     */
    TYPE_VOD = 3
}
