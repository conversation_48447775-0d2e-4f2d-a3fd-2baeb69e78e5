// @keepTs
// @ts-nocheck
/**
 * 长按事件，用于处理会话列表页面 item 长按和聊天页面 item 长按
 * @version 1.0.0
 */
export interface ItemLongClickAction<T = object> {
    /**
     * 显示标题
     */
    obtainTitle: (context: Context, data: T) => string | Resource;
    /**
     * item 的长按事件
     */
    onClick: (context: Context, data: T) => void;
    /**
     * 显示内容前的过滤条件，返回 true 表示显示该内容
     */
    onFilter: (data: T) => boolean;
    /**
     * 动作 Id ，每一个动作都有一个 ID 用来区分行为
     */
    actionId: ItemLongClickMessageActionId | ItemLongClickConversationActionId | string;
}
/**
 * 聊天页面消息长按动作 Id
 * @version 1.0.0
 */
export declare enum ItemLongClickMessageActionId {
    /**
     * 复制
     */
    Copy = "message_copy",
    /**
     * 删除
     */
    Delete = "message_delete",
    /**
     * 引用
     */
    Reference = "message_reference",
    /**
     * 撤回
     */
    Recall = "message_recall",
    /**
     * 更多
     */
    More = "message_more"
}
/**
 * 会话列表会话长按动作 Id
 * @version 1.0.0
 */
export declare enum ItemLongClickConversationActionId {
    /**
     * 置顶会话
     */
    SetTop = "conversation_setTop",
    /**
     * 删除会话
     */
    Remove = "conversation_remove"
}
