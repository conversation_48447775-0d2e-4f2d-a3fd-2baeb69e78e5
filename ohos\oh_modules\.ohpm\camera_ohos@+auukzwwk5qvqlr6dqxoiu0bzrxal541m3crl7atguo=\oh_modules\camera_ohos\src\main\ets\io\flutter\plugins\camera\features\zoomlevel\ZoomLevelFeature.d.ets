import { CameraProperties } from '../../CameraProperties';
import { CameraFeature } from '../CameraFeature';
import camera from '@ohos.multimedia.camera';
export declare class ZoomLevelFeature extends CameraFeature<number> {
    private readonly hasSupport;
    private readonly sensorArraySize;
    private currentSetting;
    private minimumZoomLevel;
    private readonly maximumZoomLevel;
    constructor(cameraProperties: CameraProperties);
    getDebugName(): string;
    getValue(): number;
    setValue(value: number): void;
    checkIsSupported(): boolean;
    getMinimumZoomLevel(captureSession: camera.PhotoSession | camera.VideoSession): number;
    getMaximumZoomLevel(captureSession: camera.PhotoSession | camera.VideoSession): number;
}
