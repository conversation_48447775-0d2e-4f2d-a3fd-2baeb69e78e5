import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_platform_utils/flutter_platform_utils.dart';
import 'package:intl/intl.dart';
import 'package:wuling_flutter_app/api/community_api.dart';
import 'package:wuling_flutter_app/constant/constant.dart';
import 'package:wuling_flutter_app/page/base_page/base_page.dart';
import 'package:wuling_flutter_app/page/profile_page/setting_search_page.dart';
import 'package:wuling_flutter_app/utils/date_time_util.dart';
import 'package:wuling_flutter_app/utils/manager/login_manager.dart';
import 'package:wuling_flutter_app/utils/manager/loading_manager.dart';
import 'package:wuling_flutter_app/models/global_data.dart';
import 'package:wuling_flutter_app/utils/manager/dialog_manager.dart';
import 'package:wuling_flutter_app/utils/manager/cache_manager.dart';
import 'package:wuling_flutter_app/utils/sp_util.dart';
import 'package:wuling_flutter_app/widgets/common/custom_dialog.dart';
import 'package:wuling_flutter_app/widgets/profile_page_widgets/system_setting_page_widgets/setting_page_item.dart';
import 'package:wuling_flutter_app/widgets/profile_page_widgets/system_setting_page_widgets/setting_cell.dart';
import 'package:wuling_flutter_app/models/global_data.dart';
import 'package:wuling_flutter_app/routes/app_routes.dart';

import '../../utils/manager/log_manager.dart';

class SystemSettingsPage extends BasePage {
  SystemSettingsPage({Key? key, Color pageBackgroundColor = Colors.white})
      : super(
          key: key,
          hideAppBar: true, // 保留顶部标题栏
          appBarTitle: '设置', // 页面标题
          isWithinSafeArea: true, // 页面内容包含在SafeArea内
          pageBackgroundColor: pageBackgroundColor,
        );
  @override
  SystemSettingsPageState createState() => SystemSettingsPageState();
}

class SystemSettingsPageState extends BasePageState<SystemSettingsPage> with WidgetsBindingObserver {
  List<List<SettingItem>> dataSource = [];
  String? cacheStr;
  bool isNotificationAuthorized = false;
  bool isAuto = true; //自动签到

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    setupData();

    autoSign();
  }

  autoSign() async {
    if(GlobalData().isLogin && isAuto) {
      bool isSign = await communityAPI.todayIsSign();
      if(!isSign) {
        communityAPI.autoSign();
      }
    }
  }

  /// 获取缓存大小并更新UI
  Future<void> _loadCacheSize() async {
    int cacheSize = await CacheManager.instance.getCacheSize();
    setState(() {
      double cacheSizeMB = cacheSize / (1024 * 1024); // 转换为 MB
      cacheStr = "${NumberFormat("#,##0.00").format(cacheSizeMB)} MB";
    });
  }

  /// 清除缓存
  Future<void> _clearCache() async {
    CacheManager.instance.clearCacheWithProgress((double progress, String message) {
      LoadingManager.showProgress(progress,status: message);
      // 当进度完成后，显示完成提示
      if (progress >= 1.0) {
        setupData();
        LoadingManager.showSuccess('缓存清理完成！');
      }
    });
  }

  void setupData() async {
    await _loadCacheSize();
    setupWithLoginStatus();
  }

  void setupWithLoginStatus() {
    configureDataSource();
  }

  void configureDataSource() async {
    dataSource.clear();
    isAuto = SpUtil().getBool(Constant.AUTO_SIGN);
    if (!GlobalData().isLogin) {
      dataSource.add(SectionType.about.aboutItems(cacheSize: cacheStr));
    } else {
      isNotificationAuthorized = await _channel.invokeMethod('isOpenHarmonySetting');
      dataSource.add(SectionType.account.defaultItems());
      dataSource.add(SectionType.notification
           .notificationItems(isPushOn: isNotificationAuthorized,isAuto: isAuto));
      // if (livePermitModel != null) {
      //   final liveItems = SettingItem.SectionType.live.liveItems(livePermitModel!.permit);
      //   if (liveItems != null) {
      //     dataSource.add(liveItems);
      //   }
      // }
      dataSource.add(SectionType.about.aboutItems(cacheSize: cacheStr));
    }

    setState(() {});
  }

  void showLogoutAlertDialog(BuildContext context) {
    DialogManager().showCustomDialog(
      CustomDialog(
        title: "提示",
        content: "确定要退出登录吗？",
        buttons: [
          DialogButton(
              label: "取消",
              onPressed: () {
                LogManager().debug("取消按钮被点击");
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xFF9B9DA9)),
          DialogButton(
              label: "确定",
              onPressed: () {
                _logout(context);
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xFF00BAFF)),
        ],
      ),
    );
  }

  void showCleanCacheAlertDialog(BuildContext context) {
    DialogManager().showCustomDialog(
      CustomDialog(
        title: "确定要清除所有缓存？",
        titleFontSize: 12,
        content: "浏览记录、草稿、图片等均会被清除",
        contentFontSize: 14,
        buttons: [
          DialogButton(
              label: "取消",
              onPressed: () {
                LogManager().debug("取消按钮被点击");
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xFF9B9DA9)),
          DialogButton(
              label: "确定",
              onPressed: () {
                _clearCache();
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xFF00BAFF)),
        ],
      ),
    );
  }

  void _logout(BuildContext context) async {
    LoadingManager.show(status: '退出登录中...');
    try {
      bool isSucceed = await LoginManager().oauthLogout();
      if (isSucceed) {
        LoadingManager.showSuccess('退登成功');
        Navigator.pop(context);
      } else {
        LoadingManager.showError('退登失败');
      }
    } catch (e) {
      LoadingManager.showError('退登出错 $e');
    }
  }

  @override
  Widget buildPageContent(
    BuildContext context,
  ) {
    // 实现SystemSettingsPage特定的页面内容
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        elevation: 0,
        title: const Text("设置"),
        centerTitle: true,
        actions: [
          IconButton(
            splashRadius: 0.1,
              splashColor: Colors.transparent,
              onPressed: () {
              Navigator.of(context).push(MaterialPageRoute(builder: (context) {
                return SettingSearchPage();
              }));
          }, icon: const Icon(CupertinoIcons.search,size: 22,color: Colors.black87,))
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.zero,
              itemCount: dataSource.length,
              itemBuilder: (BuildContext context, int sectionIndex) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: dataSource[sectionIndex].length,
                      itemBuilder: (BuildContext context, int itemIndex) {
                        SettingItem item = dataSource[sectionIndex][itemIndex];
                        return SettingCell(
                          item: item,
                          onTap: (item) {
                            //  LogManager().debug('点击了 ${item.itemType.description}');
                            Widget page = item.itemType.segueWidget()??Container();
                            if(item.itemType == ItemType.cleanCache){
                              showCleanCacheAlertDialog(context);
                            } else if(item.itemType == ItemType.autoSign || item.itemType == ItemType.authorizationStatus) {

                            }else {
                              if (page is Container) {
                                // LoadingManager.showToast('页面尚未完成 敬请期待');
                              } else {
                                Navigator.of(context).push(
                                    CustomCupertinoPageRoute(
                                      builder: (context) => page,
                                      canSwipeBack: true, // 禁用手势返回
                                    ));
                              }
                            }
                          },
                          switchValueChanged: (value, item) {
                            // 处理开关值变化
                            LogManager().debug('${item.itemType.description} 开关变为 $value');
                            if(item.itemType.description == '通知设置') {
                              openHarmonySetting();
                            }else if(item.itemType.description == '自动签到') {
                              isAuto = !isAuto;
                              SpUtil().setBool(Constant.AUTO_SIGN, isAuto);
                              setupData();
                            }
                          },
                        );
                      },
                    ),
                    // 如果需要在每个section之间添加分隔符，可以在这里添加
                    const Divider(),
                  ],
                );
              },
            ),
          ),
          if (GlobalData().isLogin)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              child: TextButton(
                onPressed: () => showLogoutAlertDialog(context),
                child: const Text(
                  '退出登录',
                  style: TextStyle(color: Color(0xffee0029), fontSize: 16),
                ),
              ),
            ),
        ],
      ),
    );
  }
  final MethodChannel _channel = const MethodChannel('com.sgmw.flutter/harmony_setting');
  void openHarmonySetting() async {
    LogManager().debug("openHarmonySetting:" + 'openHarmonySetting');
    if (PlatformUtils.isOhos) {
      try {
        await _channel.invokeMethod('openHarmonySetting');
      } on PlatformException catch (e) {
        LogManager().debug("Failed to open app detail page: ${e.message}");
      }
    }
  }
  Future<bool> isOpenHarmonySetting() async {
    if (PlatformUtils.isOhos) {
      try {
      isNotificationAuthorized =  await _channel.invokeMethod('isOpenHarmonySetting');
      LogManager().debug("isNotificationAuthorized: $isNotificationAuthorized");
      } on PlatformException catch (e) {
        LogManager().debug("Failed to open app detail page: ${e.message}");
      }
    }
    return isNotificationAuthorized;
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // TODO: implement didChangeAppLifecycleState
    if (state == AppLifecycleState.paused) {
      LogManager().debug("didChangeAppLifecycleState: ${AppLifecycleState.paused}");
    } else  if(state == AppLifecycleState.resumed){
      setupData();
    }
  }

}
