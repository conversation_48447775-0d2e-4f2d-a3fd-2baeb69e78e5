import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wuling_flutter_app/page/base_page/base_page.dart';
import 'package:wuling_flutter_app/widgets/use_car_page_widgets/car_one_key_charge_widget.dart';

import '../../api/car_api.dart';
import '../../constant/web_view_url_tool.dart';
import '../../models/car/car_charge_status_model.dart';
import '../../models/car/car_info_model.dart';
import '../../models/global_data.dart';
import '../../routes/jump_tool.dart';
import '../../utils/http/api_exception.dart';
import '../../utils/manager/dialog_manager.dart';
import '../../utils/manager/loading_manager.dart';
import '../../utils/manager/log_manager.dart';
import '../../widgets/common/custom_dialog.dart';
import '../../widgets/common/custom_refresher.dart';

class CarOneKeyChargePage extends BasePage {
  CarOneKeyChargePage({
    Key? key,
    Color pageBackgroundColor = Colors.white,
  }) : super(
          key: key,
          appBarTitle: '智能补电',
          pageBackgroundColor: pageBackgroundColor,
        );

  @override
  CarOneKeyChargePageState createState() => CarOneKeyChargePageState._();
}

class CarOneKeyChargePageState extends BasePageState<CarOneKeyChargePage> {
  //私有构造方法
  CarOneKeyChargePageState._();
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  // 默认爱车信息
  CarInfoModel? _carInfoModel;
  bool _isOn = false;

  List<Widget> getWidgetList() {
    List<Widget> list = [
      const CarOneKeyChargeTopWidget(),
      const SizedBox(height: 20),
      CarOneKeyChargeBottomWidget(
        isOn: _isOn,
        onSwitchTap: onSwitchTap,
        onOneKeyChargeTap: onOneKeyChargeTap,
      )
    ];
    return list;
  }

  @override
  void pageInitState() {
    _carInfoModel = GlobalData().carInfoModel;
    super.pageInitState();
    // 延时执行是因为要等待页面构建完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _refreshController.requestRefresh();
    });
  }

  void onRefresh() async {
    CarChargeStatusModel chargeStatusModel =
        await carAPI.getAutoChargeStatusFromSevers(_carInfoModel?.vin ?? '');
    if (_refreshController.isRefresh) {
      _refreshController.refreshCompleted();
    }
    setState(() {
      _isOn = chargeStatusModel.chargingStatus == '1';
    });
  }

  void onSwitchTap(bool isOn) {
    if (isOn) {
      //弹窗
      showWillOpenSmartChargeAlertDialog();
    } else {
      autoSmartChargeWithAction(isOn);
    }
  }

  void showWillOpenSmartChargeAlertDialog() {
    DialogManager().showCustomDialog(
      CustomDialog(
        title: "12V蓄电池自动补电",
        content:
            "1、开启自动补电后,当您的爱车12V蓄电池电压过低时，车机将自动使用动力蓄电池给12V蓄电池补电，以避免因12V蓄电池亏电影响您的出行。\n2、如果您不想使用自动补电功能，也可以进行手动“一键补电”进行补电。",
        agreementText: '我已阅读并同意《12V蓄电池自动补电免责声明》',
        agreementLinkText: '《12V蓄电池自动补电免责声明》',
        forceAgree: true,
        buttonHeight: 70,
        onLinkPressed: () {
          String url = WebViewURLTool.disclaimerURL();
          JumpTool().openWeb(context, url, false);
        },
        buttons: [
          DialogButton(
              label: "暂不开启",
              onPressed: () {
                LogManager().debug("暂不开启按钮被点击");
              },
              backgroundColor: Colors.white,
              textColor: Colors.black),
          DialogButton(
              label: "立即开启",
              onPressed: () {
                autoSmartChargeWithAction(true);
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xffea0029)),
        ],
      ),
    );
  }

  void autoSmartChargeWithAction(bool isOn) async {
    if (_carInfoModel == null || (_carInfoModel?.vin ?? '').isEmpty) {
      return;
    }
    String vin = _carInfoModel?.vin ?? '';
    try {
      String address = 'junApi/sgmw/car/warning/charging/status/change';
      Map<String, dynamic> params = {};
      params['chargingStatus'] = isOn ? 1 : 0;
      params['vin'] = vin;
      String actionDesc = isOn ? '开启自动补电' : '关闭自动补电';
      String requestStatus = '$actionDesc中...';
      LoadingManager.show(status: requestStatus);
      var response =
          await carAPI.requestCarControlServiceWithURLStr(address, params);
      LoadingManager.showSuccess('$actionDesc成功');
      setState(() {
        _isOn = isOn ? true : false;
      });
    } catch (e) {
      if (e is APIException) {
        LoadingManager.showError('设置失败\n ${e.message}');
      }
    }
  }

  void onOneKeyChargeTap() async {
    if (_carInfoModel == null || (_carInfoModel?.vin ?? '').isEmpty) {
      return;
    }
    String vin = _carInfoModel?.vin ?? '';
    String actionDesc = '手动一键补电';
    try {
      String address = 'junApi/sgmw/car/control/recharge';
      Map<String, dynamic> params = {};
      params['status'] = 1;
      params['vin'] = vin;
      String requestStatus = '$actionDesc...';
      LoadingManager.show(status: requestStatus);
      var response =
          await carAPI.requestCarControlServiceWithURLStr(address, params);
      LoadingManager.showSuccess('$actionDesc成功');
    } catch (e) {
      if (e is APIException) {
        LoadingManager.showError('$actionDesc失败\n ${e.message}');
      }
    }
  }

  @override
  Widget buildPageContent(BuildContext context) {
    return Stack(children: [
      Container(color: const Color.fromARGB(255, 248, 248, 247)),
      CustomSmartRefresher(
        controller: _refreshController,
        onRefresh: onRefresh,
        child: ListView(
          children: getWidgetList(),
        ),
      ),
    ]);
    // return CustomSmartRefresher(
    //   controller: _refreshController,
    //   onRefresh: onRefresh,
    //   child: ListView(
    //     children: getWidgetList(),
    //   ),
    // );
  }

  @override
  void dispose() {
    // 在此处添加页面销毁逻辑
    _refreshController.dispose();
    super.dispose();
  }
}
