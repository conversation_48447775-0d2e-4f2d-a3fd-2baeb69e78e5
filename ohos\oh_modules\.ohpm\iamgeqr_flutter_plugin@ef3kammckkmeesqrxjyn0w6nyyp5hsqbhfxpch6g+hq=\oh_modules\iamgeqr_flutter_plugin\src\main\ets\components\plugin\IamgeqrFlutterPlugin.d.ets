import { FlutterPlugin, <PERSON>lutter<PERSON><PERSON>in<PERSON><PERSON><PERSON>, MethodCall, MethodCallHandler, MethodResult } from '@ohos/flutter_ohos';
/** IamgeqrFlutterPlugin **/
export default class IamgeqrFlutterPlugin implements FlutterPlugin, MethodCallHandler {
    private channel;
    constructor();
    getUniqueClassName(): string;
    onAttachedToEngine(binding: FlutterPluginBinding): void;
    onDetachedFromEngine(binding: FlutterPluginBinding): void;
    onMethodCall(call: MethodCall, result: MethodResult): void;
    scanPickerImage(_result: MethodResult): void;
}
