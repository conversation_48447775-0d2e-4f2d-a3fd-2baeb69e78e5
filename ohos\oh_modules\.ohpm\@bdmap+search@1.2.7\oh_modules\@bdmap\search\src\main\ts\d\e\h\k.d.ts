import type { LatLng } from '@bdmap/base';
import type { PlanNode, RouteLine, RouteNode, RouteStep, SearchResult, SuggestAddrInfo } from "./f1";
export interface Destination {
    area_id: number;
    cname: string;
    uid: string;
    wd: string;
    destinationPt?: LatLng;
    originPt?: LatLng;
}
/**
 * Steps
 */
export interface Steps {
    /**
     * 名称
     */
    name: string;
    /**
     * 指示
     */
    instructions: string;
    /**
     * 类型
     */
    type: number;
    /**
     * 转向类型
     */
    turnType: string;
    /**
     * 时长
     */
    duration: number;
    /**
     * 距离
     */
    distance: number;
    /**
     * 路径
     */
    path: string;
}
/**
 * 骑行路径规划封装后的结果
 */
export interface BikingRouteResult extends SearchResult {
    /**
     * 服务端返回的消息内容
     */
    message?: string;
    /**
     * 建议信息
     *
     * error为{@link ERRORNO#AMBIGUOUS_ROURE_ADDR}时可通过此接口获取建议信息
     */
    suggestAddrInfo?: SuggestAddrInfo;
    /**
     * 获取所有骑行规划路线
     */
    routeLines?: BikingRouteLine[];
}
/**
 * 一条骑行路线
 */
export interface BikingRouteLine extends RouteLine<BikingStep> {
}
/**
 * 描述一个骑行路段
 */
export interface BikingStep extends RouteStep {
    /**
     * 当前道路的方向角
     */
    direction?: number;
    /**
     * 路线起点
     */
    entrance?: RouteNode;
    /**
     * 路线终点
     */
    exit?: RouteNode;
    /**
     * 路段位置坐标描述
     */
    pathString?: string;
    /**
     * 路段起点信息描述
     */
    entranceInstructions?: string;
    /**
     * 路段终点信息描述
     */
    exitInstructions?: string;
    /**
     * 路段描述
     */
    instructions?: string;
    /**
     * 行驶转向方向（如"直行", "左前方转弯"）
     */
    turnType?: string;
    /**
     * 限行信息
     */
    restrictionsInfo?: string;
    /**
     * 限行类型，1：禁行；2：逆行
     */
    restrictionsStatus?: number;
}
/**
 * 骑行路线规划参数
 */
export interface BikingRoutePlanOption {
    /**
     * 起点坐标
     */
    from?: PlanNode;
    /**
     * 终点坐标
     */
    to?: PlanNode;
    /**
     * 骑行类型（0：普通骑行模式，1：电动车模式）
     */
    ridingType?: RidingType;
    /**
     * 途径点
     */
    wayPoints?: PlanNode[];
}
/**
 * 骑行模式枚举
 */
export declare enum RidingType {
    /**
     * 普通骑行
     */
    NORMAL = 0,
    /**
     * 电动车模式
     */
    E_BIKE = 1
}
