import { Size as Size } from "@ohos.arkui.node";
import { Point } from '../Point';
import { CameraFeature } from '../CameraFeature';
import { CameraProperties } from '../../CameraProperties';
import { SensorOrientationFeature } from '../sensororientation/SensorOrientationFeature';
export declare class FocusPointFeature extends CameraFeature<Point> {
    private cameraBoundaries;
    private focusPoint;
    private readonly sensorOrientationFeature;
    constructor(cameraProperties: CameraProperties, sensorOrientationFeature: SensorOrientationFeature);
    setCameraBoundaries(cameraBoundaries: Size): void;
    getDebugName(): string;
    getValue(): Point;
    setValue(value: Point): void;
    checkIsSupported(): boolean;
    private buildFocusRectangle;
}
