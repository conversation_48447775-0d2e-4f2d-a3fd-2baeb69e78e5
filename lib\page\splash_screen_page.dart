import 'dart:async';
import 'dart:convert';

import 'package:wuling_flutter_app/constant/constant.dart';
import 'package:wuling_flutter_app/utils/sp_util.dart';
import 'package:wuling_flutter_app/widgets/common/image_view.dart';
import 'package:flutter/material.dart';

import '../api/index.dart';
import '../constant/storage.dart';
import '../models/common/advertise.dart';
import '../models/community/recommend_content_list.dart';
import '../models/index.dart';
import '../models/global_data.dart';
import '../global.dart';
import '../utils/manager/notification_manager.dart';
import '../utils/user_manager.dart';
import '../utils/manager/log_manager.dart';

class SplashScreenPage extends StatefulWidget {
  const SplashScreenPage({super.key});

  @override
  _SplashScreenState createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreenPage> {
  late Timer _timer;
  String imageUrl = "";
  int count = 5;
  @override
  void initState() {
    super.initState();

    //尝试读取缓存
    var json = SpUtil().getJSON(SP_SPLASH_ADVERTISE_DATA_KEY);
    if (json != null) {
      var advertise = Advertise.fromJson(json);
      imageUrl = advertise.advertiseImage ?? '';
    }

    fetchAdData(); //需要请求防抖。

    // 新增：检查用户车主状态
    _checkUserCarOwnerStatus();

    //启动倒计时
    startCountdown();

     communityAPI.searchHotPostList().then((value) {
       if(value.isNotEmpty) {
         SpUtil().setString(Constant.SEARCH_HOT_RANK,jsonEncode(value));
       }
     });
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  void fetchAdData() async {
    try {
      var response = await commonAPI.getAdvertiseWithPosition(1);
      if (response.isNotEmpty) {
        Advertise ad = response[0];
        await SpUtil().setJSON(SP_SPLASH_ADVERTISE_DATA_KEY, ad);
        setState(() {
          imageUrl = ad.advertiseImage ?? '';
        });
      }
    } catch (e) {
      LogManager().debug('$e');
    }
  }

  // 新增：检查用户车主状态并决定进入哪个页面
  bool _hasCheckedUserStatus = false;
  int _targetPageIndex = 0; // 默认进入发现页 (INDEX_HOME = 0)
  bool _isCheckingUserStatus = false;

  void _checkUserCarOwnerStatus() async {
    if (_isCheckingUserStatus) return;
    _isCheckingUserStatus = true;

    try {
      // 先从本地获取OAuth信息判断是否登录
      Global.getSavedUserInfo();

      // 使用User单例类检查车主状态
      bool isCarOwnerUser = await User.shareInstance().isCarOwnerUser();

      setState(() {
        _hasCheckedUserStatus = true;
        if (isCarOwnerUser) {
          _targetPageIndex = 1; // INDEX_TRAVEL - 进入出行页
          print('✅ isCarOwner = 1，车主用户，将进入出行页 (INDEX_TRAVEL = 1)');
        } else {
          _targetPageIndex = 0; // INDEX_HOME - 进入发现页
          print('❌ isCarOwner = 0，非车主用户，将进入发现页 (INDEX_HOME = 0)');
        }
      });

    } catch (e) {
      print('检查用户车主状态失败: $e');
      // 接口失败，默认进入发现页
      setState(() {
        _hasCheckedUserStatus = true;
        _targetPageIndex = 0; // INDEX_HOME
      });
    } finally {
      _isCheckingUserStatus = false;
    }
  }

  void startCountdown() {
    _timer = Timer.periodic(Duration(seconds: 1), (Timer timer) {
      setState(() {
        if (count > 0) {
          count--;
        } else {
          // 确保用户状态检查完成后再跳转
          if (_hasCheckedUserStatus) {
            enterHome();
            timer.cancel();
          } else {
            // 如果接口还没返回，延长等待时间
            count = 1;
          }
        }
      });
    });
  }

  void enterHome() {
    Navigator.pushReplacementNamed(context, '/home');

    // 如果需要切换到出行页，延迟执行
    if (_targetPageIndex == 1) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        NotificationManager().postNotification(
          Constant.NOTIFICATION_MAIN_PAGE_SELECT_PAGE,
          userInfo: {'pageIndex': _targetPageIndex}
        );
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // 获取屏幕高度,宽度并处理
    final statusBarHeight = MediaQuery.of(context).size.height-140;
    final statusBarWidth = MediaQuery.of(context).size.width;
    return Scaffold(
      body: Column(
        children: [
          AspectRatio(
            aspectRatio: statusBarWidth/statusBarHeight,//16 / 16, // 设置宽高比为9:16
            child: Stack(children: [
              Center(
                  child: imageUrl.isNotEmpty ? ImageView(imageUrl):Container()
              ),
              Positioned(
                top: 60,
                right: 20,
                child: Container(
                  height: 20,
                  width: 80,
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.3),
                  ),
                  child: TextButton(
                    onPressed: () {
                      enterHome();
                    },
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          vertical: 4, horizontal: 10),
                    ),
                    child: Text(
                      "跳过广告 $count",
                      style: const TextStyle(
                        fontSize: 12,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ]),
          ),
          Expanded(
            child: Center(
                child: SizedBox(
              width: 235,
              height: 72,
              child: Center(
                child: Image.asset(
                  'assets/images/splash/start_page_ad_logo_ph.png',
                  fit: BoxFit.contain, // 确保图像不会失真或裁剪
                ),
              ),
            )),
          )
        ],
      ),
    );
  }
}
