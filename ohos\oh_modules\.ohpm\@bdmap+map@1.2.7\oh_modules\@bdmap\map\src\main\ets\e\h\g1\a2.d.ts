import type { BuildingInfo, CoordType, LatLng, Point } from '@bdmap/base'; import type MapStatus from "../i/l"; import type WinRound from "../i/o/l1"; import type { ESatelliteLayerType, FontType, LineCapType, LineDirectionCross, LineJoinType, StrokeStyle, PolylineDottedLineType, AnimateDefine, ELineBloomType, CollisionBehavior, Located, TextureOption, UnitOption, SmoothType, FloorAnimateType } from "../util/b1/c1"; import type Stroke from "../i/o/p"; import type ImageEntity from "../i/o/s"; import type { EventBundle } from "./h1"; import type BaseLayer from "../i/d1/j1"; import type LocationLayer from "../i/d1/f1"; import type CompassLayer from "../i/d1/e1"; import type OverlayLayer from "../i/d1/k1"; import image from '@ohos.multimedia.image'; import type InfoWindow from "../i/m/u"; import type LatLngBound from "../i/o/m1"; import type PopView from "../i/n1/o1"; import type TileLayer from "../i/d1/b2"; export interface DeviceInfo { width: number; height: number; filesDir: string; cacheDir: string; densityDPI: number; mapViewId: string; dpiScale: number; }         export interface ImageOpt {   flipY?: boolean;   flipX?: boolean;   scaleDpi?: boolean; }   export type ColorNumber = number; interface PixelMap extends image.PixelMap { [propName: string | symbol]: any; }       export type ColorString = string;       export type ColorType = ColorString | number;       export type NetURLString = string;       export type RawfileURLString = string;             export type ImageURLString = NetURLString | RawfileURLString;             export type ImageSourceType =   ImageURLString   | PixelMap;   export interface IXYZ { x: number; y: number; z?: number; }        export interface IGeoPoint {   lat: number;   lng: number;   z?: number; }   export interface IMapRoundOption { minX: number; minY: number; maxX: number; maxY: number; }         export interface IAnchor {   anchorx: number;   anchory: number; }         export interface IMapStatusOption {   center?: LatLng;   zoom?: number;   rotate?: number;   overlook?: number;   winRound?: WinRound;   geoRound?: LatLngBound;   ptOffset?: [ number, number ]; }         export interface IGestureOption {   rotate?: boolean;   move?: boolean;   overlooking?: boolean;   zoom?: boolean;   touchNearly?: number; }         export interface IShowOption {   satelliteMap?: ESatelliteLayerType;   indoorMap?: boolean;   trafficMap?: boolean; }         export interface IMapOption {         mapStatus?: MapStatus;         gestures?: IGestureOption;         shows?: IShowOption;           coordType?: CoordType; }         export interface IStrokeOption {         strokeWidth?: number;         color?: ColorString;           strokeStyle?: StrokeStyle;         strokeTexture?: ImageEntity;           textureOption?: TextureOption; }         export interface IOverlayOption {   alpha?: number;   visible?: boolean;   isClickable?: boolean;   zIndex?: number;   startLevel?: number;   endLevel?: number; }         export interface ICircleOption extends IOverlayOption {   center?: LatLng;   radius?: number;   radiusUnit?: UnitOption;   fillcolor?: ColorString;   stroke?: Stroke;   isGradientCircle?: boolean;   gradientColors?: Array<ColorString>;   gradientRadiusWeight?: number;   gradientColorWeight?: number; }         export interface IDotOption extends IOverlayOption {   center?: LatLng;   radius?: number;   color?: ColorString; }         export interface IGroundOption extends IOverlayOption {   image: ImageEntity;   bounds: [ LatLng, LatLng ];   anchorX?: number;   anchorY?: number;   transparency?: number; }         export interface ILabelOption extends IBaseMarkerOption {   text?: string;   fontcolorstr?: ColorString;   fontsize?: number;   bordercolorstr?: ColorString;   bordersize?: number;   fontType?: FontType | number;   bgcolorstr?: ColorString; }         export interface IPolygonOption extends IOverlayOption {   points: Array<LatLng>;   holePoints?: Array<LatLng> | Array<Array<LatLng>>;   fillcolor?: ColorString;   stroke?: Stroke;   thin?: boolean;   thinFactor?: number;   jointType?: LineJoinType;   isHoleClickable?: boolean; }         export interface IPolylineOption extends IOverlayOption {   points: Array<LatLng>;   width?: number;   fillcolor?: ColorString;   strokeWidth?: number;   strokeColor?: ColorString;   textures?: Array<ImageEntity>;           textureOption?: TextureOption;   join?: LineJoinType;   cap?: LineCapType;   startCapType?: LineCapType;   endCapType?: LineCapType;   isGeodesic?: boolean;   directionCross180?: LineDirectionCross;   isThined?: boolean;   thinFactor?: number;   smoothType?: SmoothType;   smoothFactor?: number;   dottedline?: boolean;   dottedlineType?: PolylineDottedLineType;   colorList?: Array<ColorString>;   indexList?: Array<number>;   isGradient?: boolean;   lineBloomType?: ELineBloomType;   lineBloomWidth?: number;   lineBloomAlpha?: number;   lineBloomGradientASPeed?: number;   lineBloomBlurTimes?: number; }         export interface IBaseMarkerOption extends IOverlayOption {   position?: LatLng;   anchorX?: number;   anchorY?: number;   located?: Located;   isPerspective?: boolean;   isDraggable?: boolean;   rotate?: number;   xOffset?: number;   yOffset?: number;   isFlat?: boolean;   isTop?: boolean;   isFixed?: boolean;   period?: number;   scaleX?: number;   scaleY?: number;   fixedScreenPosition?: Point;   priority?: number;   isJoinCollision?: CollisionBehavior;   popView?: PopView; }         export interface IMarkerOption extends IBaseMarkerOption {   icon?: ImageEntity;   icons?: Array<ImageEntity>;   delPreIconResource?: boolean;   color?: ColorString;   infoWindow?: InfoWindow;   animateType?: AnimateDefine; }         export interface IPrismOption extends IOverlayOption {   points: Array<LatLng>;   height?: number;   topFaceColor?: ColorType;   sideFaceColor?: ColorType;   customSideImage?: ImageEntity; }         export interface IBuildingOption extends IPrismOption {   floorHeight?: number;   floorColor?: ColorType;   floorSideTextureImage?: ImageEntity;   buildingFloorAnimateType?: FloorAnimateType;   isAnimation?: boolean;   buildingInfo?: BuildingInfo;   isRoundedCorner?: boolean;   roundedCornerRadius?: number; }         export interface IInfoWindow extends IOverlayOption {   position?: LatLng;   content?: ImageEntity;   anchorX?: number;   anchorY?: number;   isPerspective?: boolean;   rotate?: number;   yOffset?: number;   isFlat?: boolean; }         export interface ILocation {   latlng: LatLng;   direction: number; }         export interface IGetPoint {   mercator?: boolean; }         export type ISetCenter =   LatLng   | IGeoPoint;         export interface IViewportOption {   margins?: [ number, number, number, number ];   zoomFactor?: number;   isAnimate?: boolean;   animationTime?: number; }         export interface INinePatch {   scaleX: number[];   scaleY: number[];   fillArea: number[]; }         export interface INearOptions { left: number; top: number; nearlyRadius?: number; }         export interface IHandleBundle extends EventBundle { }         export interface IHandleOptions { model: symbol; target: IHandleBundle; }         export type TLayer = BaseLayer | CompassLayer | LocationLayer | OverlayLayer | Array<TileLayer>;         export type Nullable<T> = T | null | undefined;         export interface AnyObject { [propName: string | symbol]: any; }         export type AnyValue = number | string | Array<number | string> | boolean | undefined; export {}; 