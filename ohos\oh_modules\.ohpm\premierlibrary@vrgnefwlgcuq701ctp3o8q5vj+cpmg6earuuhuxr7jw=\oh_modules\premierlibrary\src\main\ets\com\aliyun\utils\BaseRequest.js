export class BaseRequest {
    constructor(c43) {
        this.wantStop = false;
        this.outerListener = null;
        this.outerListener = c43;
    }
    getSync() {
        this.runInBackground();
    }
    getAsync() {
        this.runInBackgroundAsync();
    }
    runInBackgroundAsync() {
        setTimeout(() => {
            this.runInBackground();
        }, 0);
    }
    stop() {
        this.wantStop = true;
        this.stopInner();
    }
    sendSuccessResult(z42, a43) {
        if (this.wantStop) {
            return;
        }
        if (this.outerListener) {
            this.outerListener.onSuccess(z42, a43);
        }
    }
    sendFailResult(w42, x42, y42) {
        if (this.wantStop) {
            return;
        }
        if (this.outerListener) {
            this.outerListener.onFail(w42, x42, y42);
        }
    }
    dealMsg(v42) {
        if (v42.id == BaseRequest.WHAT_SUCCESS) {
            this.outerListener?.onSuccess(v42.data, v42.extra);
        }
        else if (v42.id == BaseRequest.WHAT_FAIL) {
            this.outerListener?.onFail(v42.failCode, v42.data, v42.extra);
        }
    }
}
BaseRequest.WHAT_SUCCESS = 1;
BaseRequest.WHAT_FAIL = 0;
BaseRequest.DATA_KEY_EXTRA = "data_extra";
