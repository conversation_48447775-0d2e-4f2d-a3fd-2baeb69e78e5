import {
  Mqtt<PERSON><PERSON>,
  Mqtt<PERSON><PERSON>,
  MqttMessage,
  MqttQos,
  MqttResponse,
  MqttSubscribeOptions,
} from '@ohos/mqtt';
import emitter from '@ohos.events.emitter';
import { hilog } from '@kit.PerformanceAnalysisKit';
import { util } from '@kit.ArkTS';

// 使用枚举定义MQTT事件ID
enum MqttEvents {
  CONNECTED = 1001,
  DISCONNECTED = 1002,
  MESSAGE_RECEIVED = 1003,
  SUBSCRIBE_SUCCESS = 1004,
  ERROR = 1005
}

// 定义消息数据接口
interface MessageData {
  topic: string;
  payload: string; // 现在存储 base64 编码的二进制数据
  qos: number;
  isBase64Encoded: boolean; // 添加标识符属性
}

// MQTT配置接口
interface MQTTConfig {
  url: string;
  clientId: string;
  username: string;
  password: string;
  topics: string[];
}

export class MqttHarmonyClient {
  private static instance: MqttHarmonyClient;
  private mqttClient: MqttClient | null = null;
  private isConnected: boolean = false;
  private config: MQTTConfig | null = null;
  private readonly TAG = 'MqttHarmonyClient';

  private constructor() {}

  public static getInstance(): MqttHarmonyClient {
    if (!MqttHarmonyClient.instance) {
      MqttHarmonyClient.instance = new MqttHarmonyClient();
    }
    return MqttHarmonyClient.instance;
  }

  // 创建MQTT客户端
  public createMqttClient(config: MQTTConfig): void {
    hilog.info(0x0000, this.TAG, '正在创建MQTT客户端，配置信息: %{public}s', JSON.stringify(config));
    
    this.config = config;
    this.mqttClient = MqttAsync.createMqtt({
      url: config.url,
      clientId: config.clientId,
      persistenceType: 1 // 使用内存持久性
    });
    
    hilog.info(0x0000, this.TAG, 'MQTT客户端创建成功');
  }

  // 连接MQTT服务器
  public async connectMqtt(): Promise<boolean> {
    if (!this.mqttClient || !this.config) {
      hilog.error(0x0000, this.TAG, 'MQTT客户端未创建或配置信息缺失');
      this.emitError('MQTT客户端未初始化');
      return false;
    }

    hilog.info(0x0000, this.TAG, '正在连接MQTT服务器...');
    hilog.info(0x0000, this.TAG, '用户名: %{public}s', this.config.username);
    hilog.info(0x0000, this.TAG, '客户端ID: %{public}s', this.config.clientId);

    try {
      await this.mqttClient.connect({
        userName: this.config.username,
        password: this.config.password,
        connectTimeout: 30,
        automaticReconnect: true,
        MQTTVersion: 0, // MQTT 3.1.1
        keepAliveInterval: 60,
        cleanSession: true
      });

      this.isConnected = true;
      hilog.info(0x0000, this.TAG, 'MQTT服务器连接成功');
      this.emitConnected();
      
      // 连接成功后自动订阅主题
      await this.subscribeToTopics();
      
      return true;
    } catch (error) {
      hilog.error(0x0000, this.TAG, 'MQTT服务器连接失败: %{public}s', JSON.stringify(error));
      this.emitError(`连接失败: ${JSON.stringify(error)}`);
      return false;
    }
  }

  // 订阅主题
  private async subscribeToTopics(): Promise<void> {
    if (!this.mqttClient || !this.config || !this.isConnected) {
      hilog.error(0x0000, this.TAG, '无法订阅主题 - 客户端未就绪');
      return;
    }

    hilog.info(0x0000, this.TAG, '正在订阅主题: %{public}s', JSON.stringify(this.config.topics));

    for (const topic of this.config.topics) {
      try {
        const subscribeOption: MqttSubscribeOptions = {
          topic: topic,
          qos: 1 // QoS 1: 至少一次
        };

        await this.mqttClient.subscribe(subscribeOption);
        hilog.info(0x0000, this.TAG, '成功订阅主题: %{public}s', topic);
      } catch (error) {
        hilog.error(0x0000, this.TAG, '订阅主题失败: %{public}s, 错误: %{public}s', topic, JSON.stringify(error));
        this.emitError(`主题 ${topic} 订阅失败: ${JSON.stringify(error)}`);
      }
    }

    // 设置消息接收监听
    this.mqttClient.messageArrived((err: Error | null, data: MqttMessage) => {
      if (err) {
        hilog.error(0x0000, this.TAG, '消息接收错误: %{public}s', JSON.stringify(err));
        this.emitError(`消息错误: ${JSON.stringify(err)}`);
      } else {
        hilog.info(0x0000, this.TAG, '收到消息: %{public}s', JSON.stringify(data));
        this.emitMessageReceived(data);
      }
    });

    this.emitSubscribeSuccess();
  }

  // 断开连接
  public async disconnectMqtt(): Promise<void> {
    if (!this.mqttClient) {
      hilog.warn(0x0000, this.TAG, 'MQTT客户端未初始化');
      return;
    }

    hilog.info(0x0000, this.TAG, '正在断开MQTT服务器连接...');
    
    try {
      await this.mqttClient.destroy();
      this.isConnected = false;
      this.mqttClient = null;
      hilog.info(0x0000, this.TAG, 'MQTT服务器断开连接成功');
      this.emitDisconnected();
    } catch (error) {
      hilog.error(0x0000, this.TAG, '断开连接失败: %{public}s', JSON.stringify(error));
      this.emitError(`断开连接失败: ${JSON.stringify(error)}`);
    }
  }

  // 发送事件到Flutter端
  private emitConnected(): void {
    const innerEvent: emitter.InnerEvent = {
      eventId: MqttEvents.CONNECTED
    };
    const eventData: emitter.EventData = {
      data: { message: 'connected' }
    };
    emitter.emit(innerEvent, eventData);
  }

  private emitDisconnected(): void {
    const innerEvent: emitter.InnerEvent = {
      eventId: MqttEvents.DISCONNECTED
    };
    const eventData: emitter.EventData = {
      data: { message: 'disconnected' }
    };
    emitter.emit(innerEvent, eventData);
  }

  private emitMessageReceived(message: MqttMessage): void {
    try {
      let processedPayload = '';
      let isBase64Encoded = false;

      // 优先使用 payloadBinary 如果存在且是 ArrayBuffer
      if (message.payloadBinary && message.payloadBinary instanceof ArrayBuffer) {
        hilog.info(0x0000, this.TAG, 'Processing payloadBinary as ArrayBuffer');
        try {
          const uint8Array = new Uint8Array(message.payloadBinary);
          const bytes: number[] = Array.from(uint8Array);
          processedPayload = this.arrayToBase64(bytes);
          isBase64Encoded = true;
        } catch (error) {
          hilog.error(0x0000, this.TAG, 'Error processing payloadBinary: %{public}s', JSON.stringify(error));
          // 转换失败，尝试将原始 payload 作为字符串处理
          processedPayload = String(message.payload); 
          isBase64Encoded = false;
        }
      } else if (message.payload) {
        // 如果 payloadBinary 不可用或类型不符，回退到使用 payload
        hilog.info(0x0000, this.TAG, 'payloadBinary not available or not ArrayBuffer, using payload');
        if (typeof message.payload === 'string') {
          processedPayload = message.payload;
          isBase64Encoded = false; // 假设字符串 payload 不需要 base64 解码，除非特定场景
        } else {
          // 如果 payload 是 ArrayBuffer (理论上应该由 payloadBinary 处理，但作为备用)
          try {
            const uint8Array = new Uint8Array(message.payload);
            const bytes: number[] = Array.from(uint8Array);
            processedPayload = this.arrayToBase64(bytes);
            isBase64Encoded = true;
          } catch (error) {
            hilog.error(0x0000, this.TAG, 'Error processing payload as ArrayBuffer: %{public}s', JSON.stringify(error));
            processedPayload = String(message.payload);
            isBase64Encoded = false;
          }
        }
      } else {
        hilog.warn(0x0000, this.TAG, 'Received message with no payload or payloadBinary');
      }

      // 明确声明messageData的类型
      const messageData: MessageData = {
        topic: message.topic || '',
        payload: processedPayload,
        qos: message.qos || 0,
        isBase64Encoded: isBase64Encoded
      };

      hilog.info(0x0000, this.TAG, '发送消息到Flutter - Topic: %{public}s, Payload Length: %{public}d, isBase64Encoded: %{public}s',
        messageData.topic, messageData.payload.length, messageData.isBase64Encoded.toString());

      const innerEvent: emitter.InnerEvent = {
        eventId: MqttEvents.MESSAGE_RECEIVED
      };
      const eventData: emitter.EventData = {
        data: { messageData: JSON.stringify(messageData) }
      };
      emitter.emit(innerEvent, eventData);
    } catch (error) {
      hilog.error(0x0000, this.TAG, '处理消息失败: %{public}s', JSON.stringify(error));
      this.emitError(`消息处理失败: ${JSON.stringify(error)}`);
    }
  }

  // 使用鸿蒙系统的 util.Base64Helper 进行 base64 编码
  private arrayToBase64(bytes: number[]): string {
    try {
      // 将数字数组转换为 Uint8Array
      const uint8Array = new Uint8Array(bytes);
      // 使用鸿蒙系统的 Base64Helper 进行编码
      const base64Helper = new util.Base64Helper();
      return base64Helper.encodeToStringSync(uint8Array);
    } catch (error) {
      hilog.error(0x0000, this.TAG, 'Base64 编码失败: %{public}s', JSON.stringify(error));
      return '';
    }
  }

  private emitSubscribeSuccess(): void {
    const innerEvent: emitter.InnerEvent = {
      eventId: MqttEvents.SUBSCRIBE_SUCCESS
    };
    const eventData: emitter.EventData = {
      data: { message: 'subscribe_success' }
    };
    emitter.emit(innerEvent, eventData);
  }

  private emitError(error: string): void {
    const innerEvent: emitter.InnerEvent = {
      eventId: MqttEvents.ERROR
    };
    const eventData: emitter.EventData = {
      data: { error: error }
    };
    emitter.emit(innerEvent, eventData);
  }

  // 获取连接状态
  public getConnectionStatus(): boolean {
    return this.isConnected;
  }
}