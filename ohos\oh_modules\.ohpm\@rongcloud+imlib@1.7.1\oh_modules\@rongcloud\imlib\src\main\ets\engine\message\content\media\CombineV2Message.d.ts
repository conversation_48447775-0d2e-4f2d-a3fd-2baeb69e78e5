import List from '@ohos.util.List';
import { ConversationType } from '../../../MacroDefine';
import { CombineMsgInfo } from '../../model/CombineMsgInfo';
import { MediaMessageContent } from '../MediaMessageContent';
/**
 * 消息标识
 * @version 1.4.0
 */
declare const CombineV2MessageObjectName: string;
/**
 * 合并转发 V2 消息
 * ```
 * CombineV2Message encode() 之后长度 < 120KB，会被当做普通消息发送
 * >= 120 KB 会被当做媒体消息进行上传
 * ```
 * # 示例代码
 * ```
 *   private testSendCombineV2Message() {
 *      // 假如拿到了消息数组
 *      let msgArray: Array<Message> = new Array();
 *
 *      let msgNum = 0;
 *      let nameList: Array<string> = [];
 *      let summaryList: Array<string> = [];
 *      let msgList: Array<CombineMsgInfo> = [];
 *      for (let i = 0; i < msgArray.length; i++) {
 *         let msg = msgArray[i];
 *         if (this.canSendCombineV2(msg)) {
 *            let name = i + "名称"; // 请根据实际消息修改
 *            let summary = i + "摘要"; // 请根据实际消息修改
 *            let combineInfo = CombineMsgInfo.createWithMessage(msg);
 *            nameList.push(name);
 *            summaryList.push(summary);
 *            msgList.push(combineInfo);
 *         }
 *      }
 *
 *      let conId = new ConversationIdentifier();
 *      conId.conversationType = ConversationType.Private;
 *      conId.targetId = "testTargetId";
 *      conId.channelId = "testChannelId";
 *
 *      let combineV2Msg = new CombineV2Message();
 *      combineV2Msg.name = "测试：发送合并转发 V2 消息";
 *      combineV2Msg.msgNum = msgNum;
 *      combineV2Msg.conversationType = ConversationType.Private;
 *      combineV2Msg.msgList = msgList;
 *      combineV2Msg.nameList = nameList;
 *      combineV2Msg.summaryList = summaryList;
 *
 *      let option: ISendMsgOption = {};
 *
 *      let msg = new Message(conId, combineV2Msg);
 *
 *      IMEngine.getInstance().sendMediaMessage(msg, option,
 *         (msg: Message) => {
 *            // 消息入库
 *         },
 *         (msg: Message, progress: number) => {
 *         // 消息上传进度
 *         },
 *      ).then(result => {
 *            if (result.code == EngineError.Success) {
 *               // 发送合并转发 V2 消息成功
 *            } else {
 *               // 发送合并转发 V2 消息失败
 *            }
 *         });
 *      }
 *
 *   // 该消息是否被合并转发 V2 版本支持
 *   private canSendCombineV2(msg: Message | undefined): boolean {
 *      if (!ObjectChecker.isValid(msg)) {
 *         return false;
 *      }
 *      let objName = msg?.objectName;
 *      if (TextMessageObjectName == objName) {
 *         return true;
 *      }
 *      if (ImageMessageObjectName == objName) {
 *         return true;
 *      }
 *      if (SightMessageObjectName == objName) {
 *         return true;
 *      }
 *      if (HQVoiceMessageObjectName == objName) {
 *         return true;
 *      }
 *      if (FileMessageObjectName == objName) {
 *         return true;
 *      }
 *      if (CombineV2MessageObjectName == objName) {
 *         return true;
 *      }
 *      return false;
 *   }
 * ```
 * @version 1.4.0
 */
declare class CombineV2Message extends MediaMessageContent {
    /**
     * 媒体内容的文件名
     */
    name: string;
    /**
     * 媒体内容的本地路径
     */
    localPath: string;
    /**
     * 媒体内容上传服务器后的网络地址
     */
    remoteUrl: string;
    /**
     * 其他属性
     */
    extra: string;
    /**
     * 合并转发的消息列表 Key 。
     * 当被转发的消息长度之和大于等于 BUFFER_MAXSIZE 时才会有此字段，此时不会有 msgList 字段。
     */
    jsonMsgKey: string;
    /**
     * 被转发的消息条数
     */
    msgNum: number;
    /**
     * 会话类型
     */
    conversationType: ConversationType;
    /**
     * 转发的全部消息的发送者名称列表
     */
    nameList: Array<string>;
    /**
     * 合并消息的摘要列表
     */
    summaryList: Array<string>;
    /**
     * 消息列表。
     * 当被转发的消息长度之和小于等于 BUFFER_MAXSIZE 时才会有此字段，此时不会有 jsonMsgKey 字段。
     */
    msgList: Array<CombineMsgInfo>;
    /**
     * 无参构造方法
     */
    constructor();
    /**
     * 编码方法，将消息转为 json 字符串
     * @returns json 字符串
     */
    encode(): string;
    /**
     * 解码方法，将 json 字符串转为消息
     * @param contentString json 字符串
     */
    decode(contentString: string): void;
    private getJsonFromMsgList;
    private parseJsonToMsgList;
    /**
     * 获取类名
     * @returns 类名
     */
    getClassName(): string;
    /**
     * 实现搜索方法
     * @returns 需要搜索的内容
     */
    getSearchableWord(): List<string> | null;
}
export { CombineV2Message, CombineV2MessageObjectName };
