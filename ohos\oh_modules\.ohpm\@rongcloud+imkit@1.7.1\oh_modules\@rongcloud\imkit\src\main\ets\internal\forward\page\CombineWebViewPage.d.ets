// @keepTs
// @ts-nocheck
export declare class CombineJSObject {
    private jsLinkCallback?;
    constructor();
    /**
     *  Android 已有的对接 JS 接口本地方法。
     *  注意：这里复用Android 的JS 接口，没有新增JS接口。是为了兼容老版本，同时其他端不需要更新合并转发模版json配置。
     */
    sendInfoToAndroid(x303: string): Promise<void>;
    /**
     * JS回调的的Callback接口
     * @param jsLinkCallback js接口的链接跳转的Callback
     */
    setJSCallback(w303: (link: string) => void): void;
    /**
     * 处理 JS 透传数据
     * @param data js 透传数据
     */
    private processCombineJS;
    private getJSInterfaceJSON;
    /**
     * 打开链接。
     * json字段格式：
     * ```
     *  link = "超链接"
     *  type = "link"
     * ```
     */
    private openLink;
}
