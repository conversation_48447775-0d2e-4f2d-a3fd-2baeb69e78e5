export var InfoCode;
(function (t20) {
    t20[t20["Unknown"] = -1] = "Unknown";
    t20[t20["LoopingStart"] = 0] = "LoopingStart";
    t20[t20["BufferedPosition"] = 1] = "BufferedPosition";
    t20[t20["CurrentPosition"] = 2] = "CurrentPosition";
    t20[t20["AutoPlayStart"] = 3] = "AutoPlayStart";
    t20[t20["CurrentDownloadSpeed"] = 4] = "CurrentDownloadSpeed";
    t20[t20["UtcTime"] = 5] = "UtcTime";
    t20[t20["LocalCacheLoaded"] = 6] = "LocalCacheLoaded";
    t20[t20["SwitchToSoftwareVideoDecoder"] = 100] = "SwitchToSoftwareVideoDecoder";
    t20[t20["AudioCodecNotSupport"] = 101] = "AudioCodecNotSupport";
    t20[t20["AudioDecoderDeviceError"] = 102] = "AudioDecoderDeviceError";
    t20[t20["VideoCodecNotSupport"] = 103] = "VideoCodecNotSupport";
    t20[t20["VideoDecoderDeviceError"] = 104] = "VideoDecoderDeviceError";
    t20[t20["VideoRenderInitError"] = 105] = "VideoRenderInitError";
    t20[t20["DemuxerTraceID"] = 106] = "DemuxerTraceID";
    t20[t20["NetworkRetry"] = 108] = "NetworkRetry";
    t20[t20["CacheSuccess"] = 109] = "CacheSuccess";
    t20[t20["CacheError"] = 110] = "CacheError";
    t20[t20["LowMemory"] = 111] = "LowMemory";
    t20[t20["NetworkRetrySuccess"] = 113] = "NetworkRetrySuccess";
    t20[t20["SubtitleSelectError"] = 114] = "SubtitleSelectError";
    t20[t20["DirectComponentMSG"] = 116] = "DirectComponentMSG";
    t20[t20["DemuxerSegmentOpenFailed"] = 119] = "DemuxerSegmentOpenFailed";
    t20[t20["RTSServerMaybeDisconnect"] = 805371905] = "RTSServerMaybeDisconnect";
    t20[t20["RTSServerRecover"] = 805371906] = "RTSServerRecover";
})(InfoCode || (InfoCode = {}));
export function getEnumValues(o20) {
    return Object.keys(o20)
        .filter(s20 => !isNaN(Number(o20[s20])))
        .map(r20 => o20[r20]);
}
export function findInfoCodeByValue(l20) {
    const m20 = getEnumValues(InfoCode);
    for (const n20 of m20) {
        if (n20 === l20) {
            return n20;
        }
    }
    return InfoCode.Unknown;
}
