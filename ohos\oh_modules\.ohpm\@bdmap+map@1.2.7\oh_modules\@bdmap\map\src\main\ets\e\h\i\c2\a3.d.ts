        import BmObject from "./u2"; import { BmVisibility } from "./d2"; import type Overlay from "../m/m"; export default abstract class BmDrawItem extends BmObject { private static readonly MAP_LEVEL_MAX; private static readonly MAP_LEVEL_MIN; protected mVisibility: BmVisibility; protected mOpacity: number; protected mDescription: string; private mStartLevel; private mEndLevel; zIndex: number; holeClickedIndex: number; owner: Overlay; constructor(x10: number, y10: number); setOwner(owner: Overlay): void; getHoleClickedIndex(): number; setHoleClickedIndex(w10: number): void;           setVisibility(visibility: BmVisibility): void; getZIndex(): number; setZIndex(zIndex: number): void;           setPBVisibility(v10: number): void;             setShowLevel(from: number, to: number): void;           setStartLevel(from: number): void;           setEndLevel(to: number): void;           setOpacity(opacity: number): void; setClickable(u10: boolean): void; setHoleClickable(t10: boolean): void;           setDescription(description: string): void; getDescription(): string; } 