import { BoxRect, IconStyleObj, PanelStyleObj, TitleStyleObj, mapBoxRect } from "../../h/g1/f2";  @Component export default struct Infowindow {  @Prop uuid: string;  @Prop icon: string;  @Prop title: string;  @Prop type: number;  @ObjectLink iconStyle: IconStyleObj;  @ObjectLink titleStyle: TitleStyleObj;  @ObjectLink panelStyle: PanelStyleObj;  @ObjectLink mapBox: BoxRect;  @State iconStyleObj: IconStyleObj;  @State titleStyleObj: TitleStyleObj;  @State panelStyleObj: PanelStyleObj; isIcon: boolean; isTitle: boolean; isTag: boolean; bound: mapBoxRect; aboutToAppear(): void; isShow(): void; formatData(): void; formatIconStyle(c59: IconStyleObj, d59: IconStyleObj): void; formatTitleStyle(a59: TitleStyleObj, b59: TitleStyleObj): void; formatPanelStyle(y58: PanelStyleObj, z58: PanelStyleObj): void; onLayoutReady(): void; panelClick(): void; build(): void; } 