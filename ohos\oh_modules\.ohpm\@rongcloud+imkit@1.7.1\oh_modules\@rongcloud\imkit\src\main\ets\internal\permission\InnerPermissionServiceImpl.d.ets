// @keepTs
// @ts-nocheck
import { RequestPermissionInterceptor } from './listener/RequestPermissionInterceptor';
import { PermissionService } from './PermissionService';
export declare class InnerPermissionServiceImpl implements PermissionService {
    setRequestPermissionInterceptor(v326: RequestPermissionInterceptor): void;
    removeRequestPermissionInterceptor(u326: RequestPermissionInterceptor): void;
}
