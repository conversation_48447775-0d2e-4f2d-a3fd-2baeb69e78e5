import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wuling_flutter_app/api/car_api.dart';
import 'package:wuling_flutter_app/api/common_api.dart';
import 'package:wuling_flutter_app/models/car/car_charge_reservation_data_model.dart';
import 'package:wuling_flutter_app/models/car/car_control_response_model.dart';
import 'package:wuling_flutter_app/models/car/ev_charging_model.dart';
import 'package:wuling_flutter_app/models/car/car_status_model.dart';
import 'package:wuling_flutter_app/models/car/car_status_response_model.dart';
import 'package:wuling_flutter_app/page/base_page/base_page.dart';
import 'package:wuling_flutter_app/models/car/car_info_model.dart';
import 'package:wuling_flutter_app/models/global_data.dart';
import 'package:wuling_flutter_app/utils/http/api_exception.dart';
import 'package:wuling_flutter_app/utils/date_time_util.dart';
import 'package:wuling_flutter_app/utils/manager/loading_manager.dart';
import 'package:wuling_flutter_app/utils/manager/time_picker_manager.dart';
import 'package:wuling_flutter_app/utils/manager/dialog_manager.dart';
import 'package:wuling_flutter_app/widgets/common/custom_refresher.dart';
import 'package:wuling_flutter_app/widgets/common/custom_time_picker.dart';
import 'package:wuling_flutter_app/widgets/common/custom_dialog.dart';
import 'package:wuling_flutter_app/widgets/common/image_view.dart';
import 'package:wuling_flutter_app/widgets/use_car_page_widgets/charge_reservation_page_widgets.dart';

import '../../utils/manager/log_manager.dart';
class ChargeReservationPage extends BasePage {
  final bool isOldTypeCar;
  ChargeReservationPage({
    Key? key,
    bool hideAppBar = false,
    bool isWithinSafeArea = true,
    String appBarTitle = '预约充电',
    Color appBarColor = Colors.transparent,
    Color pageBackgroundColor = const Color(0xFFF7F9FB),
    this.isOldTypeCar = false,
  }) : super(
    key: key,
    hideAppBar: hideAppBar,
    isWithinSafeArea: isWithinSafeArea,
    appBarTitle: appBarTitle,
    appBarColor: appBarColor,
    pageBackgroundColor: pageBackgroundColor,
  );

  @override
  _ChargeReservationPageState createState() => _ChargeReservationPageState();
}

class _ChargeReservationPageState extends BasePageState<ChargeReservationPage> {
  final RefreshController _refreshController = RefreshController(initialRefresh: false);
  late CarInfoModel? _carInfoModel;
  String _currentReservationTimeStr = '';
  EvChargingModel? _chargingModel;
  CarStatusModel? _carStatusModel;
  CarChargeReservationDataModel? _reservationDataModel;
  bool isReservationSet(){
    if(widget.isOldTypeCar) {
      if (_chargingModel?.reserveDate != null) {
        return true;
      }
      return false;
    }else{
      String startTime = _reservationDataModel?.startTime ?? '';
      return startTime.isNotEmpty;
    }
  }

  String getReservationTimeStr(EvChargingModel? chargingModel) {
    if(chargingModel == null || chargingModel?.reserveDate == null){
      return '';
    }
    int time = chargingModel!.reserveDate!;
    DateFormat dateFormatter = DateFormat('yyyy-MM-dd HH:mm');
    DateTime date = DateTime.fromMillisecondsSinceEpoch(time);
    String timeStr = dateFormatter.format(date);
    return timeStr;
  }

  Duration getDurationFromEpochAfterNMinutes(int n) {
    // 获取当前时间
    DateTime now = DateTime.now();
    // 计算n分钟后的时间
    DateTime futureTime = now.add(Duration(minutes: n));
    // 计算与1970-01-01 00:00:00的时间差
    DateTime epoch = DateTime(1970, 1, 1, 0, 0, 0);
    // 返回时间差的Duration
    return futureTime.difference(epoch);
  }

  //将 yyyy-MM-dd HH:mm 格式的日期字符串转为时间戳（秒级）
  double? dateTimeStringToTimestamp(String dateTimeString) {
    try {
      // 定义日期格式
      DateFormat dateFormat = DateFormat('yyyy-MM-dd HH:mm');
      // 解析日期字符串
      DateTime dateTime = dateFormat.parse(dateTimeString);
      // 返回时间戳（毫秒级）
      return dateTime.millisecondsSinceEpoch / 1000;
    } catch (e) {
      LogManager().debug('日期字符串解析失败: $e');
      return null; // 返回 null 表示解析失败
    }
  }

  // 输入 yyyy-MM-dd HH:mm 格式的日期字符串，比较这个时间是否比当前的时间早
  bool isEarlierThanNow(String dateTimeString) {
    try {
      // 定义日期格式
      DateFormat dateFormat = DateFormat('yyyy-MM-dd HH:mm');
      // 解析日期字符串
      DateTime inputDateTime = dateFormat.parse(dateTimeString);
      // 获取当前时间
      DateTime now = DateTime.now();
      // 比较输入时间是否早于当前时间
      return inputDateTime.isBefore(now);
    } catch (e) {
      LogManager().debug('日期字符串解析失败: $e');
      return false; // 返回 false 表示解析失败或异常情况
    }
  }


  void showReservationTimePicker(){
    TimePickerManager.showCustomTimePicker(
        context: context,
        mode: TimePickerMode.YMDHM,
        minDuration: getDurationFromEpochAfterNMinutes(15),
        leftTitle: '选择预约时间',
        onTimeSelected: (Duration selectedTime, String formattedTime) {
          setState(() {
            _currentReservationTimeStr = formattedTime;
          });
        }
    );
  }

  void _getEvChargingModel()async {
    String vin = _carInfoModel?.vin ?? "";
    if (vin.isNotEmpty) {
      try {
        LoadingManager.show();
        CarStatusResponseModel carStatusResponseModel = await carAPI.getDefaultCarInfoAndStatus();
        CarStatusModel? carStatusModel = carStatusResponseModel.carStatus;
        EvChargingModel chargingModel = await commonAPI.downloadEVChargingInfo(
            vin);
        LoadingManager.dismiss();
        setState(() {
          _carStatusModel = carStatusModel;
          _chargingModel = chargingModel;
          _currentReservationTimeStr = getReservationTimeStr(_chargingModel);
        });
        if (_refreshController.isRefresh) {
          _refreshController.refreshCompleted();
        }
      } catch (e) {
        if(e is APIException){
          LoadingManager.showError('请求数据失败\n ${e.message}');
        }
        if (_refreshController.isRefresh) {
          _refreshController.refreshCompleted();
        }
      }
    }
  }

  void _getUnifiedChargeReservationModel()async {
    String vin = _carInfoModel?.vin ?? "";
    if (vin.isNotEmpty) {
      try {
        LoadingManager.show();
        CarStatusResponseModel carStatusResponseModel = await carAPI.getDefaultCarInfoAndStatus();
        CarStatusModel? carStatusModel = carStatusResponseModel.carStatus;
        CarChargeReservationDataModel? reservationDataModel = await carAPI.downloadChargeReservationInfoDataWithVin(vin);
        DateTime? dateTime = DateTimeUtil.dateFromString(reservationDataModel?.startTime ?? '');
        String reservationTimeStr = '';
        if(dateTime != null){
          reservationTimeStr = DateTimeUtil.stringFromDate(dateTime, format: 'yyyy-MM-dd HH:mm');
        }
        LoadingManager.dismiss();
        setState(() {
          _carStatusModel = carStatusModel;
          _reservationDataModel = reservationDataModel;
          _currentReservationTimeStr = reservationTimeStr;
        });
        if (_refreshController.isRefresh) {
          _refreshController.refreshCompleted();
        }
      } catch (e) {
        if(e is APIException){
          LoadingManager.showError('请求数据失败\n ${e.message}');
        }
        if (_refreshController.isRefresh) {
          _refreshController.refreshCompleted();
        }
      }
    }
  }


  void doSetChargeReservationNetWork() async{
    if(_currentReservationTimeStr.isEmpty){
      showReservationTimePicker();
      return;
    }
    if(isEarlierThanNow(_currentReservationTimeStr)){
      LoadingManager.showToast('不能早于当前时间哦');
      return;
    }
    String vin = _carInfoModel?.vin ?? "";
    if (vin.isNotEmpty) {
      if(widget.isOldTypeCar) {
        try {
          LoadingManager.show(status: '预约设置中...');
          double time = dateTimeStringToTimestamp(_currentReservationTimeStr) ??
              0;
          CarControlResponseModel responseModel = await commonAPI
              .doOrderChargingNetWork(vin, time);
          LoadingManager.showSuccess('预约成功');
          _refreshController.requestRefresh();
        } catch (e) {
          if (e is APIException) {
            LoadingManager.showError('预约设置失败：${e.message}');
          }
        }
      }else{
        DateTime? startTime = DateTimeUtil.dateFromString(_currentReservationTimeStr, format: 'yyyy-MM-dd HH:mm');
        String startTimeStr = DateTimeUtil.stringFromDate(startTime);
        DateTime? endTime = DateTimeUtil.dateByAddingYears(startTime, 10);
        String endTimeStr = DateTimeUtil.stringFromDate(endTime);
        if(startTimeStr.isNotEmpty && endTimeStr.isNotEmpty) {
          try {
            LoadingManager.show(status: '预约设置中...');
            CarControlResponseModel responseModel = await carAPI
                .doChargeReservationNetWork(1, vin, startTimeStr, endTimeStr);
            LoadingManager.showSuccess('预约成功');
            _refreshController.requestRefresh();
          } catch (e) {
            if (e is APIException) {
              LoadingManager.showError('预约设置失败：${e.message}');
            }
          }
        }else{
          LoadingManager.showToast('时间有误');
        }
      }
    }
  }

  void doCancelChargeReservationNetWork() async{
    String vin = _carInfoModel?.vin ?? "";
    if (vin.isNotEmpty)
      if(widget.isOldTypeCar) {
        try {
          LoadingManager.show(status: '取消预约中...');
          double time = dateTimeStringToTimestamp(_currentReservationTimeStr) ??
              0;
          CarControlResponseModel responseModel = await commonAPI
              .doCancelOrderChargingNetWork(vin);
          LoadingManager.showSuccess('取消预约成功');
          _refreshController.requestRefresh();
        } catch (e) {
          if (e is APIException) {
            LoadingManager.showError('取消预约失败：${e.message}');
          }
        }
      }else{
        DateTime? startTime = DateTimeUtil.dateWithHoursBeforeNow(2);
        String startTimeStr = DateTimeUtil.stringFromDate(startTime);
        DateTime? endTime = DateTimeUtil.dateWithHoursBeforeNow(1);
        String endTimeStr = DateTimeUtil.stringFromDate(endTime);
        try {
          LoadingManager.show(status: '取消预约中...');
          CarControlResponseModel responseModel = await carAPI
              .doChargeReservationNetWork(0, vin, startTimeStr, endTimeStr);
          LoadingManager.showSuccess('取消预约成功');
          _refreshController.requestRefresh();
        } catch (e) {
          if (e is APIException) {
            LoadingManager.showError('取消预约失败：${e.message}');
          }
        }
    }
  }


  void showCancelReservationAlert(){
    DialogManager().showCustomDialog(
      CustomDialog(
        content: "您确定要取消预约吗？",
        contentFontSize: 18,
        contentColor: Colors.black,
        buttonHeight: 70,
        horizontalPadding: 20,
        buttons: [
          DialogButton(
              label: "取消",
              onPressed: () {},
              backgroundColor: Colors.white,
              textColor: const Color(0xFF9B9DA9)),
          DialogButton(
              label: "确定",
              onPressed: () {
                doCancelChargeReservationNetWork();
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xffea0029)),
        ],
      ),
    );
  }

  List<Widget> getWidgetList(){
    List<Widget> list = [];
    list.add(CarInfoHeader(
      imageUrl: _carInfoModel?.image ?? '',
      statusModel: _carStatusModel,
    ));
    list.add(ChargeInfoWidget(
      isOldType: widget.isOldTypeCar,
      chargingModel: _chargingModel,
      statusModel: _carStatusModel,
      reservationTimeStr: _currentReservationTimeStr,
      onTimePickerButtonClicked: (){
        showReservationTimePicker();
      },
    ));
    list.add(Padding(
      padding: EdgeInsets.all(20),
      child: Text(
        '预约充电功能仅限于使用上汽通用五菱公司的交流充电枪和交流充电桩，使用接地宝、公共运营桩或非本公司产品充电，该功能可能无法使用\n\n预约充电前，车辆要处于充电状态；达到预约时间后，会在10分钟内唤醒车辆进行充电。',
        style: TextStyle(
          fontSize: 14,
          color: Color(0xff868990),
        ),
      ),
    ));
    list.add(Center(
      child: GestureDetector(
        onTap: (){
          if(isReservationSet()){
            showCancelReservationAlert();
          }else{
            doSetChargeReservationNetWork();
          }
        },
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Container(
            height: 50,
            width: 256,
            color: Color(0xffEA0029),
            child: Center(
              child: Text(
                isReservationSet() ? '取消预约充电' : '开启预约充电',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Colors.white
                ),
              ),
            ),
          ),
        ),
      ),
    ));
    return list;
  }

  void onRefresh() async{
    if(widget.isOldTypeCar) {
      _getEvChargingModel();
    }else{
      _getUnifiedChargeReservationModel();
    }
  }

  @override
  void pageInitState() {
    _carInfoModel = GlobalData().carInfoModel;
    // 在此处添加页面初始化逻辑
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _refreshController.requestRefresh();
    });
  }

  @override
  Widget buildPageContent(BuildContext context) {
    List<Widget> widgetList = getWidgetList();
    return Stack(
      children: [
        // 最底层背景图片
        Positioned.fill(
          child: ImageView(
            'assets/images/use_car_page/charge_reservation_page/car_grant_bg.png', // 替换为实际图片URL或使用本地图片
            fit: BoxFit.cover, // 让图片覆盖整个Stack
          ),
        ),
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          child: CustomSmartRefresher(
            controller: _refreshController,
            onRefresh: onRefresh,
            child: ListView.builder(
              itemCount: widgetList.length,
              itemBuilder: (BuildContext context, int index) {
                return widgetList[index];
              },
            ),
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    // 在此处添加页面销毁逻辑
    _refreshController?.dispose();
    super.dispose();
  }
}