import { LanguageType } from "../../../base/x2";
import { PoiFilter } from "../p1";
/**
 * poi城市内检索参数
 */
export declare class PoiCitySearchOption {
    /**
     * 检索行政区划区域。可输入行政区划名或对应cityCode
     * 必须参数
     * 如需严格限制召回数据在区域内，请设置isCityLimit参数为true
     */
    private _city;
    set city(value: string);
    get city(): string;
    /**
     * 检索关键字，必须参数
     */
    private _keyword;
    set keyword(value: string);
    get keyword(): string;
    /**
     * 分页页码
     * 默认为0,0代表第一页，1代表第二页，以此类推
     */
    private _pageNum;
    set pageNum(value: number);
    get pageNum(): number;
    /**
     * 单页展示POI数量，默认为10条记录，最大返回20条。
     * 多关键字检索时，返回的记录数为关键字个数*pageNum
     */
    private _pageCapacity;
    set pageCapacity(value: number);
    get pageCapacity(): number;
    /**
     * 是否返回地址信息
     */
    private _isReturnAddr;
    set isReturnAddr(value: boolean);
    get isReturnAddr(): boolean;
    /**
     * 检索分类，
     * 多个分类以","分割
     */
    private _tag;
    set tag(value: string);
    get tag(): string;
    /**
     * 检索结果详细程度。
     * 取值为 或空，则返回基本信息；取值为2，返回检索POI详细信息
     */
    private _scope;
    set scope(value: number);
    get scope(): number;
    /**
     * 区域数据召回限制
     * 为true时，仅返回city对应区域内数据
     */
    private _isCityLimit;
    set isCityLimit(value: boolean);
    get isCityLimit(): boolean;
    /**
     * 检索过滤条件。
     * 当scope取值为2时，可以设置filter进行排序
     */
    private _poiFilter;
    set poiFilter(value: PoiFilter);
    get poiFilter(): PoiFilter | null;
    /**
     * 是召回行政区域编码
     */
    private _isExtendAdcode;
    set isExtendAdcode(value: boolean);
    get isExtendAdcode(): boolean;
    /**
     * 检索语言类型
     * 默认中文
     */
    private _languageType;
    set languageType(value: LanguageType);
    get languageType(): LanguageType;
    constructor(params: PoiCitySearchOptionParams);
}
export interface PoiCitySearchOptionParams {
    /**
     * 检索行政区划区域。可输入行政区划名或对应cityCode
     * 必须参数
     * 如需严格限制召回数据在区域内，请设置isCityLimit参数为true
     */
    city: string;
    /**
     * 检索关键字，必须参数
     */
    keyword: string;
    /**
     * 分页页码
     * 默认为0,0代表第一页，1代表第二页，以此类推
     */
    pageNum?: number;
    /**
     * 单页展示POI数量，默认为10条记录，最大返回20条。
     * 多关键字检索时，返回的记录数为关键字个数*pageNum
     */
    pageCapacity?: number;
    /**
     * 是否返回地址信息
     */
    isReturnAddr?: boolean;
    /**
     * 检索分类，
     * 多个分类以","分割
     */
    tag?: string;
    /**
     * 检索结果详细程度。
     * 取值为 或空，则返回基本信息；取值为2，返回检索POI详细信息
     */
    scope?: number;
    /**
     * 区域数据召回限制
     * 为true时，仅返回city对应区域内数据
     */
    isCityLimit?: boolean;
    /**
     * 检索过滤条件。
     * 当scope取值为2时，可以设置filter进行排序
     */
    poiFilter?: PoiFilter;
    /**
     * 是召回行政区域编码
     */
    isExtendAdcode?: boolean;
    /**
     * 检索语言类型
     * 默认中文
     */
    languageType?: LanguageType;
}
