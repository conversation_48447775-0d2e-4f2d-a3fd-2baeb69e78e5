// @keepTs
// @ts-nocheck
/**
 * Created on 2025/03/24
 * <AUTHOR>
 */
import { Context } from '@kit.AbilityKit';
import { ConversationIdentifier } from '@rongcloud/imlib';
import { IBoardPlugin } from '../inputbar/component/plugin/IBoardPlugin';
import { List } from '@kit.ArkTS';
/**
 * Kit扩展模块接口协议，用于 SDK 同步子模块onInit、子模块提供 + 号菜单给SDK等。
 *
 * @version 1.4.3
 */
export interface IExtensionModule {
    /**
     * SDK 初始化。 用户可以在该方法中注册自定义消息、注册消息模板、初始化自己的模块。
     *
     * @param context 上下文
     * @param appKey 应用唯一 key。
     */
    onInit(context: Context, appKey: string): void;
    /**
     * 用户可以根据不同的会话，配置 “+” 号区域插件。 可以配置一个插件，也可以同时配置多个插件。extension 展示所有返回的插件列表。
     * 注意：如果用户没有配置插件，此方法可以不用实现。
     *
     * @param convId 会话标识。
     * @return 插件列表。
     */
    getPluginModules(convId: ConversationIdentifier): List<IBoardPlugin>;
    /**
     * 返回模块名
     * @returns 模块名
     */
    getModuleName(): string;
}
