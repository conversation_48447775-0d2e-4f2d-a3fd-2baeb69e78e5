import BmDrawItem from "./a3"; import { BmGeoElement } from "./i3"; import BmSurfaceStyle from "./r3"; export default class BmPolygon extends BmDrawItem { private style; constructor();           addGeoElement(l12: BmGeoElement): any;           addHoleGeoElement(k12: BmGeoElement): any;         clearGeoElements(): any;           setThin(j12: number): any;           setThinFactor(factor: number): any;           setJointType(jointType: number): any;           setSurfaceStyle(style: BmSurfaceStyle): void;           setDrawFullscreenMaskFlag(flag: boolean): any; } 