// @keepTs
// @ts-nocheck
import { Context } from '@kit.AbilityKit';
/**
 * 对文件的基本操作
 */
export declare class FileUtil {
    /**
     * 获取应用沙箱下的通用文件路径
     * @param context 上下文
     * @returns /data/storage/el2/base/{packageName}/files/
     */
    static getFilePath(x108: Context): string;
    /**
     * 本地文件是否存在
     */
    static isFile(v108: string): boolean;
    /**
     * 创建文件夹，创建路径上的所有文件夹
     */
    static createFolder(t108: string): void;
    /**
     * 获取应用沙箱下的缓存文件路径
     * 使用此路径存储的数据当用户清理缓存时就会被清空
     * @param context 上下文
     * @returns /data/storage/el2/base/{packageName}/cache/
     */
    static getCachePath(r108: Context): string;
    /**
     * 获取应用沙箱下的临时文件路径
     * 使用此路径存储的数据退出应用后就会被清空
     * @param context 上下文
     * @returns /data/storage/el2/base/{packageName}/temp/
     */
    static getTempPath(p108: Context): string;
    /**
     * 获取应用沙箱下的文件的下载路径
     * 使用此路径存储的数据退出应用后就会被清空
     * @param context 上下文
     */
    static getDownloadFileUrlCachePath(o108: Context): string;
    /**
     * 获取应用沙箱下的文件的下载名字
     * 使用此路径存储的数据退出应用后就会被清空
     * @param context 上下文
     */
    static getDownloadFileName(h108: string, i108: string): Promise<string>;
    /**
     * rust底层下载会做一次文件名正则替换，方便上层判断是否已下载，所以这里也要做正则之后传给底层。
     */
    static getFixedFileName(e108: string): string;
    /**
     * 获取本地文件路径
     */
    static getFixedFilePath(b108: string, c108: boolean): string;
    /**
     * 获取文件的base64
     */
    static getFileBase64(u107: string): string;
    /**
     * 获取保存缩略图base64的临时目录
     * @param context 上下文
     */
    static getThumbnailBase64TempPath(t107: Context): string;
    /**
     * 保存缩略图base64到临时目录
     * @param path
     */
    static saveThumbnailBase64(l107: Context, m107: string): string;
    static getMediaUrlName(h107: string): string;
    static getMediaUrlSuffix(c107: string): string;
    static getSuffix(z106: string): string;
    /**
     * 获取完整的Base64
     */
    static getImageBase64(x106: string): string;
    /**
     * 获取文件的沙箱路径。
     *
     * 注意：该路径不能存在临时文件夹下，冷启动会删除，会造成消息重发有问题。
     */
    static getFileSandBoxPath(q106: Context, r106: string): Promise<string>;
    /**
     * 拷贝文件到沙箱
     */
    static copyFileToSandbox(m106: Context, n106: string): Promise<string>;
    /**
     * 拷贝文件到沙箱
     */
    static copyFilesToSandbox(g106: Context, h106: string[]): Promise<string[]>;
    /**
     * 拷贝文件到沙箱
     */
    static copyCombineFileToSandbox(a106: Context): Promise<void>;
}
