import 'package:wuling_flutter_app/models/user/user_model.dart';
import 'package:wuling_flutter_app/utils/http/api_exception.dart';
import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/utils/manager/login_manager.dart';
import 'package:wuling_flutter_app/models/global_data.dart';

import '../../utils/manager/log_manager.dart';


class LoginTestPage extends StatefulWidget {
  @override
  _LoginTestPageState createState() => _LoginTestPageState();
}

class _LoginTestPageState extends State<LoginTestPage> {
  final TextEditingController phoneController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();

  String responseText = '';
  bool isLogin = false;

  @override
  void initState() {
    super.initState();
    setState(() {
      isLogin = GlobalData().isLogin;
      responseText = isLogin ? 
      '已登录：\n用户昵称：${GlobalData().userModel?.nickname}\n用户ID：${GlobalData().userModel?.userIdStr}\n手机号码：${GlobalData().userModel?.mobile}' : 
      '未登录';
    });
  }

  void login(BuildContext context) async {
    String phone = phoneController.text;
    String password = passwordController.text;

    if (phone.isEmpty || password.isEmpty) {
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            title: Text('错误'),
            content: Text('手机号码和密码不能为空'),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Text(
                  '确定',
                  style: TextStyle(color: Colors.black),
                ),
              ),
            ],
          );
        },
      );
    } else {
      try {
        await LoginManager().oauthLoginWithPassword(mobile: phone, password: password);
        //获取个人信息
        UserModel userModel = await LoginManager().getSelfInfo();
        setState(() {
          responseText = '登录成功：\n用户昵称：${userModel.nickname}\n用户ID：${userModel.userIdStr}\n手机号码：${userModel.mobile}';
          isLogin = true;
        });
      } catch (e) {
        LogManager().debug('$e');
        setState(() {
          if (e is APIException){
            responseText = '登录失败：${e.userMessage}\n报错信息:${e}\nerrCode:${e.errorCode}';
          }
        });
      }
    }
  }

  void logout(BuildContext context) async {
    bool success = await LoginManager().oauthLogout();
    if (success) {
      setState(() {
        responseText = '未登录';
        isLogin = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('登录测试'),
      ),
      body: Padding(
        padding: EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            TextField(
              controller: phoneController,
              decoration: InputDecoration(
                labelText: '手机号码',
              ),
            ),
            SizedBox(height: 16.0),
            TextField(
              controller: passwordController,
              obscureText: true,
              decoration: InputDecoration(
                labelText: '密码',
              ),
            ),
            SizedBox(height: 16.0),
            ElevatedButton(
              onPressed: () => login(context),
              child: Text('登录'),
            ),
            if (isLogin)
              Column(
                children: [
                  SizedBox(height: 16.0),
                  ElevatedButton(
                    onPressed: () => logout(context),
                    child: Text('注销'),
                  ),
                ],
              ),
            Text(responseText)
          ],
        ),
      ),
    );
  }
}