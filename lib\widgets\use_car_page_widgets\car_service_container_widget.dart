import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/widgets/common/image_view.dart';
import 'package:wuling_flutter_app/models/user/user_handle_model.dart';

class CarServiceContainerWidget extends StatefulWidget {
  final List<UserHandleModel> serviceList;
  final void Function({required UserHandleModel userHandleModel})?
      onServiceButtonClicked;
  final VoidCallback? onEditButtonClicked;

  const CarServiceContainerWidget({
    Key? key,
    required this.serviceList,
    this.onServiceButtonClicked,
    this.onEditButtonClicked,
  }) : super(key: key);

  @override
  CarServiceContainerWidgetState createState() =>
      CarServiceContainerWidgetState();
}

class CarServiceContainerWidgetState extends State<CarServiceContainerWidget> {
  @override
  Widget build(BuildContext context) {
    // 限制显示最多5个服务
    List<UserHandleModel> displayServices = widget.serviceList.take(5).toList();

    return Padding(
      padding: const EdgeInsets.only(top: 12, left: 20, right: 20),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: const [
            BoxShadow(
              color: Color(0x0a000000),
              spreadRadius: 0,
              blurRadius: 4,
              offset: Offset(0, 0),
            ),
          ],
        ),
        child: Column(
          children: [
            // 标题栏
            Padding(
              padding: const EdgeInsets.only(
                  left: 16, right: 16, top: 16, bottom: 16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    '用车服务',
                    style: TextStyle(
                      fontSize: 16,
                      color: Color(0xff383A40),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Visibility(
                    visible: false, // 隐藏编辑按钮
                    child: GestureDetector(
                      onTap: widget.onEditButtonClicked,
                      child: const Text(
                        '编辑',
                        style: TextStyle(
                          fontSize: 14,
                          color: Color(0xff666666),
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 顶部分隔线
            Container(
              height: 0.5,
              margin: const EdgeInsets.only(
                  left: 16, right: 16, top: 0, bottom: 12),
              color: const Color(0xffE5E5E5),
            ),

            // 服务按钮网格
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              padding: const EdgeInsets.only(
                  left: 16, right: 16, top: 0, bottom: 16),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 5, // 5列
                childAspectRatio: 1.0, // 正方形
                crossAxisSpacing: 8,
                mainAxisSpacing: 8,
              ),
              itemCount: displayServices.length,
              itemBuilder: (context, index) {
                final service = displayServices[index];
                return GestureDetector(
                  onTap: () {
                    if (widget.onServiceButtonClicked != null) {
                      widget.onServiceButtonClicked!(userHandleModel: service);
                    }
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // 图标
                        SizedBox(
                          width: 28,
                          height: 28,
                          child: ImageView(
                            service.functionIconUrl ?? '',
                            fit: BoxFit.contain,
                            width: 28,
                            height: 28,
                          ),
                        ),
                        const SizedBox(height: 6),
                        // 文字
                        Flexible(
                          child: Text(
                            service.functionIconName ?? '',
                            style: const TextStyle(
                              fontSize: 11,
                              color: Color(0xff383A40),
                              fontWeight: FontWeight.w400,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
