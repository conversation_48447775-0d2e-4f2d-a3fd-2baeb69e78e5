export declare class DownloaderConfig {
    /**
     * 网络超时时间
     */
    /****
     * Network connection timeout time.
     */
    mNetworkTimeoutMs: number;
    /**
     * 链接超时时间
     */
    /****
     * Connection timeout time.
     */
    mConnectTimeoutS: number;
    /**
     * http 请求代理
     */
    /****
     * HTTP proxy.
     */
    mHttpProxy: string;
    /**
     * referrer
     */
    /****
     * Referer.
     */
    mReferrer: string;
    /**
     * ua
     */
    /****
     * UserAgent.
     */
    mUserAgent: string;
}
