import { PlayerErrorCode } from "../../player/bean/PlayerErrorCode";
import { BaseRequest } from "../../utils/BaseRequest";
import { HttpClientHelper } from "../../utils/HttpClientHelper";
export class GetTimeShiftRequest extends BaseRequest {
    constructor(c4, d4) {
        super(d4);
        this.mLiveShiftSource = null;
        this.mReferer = null;
        this.mNetworkTimeout = -1;
        this.mHttpProxy = null;
        this.mUserAgent = null;
        this.mCustomHeaders = [{}];
        this.httpClientHelper = null;
        this.mLiveShiftSource = c4;
    }
    setRefer(b4) {
        this.mReferer = b4;
    }
    setTimeout(a4) {
        this.mNetworkTimeout = a4;
    }
    setHttpProxy(z3) {
        this.mHttpProxy = z3;
    }
    setUserAgent(y3) {
        this.mUserAgent = y3;
    }
    setCustomHeaders(x3) {
        this.mCustomHeaders = x3;
    }
    async runInBackground() {
        let v3 = "";
        if (this.mLiveShiftSource) {
            v3 = this.mLiveShiftSource.getTimeLineUrl();
        }
        else {
            console.info("mLiveShiftSource is null");
        }
        if (this.wantStop) {
            this.sendFailResult(-1, "", "");
            return;
        }
        this.httpClientHelper = new HttpClientHelper(v3);
        if (this.mReferer) {
            this.httpClientHelper?.setRefer(this.mReferer);
        }
        if (this.mHttpProxy) {
            this.httpClientHelper?.setHttpProxy(this.mHttpProxy);
        }
        this.httpClientHelper?.setTimeout(this.mNetworkTimeout);
        if (this.mUserAgent) {
            this.httpClientHelper?.setUserAgent(this.mUserAgent);
        }
        if (this.mCustomHeaders.length > 0) {
            this.httpClientHelper?.setCustomHeaders(this.mCustomHeaders);
        }
        const w3 = await this.httpClientHelper?.doGet();
        if (w3 == null || v3?.length <= 0) {
            this.sendFailResult(PlayerErrorCode.ERROR_SERVER_LIVESHIFT_REQUEST_ERROR, "request fail", "");
            return;
        }
        if (w3 != null) {
            this.sendSuccessResult(w3, "");
        }
    }
    stopInner() {
        if (this.httpClientHelper) {
            this.httpClientHelper.stop();
        }
    }
}
