import { AliP<PERSON> } from './AliPlayer';
import { Context } from '@ohos.abilityAccessCtrl';
import { AliListPlayer } from './AliListPlayer';
import { AliLiveShiftPlayer } from './AliLiveShiftPlayer';
export declare class AliPlayerFactory {
    static createAliPlayer(z6: Context, a7?: string): AliPlayer;
    static createAliListPlayer(x6: Context, y6?: string): AliListPlayer;
    static createAliLiveShiftPlayer(v6: Context, w6?: string): AliLiveShiftPlayer;
}
