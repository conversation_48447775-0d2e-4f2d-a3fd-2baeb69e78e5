// @keepTs
// @ts-nocheck
import { BaseComponentConfig } from "../../conversation/config/ComponentConfig";
/**
 * 组件标识别名。用来标注具有相同特征的枚举值。
 * ```
 * 16位设计：高8位存储类型（递减编码），低8位存储值.
 * 类型按优先级排序，判断时从高到低逐一完全匹配.
 * 以新增枚举类型，固定写法：
 * ${新定义枚举名} = (ComponentIdentifierAlias.${组件标识别名，可以使用现有或新增} << 8) | ${枚举值，按枚举定义顺序顺延即可}
 * InputBarVoiceButton = (ComponentIdentifierAlias.INPUT << 8) | 1,
 * ```
 *
 * @since 1.7.0
 */
export declare enum ComponentIdentifierAlias {
    /** 输入区域组件 */
    INPUT = 255,
    /** 会话内容组件 */
    CONVERSATION_CONTENT = 254,
    /** 消息气泡组件 */
    MESSAGE_BUBBLE = 253,
    /** 水印组件 */
    WATERMARK = 252
}
/** 通用组件标识 */
export declare enum ComponentIdentifier {
    /**
     * 输入框左侧语音按钮
     *
     *```
     * 注意：
     * 支持 InputAreaComponentConfig 配置。
     *```
     */
    InputBarVoiceButton = 65281,
    /**
     * 输入框表情按钮
     *
     *```
     * 注意：
     * 支持 InputAreaComponentConfig 配置。
     *```
     */
    InputBarEmoticonButton = 65282,
    /**
     * 输入框发送按钮
     *
     *```
     * 注意：
     * 支持 InputAreaComponentConfig 配置。
     *```
     */
    InputBarSendButton = 65283,
    /**
     * 输入框插件加号按钮
     *
     *```
     * 注意：
     * 支持 InputAreaComponentConfig 配置。
     *```
     */
    InputBarPluginButton = 65284,
    /**
     * 文本输入组件输入了大于2行文本时，输入框左侧的展开输入按钮，默认该组件不展示
     *
     *```
     * 注意：
     * 支持 InputAreaComponentConfig 配置。
     *```
     * @since 1.7.0
     */
    InputBarExpandTextAreaButton = 65285,
    /**
     * 阅后即焚输入框语音按钮
     *
     *```
     * 注意：
     * 支持 InputAreaComponentConfig 配置。
     *```
     */
    DestructBarVoiceButton = 65286,
    /**
     * 阅后即焚输入框图片按钮
     *
     *```
     * 注意：
     * 支持 InputAreaComponentConfig 配置。
     *```
     */
    DestructBarImageButton = 65287,
    /**
     * 阅后即焚输入框发送按钮
     *
     *```
     * 注意：
     * 支持 InputAreaComponentConfig 配置。
     *```
     */
    DestructBarSendButton = 65288,
    /**
     * 阅后即焚输入框删除按钮
     *
     *```
     * 注意：
     * 支持 InputAreaComponentConfig 配置。
     *```
     */
    DestructBarDeleteButton = 65289,
    /**
     * 表情面板左下角的添加按钮
     *
     *```
     * 注意：
     * 支持 InputAreaComponentConfig 配置。
     *```
     */
    EmoticonBoardAddButton = 65290,
    /**
     * 进入会话时展示未读的消息的组件，默认该组件在会话页面顶部右侧
     *
     *```
     * 注意：
     * 支持 ConversationContentComponentConfig 配置
     *```
     */
    ConversationUnreadMessageButton = 65035,
    /**
     * 进入会话时展示未读@我的消息的组件，默认该组件在会话页面顶部右侧
     *
     *```
     * 注意：
     * 支持 ConversationContentComponentConfig 配置
     *```
     */
    ConversationUnreadMentionedMessageButton = 65036,
    /**
     * 在进入会话后新收到的未读消息的组件，默认该组件在会话页面底部右侧
     *
     *```
     * 注意：
     * 支持 ConversationContentComponentConfig 配置
     *```
     */
    ConversationNewReceivedUnreadMessageButton = 65037,
    /**
     * 水印组件
     *
     *```
     * 注意：
     * 支持 WatermarkComponentConfig 配置
     *```
     */
    WatermarkComponent = 64526
}
/**
 * 组件标识辅助判断类
 * @since 1.7.0
 */
export declare class ComponentIdentifierHelper {
    /**
     * 获取组件的类型位（高8位）
     * @param identifier 组件标识
     * @returns 类型位
     */
    static getTypeField(b10: ComponentIdentifier): number;
    /**
     * 获取组件的值位（低8位）
     * @param identifier 组件标识
     * @returns 值位
     */
    static getValueField(a10: ComponentIdentifier): number;
    /**
     * 判断是否属于指定类型（完全匹配高8位）
     *
     * 整体设计思路：
     * 本方案使用16位二进制数值来编码组件标识，采用以下结构：
     * - **高8位（第16~9位）**：存储组件类型，使用递减编码
     * - **低8位（第8~1位）**：存储组件序号，从1开始递增
     *
     * 类型编码规则：
     *```
     * INPUT = 255                (11111111) - 输入区域组件
     * CONVERSATION_CONTENT = 254 (11111110) - 会话内容组件
     * 扩展类型 = 253, 252, 251...  (11111101, 11111100, 11111011...) - 未来扩展
     *```
     *
     *
     * 组件标识生成公式：
     *```
     * ComponentIdentifier = (类型编码 << 8) | 序号
     *```
     *
     * 枚举定义示例：
     *```
     * InputBarVoiceButton = (255 << 8) | 1 = 65281
     * 16位二进制: 1111111100000001
     *             ^^^^^^^^--------
     *             类型=255  序号=1
     *
     * ConversationUnreadMessageButton = (254 << 8) | 11 = 65035
     * 16位二进制: 1111111000001011
     *             ^^^^^^^^--------
     *             类型=254  序号=11
     *```
     *
     * 判断过程详解：
     * 1. **提取类型位**：`(identifier >> 8) & 0xFF`
     *    - 右移8位：将高8位移到低8位位置
     *    - 与0xFF按位AND：确保只保留8位，清除高位干扰
     *
     * 2. **完全匹配比较**：提取出的类型位 === 目标类型
     *    - 例如：255 === 255 ✅ (输入区域组件)
     *    - 例如：254 === 255 ❌ (不是输入区域组件)
     *
     * 方案优势：
     * - **完全匹配**：避免位运算OR/AND可能的冲突问题
     * - **优先级明确**：数值大小直接体现判断优先级
     * - **扩展简单**：新增类型只需递减数值，无需修改判断逻辑
     * - **容量充足**：支持255种类型，每种类型256个组件
     *
     * 方法使用示例
     *```typescript
     * // 判断 InputBarVoiceButton 是否为输入区域组件
     * const isInput = ComponentIdentifierHelper.belongsToType(
     *   ComponentIdentifier.InputBarVoiceButton,           // 65281 (1111111100000001)
     *   ComponentIdentifierAlias.INPUT                     // 255   (11111111)
     * );
     * // 判断过程：
     * // 1. 提取类型位：(65281 >> 8) & 0xFF = 255
     * // 2. 完全匹配：255 === 255 ✅ 返回 true
     *
     * // 判断 ConversationUnreadMessageButton 是否为输入区域组件
     * const isInput2 = ComponentIdentifierHelper.belongsToType(
     *   ComponentIdentifier.ConversationUnreadMessageButton,  // 65035 (1111111000001011)
     *   ComponentIdentifierAlias.INPUT                        // 255   (11111111)
     * );
     * // 判断过程：
     * // 1. 提取类型位：(65035 >> 8) & 0xFF = 254
     * // 2. 完全匹配：254 === 255 ❌ 返回 false
     *```
     *
     * @param componentConfig 组件配置，componentConfig.identifier是组件标识（16位编码值）
     * @param type 目标类型标识（8位类型编码）
     * @returns 是否属于该类型
     *
     */
    static belongsToType<x9 extends Object[]>(y9: BaseComponentConfig<x9>, z9: ComponentIdentifierAlias): boolean;
}
/**
 * 水印页面标识，定义了支持展示水印的页面。
 * @since 1.7.0
 */
export declare enum WatermarkPageIdentifier {
    /**
     * 合并转发页面
     */
    CombinePage = 1
}
