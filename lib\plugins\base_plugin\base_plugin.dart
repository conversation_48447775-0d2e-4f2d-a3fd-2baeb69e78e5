import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:flutter_uikit/ui_overlay.dart';

import '../../utils/manager/log_manager.dart';



class BasePlugin {

  static const MethodChannel _channel =
  MethodChannel('com.sgmw.flutter/base');

  /// 保存图片到相册
  static Future<bool> saveImage(String base64) async {
    LogManager().debug(base64);
    return await _channel.invokeMethod('saveImage', base64);
  }

  /// 获取定位
  static Future<dynamic> getLocation({dynamic args}) async {
    LogManager().debug("getLocation");
    final dynamic message = await _channel.invokeMethod('getLocation',args).catchError((e){
      UIOverlay.toast(e.toString());
    });
    return message;
  }

  /// 获取火星定位
  static Future<dynamic> getLocationGCJ02({dynamic args}) async {
    final dynamic message = await _channel.invokeMethod('getLocationGCJ02',args);
    return message;
  }

}
