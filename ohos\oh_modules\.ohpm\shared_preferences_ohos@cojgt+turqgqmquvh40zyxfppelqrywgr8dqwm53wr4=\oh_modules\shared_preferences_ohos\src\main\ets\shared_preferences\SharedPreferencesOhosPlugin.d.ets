import { FlutterPlugin, FlutterPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
import MessageCodec from '@ohos/flutter_ohos/src/main/ets/plugin/common/MessageCodec';
import { SharedPreferencesApi } from './Messages';
import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
export default class SharedPreferencesOhosPlugin implements FlutterPlugin, SharedPreferencesApi {
    private preferences;
    private listEncoder;
    getUniqueClassName(): string;
    constructor();
    setup(binaryMessenger: BinaryMessenger, api: SharedPreferencesApi | null): void;
    remove(key: string): boolean;
    setString(key: string, value: string): Promise<void>;
    setInt(key: string, value: number): Promise<void>;
    put(key: string, value: ESObject): Promise<void>;
    setDouble(key: string, value: number): Promise<void>;
    setStringList(key: string, value: string[]): Promise<void>;
    clear(prefix: string, allowList: string[]): Promise<void>;
    filterData(value: [string, Object], prefix: string, allowList: string[]): Map<string, Object>;
    getAll(prefix: string, allowList: Array<string>): Promise<Object>;
    getCodec(): MessageCodec<Object>;
    onAttachedToEngine(binding: FlutterPluginBinding): void;
    onDetachedFromEngine(binding: FlutterPluginBinding): void;
    setBool(key: string, value: boolean): Promise<void>;
    transformPref(value: Object): Object;
}
