import { InstanceManager } from './InstanceManager';
import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import { Reply } from "./GeneratedOhosWebView";
export declare class GeolocationPermissionsCallbackFlutterApiImpl {
    private binaryMessenger;
    private instanceManager;
    private api;
    constructor(binaryMessenger: BinaryMessenger, instanceManager: InstanceManager);
    create(instance: JsGeolocation, callback: Reply<void>): void;
}
