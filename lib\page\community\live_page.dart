import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';

import '../../api/community_api.dart';
import '../../models/community/live_model.dart';
import '../../models/community/aliyun_access_key.dart';
import '../../utils/manager/log_manager.dart';
import 'custom_ohos_view.dart';

class LivePage extends StatefulWidget {
  const LivePage({super.key});

  @override
  State<LivePage> createState() => _LivePageState();
}

class _LivePageState extends State<LivePage> {
  CustomViewController? _controller;
  int _position = 10; // 默认位置参数
  bool _isLoading = false;
  Timer? _stsRefreshTimer; // 用于定时刷新STS信息的定时器
  AliyunAccessKey? _currentAliyunConfig; // 缓存当前的阿里云配置
  final Duration _stsRefreshInterval = const Duration(minutes: 15); // STS刷新间隔

  @override
  void initState() {
    super.initState();
  }

  void _onCustomOhosViewCreated(CustomViewController controller) {
    _controller = controller;
    LogManager().debug('鸿蒙视图控制器已创建');

    // 初始化视频队列
    _controller?.initVideoQueue(_position);

    // 开始预加载STS信息
    _preloadStsInfoBatch();

    // 开始STS定时刷新
    _startStsRefreshTimer();

    // 监听从鸿蒙端返回的消息
    _controller?.customDataStream.listen((message) {
      LogManager().debug('Received from OHOS: $message');

      // 如果收到鸿蒙端的滑动事件，触发发送下一个视频
      if (message.contains('SLIDE_NEXT_VIDEO')) {
        _sendNextVideo();
      }
    });
  }

  // 发送下一个视频的方法
  void _sendNextVideo() {
    _controller?.sendNextVideo();
    LogManager().debug('发送下一个视频到鸿蒙端');
  }

  // 开始STS定时刷新计时器
  void _startStsRefreshTimer() {
    // 先取消之前的计时器（如果存在）
    _stsRefreshTimer?.cancel();

    // 设置新的定时刷新计时器
    _stsRefreshTimer = Timer.periodic(_stsRefreshInterval, (timer) {
      _refreshStsInfo();
    });

    LogManager().debug('已设置STS信息定时刷新，间隔: ${_stsRefreshInterval.inMinutes}分钟');
  }

  // 刷新STS信息
  Future<void> _refreshStsInfo() async {
    try {
      LogManager().debug('开始刷新阿里云STS配置信息');

      // 获取最新的阿里云STS配置
      final aliyun = await communityAPI.getAliyunAccessKey();
      _currentAliyunConfig = aliyun;

      LogManager().debug('已更新阿里云STS配置信息');

      // 如果视频队列中有需要播放的视频，使用新的STS信息更新
      if (_controller != null &&
          _currentAliyunConfig != null &&
          _currentAliyunConfig!.accessKeyId != null &&
          _currentAliyunConfig!.accessKeySecret != null &&
          _currentAliyunConfig!.securityToken != null) {
        // 通知鸿蒙端更新STS信息（可根据实际需要调整）
        LogManager().debug('向鸿蒙端发送更新后的STS信息');
        // 这里可以考虑实现一个专门用于更新STS信息的方法
      }
    } catch (e) {
      LogManager().debug('刷新STS信息失败: $e');
    }
  }

  Future<void> _preloadStsInfoBatch() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // 获取阿里云STS配置
      final aliyun = await communityAPI.getAliyunAccessKey();
      _currentAliyunConfig = aliyun;

      // 获取视频列表
      final liveModels =
          await communityAPI.getCommunityLiveData(_position, 1, 5);

      if (_controller != null && liveModels.isNotEmpty) {
        LogManager().debug('获取到${liveModels.length}条直播数据，开始批量传输STS信息');

        for (var liveModel in liveModels) {
          // 提取视频ID
          String? videoId = _extractVideoId(liveModel);

          if (videoId != null && videoId.isNotEmpty) {
            await _controller!.sendVideoToNative(
                videoId: videoId,
                accessKeyId: aliyun.accessKeyId ?? '',
                accessKeySecret: aliyun.accessKeySecret ?? '',
                securityToken: aliyun.securityToken ?? '');

            // 短暂延迟，确保鸿蒙端有时间处理
            await Future.delayed(const Duration(milliseconds: 100));
          }
        }

        LogManager().debug('批量预加载STS信息完成');
      }
    } catch (e) {
      LogManager().debug('批量预加载STS信息失败: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // 从LiveModel中提取videoId的帮助方法
  String? _extractVideoId(LiveModel liveModel) {
    // 检查firstImg是否包含videoId
    if (liveModel.firstImg != null &&
        liveModel.firstImg?.videoId != null &&
        liveModel.firstImg?.videoId?.isNotEmpty == true) {
      return liveModel.firstImg?.videoId;
    }

    // 检查imgTexts是否包含videoId
    if (liveModel.imgTexts != null && liveModel.imgTexts!.isNotEmpty) {
      for (var imgText in liveModel.imgTexts!) {
        if (imgText.videoId != null && imgText.videoId!.isNotEmpty) {
          return imgText.videoId;
        }
      }
    }

    return null;
  }

  Widget _buildOhosView() {
    return Expanded(
      child: Container(
        color: Colors.black, // 改为黑色背景，更适合视频播放
        child: CustomOhosView(_onCustomOhosViewCreated),
      ),
      flex: 1,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildOhosView(),
      ],
    );
  }

  @override
  void dispose() {
    // 取消定时器
    _stsRefreshTimer?.cancel();
    // 在页面销毁时清理资源
    super.dispose();
  }
}
