import { AVPLBase } from './AVPLBase';
import { IPlayer } from './IPlayer';
import { ListPlayerBase } from './nativeclass/ListPlayerBase';
import { UrlListPlayer } from './UrlListPlayer';
import { UrlPlayer } from './UrlPlayer';
import { Context } from '@ohos.abilityAccessCtrl';
import { UrlSource } from './source/UrlSource';
export declare class UrlVideoListPlayer extends AVPLBase implements UrlListPlayer, UrlPlayer {
    private mLog;
    private mUrlVideoPlayer;
    private mUrlPrerenderPlayer;
    constructor(n41: Context, o41: string);
    protected createListPlayer(j41: Context, k41: string, l41: number, m41: number): ListPlayerBase;
    protected getNativePlayerWithContext(h41: Context, i41: string): IPlayer;
    protected getPrerenderPlayerWithContext(f41: Context, g41: string): IPlayer;
    protected getCurrentPlayerIndex(): number;
    getPreRenderPlayer(): IPlayer | undefined;
    addUrl(z40: string, a41: string): void;
    moveToNext(): boolean;
    moveToNextWithPrerendered(): boolean;
    moveToPrev(): boolean;
    moveTo(u40: string): boolean;
    setUrlDataSource(s40: UrlSource): void;
}
