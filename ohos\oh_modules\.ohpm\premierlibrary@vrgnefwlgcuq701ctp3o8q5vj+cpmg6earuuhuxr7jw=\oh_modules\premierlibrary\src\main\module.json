{"app": {"bundleName": "com.aliyun.player.demo_ohos", "versionCode": 1000000, "versionName": "1.0.0", "minAPIVersion": 50000012, "targetAPIVersion": 50000012, "apiReleaseType": "Release", "compileSdkVersion": "********", "compileSdkType": "HarmonyOS", "appEnvironments": [], "bundleType": "app"}, "module": {"name": "premierlibrary", "type": "har", "description": "$string:module_desc", "mainElement": "EntryAbility", "deviceTypes": ["phone"], "requestPermissions": [{"name": "ohos.permission.INTERNET"}, {"name": "ohos.permission.GET_WIFI_INFO"}, {"name": "ohos.permission.GET_NETWORK_INFO"}], "packageName": "premierlibrary", "installationFree": false, "virtualMachine": "ark12.0.2.0", "compileMode": "esmodule", "dependencies": []}}