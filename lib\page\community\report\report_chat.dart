import 'dart:math';

import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/api/report_api/report_api.dart';
import 'package:wuling_flutter_app/common/action.dart';
import 'package:wuling_flutter_app/constant/storage.dart';
import 'package:wuling_flutter_app/page/community/report/report_chat/report_chat_group_page.dart';
import 'package:wuling_flutter_app/page/community/report/report_chat/report_chat_page.dart';
import 'package:wuling_flutter_app/platform/im_view/IMView.dart';

import '../../../utils/manager/log_manager.dart';
import '../../../utils/sp_util.dart';

class ReportChat extends StatefulWidget {
  const ReportChat({super.key});

  @override
  State<StatefulWidget> createState() => _ReportChatState();
}

class _ReportChatState extends State<ReportChat> {

  Map<String,Future Function(dynamic message)> methodCallbacks = {};

  refresh(){
    if(methodCallbacks.containsKey("refresh")){
      Function methodCall =  methodCallbacks['refresh']!;
      methodCall();
    }
  }

  Future<String> getImToken({bool isRefresh = false})async{
    String token = "";
    if(isRefresh != true){
      token = SpUtil().getString(RR_IM_TOKEN);
    }
    if(token == ""){
      token = await reportApi.imToken();
      await SpUtil().setString(RR_IM_TOKEN, token);
    }
    return token;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Center(
        child: FutureBuilder<String>(
          future: getImToken(), // 异步操作的Future对象
          builder: (BuildContext context, AsyncSnapshot<String> snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              // 当Future还未完成时，显示加载中的UI
              return CircularProgressIndicator();
            } else if (snapshot.hasError) {
              // 当Future发生错误时，显示错误提示的UI
              return Text('Error: ${snapshot.error}');
            } else {
              // 当Future成功完成时，显示数据
              return IMView(token: snapshot.data ?? "", getToken: (isRefresh)async {
                String token = await getImToken(isRefresh:isRefresh);
                LogManager().debug(token);
                return token;
              }, callBacks: methodCallbacks, chatPage: (args) {
                LogManager().debug('$args');
                NavigatorAction.init(context,view: ReportChatPage(args:args));
              }, type: 'list',);
            }
          },
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();
    list = getReportChatList();
  }

  List<ReportChatItemModel> list = [];

  List<ReportChatItemModel> getReportChatList() {
    List<ReportChatItemModel> list = [];
    for(int i = 0; i<50;i++){
      list.add(ReportChatItemModel.fromMap());
    }
    return list;
  }

}

class ReportChatItemModel{
  final String name;
  final String headImgSrc;
  final String content;
  final String time;
  final int newNum;

  ReportChatItemModel({required this.newNum, required this.name, required this.content,required this.time, required this.headImgSrc});

  factory ReportChatItemModel.fromMap(){
    return ReportChatItemModel(
        name:"longjoe360",
        newNum: Random().nextInt(6),
        headImgSrc:"assets/images/animation_frame/ac_animation/normal/accAnimationFrame_55.png",
        time:"10:22",
        content:"您好，求互相关注和点赞，谢谢。"
    );
  }

}

class ReportChatItem extends StatelessWidget{
  final ReportChatItemModel model;
  const ReportChatItem({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 10),
      padding: const EdgeInsets.symmetric(horizontal: 20,vertical: 10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Image.asset(model.headImgSrc,width: 40,height: 40),
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(model.name),
                Padding(
                  padding: const EdgeInsets.only(top: 5),
                  child: Text(model.content),
                )
              ],
            ),
          ),
          Text(model.time)
        ],
      ),
    );
  }
}