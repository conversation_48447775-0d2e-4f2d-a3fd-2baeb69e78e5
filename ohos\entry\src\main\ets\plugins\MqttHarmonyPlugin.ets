import {
  FlutterPlugin,
  Flutter<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>all,
  MethodCallHandler,
  MethodChannel,
  MethodResult,
} from '@ohos/flutter_ohos';
import { MqttHarmonyClient } from '../mqtt/MqttHarmonyClient';
import emitter from '@ohos.events.emitter';
import { hilog } from '@kit.PerformanceAnalysisKit';

const CHANNEL_NAME = 'mqtt_harmony_channel';

// 使用枚举定义MQTT事件ID（与MqttHarmonyClient保持一致）
enum MqttEvents {
  CONNECTED = 1001,
  DISCONNECTED = 1002,
  MESSAGE_RECEIVED = 1003,
  SUBSCRIBE_SUCCESS = 1004,
  ERROR = 1005
}

// 定义消息数据接口
interface MessageData {
  topic: string;
  payload: string;
  qos: number;
  isBase64Encoded: boolean;  // 添加这一行
}

// 定义事件数据接口
interface EventMessageData {
  messageData: string;
}

// 定义错误事件数据接口
interface EventErrorData {
  error: string;
}

export class MqttHarmonyPlugin implements FlutterPlugin {
  private channel: MethodChannel | null = null;
  private mqttClient: MqttHarmonyClient;
  private readonly TAG = 'MqttHarmonyPlugin';

  constructor() {
    this.mqttClient = MqttHarmonyClient.getInstance();
    this.setupEventListeners();
  }

  getUniqueClassName(): string {
    return 'MqttHarmonyPlugin';
  }

  onAttachedToEngine(binding: FlutterPluginBinding): void {
    this.channel = new MethodChannel(binding.getBinaryMessenger(), CHANNEL_NAME);
    this.channel.setMethodCallHandler({
      onMethodCall: (call: MethodCall, result: MethodResult): void => {
        hilog.info(0x0000, this.TAG, '收到方法调用: %{public}s', call.method);
  
        switch (call.method) {
          case 'connectMqtt':
            this.handleConnectMqtt(call, result);
            break;
          case 'disconnectMqtt':
            this.handleDisconnectMqtt(call, result);
            break;
          case 'getConnectionStatus':
            this.handleGetConnectionStatus(result);
            break;
          default:
            result.notImplemented();
            break;
        }
      }
    });
    hilog.info(0x0000, this.TAG, '插件已附加到引擎');
  }

  onDetachedFromEngine(binding: FlutterPluginBinding): void {
    if (this.channel != null) {
      this.channel.setMethodCallHandler(null);
    }
    hilog.info(0x0000, this.TAG, '插件已从引擎分离');
  }


  private async handleConnectMqtt(call: MethodCall, result: MethodResult): Promise<void> {
    try {
      const url = call.argument('url') as string;
      const clientId = call.argument('clientId') as string;
      const username = call.argument('username') as string;
      const password = call.argument('password') as string;
      const topics = call.argument('topics') as string[];

      hilog.info(0x0000, this.TAG, '连接请求参数:');
      hilog.info(0x0000, this.TAG, 'URL: %{public}s', url);
      hilog.info(0x0000, this.TAG, '客户端ID: %{public}s', clientId);
      hilog.info(0x0000, this.TAG, '用户名: %{public}s', username);
      hilog.info(0x0000, this.TAG, '主题: %{public}s', JSON.stringify(topics));

      // 创建MQTT客户端
      this.mqttClient.createMqttClient({
        url: url,
        clientId: clientId,
        username: username,
        password: password,
        topics: topics
      });

      // 连接MQTT服务器
      const connected = await this.mqttClient.connectMqtt();
      
      if (connected) {
        result.success('连接已启动');
      } else {
        result.error('CONNECTION_FAILED', '连接MQTT服务器失败', null);
      }
    } catch (error) {
      hilog.error(0x0000, this.TAG, '连接错误: %{public}s', JSON.stringify(error));
      result.error('CONNECTION_ERROR', JSON.stringify(error), null);
    }
  }

  private async handleDisconnectMqtt(call: MethodCall, result: MethodResult): Promise<void> {
    try {
      hilog.info(0x0000, this.TAG, '收到断开连接请求');
      await this.mqttClient.disconnectMqtt();
      result.success('已断开连接');
    } catch (error) {
      hilog.error(0x0000, this.TAG, '断开连接错误: %{public}s', JSON.stringify(error));
      result.error('DISCONNECT_ERROR', JSON.stringify(error), null);
    }
  }

  private handleGetConnectionStatus(result: MethodResult): void {
    const isConnected = this.mqttClient.getConnectionStatus();
    hilog.info(0x0000, this.TAG, '连接状态: %{public}s', isConnected.toString());
    result.success(isConnected);
  }

  // 设置事件监听器
  private setupEventListeners(): void {
    // 连接成功事件
    emitter.on({ eventId: MqttEvents.CONNECTED }, (data) => {
      hilog.info(0x0000, this.TAG, '向Flutter发送连接成功事件');
      this.channel?.invokeMethod('onConnected', null);
    });

    // 断开连接事件
    emitter.on({ eventId: MqttEvents.DISCONNECTED }, (data) => {
      hilog.info(0x0000, this.TAG, '向Flutter发送断开连接事件');
      this.channel?.invokeMethod('onDisconnected', null);
    });

    // 消息接收事件
    emitter.on({ eventId: MqttEvents.MESSAGE_RECEIVED }, (data) => {
      hilog.info(0x0000, this.TAG, '向Flutter发送消息接收事件');
      const eventData = data.data as EventMessageData;
      const messageData: MessageData = JSON.parse(eventData.messageData);
      this.channel?.invokeMethod('onMessageReceived', {
        'topic': messageData.topic,
        'payload': messageData.payload,
        'qos': messageData.qos,
        'isBase64Encoded': messageData.isBase64Encoded  // 添加这一行
      });
    });

    // 订阅成功事件
    emitter.on({ eventId: MqttEvents.SUBSCRIBE_SUCCESS }, (data) => {
      hilog.info(0x0000, this.TAG, '向Flutter发送订阅成功事件');
      this.channel?.invokeMethod('onSubscribeSuccess', null);
    });

    // 错误事件
    emitter.on({ eventId: MqttEvents.ERROR }, (data) => {
      const eventData = data.data as EventErrorData;
      hilog.error(0x0000, this.TAG, '向Flutter发送错误事件: %{public}s', eventData.error);
      this.channel?.invokeMethod('onError', {
        'error': eventData.error
      });
    });
  }
}