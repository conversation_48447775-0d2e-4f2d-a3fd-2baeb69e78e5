import MapViewListener from "./l2"; import MapControlHand<PERSON> from "./o4"; import type BaseMap from "./o2"; import type { IHandleBundle, IHandleOptions } from "../g1/a2"; import type { MapStatusBundle } from "../g1/i1"; import type BmLayer from "./c2/q2"; import OnTouchListener, { TouchType } from "../g1/z1";               export default class MapController { baseMap: BaseMap; mapViewListener: MapViewListener; mHandler: MapControlHandler; bmLayer: BmLayer; touchListeners: Array<OnTouchListener>; constructor(map?: BaseMap); registMapViewListener(j30: MapViewListener, k30: BmLayer): void; registerMsgs(): void; setOnTouchMessageListener(listener: OnTouchListener): void; onTouchMessage(data: TouchType, x: number, y: number): void; handleTouchSingleClick(e: IHandleOptions): boolean; handleTouchDoubleClick(e: IHandleOptions): boolean; handleTouchLongPressClick(e: IHandleOptions): boolean; handleTouchSingleMove(e: IHandleOptions): boolean; handleTouchSingleMoveStart(e: IHandleOptions): boolean; handleTouchSingleMoveEnd(e: IHandleOptions): boolean; handleRotation(e: IHandleOptions): boolean; handleRotationStart(e: IHandleOptions): boolean; handleRotationEnd(e: IHandleOptions): boolean; handleTouchDoubleMove(e: IHandleOptions): boolean; handleTouchDoubleMoveStart(e: IHandleOptions): boolean; handleTouchDoubleMoveEnd(e: IHandleOptions): boolean; handleTouchPinchScale(e: IHandleOptions): boolean; handleTouchPinchScaleStart(e: IHandleOptions): boolean; handleTouchPinchScaleEnd(e: IHandleOptions): boolean; bmHandleOverlay(target: IHandleBundle): boolean; handleOverlay(target: IHandleBundle): false | void; onMapStatusChange(data: MapStatusBundle): boolean; onMapStateChange(data: MapStatusBundle): boolean; onMapIndoorChange(data: boolean): boolean; checkAppBaseMap(): boolean; destroy(): void; } 