import camera from '@ohos.multimedia.camera';
import { DeviceOrientation } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/systemchannels/PlatformChannel';
export declare class CameraUtils {
    constructor(context: Context);
    static getCameraManager(context: Context): camera.CameraManager;
    static getCameraDevice(cameraId: string, cameraManager: camera.CameraManager): camera.CameraDevice;
    static getCameraInput(cameraDevice: camera.CameraDevice | undefined, cameraManager: camera.CameraManager): camera.CameraInput | undefined;
    static serializeDeviceOrientation(orientation: DeviceOrientation): string;
    static deserializeDeviceOrientation(orientation: string): DeviceOrientation;
    static getAvailableCameras(context: Context): Array<Map<String, Object>>;
    static getSupportedOutputCapability(device: camera.CameraDevice, mode: camera.SceneMode, manager: camera.CameraManager): camera.CameraOutputCapability;
    static hasFlash(captureSession: camera.PhotoSession | camera.VideoSession): boolean;
    static isFlashModeSupported(captureSession: camera.PhotoSession | camera.VideoSession, mode: camera.FlashMode): boolean;
    static isExposureModeSupported(captureSession: camera.PhotoSession | camera.VideoSession, mode: camera.ExposureMode): boolean;
    static getZoomRatioRange(captureSession: camera.PhotoSession | camera.VideoSession): Array<number>;
    static getMinZoomRatio(captureSession: camera.PhotoSession | camera.VideoSession): number;
    static getMaxZoomRatio(captureSession: camera.PhotoSession | camera.VideoSession): number;
    static getExposureBiasRange(captureSession: camera.PhotoSession | camera.VideoSession): Array<number>;
    static getMinExposureBias(captureSession: camera.PhotoSession | camera.VideoSession): number;
    static getMaxExposureBias(captureSession: camera.PhotoSession | camera.VideoSession): number;
    static getExposureBiasStep(captureSession: camera.PhotoSession | camera.VideoSession): number;
}
