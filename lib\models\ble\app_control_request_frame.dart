import 'dart:typed_data';
import 'package:wuling_flutter_app/utils/str_util.dart';

import '../../utils/manager/log_manager.dart';

class AppControlRequestFrame {
  // 属性定义
  String? serviceId;          // 服务类型
  String? subFunction;        // 功能类型
  String? rollData;           // 滚码
  String? randomData;         // 随机数
  String? bleKey;             // BLE 密钥
  String? payloadLength;      // 负载长度
  String? controlData;        // 控制码
  String? dataCheck;          // 数据校验码
  String? finalDataStr;       // 完整数据帧字符串
  String? encryptedDataStr;   // 加密后的数据帧字符串
  Uint8List? encryptedData;   // 加密后的数据


  // 辅助方法：在字符串前补零至指定长度
  String _addZero(String str, int length) {
    return str.padLeft(length, '0');
  }

  // 构造函数1：带滚码的初始化方法
  AppControlRequestFrame.withRollData({
    required String rollData,
    required String randomData,
    required String bleKey,
    required String controlData,
    required String key,
  }) {
    StringBuffer dataStr = StringBuffer();
    LogManager().debug("[E300BLE]---App创建车控控制指令---");

    // 服务类型
    serviceId = "39D6";
    LogManager().debug("[E300BLE]---服务类型 = 0x${serviceId}---");
    dataStr.write(serviceId);

    // 功能类型
    subFunction = "0001";
    LogManager().debug("[E300BLE]---功能类型 = 0x${subFunction}---");
    dataStr.write(subFunction);

    // 滚码（此处设为 "00000000"）
    this.rollData = "00000000";
    LogManager().debug("[E300BLE]---滚码 = 0x${this.rollData}---");
    dataStr.write(this.rollData!);

    // 随机数
    this.randomData = _addZero(randomData, 8);
    LogManager().debug("[E300BLE]---随机数 = 0x${this.randomData}---");
    dataStr.write(this.randomData!);

    // BLE 密钥（转为大写）
    this.bleKey = _addZero(bleKey, 8).toUpperCase();
    LogManager().debug("[E300BLE]---bleKey = 0x${this.bleKey}---");
    dataStr.write(this.bleKey!);

    // 负载长度
    payloadLength = "06";
    LogManager().debug("[E300BLE]---payload长度 = 0x${payloadLength}---");
    dataStr.write(payloadLength!);

    // 控制码（转为大写）
    this.controlData = controlData.toUpperCase();
    LogManager().debug("[E300BLE]---控制码 = 0x${this.controlData}---");
    dataStr.write(this.controlData!);

    // 输出源数据帧
    LogManager().debug("[E300BLE]---源数据帧为：${dataStr.toString()}---");

    // 计算 CRC 校验码
    Uint8List data = StrUtil.hexStringToBytes(dataStr.toString());
    int crc = StrUtil.crc16CcittFalse(data);
    String crcCode = crc.toRadixString(16).toUpperCase();
    dataCheck = _addZero(crcCode, 4);
    LogManager().debug("[E300BLE]---CRC校验码：${dataCheck}---");
    dataStr.write(dataCheck!);

    // 补充数据帧（填充 7 个 "00"）
    for (int i = 0; i < 7; i++) {
      dataStr.write("00");
    }

    // 完整数据帧
    finalDataStr = dataStr.toString();
    LogManager().debug("[E300BLE]---完整数据帧为：${finalDataStr}---");

    // 数据加密
    encryptedDataStr = StrUtil.aes128Encrypt(finalDataStr!, key);
    LogManager().debug("[E300BLE]---加密后数据帧为：${encryptedDataStr}---");

    // 转换为字节数组
    encryptedData = StrUtil.hexStringToBytes(encryptedDataStr!);
    LogManager().debug("[E300BLE]---实际发送的数据：${encryptedData}---");
  }

  // 构造函数2：不带滚码的初始化方法
  AppControlRequestFrame.withRandomData({
    required String randomData,
    required String bleKey,
    required String controlData,
    required String key,
  }) {
    StringBuffer dataStr = StringBuffer();
    LogManager().debug("[E300BLE]---App创建控制指令2---");

    // 服务类型
    serviceId = "39D6";
    LogManager().debug("[E300BLE]---服务类型 = 0x${serviceId}---");
    dataStr.write(serviceId);

    // 功能类型
    subFunction = "0001";
    LogManager().debug("[E300BLE]---功能类型 = 0x${subFunction}---");
    dataStr.write(subFunction);

    // 滚码（固定为 "00000000"）
    dataStr.write("00000000");

    // 随机数
    this.randomData = _addZero(randomData, 8);
    LogManager().debug("[E300BLE]---随机数 = 0x${this.randomData}---");
    dataStr.write(this.randomData!);

    // BLE 密钥
    this.bleKey = _addZero(bleKey, 8);
    LogManager().debug("[E300BLE]---bleKey = 0x${this.bleKey}---");
    dataStr.write(this.bleKey!);

    // 负载长度
    payloadLength = "06";
    LogManager().debug("[E300BLE]---payload长度 = 0x${payloadLength}---");
    dataStr.write(payloadLength!);

    // 控制码
    this.controlData = controlData;
    LogManager().debug("[E300BLE]---控制码 = 0x${this.controlData}---");
    dataStr.write(this.controlData!);

    // 输出源数据帧
    LogManager().debug("[E300BLE]---源数据帧为：${dataStr.toString()}---");

    // 计算 CRC 校验码
    Uint8List data = StrUtil.hexStringToBytes(dataStr.toString());
    int crc = StrUtil.crc16CcittFalse(data);
    String crcCode = crc.toRadixString(16).toUpperCase();
    dataCheck = _addZero(crcCode, 4);
    LogManager().debug("[E300BLE]---CRC校验码：${dataCheck}---");
    dataStr.write(dataCheck!);

    // 补充数据帧（填充 7 个 "00"）
    for (int i = 0; i < 7; i++) {
      dataStr.write("00");
    }

    // 完整数据帧
    finalDataStr = dataStr.toString();
    LogManager().debug("[E300BLE]---完整数据帧为：${finalDataStr}---");

    // 数据加密
    encryptedDataStr = StrUtil.aes128Encrypt(finalDataStr!, key);
    LogManager().debug("[E300BLE]---加密后数据帧为：${encryptedDataStr}---");

    // 转换为字节数组
    encryptedData = StrUtil.hexStringToBytes(encryptedDataStr!);
    LogManager().debug("[E300BLE]---实际发送的数据：${encryptedData}---");
  }

  // 构造函数3：自定义服务类型和功能类型的初始化方法
  AppControlRequestFrame.withServiceId({
    required String serviceId,
    required String subfunction,
    required String randomData,
    required String bleKey,
    required String controlData,
    required String key,
  }) {
    StringBuffer dataStr = StringBuffer();
    LogManager().debug("[E300BLE]---App创建通用控制指令---");

    // 服务类型
    this.serviceId = _addZero(serviceId, 4);
    LogManager().debug("[E300BLE]---服务类型 = 0x${this.serviceId}---");
    dataStr.write(this.serviceId!);

    // 功能类型
    subFunction = _addZero(subfunction, 4);
    LogManager().debug("[E300BLE]---功能类型 = 0x${subFunction}---");
    dataStr.write(subFunction!);

    // 滚码（固定为 "00000000"）
    dataStr.write("00000000");

    // 随机数
    this.randomData = _addZero(randomData, 8);
    LogManager().debug("[E300BLE]---随机数 = 0x${this.randomData}---");
    dataStr.write(this.randomData!);

    // BLE 密钥
    this.bleKey = _addZero(bleKey, 8);
    LogManager().debug("[E300BLE]---bleKey = 0x${this.bleKey}---");
    dataStr.write(this.bleKey!);

    // 负载长度
    payloadLength = "06";
    LogManager().debug("[E300BLE]---payload长度 = 0x${payloadLength}---");
    dataStr.write(payloadLength!);

    // 控制码
    this.controlData = controlData;
    LogManager().debug("[E300BLE]---控制码 = 0x${this.controlData}---");
    dataStr.write(this.controlData!);

    // 输出源数据帧
    LogManager().debug("[E300BLE]---源数据帧为：${dataStr.toString()}---");

    // 计算 CRC 校验码
    Uint8List data = StrUtil.hexStringToBytes(dataStr.toString());
    int crc = StrUtil.crc16CcittFalse(data);
    String crcCode = crc.toRadixString(16).toUpperCase();
    dataCheck = _addZero(crcCode, 4);
    LogManager().debug("[E300BLE]---CRC校验码：${dataCheck}---");
    dataStr.write(dataCheck!);

    // 补充数据帧（填充 7 个 "00"）
    for (int i = 0; i < 7; i++) {
      dataStr.write("00");
    }

    // 完整数据帧
    finalDataStr = dataStr.toString();
    LogManager().debug("[E300BLE]---完整数据帧为：${finalDataStr}---");

    // 数据加密
    encryptedDataStr = StrUtil.aes128Encrypt(finalDataStr!, key);
    LogManager().debug("[E300BLE]---加密后数据帧为：${encryptedDataStr}---");

    // 转换为字节数组
    encryptedData = StrUtil.hexStringToBytes(encryptedDataStr!);
    LogManager().debug("[E300BLE]---实际发送的数据：${encryptedData}---");
  }
}