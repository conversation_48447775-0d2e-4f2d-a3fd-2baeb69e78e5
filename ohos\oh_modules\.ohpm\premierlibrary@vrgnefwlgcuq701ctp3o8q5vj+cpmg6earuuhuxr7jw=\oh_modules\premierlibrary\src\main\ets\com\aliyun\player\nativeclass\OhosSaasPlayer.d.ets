import { OhosUrlPlayer } from './OhosUrlPlayer';
export declare class OhosSaasPlayer extends OhosUrlPlayer {
    constructor(t30: Context);
    setUrlDataSource(s30: object): void;
    setLiveStsDataSource(r30: object): void;
    updateStsInfo(q30: object): void;
    setVidAuthSource(p30: object): void;
    updateVidAuth(o30: object): void;
    setVidStsSource(n30: object): void;
    setVidMpsSource(m30: object): void;
}
