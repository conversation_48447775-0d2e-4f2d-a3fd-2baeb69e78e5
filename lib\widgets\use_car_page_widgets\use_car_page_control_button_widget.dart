import 'package:wuling_flutter_app/models/car/car_service_status_model.dart';
import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/widgets/common/image_view.dart';
import 'package:wuling_flutter_app/models/car/car_service_model.dart';
import 'package:wuling_flutter_app/models/car/car_control_item_model.dart';

class ControlButton extends StatelessWidget {
  final String imageUrl;
  final String name;
  final VoidCallback? onPressed;

  const ControlButton({
    Key? key,
    required this.imageUrl,
    required this.name,
    this.onPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
        style: ButtonStyle(
          backgroundColor: MaterialStateProperty.all(Colors.transparent),
          elevation: MaterialStateProperty.all(0), // 取消阴影
          shadowColor:
              MaterialStateProperty.all(Colors.transparent), // 将阴影颜色也设置为透明
          padding: MaterialStateProperty.all(
              const EdgeInsets.symmetric(horizontal: 0)),
          shape: MaterialStateProperty.all<RoundedRectangleBorder>(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(18),
            ),
          ),
        ),
        onPressed: onPressed,
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 28,
                height: 28,
                child: ImageView(
                  imageUrl,
                  fit: BoxFit.fill,
                  width: 28,
                  height: 28,
                ),
              ),
              const SizedBox(height: 3),
              Text(
                name,
                style: const TextStyle(fontSize: 12, color: Color(0xff383A40)),
              ),
            ],
          ),
        ));
  }
}

class ControlButtonPaginationWidget extends StatefulWidget {
  final List<CarControlItemModel> ctrlItemList;
  final void Function({CarControlItemModel carControlItemModel})?
      onControlButtonClicked;
  const ControlButtonPaginationWidget(
      {Key? key,
      required this.ctrlItemList,
      this.onControlButtonClicked,})
      : super(key: key);

  @override
  _ControlButtonPaginationWidgetState createState() =>
      _ControlButtonPaginationWidgetState();
}

class _ControlButtonPaginationWidgetState
    extends State<ControlButtonPaginationWidget> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  String getCurrentStatusImageUrl(CarControlItemModel carControlItemModel) {
    String imageUrl = '';
    if (carControlItemModel.serviceModel.serviceStatusList != null)
      for (CarServiceStatusModel statusModel
          in carControlItemModel.serviceModel.serviceStatusList!) {
        if (carControlItemModel.status == statusModel.serviceStatusValue) {
          imageUrl = statusModel.serviceStatusImage;
          break;
        }
      }
    return imageUrl;
  }

  String getCurrentStatusName(CarControlItemModel carControlItemModel) {
    String name = '';
    if (carControlItemModel.serviceModel.serviceStatusList != null)
      for (CarServiceStatusModel statusModel
          in carControlItemModel.serviceModel.serviceStatusList!) {
        if (carControlItemModel.status == statusModel.serviceStatusValue) {
          name = statusModel.serviceStatusName;
          break;
        }
      }
    return name;
  }

  double getProperAspectRatio(double buttonSize){
    double height = buttonSize / 1.2;
    double minHeight = 64.0;
    if(height >= minHeight){
      return 1.2;
    } else {
      return buttonSize / minHeight;
    }
  }
  @override
  Widget build(BuildContext context) {
    int itemCount = 4;
    double screenWidth = MediaQuery.of(context).size.width - 40;//需减去两边padding
    double spacing = 10;
    double topPadding = 20;
    double bottomPadding = 10;
    double buttonSize = (screenWidth - ((itemCount + 1) * spacing)) / itemCount;

    int numPages = (widget.ctrlItemList.length / itemCount).ceil();
    return Column(
      children: [
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 8),
          decoration: BoxDecoration(
            color: const Color(0xfff8f8f8),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                height: widget.ctrlItemList.isNotEmpty ? topPadding : 0,
              ),
              Container(
                color: Colors.transparent,
                height: widget.ctrlItemList.isNotEmpty ? buttonSize / getProperAspectRatio(buttonSize) : 0,
                child: PageView.builder(
                  controller: _pageController,
                  itemCount: numPages,
                  onPageChanged: (int page) {
                    setState(() {
                      _currentPage = page;
                    });
                  },
                  itemBuilder: (_, pageIndex) {
                    int startIndex = pageIndex * itemCount;
                    int endIndex = startIndex + itemCount;
                    List<CarControlItemModel> pageItems =
                        widget.ctrlItemList.sublist(
                      startIndex,
                      endIndex > widget.ctrlItemList.length
                          ? widget.ctrlItemList.length
                          : endIndex,
                    );
                    return GridView.builder(
                      padding: EdgeInsets.only(left: spacing, right: spacing),
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: itemCount,
                        childAspectRatio: getProperAspectRatio(buttonSize),
                        crossAxisSpacing: spacing,
                      ),
                      physics:
                          const NeverScrollableScrollPhysics(), // 禁用GridView的滚动
                      itemBuilder: (_, index) {
                        if (index < pageItems.length) {
                          return ControlButton(
                            imageUrl:
                                getCurrentStatusImageUrl(pageItems[index]),
                            name: getCurrentStatusName(pageItems[index]),
                            onPressed: () {

                              if (widget.onControlButtonClicked != null) {
                                widget.onControlButtonClicked!(
                                    carControlItemModel: pageItems[index]);
                              }
                            },
                          );
                        } else {
                          return Container(); // 透明容器占位
                        }
                      },
                      itemCount: itemCount, // 总是创建4个元素，不足的用透明容器填充
                    );
                  },
                ),
              ),
              // SizedBox(height: 20),
              SizedBox(
                child: Container(
                  decoration: BoxDecoration(
                    color: const Color(0xFFDFE1E3),
                    borderRadius: BorderRadius.circular(2),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: List.generate(numPages, (index) {
                      return Container(
                        width: 8,
                        height: 4,
                        margin: const EdgeInsets.symmetric(horizontal: 0),
                        decoration: BoxDecoration(
                          shape: BoxShape.rectangle,
                          color: _currentPage == index
                              ? const Color(0xFF050B29)
                              : const Color(0xFFDFE1E3),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      );
                    }),
                  ),
                ),
              ),
              SizedBox(height: widget.ctrlItemList.isNotEmpty ? bottomPadding : 0),
            ],
          ),
        )
      ],
    );
  }
}
