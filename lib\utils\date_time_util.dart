import 'package:intl/intl.dart';

import 'manager/log_manager.dart';

class DateTimeUtil {
  static const int D_MINUTE = 60;
  static const int D_HOUR = 3600; // 60 * 60
  static const int D_DAY = 86400; // 60 * 60 * 24
  static const int D_WEEK = 604800; // 60 * 60 * 24 * 7

  /// 获取当前时间的逻辑日历
  static DateFormat _getFormatter(String format) {
    return DateFormat(format);
  }

  /// 将日期转换为当前时区的日期
  static DateTime? convertDateToLocalTime(DateTime? forDate) {
    if (forDate == null) return null;
    return forDate.toLocal();
  }

  /// 将时间戳转换为 DateTime（秒级时间戳）
  static DateTime? dateWithSecondsSince1970(double? timeIntervalInSeconds) {
    if (timeIntervalInSeconds == null) return null;
    return DateTime.fromMillisecondsSinceEpoch(
      (timeIntervalInSeconds * 1000).toInt(),
      isUtc: false,
    );
  }

  /// 将时间戳转换为 DateTime（毫秒级时间戳）
  static DateTime? dateWithMillisecondsSince1970(double? timeIntervalInMilliSeconds) {
    if (timeIntervalInMilliSeconds == null) return null;
    return DateTime.fromMillisecondsSinceEpoch(
      timeIntervalInMilliSeconds.toInt(),
      isUtc: false,
    );
  }

  /// 日期字符串转换为 DateTime
  static DateTime? dateFromString(String? dateString, {String format = 'yyyy-MM-dd HH:mm:ss'}) {
    if (dateString == null || dateString.isEmpty) return null;
    try {
      return _getFormatter(format).parse(dateString);
    } catch (e) {
      LogManager().debug("日期字符串解析失败: $e");
      return null;
    }
  }

  /// DateTime 转换为日期字符串，如果传入的 DateTime? 参数为 null，则返回空字符串
  static String stringFromDate(DateTime? date, {String format = 'yyyy-MM-dd HH:mm:ss'}) {
    if (date == null) {
      return '';
    }
    return _getFormatter(format).format(date);
  }

  /// 从现在起向后推几天的日期
  static DateTime dateWithDaysFromNow(int days) {
    return DateTime.now().add(Duration(days: days));
  }

  /// 从现在起向前推几天的日期
  static DateTime dateWithDaysBeforeNow(int days) {
    return DateTime.now().subtract(Duration(days: days));
  }

  /// 从现在起向后推几小时的日期
  static DateTime dateWithHoursFromNow(int hours) {
    return DateTime.now().add(Duration(hours: hours));
  }

  /// 从现在起向前推几小时的日期
  static DateTime dateWithHoursBeforeNow(int hours) {
    return DateTime.now().subtract(Duration(hours: hours));
  }

  /// 从现在起向后推几分钟的日期
  static DateTime dateWithMinutesFromNow(int minutes) {
    return DateTime.now().add(Duration(minutes: minutes));
  }

  /// 从现在起向前推几分钟的日期
  static DateTime dateWithMinutesBeforeNow(int minutes) {
    return DateTime.now().subtract(Duration(minutes: minutes));
  }

  /// 获取明天的日期
  static DateTime dateTomorrow() {
    return dateWithDaysFromNow(1);
  }

  /// 获取昨天的日期
  static DateTime dateYesterday() {
    return dateWithDaysBeforeNow(1);
  }

  /// 获取今天的日期
  static DateTime dateNow() {
    return DateTime.now();
  }

  /// 比较两个日期是否是同一天
  static bool isSameDay(DateTime? date1, DateTime? date2) {
    if (date1 == null || date2 == null) return false;
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  /// 判断给定日期是否是今天
  static bool isToday(DateTime? date) {
    return isSameDay(date, dateNow());
  }

  /// 判断给定日期是否是明天
  static bool isTomorrow(DateTime? date) {
    return isSameDay(date, dateTomorrow());
  }

  /// 判断给定日期是否是昨天
  static bool isYesterday(DateTime? date) {
    return isSameDay(date, dateYesterday());
  }

  /// 判断给定日期是否在本周内
  static bool isThisWeek(DateTime? date) {
    if (date == null) return false;
    final DateTime now = dateNow();
    final int diff = date.difference(now).inDays;
    return diff.abs() < 7 && date.weekday <= now.weekday;
  }

  /// 获取指定日期的前推几年
  static DateTime? dateBySubtractingYears(DateTime? date, int years) {
    if (date == null) return null;
    return DateTime(date.year - years, date.month, date.day, date.hour, date.minute, date.second);
  }

  /// 获取指定日期的后推几年
  static DateTime? dateByAddingYears(DateTime? date, int years) {
    if (date == null) return null;
    return DateTime(date.year + years, date.month, date.day, date.hour, date.minute, date.second);
  }

  /// 获取指定日期的前推几个月
  static DateTime? dateBySubtractingMonths(DateTime? date, int months) {
    if (date == null) return null;
    return DateTime(date.year, date.month - months, date.day, date.hour, date.minute, date.second);
  }

  /// 获取指定日期的后推几个月
  static DateTime? dateByAddingMonths(DateTime? date, int months) {
    if (date == null) return null;
    return DateTime(date.year, date.month + months, date.day, date.hour, date.minute, date.second);
  }

  /// 获取指定日期的前推几天
  static DateTime? dateBySubtractingDays(DateTime? date, int days) {
    if (date == null) return null;
    return date.subtract(Duration(days: days));
  }

  /// 获取指定日期的后推几天
  static DateTime? dateByAddingDays(DateTime? date, int days) {
    if (date == null) return null;
    return date.add(Duration(days: days));
  }

  /// 获取指定日期的前推几小时
  static DateTime? dateBySubtractingHours(DateTime? date, int hours) {
    if (date == null) return null;
    return date.subtract(Duration(hours: hours));
  }

  /// 获取指定日期的后推几小时
  static DateTime? dateByAddingHours(DateTime? date, int hours) {
    if (date == null) return null;
    return date.add(Duration(hours: hours));
  }

  /// 获取指定日期的前推几分钟
  static DateTime? dateBySubtractingMinutes(DateTime? date, int minutes) {
    if (date == null) return null;
    return date.subtract(Duration(minutes: minutes));
  }

  /// 获取指定日期的后推几分钟
  static DateTime? dateByAddingMinutes(DateTime? date, int minutes) {
    if (date == null) return null;
    return date.add(Duration(minutes: minutes));
  }

  /// 获取某天的开始时间
  static DateTime? dateAtStartOfDay(DateTime? date) {
    if (date == null) return null;
    return DateTime(date.year, date.month, date.day, 0, 0, 0);
  }

  /// 获取某天的结束时间
  static DateTime? dateAtEndOfDay(DateTime? date) {
    if (date == null) return null;
    return DateTime(date.year, date.month, date.day, 23, 59, 59);
  }

  /// 获取指定日期这一天在未来的几天内
  static DateTime? dateWithDaysInFuture(DateTime? date, int days) {
    if (date == null) return null;
    return date.add(Duration(days: days));
  }

  /// 获取指定日期这一天在过去的几天内
  static DateTime? dateWithDaysInPast(DateTime? date, int days) {
    if (date == null) return null;
    return date.subtract(Duration(days: days));
  }

  /// 计算两个日期之间的天数差
  static int? daysBetween(DateTime? start, DateTime? end) {
    if (start == null || end == null) return null;
    return end.difference(start).inDays;
  }

  /// 计算两个日期之间的小时数差
  static int? hoursBetween(DateTime? start, DateTime? end) {
    if (start == null || end == null) return null;
    return end.difference(start).inHours;
  }

  /// 计算两个日期之间的分钟数差
  static int? minutesBetween(DateTime? start, DateTime? end) {
    if (start == null || end == null) return null;
    return end.difference(start).inMinutes;
  }

  /// 获取当前时间距离给定日期之后的分钟数
  static int? minutesAfterDate(DateTime? date) {
    if (date == null) return null;
    return dateNow().difference(date).inMinutes;
  }

  /// 获取当前时间距离给定日期之前的分钟数
  static int? minutesBeforeDate(DateTime? date) {
    if (date == null) return null;
    return date.difference(dateNow()).inMinutes;
  }

  /// 获取当前时间距离给定日期之后的小时数
  static int? hoursAfterDate(DateTime? date) {
    if (date == null) return null;
    return dateNow().difference(date).inHours;
  }

  /// 获取当前时间距离给定日期之前的小时数
  static int? hoursBeforeDate(DateTime? date) {
    if (date == null) return null;
    return date.difference(dateNow()).inHours;
  }

  /// 获取当前时间距离给定日期之后的天数
  static int? daysAfterDate(DateTime? date) {
    if (date == null) return null;
    return dateNow().difference(date).inDays;
  }

  /// 获取当前时间距离给定日期之前的天数
  static int? daysBeforeDate(DateTime? date) {
    if (date == null) return null;
    return date.difference(dateNow()).inDays;
  }

  ///把时间戳转换成自定义的日期格式
  static String customDateFormat(String format,String timestamp) {
    if(timestamp.isEmpty) return "";
    final dateTime = DateTime.fromMillisecondsSinceEpoch(int.parse(timestamp));
    return DateFormat(format).format(dateTime);
  }
  ///根据时间戳格式化时间
  static String dateTimeFormat(int dateTime) {
    String str = "";
    int nowTime = DateTime.now().millisecondsSinceEpoch;
    int time = nowTime - dateTime;
    int minute = time ~/ 60000000;
    if(minute > 60*24*30) {
      minute = minute ~/ (60*24*30);
      str = "$minute月前";
    }else if(minute > 60*24) {
      minute = minute ~/ (60*24);
      str = "$minute天前";
    }else if(minute > 60) {
      minute = minute ~/ 60;
      str = "$minute小时前";
    }else {
      str = "$minute分钟前";
    }
    return str;
  }

  ///帖子话题时间格式化
  static String formatPostTime(double? timeInterval) {
    String createDateStr = '';
    DateTime? createDate = DateTimeUtil.dateWithMillisecondsSince1970(timeInterval);
    createDateStr = DateTimeUtil.stringFromDate(createDate, format: 'M月d日 HH:mm');
    return createDateStr;
  }

  static String timestampToDate(String timestamp, String format) {
    // 处理10位时间戳
    int milliseconds = timestamp.length <= 10
        ? int.parse(timestamp) * 1000
        : int.parse(timestamp);

    DateTime date = DateTime.fromMillisecondsSinceEpoch(milliseconds).toLocal();

    // 选择格式化方式
    return DateFormat(format).format(date);

  }

}