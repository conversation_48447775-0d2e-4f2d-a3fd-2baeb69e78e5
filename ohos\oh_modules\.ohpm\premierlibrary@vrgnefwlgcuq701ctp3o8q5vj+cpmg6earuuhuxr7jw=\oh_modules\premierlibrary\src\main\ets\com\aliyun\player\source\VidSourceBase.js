import { SourceBase } from './SourceBase';
import { Definition } from './Definition';
export class VidSourceBase extends SourceBase {
    constructor() {
        super();
        this.mPlayConfig = null;
        this.mOutputType = null;
        this.mStreamTypes = null;
        this.mResultType = null;
        this.mDigitalWatermarkType = null;
        this.nativeGetOutputTypeStr = () => {
            if (this.mOutputType == null) {
                return "";
            }
            else {
                return this.mOutputType.toString();
            }
        };
        this.nativeGetReAuthInfoStr = () => {
            return this.mReAuthInfo;
        };
        this.nativeGetResultTypeStr = () => {
            if (this.mResultType == null) {
                return "";
            }
            else {
                return this.mResultType;
            }
        };
        this.nativeGetStreamTypeStr = () => {
            if (this.mStreamTypes == null || this.mStreamTypes.size == 0) {
                return "";
            }
            let u39 = "";
            for (let v39 of this.mStreamTypes) {
                if (v39 != null) {
                    u39 += v39 + ",";
                }
            }
            if (u39.length > 0) {
                u39 = u39.slice(0, -1);
            }
            return u39;
        };
        this.nativeGetDefinitionStr = () => {
            if (this.mDefinitions == null || this.mDefinitions.length == 0) {
                return "";
            }
            if (this.mDefinitions.includes(Definition.DEFINITION_AUTO, 0)) {
                return Definition.DEFINITION_AUTO;
            }
            let s39 = "";
            for (let t39 of this.mDefinitions) {
                if (t39 != null) {
                    s39 += t39 + ",";
                }
            }
            if (s39.length > 0) {
                s39 = s39.slice(0, -1);
            }
            return s39;
        };
        this.nativeGetPlayerConfigStr = () => {
            if (this.mPlayConfig == null) {
                return "";
            }
            else {
                return this.mPlayConfig.genConfig();
            }
        };
        this.nativeGetFormatStr = () => {
            return this.getFormatStr();
        };
        this.nativeGetAuthTimeout = () => {
            return this.mAuthTimeout;
        };
        this.nativeGetTrace = () => {
            return this.mTrace;
        };
        this.nativeGetDigitalWatermarkTypeStr = () => {
            if (this.mDigitalWatermarkType == null) {
                return "";
            }
            else {
                return this.mDigitalWatermarkType.toString();
            }
        };
        this.mFormats = [];
        this.mDefinitions = [];
        this.mReAuthInfo = "";
        this.mAuthTimeout = 3600;
        this.mTrace = "";
    }
    getPlayerConfig() {
        if (this.mPlayConfig == null) {
            return "";
        }
        else {
            return this.mPlayConfig.genConfig();
        }
    }
    setPlayerConfig(h39) {
        this.mPlayConfig = h39;
    }
    getFormats() {
        return this.mFormats;
    }
    setFormats(g39) {
        this.mFormats = g39;
    }
    getFormatStr() {
        if (this.mFormats.length == 0) {
            return "";
        }
        let e39 = "";
        for (let f39 of this.mFormats) {
            if (f39 != null) {
                e39 += f39 + ",";
            }
        }
        if (e39.length > 0) {
            e39 = e39.slice(0, -1);
        }
        return e39;
    }
    setDefinition(d39) {
        this.mDefinitions = d39;
    }
    getOutputType() {
        return this.mOutputType;
    }
    setOutputType(c39) {
        this.mOutputType = c39;
    }
    setAuthTimeout(b39) {
        this.mAuthTimeout = b39;
    }
    setTrace(a39) {
        this.mTrace = a39;
    }
    setDigitalWatermarkType(z38) {
        this.mDigitalWatermarkType = z38;
    }
    getTrace() {
        return this.mTrace;
    }
    getDigitalWatermarkType() {
        return this.mDigitalWatermarkType;
    }
}
export var OutputType;
(function (y38) {
    y38["oss"] = "oss";
    y38["cdn"] = "cdn";
})(OutputType || (OutputType = {}));
export var StreamType;
(function (x38) {
    x38["video"] = "video";
    x38["audio"] = "audio";
})(StreamType || (StreamType = {}));
export var ResultType;
(function (w38) {
    w38["Single"] = "Single";
    w38["Multiple"] = "Multiple";
})(ResultType || (ResultType = {}));
export var DigitalWatermarkType;
(function (v38) {
    v38["TraceMark"] = "TraceMark";
    v38["CopyrightMark"] = "CopyrightMark";
})(DigitalWatermarkType || (DigitalWatermarkType = {}));
