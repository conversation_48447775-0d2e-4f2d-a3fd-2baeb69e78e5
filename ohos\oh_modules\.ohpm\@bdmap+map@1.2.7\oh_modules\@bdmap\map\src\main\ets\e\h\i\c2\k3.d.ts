import { Point } from '@bdmap/base'; import BmDrawItem from "./a3"; import BmDrawableResource from "./f2/l3"; export default class BmGround extends BmDrawItem { constructor();           setCenter(m11: Point): any;           setWidth(width: number): any; setHeight(height: number): any; setAnchorX(l11: number): any; setAnchorY(k11: number): any;                     setBitmapResource(j11: BmDrawableResource): any; } 