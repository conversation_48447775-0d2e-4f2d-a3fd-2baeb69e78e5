            import StringBuilder from "./q4"; export default class Bundle { private bundle; constructor(); put(key: string, value: any): this; get(key: string): any; has(key: string): boolean; delete(key: string): void; copy(b: Bundle): void; clear(): void; keySet(): string[]; first(): { key: string; value: any; }; size(): number; toObject(): any; toArray(h33?: boolean): any[]; toStringBuilder(name?: string): StringBuilder; } 