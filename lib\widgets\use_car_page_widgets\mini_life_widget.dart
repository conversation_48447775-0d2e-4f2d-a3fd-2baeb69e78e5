import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:wuling_flutter_app/utils/app_config_util.dart';
import 'package:wuling_flutter_app/utils/manager/log_manager.dart';
import 'package:wuling_flutter_app/utils/str_util.dart';

import '../../models/car/car_mini_life_model.dart';
import '../../models/car/car_service_model.dart';

class MiniLifeWidget extends StatefulWidget {
  final List<CarServiceModel> activityList;
  final CarMiniLifeModel? miniLifeModel;
  final void Function({CarServiceModel serviceModel})? onServiceButtonClicked;
  final void Function(String? url) onMiniLifeClicked;

  const MiniLifeWidget({
    super.key,
    required this.activityList,
    required this.miniLifeModel,
    required this.onServiceButtonClicked,
    required this.onMiniLifeClicked,
  });

  @override
  State<StatefulWidget> createState() => _MiniLifeWidgetState();
}

class _MiniLifeWidgetState extends State<MiniLifeWidget> {
  int bannerCurrentPage = 0;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: const Color(0xFFF7F7FA),
      child: Column(
        mainAxisSize: MainAxisSize.min, // 重要：让Column根据内容调整大小
        children: [
          const SizedBox(height: 10),
          buildBannerWidget(),
          const SizedBox(height: 10),
          buildBannerIndicatorWidget(),
          const SizedBox(height: 10),
        ],
      ),
    );
  }

  buildBannerWidget() {
    int listLength =
        widget.activityList.length + (widget.miniLifeModel != null ? 1 : 0);
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      width: double.infinity,
      child: Column(
        children: [
          SizedBox(
            height: 20,
            width: AppConfigUtil.screenWidth - 40,
            child: const Text(
              '车型互动',
              style: TextStyle(
                  fontSize: 18,
                  color: Color(0xFF383a40),
                  fontWeight: FontWeight.bold),
              textAlign: TextAlign.left,
            ),
          ),
          Container(
            height: 10,
          ),
          AspectRatio(
              aspectRatio: 335 / 144,
              child: SizedBox(
                height: 144,
                child: PageView.builder(
                  itemCount: listLength,
                  itemBuilder: (context, pageIndex) {
                    return buildBannerItemWidget(pageIndex);
                  },
                  onPageChanged: (index) {
                    setState(() {
                      bannerCurrentPage = index;
                    });
                  },
                ),
              ))
        ],
      ),
    );
  }

  buildBannerItemWidget(pageIndex) {
    String imageUrl;
    String url;///mini人生跳转链接
    CarServiceModel? model;
    if (widget.miniLifeModel != null) {
      if (pageIndex == 0) {
        imageUrl = widget.miniLifeModel?.image ?? '';
        url = widget.miniLifeModel?.url ?? '';
      } else {
        model = widget.activityList[pageIndex - 1];
        imageUrl = (model.serviceStatusList != null &&
                model.serviceStatusList!.isNotEmpty)
            ? model.serviceStatusList?.first.serviceStatusImage ?? ''
            : '';
        url = '';
      }
    } else {
      model = widget.activityList[pageIndex];
      imageUrl = (model.serviceStatusList != null &&
              model.serviceStatusList!.isNotEmpty)
          ? model.serviceStatusList?.first.serviceStatusImage ?? ''
          : '';
      url = '';
    }

    return GestureDetector(
        onTap: () {
          if (widget.miniLifeModel != null && pageIndex == 0) {
            widget.onMiniLifeClicked(url);
          } else {
            if (widget.onServiceButtonClicked != null && model != null) {
              widget.onServiceButtonClicked!(serviceModel: model);
            }
          }
        },
        child: Stack(
          children: [
            SizedBox.expand(
                child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: CachedNetworkImage(
                      imageUrl: imageUrl,
                      fit: BoxFit.contain,
                      placeholder: (context, url) {
                        return Container(
                          color: Colors.blue[100],
                          child: Center(child: Text('加载中...')),
                        );
                      },
                      errorWidget: (context, url, error) {
                        return Container(
                          color: Colors.orange[100],
                          child: Center(child: Text('加载失败')),
                        );
                      },
                    ))),
            if (widget.miniLifeModel != null && pageIndex == 0)
              Positioned(
                left: 0,
                right: 0,
                bottom: 1,
                child: Container(
                  height: 50,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      shape: BoxShape.rectangle,
                      color: const Color(0x99FFFFFF)),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      buildBannerTextWidget1(),
                      SizedBox(
                          width: 1,
                          child: Container(
                            height: 25,
                            color: const Color(0xFFCCCCCC),
                          )),
                      buildBannerTextWidget2(),
                      SizedBox(
                          width: 1,
                          child: Container(
                            height: 25,
                            color: const Color(0xFFCCCCCC),
                          )),
                      buildBannerTextWidget3()
                    ],
                  ),
                ),
              ),
            if (widget.miniLifeModel != null && pageIndex == 0)
              Positioned(
                  top: 15,
                  right: 15,
                  child: SizedBox(
                    height: 30,
                    width: 60,
                    child: RichText(
                      text: const TextSpan(
                          text: '前往打卡',
                          style: TextStyle(
                              fontSize: 14,
                              color: Color(0xFFFFFFFF),
                              fontWeight: FontWeight.normal,
                              decoration: TextDecoration.underline,
                              decorationColor: Color(0xFFFFFFFF),
                              decorationStyle: TextDecorationStyle.solid)),
                    ),
                  ))
          ],
        ));
  }

  buildBannerTextWidget1() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Row(
          children: [
            Text(
              widget.miniLifeModel?.cutEmission ?? '--',
              style: const TextStyle(
                  fontSize: 18,
                  color: Color(0xff383A40),
                  fontWeight: FontWeight.bold,
                  height: 1),
            ),
            const Text("g",
                style: TextStyle(fontSize: 10, color: Color(0xff383A40))),
          ],
        ),
        Container(
          height: 4,
        ),
        const Text("昨日减排",
            style: TextStyle(fontSize: 12, color: Color(0xFF868990))),
      ],
    );
  }

  buildBannerTextWidget2() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Row(
          children: [
            Text(
              widget.miniLifeModel?.save ?? '--',
              style: const TextStyle(
                  fontSize: 18,
                  color: Color(0xff383A40),
                  fontWeight: FontWeight.bold,
                  height: 1),
            ),
            const Text("元",
                style: TextStyle(fontSize: 10, color: Color(0xff383A40))),
          ],
        ),
        Container(
          height: 4,
        ),
        const Text("节省",
            style: TextStyle(fontSize: 12, color: Color(0xFF868990))),
      ],
    );
  }

  buildBannerTextWidget3() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        RichText(
            text: TextSpan(
                text: 'NO.',
                style: const TextStyle(fontSize: 10, color: Color(0xff383A40)),
                children: [
              TextSpan(
                  text: widget.miniLifeModel?.ranking ?? '--',
                  style: const TextStyle(
                      fontSize: 18,
                      color: Color(0xff383A40),
                      fontWeight: FontWeight.bold))
            ])),
        Container(
          height: 4,
        ),
        const Text("排名",
            style: TextStyle(fontSize: 12, color: Color(0xFF868990))),
      ],
    );
  }

  buildBannerIndicatorWidget() {
    int listLength =
        widget.activityList.length + (widget.miniLifeModel != null ? 1 : 0);
    return Visibility(
        visible: listLength > 1,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(
            listLength,
            (index) => Container(
              margin: const EdgeInsets.symmetric(horizontal: 2),
              width: bannerCurrentPage == index ? 41 : 14,
              height: 6,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2),
                shape: BoxShape.rectangle,
                color: bannerCurrentPage == index
                    ? const Color(0xFF008DFF)
                    : const Color(0xFFD8D8D8),
              ),
            ),
          ),
        ));
  }
}
