export class TrackInfo {
    constructor() {
        this.index = 0;
        this.mType = TrackType.TYPE_VOD;
        this.description = "";
        this.videoBitrate = 0;
        this.videoWidth = 0;
        this.videoHeight = 0;
        this.videoHDRType = VideoHDRType.VideoHDRType_SDR;
        this.audioLang = "";
        this.audioChannels = 0;
        this.audioSampleRate = 0;
        this.audioSampleFormat = 0;
        this.subtitleLang = "";
        this.vodDefinition = "";
        this.vodFileSize = 0;
        this.vodPlayUrl = "";
        this.vodWaterMarkPlayUrl = "";
        this.vodFormat = "";
        this.nativeSetType = (i33) => {
            if (i33 === TrackType.TYPE_VIDEO) {
                this.mType = TrackType.TYPE_VIDEO;
            }
            else if (i33 === TrackType.TYPE_AUDIO) {
                this.mType = TrackType.TYPE_AUDIO;
            }
            else if (i33 === TrackType.TYPE_SUBTITLE) {
                this.mType = TrackType.TYPE_SUBTITLE;
            }
            else if (i33 === TrackType.TYPE_VOD) {
                this.mType = TrackType.TYPE_VOD;
            }
        };
        this.nativeGetType = () => {
            return this.mType;
        };
    }
    setVideoHDRType(f33) {
        if (f33 === VideoHDRType.VideoHDRType_SDR) {
            this.videoHDRType = VideoHDRType.VideoHDRType_SDR;
        }
        else if (f33 === VideoHDRType.VideoHDRType_HDR10) {
            this.videoHDRType = VideoHDRType.VideoHDRType_HDR10;
        }
    }
    getVideoHDRType() {
        return this.videoHDRType;
    }
    getVodPlayUrl() {
        return this.vodPlayUrl;
    }
    getVodFormat() {
        return this.vodFormat;
    }
    getVodWaterMarkPlayUrl() {
        return this.vodWaterMarkPlayUrl;
    }
    getIndex() {
        return this.index;
    }
    getType() {
        return this.mType;
    }
    getVideoBitrate() {
        return this.videoBitrate;
    }
    getVideoWidth() {
        return this.videoWidth;
    }
    getVideoHeight() {
        return this.videoHeight;
    }
    getAudioLang() {
        return this.audioLang;
    }
    getAudioChannels() {
        return this.audioChannels;
    }
    getAudioSampleRate() {
        return this.audioSampleRate;
    }
    getAudioSampleFormat() {
        return this.audioSampleFormat;
    }
    getSubtitleLang() {
        return this.subtitleLang;
    }
    getDescription() {
        return this.description;
    }
    getVodDefinition() {
        return this.vodDefinition;
    }
    getVodFileSize() {
        return this.vodFileSize;
    }
}
TrackInfo.AUTO_SELECT_INDEX = -1;
export var VideoHDRType;
(function (e33) {
    e33[e33["VideoHDRType_SDR"] = 0] = "VideoHDRType_SDR";
    e33[e33["VideoHDRType_HDR10"] = 1] = "VideoHDRType_HDR10";
})(VideoHDRType || (VideoHDRType = {}));
export var TrackType;
(function (d33) {
    d33[d33["TYPE_VIDEO"] = 0] = "TYPE_VIDEO";
    d33[d33["TYPE_AUDIO"] = 1] = "TYPE_AUDIO";
    d33[d33["TYPE_SUBTITLE"] = 2] = "TYPE_SUBTITLE";
    d33[d33["TYPE_VOD"] = 3] = "TYPE_VOD";
})(TrackType || (TrackType = {}));
