// @keepTs
// @ts-nocheck
import { SightMessage } from '@rongcloud/imlib';
@Component
export declare struct SightPreviewItemComponent {
    @Prop
    sightMessage: SightMessage;
    @State
    progress: number;
    @Require
    messageId: number;
    @Prop
    messageUid: string;
    @State
    destructDuration: number;
    @State
    uri: string;
    @State
    downloadFailed: boolean;
    private defaultIndex;
    @Prop
    currentIndex: number;
    private isPrepared;
    private controller;
    private isPlaying;
    private oneClick?;
    private onPlayEnd?;
    private fileInfo;
    private downloadListener;
    aboutToAppear(): void;
    aboutToDisappear(): void;
    private initFileDownloadInfo;
    private loadMedia;
    private downloadMedia;
    build(): void;
    onCurrentPositionChange(): void;
    private startPlay;
}
