import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/utils/http/http_request.dart';

import '../../utils/manager/log_manager.dart';

HttpRequest _httpRequest = HttpRequest();

class NetworkTestPage extends StatefulWidget {
  @override
  _NetworkTestPageState createState() => _NetworkTestPageState();
}

class _NetworkTestPageState extends State<NetworkTestPage> {
  String url = '';
  List<Map<String, String>> keyValuePairs = [];
  List<Map<String, String>> headerPairs = [];
  String selectedMethod = 'GET';
  String responseText = '';

  void addKeyValuePair() {
    setState(() {
      keyValuePairs.add({'key': '', 'value': ''});
    });
  }

  void addHeaderPair() {
    setState(() {
      headerPairs.add({'key': '', 'value': ''});
    });
  }

  void removeKeyValuePair(int index) {
    setState(() {
      keyValuePairs.removeAt(index);
    });
  }

  void removeHeaderPair(int index) {
    setState(() {
      headerPairs.removeAt(index);
    });
  }

  void sendRequest() async {
    LogManager().debug('URL: $url');
    LogManager().debug('Method: $selectedMethod');
    LogManager().debug('Params Key-Value Pairs:');
    for (var pair in keyValuePairs) {
      LogManager().debug('${pair['key']}: ${pair['value']}');
    }
    LogManager().debug('Headers Key-Value Pairs:');
    for (var pair in headerPairs) {
      LogManager().debug('${pair['key']}: ${pair['value']}');
    }
    var params = Map<String, dynamic>.fromIterable(keyValuePairs,
        key: (pair) => pair['key'], value: (pair) => pair['value']);
    var headers =
        Map<String, dynamic>.fromIterable(headerPairs, // 将Header键值对转换为Map
            key: (pair) => pair['key'],
            value: (pair) => pair['value']);
    late var response;
    if (selectedMethod == 'GET') {
      response = await _httpRequest.get(url, data: params, headers: headers);
    } else {
      response = await _httpRequest.post(url, data: params, headers: headers);
    }
    LogManager().debug(response);
    if (response.statusCode == 200 && response.data['result'] == true) {
      setState(() {
        responseText = '请求成功：' + response.data['data'].toString();
      });
    } else {
      setState(() {
        responseText = '请求失败：$response';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('网络测试'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            TextField(
              decoration: InputDecoration(labelText: 'URL'),
              onChanged: (value) {
                setState(() {
                  url = value;
                });
              },
            ),
            SizedBox(height: 16.0),
            Row(
              children: [
                Expanded(
                  child: Text('请求参数:'),
                ),
                IconButton(
                  icon: Icon(Icons.add),
                  onPressed: addKeyValuePair,
                ),
              ],
            ),
            ListView.builder(
              physics: NeverScrollableScrollPhysics(), // 防止ListView滚动
              shrinkWrap: true, // 使ListView尽可能小
              itemCount: keyValuePairs.length,
              itemBuilder: (context, index) {
                return Row(
                  children: [
                    Expanded(
                      child: TextField(
                        decoration: InputDecoration(labelText: 'Key'),
                        onChanged: (value) {
                          setState(() {
                            keyValuePairs[index]['key'] = value;
                          });
                        },
                      ),
                    ),
                    SizedBox(width: 8.0),
                    Expanded(
                      child: TextField(
                        decoration: InputDecoration(labelText: 'Value'),
                        onChanged: (value) {
                          setState(() {
                            keyValuePairs[index]['value'] = value;
                          });
                        },
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.delete),
                      onPressed: () => removeKeyValuePair(index),
                    ),
                  ],
                );
              },
            ),
            SizedBox(height: 16.0),
            Row(
              children: [
                Expanded(
                  child: Text('请求Header:'),
                ),
                IconButton(
                  icon: Icon(Icons.add),
                  onPressed: addHeaderPair, // 添加Header键值对
                ),
              ],
            ),
            ListView.builder(
              physics: NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount: headerPairs.length,
              itemBuilder: (context, index) {
                return Row(
                  children: [
                    Expanded(
                      child: TextField(
                        decoration: InputDecoration(labelText: 'Header Key'),
                        onChanged: (value) {
                          setState(() {
                            headerPairs[index]['key'] = value;
                          });
                        },
                      ),
                    ),
                    SizedBox(width: 8.0),
                    Expanded(
                      child: TextField(
                        decoration: InputDecoration(labelText: 'Header Value'),
                        onChanged: (value) {
                          setState(() {
                            headerPairs[index]['value'] = value;
                          });
                        },
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.delete),
                      onPressed: () => removeHeaderPair(index), // 删除Header键值对
                    ),
                  ],
                );
              },
            ),
            SizedBox(height: 16.0),
            Text('请求方法:'),
            DropdownButton<String>(
              value: selectedMethod,
              onChanged: (value) {
                setState(() {
                  selectedMethod = value!;
                });
              },
              items: ['GET', 'POST'].map((String method) {
                return DropdownMenuItem<String>(
                  value: method,
                  child: Text(method),
                );
              }).toList(),
            ),
            SizedBox(height: 16.0),
            ElevatedButton(
              onPressed: sendRequest,
              child: Text('发送'),
            ),
            SizedBox(height: 16.0),
            Text(responseText, overflow: TextOverflow.clip),
          ],
        ),
      ),
    );
  }
}
