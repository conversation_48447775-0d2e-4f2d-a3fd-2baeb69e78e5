export class VidPlayerConfigGen {
    constructor() {
        this.configMap = new Map();
    }
    addPlayerConfigNumber(f42, g42) {
        if (f42 != null) {
            this.configMap.set(f42, g42);
        }
    }
    addPlayerConfigString(d42, e42) {
        if (d42 != null) {
            this.configMap.set(d42, e42);
        }
    }
    setPreviewTime(c42) {
        this.addPlayerConfigNumber("PreviewTime", c42);
    }
    setEncryptType(b42) {
        if (b42 == EncryptType.AliyunVodEncryption) {
            this.addPlayerConfigString("EncryptType", "AliyunVoDEncryption");
        }
        else if (b42 == EncryptType.HLSEncryption) {
            this.addPlayerConfigString("EncryptType", "HLSEncryption");
        }
        else if (b42 == EncryptType.Unencrypted) {
            this.addPlayerConfigString("EncryptType", "Unencrypted");
        }
    }
    setMtsHlsUriToken(a42) {
        this.addPlayerConfigString("MtsHlsUriToken", a42);
    }
    genConfig() {
        let w41 = {};
        if (this.configMap.size == 0) {
            return "";
        }
        this.configMap.forEach((y41, z41) => {
            if (z41 != undefined && y41 != undefined) {
                w41[z41] = y41;
            }
        });
        return JSON.stringify(w41);
    }
}
export var EncryptType;
(function (v41) {
    v41[v41["Unencrypted"] = 0] = "Unencrypted";
    v41[v41["AliyunVodEncryption"] = 1] = "AliyunVodEncryption";
    v41[v41["HLSEncryption"] = 2] = "HLSEncryption";
})(EncryptType || (EncryptType = {}));
