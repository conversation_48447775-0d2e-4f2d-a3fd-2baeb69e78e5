{"resolveConflictMode": true, "depName2RootPath": {"premierlibrary": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=\\oh_modules\\premierlibrary", "shared_preferences_ohos": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\shared_preferences_ohos@nadksbxq1mqjmuixnx2pjjqabwptj2cvju+p83h8xgu=\\oh_modules\\shared_preferences_ohos", "webview_flutter_ohos": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\webview_flutter_ohos@bfdx0tympjxsspo60mgoonn+aa7mmwytsvuwcpok25o=\\oh_modules\\webview_flutter_ohos", "path_provider_ohos": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\path_provider_ohos@rz08ypf+jhpfa5j6qopves7vsk91bavu8lb+8wfkzfa=\\oh_modules\\path_provider_ohos", "url_launcher_ohos": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\url_launcher_ohos@wlne140wohx1vkd8ccrurbfnnoly1pw5l2t4tlxnw60=\\oh_modules\\url_launcher_ohos", "image_picker_ohos": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\image_picker_ohos@ayxn+24xl+l1dzv0bylaz9kq36nfp36xzdpkk2rtp+a=\\oh_modules\\image_picker_ohos", "permission_handler_ohos": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\permission_handler_ohos@v1f9zvgtzwiwps6urt4cdkc0xmr4k3+ghajbhe99nii=\\oh_modules\\permission_handler_ohos", "device_info_plus": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\device_info_plus@v+2oszykcfti10hnkmmvwgy+solmmbsrwc3c4yk1uuo=\\oh_modules\\device_info_plus", "package_info_plus": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\package_info_plus@oddkx3spdpyogivhls6nikfvehqoth7jfcfacihwabg=\\oh_modules\\package_info_plus", "flutter_blue_plus_ohos": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\flutter_blue_plus_ohos@4df70qmo49oaztang7dsivr+mi1tpjfgwsjngar+e2u=\\oh_modules\\flutter_blue_plus_ohos", "flutter_blue_plus": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\flutter_blue_plus@toon5rvyx27suvcikxe8x+l7wbqukliqqpr24doow+o=\\oh_modules\\flutter_blue_plus", "iamgeqr_flutter_plugin": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\iamgeqr_flutter_plugin@1vjbuscikzvjpfjjpnjx0g8lpzpk+dylqndaetdipbe=\\oh_modules\\iamgeqr_flutter_plugin", "connectivity_plus": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\connectivity_plus@kqukugwl2nsdoqs00mqdcainj8gozua8ux6peiraxwi=\\oh_modules\\connectivity_plus", "mobile_scanner": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\mobile_scanner@fa18s1hwupccovgme0u7zrnan0ixc+q+mhbg2xvg46m=\\oh_modules\\mobile_scanner", "@pura/harmony-utils": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@pura+harmony-utils@1.2.4\\oh_modules\\@pura\\harmony-utils", "@bdmap/base": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@bdmap+base@1.2.7\\oh_modules\\@bdmap\\base", "@bdmap/search": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@bdmap+search@1.2.7\\oh_modules\\@bdmap\\search", "@bdmap/map": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@bdmap+map@1.2.7\\oh_modules\\@bdmap\\map", "open_app_settings": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\open_app_settings@yuq0uoolkhzidhegjyos7acnrx5+ddlclpefshq4g0g=\\oh_modules\\open_app_settings", "fluwx": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\fluwx@0luy3guwyen9n4kt97wftqotzdsl407ewqtntkbvyq0=\\oh_modules\\fluwx", "@tencent/wechat_open_sdk": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@tencent+wechat_open_sdk@1.0.14\\oh_modules\\@tencent\\wechat_open_sdk", "camera_ohos": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\camera_ohos@eoyi02pyjbzogdkwrnb0ry+85medqnonnkge8wnwk+0=\\oh_modules\\camera_ohos", "libpremierlibrary.so": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=\\oh_modules\\premierlibrary\\src\\main\\cpp\\types\\libpremierlibrary", "@acpm/acpm_ohos_pc": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=\\oh_modules\\@acpm\\acpm_ohos_pc", "@ohos/flutter_ohos": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=\\oh_modules\\@ohos\\flutter_ohos", "class-transformer": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\class-transformer@0.5.1\\oh_modules\\class-transformer", "@types/libbaidumapsdk_base_for_js_v1_0_0.so": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@bdmap+base@1.2.7\\oh_modules\\@bdmap\\base\\src\\main\\cpp\\type", "@bdmap/verify": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@bdmap+verify@1.0.2\\oh_modules\\@bdmap\\verify", "@types/libbaidumapsdk_map_for_js_v1_0_0.so": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@bdmap+map@1.2.7\\oh_modules\\@bdmap\\map\\src\\main\\cpp\\type", "@acpm/aio_crashsdk": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@acpm+aio_crashsdk@rynukgn6cor9bomrriugyxyd1z7zerm+gebyzjm0l0s=\\oh_modules\\@acpm\\aio_crashsdk", "@acpm/aio_util": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@acpm+aio_util@m7v9iv6jbihoz+rwxvd+y3p2msbrx6lznc1xn3kcclu=\\oh_modules\\@acpm\\aio_util", "@free/global": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@free+global@1.0.3\\oh_modules\\@free\\global", "@rongcloud/imkit": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@rongcloud+imkit@1.7.1\\oh_modules\\@rongcloud\\imkit", "@rongcloud/imlib": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@rongcloud+imlib@1.7.1\\oh_modules\\@rongcloud\\imlib", "@ohos/mqtt": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@ohos+mqtt@2.0.23\\oh_modules\\@ohos\\mqtt", "@ohos/hypium": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@ohos+hypium@1.0.18\\oh_modules\\@ohos\\hypium", "@charles/amrnbconverter": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@charles+amrnbconverter@1.0.1\\oh_modules\\@charles\\amrnbconverter", "librongimlib.so": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@rongcloud+imlib@1.7.1\\oh_modules\\@rongcloud\\imlib\\src\\main\\cpp\\types\\librongimlib", "libmqttasync.so": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@ohos+mqtt@2.0.23\\oh_modules\\@ohos\\mqtt\\src\\main\\cpp\\types\\libmqttasync", "libamrconverter.so": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@charles+amrnbconverter@1.0.1\\oh_modules\\@charles\\amrnbconverter\\src\\main\\cpp\\types\\libamrconverter"}, "depName2DepInfo": {"premierlibrary": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=\\oh_modules\\premierlibrary", "pkgName": "premierlibrary", "pkgVersion": "1.0.0"}, "shared_preferences_ohos": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\shared_preferences_ohos@nadksbxq1mqjmuixnx2pjjqabwptj2cvju+p83h8xgu=\\oh_modules\\shared_preferences_ohos", "pkgName": "shared_preferences_ohos", "pkgVersion": "1.0.0"}, "webview_flutter_ohos": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\webview_flutter_ohos@bfdx0tympjxsspo60mgoonn+aa7mmwytsvuwcpok25o=\\oh_modules\\webview_flutter_ohos", "pkgName": "webview_flutter_ohos", "pkgVersion": "1.0.0"}, "path_provider_ohos": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\path_provider_ohos@rz08ypf+jhpfa5j6qopves7vsk91bavu8lb+8wfkzfa=\\oh_modules\\path_provider_ohos", "pkgName": "path_provider_ohos", "pkgVersion": "1.0.0"}, "url_launcher_ohos": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\url_launcher_ohos@wlne140wohx1vkd8ccrurbfnnoly1pw5l2t4tlxnw60=\\oh_modules\\url_launcher_ohos", "pkgName": "url_launcher_ohos", "pkgVersion": "1.0.0"}, "image_picker_ohos": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\image_picker_ohos@ayxn+24xl+l1dzv0bylaz9kq36nfp36xzdpkk2rtp+a=\\oh_modules\\image_picker_ohos", "pkgName": "image_picker_ohos", "pkgVersion": "1.0.0"}, "permission_handler_ohos": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\permission_handler_ohos@v1f9zvgtzwiwps6urt4cdkc0xmr4k3+ghajbhe99nii=\\oh_modules\\permission_handler_ohos", "pkgName": "permission_handler_ohos", "pkgVersion": "1.0.0"}, "device_info_plus": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\device_info_plus@v+2oszykcfti10hnkmmvwgy+solmmbsrwc3c4yk1uuo=\\oh_modules\\device_info_plus", "pkgName": "device_info_plus", "pkgVersion": "1.0.0"}, "package_info_plus": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\package_info_plus@oddkx3spdpyogivhls6nikfvehqoth7jfcfacihwabg=\\oh_modules\\package_info_plus", "pkgName": "package_info_plus", "pkgVersion": "1.0.0"}, "flutter_blue_plus_ohos": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\flutter_blue_plus_ohos@4df70qmo49oaztang7dsivr+mi1tpjfgwsjngar+e2u=\\oh_modules\\flutter_blue_plus_ohos", "pkgName": "flutter_blue_plus_ohos", "pkgVersion": "1.0.0"}, "flutter_blue_plus": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\flutter_blue_plus@toon5rvyx27suvcikxe8x+l7wbqukliqqpr24doow+o=\\oh_modules\\flutter_blue_plus", "pkgName": "flutter_blue_plus", "pkgVersion": "1.0.0"}, "iamgeqr_flutter_plugin": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\iamgeqr_flutter_plugin@1vjbuscikzvjpfjjpnjx0g8lpzpk+dylqndaetdipbe=\\oh_modules\\iamgeqr_flutter_plugin", "pkgName": "iamgeqr_flutter_plugin", "pkgVersion": "1.0.0"}, "connectivity_plus": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\connectivity_plus@kqukugwl2nsdoqs00mqdcainj8gozua8ux6peiraxwi=\\oh_modules\\connectivity_plus", "pkgName": "connectivity_plus", "pkgVersion": "1.0.0"}, "mobile_scanner": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\mobile_scanner@fa18s1hwupccovgme0u7zrnan0ixc+q+mhbg2xvg46m=\\oh_modules\\mobile_scanner", "pkgName": "mobile_scanner", "pkgVersion": "1.0.0"}, "@pura/harmony-utils": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@pura+harmony-utils@1.2.4\\oh_modules\\@pura\\harmony-utils", "pkgName": "@pura/harmony-utils", "pkgVersion": "1.2.4"}, "@bdmap/base": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@bdmap+base@1.2.7\\oh_modules\\@bdmap\\base", "pkgName": "@bdmap/base", "pkgVersion": "1.2.7"}, "@bdmap/search": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@bdmap+search@1.2.7\\oh_modules\\@bdmap\\search", "pkgName": "@bdmap/search", "pkgVersion": "1.2.7"}, "@bdmap/map": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@bdmap+map@1.2.7\\oh_modules\\@bdmap\\map", "pkgName": "@bdmap/map", "pkgVersion": "1.2.7"}, "open_app_settings": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\open_app_settings@yuq0uoolkhzidhegjyos7acnrx5+ddlclpefshq4g0g=\\oh_modules\\open_app_settings", "pkgName": "open_app_settings", "pkgVersion": "1.0.0"}, "fluwx": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\fluwx@0luy3guwyen9n4kt97wftqotzdsl407ewqtntkbvyq0=\\oh_modules\\fluwx", "pkgName": "fluwx", "pkgVersion": "1.0.0"}, "@tencent/wechat_open_sdk": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@tencent+wechat_open_sdk@1.0.14\\oh_modules\\@tencent\\wechat_open_sdk", "pkgName": "@tencent/wechat_open_sdk", "pkgVersion": "1.0.14"}, "camera_ohos": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\camera_ohos@eoyi02pyjbzogdkwrnb0ry+85medqnonnkge8wnwk+0=\\oh_modules\\camera_ohos", "pkgName": "camera_ohos", "pkgVersion": "1.0.0"}, "libpremierlibrary.so": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=\\oh_modules\\premierlibrary\\src\\main\\cpp\\types\\libpremierlibrary", "pkgName": "libpremierlibrary.so", "pkgVersion": ""}, "@acpm/acpm_ohos_pc": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=\\oh_modules\\@acpm\\acpm_ohos_pc", "pkgName": "@acpm/acpm_ohos_pc", "pkgVersion": "1.0.0"}, "@ohos/flutter_ohos": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=\\oh_modules\\@ohos\\flutter_ohos", "pkgName": "@ohos/flutter_ohos", "pkgVersion": "1.0.0-a9e521ff88"}, "class-transformer": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\class-transformer@0.5.1\\oh_modules\\class-transformer", "pkgName": "class-transformer", "pkgVersion": "0.5.1"}, "@types/libbaidumapsdk_base_for_js_v1_0_0.so": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@bdmap+base@1.2.7\\oh_modules\\@bdmap\\base\\src\\main\\cpp\\type", "pkgName": "@types/libbaidumapsdk_base_for_js_v1_0_0.so", "pkgVersion": "1.0.0"}, "@bdmap/verify": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@bdmap+verify@1.0.2\\oh_modules\\@bdmap\\verify", "pkgName": "@bdmap/verify", "pkgVersion": "1.0.2"}, "@types/libbaidumapsdk_map_for_js_v1_0_0.so": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@bdmap+map@1.2.7\\oh_modules\\@bdmap\\map\\src\\main\\cpp\\type", "pkgName": "@types/libbaidumapsdk_map_for_js_v1_0_0.so", "pkgVersion": "1.0.0"}, "@acpm/aio_crashsdk": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@acpm+aio_crashsdk@rynukgn6cor9bomrriugyxyd1z7zerm+gebyzjm0l0s=\\oh_modules\\@acpm\\aio_crashsdk", "pkgName": "@acpm/aio_crashsdk", "pkgVersion": "1.20.0"}, "@acpm/aio_util": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@acpm+aio_util@m7v9iv6jbihoz+rwxvd+y3p2msbrx6lznc1xn3kcclu=\\oh_modules\\@acpm\\aio_util", "pkgName": "@acpm/aio_util", "pkgVersion": "2.1.0"}, "@free/global": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@free+global@1.0.3\\oh_modules\\@free\\global", "pkgName": "@free/global", "pkgVersion": "1.0.3"}, "@rongcloud/imkit": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@rongcloud+imkit@1.7.1\\oh_modules\\@rongcloud\\imkit", "pkgName": "@rongcloud/imkit", "pkgVersion": "1.7.1"}, "@rongcloud/imlib": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@rongcloud+imlib@1.7.1\\oh_modules\\@rongcloud\\imlib", "pkgName": "@rongcloud/imlib", "pkgVersion": "1.7.1"}, "@ohos/mqtt": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@ohos+mqtt@2.0.23\\oh_modules\\@ohos\\mqtt", "pkgName": "@ohos/mqtt", "pkgVersion": "2.0.23"}, "@ohos/hypium": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@ohos+hypium@1.0.18\\oh_modules\\@ohos\\hypium", "pkgName": "@ohos/hypium", "pkgVersion": "1.0.18"}, "@charles/amrnbconverter": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@charles+amrnbconverter@1.0.1\\oh_modules\\@charles\\amrnbconverter", "pkgName": "@charles/amrnbconverter", "pkgVersion": "1.0.1"}, "librongimlib.so": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@rongcloud+imlib@1.7.1\\oh_modules\\@rongcloud\\imlib\\src\\main\\cpp\\types\\librongimlib", "pkgName": "librongimlib.so", "pkgVersion": "1.7.1"}, "libmqttasync.so": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@ohos+mqtt@2.0.23\\oh_modules\\@ohos\\mqtt\\src\\main\\cpp\\types\\libmqttasync", "pkgName": "libmqttasync.so", "pkgVersion": ""}, "libamrconverter.so": {"pkgRootPath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\oh_modules\\.ohpm\\@charles+amrnbconverter@1.0.1\\oh_modules\\@charles\\amrnbconverter\\src\\main\\cpp\\types\\libamrconverter", "pkgName": "libamrconverter.so", "pkgVersion": "1.0.0"}}}