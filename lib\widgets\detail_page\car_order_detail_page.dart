import 'dart:async';
import 'dart:convert' as convert;
// import 'package:wuling_flutter_app/widgets/detail_page/car_advertise_detail_header_price.dart';

import 'dart:ui';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wuling_flutter_app/api/car_api.dart';
import 'package:wuling_flutter_app/api/common_api.dart';
import 'package:wuling_flutter_app/constant/constant.dart';
import 'package:wuling_flutter_app/constant/storage.dart';
import 'package:wuling_flutter_app/models/car/car_control_item_model.dart';
import 'package:wuling_flutter_app/models/global_data.dart';
import 'package:wuling_flutter_app/models/index.dart';
import 'package:wuling_flutter_app/page/base_page/base_page.dart';
import 'package:wuling_flutter_app/routes/jump_tool.dart';
import 'package:wuling_flutter_app/utils/app_config_util.dart';
import 'package:wuling_flutter_app/utils/manager/dialog_manager.dart';
import 'package:wuling_flutter_app/utils/manager/loading_manager.dart';
import 'package:wuling_flutter_app/utils/manager/log_manager.dart';
import 'package:wuling_flutter_app/utils/manager/login_manager.dart';
import 'package:wuling_flutter_app/utils/manager/notification_manager.dart';
import 'package:wuling_flutter_app/utils/sp_util.dart';
import 'package:wuling_flutter_app/widgets/common/custom_dialog.dart';
import 'package:wuling_flutter_app/widgets/common/custom_refresher.dart';
import 'package:wuling_flutter_app/widgets/common/custom_timer.dart';
// import 'package:wuling_flutter_app/widgets/detail_page/car_advertise_detail_container_image.dart';
import 'package:wuling_flutter_app/widgets/detail_page/car_advertise_detail_bottomNav.dart';
import 'package:wuling_flutter_app/widgets/detail_page/car_advertise_detail_comment.dart';
import 'package:wuling_flutter_app/widgets/detail_page/car_advertise_detail_header.dart';
import 'package:wuling_flutter_app/widgets/detail_page/car_advertise_header_price.dart';

import '../../widgets/detail_page/car_advertise_detail_container.dart';
import '../../widgets/detail_page/car_advertise_detail_grid_image.dart';
// import '../base_page/base_page.dart';
import '../../widgets/detail_page/car_advertise_size_selectiondialog.dart';
import '../../widgets/profile_page_widgets/profile_page_app_bar_button.dart';

class CarOrderDetailPage extends BasePage {
  final String commodityId;
  final String channelCode;
  final String urlPre;
  CarOrderDetailPage({
    Key? key,
    bool hideAppBar = true,
    bool isWithinSafeArea = false,
    required this.commodityId,
    required this.channelCode,
    required this.urlPre,
  }) : super(
    key: key,
    hideAppBar: hideAppBar,
    isWithinSafeArea: isWithinSafeArea,
  );
  @override
  CarOrderDetailPageState createState() => CarOrderDetailPageState();
}

class CarOrderDetailPageState extends BasePageState<CarOrderDetailPage> {
  final RefreshController _refreshController =
  RefreshController(initialRefresh: false);
  final log = LogManager();
  bool _isCarUser = false;
  bool _opacity = false;
  bool _color = true;
  bool _color_xq = false;
  bool _color_comm = false;

  // int type = type;
  bool _isAutoRefreshing = false;
  bool _isNeedCheckTire = false;
  bool _needShowCarControlPage = true;
  CarInfoModel? _carInfoModel;
  List<AdvertisePositionModel> _positionModelList = [];
  List<Advertise> _advertiseList = [];
  List<ImgText> _imgtiseList = [];
  List<String> _mainImageList = [];
  List<String> activityLabelList = [];
  List<String> _skuSizeList = [];


  AdvertiseCommodify? adverseCommodity;
  CommoditySkuModel? skuModel;

  ScrollController _scrollController = ScrollController();


  @override
  void initState() {
    super.initState();
    initConnectivity();
    double safeAreaTop = MediaQueryData.fromWindow(window).padding.top;
    _scrollController.addListener(() {
      // if(_scrollController.offset<1000){
      setState(() {
        _opacity = _scrollController.position.pixels <= safeAreaTop;
        // _color = _scrollController.position.pixels <= safeAreaTop;
      });
      // }
    });
    WidgetsBinding.instance.addPostFrameCallback((_) {
          // 在这里调用 requestRefresh 方法，确保 UI 渲染完成
        _refreshController.requestRefresh();

    });

  }

  // 初始化网络监听
  void initConnectivity() async {
    Connectivity().onConnectivityChanged.listen((ConnectivityResult result) {
      // setConnectionStatus(result);
    });
  }


  // </editor-fold>

  // <editor-fold desc="Refresher Methods">
  void _onRefresh() async {
    if (GlobalData().isLogin) {
      try {
        CarStatusResponseModel carStatusResponseModel = await carAPI.getDefaultCarInfoAndStatus();
        final carInfoModel = carStatusResponseModel.carInfo;
        final carStatusModel = carStatusResponseModel.carStatus;
        setState(() {
          _carInfoModel = carInfoModel;
          if (_carInfoModel != null) {
            String key =
                '${SP_USER_DEFAULT_CAR_KEY}_${GlobalData().userModel
                ?.userIdStr}';
            SpUtil().setJSON(key, _carInfoModel!.toJson());
            GlobalData().carInfoModel = _carInfoModel;
          }
          _isCarUser = _carInfoModel?.vin?.isNotEmpty ?? false;
        });
        // showAdverPage();
      }catch(e){
        if (_refreshController.isRefresh) {
          _refreshController.refreshCompleted();
        }
      }

    } else {
      setState(() {
        _isCarUser = false;
      });
      // getAdvertiseData();
    }
    showAdverPage();
    // showBuyPage();
  }


  void showAdverPage() async {
    try{
      Map<String, dynamic> map = {
        'channelCode': 0,
        'commodityId': widget.commodityId,
      };
      _imgtiseList.clear();
      LoadingManager.show(status: '获取数据中...');
      commonAPI.getCommodityDetail(map).then((commodity){
        setState(() {
            adverseCommodity =  commodity;
            List<dynamic> rotationImages = convert.jsonDecode(commodity.lmCommodityAsDetail.commodityRotationImage);
            List<dynamic>?  activityLabelList = convert.jsonDecode(commodity.promotionActivity.activityLabelList.toString());
            activityLabelList = activityLabelList;
            LogManager().log('activityLabelList ${activityLabelList}');

            LogManager().log('rotationImages ${rotationImages.toString()}');
            for (var r in commodity.lmCommodityAsDetail.rotationImages) {
              _mainImageList.add(r.mainImg);
            }
            List<dynamic> imageList = convert.jsonDecode(commodity.lmCommodityAsDetail.commodityDetailContent);
            for (var r in imageList) {
              if(r["img"] != null) {
                ImgText img = ImgText(
                    img: r["img"], width: r["height"], height: r["width"]);
                LogManager().log('getAdvertisePositionList ${img.img}');
                _imgtiseList.add(img);
              }
            }
          if (_refreshController.isRefresh) {
            _refreshController.refreshCompleted();
          }
        });
        // LoadingManager.showError('获取失败');
        LoadingManager.dismiss();
      }).onError((error, stackTrace){
        LoadingManager.showError('获取失败');
        LoadingManager.dismiss();
        LogManager().log('getAdvertisePositionList ${error.toString()}');
        if (_refreshController.isRefresh) {
          _refreshController.refreshCompleted();
        }
      });
    }catch(e)
    {
      LogManager().error('$e');
    }
  }


  void showBuyPage() async {
    try{
      Map<String, dynamic> map = {
        'channelCode': 0,
        'commodityId': widget.commodityId,
       };
      LoadingManager.show(status: '获取数据中...');
       commonAPI.getCommoditySkuList(map).then((sku){
          setState(() {
            skuModel = sku;
           if(skuModel!=null){
             LoadingManager.dismiss();
             // showBuyAlertDialog(context);
             LoadingManager.showToast('功能开发中 敬请期待');
           }
          });
        }).onError((error, stackTrace){
        LoadingManager.showError('获取失败');
         LoadingManager.dismiss();
        LogManager().log('showBuyPage ${error.toString()}');
       });
    }catch(e)
    {
      LogManager().error('$e');
    }
  }

  void getAdvertiseData() async{
    try{
      commonAPI.getAdvertisePositionList(2).then((List<AdvertisePositionModel> list){
        setState(() {
          // _positionModelList = list;
        });
      }).onError((error, stackTrace){
        LogManager().log('getAdvertisePositionList ${error.toString()}');
      });

      if (_refreshController.isRefresh) {
        _refreshController.refreshCompleted();
      }
    }catch(e){
      if (_refreshController.isRefresh) {
        _refreshController.refreshCompleted();
      }
    }
  }

  void _onLoading() async {
    if (GlobalData().isLogin) {
      if (_refreshController.isLoading) {
        await Future.delayed(const Duration(milliseconds: 1000));
        _refreshController.loadNoData();
      }
    } else {
      if (_refreshController.isLoading) {
        await Future.delayed(const Duration(milliseconds: 1000));
        _refreshController.loadNoData();
      }
    }
  }

  void handleError(String message) {
    // 处理错误
    log.info("请求错误: $message");
    if (_refreshController.isRefresh) {
      _refreshController.refreshCompleted();
    }
  }



  void fetchDefaultCarStatus({
    required Function({
    CarStatusModel? carStatus,
    })
    onSuccess,
    required Function(String) onError,
  }) async {
    if (_isAutoRefreshing) {
      log.info('自动刷新接口还在请求中，取消本次请求');
      return;
    }
    _isAutoRefreshing = true;
    try {
      // final carStatusResult = await CarAPI.fetchDefaultCarInfoAndStatus();
      // final carStatus = carStatusResult.data?['carStatus'];
      CarStatusResponseModel carStatusResponseModel = await carAPI.getDefaultCarInfoAndStatus();
      final carStatus = carStatusResponseModel.carStatus;
      _isAutoRefreshing = false;
      log.info('自动刷新接口请求成功');
      onSuccess(carStatus: carStatus);
    } catch (e) {
      _isAutoRefreshing = false;
      log.info('自动刷新接口请求出现异常');
      onError(e is Exception ? e.toString() : '发生未知错误');
    }
  }

  void jumpToWebViewPageWithServiceModel(CarServiceModel serviceModel, bool needTitleBar) {
    String url = CarControlItemModel.getCarServiceSkipTargetUrlForServiceModel(
        serviceModel: serviceModel,
        insideParamsMap: GlobalData().insideParamsMap);
    jumpToWebViewPage(url, needTitleBar);
  }

  void jumpToWebViewPage(String url, bool needTitleBar) {
    JumpTool().openWeb(context, url, needTitleBar);
  }

  void _onButtonTab() {
    if (!GlobalData().isLogin) {
      LoginManager().showLoginModal();
    } else {
      NotificationManager().postNotification(
          Constant.NOTIFICATION_MAIN_PAGE_SELECT_PAGE,
          userInfo: {'pageIndex': 1});
    }
  }

  void _scrollListener() {

    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      /// 触发上拉加载更多机制
    }
  }

  @override
  void pageInitState() {
    double screenHeight = MediaQueryData.fromWindow(window).size.height;
    double safeAreaBottom = MediaQueryData.fromWindow(window).padding.bottom;
    double safeAreaTop = MediaQueryData.fromWindow(window).padding.top;
    double height = screenHeight - safeAreaBottom - 56;

    _scrollController.addListener(_scrollListener);

    // setupNotification();
    // 延时执行是因为要等待页面构建完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      String key =
          '${SP_USER_DEFAULT_CAR_KEY}_${GlobalData().userModel?.userIdStr}';
      var carInfoMap = SpUtil().getJSON(key);
      if(carInfoMap != null){
        setState(() {
          // CarInfoModel carInfo = CarInfoModel.fromJson(carInfoMap);
          // _carInfoModel = carInfo;
          // _isCarUser = true;
          // GlobalData().carInfoModel = _carInfoModel;
         // _refreshController.requestRefresh();

        });
       }
      });
  }

  bool showCarControlPage(){
    if(GlobalData().isLogin && _isCarUser && _needShowCarControlPage){
      return true;
    }
    return false;
  }

  List<Widget> getWidgetList(){
    List<Widget> list = [];
    changeStatusBarBrightness(Brightness.light);

    // list.add(CarAdvertiseDetailContainer(onDraft: _getButtonOnPressed));
    if(_mainImageList.length>0){
      list.add(CarAdvertiseDetailHeader(positionModelList: _positionModelList, banner: _mainImageList,urlPre: widget.urlPre,));
    }

    if(adverseCommodity?.commodityName!=null){
      list.add(CarAdvertiseDetailHeaderPrice(positionModelList: _positionModelList, adverseCommodity: adverseCommodity!));
    }
    list.add(CarAdvertiseDetailContainer(onDraft: _getButtonOnPressed));
    list.add(const SizedBox(height: 10,));
    list.add(CarAdvertiseDetailGridImage(imgTextList: _imgtiseList,httpPre: widget.urlPre,));
    list.add(CarAdvertiseDetailComment(onDraft: _getButtonOnPressed));
    list.add(const SizedBox(height: 50,));
    return list;
  }
  //选项尺码页面
  VoidCallback? _getBudOnPressed() {
    if (!GlobalData().isLogin) {
      showNotLoginAlertDialog(context);
     }else {
      if(skuModel!=null){
        // showBuyAlertDialog(context);
        LoadingManager.showToast('功能开发中 敬请期待');
       }else {
        showBuyPage();
      }
    }
  }

  VoidCallback? _getButtonOnPressed() {
     if (!GlobalData().isLogin) {
       showNotLoginAlertDialog(context);
      }else {
       LoadingManager.showToast('功能开发中 敬请期待');
     }
   }
  @override
  Widget buildPageContent(
      BuildContext context,
      ) {
    // 实现UseCarPage特定的页面内容
    double safeAreaTop = MediaQueryData.fromWindow(window).padding.top;
    double safeAreaBottom = MediaQueryData.fromWindow(window).padding.bottom;

    return Stack(
      children: [
         Column(
          children: [
            Expanded(
              child:
              Stack(
                children: [
                  CustomSmartRefresher(
                    controller: _refreshController,
                    onRefresh: _onRefresh,
                    onLoading: _onLoading,
                    child: ListView.builder(itemBuilder:
                        (BuildContext context, int index) {
                        return  getWidgetList()[index];
                     },
                      controller: _scrollController,
                      shrinkWrap: true,
                      itemCount: getWidgetList().length, // 共20个列表项
                    ),
                  ),
                 ],
              ),
            ),

          ],
        ),
        setCustomAppBar(),
        Positioned(
          height: safeAreaBottom + 90,
          bottom: 0,
          left: 0,
          right: 0,
          child: setBottomBarActions(),
        ),
      ],
    );

  }




  @override
  void dispose() {
    _refreshController?.dispose();
    _scrollController?.dispose();
    super.dispose();
  }


  Widget  setCustomAppBar() {
    double safeAreaTop = MediaQueryData.fromWindow(window).padding.top;
    return Container(
      height: safeAreaTop + 50,
      color: Colors.white.withOpacity(_opacity == true? 0 : 1),
      // color: Colors.red,
      child: Visibility(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
           children: <Widget>[
            SizedBox(width: 10,),
            Visibility(visible:
            true, child:
            TextButton(
                style: ButtonStyle(
                  padding: MaterialStateProperty.all(EdgeInsets.only(left: 0.0, top: safeAreaTop-10)),
                  // minimumSize: MaterialStateProperty.all(Size(30, 50)),
                ),
                child: Icon(
                  Icons.arrow_back, color: Colors.black, size: 30.0,
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                }
              ),
            ),
           Expanded(
             child:
             Row(
               mainAxisAlignment: MainAxisAlignment.center,
               children: [
                 Container(
                   margin:EdgeInsets.only(top:40,bottom: 10) ,
                   // color: Colors.red,
                   width: _opacity == true? 0 : 50,

                   child: TextButton(
                       style: TextButton.styleFrom(
                         foregroundColor:_color == true? Colors.black:Colors.grey,
                       ),
                       child: Text(
                            '商品',
                           style: TextStyle(
                           fontWeight: FontWeight.bold, // 字体加粗
                           fontSize: 15, // 字体大小设置为 20
                         ),
                       ),
                       onPressed: () {
                         setState(() {
                           _color_xq = false;
                           _color = true;
                           _color_comm = false;
                         });
                         _jumpToIndex(0);
                        }
                   ),
                 ),
                 Container(
                   margin:EdgeInsets.only(top:40,bottom: 10) ,
                   width: _opacity == true? 0 : 50,
                   // color: Colors.red,
                   child: TextButton(
                     style: TextButton.styleFrom(
                       foregroundColor:_color_xq == true? Colors.black:Colors.grey,
                     ),
                     child: const Text(
                       '详情',
                       style: TextStyle(
                       fontWeight: FontWeight.bold, // 字体加粗
                       fontSize: 15, // 字体大小设置为 20
                     ),
                     ),
                     onPressed: () {
                       setState(() {
                         _color_xq = true;
                         _color_comm = false;
                         _color = false;
                       });
                       _jumpToIndex(2);
                     },
                   ),
                 ),
                 Container(
                   margin:EdgeInsets.only(top:40,bottom: 10) ,
                   width: _opacity == true? 0 : 50,
                   // color: Colors.red,
                   child: TextButton(
                     style: TextButton.styleFrom(
                       foregroundColor:_color_comm == true? Colors.black:Colors.grey,
                     ),
                     child: Text(
                       '评价',
                       style: TextStyle(
                         fontWeight: FontWeight.bold, // 字体加粗
                         fontSize: 15, // 字体大小设置为 20
                       ),
                     ),
                     onPressed: () {
                       setState(() {
                         _color_comm = true;
                         _color_xq = false;
                         _color = false;
                       });
                       _jumpToIndex(3);
                     },
                   ),
                 ),
                 // Container(
                 //   margin:EdgeInsets.only(top:40,bottom: 10) ,
                 //   width: _opacity == true? 0 : 50,
                 //   // color: Colors.red,
                 //   child: TextButton(
                 //     style: TextButton.styleFrom(
                 //       foregroundColor:_color == true? Colors.black:Colors.grey,
                 //     ),
                 //     child: Text(
                 //       '推荐',
                 //       style: TextStyle(
                 //         fontWeight: FontWeight.bold, // 字体加粗
                 //         fontSize: 15, // 字体大小设置为 20
                 //       ),
                 //     ),
                 //     onPressed: () {
                 //       _jumpToIndex(4);
                 //     },
                 //   ),
                 // ),
               ],
             ),
           ),
             SizedBox(width: 70), // 设置一个较小的宽度
             // Spacer(),
           ],

        ),
        // ),
      ),
    );
  }
  void _jumpToIndex(int index) {
    double offset = 0;
       if(index == 1)
        {
        offset = index * 150.0;
       }else if(index==1){
         offset = index * 150.0;
       }else if(index==2){
         offset = index * 300.0;
        }else if(index==3){
         offset = index * 7 * AppConfigUtil.screenHeight;
       }
    _scrollController.jumpTo(offset);
  }
  Widget  AppBarListAction() {
    return Padding(
      padding: const EdgeInsets.only(top: 40.0),
      child: Container(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: <Widget>[
            SizedBox(width: 20,),
            TextButton(
                child: const Text('评价'),
                style: TextButton.styleFrom(
                  foregroundColor: Colors.white, // 设置按钮前景颜色为蓝色
                  // 设置按钮前景颜色为蓝色
                ),
                onPressed: () {
                  LogManager().debug('Button was pressed!');
                }
            ),
            Expanded(child:

            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Container(
                  width: 70,
                  height: 40,

                  margin:EdgeInsets.only(left: 0) ,
                  child: TextButton(
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.white, // 设置按钮前景颜色为蓝色
                      ),
                      child: const Text('详情'),
                      onPressed: () {
                        LogManager().debug('Button was pressed!');
                      }
                  ),
                ),
                // SizedBox(width: 10,),
                Container(
                  width: 50,
                  height: 40,

                  // margin:EdgeInsets.only(right: 20) ,
                  // color: Colors.red,
                  child: TextButton(
                      style: TextButton.styleFrom(
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('配置'),
                      onPressed: () {
                        LogManager().debug('Button was pressed!');
                      }
                  ),
                ),
              ],
            )
            )
          ],
        ),
        // ),
      ),
    );

  }
  List<Widget>? AppBarAction() {
    double buttonHeight = 54;
    return [
      Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          AppBarButton(
            visible: GlobalData().isLogin?false:true,
            imageUrl: 'assets/images/profile_page/my_new_scanf.png',
            text: '扫描',
            onPress: ({required BuildContext context}){
              LogManager().log('扫描响应');
            },
          ),
          AppBarButton(
            imageUrl: 'assets/images/profile_page/my_customer_service.png',
            text: '客服',
            onPress: ({required BuildContext context}){
            },
          ),
          const SizedBox(width: 10,)
        ],
      )
    ];

  }


  Widget setBottomBarActions() {
    double buttonHeight = 54;
    double safeAreaBottom = MediaQueryData.fromWindow(window).padding.bottom;
    double safeAreaTop = MediaQueryData.fromWindow(window).padding.top;

    return Container(
      // margin: EdgeInsets.only(bottom:safeAreaBottom),
      height: safeAreaTop + safeAreaBottom ,
      child:  CarAdvertiseDetailBottomNav(advertise: adverseCommodity,onBuy: _getBudOnPressed,),
    );
  }


  void showBuyAlertDialog(BuildContext context) {
    double safeAreaBottom = MediaQueryData.fromWindow(window).padding.bottom;
    double safeAreaTop = MediaQueryData.fromWindow(window).padding.top;
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: false,
      // backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Container(
          color: Colors.white, // 设置背景颜色
          height: MediaQuery.of(context).size.height-safeAreaTop, // 设置容器高度为屏幕高度
          child: CarAdvertiseSizeSelectionDialog(sku: skuModel,),
        );
      },
    );
  }

  void showNotLoginAlertDialog(BuildContext context) {
    DialogManager().showCustomDialog(
      CustomDialog(
        content: "您还没有登录",
        contentFontSize: 18,
        contentColor: Colors.black,
        buttonHeight: 70,
        horizontalPadding: 20,
        buttons: [
          DialogButton(
              label: "继续逛逛",
              onPressed: () {
                LogManager().debug("取消按钮被点击");
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xFF9B9DA9)),
          DialogButton(
              label: "去登录",
              onPressed: () {
                LoginManager().showLoginModal();
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xffea0029)),
        ],
      ),
    );
  }

// </editor-fold>

}


