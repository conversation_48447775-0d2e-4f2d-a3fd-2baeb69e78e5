// @keepTs
// @ts-nocheck
/**
 * 音频播放
 * <AUTHOR>
 * @since 2024-10-16
 */
import { media } from '@kit.MediaKit';
/**
 * 高清语音播放
 */
declare class AVPlayer {
    private count;
    private isSeek;
    private static instance;
    private avPlayer;
    private playId;
    private listeners;
    static getInstance(): AVPlayer;
    setAVPlayer(b340: media.AVPlayer): void;
    private notifyChanged;
    /**
     * 停止播放音频
     */
    stopAudio(): void;
    /**
     * 根据消息ID跟当前播放的消息ID对比，获取当前播放状态
     * @param msgId 消息ID
     * @returns 消息ID对应的播放状态。如果当前没有消息在播放或者播放的不是当前消息，则返回 initialized 。
     */
    getMessagePlayState(v339: number): string;
    /**
     * 播放本地音频
     * @param fileName
     * @returns
     */
    playAudio(r339: string, s339: number): Promise<void>;
    addListener(q339: AVPlayerListener): void;
    removeListener(p339: AVPlayerListener): void;
}
/**
 * AVPlayer播放回调
 */
interface AVPlayerListener {
    /**
     * 播放状态回调
     */
    onPlayStateChanged?: (state: string, playId: number) => void;
}
/**
 * 播放事件
 */
declare enum AVPlayerEvent {
    /**
     * 播放
     */
    PLAY = 0,
    /**
     * 暂停
     */
    PAUSE = 1
}
export { AVPlayer, AVPlayerListener, AVPlayerEvent };
