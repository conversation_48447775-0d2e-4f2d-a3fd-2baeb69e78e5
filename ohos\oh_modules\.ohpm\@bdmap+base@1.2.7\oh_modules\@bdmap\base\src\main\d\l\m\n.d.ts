import HttpCall from "./o";         export declare class Dispatcher { private maxRequests; private runningSyncCalls; private readyAsyncCalls; private runningAsyncCalls;         setMaxRequestCount(max: number): Promise<void>;         getMaxRequestCount(): number;         getMaxRequestPreHostCount(): number;         executed(call: HttpCall): void;         enqueue(call: HttpCall): void;       finished(call: HttpCall, g6: boolean): void;                         private finishedAll;                 private promoteAndExecute;         getReadyCallsCount(): number;         getRunningCallsCount(): number; } export default Dispatcher; 