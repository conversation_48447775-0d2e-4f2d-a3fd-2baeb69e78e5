// @keepTs
// @ts-nocheck
/**
 * Created on 2025/03/04
 * <AUTHOR>
 */
import { Message } from '@rongcloud/imlib';
import { ArrayList } from '@kit.ArkTS';
import taskPool from '@ohos.taskpool';
import { FileDownloadListener } from './listener/FileDownloadListener';
/**
 * 高清语音消息预下载逻辑
 * @version 1.4.0
 */
declare class HQVoiceMsgDownloadManager {
    private static instance;
    private networkType;
    private foreground;
    private normalPriorityQueue;
    private highPriorityQueue;
    private taskMap;
    private downloadTask;
    private downloadListeners;
    private messageReceivedListener;
    private monitorListener;
    private constructor();
    static getInstance(): HQVoiceMsgDownloadManager;
    init(): void;
    addHQMsgDownloadListener(y103: FileDownloadListener): void;
    removeHQMsgDownloadListener(x103: FileDownloadListener): void;
    getHQMsgDownloadListener(): ArrayList<FileDownloadListener>;
    enqueueArray(t103: HQMsgDownloadModel[]): void;
    enqueue(p103: HQMsgDownloadModel): void;
    private dispatch;
    private startDownload;
    download(w102: Message, x102: FileDownloadListener): void;
    private notifySuccess;
    private notifyFailed;
    cancelDownload(): Promise<void>;
    isHQMessageDownload(l102: Message): boolean;
}
/**
 * 下载模型。
 */
declare class HQMsgDownloadModel {
    message: Message;
    priority: taskPool.Priority;
    retryTimes: number;
    constructor(j102: Message, k102: taskPool.Priority);
    maxRetryTimes(): boolean;
}
/**
 * 播放图标SVG
 */
declare const PLAY_ICON = "\n<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n  <circle cx=\"12\" cy=\"12\" r=\"11\" stroke=\"currentColor\" stroke-width=\"2\"/>\n  <path d=\"M10 8.5C10 7.94772 10.4477 7.5 11 7.5C11.5523 7.5 12 7.94772 12 8.5V15.5C12 16.0523 11.5523 16.5 11 16.5C10.4477 16.5 10 16.0523 10 15.5V8.5Z\" fill=\"currentColor\"/>\n  <path d=\"M13 8.5C13 7.94772 13.4477 7.5 14 7.5C14.5523 7.5 15 7.94772 15 8.5V15.5C15 16.0523 14.5523 16.5 14 16.5C13.4477 16.5 13 16.0523 13 15.5V8.5Z\" fill=\"currentColor\"/>\n</svg>\n";
/**
 * 暂停图标SVG
 */
declare const PAUSE_ICON = "\n<svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n  <circle cx=\"12\" cy=\"12\" r=\"11\" stroke=\"currentColor\" stroke-width=\"2\"/>\n  <path d=\"M9.5 8.5C9.5 7.94772 10.0523 7.5 10.5 7.5C10.9477 7.5 11.5 7.94772 11.5 8.5V15.5C11.5 16.0523 10.9477 16.5 10.5 16.5C10.0523 16.5 9.5 16.0523 9.5 15.5V8.5Z\" fill=\"currentColor\"/>\n  <path d=\"M13.5 8.5C13.5 7.94772 14.0523 7.5 14.5 7.5C14.9477 7.5 15.5 7.94772 15.5 8.5V15.5C15.5 16.0523 14.9477 16.5 14.5 16.5C14.0523 16.5 13.5 16.0523 13.5 15.5V8.5Z\" fill=\"currentColor\"/>\n</svg>\n";
export { HQVoiceMsgDownloadManager, HQMsgDownloadModel, taskPool, PLAY_ICON, PAUSE_ICON };
