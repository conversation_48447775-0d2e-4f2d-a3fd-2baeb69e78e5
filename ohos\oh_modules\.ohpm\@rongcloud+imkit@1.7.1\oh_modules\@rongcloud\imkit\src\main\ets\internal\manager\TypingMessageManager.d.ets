// @keepTs
// @ts-nocheck
/**
 * Created on 2025/07/14
 * TypingMessageManager 负责“正在输入(Typing)”功能的完整生命周期管理。
 * 1. 发送端限流：同一会话 6s 内只发一次 TypingStatusMessage。
 * 2. 接收端维护：记录谁在输入，超时自动消失；若收到真实消息即清理对应用户状态。
 */
import { ConversationIdentifier, TypingStatusListener } from '../../../../../Index';
export declare class TypingMessageManager {
    /** key 拼接分隔符 */
    private static readonly SEPARATOR;
    /** 默认消失时间 (ms) */
    private static readonly DISAPPEAR_INTERVAL;
    /** 支持 Typing 的会话类型 */
    private static readonly typingSupportTypes;
    private static instance;
    /**
     * key: 会话维度标识 → "conversationType;;;targetId;;;channelId"
     * 此 Map 只保存 TypingStatus，不混入定时器信息，以保持对外模型干净且避免误用。
     */
    private typingMap;
    /**
     * cancelTypingMap —— 定时器缓存
     * key: token = 会话 key + SEPARATOR + userId
     * 避免让 UI 侧误接触定时器信息。
     */
    private cancelTypingMap;
    /** sendingConversation：发送端限流 Map */
    private sendingConversation;
    /** 输入状态监听 */
    private typingStatusListener?;
    /** 子频道输入状态监听【超级群】*/
    private conversationChannelTypingStatusListener?;
    /** 实际生效的消失时间 */
    private disappearInterval;
    /** 收消息监听 */
    private messageReceivedListener?;
    /** lib TypingStatus 监听 */
    private libTypingStatusListener?;
    static getInstance(): TypingMessageManager;
    init(): void;
    /** 设置 TypingStatus 单聊会话监听 */
    addTypingMessageStatusListener(y318: TypingStatusListener): void;
    /** 移除 TypingStatus 单聊会话监听 */
    removeTypingMessageStatusListener(): void;
    /** 设置子频道输入状态监听【超级群】 */
    addConversationChannelTypingStatusListener(x318: TypingStatusListener): void;
    /** 移除子频道输入状态监听【超级群】 */
    removeConversationChannelTypingStatusListener(): void;
    /** 发送 TypingStatusMessage，并做 6s 防抖 */
    sendTypingMessage(t318: ConversationIdentifier, u318: string): void;
    /** 主动结束输入，移除限流 key */
    removeTypingEnd(r318: ConversationIdentifier): void;
    /** 注册 lib 核心监听 */
    private registerCoreListeners;
    /** 处理 TypingStatusMessage */
    private handleTypingStatusChange;
    /** 收到普通消息后，立即移除 sender 的 Typing 状态 */
    private handleReceiveOtherMessage;
    /** 启动 / 刷新自动消失定时器 */
    private startDisappearTimer;
    /** 通知输入状态监听 */
    private notifyStatusListener;
    /** 将 Map 转换为 List */
    private convertMapToList;
    /** 获取会话 key */
    private obtainKey;
    /** 获取定时器 token */
    private obtainToken;
}
