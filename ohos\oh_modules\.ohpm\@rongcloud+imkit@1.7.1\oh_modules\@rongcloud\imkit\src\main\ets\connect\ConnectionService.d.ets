// @keepTs
// @ts-nocheck
import { ConnectionStatus, ConnectionStatusListener, IAsyncResult } from '@rongcloud/imlib';
/**
 * 连接服务
 * @version 1.0.0
 */
export interface ConnectionService {
    /**
     * 增加连接状态监听
     * @param listener 监听
     * @warning 请勿使用 IMEngine 的连接状态监听，否则该监听会失效
     * @warning addConnectionStatusListener & removeConnectionStatusListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     */
    addConnectionStatusListener(listener: ConnectionStatusListener): void;
    /**
     * 移除连接状态监听
     * @param listener 监听
     * @warning addConnectionStatusListener & removeConnectionStatusListener 配合使用，避免内存泄露
     */
    removeConnectionStatusListener(listener: ConnectionStatusListener): void;
    /**
     * 获取当前用户 Id
     * @returns 用户 Id，连接成功后返回有效的用户 Id。未连接成功返回空字符串
     */
    getCurrentUserId(): string;
    /**
     * 获取当前连接状态
     * @returns 连接状态
     */
    getCurrentConnectionStatus(): Promise<IAsyncResult<ConnectionStatus>>;
}
