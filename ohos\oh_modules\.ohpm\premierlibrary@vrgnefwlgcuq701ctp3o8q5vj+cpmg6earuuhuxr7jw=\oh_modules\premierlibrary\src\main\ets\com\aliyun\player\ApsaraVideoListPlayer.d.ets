import { AliListPlayer } from './AliListPlayer';
import { AliPlayer } from './AliPlayer';
import { UrlVideoListPlayer } from './UrlVideoListPlayer';
import { Context } from '@ohos.abilityAccessCtrl';
import { ListPlayerBase } from './nativeclass/ListPlayerBase';
import { IPlayer } from './IPlayer';
export declare class ApsaraVideoListPlayer extends UrlVideoListPlayer implements AliListPlayer, AliPlayer {
    private mmLog;
    private mSaasVidePlayer;
    private mSaaSPrerenderPlayer;
    constructor(o10: Context, p10: string);
    protected createListPlayer(k10: Context, l10: string, m10: number, n10: number): ListPlayerBase;
    protected getNativePlayerWithContext(i10: Context, j10: string): IPlayer;
    protected getPrerenderPlayerWithContext(g10: Context, h10: string): IPlayer;
    protected getCurrentPlayerIndex(): number;
    stop(): void;
    getPreRenderPlayer(): IPlayer | undefined;
    /**
     * 设置auth源
     *
     * @param auth auth源
     */
    setVidAuthDataSource(a10: object): void;
    /**
     * 设置sts源
     *
     * @param sts sts源
     */
    setVidStsDataSource(y9: object): void;
    /**
     * 设置mps源
     *
     * @param mps mps源
     */
    setVidMpsDataSource(w9: object): void;
    /**
     * 设置liveSts源
     *
     * @param liveSts liveSts源
     */
    setLiveStsDataSource(u9: object): void;
    updateVidAuth(s9: object): void;
    updateStsInfo(q9: object): void;
}
