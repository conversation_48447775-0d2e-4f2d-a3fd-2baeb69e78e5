import { InstanceManager } from './InstanceManager';
import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import { Reply, FileChooserParamsFlutterApi } from "./GeneratedOhosWebView";
export declare class FileChooserParamsFlutterApiImpl extends FileChooserParamsFlutterApi {
    private instanceManager;
    constructor(binaryMessenger: BinaryMessenger, instanceManager: InstanceManager);
    private toFileChooserEnumData;
    /**
     * Stores the FileChooserParams instance and notifies <PERSON><PERSON> to create a new FileChooserParams
     * instance that is attached to this one.
     */
    create(instance: FileSelectorParam, callback: Reply<void>): void;
}
