import { AbilityAware, AbilityPluginBinding, FlutterPlugin, FlutterPluginBinding, MethodCall, MethodCallHandler, MethodResult } from '@ohos/flutter_ohos';
/**
 * OpenAppSettingsPlugin
 */
export declare class OpenAppSettingsPlugin implements AbilityAware, FlutterPlugin, MethodCallHandler {
    private channel;
    private ability;
    private context;
    onAttachedToEngine(binding: FlutterPluginBinding): void;
    onAttachedToAbility(binding: AbilityPluginBinding): void;
    onReattachedToAbilityForConfigChanges(binding: AbilityPluginBinding): void;
    onDetachedFromAbilityForConfigChanges(): void;
    onDetachedFromAbility(): void;
    getUniqueClassName(): string;
    openSettings(url: string): void;
    openAppSettings(): void;
    onMethodCall(call: MethodCall, result: MethodResult): void;
    onDetachedFromEngine(binding: FlutterPluginBinding): void;
}
