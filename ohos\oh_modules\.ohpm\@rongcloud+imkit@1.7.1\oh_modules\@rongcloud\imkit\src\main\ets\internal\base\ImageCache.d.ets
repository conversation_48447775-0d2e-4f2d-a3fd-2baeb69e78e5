// @keepTs
// @ts-nocheck
/**
 * 图片下载缓存管理
 */
export declare class ImageCache {
    private static instance;
    private imageCache;
    private imageErrorCache;
    /**
     * 单例方法
     * @returns 核心类
     */
    static getInstance(): ImageCache;
    get(x53: string): string | undefined;
    put(v53: string, w53: string): void;
    hasError(u53: string): boolean;
    putError(t53: string): void;
    clear(): void;
}
