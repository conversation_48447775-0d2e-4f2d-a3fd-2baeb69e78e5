import { ErrorInfo } from './bean/ErrorInfo';
import { InfoBean } from './bean/InfoBean';
import { MediaInfo } from './nativeclass/MediaInfo';
import { PlayerConfig } from './nativeclass/PlayerConfig';
import { TrackInfo, TrackType } from './nativeclass/TrackInfo';
import { CacheConfig } from './nativeclass/CacheConfig';
export declare const SET_MEDIA_TYPE: number;
export declare const ALLOW_PRE_RENDER: number;
export declare const PLAYED_DURATION_INCLUDE_SPEED = 3;
/**
 * 未知状态
 */
/****
 * Unknown status.
 */
export declare const unKnow: number;
/**
 * 空状态。刚创建出来的状态。
 */
/****
 * Blank. The player enters this state after it is created.
 */
export declare const idle: number;
/**
 * 初始化了的状态，设置播放源之后的状态
 */
/****
 * Initialized. The player enters this state after a media source is specified for the player.
 */
export declare const initalized: number;
/**
 * 准备成功的状态
 */
/****
 * Prepared.
 */
export declare const prepared: number;
/**
 * 正在播放的状态
 */
/****
 * The player is playing video or audio.
 */
export declare const started: number;
/**
 * 播放已暂停的状态
 */
/****
 * The player is paused.
 */
export declare const paused: number;
/**
 * 播放已停止的状态
 */
/****
 * The player is stopped.
 */
export declare const stopped: number;
/**
 * 播放完成状态
 */
/****
 * The player has completed playing the video or audio.
 */
export declare const completion: number;
/**
 * 出错状态
 */
/****
 * The player has an error.
 */
export declare const error: number;
export interface IPlayer {
    /**
     * 开始播放。
     */
    /****
     * Start the player.
     */
    start: () => void;
    /**
     * 暂停播放
     */
    /****
     * Pause the player.
     */
    pause: () => void;
    /**
     * 停止播放
     */
    /****
     * Stop the player.
     */
    stop: () => void;
    /**
     * 设置自动播放。如果是自动播放，则接受不到{@link OnPreparedListener}回调。
     *
     * @param on true：是。默认否。
     */
    /****
     * Enable autoplay. If autoplay is enabled, then the {@link OnPreparedListener} callback is not returned.
     *
     * @param on Value true indicates that autoplay is enabled. Default: disabled.
     */
    setAutoPlay: (isAutoPlay: boolean) => void;
    /**
     * 准备。成功结果通过{@link OnPreparedListener}回调，或者失败{@link OnErrorListener}
     */
    /****
     * Prepare the player. Call {@link OnPreparedListener} to return success messages. Call {@link OnErrorListener} to return error messages.
     */
    prepare: () => void;
    /**
     * 设置surfaceId，将播放器的显示surface与XComponent绑定。不同的XComponent的XComponentId应不重复
     *
     * @param surfaceId：实际上是XComponentId。
     */
    /****
     * Set the surfaceId to bind the player's display surface with XComponent. The XComponent id ofSet the surfaceId to bind the player's display surface with XComponent. The XComponent id of different XComponents should not be duplicated.
     *
     * @param surfaceId：it's XComponentId actually。
     */
    setSurfaceId: (surfaceId: string) => void;
    /**
     * 设置倍数播放。
     *
     * @param speed 范围[0.5,5]
     */
    /****
     * Set the playback speed.
     *
     * @param speed Valid values: [0.5,5].
     */
    setSpeed: (speed: number) => void;
    /**
     * 设置音量（非系统音量），范围0.0~2.0，当音量大于1.0时，可能出现噪音，不推荐使用。
     *
     * @param gain 范围[0,1]
     */
    /****
     * Set the volume of the player(Not system volume). The range is 0.0~2.0，it maybe lead to noise if set volume more then 1.0, not recommended.
     *
     * @param gain Valid values: [0,1].
     */
    setVolume: (volume: number) => void;
    /**
     * 获取音量。
     *
     * @return 范围[0, 1]
     */
    /****
     * Query the volume of the player.
     *
     * @return Valid values: [0, 1].
     */
    getVolume: () => number;
    /**
     * 跳转到。
     *
     * @param position 位置。单位毫秒。
     * @param mode , mode 为 1时为精准Seek，为16时为非精准
     */
    /****
     * Seek to a specified position.
     *
     * @param position The specified position. Unit: millisecond.
     * @param mode The specified seeking mode. mode is accurate when value is 1, inaccurate when value is 16
     */
    seekTo: (position: number, mode: number) => void;
    /**
     * 以指定位置起播，每次prepare前调用，仅生效一次。（用于代替原先的起播前seek的方案）
     *
     * @param position 位置。单位毫秒。
     * @param mode seek模式, mode 为 1时为精准Seek，为16时为非精准
     */
    /****
     * Set a specified position as play start. Call this API before prepare, only take effect once.
     *
     * @param position The specified position. Unit: millisecond.
     * @param mode The specified seeking mode. mode is accurate when value is 1, inaccurate when value is 16
     */
    setStartTime: (position: number, mode: number) => void;
    /**
     * 获取总时长。
     *
     * @return 总时长。单位ms。
     */
    /****
     * Query the total length of the stream.
     *
     * @return The total length of the stream. Unit: milliseconds.
     */
    getDuration: () => number;
    /**
      * 获取播放时长
      * @return played duration
      */
    /****
      * Get played duration
      * @return played duration
      */
    getPlayedDuration: () => number;
    /**
     * 获取当前播放位置。
     *
     * @return 播放位置。单位ms。
     */
    /****
     * Query the current playback position.
     *
     * @return The current playback position. Unit: milliseconds.
     */
    getCurrentPosition: () => number;
    /**
     * 获取当前播放器状态
     *
     * @return 播放器状态，整型变量
     *  int idle = 0;
     *  int initalized = 1;
     *  int prepared = 2;
     *  int started = 3;
     *  int paused = 4;
     *  int stopped = 5;
     *  int completion = 6;
     *  int error = 7;
     */
    getPlayerStatus: () => number;
    /**
     * 获取缓冲进度位置。注意：与{@link OnLoadingStatusListener}的区别。
     *
     * @return 缓冲进度位置。单位ms。
     */
    /****
     * Query the current buffer progress. Note: Pay close attention to the difference between this method and {@link OnLoadingStatusListener}.
     *
     * @return The current buffer progress. Unit: milliseconds.
     */
    getBufferedPosition: () => number;
    /**
     * 根据url进行多清晰度切换，选择成功与否的结果通过{@link OnStreamSwitchedListener}回调。
     * 注意：
     * 1. 必须是阿里云的直播地址。
     * 2. 必须是直播流的不同清晰度。
     * 3. 切换到无关流地址可能会失败。
     *
     * @param url 新流的url地址
     */
    /****
     * Multi definition stream switch by url. You can get result from {@link OnStreamSwitchedListener}.
     * Note:
     * 1. Must be aliyun's live stream address.
     * 2. Must be different definition of the same stream.
     * 3. If switch to an unrelated address, switch may failed.
     *
     * @param url new url address
     */
    switchStream: (url: string) => void;
    /**
     * 选择播放的流。选择成功与否的结果通过{@link OnTrackChangedListener}回调。
     * 注意：自动切换码率与其他Track的选择是互斥的。选择其他Track之后，自动切换码率就失效，不起作用了。
     *
     * @param trackInfoIndex 流信息。见{@link TrackInfo#getIndex()}。
     *                       如果需要自动切换码率，则传递{@link TrackInfo#AUTO_SELECT_INDEX}.
     */
    /****
     * Specify a stream to play. You can call {@link OnTrackChangedListener} to check whether the stream is played.
     * Note: If you have specified a track, then automatic bitrate switchover does not take effect even if it is enabled.
     *
     * @param trackInfoIndex Stream information. See {@link TrackInfo#getIndex()}.
     *                       If you want to enable automatic bitrate switchover, pass {@link TrackInfo#AUTO_SELECT_INDEX}.
     */
    selectTrack: (trackInfoIndex: number) => void;
    /**
     * 获取媒体信息。注意：只有在{@link OnPreparedListener#onPrepared()}回调之后获取才能正确。
     *
     * @return 媒体信息。见{@link MediaInfo}。
     */
    /****
     * Query media information. Note: Only if {@link OnPreparedListener#onPrepared()} is called back, the media information can be returned.
     *
     * @return The media information. See {@link MediaInfo}.
     */
    getMediaInfo: () => MediaInfo | null;
    /**
     * 获取媒体信息。注意：只有在{@link OnPreparedListener#onPrepared()}回调之后获取才能正确。
     * 多码率流切换码率后，通过{@link OnSubTrackReadyListener#onSubTrackReady(MediaInfo)}回调之后获取才能正确
     *
     * @return 媒体信息。见{@link MediaInfo}。
     */
    /****
     * Query media information. Note: Only if {@link OnPreparedListener#onPrepared()} is called back, the media information can be returned.
     * After selecting one sub m3u8 stream from multiStream m3u8, Only if {@link OnSubTrackReadyListener#onSubTrackReady(MediaInfo)} is called back, the media information can be returned.
     *
     * @return The media information. See {@link MediaInfo}.
     */
    getSubMediaInfo: () => MediaInfo | null;
    /**
     * 根据type获取当前播放的流。
     *
     * @param type 类型。见{@link TrackInfo.Type}
     * @return 当前播放的流。如果没有，则返回null。
     */
    /****
     * Query the playing track info according to the specified track type.
     *
     * @param type The type of the track. See {@link TrackInfo.Type}.
     * @return The track that is playing. If no track is playing, null is returned.
     */
    currentTrack: (type: TrackType) => TrackInfo | null;
    /**
     * 添加外挂字幕
     * @param url 字幕地址
     */
    /****
     * Add external subtitles
     * @param url subtitle address
     */
    addExtSubtitle: (url: string) => void;
    /**
     * 选择外挂字幕
     * @param trackIndex 字幕索引
     * @param select true：选择，false：关闭
     */
    /****
     * Select external subtitles
     * @param trackIndex caption index
     * @param select true: select, false: close
     */
    selectExtSubtitle: (trackIndex: number, select: boolean) => void;
    /**
     * 是否启用硬解码
     *
     * @param enable true:启用。false:关闭。
     */
    /****
     * Enable or disable hardware decoding.
     *
     * @param enable Set to true to enable hardware decoding and set to false to disable hardware decoding.
     */
    enableHardwareDecoder: (enabled: boolean) => void;
    /**
     * 释放。
     */
    /****
     * Release.
     */
    release: () => void;
    /**
     * 异步释放。当实例不再需要时，省去stop的调用并使用releaseAsync进行异步释放，可以加快页面响应速度，提高体验，释放后不要再调用prepare进行新的起播，否则调用效果不可预知。
     */
    /****
     * async release. When instance is not needed, skip calling stop api and call this releaseAsync api can speed up view response. Once called, don't call prepare to start new play.
     */
    releaseAsync: () => void;
    /**
     * 设置静音
     *
     * @param on true:静音。默认false。
     */
    /****
     * Mute the player.
     *
     * @param on Set to true to mute the player. Default: false.
     */
    setMute: (mute: boolean) => void;
    /**
     * 是否静音
     *
     * @return true:静音。默认false。
     */
    /****
     * Query whether the player is muted.
     *
     * @return Value true indicates that the player is muted. Default: false.
     */
    isMuted: () => boolean;
    /**
     * 设置画面缩放模式
     *
     * @param scaleMode 缩放模式。默认{@link ScaleMode#SCALE_TO_FILL}. 见{@link ScaleMode}。
     */
    /****
     * Set a zoom mode.
     *
     * @param scaleMode The specified zoom mode. Default: {@link ScaleMode#SCALE_TO_FILL}. See {@link ScaleMode}.
     */
    setScaleMode: (mode: ScaleMode) => void;
    /**
     * 获取画面缩放模式
     *
     * @return 缩放模式。默认{@link ScaleMode#SCALE_TO_FILL}.
     */
    /****
     * Query the current zoom mode.
     *
     * @return The current zoom mode. Default: {@link ScaleMode#SCALE_TO_FILL}.
     */
    getScaleMode: () => ScaleMode;
    /**
     * 设置循环播放。如果本地有了缓存，那么下次循环播放则播放本地文件。
     *
     * @param on true：开启循环播放。默认关闭。
     */
    /****
     * Enable loop playback. If the media file is already downloaded to the local host, then the downloaded media file will be used for loop playback.
     *
     * @param on true：Enable loop playback. Default: disabled.
     */
    setLoop: (on: boolean) => void;
    /**
     * 是否循环播放
     *
     * @return true：开启了循环播放。默认关闭。
     */
    /****
     * Query whether loop playback is enabled.
     *
     * @return Value true indicates that loop playback is enabled. Default: disabled.
     */
    isLoop: () => boolean;
    /**
     * 获取视频宽度
     *
     * @return 视频宽度
     */
    /****
     * Query the width of the video.
     *
     * @return The width of the video.
     */
    getVideoWidth: () => number;
    /**
     * 获取视频高度
     *
     * @return 视频高度
     */
    /****
     * Query the height of the video.
     *
     * @return The height of the video.
     */
    getVideoHeight: () => number;
    /**
     * 获取视频旋转角度
     *
     * @return 视频旋转角度
     */
    /****
     * Query the rotate angle of the video.
     *
     * @return The rotate angle of the video.
     */
    getVideoRotation: () => number;
    /**
     * 重新加载。比如网络超时时，可以重新加载。
     */
    /****
     * Reload. Call this method when the network connection times out.
     */
    reload: () => void;
    /**
     * 设置画面旋转模式
     *
     * @param rotateMode 旋转模式。见{@link RotateMode}
     */
    /****
     * Set a rotate mode.
     *
     * @param rotateMode The specified rotate mode. See {@link RotateMode}.
     */
    setRotateMode: (mode: RotateMode) => void;
    /**
     * 获取画面旋转模式
     *
     * @return 旋转模式。见{@link RotateMode}
     */
    /****
     * Query the current rotate mode.
     *
     * @return The current rotate mode. See {@link RotateMode}.
     */
    getRotateMode: () => RotateMode;
    /**
     * 设置Alpha渲染模式
     *
     * @param alphaRenderMode 镜像模式。 见{@link AlphaRenderMode}。
     */
    /****
     * Set a alpha render mode
     *
     * @param alphaRenderMode The specified alpha render mode. See {@link AlphaRenderMode}.
     */
    setAlphaRenderMode: (mode: AlphaRenderMode) => void;
    /**
     * 获取当前Alpha渲染模式。
     *
     * @return Alpha渲染模式。 见{@link AlphaRenderMode}。
     */
    /****
     * Query the current alpha render mode.
     *
     * @return The current alpha render mode. See {@link AlphaRenderMode}.
     */
    getAlphaRenderMode: () => AlphaRenderMode;
    /**
     * 设置镜像模式
     *
     * @param mirrorMode 镜像模式。 见{@link MirrorMode}。
     */
    /****
     * Set a mirroring mode
     *
     * @param mirrorMode The specified mirroring mode. See {@link MirrorMode}.
     */
    setMirrorMode: (mode: MirrorMode) => void;
    /**
     * 获取当前镜像模式。
     *
     * @return 镜像模式。 见{@link MirrorMode}。
     */
    /****
     * Query the current mirroring mode.
     *
     * @return The current mirroring mode. See {@link MirrorMode}.
     */
    getMirrorMode: () => MirrorMode;
    /**
     * 设置视频的背景色
     *
     * @param color  ARGB
     *
     */
    /****
     * Set video background color
     * @param color  ARGB
     */
    setVideoBackgroundColor: (color: number) => void;
    /**
     * 获取倍数播放值。
     *
     * @return 倍数播放值。范围[0.5,5]
     */
    /****
     * Query the playback speed.
     *
     * @return The playback speed. Valid values: [0.5,2].
     */
    getSpeed: () => number;
    /**
     * 是否自动播放。
     *
     * @return true：是。默认否。
     */
    /****
     * Query whether autoplay is enabled.
     *
     * @return Value true indicates that autoplay is enabled. Default: disabled.
     */
    isAutoPlay: () => boolean;
    /**
     * 设置播放器配置。
     *
     * @param config 播放器配置。见{@link PlayerConfig}
     */
    /****
     * Modify the player configuration.
     *
     * @param config The configuration of the player. See {@link PlayerConfig}.
     */
    setConfig: (config: PlayerConfig) => void;
    /**
     * 获取播放器配置
     *
     * @return 播放器配置
     */
    /****
     * Query the player configuration.
     *
     * @return The player configuration.
     */
    getConfig: () => PlayerConfig | undefined;
    /**
     * 设置特定功能选项
     * @param key 选项key
     * @param value 选项的值
     */
    /****
     * Set specific option
     * @param key key option
     * @param value value of key
     */
    setOption: (key: string, value: string) => void;
    /**
     * 设置特定功能选项
     * @param key 选项key
     * @param value 选项的值
     */
    /****
     * Set specific option
     * @param key key option
     * @param value value of key
     */
    setOptionNum: (key: number, value: number) => void;
    /**
     * 获取播放器的选项参数
     *
     * @param key 参数值。见{@linkplain Option}
     * @return
     */
    /****
     * gets the player's options parameters
     *
     * @param key parameter value See {@linkplain Option}
     * @return
     */
    getOption: (key: string) => string | number | undefined;
    /**
     * 设置某路流相对于主时钟的延时时间，默认是0, 目前只支持外挂字幕
     * @param index 流的索引
     * @param time  延时，单位毫秒
     */
    /****
     * set the delay time of the stream
     * @param index stream index
     * @param time  ms
     */
    setStreamDelay: (index: number, time: number) => void;
    /**
     * 设置精准seek的最大间隔。
     * @param delta 间隔时间，单位毫秒
     */
    /****
     * set the maximum interval of precision seek.
     * @param delta interval in milliseconds
     */
    setMaxAccurateSeekDelta: (delta: number) => void;
    /**
     * 设置缓存配置
     *
     * @param cacheConfig 缓存配置。见{@link CacheConfig}。
     */
    /****
     * Configure cache settings.
     *
     * @param cacheConfig Cache settings. See {@link CacheConfig}.
     */
    setCacheConfig: (config: CacheConfig) => void;
    /**
     * 设置网络ip解析类型
     * @param type 见 {@link IPResolveType}
     */
    /****
     * set ip resolve type
     * @param type See {@link IPResolveType}
     */
    setIPResolveType: (type: IPResolveType) => void;
    /**
     * 设置快速播放。
     * @param open
     */
    /****
     * set fast start
     * @param open
     */
    setFastStart: (enable: boolean) => void;
    /**
     * 截取当前画面.截图结果通过{@link OnSnapShotListener}回调。
     */
    /****
     * Create a snapshot for the current frame. You can call {@link OnSnapShotListener} to return the snapshot creation result.
     */
    snapShot: () => void;
    /**
     * 清空画面。在SurfaceTexture重用时，可以清除之前的画面
     */
    /****
     * Clear the screen. When SurfaceTexture is reused, the previous screen can be erased
     */
    clearScreen: () => void;
    /**
     * 根据url获取缓存的文件名。必须先调用setCacheConfig才能获取到。
     *
     * @param URL URL
     * @return 最终缓存的文件绝对路径。
     */
    /****
     * Query the name of a cached file with the specified URL. You must first call setCacheConfig.
     *
     * @param URL The URL of the cached file.
     * @return The absolute path of the cached file.
     */
    getCacheFilePathByUrl: (url: string) => string;
    /**
     * 根据vid 获取缓存的文件名。必须先调用setCacheConfig才能获取到。
     *
     * @param vid         视频id
     * @param format      视频格式
     * @param definition  视频清晰度
     * @param previewTime 试看时长
     * @return 最终缓存的文件绝对路径。
     */
    /****
     * Query the name of a cached file with the specified VID. You must first call setCacheConfig.
     *
     * @param vid         The ID of the video.
     * @param format      The format of the video.
     * @param definition  The definition of the video.
     * @param previewTime The preview duration for the video.
     * @return The absolute path of the cached file.
     */
    getCacheFilePathByVid: (vid: string, format: string, definition: string, previewTime: number) => string;
    /**
     * 根据key获取相应的信息。
     * @param key 关键字枚举值
     * @return 相应信息（找不到相应信息返回空字符串）。
     *
     */
    /****
     * Get information by key.
     * @param key The enum of key
     * @return corresponding information, return "" if doesn't exist.
     */
    getPropertyString: (propertyKey: PropertyKey) => string;
    /**
     * 设置多码率时默认播放的码率。将会选择与之最接近的一路流播放。
     * @param bandWidth 播放的码率。
     */
    /****
     * Set the default playback bitrate for multi-bit rate. The nearest stream will be selected.
     * @param bandWidth bit rate .
     */
    setDefaultBandWidth: (bandwidth: number) => void;
    /**
     * 发送用户自定义事件，将通过{@link OnReportEventListener} 回调。用户自定义事件 e = 5001。args为传入的参数。
     * @param args
     */
    /****
     * Sends a user-defined event, which will be called back via {@link OnReportEventListener}. User-defined event e = 5001. Args is the parameter passed in.
     * @param args
     */
    sendCustomEvent: (event: string) => void;
    /**
     * 设置视频标签。值范围为[0,99]。
     *
     * @param tags
     */
    /****
     * Set the video tags. Values range from [0,99].
     *
     * @param tags
     */
    setVideoTag: (tags: number[]) => void;
    /**
     * 设置UserData，用于一些全局API的透传，以区分player实例。
     * @param userData
     */
    /****
     * Set user data, and will be passed to some global API, so that client can know from which player instance
     * @param userData
     */
    setUserData: (userData: string) => void;
    /**
     * 设置traceId 便于跟踪日志。
     *
     * @param traceId traceId
     */
    /****
     * Set a trace ID for troubleshooting with the relevant log.
     *
     * @param traceId The specified trace ID.
     */
    setTraceId: (traceId: string) => void;
    /**
     * 获取UserData，一个实例绑定一个。
     * @param
     */
    /****
     * Get user data, and each player instance bind one.
     * @param
     */
    getUserData: () => string;
    getNativeContextAddr: () => number;
    /**
     * 设置准备成功通知。如果失败，则会通知{@link OnErrorListener}。
     *
     * @param l 准备成功通知
     */
    /****
     * Set a preparation success callback. If the preparation failed, the {@link OnErrorListener} is triggered.
     *
     * @param l Preparation success notification.
     */
    setOnPreparedListener: (l: OnPreparedListener) => void;
    /**
     * 设置渲染开始通知。可以监听首帧显示事件等，用于隐藏封面等功能。
     *
     * @param l 渲染开始通知。
     */
    /****
     * Set a rendering start callback. You can use this callback to listen to first frame display events and hide the album cover.
     *
     * @param l Rendering start notification.
     */
    setOnRenderingStartListener: (l: OnRenderingStartListener) => void;
    /**
     * 设置播放器状态变化通知
     *
     * @param l 播放器状态变化通知
     */
    /****
     * Set a player status update callback.
     *
     * @param l Player status update notification.
     */
    setOnStateChangedListener: (l: OnStateChangedListener) => void;
    /**
     * 设置播放完成通知.注意：循环播放不会发出此通知。
     *
     * @param l 播放完成通知.
     */
    /****
     * Set a playback completion callback. Note: No notification is sent if loop playback is enabled.
     *
     * @param l Playback completion notification.
     */
    setOnCompletionListener: (l: OnCompletionListener) => void;
    /**
     * 设置加载状态通知。
     *
     * @param l 加载状态通知
     */
    /****
     * Set a loading status callback.
     *
     * @param l Loading status notification.
     */
    setOnLoadingStatusListener: (l: OnLoadingStatusListener) => void;
    /**
     * 设置出错通知
     *
     * @param l 出错通知
     */
    /****
     * Set an error callback.
     *
     * @param l Error message.
     */
    setOnErrorListener: (l: OnErrorListener) => void;
    /**
     * 设置信息监听
     *
     * @param l 信息监听
     */
    /****
     * Set a notification callback.
     *
     * @param l The notification.
     */
    setOnInfoListener: (l: OnInfoListener) => void;
    /**
     * 设置视频宽高变化通知
     *
     * @param l 视频宽高变化通知
     */
    /****
     * Set a video size change callback.
     *
     * @param l Video size change notification.
     */
    setOnVideoSizeChangedListener: (l: OnVideoSizeChangedListener) => void;
    /**
     * 设置拖动完成通知
     *
     * @param l 拖动完成通知
     */
    /****
     * Set a seeking completion callback.
     *
     * @param l Seeking completion notification.
     */
    setOnSeekCompleteListener: (l: OnSeekCompleteListener) => void;
    /**
     * 设置字幕显示通知
     *
     * @param l 字幕显示通知
     */
    /****
     * Set a subtitle display callback.
     *
     * @param l Subtitle display notification.
     */
    setOnSubtitleDisplayListener: (l: OnSubtitleDisplayListener) => void;
    /**
     * 设置视频渲染回调
     * @param l 视频渲染回调。见{@linkplain OnVideoRenderedListener}
     */
    /****
     * set the video render callback
     * @param l video render callback. See {@linkplain OnVideoRenderedListener}
     */
    setOnVideoRenderedListener: (l: OnVideoRenderedListener) => void;
    /**
     * 设置音画不同步状态通知。
     *
     * @param l 音画不同步状态通知
     */
    /****
     * Set an audio and video not sync status callback.
     *
     * @param l Audio and video not sync status callback.
     */
    setOnAVNotSyncStatusListener: (l: OnAVNotSyncStatusListener) => void;
    /**
     * 设置音频打断事件回调
     * @param l 音频打断事件回调。见{@linkplain OnAudioInterruptEventListener}
     */
    /****
     * set the  audio interrupt event Listener
     * @param l audio interrupt event Listener. See {@linkplain OnAudioInterruptEventListener}
     */
    setOnAudioInterruptEventListener: (l: OnAudioInterruptEventListener) => void;
    /**
     * 设置流准备完成通知
     *
     * @param l 流准备完成通知
     */
    /****
     * Set a stream preparation success callback.
     *
     * @param l Stream preparation success notification.
     */
    setOnTrackReadyListener: (l: OnTrackReadyListener) => void;
    /**
     * 设置子流准备完成通知
     *
     * @param l 子流准备完成通知
     */
    /****
     * Set a subStream preparation success callback.
     *
     * @param l SubStream preparation success notification.
     */
    setOnSubTrackReadyListener: (l: OnSubTrackReadyListener) => void;
    /**
     * 设置清晰度切换变化通知
     *
     * @param l 清晰度变化通知
     */
    /****
     * Set a stream switch callback.
     *
     * @param l Stream switch notification.
     */
    setOnStreamSwitchedListener: (l: OnStreamSwitchedListener) => void;
    /**
     * 设置流变化通知
     *
     * @param l 流变化通知
     */
    /****
     * Set a stream switchover callback.
     *
     * @param l Stream switchover notification.
     */
    setOnTrackChangedListener: (l: OnTrackChangedListener) => void;
    /**
     * 截图结果监听
     *
     * @param l 截图结果事件
     */
    /****
     * Set a snapshot creation result callback.
     *
     * @param l Snapshot creation notification.
     */
    setOnSnapShotListener: (l: OnSnapShotListener) => void;
    /**
     * 设置SEI数据的监听回调
     * @param l SEI数据的监听回调
     */
    /****
     * Set a SEI data callback.
     * @param l SEI data callback.
     */
    setOnSeiDataListener: (l: OnSeiDataListener) => void;
}
/**
 * 准备成功通知
 */
/****
 * Preparation success callback.
 */
export interface OnPreparedListener {
    /**
     * 准备成功
     */
    /****
     * Preparation is complete.
     */
    onPrepared: () => void;
}
/**
 * 信息通知
 */
/****
 * Notification callback.
 */
export interface OnInfoListener {
    /**
     * 信息
     *
     * @param infoBean 信息对象。见{@linkplain InfoBean}
     */
    /****
     * Indicate a notification.
     *
     * @param infoBean The notification object. See {@linkplain InfoBean}.
     */
    onInfo: (infoBean: InfoBean) => void;
}
/**
 * 渲染开始通知
 */
/****
 * Rendering start callback.
 */
export interface OnRenderingStartListener {
    /**
     * 渲染开始。
     */
    /****
     * Rendering starts.
     */
    onRenderingStart: () => void;
}
/**
 * 播放器状态变化通知
 */
/****
 * Player status update callback.
 */
export interface OnStateChangedListener {
    /**
     * 状态变化
     *
     * @param newState 新状态
     */
    /****
     * The player status is updated.
     *
     * @param newState The updated status.
     */
    onStateChanged: (newState: number) => void;
}
/**
 * 播放完成通知.
 */
/****
 * Playback completion callback.
 */
export interface OnCompletionListener {
    /**
     * 播放完成
     */
    /****
     * The player has completed playing the video or audio.
     */
    onCompletion: () => void;
}
/**
 * 加载状态通知
 */
/****
 * Loading status callback.
 */
export interface OnLoadingStatusListener {
    /**
     * 开始加载。
     */
    /****
     * Start loading.
     */
    onLoadingBegin: () => void;
    /**
     * 加载进度
     *
     * @param percent  百分比，[0,100]
     * @param netSpeed 当前网速。kbps
     */
    /****
     * Loading progress.
     *
     * @param percent  The loading progress in percentage. Valid values: [0,100].
     * @param netSpeed The current bandwidth. Unit: kbit/s.
     */
    onLoadingProgress: (percent: number, netSpeed: number) => void;
    /**
     * 加载结束
     */
    /****
     * Loading is complete.
     */
    onLoadingEnd: () => void;
}
/**
 * 出错通知
 */
/****
 * Error callback.
 */
export interface OnErrorListener {
    /**
     * 出错
     *
     * @param errorInfo 错误信息
     */
    /****
     * An error occurs.
     *
     * @param errorInfo Error message.
     */
    onError: (errorInfo: ErrorInfo) => void;
}
/**
 * 视频宽高变化通知
 */
/****
 * Video size change callback.
 */
export interface OnVideoSizeChangedListener {
    /**
     * 视频宽高变化
     *
     * @param width  宽
     * @param height 高
     */
    /****
     * Video size changes.
     *
     * @param width  Width.
     * @param height Height.
     */
    onVideoSizeChanged: (width: number, height: number) => void;
}
/**
 * 音频打断事件通知
 */
/****
 * Audio Interrupt change callback.
 */
export interface OnAudioInterruptEventListener {
    /**
     * 音频打断事件
     *
     * @param audioStatus  音频打断后状态
     */
    /****
     * audio interrupt event
     *
     * @param audioStatus  audio status after interrupted
     */
    onAudioInterruptEvent: (audioStatus: AudioStatus) => void;
}
/**
 * 拖动完成通知
 */
/****
 * Seeking completion callback.
 */
export interface OnSeekCompleteListener {
    /**
     * 拖动完成
     */
    /****
     * Seeking is complete.
     */
    onSeekComplete: () => void;
}
/**
 * 字幕显示通知
 */
/****
 * Subtitle display callback.
 */
export interface OnSubtitleDisplayListener {
    /**
     * 外挂字幕被添加
     * @param trackIndex 流id
     * @param url 字幕url
     */
    /****
     * External subtitles have been added
     * @param trackIndex stream id
     * @param url subtitle url
     */
    onSubtitleExtAdded: (trackIndex: number, url: string) => void;
    /**
     * 显示字幕
     *
     * @param tackIndex 流id
     * @param id   索引
     * @param data 内容
     */
    /****
     * Show subtitles.
     *
     * @param id   Index.
     * @param data Content.
     */
    onSubtitleShow: (trackIndex: number, id: number, data: string) => void;
    /**
     * 隐藏字幕
     * @param trackIndex 流id
     * @param id 字幕id
     */
    /****
     * Hide subtitles.
     *
     * @param trackIndex track index
     * @param id subtitle Index.
     */
    onSubtitleHide: (trackIndex: number, id: number) => void;
    /**
     * 字幕头信息
     * @param trackIndex 流id
     * @param header 头信息
     */
    /****
     * Subtitle header.
     *
     * @param trackIndex track index
     * @param header subtitle header.
     */
    onSubtitleHeader: (trackIndex: number, header: string) => void;
}
/**
 * 根据url切换清晰度流结果通知。
 */
/****
 * Stream switch by url result callback.
 */
export interface OnStreamSwitchedListener {
    /**
     * 切换成功
     *
     * @param url 切流地址。
     */
    /****
     * success to switch the stream.
     *
     * @param url swich stream url。
     */
    onSwitchedSuccess: (url: string) => void;
    /**
     * 切换失败
     *
     * @param url 切流地址。
     * @param errorInfo 错误信息。见{@link ErrorInfo}
     */
    /****
     * Failed to switch the stream.
     *
     * @param url swich stream url。
     * @param errorInfo Error message. See {@link ErrorInfo}.
     */
    onSwitchedFail: (url: string, errorInfo: ErrorInfo) => void;
}
/**
 * 切换流变化通知。
 */
/****
 * Stream switchover callback.
 */
export interface OnTrackChangedListener {
    /**
     * 切换成功
     *
     * @param trackInfo 流信息。见{@link TrackInfo}
     */
    /****
     * The stream is switched.
     *
     * @param trackInfo Stream information. See {@link TrackInfo}.
     */
    onChangedSuccess: (trackInfo: TrackInfo) => void;
}
/**
 * 流准备完成通知
 */
/****
 * Stream preparation success callback.
 */
export interface OnTrackReadyListener {
    /**
     * 流准备完成
     *
     * @param mediaInfo 媒体信息。见{@link MediaInfo}
     */
    /****
     * The stream is ready.
     *
     * @param mediaInfo Media information. See {@link MediaInfo}.
     */
    onTrackReady: (mediaInfo: MediaInfo) => void;
}
/**
 * 子流准备完成通知
 */
/****
 * SubStream preparation success callback.
 */
export interface OnSubTrackReadyListener {
    /**
     * 子流准备完成
     *
     * @param mediaInfo 媒体信息。见{@link MediaInfo}
     */
    /****
     * The SubStream is ready.
     *
     * @param mediaInfo Media information. See {@link MediaInfo}.
     */
    onSubTrackReady: (mediaInfo: MediaInfo) => void;
}
/**
 * 音画不同步状态通知
 */
/****
 * audio and video play not sync status callback.
 */
export interface OnAVNotSyncStatusListener {
    /**
     * 音画不同步开始。
     *
     *  @param type  类型, 当type为0时，即为Video_Drop_Too_Many, 原因为视频解码速率与播放速率不匹配, 建议降低播放倍速
     */
    /****
     * Audio and Video not sync start.
     *
     * @param type  Type
     */
    onAVNotSyncStart: (type: number) => void;
    /**
     * 音画不同步恢复。
     */
    /****
     * Audio and Video not sync complete.
     */
    onAVNotSyncEnd: () => void;
}
/**
 * 截图结果。
 */
/****
 * Snapshot creation result callback.
 */
export interface OnSnapShotListener {
    /**
     * 截图结果的回调
     *
     * @param pixelMap     图片。如果截图失败，则为null。 注意:使用完之后注意释放recycle。
     * @param width   宽度
     * @param height  高度
     */
    /****
     * Snapshot result callback.
     *
     * @param pixelMap     The snapshot. If fail, be null. Note: After you use the snapshot, call the recycle method to release it.
     * @param width   Width.
     * @param height  Height.
     */
    onSnapShot: (arrayBuffer: ArrayBuffer, width: number, height: number) => void;
}
/**
 * SEI数据回调
 */
/****
 * SEI data callback.
 */
export interface OnSeiDataListener {
    /**
     * 回调
     * @param type 类型
     * @param data 数据
     */
    /****
     * Callback.
     * @param type Type.
     * @param data Data.
     */
    onSeiData: (type: number, data: Uint8Array) => void;
}
/**
 * 视频渲染回调
 */
/****
 * video render callback
 */
export interface OnVideoRenderedListener {
    /**
     * 视频帧被渲染
     * @param timeMs 渲染的时间点
     * @param pts 渲染的pts
     */
    /****
     * video frames are rendered
     * @param timeMs render point in time
     * @param pts rendered pts
     */
    onVideoRendered: (timeMs: number, pts: number) => void;
}
export interface OnKeyGenerateListener {
    onHlsKeyInfoInit: (key: string, time: number, userData: string) => void;
    getHlsKey: (key: string, userData: string) => string;
}
export declare enum IPResolveType {
    /**
     * 任意类型
     */
    /****
     * default
     */
    IpResolveWhatEver = 0,
    /**
     * 只使用ipV4
     */
    /****
     * only ip v4
     */
    IpResolveV4 = 1,
    /**
     * 只使用ipV6
     */
    /***
     * only ip v6
     */
    IpResolveV6 = 2
}
export declare enum ScaleMode {
    /**
     * 宽高比适应
     */
    /****
     * Auto zoom to fit.
     */
    SCALE_ASPECT_FIT = 0,
    /**
     * 宽高比填充
     */
    /****
     * Fill to fit.
     */
    SCALE_ASPECT_FILL = 1,
    /**
     * 拉伸填充
     */
    /****
     * Stretch to fit.
     */
    SCALE_TO_FILL = 2
}
export declare enum MirrorMode {
    /**
     * 无镜像
     */
    /****
     * Disable mirroring.
     */
    MIRROR_MODE_NONE = 0,
    /**
     * 水平镜像
     */
    /****
     * Horizontal mirroring.
     */
    MIRROR_MODE_HORIZONTAL = 1,
    /**
     * 垂直镜像
     */
    /****
     * Vertical mirroring.
     */
    MIRROR_MODE_VERTICAL = 2
}
export declare enum RotateMode {
    /**
     * 顺时针旋转0度
     */
    /****
     * Do not rotate.
     */
    ROTATE_0 = 0,
    /**
     * 顺时针旋转90度
     */
    /****
     * Rotate 90 degree clockwise.
     */
    ROTATE_90 = 90,
    /**
     * 顺时针旋转180度
     */
    /****
     * Rotate 180 degree clockwise.
     */
    ROTATE_180 = 180,
    /**
     * 顺时针旋转270度
     */
    /****
     * Rotate 270 degree clockwise.
     */
    ROTATE_270 = 270
}
export declare enum Option {
    /**
     * 渲染的fps。类型为Float
     */
    /****
     * render fps. Return value type is Float
     */
    RenderFPS = "renderFps",
    /**
     * 当前的网络下行码率。类型为Float
     */
    /****
     * current download bitrate. Return value type is Float
     */
    DownloadBitrate = "downloadBitrate",
    /**
     * 当前播放的视频码率。类型为Float
     */
    /****
     * current playback video bitrate. Return value type is Float
     */
    VideoBitrate = "videoBitrate",
    /**
     * 当前播放的音频码率。类型为Float
     */
    /****
     * current playback audio bitrate. Return value type is Float
     */
    AudioBitrate = "audioBitrate",
    /**
     * 切换清晰度为AUTO(自动切换码率)时，判断当前是否处于ABR切换的状态。返回类型为Int, 当处于ABR切换状态时，返回"1"， 否则返回"0".
     */
    /****
     * When switched the resolution to AUTO (automatic bitrate switching),
     * it determines whether the current state is undergoing ABR (Adaptive Bitrate) switching.
     * The return type is Int. If it is in the state of ABR switching, it returns "1"; otherwise, it returns "0".
     */
    isAbrSwitching = "isAbrSwitching"
}
export declare enum AudioStatus {
    /**
     * 音频处于默认状态，回调时建议什么也不做
     */
    /****
     * default status, recommend do nothing when callback
     */
    AUDIO_STATUS_DEFAULT = 0,
    /**
     * 音频处于恢复状态，回调时建议调用播放器的start接口恢复播放
     */
    /****
     * audio resume status, recommend call start() method when callback
     */
    AUDIO_STATUS_RESUME = 1,
    /**
     * 音频处于暂停状态，回调时建议调用播放器的pause接口暂停播放
     */
    /****
     * audio pause status, recommend call pause() method when callback
     */
    AUDIO_STATUS_PAUSE = 2,
    /**
     * 音频处于停止状态，回调时建议调用播放器的stop接口停止播放
     */
    /****
     * audio stop status, recommend call stop() method when callback
     */
    AUDIO_STATUS_STOP = 3,
    /**
     * 音频处于扬声状态，回调时根据需要调节音量
     */
    /****
     * audio duck status, adjust volume when need when callback
     */
    AUDIO_STATUS_DUCK = 4,
    /**
     * 音频不处于扬声状态, 回调时根据需要调节音量
     */
    /****
     * audio unduck status, adjust volume when need when callback
     */
    AUDIO_STATUS_UNDUCK = 5
}
export declare enum PropertyKey {
    /**
     * Http的response信息
     * 返回的字符串是JSON数组，每个对象带response和type字段。type字段可以是url/video/audio/subtitle，根据流是否有相应Track返回。
     * 例如：[{"response":"response string","type":"url"},{"response":"","type":"video"}]
     */
    /**** Http response info
     * Return with JSON array，each object item include 'response'/'type' filed。'type' could be  url/video/audio/subtitle, depend on the stream whether have the tracks。
     * For example: [{"response":"response string","type":"url"},{"response":"","type":"video"}]
     */
    RESPONSE_INFO = 0,
    /**
     * 主URL的连接信息
     * 返回的字符串是JSON对象，带url/ip/eagleID/cdnVia/cdncip/cdnsip等字段（如果解析不到则不添加）
     * 例如：{"url":"http://xxx","openCost":23,"ip":"*************","cdnVia":"xxx","cdncip":"*************","cdnsip":"xxx"}
     */
    /**** Major URL connect information
     * Return with JSON object，include sub fileds such as url/ip/eagleID/cdnVia/cdncip/cdnsip.
     * For example: {"url":"http://xxx","openCost":23,"ip":"*************","cdnVia":"xxx","cdncip":"*************","cdnsip":"xxx"}
     */
    CONNECT_INFO = 1
}
export declare enum AlphaRenderMode {
    /**
     * 无Alpha模式
     */
    /****
     * Disable alpha render mode.
     */
    RENDER_MODE_ALPHA_NONE = 0,
    /**
     * alpha在右侧
     */
    /****
     * alpha at right.
     */
    RENDER_MODE_ALPHA_AT_RIGHT = 1,
    /**
     * alpha在左侧
     */
    /****
     * alpha at left.
     */
    RENDER_MODE_ALPHA_AT_LEFT = 2,
    /**
     * alpha在上侧
     */
    /****
     * alpha at top.
     */
    RENDER_MODE_ALPHA_AT_TOP = 3,
    /**
     * alpha在下侧
     */
    /****
     * alpha at bottom.
     */
    RENDER_MODE_ALPHA_AT_BOTTOM = 4
}
