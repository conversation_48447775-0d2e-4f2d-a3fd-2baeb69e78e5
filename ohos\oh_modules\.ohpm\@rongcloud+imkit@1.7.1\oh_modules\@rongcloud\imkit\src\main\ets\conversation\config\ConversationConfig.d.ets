// @keepTs
// @ts-nocheck
import { AvatarStyle } from "../../base/enum/AvatarStyle";
import { VoiceMessageType } from "../model/VoiceMessageType";
/**
 * 聊天页面配置
 * @version 1.0.0
 */
export declare class ConversationConfig {
    /**
     * 会话页面输入框组件ID
     */
    static readonly CONVERSATION_INPUT_BAR_ID: string;
    private maxRecallDuration;
    private maxEditableDuration;
    private messageAvatarStyle;
    private receivedMessageBorderRadius;
    private sentMessageBorderRadius;
    private receivedMessageBorderColorMap;
    private sentMessageBorderColorMap;
    private receivedMessageBackgroundColorMap;
    private sentMessageBackgroundColorMap;
    private enableDestruct;
    private styleFontColor;
    private fileIcons;
    private enablePlayAudioContinuous;
    private voiceMessageType;
    private combineHtmlStyle;
    private enableAutoDownloadHQVoice;
    private enableResendMessage;
    private messageRenderTextInterceptor;
    private referencedMessageClickType;
    private enableShowAllGroupReceipt;
    constructor();
    /**
     * 设置最大撤回时间（单位秒）,默认 180
     * @param duration
     */
    setMaxRecallDuration(r24: number): void;
    /**
     * 获取最大撤回时间（单位秒）
     * @returns
     */
    getMaxRecallDuration(): number;
    /**
     * 设置消息撤回后可重新编辑的时间（单位秒），默认 30
     * @param duration
     */
    setMaxEditableDuration(q24: number): void;
    /**
     * 获取消息撤回后可重新编辑的时间（单位秒）
     * @returns
     */
    getMaxEditableDuration(): number;
    /**
     * 设置文件消息的文件类型图标，会完全覆盖 sdk 内部图标
     * @param fileIcons key：文件后缀名，value 文件图标
     */
    setFileMessageIcons(m24: Map<string, ResourceStr>): void;
    /**
     * 获取文件消息内的文件类型图标map
     * @returns
     * @discussion 如果找不到，返回 sdk 内置的默认图标
     */
    getFileMessageIcons(): Map<string, ResourceStr>;
    /**
     * 获取文件消息内的文件类型图标
     * @param suffix
     * @returns
     * @discussion 如果找不到，返回 sdk 内置的默认图标
     */
    getFileMessageIcon(k24: string): ResourceStr;
    getDefaultFileMessageIcon(): Resource;
    /**
     * 设置是否显示会话列表头像圆角
     * @param isRoundedCornersForPortrait
     */
    setMessageAvatarStyle(j24: AvatarStyle): void;
    /**
     * 获取是否显示会话列表头像圆角
     * @returns
     */
    getMessageAvatarStyle(): AvatarStyle;
    /**
     * 设置收到的消息气泡的边框圆角大小
     * @param borderRadius 边框圆角大小
     * @warning 仅支持初始化前配置，若初始化后开发者设置，则不生效。
     */
    setReceivedMessageBorderRadius(i24: Length | BorderRadiuses | LocalizedBorderRadiuses): void;
    /**
     * 获取收到的消息气泡的边框圆角大小
     * @returns 边框圆角大小，如果未设置过则返回 undefined
     */
    getReceivedMessageBorderRadius(): Length | BorderRadiuses | LocalizedBorderRadiuses | undefined;
    /**
     * 设置发送的消息气泡的边框圆角大小
     * @param borderRadius 边框圆角大小
     * @warning 仅支持初始化前配置，若初始化后开发者设置，则不生效。
     */
    setSentMessageBorderRadius(h24: Length | BorderRadiuses | LocalizedBorderRadiuses): void;
    /**
     * 获取发送的消息气泡的边框圆角大小
     * @returns 边框圆角大小，如果未设置过则返回 undefined
     */
    getSentMessageBorderRadius(): Length | BorderRadiuses | LocalizedBorderRadiuses | undefined;
    /**
     * 设置收到的消息气泡边框色
     *
     * 使用说明：
     *```
     * 1，仅修改文本消息类型的边框色为灰色，其他消息类型不修改，示例：
     * setReceivedMessageBorderColor("RC:TxtMsg" , Color.Gray)
     *```
     *```
     * 2，修改全部消息类型的边框色为灰色，示例：
     * setReceivedMessageBorderColor("" , Color.Gray)
     *```
     *```
     * 3，修改文本消息类型的边框色为灰色，同时修改其余消息类型的边框色为蓝色，示例（可以不分顺序调用）：
     * setReceivedMessageBorderColor("RC:TxtMsg" , Color.Gray)
     * setReceivedMessageBorderColor("" , Color.Blue)
     *```
     * @param objectName 消息类型。如果填空字符串 "" ，则代表设置所有消息类型的边框色。SDK优先使用设置了objectName的边框色。
     * @param color 边框色
     * @warning 仅支持初始化前配置，若初始化后开发者设置，则不生效。
     */
    setReceivedMessageBorderColor(f24: string, g24: ResourceColor): void;
    /**
     * 获取收到的消息气泡边框色
     * @param objectName 消息类型
     * @returns 边框色
     */
    getReceivedMessageBorderColor(e24: string): ResourceColor;
    /**
     * 设置发送的消息气泡边框色
     *
     * 使用说明：
     *```
     * 1，仅修改文本消息类型的边框色为灰色，其他消息类型不修改，示例：
     * setSentMessageBorderColor("RC:TxtMsg" , Color.Gray)
     *```
     *```
     * 2，修改全部消息类型的边框色为灰色，示例：
     * setSentMessageBorderColor("" , Color.Gray)
     *```
     *```
     * 3，修改文本消息类型的边框色为灰色，同时修改其余消息类型的边框色为蓝色，示例（可以不分顺序调用）：
     * setSentMessageBorderColor("RC:TxtMsg" , Color.Gray)
     * setSentMessageBorderColor("" , Color.Blue)
     *```
     * @param objectName 消息类型。如果填空字符串 "" ，则代表设置所有消息类型的边框色。SDK优先使用设置了objectName的边框色。
     * @param color 边框色
     * @warning 仅支持初始化前配置，若初始化后开发者设置，则不生效。
     */
    setSentMessageBorderColor(c24: string, d24: ResourceColor): void;
    /**
     * 获取发送的消息气泡边框色
     * @param objectName 消息类型
     * @returns 边框色
     */
    getSentMessageBorderColor(b24: string): ResourceColor;
    /**
     * 设置收到的消息气泡背景色
     *
     * 使用说明：
     *```
     * 1，仅修改文本消息类型的背景色为灰色，其他消息类型不修改，示例：
     * setReceivedMessageBackgroundColor("RC:TxtMsg" , Color.Gray)
     *```
     *```
     * 2，修改全部消息类型的背景色为灰色，示例：
     * setReceivedMessageBackgroundColor("" , Color.Gray)
     *```
     *```
     * 3，修改文本消息类型的背景色为灰色，同时修改其余消息类型的背景色为蓝色，示例（可以不分顺序调用）：
     * setReceivedMessageBackgroundColor("RC:TxtMsg" , Color.Gray)
     * setReceivedMessageBackgroundColor("" , Color.Blue)
     *```
     * @param objectName 消息类型。如果填空字符串 "" ，则代表设置所有消息类型的背景色。SDK优先使用设置了objectName的背景色。
     * @param color 背景色
     * @warning 仅支持初始化前配置，若初始化后开发者设置，则不生效。
     */
    setReceivedMessageBackgroundColor(z23: string, a24: ResourceColor): void;
    /**
     * 获取收到的消息气泡背景色
     * @param objectName 消息类型
     * @returns 背景色
     */
    getReceivedMessageBackgroundColor(y23: string): ResourceColor;
    /**
     * 设置发送的消息气泡背景色
     *
     * 使用说明：
     *```
     * 1，仅修改文本消息类型的背景色为灰色，其他消息类型不修改，示例：
     * setSentMessageBackgroundColor("RC:TxtMsg" , Color.Gray)
     *```
     *```
     * 2，修改全部消息类型的背景色为灰色，示例：
     * setSentMessageBackgroundColor("" , Color.Gray)
     *```
     *```
     * 3，修改文本消息类型的背景色为灰色，同时修改其余消息类型的背景色为蓝色，示例（可以不分顺序调用）：
     * setSentMessageBackgroundColor("RC:TxtMsg" , Color.Gray)
     * setSentMessageBackgroundColor("" , Color.Blue)
     *```
     * @param objectName 消息类型。如果填空字符串 "" ，则代表设置所有消息类型的背景色。SDK优先使用设置了objectName的背景色。
     * @param color 背景色
     * @warning 仅支持初始化前配置，若初始化后开发者设置，则不生效。
     */
    setSentMessageBackgroundColor(w23: string, x23: ResourceColor): void;
    /**
     * 获取发送的消息气泡背景色
     * @param objectName 消息类型
     * @returns 背景色
     */
    getSentMessageBackgroundColor(v23: string): ResourceColor;
    /**
     * 设置阅后即焚是否打开
     * @param value
     */
    setEnableDestruct(u23: boolean): void;
    /**
     * 获取阅后即焚是否打开
     * @returns
     */
    getEnableDestruct(): boolean;
    private setDefaultFileIcons;
    setStyleFontColor(t23: Color): void;
    getStyleFontColor(): Color;
    /**
     * 设置连续播放未听的语音消息的配置
     * @param enable true：连续播放未接听语音消息，false：不连续播放未接听语音消息。
     */
    setEnablePlayAudioContinuous(s23: boolean): void;
    /**
     * 获取是否连续播放未听的语音消息的配置，默认为true
     */
    getEnablePlayAudioContinuous(): boolean;
    /**
     * 设置合并转发的Html样式
     *
     *代码示例
     *```
     *let config = RongIM.getInstance().conversationService().getConversationConfig()
     * let style = ".custom-msg-style1 {
     *  font-size: 14px;
     *  color: #FF0000;
     *  margin: 0;
     *  padding: 0;
     * }
     * .custom-msg-style2 {
     *  font-size: 16px;
     *  color: #CC0066;
     *  margin: 0;
     *  padding: 0;
     * }"
     * config.setCombineHtmlStyle(style)
     * RongIM.getInstance().conversationService().setConversationConfig(config)
     *```
     *
     * @param style
     * @since 1.4.0
     */
    setCombineHtmlStyle(r23: string): void;
    /**
     * 获取合并转发的Html样式
     * @returns 合并转发的Html样式
     * @since 1.4.0
     */
    getCombineHtmlStyle(): string;
    /**
     * 设置会话页面自动下载高清语音消息
     * @param enable true：开启，false：关闭
     */
    setEnableAutoDownloadHQVoice(q23: boolean): void;
    /**
     * 获取是否会话页面是否自动下载高清语音消息
     * @returns
     */
    isEnableAutoDownloadHQVoice(): boolean;
    /**
     * 设置消息重发开关
     * @param enable true：开启，false：关闭
     */
    setEnableResendMessage(p23: boolean): void;
    /**
     * 获取消息重发开关配置
     * @returns
     */
    isEnableResendMessage(): boolean;
    /**
     * 设置语音消息类型
     * @param voiceMessageType 语音消息类型，见 VoiceMessageType
     * @since 1.5.1
     */
    setVoiceMessageType(o23: VoiceMessageType): void;
    /**
     * 获取语音消息类型
     * @returns 当前设置的语音消息类型
     * @since 1.5.1
     */
    getVoiceMessageType(): VoiceMessageType;
    /**
    * 设置TextMessage、ReferenceMessage渲染文本内容的拦截器，用来开发者自定义渲染逻辑
    *
    * 注意：仅TextMessage、ReferenceMessage生效
    * @param interceptor 拦截器。如果返回true代表拦截渲染逻辑，SDK 则不会进行渲染。
     * @since 1.6.0
    */
    setMessageRenderTextInterceptor(n23: ((controller: TextController, content: string) => boolean) | null): void;
    /**
    * 获取TextMessage、ReferenceMessage渲染文本内容的拦截器
     * @since 1.6.0
    */
    getMessageRenderTextInterceptor(): ((controller: TextController, content: string) => boolean) | null;
    /**
     * 设置引用消息的被引用消息内容的点击行为
     * @param type 引用消息的被引用消息内容的点击行为，见 ReferencedMessageClickType
     * @since 1.6.0
     */
    setReferencedMessageClickType(m23: ReferencedMessageClickType): void;
    /**
     * 获取引用消息的被引用消息内容的点击行为
     * @returns 当前设置的引用消息的被引用消息内容的点击行为
     * @since 1.6.0
     */
    getReferencedMessageClickType(): ReferencedMessageClickType;
    /**
     * 设置是否每条消息都展示群已读请求按钮，默认为false，仅最新一条消息展示
     * @param enable 是否开启。true：每条消息均展示群已读请求按钮，false：仅最新一条消息展示
     */
    setEnableShowAllGroupReceipt(l23: boolean): void;
    /**
     * 获取是否每条消息都展示群已读请求按钮的配置
     * @returns 当前设置的群已读请求按钮展示配置
     */
    isEnableShowAllGroupReceipt(): boolean;
}
/**
 * 引用消息的被引用消息体点击后的处理方式
 * @since 1.6.0
 */
export declare enum ReferencedMessageClickType {
    /**
     * 跳转到预览页面。
     */
    JumpToDetailPage = 0,
    /**
     * 根据 被引用消息的uid 滚动到被引用消息
     */
    ScrollToReferencedMessage = 1
}
