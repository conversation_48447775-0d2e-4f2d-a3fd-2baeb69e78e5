import { OhosUrlPlayer } from './OhosUrlPlayer';
import premierlibrary from 'libpremierlibrary.so';
export class OhosSaasPlayer extends OhosUrlPlayer {
    constructor(t30) {
        super(t30);
    }
    setUrlDataSource(s30) {
        premierlibrary.nSetUrlDataSource(this, s30);
    }
    setLiveStsDataSource(r30) {
        premierlibrary.nSetLiveStsDataSource(this, r30);
    }
    updateStsInfo(q30) {
        premierlibrary.nUpdateStsInfo(this, q30);
    }
    setVidAuthSource(p30) {
        premierlibrary.nSetVidAuthDataSource(this, p30);
    }
    updateVidAuth(o30) {
        premierlibrary.nUpdateVidAuth(this, o30);
    }
    setVidStsSource(n30) {
        premierlibrary.nSetVidStsDataSource(this, n30);
    }
    setVidMpsSource(m30) {
        premierlibrary.nSetVidMpsDataSource(this, m30);
    }
}
