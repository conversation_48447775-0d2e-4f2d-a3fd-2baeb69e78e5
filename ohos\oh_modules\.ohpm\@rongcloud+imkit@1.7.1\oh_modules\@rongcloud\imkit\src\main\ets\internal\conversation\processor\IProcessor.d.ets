// @keepTs
// @ts-nocheck
/**
 * Created on 2024/07/15
 * <AUTHOR>
 */
import { ConnectionStatus, Message, ReceivedInfo } from '@rongcloud/imlib';
import { UiMessage } from '../../../conversation/model/UiMessage';
import { ConversationComponentData } from '../../../conversation/model/ConversationComponentData';
type DataResult = (localData: UiMessage[], remoteData?: UiMessage[]) => void;
export interface IProcessor {
    /**
     * 初始化，进入页面创建时
     */
    onInit(data: ConversationComponentData, localCallback?: DataResult, finalCallback?: DataResult): void;
    /**
     * 当接收到消息时
     * @param message 接受的消息
     * @param receivedInfo 接受的消息信息
     */
    onReceiveMessage(message: Message, receivedInfo: ReceivedInfo): void;
    /**
     * 当加载消息时，第一次加载消息，刷新时都会调用
     * @param messageArray array
     */
    /**
     * 加载更多消息
     */
    onLoadMore(data: ConversationComponentData, localCallback?: DataResult, finalCallback?: DataResult): void;
    /**
     * 刷新消息
     */
    /**
     * 链接状态变更
     * @param state 变更后的状态
     */
    onConnectStatusChange(state: ConnectionStatus): void;
}
export {};
