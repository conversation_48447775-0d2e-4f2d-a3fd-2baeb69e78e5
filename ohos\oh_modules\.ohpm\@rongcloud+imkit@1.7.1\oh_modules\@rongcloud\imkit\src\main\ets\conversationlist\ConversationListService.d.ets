// @keepTs
// @ts-nocheck
import { Conversation, ConversationType, IAsyncResult } from '@rongcloud/imlib';
import { ItemLongClickAction } from '../base/click/ItemLongClickAction';
import { ItemProvider } from '../base/item/provider/ItemProvider';
import { BaseUiConversation } from './model/BaseUiConversation';
import List from '@ohos.util.List';
import { ConversationListEventListener } from './listener/ConversationListEventListener';
import { ConversationListConfig } from './config/ConversationListConfig';
/**
 * 会话列表服务
 * @version 1.0.0
 */
export interface ConversationListService {
    /**
     * 设置会话列表配置
     * @param config 配置
     */
    getConversationListConfig(): ConversationListConfig;
    /**
     * 设置会话列表配置 会完全覆盖sdk配置，建议通过 getConversationListConfig 获取配置，修更改需要修改的配置
     * @param config 配置
     * @warning 配置不支持动态刷新 UI，必须在会话列表展示前设置
     */
    setConversationListConfig(config: ConversationListConfig): void;
    /**
     * 增加指定会话类型的 provider
     * @param conversationType 会话类型
     * @param provider
     * @discussion SDK 默认实现了单聊群聊的 provider，可以使用该接口增加或者替换 sdk 的 provider
     */
    addConversationItemProvider(conversationType: ConversationType, provider: ItemProvider<BaseUiConversation>): void;
    /**
     * 移除指定会话类型的 provider
     * @param conversationType
     */
    removeConversationItemProvider(conversationType: ConversationType): void;
    /**
     * 增加会话列表事件监听
     * @param listener 监听
     * @warning addConversationListEventListener & removeConversationListEventListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     */
    addConversationListEventListener(listener: ConversationListEventListener): void;
    /**
     * 移除会话列表事件监听
     * @param listener 监听
     * @warning addConversationListEventListener & removeConversationListEventListener 配合使用，避免内存泄露
     */
    removeConversationListEventListener(listener: ConversationListEventListener): void;
    /**
     * 增加会话列表 item 长按事件
     * @param actionInfo 长按事件，相同的 actionId 只能添加一次
     */
    addConversationItemLongClickAction(actionInfo: ItemLongClickAction<Conversation>): void;
    /**
     * 获取会话列表
     * @param typeList 会话类型数组
     * @param time 时间，默认值为 0（获取最新的会话）
     * @param count 个数，默认值 50
     * @returns 会话列表
     */
    getConversationList(typeList: ConversationType[], time?: number, count?: number): Promise<IAsyncResult<List<Conversation>>>;
}
