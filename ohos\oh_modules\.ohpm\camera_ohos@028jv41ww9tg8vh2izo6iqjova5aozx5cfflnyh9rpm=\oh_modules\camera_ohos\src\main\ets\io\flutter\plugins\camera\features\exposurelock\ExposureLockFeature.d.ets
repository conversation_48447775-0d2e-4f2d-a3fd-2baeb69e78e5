import camera from "@ohos.multimedia.camera";
import { CameraFeature } from '../CameraFeature';
import { CameraProperties } from '../../CameraProperties';
export declare class ExposureLockFeature extends CameraFeature<camera.ExposureMode> {
    private currentSetting;
    constructor(cameraProperties: CameraProperties);
    getDebugName(): string;
    getValue(): camera.ExposureMode;
    setValue(value: camera.ExposureMode): void;
    checkIsSupported(): boolean;
}
