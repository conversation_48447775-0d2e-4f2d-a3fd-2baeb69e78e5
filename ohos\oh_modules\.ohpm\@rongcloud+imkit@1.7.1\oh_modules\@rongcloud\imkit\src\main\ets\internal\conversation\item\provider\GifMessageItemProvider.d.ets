// @keepTs
// @ts-nocheck
import { GIFMessage } from '@rongcloud/imlib';
import { BaseMessageItemProvider } from '../../../../conversation/item/provider/BaseMessageItemProvider';
import { UiMessage } from '../../../../conversation/model/UiMessage';
import { FileDownloadInfo } from '../../filedownload/model/FileDownloadInfo';
export declare class GifMessageItemProvider extends BaseMessageItemProvider<GIFMessage> {
    getMessageWrapBuilder(): WrappedBuilder<[
        Context,
        UiMessage,
        number
    ]>;
    isShowSummaryName(n172: Context, o172: GIFMessage): boolean;
    getSummaryTextByMessageContent(j172: Context, k172: GIFMessage): Promise<MutableStyledString>;
}
@Builder
export declare function bindImageMessageData(y171: Context, z171: UiMessage, a172: number): void;
@Component
export declare struct GifMessageView {
    @Require
    @Prop
    context: Context;
    @Require
    @Prop
    index: number;
    @ObjectLink
    uiMessage: UiMessage;
    private messageContent;
    @State
    imageWidth: number;
    @State
    imageHeight: number;
    @State
    localPath: string;
    @State
    downloadingProgress: number;
    @State
    downloading: boolean;
    @State
    fileInfo: FileDownloadInfo;
    private downloadListener;
    aboutToAppear(): void;
    aboutToDisappear(): void;
    private downloadGifImage;
    private getGIFSize;
    private bubbleBorderRadius;
    build(): void;
}
