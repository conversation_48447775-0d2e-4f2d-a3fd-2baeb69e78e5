import { LocationShareURLOption } from "./p";
import { PoiDetailShareURLOption } from "./q";
import { RouteShareURLOption } from "./r";
export declare class ShareUrlSearch {
    private readonly iShareUrlSearch;
    private constructor();
    newInstance(): ShareUrlSearch;
    /**
     * 请求poi详情分享URL
     */
    requestPoiDetailShareUrl(option: PoiDetailShareURLOption): void;
    /**
     * 请求位置信息分享URL
     */
    requestLocationShareUrl(option: LocationShareURLOption): void;
    /**
     * 路线规划短串分享
     */
    requestRouteShareUrl(option: RouteShareURLOption): void;
}
