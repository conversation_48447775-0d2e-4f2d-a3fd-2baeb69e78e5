import { DeviceMetadata } from "../../h1/o1"; import { PreferencesManager } from "../j1"; import { RuntimeDataManager } from "../k1"; import { BaseInfoManager } from "./n1"; export declare class CoordTypeInfoManager extends BaseInfoManager<DeviceMetadata.GlobalCoordType> { protected _preferencesManager: PreferencesManager<DeviceMetadata.GlobalCoordType>; protected _runtimeDataManager: RuntimeDataManager<DeviceMetadata.GlobalCoordType>; } 