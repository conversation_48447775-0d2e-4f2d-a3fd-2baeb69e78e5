// @keepTs
// @ts-nocheck
import { UiMessage } from '../../../conversation/model/UiMessage';
import { ConversationComponentData } from '../../../conversation/model/ConversationComponentData';
import { IBusinessState } from './IBusinessState';
type DataResult = (localData: UiMessage[]) => void;
export declare class NormalBusinessState implements IBusinessState {
    private conversationData;
    onInit(l265: ConversationComponentData, m265?: DataResult): void;
    onLoadMore(j265: ConversationComponentData, k265?: DataResult): void;
    /**
     * 获取历史消息
     * 先拉取本地消息，再去拉远端消息并和本地消息排重组合
     */
    private getHistoryMessages;
    /**
     * 按发送时间，降序排序
     * @param array
     * @returns
     */
    private sort;
}
export {};
