import { SearchResult } from "../../../d/e/h/f1";
import { SearchParser } from "../../base/base";
/**
 * SuggestionSearchParser 类用于解析建议查询的结果
 */
export declare class SuggestionSearchParser extends SearchParser {
    private static readonly TAG;
    /**
     * 解析搜索结果
     * @param json - JSON 格式的搜索结果字符串
     * @returns 解析后的搜索结果
     */
    parseSearchResult(p32: string): SearchResult;
    /**
     * 解析建议结果
     * @param strRst - JSON 格式的结果字符串
     * @param result - 解析后的结果对象
     * @returns 是否成功解析
     */
    private parseSuggestionResult;
    /**
     * 解析建议信息
     * @param jsonObject - JSON 对象
     * @param suggestionResult - 解析后的建议结果对象
     * @returns 是否成功解析
     */
    private parseSuggestionInfo;
    /**
     * 获取 POI 位置
     * @param locationJson - 位置的 JSON 对象
     * @returns 解析后的 LatLng 对象
     */
    private getPoiLocation;
    /**
     * 解析 POI 子信息
     * @param poiChildrenPoiJson - POI 子信息的 JSON 数组
     * @returns 解析后的 PoiChildrenInfo 列表
     */
    private parsePoiChildrenInfo;
}
