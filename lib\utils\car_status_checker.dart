import 'package:intl/intl.dart';
import 'sp_util.dart';
import 'package:wuling_flutter_app/utils/manager/log_manager.dart';
import 'package:wuling_flutter_app/utils/str_util.dart';

class CarStatusChecker {
  // 单例实例
  static final CarStatusChecker _instance = CarStatusChecker._internal();
  final log = LogManager();
  // 私有构造函数
  CarStatusChecker._internal();

  // 工厂构造函数，返回单例实例
  factory CarStatusChecker() {
    return _instance;
  }

  /// 比较传入的时间和保存时间，判定状态是否过期
  /// [timeStr] 时间字符串，格式为 yyyy-MM-dd HH:mm:ss
  /// [vin] 车辆识别码
  Future<bool> isCarStatusOutOfDateWithTimeStr(
      String? timeStr, String vin) async {
    if (timeStr == null ||
        timeStr.length < 10 ||
        !StrUtil.isDateTime(timeStr)) {
      return false;
    }
    String savedTimeStr = SpUtil().getString(_carStatusCollectionTimeFor(vin));

    if (!StrUtil.isDateTime(savedTimeStr) || savedTimeStr.isEmpty) {
      // 没有找到对应状态采集时间，则直接保存当前的 collectionTime 作为当前的状态时间
      log.info("[TIME_TEST]===${vin}保存时间为空，输入时间为${timeStr}，保存输入时间为状态时间");
      await saveCarStatusTimeWithTimeStr(timeStr, vin);
      return true;
    } else {
      DateTime newTime = DateFormat("yyyy-MM-dd HH:mm:ss").parse(timeStr);
      DateTime savedTime =
          DateFormat("yyyy-MM-dd HH:mm:ss").parse(savedTimeStr);
      Duration dur = newTime.difference(savedTime);

      if (dur.inMilliseconds < 0) {
        // 如果传入的时间不比保存的时间晚，说明当前状态尚未过期
        log.info(
            "[TIME_TEST]===${vin}保存时间为${savedTimeStr}，输入时间为${timeStr}，相差${dur.inMilliseconds}，状态未过期");
        return false;
      } else {
        // 如果传入的时间比保存的时间晚，说明当前状态过期, 更新状态并保存当前的 collectionTime 作为当前的状态时间
        log.info(
            "[TIME_TEST]===${vin}保存时间为${savedTimeStr}，输入时间为${timeStr}，相差${dur.inMilliseconds}，状态已过期，更新输入时间为状态时间");
        await saveCarStatusTimeWithTimeStr(timeStr, vin);
        return true;
      }
    }
  }

  /// 保存车辆状态采集时间
  Future<void> saveCarStatusTimeWithTimeStr(String timeStr, String vin) async {
    LogManager().debug("[TIME_TEST]===保存$vin的新状态时间为$timeStr");
    await SpUtil().setString(_carStatusCollectionTimeFor(vin), timeStr);
  }

  /// 获取存储键的帮助方法
  String _carStatusCollectionTimeFor(String vin) {
    return 'carStatusCollectionTime_$vin';
  }
}
