import { PlayerErrorCode } from './PlayerErrorCode';
export declare class ErrorInfo {
    private mCode;
    private mMsg;
    private mExtra;
    /**
     * 获取错误码
     *
     * @return 错误码
     */
    /****
     * Query error codes.
     *
     * @return The returned error codes.
     */
    getCode(): PlayerErrorCode;
    /**
     * 设置错误码
     *
     * @param mCode 错误码
     */
    /****
     * Set error codes.
     *
     * @param mCode Error codes.
     */
    setCode(h20: PlayerErrorCode): void;
    /**
     * 获取错误消息
     *
     * @return 错误消息
     */
    /****
     * Query error messages.
     *
     * @return Error messages.
     */
    getMsg(): string;
    /**
     * 设置错误消息
     *
     * @param mMsg 错误消息
     */
    /****
     * Set error messages.
     *
     * @param mMsg Error messages.
     */
    setMsg(g20: string): void;
    /**
     * 获取额外信息
     *
     * @return 额外信息
     */
    /****
     * Query additional information.
     *
     * @return Additional information.
     */
    getExtra(): string;
    /**
     * 设置额外信息
     *
     * @param mExtra 额外信息
     */
    /****
     * Set additional information.
     *
     * @param mExtra Additional information.
     */
    setExtra(f20: string): void;
}
