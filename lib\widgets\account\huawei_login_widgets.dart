import 'dart:convert';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/constant/constant.dart';
import 'package:wuling_flutter_app/utils/manager/loading_manager.dart';
import 'package:wuling_flutter_app/utils/manager/login_manager.dart';
import 'package:wuling_flutter_app/utils/manager/notification_manager.dart';
import 'package:wuling_flutter_app/widgets/account/custom_huaweiid_button_ohos_view.dart';

import '../../page/account/login_verification_code_page.dart';
import '../../utils/http/api_exception.dart';
import '../../utils/manager/dialog_manager.dart';
import '../../utils/manager/log_manager.dart';
import '../common/custom_dialog.dart';
import '../common/image_view.dart';
import '../webview/webview.dart';

class HuaweiLoginWidget extends StatefulWidget {
  final String phoneNum;
  final Function otherClicked;
  const HuaweiLoginWidget(
      {super.key, required this.phoneNum, required this.otherClicked});

  @override
  State<HuaweiLoginWidget> createState() => _HuaweiLoginWidgetState();
}

class _HuaweiLoginWidgetState extends State<HuaweiLoginWidget> {
  CustomViewController? _controller;
  bool isSelected = false;

  @override
  Widget build(BuildContext context) {
    double heightScale = MediaQuery.of(context).size.height / 667;
    double weightScale = MediaQuery.of(context).size.width / 375;
    final ThemeData theme = Theme.of(context);
    final ColorScheme colorScheme = theme.colorScheme;
    return Scaffold(
      body: SafeArea(
        child: GestureDetector(
          onTap: () {
            // 调用这个方法来隐藏键盘
            FocusScope.of(context).unfocus();
          },
          child: Stack(
            children: [
              Container(
                color: Colors.white,
              ),
              // 背景图片
              Container(
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(
                        'assets/images/login/login_bg.png'), // 背景图片路径
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Expanded(
                        child: Column(
                      children: [
                        const SizedBox(height: 40),
                        GestureDetector(
                          onTap: () {
                            // 处理点击事件
                            Navigator.pop(context);
                          },
                          child: Row(
                            children: const [
                              Icon(Icons.arrow_back_ios, color: Colors.black),
                            ],
                          ),
                        ),
                        SizedBox(height: 120 * heightScale),
                        SizedBox(
                          height: 42,
                          width: 80,
                          child: ImageView(
                            'assets/images/profile_page/mine_wuling_logo.png',
                            fit: BoxFit.fill,
                          ),
                        ),
                        SizedBox(height: 50 * heightScale),
                        Center(
                          child: Text(
                            widget.phoneNum,
                            style: const TextStyle(
                                fontSize: 28,
                                fontWeight: FontWeight.w600,
                                color: Colors.black),
                          ),
                        ),
                        SizedBox(height: 20 * heightScale),
                        const Center(
                          child: Text(
                            "华为账号绑定号码",
                            style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w400,
                                color: Color(0xFFA4A4A4)),
                          ),
                        ),
                        SizedBox(height: 40 * heightScale),
                        Container(
                          margin: const EdgeInsets.only(left: 10, right: 10),
                          height: 40,
                          child: CustomHuaweiIDButtonOhosView(
                              _onCustomOhosViewCreated),
                        ),
                        SizedBox(height: 18 * heightScale),
                        GestureDetector(
                          onTap: () {
                            widget.otherClicked.call();
                          },
                          child: Container(
                            margin: const EdgeInsets.only(left: 10, right: 10),
                            height: 40,
                            decoration: BoxDecoration(
                              color: colorScheme.onSurface.withOpacity(0.38),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Center(
                              child: Text(
                                "其他方式登录",
                                style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.white),
                              ),
                            ),
                          ),
                        ),
                      ],
                    )),
                    Padding(
                      padding: EdgeInsets.only(
                          left: 30 * weightScale - 16,
                          right: 40 * weightScale - 16),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          GestureDetector(
                              onTap: () {
                                setState(() {
                                  _changeSelected();
                                });
                              },
                              child: Padding(
                                padding: EdgeInsets.only(
                                    left: 10 * weightScale,
                                    right: 10,
                                    top: 4,
                                    bottom: 10),
                                child: SizedBox(
                                  height: 16,
                                  width: 16,
                                  child: ImageView(
                                    isSelected
                                        ? 'assets/images/use_car_page/add_authorization_page/preAir_sel_sel.png'
                                        : 'assets/images/use_car_page/add_authorization_page/preAir_sel_nor.png',
                                    fit: BoxFit.fill,
                                  ),
                                ),
                              )),
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  _changeSelected();
                                });
                              },
                              child: Text.rich(
                                TextSpan(
                                  style: const TextStyle(
                                    color: Color(0xff7b7b7b),
                                    fontSize: 12,
                                    height: 1.5,
                                  ),
                                  children: [
                                    const TextSpan(
                                      text: '我已阅读并同意',
                                    ),
                                    TextSpan(
                                      text: '《五菱汽车用户协议》',
                                      style: const TextStyle(
                                        color: Colors.blue,
                                        // decoration: TextDecoration.underline,
                                      ),
                                      recognizer: TapGestureRecognizer()
                                        ..onTap = () {
                                          jumpToPrivacy(1);
                                        },
                                    ),
                                    const TextSpan(text: ' '),
                                    TextSpan(
                                      text: '《隐私政策》',
                                      style: const TextStyle(
                                        color: Colors.blue,
                                        // decoration: TextDecoration.underline,
                                      ),
                                      recognizer: TapGestureRecognizer()
                                        ..onTap = () {
                                          jumpToPrivacy(2);
                                        },
                                    ),
                                    const TextSpan(text: '和'),
                                    TextSpan(
                                      text: '《华为账号用户认证协议》',
                                      style: const TextStyle(
                                        color: Colors.blue,
                                        // decoration: TextDecoration.underline,
                                      ),
                                      recognizer: TapGestureRecognizer()
                                        ..onTap = () {
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) => WebViewPage(
                                                url:
                                                    'https://privacy.consumer.huawei.com/legal/id/authentication-terms.htm?code=CN&language=zh-CN',
                                                needTitleBar: true,
                                                titleName: '华为账号用户认证协议',
                                              ),
                                            ),
                                          );
                                        },
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                    SizedBox(height: 26 * heightScale),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void jumpToPrivacy(int type) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WebViewPage(
          url:
              'https://cdn-m.00bang.cn/llb/html/agreement/linglingProtocol.html?protocolType=$type',
          needTitleBar: false,
        ),
      ),
    );
  }

  void _onCustomOhosViewCreated(CustomViewController controller) {
    _controller = controller;
    _controller?.customDataStream.listen((data) {
      //接收到来自OHOS端的数据
      setState(() {
        LogManager().debug('接收到来自OHOS端的数据----data:$data');
        Map<String, dynamic> ohosData = jsonDecode(data);
        String authCode = ohosData['authCode'];
        if (authCode.isNotEmpty) {
          _loginWithHuaiwei(authCode);
        }else{
          if(ohosData['errorCode'] == 1005300001){
            _showCustomDialog(context);
          }else{
            String errorMessage = ohosData['errorMessage'];
            LoadingManager.showError(errorMessage);
          }
        }
      });
    });
  }

  void _showCustomDialog(BuildContext context) {
    final ThemeData theme = Theme.of(context);
    final ColorScheme colorScheme = theme.colorScheme;
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Center(
          child: Container(
            width: MediaQuery.of(context).size.width * 0.8,
            padding: const EdgeInsets.all(20.0),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10.0), // 圆角
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 10.0,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text(
                  '请先阅读并同意',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                GestureDetector(
                    onTap: () {
                      setState(() {
                        _changeSelected();
                      });
                    },
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _changeSelected();
                        });
                      },
                      child: Text.rich(
                        TextSpan(
                          style: const TextStyle(
                            color: Color(0xff7b7b7b),
                            fontSize: 14,
                            height: 1.5,
                          ),
                          children: [
                            TextSpan(
                              text: '《五菱汽车用户协议》',
                              style: const TextStyle(
                                color: Colors.blue,
                                // decoration: TextDecoration.underline,
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  jumpToPrivacy(1);
                                },
                            ),
                            const TextSpan(text: '  '),
                            TextSpan(
                              text: '《隐私政策》',
                              style: const TextStyle(
                                color: Colors.blue,
                                // decoration: TextDecoration.underline,
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  jumpToPrivacy(2);
                                },
                            ),
                            const TextSpan(text: ' 以及 '),
                            TextSpan(
                              text: '《华为账号用户认证协议》',
                              style: const TextStyle(
                                color: Colors.blue,
                                // decoration: TextDecoration.underline,
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => WebViewPage(
                                        url:
                                            'https://privacy.consumer.huawei.com/legal/id/authentication-terms.htm?code=CN&language=zh-CN',
                                        needTitleBar: true,
                                        titleName: '华为账号用户认证协议',
                                      ),
                                    ),
                                  );
                                },
                            ),
                          ],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    )),
                const SizedBox(height: 16),
                GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop();
                    _changeSelected();
                    _controller?.sendContinueLoginToOhosView();
                  },
                  child: Container(
                    // width: MediaQuery.of(context).size.width * 0.8,
                    margin: const EdgeInsets.only(left: 10, right: 10),
                    height: 40,
                    decoration: BoxDecoration(
                      color: const Color(0xFFEA0029),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: const Center(
                      child: Text(
                        "同意并登录",
                        style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.white),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                GestureDetector(
                  onTap: () {
                    Navigator.of(context).pop();
                  },
                  child: Container(
                    // width: MediaQuery.of(context).size.width * 0.8,
                    margin: const EdgeInsets.only(left: 10, right: 10),
                    height: 40,
                    decoration: BoxDecoration(
                      color: colorScheme.onSurface.withOpacity(0.38),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: const Center(
                      child: Text(
                        "取消",
                        style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                            color: Colors.white),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        );
      },
    );
  }

  void showNotRegisterAlertDialog(BuildContext context) {
    DialogManager().showCustomDialog(
      CustomDialog(
        title: "提示",
        titleFontSize: 16,
        content: "您登录的号码尚未注册五菱账号",
        contentFontSize: 14,
        contentColor: Colors.black,
        buttonHeight: 70,
        horizontalPadding: 20,
        buttons: [
          DialogButton(
              label: "继续逛逛",
              onPressed: () {
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xFF9B9DA9)),
          DialogButton(
              label: "去注册",
              onPressed: () {
                // 处理注册
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) =>
                        LoginVerificationCodePage(
                            type:
                            LoginVerificationCodeType.Register),
                  ),
                );
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xffea0029)),
        ],
      ),
    );
  }

  void _changeSelected() {
    isSelected = !isSelected;
    _controller?.sendMessageToOhosView(isSelected ? '1' : '0');
  }

  void _loginWithHuaiwei(String authCode) async {
    LoadingManager.show(status: '登录中...');
    try {
      await LoginManager().oauthLoginWithHuawei(authCode: authCode);
      await LoginManager().getSelfInfo();
      NotificationManager()
          .postNotification(Constant.NOTIFICATION_LOGIN_SUCCEED);
      LoadingManager.dismiss();
      if (!mounted) return;
      Navigator.popUntil(context, (route) => route.isFirst);
    } catch (e) {
      LoadingManager.dismiss();
      if(e is APIException){
        if(e.errorCode == "161036"){
          showNotRegisterAlertDialog(context);
        }
      }
      return;
    }
  }
}
