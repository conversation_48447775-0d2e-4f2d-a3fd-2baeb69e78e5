// @keepTs
// @ts-nocheck
/**
 * Created on 2024/07/09
 * <AUTHOR>
 */
import { ConversationIdentifier } from '@rongcloud/imlib';
import { BaseUiConversation } from '../../../conversationlist/model/BaseUiConversation';
import { ListDataSource } from '../../base/datasource/ListDataSource';
/**
 * 会话列表数据源
 * @version 1.0.0
 */
@Observed
export declare class ConversationListDataSource extends ListDataSource<BaseUiConversation> {
    private cacheConversation;
    addDataByIndex(o267: number, p267: BaseUiConversation | undefined): void;
    addData(n267: BaseUiConversation | undefined): void;
    private updateConversation;
    /**
     * List 内是否包含指定的数据
     */
    has(h267: BaseUiConversation | undefined): boolean;
    /**
     * 通过 conversation 找到 BaseUiConversation
     * @param conversation conversation
     * @returns BaseUiConversation
     */
    findConversation(d267: ConversationIdentifier): BaseUiConversation | undefined | null;
    findPosition(c267: BaseUiConversation | undefined | null): number;
    removeConversation(z266: ConversationIdentifier): void;
}
