import 'dart:ffi';
// import 'dart:html';
import 'dart:ui';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/constant/constant.dart';
import 'package:wuling_flutter_app/models/common/advertise.dart';
import 'package:wuling_flutter_app/models/common/advertise_position_model.dart';
import 'package:wuling_flutter_app/routes/app_routes.dart';
import 'package:wuling_flutter_app/widgets/common/custom_text_indicator.dart';
import 'package:wuling_flutter_app/widgets/common/image_view.dart';
import 'package:wuling_flutter_app/widgets/common/custom_page_view.dart';
import 'package:wuling_flutter_app/widgets/detail_page/car_advertise_detail_pic.dart';
import '../../models/common/shop_car_type.dart';
import '../../utils/app_config_util.dart';
import '../../utils/manager/dialog_manager.dart';
import '../../utils/manager/log_manager.dart';
import '../common/custom_dialog.dart';

class ImageCardView extends StatelessWidget {

  final String strUrl;
  final int index;
  final List<ImgText> imgTextList;
  final String httpUrlPre;
  const ImageCardView({super.key,required this.strUrl,required this.index,required this.imgTextList,required this.httpUrlPre});



  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Column(
      children: [
        GestureDetector(
          child:
          CachedNetworkImage(
            imageUrl: httpUrlPre + (strUrl ?? ""),
            placeholder: (context, url) => CircularProgressIndicator(),
            errorWidget: (context, url, error) => Icon(Icons.error),
            fit: BoxFit.fitWidth,
          ),
          onTap: () {
            // CarAdvertiseDetailPic
            Navigator.of(context).push(CustomCupertinoPageRoute(
              builder: (context) => CarAdvertiseDetailPic(imgList: imgTextList,httpPre: httpUrlPre,),
              canSwipeBack: false, // 禁用手势返回
            ));
          },
        ),
      ],
    );
  }
}



class CarAdvertiseDetailGridImage extends StatelessWidget {
  // final ImgText? advertise;
  final List<ImgText> imgTextList;
  final  h = 10;
  final  String  httpPre;
  const CarAdvertiseDetailGridImage({super.key,required this.imgTextList,required this.httpPre});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: _getListData(),
    );
  }

  List<Widget> _getListData() {
    List<Widget> list = [];

    for(int i=0;i<imgTextList.length;i++) {
      ImgText r = imgTextList[i];
      list.add(ImageCardView(strUrl: r.img,index: i,imgTextList: imgTextList,httpUrlPre: httpPre,));
    }
    return list;
  }

  static Widget getDialog(String item) {
    return  AlertDialog(
      title: Text(
        //标题
        '提示',
        style: new TextStyle(color: Colors.red[300], fontSize: 18),
      ),
      content: new Text(item), //提示语
    );
  }

  void showNotLoginAlertDialog(BuildContext context) {
    DialogManager().showCustomDialog(
      CustomDialog(
        content: "您还没有登录",
        contentFontSize: 18,
        contentColor: Colors.black,
        buttonHeight: 70,
        horizontalPadding: 20,
        buttons: [
          DialogButton(
              label: "继续逛逛",
              onPressed: () {
                LogManager().debug("取消按钮被点击");
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xFF9B9DA9)),
          DialogButton(
              label: "去登录",
              onPressed: () {
                // LoginManager().showLoginModal();
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xffea0029)),
        ],
      ),
    );
  }
}

