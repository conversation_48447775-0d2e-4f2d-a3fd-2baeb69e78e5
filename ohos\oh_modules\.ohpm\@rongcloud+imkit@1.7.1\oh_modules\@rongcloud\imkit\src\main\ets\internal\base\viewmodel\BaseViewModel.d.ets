// @keepTs
// @ts-nocheck
/**
 * Created on 2024/07/15
 * <AUTHOR>
 */
import { ConversationComponentData } from '../../../conversation/model/ConversationComponentData';
/**
 * 基础的 ViewModel
 * @version 1.0.0
 */
export declare abstract class BaseViewModel {
    constructor();
    /**
     * 绑定 ViewModel
     */
    abstract onBind(r54: ConversationComponentData): void;
    /**
     * 释放数据
     */
    abstract onCleared(): void;
}
