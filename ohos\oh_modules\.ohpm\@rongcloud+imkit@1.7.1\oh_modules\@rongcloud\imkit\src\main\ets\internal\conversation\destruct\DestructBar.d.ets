// @keepTs
// @ts-nocheck
import { InputAreaController } from '../inputbar/InputAreaController';
@Component
export declare struct DestructBar {
    private touchesY;
    private intervalId;
    private timeCount;
    private pauseCount;
    private audioTapTs;
    private hasBeenSent;
    private GESTURE_DISTANCE;
    @State
    count: number;
    @State
    voiceTextOpacity: number;
    @State
    mode: number;
    @Link
    inputAreaController: InputAreaController;
    @State
    dialogTxtBgColor: Resource;
    @State
    dialogCancelText: Resource;
    @State
    voiceInputTouchContent: string | Resource;
    @State
    dialogImageRes: Resource;
    private messageClickListener;
    private inputAreaComponentConfig;
    private audioDialog;
    private callStateChangeObserver;
    private RecallEditClickEventCallback;
    private AVRecorderEventCallback;
    private applicationStateChangeCallback;
    aboutToAppear(): void;
    aboutToDisappear(): void;
    private getComponentConfigVisibility;
    private getConfigContentBuilder;
    private getConfigComponent;
    build(): void;
    @Builder
    VoiceOrKeyboardButton(): void;
    @Builder
    TextAreaOrVoiceButton(): void;
    @Builder
    MediaButton(): void;
    @Builder
    SendOrPlusButton(): void;
    @Builder
    SendButton(): void;
    @Builder
    DeleteButton(): void;
    /**
     * 语音模式下，手指按下
     */
    private onTouchByDown;
    /**
     * 设置语音录制时长限制
     *
     * @param state
     */
    private changeIntervalForAVRecorderState;
    /**
     * 停止录音并发送消息
     */
    private stopRecordingAndSendMessage;
    /**
     * 销毁语音时长计时器并且重置秒数和id
     */
    private clearRecordTimeInterval;
    /**
     * 语音输入模式松开
     */
    private onTouchByUp;
    /**
     * 语音输入模式移动
     * @param event
     */
    private onTouchByMove;
    /**
     * 根据录音的振幅获取图片
     * @param maxAmplitude
     */
    private changeAudioImageResByMaxAmplitude;
    private convertToMentionInfo;
    /**
     * 判断是否为空
     * @param str
     * @returns
     */
    private isSpace;
}
