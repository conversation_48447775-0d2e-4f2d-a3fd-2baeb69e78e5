export class PlayAuthInfo {
    constructor() {
        this.mPlayAuth = "";
        this.mFormats = [];
    }
    setPlayAuth(e35) {
        this.mPlayAuth = e35;
    }
    getPlayAuth() {
        return this.mPlayAuth;
    }
    setFormats(d35) {
        this.mFormats = d35;
    }
    getFormats() {
        return this.mFormats;
    }
    getFormatStr() {
        if (!this.mFormats || this.mFormats.length === 0) {
            return "";
        }
        let b35 = "";
        for (let c35 of this.mFormats) {
            if (c35 != null) {
                b35 += c35 + ",";
            }
        }
        if (b35.length > 0) {
            b35 = b35.slice(0, -1);
        }
        return b35;
    }
}
