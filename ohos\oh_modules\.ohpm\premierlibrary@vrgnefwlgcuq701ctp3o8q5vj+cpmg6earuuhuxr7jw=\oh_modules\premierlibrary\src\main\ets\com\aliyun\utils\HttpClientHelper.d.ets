export declare class HttpClientHelper {
    private mUrl;
    private mReferer;
    private static CONNECTION_TIMEOUT;
    private urlRequest;
    private mNetworkTimeout;
    private mHttpProxy;
    private mUserAgent;
    private mCustomHeaders;
    constructor(z43: string);
    setRefer(y43: string | null): void;
    setTimeout(x43: number): void;
    setHttpProxy(w43: string | null): void;
    setUserAgent(v43: string | null): void;
    setCustomHeaders(u43: Array<Record<string, string>>): void;
    stop(): void;
    doGet(): Promise<string | null>;
}
