import { VidSourceBase } from './VidSourceBase';
export class VidAuth extends VidSourceBase {
    constructor() {
        super();
        this.mVid = "";
        this.mPlayAuth = "";
        this.mRegion = "";
        this.nativeGetVid = () => {
            return this.mVid;
        };
        this.nativeSetVid = (m37) => {
            this.mVid = m37;
        };
        this.nativeGetPlayAuth = () => {
            return this.mPlayAuth;
        };
        this.nativeSetPlayAuth = (l37) => {
            this.mPlayAuth = l37;
        };
        this.nativeGetRegion = () => {
            return this.mRegion;
        };
        this.nativeSetRegion = (k37) => {
            this.mRegion = k37;
        };
    }
    setQuality(c37, d37) {
        this.mQuality = c37;
        this.mForceQuality = d37;
    }
    getVid() {
        return this.mVid;
    }
    setVid(b37) {
        this.mVid = b37;
    }
    getPlayAuth() {
        return this.mPlayAuth;
    }
    setPlayAuth(a37) {
        this.mPlayAuth = a37;
    }
    getRegion() {
        return this.mRegion;
    }
    setRegion(z36) {
        this.mRegion = z36;
    }
}
