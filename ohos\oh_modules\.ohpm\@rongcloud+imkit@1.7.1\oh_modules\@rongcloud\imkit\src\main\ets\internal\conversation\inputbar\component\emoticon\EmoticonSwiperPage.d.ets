// @keepTs
// @ts-nocheck
import { ConversationIdentifier } from '@rongcloud/imlib';
import { IEmoticonTab } from '../../../../../conversation/inputbar/component/emoticon/IEmoticonTab';
/**
 * 1.5.1
 */
@Builder
export declare function buildEmoticonSwiperPageView(b129: Context, c129: ConversationIdentifier, d129: IEmoticonTab): void;
@Component
export declare struct EmoticonSwiperPage {
    @Require
    @Prop
    tab: IEmoticonTab;
    private emoticonDataSource;
    aboutToAppear(): void;
    build(): void;
    @Builder
    emojiView(): void;
    @Builder
    emojiPageView(g126: string[]): void;
}
