import emitter from '@ohos.events.emitter'; import display from '@ohos.display'; import MapController from "../h/i/j"; import MapOptions from "../h/i/k"; import { AsyncCallback } from '@ohos.base'; import { mapBoxRect, ControlStyleObj, infoPanel } from "../h/g1/f2"; import { FingerData } from "../h/g1/i1";  @Component export declare struct BMapPage { onReady?: AsyncCallback<MapController>; mapOptions: MapOptions | undefined; mapViewId: string; message: string; xcomWidth: number; xcomHeight: number; mapController: MapController | null; featureAbilityContext: Context; disOpt: display.Display; xcomponentController: XComponentController; perPixelMc: number; gestureStatus: boolean;  @State showBgMask: boolean;  @State zoom: ControlStyleObj;  @State scaledata: ControlStyleObj;  @State logo: ControlStyleObj;  @State copyright: ControlStyleObj;  @State clientRect: mapBoxRect;  @State uuid: string;  @State infoPanel: Array<infoPanel>; onPageShow(): void; onPageHide(): void; onBackPress(): void; _mapRunStatus(): import("../common/mapkit/basemap").default; zoomIn(e: emitter.EventData): void; zoomOut(e: emitter.EventData): void; getPerPixel(): void; changeGeoLocation(e: emitter.EventData): void; aboutToAppear(): void; aboutToDisappear(): void; initMap(): void; readyInitMap(o58: MapController | null, code?: number, msg?: string): void; unitMouseEventTrans(input: GestureEvent): FingerData; setLogoCom(n58: ControlStyleObj): void; setZoomCom(m58: ControlStyleObj): void; setLocationCom(l58: ControlStyleObj): void; setScaleCom(j58: ControlStyleObj): void; setCopyrightCom(i58: ControlStyleObj): void; build(): void; } 