import 'dart:typed_data';
import 'package:wuling_flutter_app/utils/exceptions.dart';
import 'package:wuling_flutter_app/utils/str_util.dart';

import '../../utils/manager/log_manager.dart';

class UcuAuthorizationRequestFrame {
  String? msgResource;        // 加密前缀
  String? serviceId;          // 服务类型
  String? subFunction;        // 功能类型
  String? randomData;         // 随机数
  String? bleKey;             // bleKey
  String? payloadLength;      // payload长度
  String? errorCode;          // 错误码
  String? dataCheck;          // 数据校验
  String? encryptedFrame;     // 加密帧
  Uint8List? originalFrame;   // 原始数据帧

  UcuAuthorizationRequestFrame();

  // 使用数据帧和密钥初始化
  UcuAuthorizationRequestFrame.fromDataFrame(Uint8List ucuRequestData, String key) {
    try {
      String ucuRequestDataStr = StrUtil.bytesToHexString(ucuRequestData); // 将数据转换为十六进制字符串
      String mainDataStr;
      int len = ucuRequestDataStr.length;
      LogManager().debug("[BleKey]---UCU授权请求报文解析---");
      if (len >= 6) {
        // 提取加密前缀
        msgResource = ucuRequestDataStr.substring(0, 2);
        LogManager().debug("[BleKey]---加密前缀 = 0x${msgResource}---");
        if (msgResource == "00") {
          // 数据为加密，需要解密
          String encryptedStr = ucuRequestDataStr.substring(2, len);
          LogManager().debug("[BleKey]---加密部分：$encryptedStr---");
          LogManager().debug("[BleKey]---加密秘钥：$key---");
          try {
            mainDataStr = StrUtil.aes128Decrypt(encryptedStr, key); // AES128解密
            if (mainDataStr.isNotEmpty) {
              LogManager().debug("[BleKey]---解密数据：$mainDataStr---");
            } else {
              LogManager().debug("[BleKey]---解密结果为空---");
              throw DecryptionException("解密结果为空");
            }
          } on DecryptionException catch (e) {
            LogManager().debug("[BleKey]---解密出错：$e");
            throw ParsingException("解密失败：$e");
          } catch (e) {
            LogManager().debug("[BleKey]---解密过程中发生未知错误：$e");
            throw ParsingException("解密过程中发生未知错误：$e");
          }
        } else {
          // 数据未加密，直接获取主数据帧
          mainDataStr = ucuRequestDataStr.substring(2, len);
        }
        encryptedFrame = mainDataStr;
        int mainLen = mainDataStr.length;
        // 检查数据长度是否足够
        if (mainLen < 4) {
          throw ParsingException("主数据帧长度不足，无法解析");
        }
        // 提取数据校验码
        dataCheck = mainDataStr.substring(mainLen - 4, mainLen);
        if (crcCheckPassed()) {
          // CRC校验通过，解析数据
          LogManager().debug("[BleKey]---主数据帧 = 0x$mainDataStr---");
          serviceId = mainDataStr.substring(0, 4);
          LogManager().debug("[BleKey]---服务类型 = 0x${serviceId}---");
          if(!isCorrectServiceId()){
            throw ParsingException("服务id为$serviceId,未接收到服务类型正确的服务数据'");
          }
          subFunction = mainDataStr.substring(4, 8);
          LogManager().debug("[BleKey]---功能类型 = 0x${subFunction}---");
          if (serviceId == "FFFF") {
            // 错误处理
            if (mainLen < 26) {
              throw ParsingException("数据长度不足，无法提取错误码");
            }
            payloadLength = mainDataStr.substring(16, 18);
            LogManager().debug("[BleKey]---payload长度 = 0x${payloadLength}---");
            errorCode = mainDataStr.substring(18, 26);
            LogManager().debug("[BleKey]---错误码 = 0x${errorCode}---");
          } else {
            // 提取随机数和bleKey
            if (mainLen < 34) {
              throw ParsingException("数据长度不足，无法提取随机数和bleKey");
            }
            randomData = mainDataStr.substring(16, 24);
            LogManager().debug("[BleKey]---随机数 = 0x${randomData}---");
            bleKey = mainDataStr.substring(24, 32);
            LogManager().debug("[BleKey]---bleKey = 0x${bleKey}---");
            payloadLength = mainDataStr.substring(32, 34);
            LogManager().debug("[BleKey]---payload长度 = 0x${payloadLength}---");
          }
        } else {
          // CRC校验未通过，可能蓝牙钥匙过期，通知用户刷新
          LogManager().debug("[BleKey]---CRC校验未通过，可能蓝牙钥匙过期了---");
          throw ParsingException("CRC校验未通过，数据可能已损坏或过期");
          // 您可以在此处添加通知逻辑
        }
      } else {
        throw ParsingException("数据长度不足，无法解析");
      }
    } on ParsingException catch (e) {
      // 捕获解析异常，向上抛出或处理
      rethrow; // 向上抛出异常，供调用者处理
    } catch (e) {
      // 捕获其他未知异常
      LogManager().debug("[BleKey]---解析过程中发生未知错误：$e");
      throw ParsingException("解析过程中发生未知错误：$e");
    }
  }

  // 其他方法保持不变

  // 获取服务类型
  String getServiceId() {
    return serviceId ?? '';
  }

  // 获取功能类型
  String getSubFunction() {
    return subFunction ?? '';
  }

  // 获取随机数
  String getRandomData() {
    return randomData ?? '';
  }

  // CRC校验
  bool crcCheckPassed() {
    int crc;
    Uint8List encryptedData = StrUtil.hexStringToBytes(encryptedFrame!);
    crc =  StrUtil.crc16CcittFalse(encryptedData);
    LogManager().debug("[BleKey]---CRC校验数据：${encryptedFrame} 校验结果 = $crc ${crc == 0 ? "通过" : "未通过"}---");
    return crc == 0;
  }

  // 检查ServiceID是否正确
  bool isCorrectServiceId(){
    String serviceIdStr = serviceId ?? '';
    return serviceIdStr.isNotEmpty && serviceIdStr == 'A857';
  }

  // 服务类型为鉴权请求
  bool isUcuAuthorizeRequest(){
    String subFunctionStr = subFunction ?? '';
    return subFunctionStr.isNotEmpty && subFunctionStr == '0001';
  }

  // 服务类型为鉴权回复
  bool isUcuAuthorizeResponse(){
    String subFunctionStr = subFunction ?? '';
    return subFunctionStr.isNotEmpty && subFunctionStr == '0002';
  }
}