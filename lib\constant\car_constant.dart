// CYCarPermissionItemType 枚举类型转换
enum CarPermissionItemType {
  bluetooth(0), // 近控
  remote(1);    // 远控

  final int value;
  const CarPermissionItemType(this.value);
}

// CYCarPermissionItemState 枚举类型转换
enum CarPermissionItemState {
  mandatory(1),             // 必选
  optionalSelected(2),      // 可选-选择
  optionalUnselected(3);    // 可选-未选择

  final int value;
  const CarPermissionItemState(this.value);
}

const STEERING_WHEEL_HEATING_CLICK_COUNT = 'steeringWheelHeatingClickCount';
const DRIVER_SEAT_VENTILATE_CLICK_COUNT = 'driverSeatVentilateClickCount';
const DRIVER_SEAT_HEATING_CLICK_COUNT = 'driverSeatHeatingClickCount';
const COPILOT_SEAT_VENTILATE_CLICK_COUNT = 'copilotSeatVentilateClickCount';
const COPILOT_SEAT_HEATING_CLICK_COUNT = 'copilotSeatHeatingClickCount';
const LEFT_REAR_SEAT_VENTILATE_CLICK_COUNT = 'leftRearSeatVentilateClickCount';
const LEFT_REAR_SEAT_HEATING_CLICK_COUNT = 'leftRearSeatHeatingClickCount';
const RIGHT_REAR_SEAT_VENTILATE_CLICK_COUNT = 'rightRearSeatVentilateClickCount';
const RIGHT_REAR_SEAT_HEATING_CLICK_COUNT = 'rightRearSeatHeatingClickCount';

const String STEERING_WHEEL_HEATING_STATUS = "steeringWheelHeatingStatus";
int STEERING_WHEEL_HEATING_LEVEL = 0;

const String SEAT1_HOT_STATUS = "seat1HotStatus";
const String SEAT2_HOT_STATUS = "seat2HotStatus";
const String SEAT3_HOT_STATUS = "seat3HotStatus";
const String SEAT4_HOT_STATUS = "seat4HotStatus";
int SEAT1_HOT_LEVEL = 0;
int SEAT2_HOT_LEVEL = 0;
int SEAT3_HOT_LEVEL = 0;
int SEAT4_HOT_LEVEL = 0;

const String SEAT1_WIND_STATUS = "seat1WindStatus";
const String SEAT2_WIND_STATUS = "seat2WindStatus";
const String SEAT3_WIND_STATUS = "seat3WindStatus";
const String SEAT4_WIND_STATUS = "seat4WindStatus";
int SEAT1_WIND_LEVEL = 0;
int SEAT2_WIND_LEVEL = 0;
int SEAT3_WIND_LEVEL = 0;
int SEAT4_WIND_LEVEL = 0;
const String STATUS_MODEL = "statusModel";

//空调的开启时长缓存
const int AC_TIME_CACHE = 10;
//是否正在车控座椅加热通风： true是设置中，false是设置完成
bool IS_AC_HOT_WIND_SETTING = false;
//是否正在车控空调： true是设置中，false是设置完成
bool IS_AC_SETTING = false;
bool IS_MQTT_CAR_CONTROL = false;//是否是异步车控
String AC_TEMPERATURE = '0.0';//设置的空调温度或档位


// CarRemoteParkInPageState 枚举类型转换
enum CarRemoteParkInPageState {
  pageDefault(0),             // 初始化
  parkInIng(1),                 //正在泊入
  parkPausePrepare(2),        //即将泊入暂停
  parkPause(3),               //泊入暂停
  parkInSuccess(4);           //泊入完成
  //parkInError(5);             //泊入出错（弹窗）

  final int value;
  const CarRemoteParkInPageState(this.value);
}

// CarRemoteParkOutPageState 枚举类型转换
enum CarRemoteParkOutPageState {
  pageDefault(0),              // 初始化
  parkOutPowerOn(1),              // 上电
  parkOutPowerOnSuccess(2),       // 上电成功
  parkOutIng(3),                  //正在泊出
  parkOutPausePrepare(4),         //即将泊出暂停
  parkOutPause(5),                //泊出暂停
  parkOutSuccess(6);              //泊出完成
  //parkOutError(5);                //泊出出错（弹窗）

  final int value;
  const CarRemoteParkOutPageState(this.value);
}
const BLUE_TOOTH_KEY_CONNECT_MARK = 'bluetoothKeyConnectMark';//蓝牙钥匙自动连接开关，"0"：不支持，"1"：支持