    export interface PermissionCheckResultListener {         onGetPermissionCheckResult(result: PermissionCheckResult): void; } export declare class PermissionCheckResult { mErrorCode: number; msg: string; mToken: string; } export declare class PermissionCheck { private static mKeyMsg; private static mResultListener; private static authManager; constructor(); static init(): void; static setApiKey(y5: string): void;                                   static permissionCheck(): number; static setPermissionCheckResultListener(listener: PermissionCheckResultListener): void; } 