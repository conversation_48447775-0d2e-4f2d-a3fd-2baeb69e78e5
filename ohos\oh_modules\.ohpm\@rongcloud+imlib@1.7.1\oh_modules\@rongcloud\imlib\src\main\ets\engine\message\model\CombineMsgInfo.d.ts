import { MessageContent } from '../content/MessageContent';
import { Message } from '../Message';
/**
 * 合并消息信息
 * @version 1.6.0
 */
export declare class CombineMsgInfo {
    /**
     * 消息发送方 id
     */
    fromUserId: string;
    /**
     * 消息的目标会话 id
     */
    targetId: string;
    /**
     * 时间戳，单位毫秒
     */
    timestamp: number;
    /**
     * 消息 objectName
     */
    objectName: string;
    /**
     * 消息内容
     */
    content: MessageContent;
    /**
     * 无参构造方法
     */
    constructor();
    /**
     * 通过 Message 创建
     * @param msg 如果 msg 为空，将创建 CombineMsgInfo 的默认值
     * @returns 合并消息信息
     */
    static createWithMessage(msg: Message): CombineMsgInfo;
}
