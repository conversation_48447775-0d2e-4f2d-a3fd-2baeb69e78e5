import 'dart:convert';
import 'dart:typed_data';
import 'package:dio/dio.dart';
import 'package:wuling_flutter_app/models/car/car_mini_life_model.dart';
import 'package:wuling_flutter_app/models/car/car_service_model.dart';
import 'package:wuling_flutter_app/models/car/car_ble_key_model.dart';
import 'package:wuling_flutter_app/utils/sp_util.dart';
import 'package:wuling_flutter_app/utils/manager/log_manager.dart';

/// MiniLife 数据缓存管理器
/// 用于缓存车型互动相关数据，支持离线显示
/// 缓存永久有效，只在用户刷新时更新
class MiniLifeCacheManager {
  static const String _keyActivityList = 'mini_life_activity_list';
  static const String _keyMiniLifeModel = 'mini_life_model';
  static const String _keyBleKeyModel = 'ble_key_model';

  /// 保存活动列表到缓存
  static Future<void> saveActivityList(
      List<CarServiceModel> activityList) async {
    try {
      LogManager().debug(
          'MiniLifeCacheManager: 开始保存活动列表到缓存，数量: ${activityList.length}');

      // 打印每个活动的详细信息
      for (int i = 0; i < activityList.length; i++) {
        final activity = activityList[i];
        LogManager().debug(
            'MiniLifeCacheManager: 活动[$i] - serviceName: ${activity.serviceName}');
        LogManager().debug(
            'MiniLifeCacheManager: 活动[$i] - serviceCode: ${activity.serviceCode}');
        LogManager().debug(
            'MiniLifeCacheManager: 活动[$i] - serviceSkipTarget: ${activity.serviceSkipTarget}');
        LogManager().debug(
            'MiniLifeCacheManager: 活动[$i] - serviceDesc: ${activity.serviceDesc}');

        // 打印图片信息
        if (activity.serviceStatusList != null &&
            activity.serviceStatusList!.isNotEmpty) {
          final firstStatus = activity.serviceStatusList!.first;
          LogManager().debug(
              'MiniLifeCacheManager: 活动[$i] - serviceStatusImage: ${firstStatus.serviceStatusImage}');
        } else {
          LogManager()
              .debug('MiniLifeCacheManager: 活动[$i] - serviceStatusList为空，没有图片');
        }
      }

      List<Map<String, dynamic>> jsonList =
          activityList.map((item) => item.toJson()).toList();
      String jsonString = jsonEncode(jsonList);

      await SpUtil().setString(_keyActivityList, jsonString);

      LogManager()
          .debug('MiniLifeCacheManager: 活动列表缓存保存成功，数量: ${activityList.length}');
    } catch (e) {
      LogManager().debug('MiniLifeCacheManager: 保存活动列表缓存失败: $e');
    }
  }

  /// 保存 MiniLife 模型到缓存
  static Future<void> saveMiniLifeModel(CarMiniLifeModel? miniLifeModel) async {
    try {
      if (miniLifeModel != null) {
        LogManager().debug('MiniLifeCacheManager: 开始保存MiniLife模型到缓存');

        String jsonString = jsonEncode(miniLifeModel.toJson());
        await SpUtil().setString(_keyMiniLifeModel, jsonString);

        LogManager().debug('MiniLifeCacheManager: MiniLife模型缓存保存成功');
        LogManager().debug(
            'MiniLifeCacheManager: 模型数据 - 排名: ${miniLifeModel.ranking}, 减排: ${miniLifeModel.cutEmission}, 节省: ${miniLifeModel.save}');
      } else {
        LogManager().debug('MiniLifeCacheManager: MiniLife模型为null，跳过缓存保存');
      }
    } catch (e) {
      LogManager().debug('MiniLifeCacheManager: 保存MiniLife模型缓存失败: $e');
    }
  }

  /// 保存蓝牙钥匙到缓存
  static Future<void> saveBleKeyModel(CarBleKeyModel? bleKeyModel) async {
    try {
      if (bleKeyModel != null) {
        LogManager().debug('MiniLifeCacheManager: 开始保存蓝牙钥匙到缓存');
        LogManager()
            .debug('MiniLifeCacheManager: 蓝牙钥匙 bleMac: ${bleKeyModel.bleMac}');

        String jsonString = jsonEncode(bleKeyModel.toJson());
        await SpUtil().setString(_keyBleKeyModel, jsonString);

        LogManager().debug('MiniLifeCacheManager: 蓝牙钥匙缓存保存成功');
      } else {
        LogManager().debug('MiniLifeCacheManager: 蓝牙钥匙为null，跳过缓存保存');
      }
    } catch (e) {
      LogManager().debug('MiniLifeCacheManager: 保存蓝牙钥匙缓存失败: $e');
    }
  }

  /// 同时保存活动列表和 MiniLife 模型
  static Future<void> saveMiniLifeData({
    required List<CarServiceModel> activityList,
    CarMiniLifeModel? miniLifeModel,
  }) async {
    LogManager().debug('MiniLifeCacheManager: 开始批量保存MiniLife数据');
    LogManager().debug(
        'MiniLifeCacheManager: 活动列表数量: ${activityList.length}, MiniLife模型: ${miniLifeModel != null ? "有数据" : "null"}');

    await Future.wait([
      saveActivityList(activityList),
      saveMiniLifeModel(miniLifeModel),
    ]);

    LogManager().debug('MiniLifeCacheManager: MiniLife数据批量保存完成');
  }

  /// 从缓存获取活动列表
  static Future<List<CarServiceModel>> getCachedActivityList() async {
    try {
      LogManager().debug('MiniLifeCacheManager: 开始从缓存获取活动列表');

      String jsonString = SpUtil().getString(_keyActivityList);
      if (jsonString.isNotEmpty) {
        List<dynamic> jsonList = jsonDecode(jsonString);
        List<CarServiceModel> activityList = jsonList
            .map((json) =>
                CarServiceModel.fromJson(json as Map<String, dynamic>))
            .toList();

        LogManager().debug(
            'MiniLifeCacheManager: 从缓存获取活动列表成功，数量: ${activityList.length}');

        LogManager().debug('MiniLifeCacheManager: 准备打印活动详细信息...');

        // 打印每个活动的详细信息
        for (int i = 0; i < activityList.length; i++) {
          LogManager().debug('MiniLifeCacheManager: 开始打印活动[$i]的信息');
          final activity = activityList[i];
          LogManager().debug(
              'MiniLifeCacheManager: 缓存活动[$i] - serviceName: ${activity.serviceName}');
          LogManager().debug(
              'MiniLifeCacheManager: 缓存活动[$i] - serviceCode: ${activity.serviceCode}');
          LogManager().debug(
              'MiniLifeCacheManager: 缓存活动[$i] - serviceSkipTarget: ${activity.serviceSkipTarget}');
          LogManager().debug(
              'MiniLifeCacheManager: 缓存活动[$i] - serviceDesc: ${activity.serviceDesc}');

          // 打印图片信息
          if (activity.serviceStatusList != null &&
              activity.serviceStatusList!.isNotEmpty) {
            final firstStatus = activity.serviceStatusList!.first;
            LogManager().debug(
                'MiniLifeCacheManager: 缓存活动[$i] - serviceStatusImage: ${firstStatus.serviceStatusImage}');
          } else {
            LogManager().debug(
                'MiniLifeCacheManager: 缓存活动[$i] - serviceStatusList为空，没有图片');
          }
        }

        return activityList;
      } else {
        LogManager().debug('MiniLifeCacheManager: 缓存中没有活动列表数据');
      }
    } catch (e) {
      LogManager().debug('MiniLifeCacheManager: 获取缓存活动列表失败: $e');
    }
    return [];
  }

  /// 从缓存获取 MiniLife 模型
  static Future<CarMiniLifeModel?> getCachedMiniLifeModel() async {
    try {
      LogManager().debug('MiniLifeCacheManager: 开始从缓存获取MiniLife模型');

      String jsonString = SpUtil().getString(_keyMiniLifeModel);
      if (jsonString.isNotEmpty) {
        Map<String, dynamic> json = jsonDecode(jsonString);
        CarMiniLifeModel miniLifeModel = CarMiniLifeModel.fromJson(json);

        LogManager().debug('MiniLifeCacheManager: 从缓存获取MiniLife模型成功');
        LogManager().debug(
            'MiniLifeCacheManager: 模型数据 - 排名: ${miniLifeModel.ranking}, 减排: ${miniLifeModel.cutEmission}, 节省: ${miniLifeModel.save}');
        return miniLifeModel;
      } else {
        LogManager().debug('MiniLifeCacheManager: 缓存中没有MiniLife模型数据');
      }
    } catch (e) {
      LogManager().debug('MiniLifeCacheManager: 获取缓存MiniLife模型失败: $e');
    }
    return null;
  }

  /// 从缓存获取蓝牙钥匙
  static Future<CarBleKeyModel?> getCachedBleKeyModel() async {
    try {
      LogManager().debug('MiniLifeCacheManager: 开始从缓存获取蓝牙钥匙');

      String jsonString = SpUtil().getString(_keyBleKeyModel);
      if (jsonString.isNotEmpty) {
        Map<String, dynamic> json = jsonDecode(jsonString);
        CarBleKeyModel bleKeyModel = CarBleKeyModel.fromJson(json);

        LogManager().debug('MiniLifeCacheManager: 从缓存获取蓝牙钥匙成功');
        LogManager()
            .debug('MiniLifeCacheManager: 蓝牙钥匙 bleMac: ${bleKeyModel.bleMac}');
        return bleKeyModel;
      } else {
        LogManager().debug('MiniLifeCacheManager: 缓存中没有蓝牙钥匙数据');
      }
    } catch (e) {
      LogManager().debug('MiniLifeCacheManager: 获取缓存蓝牙钥匙失败: $e');
    }
    return null;
  }

  /// 获取缓存的 MiniLife 数据
  static Future<MiniLifeCacheData> getCachedMiniLifeData() async {
    LogManager().debug('MiniLifeCacheManager: 开始获取完整的MiniLife缓存数据');

    final activityList = await getCachedActivityList();
    final miniLifeModel = await getCachedMiniLifeModel();

    bool isFromCache = activityList.isNotEmpty || miniLifeModel != null;

    LogManager().debug('MiniLifeCacheManager: MiniLife缓存数据获取完成');
    LogManager().debug(
        'MiniLifeCacheManager: 活动列表数量: ${activityList.length}, MiniLife模型: ${miniLifeModel != null ? "有数据" : "null"}, 是否来自缓存: $isFromCache');

    return MiniLifeCacheData(
      activityList: activityList,
      miniLifeModel: miniLifeModel,
      isFromCache: isFromCache,
    );
  }

  /// 清除所有缓存
  static Future<void> clearCache() async {
    try {
      LogManager().debug('MiniLifeCacheManager: 开始清除所有MiniLife缓存');

      await Future.wait([
        SpUtil().remove(_keyActivityList),
        SpUtil().remove(_keyMiniLifeModel),
        SpUtil().remove(_keyBleKeyModel),
      ]);

      LogManager().debug('MiniLifeCacheManager: MiniLife缓存清除成功');
    } catch (e) {
      LogManager().debug('MiniLifeCacheManager: 清除MiniLife缓存失败: $e');
    }
  }

  /// 检查是否有缓存数据
  static Future<bool> hasCachedData() async {
    try {
      String activityListCache = SpUtil().getString(_keyActivityList);
      String miniLifeModelCache = SpUtil().getString(_keyMiniLifeModel);
      String bleKeyModelCache = SpUtil().getString(_keyBleKeyModel);

      bool hasData = activityListCache.isNotEmpty ||
          miniLifeModelCache.isNotEmpty ||
          bleKeyModelCache.isNotEmpty;

      LogManager().debug(
          'MiniLifeCacheManager: 缓存数据检查 - 活动列表: ${activityListCache.isNotEmpty ? "有数据" : "无数据"}, MiniLife模型: ${miniLifeModelCache.isNotEmpty ? "有数据" : "无数据"}, 蓝牙钥匙: ${bleKeyModelCache.isNotEmpty ? "有数据" : "无数据"}');
      LogManager().debug('MiniLifeCacheManager: 是否有缓存数据: $hasData');

      return hasData;
    } catch (e) {
      LogManager().debug('MiniLifeCacheManager: 检查缓存数据失败: $e');
      return false;
    }
  }
}

/// MiniLife 缓存数据模型
class MiniLifeCacheData {
  final List<CarServiceModel> activityList;
  final CarMiniLifeModel? miniLifeModel;
  final bool isFromCache;

  MiniLifeCacheData({
    required this.activityList,
    this.miniLifeModel,
    required this.isFromCache,
  });
}
