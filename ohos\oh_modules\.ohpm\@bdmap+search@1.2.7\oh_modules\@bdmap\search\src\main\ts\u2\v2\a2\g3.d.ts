import { SearchResult } from "../../../d/e/h/f1";
import { SearchParser } from '../../base/base';
export declare class AoiParser extends SearchParser {
    private static readonly TAG;
    /**
     * @description
     * 解析搜索结果，返回包含错误信息的对象。如果没有错误，则解析 AoiResult。
     *
     * @param json {string} - 搜索结果的 JSON 字符串，包含 AoiResult 数据。
     *
     * @returns {SearchResult} - 包含错误信息和 AoiResult 数据的对象。
     * 如果没有错误，则 error 属性为 undefined；否则为 ERRORNO 中定义的错误类型。
     */
    parseSearchResult(l5: string): SearchResult;
    /**
     * @description
     * 解析AOI查询结果，包括成功和失败的情况。
     * 如果是成功的结果，则调用parseAoiInfo方法进行解析。
     * 否则，根据不同的状态码，将aoiResult中的error设置为对应的错误码。
     *
     * @param resultJson {string} - AOI查询结果JSON字符串。
     * @param aoiResult {AoiResult} - AOI查询结果对象，包含error属性。
     *
     * @returns {boolean} - 返回一个布尔值，表示是否解析成功。true表示解析成功，false表示解析失败。
     */
    private parseAoiResult;
    /**
     * @description
     * 解析AOI信息，将其存储在aoiResult中。
     *
     * @param jsonObject {any} - JSON对象，包含AOI数组信息。必须包含'aoi_array'属性。
     * @param aoiResult {AoiResult} - AoiResult类型的对象，用于存储AOI信息。
     *
     * @returns {boolean} - 返回一个布尔值，表示是否成功解析了AOI信息。如果没有解析到任何AOI信息，则返回false。
     */
    private parseAoiInfo;
}
