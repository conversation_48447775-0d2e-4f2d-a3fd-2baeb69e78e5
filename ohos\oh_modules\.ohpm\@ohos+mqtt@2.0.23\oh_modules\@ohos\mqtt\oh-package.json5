{"types": "", "keywords": ["mqtt", "OpenHarmony", "HarmonyOs"], "author": {"name": "ohos_sig"}, "ohos": {"org": "opensource"}, "description": "专门为OpenHarmony打造的一款应用程序,能够连接到MQTT代理以发布消息、订阅主题和接收发布的消息。", "main": "index.ets", "type": "module", "repository": "https://gitcode.com/openharmony-tpc/ohos_mqtt", "version": "2.0.23", "tags": ["Network"], "dependencies": {"libmqttasync.so": "file:./src/main/cpp/types/libmqttasync"}, "license": "EPL-2.0", "devDependencies": {}, "name": "@ohos/mqtt", "metadata": {"sourceRoots": ["./src/main"], "debug": true, "nativeDebugSymbol": true}, "compatibleSdkVersion": 12, "compatibleSdkType": "OpenHarmony", "obfuscated": false, "nativeComponents": [{"name": "libmqttasync.so", "compatibleSdkVersion": 12, "compatibleSdkType": "OpenHarmony", "linkLibraries": []}]}