// @keepTs
// @ts-nocheck
import { List } from '@kit.ArkTS';
import { ConversationIdentifier, IBoardPlugin } from '../../../../../../Index';
import { IExtensionConfig } from '../../../conversation/extension/IExtensionConfig';
import { IExtensionModule } from '../../../conversation/extension/IExtensionModule';
import { IEmoticonTab } from '../../../conversation/inputbar/component/emoticon/IEmoticonTab';
/**
 * 模块组件管理类
 * @version 1.4.3
 */
export declare class RongExtensionManager {
    private static instance;
    private CallKitModuleName;
    private mAppKey;
    private mContext;
    private mExtModules;
    private mBoardPluginList;
    private mBoardPluginViewMap;
    private mEmoticonTabList;
    private mDefaultExtensionConfig;
    private mExtensionConfig;
    private constructor();
    /**
     * 单例方法
     * @returns 核心类
     */
    static getInstance(): RongExtensionManager;
    /**
     * 初始化，SDK 在初始化时已调用此方法，用户不需要再调用。
     *
     * @param context 应用上下文.
     * @param appKey 应用 key.
     */
    init(f97: Context, g97: string): void;
    private buildExtensionPlugins;
    private buildExtensionEmoticonTabs;
    /**
     * 注册自定义的 {@link IExtensionModule},注册后，可以通过 {@link #getExtensionModules()} 获取已注册的 module
     *
     * <pre>
     * 注意：
     * 1. 请在 SDK 初始化后，调用此方法注册自定义 {@link IExtensionModule}
     * 2. 一定要在进入会话界面之前调此方法
     * </pre>
     *
     * @param extensionModule 自定义模块。
     */
    registerExtensionModule(j96: IExtensionModule): void;
    /**
     * 注册自定义的 {@link IExtensionModule},注册后，可以通过 {@link #getExtensionModules()} 获取已注册的 module
     *
     * <pre>
     * 注意：
     * 1. 请在 SDK 初始化后，调用此方法注册自定义 {@link IExtensionModule}
     * 2. 一定要在进入会话界面之前调此方法
     * </pre>
     *
     * @param extensionModule 自定义模块。
     * @param index 插入位置。
     */
    registerExtensionModuleByIndex(g96: number, h96: IExtensionModule): void;
    /**
     * 添加自定义的 {@link IExtensionModule},添加后，可以通过 {@link #getExtensionModules()} 获取已注册的 module
     *
     * <pre>
     * 注意：
     * 1. 此方法只是把自定义IExtensionModule加入到IExtensionModule列表,不会调用{@link IExtensionModule#onInit(Context, String)}}
     * 2. 注册请使用{@link #registerExtensionModule(IExtensionModule)}
     * 3. 此方法适用于IExtensionModule的排序
     * </pre>
     *
     * @param extensionModule 自定义模块。
     */
    addExtensionModule(e96: IExtensionModule): void;
    /**
     * 注销 {@link IExtensionModule} 模块
     *
     * <pre>
     * 注意：
     * 1. 请在 SDK 初始化后 调用此方法反注册注册 {@link IExtensionModule}
     * 2. 一定要在进入会话界面之前调次方法
     * </pre>
     *
     * @param extensionModule 已注册的 IExtensionModule 模块
     */
    unregisterExtensionModule(a96: IExtensionModule): void;
    /**
     * 获取已注册的模块。
     *
     * @return 已注册的模块列表
     */
    getExtensionModules(): List<IExtensionModule>;
    private findExtensionModule;
    getExtensionConfig(): IExtensionConfig;
    setExtensionConfig(w95: IExtensionConfig): void;
    addBoardPlugin(u95: IBoardPlugin): void;
    insertBoardPlugin(r95: number, s95: IBoardPlugin): void;
    /**
     * 检查插入的插件是否为内置插件，且是否重复插入了
     * @param plugin 插件
     * @returns 是否重复插入
     */
    private isDefaultPluginAdded;
    replaceBoardPlugin(g95: number, h95: IBoardPlugin): void;
    private replaceBoardPluginByName;
    removeBoardPlugin(z94: IBoardPlugin): void;
    removeBoardPluginByName(v94: string): void;
    setBoardPluginViewByName(t94: string, u94: WrappedBuilder<[
        Context,
        ConversationIdentifier
    ]> | null): void;
    getBoardPluginViewByName(s94: string): WrappedBuilder<[
        Context,
        ConversationIdentifier
    ]> | null;
    getBoardPluginList(): List<IBoardPlugin>;
    clearBoardPlugin(): void;
    private removeBoardPluginInList;
    addEmoticonTab(m94: IEmoticonTab): void;
    removeEmoticonTabByName(j94: string): void;
    clearAllEmoticonTab(): void;
    updateEmoticonTab(): void;
    getEmoticonTabList(): List<IEmoticonTab>;
}
