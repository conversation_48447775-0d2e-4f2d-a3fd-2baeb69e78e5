import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:popover/popover.dart';
import 'package:wuling_flutter_app/api/community_api.dart';
import 'package:wuling_flutter_app/page/base_page/base_page.dart';
import 'package:wuling_flutter_app/page/community/bin_go_page.dart';
import 'package:wuling_flutter_app/page/community/keet_alive_page.dart';
import 'package:wuling_flutter_app/page/community/live_page.dart';
import 'package:wuling_flutter_app/page/community/recommend_page.dart';
import 'package:wuling_flutter_app/page/community/report/report_page.dart';
import 'package:wuling_flutter_app/widgets/webview/webview.dart';

import '../../constant/constant.dart';
import '../../constant/service_constant.dart';
import '../../constant/web_view_url_tool.dart';
import '../../models/community/community_tabs.dart';
import '../../models/global_data.dart';
import '../../routes/jump_tool.dart';
import '../../utils/manager/dialog_manager.dart';
import '../../utils/manager/log_manager.dart';
import '../../utils/manager/login_manager.dart';
import '../../utils/manager/phone_call_manager.dart';
import '../../widgets/common/custom_dialog.dart';
import '../../widgets/profile_page_widgets/customer_service_button_list.dart';
import '../../widgets/profile_page_widgets/profile_page_app_bar_button.dart';
import '../../utils/manager/notification_manager.dart';

class CommunityPage extends BasePage {
  CommunityPage({
    Key? key,
    bool hideAppBar = true,
    bool isWithinSafeArea = false,
    String appBarTitle = '',
    Color appBarColor = Colors.transparent,
    Color pageBackgroundColor = Colors.transparent,
  }) : super(
          key: key,
          hideAppBar: hideAppBar,
          hideBottomBar: false,
          isWithinSafeArea: isWithinSafeArea,
          appBarTitle: appBarTitle,
          appBarColor: appBarColor,
          pageBackgroundColor: pageBackgroundColor,
        );

  @override
  _CommunityPageState createState() => _CommunityPageState();
}

class _CommunityPageState extends BasePageState<CommunityPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int count = 5;
  late final List<CommunityTabs> _tabs = [];
  bool _isLogin = false;

  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _tabs.add(const CommunityTabs(
        itemName: "推荐",
        itemDescription: "",
        itemImage: "",
        itemImageChecked: "",
        linkType: 0,
        linkUrl: ""));

    _isLogin = GlobalData().isLogin;

    NotificationManager().subscribe(
        Constant.NOTIFICATION_LOGIN_SUCCEED, _handleLoginStateChanged);
    NotificationManager().subscribe(
        Constant.NOTIFICATION_LOGOUT_SUCCEED, _handleLoginStateChanged);

    _scrollController.addListener(() {
      LogManager().debug("offset: ${_scrollController.offset}");
    });
  }

  @override
  void dispose() {
    NotificationManager().unsubscribe(
        Constant.NOTIFICATION_LOGIN_SUCCEED, _handleLoginStateChanged);
    NotificationManager().unsubscribe(
        Constant.NOTIFICATION_LOGOUT_SUCCEED, _handleLoginStateChanged);
    _tabController?.dispose();
    super.dispose();
  }

  void _handleLoginStateChanged(dynamic data) {
    bool newLoginState = GlobalData().isLogin;
    if (_isLogin != newLoginState) {
      setState(() {
        _isLogin = newLoginState;
      });
    }
  }

  @override
  Widget buildPageContent(BuildContext context) {
    return FutureBuilder<List<CommunityTabs>>(
        future: _loadData(),
        builder: (BuildContext context, AsyncSnapshot snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            return _contentWidget();
          }
          return Container();
        });
  }

  List<ButtonModel> getPopOverButtonList() {
    List<ButtonModel> list = [];
    list.add(ButtonModel(
        title: '客服热线',
        imageUrl: 'assets/images/profile_page/menu_hotline.png',
        type: ButtonType.customerService));
    list.add(ButtonModel(
        title: '联系客服',
        imageUrl: 'assets/images/profile_page/menu_service.png',
        type: ButtonType.onlineChat));
    return list;
  }

  Future<List<CommunityTabs>> _loadData() async {
    List<CommunityTabs> list = await communityAPI.getCommunityTabs();
    if (_tabs.length <= 1) {
      for (var element in list) {
        if (element.itemName != "LIVE") {
          _tabs.add(element);
        }
      }
    }
    _tabController = TabController(length: _tabs.length, vsync: this);
    return _tabs;
  }
  Widget _contentWidget() {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
          elevation: 0,
          title: const SizedBox.shrink(),
          toolbarHeight: 0,
          bottom: PreferredSize(
              preferredSize: const Size.fromHeight(48),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                        child: TabBar(
                      isScrollable: true,
                      physics: const BouncingScrollPhysics(),
                      controller: _tabController,
                      tabs: _tabs
                          .map((model) => Tab(
                                child: model.itemImage == ""
                                    ? Text(model.itemName)
                                    : SizedBox(
                                        height: 22,
                                        child: CachedNetworkImage(
                                          imageUrl: model.itemImage,
                                        ),
                                      ),
                              ))
                          .toList(),
                      labelStyle: const TextStyle(
                          fontWeight: FontWeight.bold, fontSize: 18),
                      indicator: const UnderlineTabIndicator(
                          borderSide:
                              BorderSide(width: 2, color: Colors.redAccent),
                          insets: EdgeInsets.symmetric(horizontal: 20)),
                      unselectedLabelStyle: const TextStyle(
                          fontWeight: FontWeight.normal, fontSize: 14),
                    )),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (_isLogin)
                          AppBarButton(
                            imageUrl:
                                'assets/images/profile_page/mine_message.png',
                            text: '消息',
                            onPress: ({required BuildContext context}) {
                              Navigator.of(context)
                                  .push(MaterialPageRoute(builder: (context) {
                                return const ReportPage();
                              }));
                            },
                          ),
                        AppBarButton(
                          imageUrl:
                              'assets/images/profile_page/my_customer_service.png',
                          text: '客服',
                          onPress: ({required BuildContext context}) {
                            showPopover(
                                context: context,
                                bodyBuilder: (context) => ButtonList(
                                    buttons: getPopOverButtonList(),
                                    onButtonTap: (buttonModel) {
                                      Navigator.of(context).pop();
                                      if (buttonModel.type ==
                                          ButtonType.customerService) {
                                        PhoneCallManager()
                                            .callTelWithPhoneNumber(
                                                context,
                                                Constant
                                                    .HOT_LINE_TELEPHONE_NUMBER);
                                      } else if (buttonModel.type ==
                                          ButtonType.onlineChat) {
                                        if (!GlobalData().isLogin) {
                                          showNotLoginAlertDialog(context);
                                          return;
                                        }
                                        String url =
                                            WebViewURLTool.kefuURLStrWithGroup(
                                                KefuGroup.mm.value, '', '');
                                        JumpTool().openWeb(context, url, true);
                                      }
                                    }),
                                direction: PopoverDirection.bottom,
                                backgroundColor: const Color(0xff383a40),
                                width: 140,
                                height: getPopOverButtonList().length * 54,
                                arrowHeight: 8,
                                arrowWidth: 16,
                                radius: 4);
                          },
                        ),
                      ],
                    )
                  ],
                ),
              ))),
      body: TabBarView(
          controller: _tabController,
          physics: const ClampingScrollPhysics(), // 禁止垂直滑动
          children: _tabs.map((model) {
            Widget widget;
            if (model.itemName == "推荐") {
              widget = const Padding(
                padding: EdgeInsets.only(left: 16, right: 16),
                child: KeepAlivePage(myWidget: ReCommendPage()),
              );
            } else if (model.itemName == "缤果") {
              widget = const KeepAlivePage(myWidget: BinGoPage());
            } else if (model.itemName == "LIVE") {
              widget = const KeepAlivePage(myWidget: LivePage());
            } else if (model.linkType == 1) {
              if(model.itemName == "百科") {
                if(MediaQuery.of(context).size.width > 600) {
                  widget = KeepAlivePage(
                    myWidget:  WebViewPage(
                      url: model.linkUrl,
                      titleName: model.itemName,
                    ),);
                }else {
                  widget = KeepAlivePage(
                      myWidget: Stack(
                        children: [
                          WebViewPage(
                            url: model.linkUrl,
                            titleName: model.itemName,
                            horizontalDrag: true,
                          ),
                          Positioned(
                              top: 0,
                              left: 0,
                              right: 0,
                              height: 300,
                              child: _scroller()),
                          Positioned(
                              top: 380,
                              left: 0,
                              right: 0,
                              bottom: 0,
                              child: _scroller()),
                        ],
                      ));
                }
              }else {
                widget = KeepAlivePage(
                    myWidget:  WebViewPage(
                      url: model.linkUrl,
                      titleName: model.itemName,
                    ),);
              }

            } else {
              widget = Center(
                child: Text(model.itemName),
              );
            }
            return widget;
          }).toList()),
    );
  }
  Widget _scroller() {
    return GestureDetector(
      onHorizontalDragUpdate: (details) {
        if(details.delta.dx < -0.2) {
          _tabController.animateTo(4,duration: const Duration(microseconds: 300));
        }else if(details.delta.dx > 0.2) {
          _tabController.animateTo(2,duration: const Duration(microseconds: 300));
        }
      },
    );
  }

  void showNotLoginAlertDialog(BuildContext context) {
    DialogManager().showCustomDialog(
      CustomDialog(
        content: "您还没有登录",
        contentFontSize: 18,
        contentColor: Colors.black,
        buttonHeight: 70,
        horizontalPadding: 20,
        buttons: [
          DialogButton(
              label: "继续逛逛",
              onPressed: () {},
              backgroundColor: Colors.white,
              textColor: const Color(0xFF9B9DA9)),
          DialogButton(
              label: "去登录",
              onPressed: () {
                LoginManager().showLoginModal();
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xffea0029)),
        ],
      ),
    );
  }
}
