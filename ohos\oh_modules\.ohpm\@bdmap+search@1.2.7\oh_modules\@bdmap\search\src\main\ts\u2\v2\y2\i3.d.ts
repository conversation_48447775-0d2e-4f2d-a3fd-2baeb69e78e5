import { SearchResult } from "../../../d/e/h/f1";
import { SearchParser } from '../../base/base';
export declare class BuildingParser extends SearchParser {
    private static readonly TAG;
    /**
     * @description
     * 解析搜索结果，返回一个包含错误码和 BuildingResult 的对象。
     * 如果没有找到结果，则 error 为 ERRORNO.RESULT_NOT_FOUND；
     * 如果权限未完成，则 error 为 ERRORNO.PERMISSION_UNFINISHED；
     * 其他情况下，error 为 ERRORNO.SEARCH_SERVER_INTERNAL_ERROR。
     *
     * @param json 需要解析的字符串，格式为 JSON 字符串，包含 SDK_InnerError 字段。
     * @returns {SearchResult} 包含 error 和 BuildingResult 的对象。
     */
    parseSearchResult(q6: string): SearchResult;
    /**
     * @description
     * 解析建筑物结果，包括状态码、关系（是否在内）和错误码。
     * 如果状态码为 SEARCH_SUCCESS，则解析建筑物信息并返回 true；否则，设置错误码并返回 false。
     *
     * @param resultJson {string} - 响应结果的 JSON 字符串。
     * @param buildingResult {BuildingResult} - 用于存储建筑物结果的 BuildingResult 对象。
     *
     * @returns {boolean} - 如果解析成功，返回 true；否则，返回 false。
     */
    private parseBuildingResult;
    /**
     * @description
     * 解析建筑物信息，将其转换为 BuildingInfo 类型的数组并赋值给 BuildingResult 对象的 buildingList 属性。
     *
     * @param jsonObject {any} - JSON 格式的字典，包含 'buildinginfo' 键，该键下是一个数组，每个元素都是一个建筑物的相关信息。
     * @param buildingResult {BuildingResult} - 需要更新的 BuildingResult 对象。
     *
     * @returns {boolean} - 返回布尔值，表示是否成功解析了建筑物信息。如果没有解析到任何建筑物信息，则返回 false。
     */
    private parseBuildingInfo;
}
