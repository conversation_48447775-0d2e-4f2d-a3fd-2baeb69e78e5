import Ability from '@ohos.app.ability.Ability';
import { CameraFeature } from '../CameraFeature';
import { DartMessenger } from '../../DartMessenger';
import { CameraProperties } from '../../CameraProperties';
import { DeviceOrientationManager } from './DeviceOrientationManager';
import { DeviceOrientation } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/systemchannels/PlatformChannel';
export declare class SensorOrientationFeature extends CameraFeature<number> {
    private currentSetting;
    private readonly deviceOrientationListener;
    private lockedCaptureOrientation;
    constructor(cameraProperties: CameraProperties, ability: Ability, dartMessenger: DartMessenger);
    getDebugName(): string;
    getValue(): number;
    setValue(value: number): void;
    checkIsSupported(): boolean;
    getDeviceOrientationManager(): DeviceOrientationManager;
    lockCaptureOrientation(orientation: DeviceOrientation): void;
    unlockCaptureOrientation(): void;
    getLockedCaptureOrientation(): DeviceOrientation | null;
}
