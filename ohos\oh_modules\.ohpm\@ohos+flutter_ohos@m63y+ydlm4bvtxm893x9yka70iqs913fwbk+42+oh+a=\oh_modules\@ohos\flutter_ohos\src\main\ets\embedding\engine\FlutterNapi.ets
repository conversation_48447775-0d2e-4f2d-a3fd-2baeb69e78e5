/*
* Copyright (c) 2023 Hunan OpenValley Digital Industry Development Co., Ltd. All rights reserved.
* Use of this source code is governed by a BSD-style license that can be
* found in the LICENSE_KHZG file.
*/

import flutter from 'libflutter.so';
import common from '@ohos.app.ability.common';
import Log from '../../util/Log';
import resourceManager from '@ohos.resourceManager';
import { PlatformMessageHandler } from './dart/PlatformMessageHandler';
import { FlutterCallbackInformation } from '../../view/FlutterCallbackInformation';
import image from '@ohos.multimedia.image';
import { EngineLifecycleListener } from './FlutterEngine';
import { ByteBuffer } from '../../util/ByteBuffer';
import { AccessibilityManager, Action } from '../../view/AccessibilityBridge'
import LocalizationPlugin from '../../plugin/localization/LocalizationPlugin';
import i18n from '@ohos.i18n';
import Any from '../../plugin/common/Any';
import FlutterManager from '../ohos/FlutterManager';
import deviceInfo from '@ohos.deviceInfo';
import TouchEventProcessor from '../ohos/TouchEventProcessor';
import { EmbeddingNodeController } from '../ohos/EmbeddingNodeController';
import BuildProfile from '../../../../../BuildProfile';

const TAG = "FlutterNapi";

enum ContextType {
  APP_LIFECYCLE = 0,
  JS_PAGE_LIFECYCLE,
}

/**
 * 提供arkTs的flutterNAPI接口
 */
export default class FlutterNapi {
  private static hasInit: boolean = false;
  //是否已实现
  hasImplemented: boolean = false;

  nativeShellHolderId: number | null = null;
  platformMessageHandler: PlatformMessageHandler | null = null;
  private engineLifecycleListeners = new Set<EngineLifecycleListener>();
  accessibilityDelegate: AccessibilityDelegate | null = null;
  localizationPlugin: LocalizationPlugin | null = null;
  isDisplayingFlutterUi: boolean = false;
  accessibilityManager: AccessibilityManager | null = null;


  /**
   * 更新刷新率
   * @param rate
   */
  updateRefreshRate(refreshRateFPS : number) {
    flutter.nativeUpdateRefreshRate(refreshRateFPS);
  }

  init(context: common.Context,
       args: Array<string>,
       bundlePath: string,
       appStoragePath: string,
       engineCachesPath: string,
       initTimeMillis: number) {
    if (FlutterNapi.hasInit) {
      Log.e(TAG, "the engine has init");
      return;
    }
    Log.w(TAG, "HAR_VERSION=" + BuildProfile.HAR_VERSION);
    Log.d(TAG, JSON.stringify({
      "name": "init, initTimeMillis=" + initTimeMillis,
      "bundlePath": bundlePath,
      "appStoragePath": appStoragePath,
      "engineCachesPath": engineCachesPath,
      "args": args,
    }));
    let code: number | null = flutter.nativeInit(context, args, bundlePath, appStoragePath,
      engineCachesPath, initTimeMillis, deviceInfo.productModel);
    FlutterNapi.hasInit = code == 0;
    Log.d(TAG, "init code=" + code + ", FlutterNapi.hasInit" + FlutterNapi.hasInit);
  }

  static prefetchDefaultFontManager(): void {
    flutter.nativePrefetchDefaultFontManager();
  }

  attachToNative(): void {
    if (!FlutterNapi.hasInit) {
      Log.e(TAG, "attachToNative fail, FlutterNapi.hasInit=" + FlutterNapi.hasInit);
      return;
    }
    this.nativeShellHolderId = flutter.nativeAttach(this);
    Log.d(TAG, "nativeShellHolderId=" + this.nativeShellHolderId);
  }

  runBundleAndSnapshotFromLibrary(
    bundlePath: string,
    entrypointFunctionName: string | undefined,
    pathToEntrypointFunction: string | undefined,
    assetManager: resourceManager.ResourceManager,
    entrypointArgs: Array<string>) {
    if (!FlutterNapi.hasInit) {
      Log.e(TAG, "runBundleAndSnapshotFromLibrary fail, FlutterNapi.hasInit=" + FlutterNapi.hasInit);
      return;
    }
    Log.d(TAG, "init: bundlePath=" + bundlePath + "  entrypointFunctionName=" + entrypointFunctionName + "  pathToEntrypointFunction=" + pathToEntrypointFunction + "  entrypointArgs=" + JSON.stringify(entrypointArgs))
    if (!this.isAttached()) {
      Log.e(TAG, "runBundleAndSnapshotFromLibrary this.nativeShellHolderId:" + this.nativeShellHolderId)
      return;
    }
    flutter.nativeRunBundleAndSnapshotFromLibrary(this.nativeShellHolderId!, bundlePath, entrypointFunctionName as string, pathToEntrypointFunction as string, assetManager, entrypointArgs);
  };

  /**
   * 当前so方法是否都实现
   * @returns
   */
  checkImplemented(methodName: string = ""): boolean {
    if (!this.hasImplemented) {
      Log.e(TAG, "this method has not implemented -> " + methodName)
    }
    return this.hasImplemented;
  }

  setPlatformMessageHandler(platformMessageHandler: PlatformMessageHandler | null): void {
    this.ensureRunningOnMainThread();
    this.platformMessageHandler = platformMessageHandler;
  }

  private nativeNotifyLowMemoryWarning(nativeShellHolderId: number): void {

  }

  static nativeLookupCallbackInformation(handle: number): FlutterCallbackInformation | null {
    let callbackInformation = new FlutterCallbackInformation();
    let ret : number = flutter.nativeLookupCallbackInformation(callbackInformation, handle);
    if (ret == 0) {
      return callbackInformation;
    }
    return null;
  }

  notifyLowMemoryWarning(): void {
    this.ensureRunningOnMainThread();
    if (!this.isAttached()) {
      Log.e(TAG, "notifyLowMemoryWarning this.nativeShellHolderId:" + this.nativeShellHolderId)
      return;
    }
    this.nativeNotifyLowMemoryWarning(this.nativeShellHolderId!);
  }

  isAttached(): boolean {
    return this.nativeShellHolderId != null;
  }

  private ensureRunningOnMainThread(): void {

  }

  dispatchEmptyPlatformMessage(channel: String, responseId: number): void {
    this.ensureRunningOnMainThread();
    if (this.isAttached()) {
      flutter.nativeDispatchEmptyPlatformMessage(this.nativeShellHolderId!, channel, responseId);
    } else {
      Log.w(
        TAG,
        "Tried to send a platform message to Flutter, but FlutterNapi was detached from native C++. Could not send. Channel: "
          + channel
          + ". Response ID: "
          + responseId);
    }
  }

  /** Sends a reply {@code message} from Android to Flutter over the given {@code channel}. */
  dispatchPlatformMessage(channel: String, message: ArrayBuffer, position: number, responseId: number): void {
    this.ensureRunningOnMainThread();
    if (this.isAttached()) {
      flutter.nativeDispatchPlatformMessage(this.nativeShellHolderId!, channel, message, position, responseId);
    } else {
      Log.w(
        TAG,
        "Tried to send a platform message to Flutter, but FlutterNapi was detached from native C++. Could not send. Channel: "
          + channel
          + ". Response ID: "
          + responseId);
    }
  }

  invokePlatformMessageEmptyResponseCallback(responseId: number): void {
    if (this.isAttached()) {
      flutter.nativeInvokePlatformMessageEmptyResponseCallback(this.nativeShellHolderId!, responseId);
    } else {
      Log.w(
        TAG,
        "Tried to send a platform message response, but FlutterNapi was detached from native C++. Could not send. Response ID: "
          + responseId);
    }
  }

  invokePlatformMessageResponseCallback(responseId: number, message: ArrayBuffer, position: number) {
    if (this.isAttached()) {
      flutter.nativeInvokePlatformMessageResponseCallback(
        this.nativeShellHolderId!, responseId, message, position);
    } else {
      Log.w(
        TAG,
        "Tried to send a platform message response, but FlutterNapi was detached from native C++. Could not send. Response ID: "
          + responseId);
    }
  }

  setViewportMetrics(devicePixelRatio: number, physicalWidth: number
                     , physicalHeight: number, physicalPaddingTop: number, physicalPaddingRight: number
                     , physicalPaddingBottom: number, physicalPaddingLeft: number, physicalViewInsetTop: number
                     , physicalViewInsetRight: number, physicalViewInsetBottom: number, physicalViewInsetLeft: number
                     , systemGestureInsetTop: number, systemGestureInsetRight: number, systemGestureInsetBottom: number
                     , systemGestureInsetLeft: number, physicalTouchSlop: number, displayFeaturesBounds: Array<number>
                     , displayFeaturesType: Array<number>, displayFeaturesState: Array<number>): void {
    if (this.isAttached()) {
      flutter.nativeSetViewportMetrics(this.nativeShellHolderId!, devicePixelRatio,
        physicalWidth, physicalHeight, physicalPaddingTop, physicalPaddingRight,
        physicalPaddingBottom, physicalPaddingLeft, physicalViewInsetTop,
        physicalViewInsetRight, physicalViewInsetBottom, physicalViewInsetLeft,
        systemGestureInsetTop, systemGestureInsetRight, systemGestureInsetBottom,
        systemGestureInsetLeft, physicalTouchSlop, displayFeaturesBounds,
        displayFeaturesType, displayFeaturesState);
    }
  }

  spawn(entrypointFunctionName: string, pathToEntrypointFunction: string, initialRoute: string, entrypointArgs: Array<string>): FlutterNapi {
    let flutterNapi = new FlutterNapi()
    let shellHolderId: number = flutter.nativeSpawn(this.nativeShellHolderId, entrypointFunctionName, pathToEntrypointFunction, initialRoute, entrypointArgs, flutterNapi)
    flutterNapi.nativeShellHolderId = shellHolderId
    return flutterNapi;
  }

  addEngineLifecycleListener(engineLifecycleListener: EngineLifecycleListener): void {
    this.engineLifecycleListeners.add(engineLifecycleListener);
  }

  removeEngineLifecycleListener(engineLifecycleListener: EngineLifecycleListener) {
    this.engineLifecycleListeners.delete(engineLifecycleListener);
  }

  //Called by native to respond to a platform message that we sent.
  handlePlatformMessageResponse(replyId: number, reply: ArrayBuffer): void {
    Log.d(TAG, "called handlePlatformMessageResponse Response ID: " + replyId);
    if (this.platformMessageHandler != null) {
      this.platformMessageHandler.handlePlatformMessageResponse(replyId, reply);
    }
  }

  // Called by native on any thread.
  handlePlatformMessage(channel: string, message: ArrayBuffer, replyId: number, messageData: number): void {
    Log.d(TAG, "called handlePlatformMessage Channel: " + channel + ". Response ID: " + replyId);
    if (this.platformMessageHandler != null) {
      this.platformMessageHandler.handleMessageFromDart(channel, message, replyId, messageData);
    }
  }

  // Called by native to notify first Flutter frame rendered.
  onFirstFrame(): void {
    this.isDisplayingFlutterUi = true;
    Log.d(TAG, "called onFirstFrame")
    FlutterManager.getInstance().getFlutterViewList().forEach((value)=> {
      value.onFirstFrame();
    });
  }

  // Called by native.
  onPreEngineRestart(): void {
    Log.d(TAG, "called onPreEngineRestart")
    this.engineLifecycleListeners.forEach( listener =>  listener.onPreEngineRestart());
  }

  //  /** Invoked by native to obtain the results of OHOS's locale resolution algorithm. */
  computePlatformResolvedLocale(strings: Array<string>): Array<string> {
    Log.d(TAG, "called computePlatformResolvedLocale " + JSON.stringify(strings))
    return []
  }

  decodeImage(buffer: ArrayBuffer, imageGeneratorAddress: number): void {
    if (buffer) {
      Log.d(TAG, "called decodeImage=" + buffer.byteLength)
      const imageSourceApi = image.createImageSource(buffer);
      let tempPixelMap: image.PixelMap | null = null;
      imageSourceApi.createPixelMap({
        desiredPixelFormat: image.PixelMapFormat.RGBA_8888
      }).then(pixelMap => {
        Log.d(TAG, "called createPixelMap end " + pixelMap.getPixelBytesNumber())
        tempPixelMap = pixelMap
        return pixelMap.getImageInfo()
      }).then(imageInfo => {
        Log.d(TAG, `nativeImageHeaderCallback width=${imageInfo.size.width}  height=${imageInfo.size.height} imageGeneratorAddress=${imageGeneratorAddress}`)
        flutter.nativeImageDecodeCallback(imageInfo.size.width, imageInfo.size.height, imageGeneratorAddress, tempPixelMap)
      }).catch((error: Any) => {
        Log.d(TAG, "decodeImage error=" + JSON.stringify(error))
        flutter.nativeImageDecodeCallback(0, 0, imageGeneratorAddress, null);
      })
    } else {
      flutter.nativeImageDecodeCallback(0, 0, imageGeneratorAddress, null);
    }
  }

  setSemanticsEnabledWithRespId(enabled: boolean, responseId: number): void {
    this.ensureRunningOnMainThread();
    if (this.isAttached()) {
      flutter.nativeSetSemanticsEnabled(this.nativeShellHolderId!, enabled);
    } else {
      Log.w(
        TAG,
        "Tried to send a platform message response, but FlutterNapi was detached from native C++. Could not send. Response ID: "
          + responseId);
    }
  }

  setSemanticsEnabled(enabled: boolean): void {
    this.ensureRunningOnMainThread();
    if (this.isAttached()) {
      flutter.nativeSetSemanticsEnabled(this.nativeShellHolderId!, enabled);
    } else {
      Log.e(
        TAG,
        "Tried to send a platform message response, but FlutterNapi was detached from native C++. Could not send.");
    }
  }

  setAccessibilityFeatures(accessibilityFeatureFlags: number, responseId: number): void {
    if (this.isAttached()) {
      flutter.nativeSetAccessibilityFeatures(accessibilityFeatureFlags, responseId);
    } else {
      Log.w(
        TAG,
        "Tried to send a platform message response, but FlutterNapi was detached from native C++. Could not send. Response ID: "
          + responseId);
    }
  }

  nativeSetAccessibilityFeatures(accessibilityFeatureFlags: number, responseId: number): void {}

  dispatchSemanticsAction(virtualViewId: number, action: Action, responseId: number): void {
    if (this.isAttached()) {
      this.nativeDispatchSemanticsAction(virtualViewId, action, responseId);
    } else {
      Log.w(
        TAG,
        "Tried to send a platform message response, but FlutterNapi was detached from native C++. Could not send. Response ID: "
          + responseId);
    }
  }

  nativeDispatchSemanticsAction(virtualViewId: number, action: Action, responseId: number): void {}

  setAccessibilityDelegate(delegate: AccessibilityDelegate, responseId: number): void {
    if (this.isAttached()) {
      this.accessibilityDelegate = delegate;
    } else {
      Log.w(
        TAG,
        "Tried to send a platform message response, but FlutterNapi was detached from native C++. Could not send. Response ID: "
          + responseId);
    }
  }

  accessibilityStateChange(state: Boolean): void {
    this.ensureRunningOnMainThread();
    if(this.accessibilityDelegate != null) {
      this.accessibilityDelegate.accessibilityStateChange(state);
    }
    Log.d(TAG, "accessibilityStateChange is called");
    flutter.nativeAccessibilityStateChange(this.nativeShellHolderId!, state);
  }

  setLocalizationPlugin(localizationPlugin: LocalizationPlugin | null): void {
    this.localizationPlugin = localizationPlugin;
  }

  /**
   * 获取系统语言列表
   * @param rate
   */
  getSystemLanguages() {
    Log.d(TAG, "called getSystemLanguages ")
    let index: number;
    let systemLanguages = i18n.System.getPreferredLanguageList();
    for (index = 0; index < systemLanguages.length; index++) {
      Log.d(TAG, "systemlanguages "+ index + ":" + systemLanguages[index]);
    }
    if (!this.isAttached()) {
      Log.e(TAG, "getSystemLanguages this.nativeShellHolderId:" + this.nativeShellHolderId)
      return;
    }
    flutter.nativeGetSystemLanguages(this.nativeShellHolderId!, systemLanguages);
  }

  /**
   * xcomponet绑定flutterEngine
   * @param xcomponentId
   */
  xComponentAttachFlutterEngine(xcomponentId: string) {
    flutter.nativeXComponentAttachFlutterEngine(xcomponentId, this.nativeShellHolderId!);
  }

  /**
   * xcomponet解除绑定flutterEngine
   * @param xcomponentId
   */
  xComponentDetachFlutterEngine(xcomponentId: string) {
    flutter.nativeXComponentDetachFlutterEngine(xcomponentId, this.nativeShellHolderId!);
  }

  /**
   * xcomponent send mouseWheel event to flutterEngine
   * @param xcomponentId
   * @param eventType
   * @param event
   */
  xComponentDisPatchMouseWheel(xcomponentId: string, eventType: string, event: PanGestureEvent) {
    // only mouse
    if (event.source !== SourceType.Mouse) {
      return;
    }
    const vaildFinger = event.fingerList?.find(item => item.globalX && item.globalY);
    if (!vaildFinger) {
      return;
    }
    flutter.nativeXComponentDispatchMouseWheel(
      this.nativeShellHolderId!!,
      xcomponentId,
      eventType,
      vaildFinger?.id,
      vaildFinger?.localX,
      vaildFinger?.localY,
      event.offsetY,
      event.timestamp
    );
  }

  detachFromNativeAndReleaseResources() {
    if (!this.isAttached()) {
      Log.e(TAG, "detachFromNativeAndReleaseResources this.nativeShellHolderId:" + this.nativeShellHolderId)
      return;
    }
    flutter.nativeDestroy(this.nativeShellHolderId!);
    this.nativeShellHolderId = null;
  }

  initNativeImage(textureId: number, aImage: image.Image) {
    Log.d(TAG, "called initNativeImage ");
    if (!this.isAttached()) {
      Log.e(TAG, "initNativeImage this.nativeShellHolderId:" + this.nativeShellHolderId)
      return;
    }
    flutter.nativeInitNativeImage(this.nativeShellHolderId!, textureId, aImage);
  }

  unregisterTexture(textureId: number): void {
    Log.d(TAG, "called unregisterTexture ");
    if (!this.isAttached()) {
      Log.e(TAG, "unregisterTexture this.nativeShellHolderId:" + this.nativeShellHolderId)
      return;
    }
    flutter.nativeUnregisterTexture(this.nativeShellHolderId!, textureId);
  }

  registerPixelMap(textureId: number, pixelMap: PixelMap): void {
    Log.d(TAG, "called registerPixelMap ");
    if (!this.isAttached()) {
      Log.e(TAG, "registerPixelMap this.nativeShellHolderId:" + this.nativeShellHolderId)
      return;
    }
    flutter.nativeRegisterPixelMap(this.nativeShellHolderId!, textureId, pixelMap);
  }

  setTextureBackGroundPixelMap(textureId: number, pixelMap: PixelMap): void {
    Log.d(TAG, "called setTextureBackGroundPixelMap ");
    flutter.nativeSetTextureBackGroundPixelMap(this.nativeShellHolderId!, textureId, pixelMap);
  }

  setTextureBackGroundColor(textureId: number, color: number): void {
    Log.d(TAG, "called setTextureBackGroundColor ");
    flutter.nativeSetTextureBackGroundColor(this.nativeShellHolderId!, textureId, color);
  }

  registerTexture(textureId: number): number {
    Log.d(TAG, "called registerTexture ");
    if (!this.isAttached()) {
      Log.e(TAG, "registerTexture this.nativeShellHolderId:" + this.nativeShellHolderId)
      return 0;
    }
    return flutter.nativeRegisterTexture(this.nativeShellHolderId!, textureId);
  }

  setTextureBufferSize(textureId: number, width: number, height: number): void {
    Log.d(TAG, "called setTextureBufferSize ");
    if (!this.isAttached()) {
      Log.e(TAG, "setTextureBufferSize this.nativeShellHolderId:" + this.nativeShellHolderId)
      return;
    }
    flutter.nativeSetTextureBufferSize(this.nativeShellHolderId!, textureId, width, height);
  }

  /** Dispatch Touch Event */
  onTouchEvent(strings: Array<string>): void {
    if (this.isAttached()) {
      TouchEventProcessor.getInstance().postTouchEvent(strings);
    }
  }

  //native c++ get the shell holder id
  getShellHolderId(): void {
    this.ensureRunningOnMainThread();
    if (this.isAttached()) {
      flutter.nativeGetShellHolderId(this.nativeShellHolderId!);
    }
  }

  setFontWeightScale(fontWeightScale: number): void {
    this.ensureRunningOnMainThread();
    if (this.isAttached()) {
      Log.i(TAG, "setFontWeightScale: " + fontWeightScale);
      flutter.nativeSetFontWeightScale(this.nativeShellHolderId!, fontWeightScale);
    } else {
      Log.w(TAG, "setFontWeightScale is detached !");
    }
  }

  setFlutterNavigationAction(isNavigate: boolean): void {
    this.ensureRunningOnMainThread();
    if (this.isAttached()) {
      Log.i(TAG, "setFlutterNavigationAction: " + isNavigate);
      flutter.nativeGetFlutterNavigationAction(isNavigate);
    } else {
      Log.w(TAG, "setFlutterNavigationAction is detached !");
    }
  }

  static unicodeIsEmoji(code: number): boolean {
    return Boolean(flutter.nativeUnicodeIsEmoji(code));
  }

  static unicodeIsEmojiModifier(code: number): boolean {
    return Boolean(flutter.nativeUnicodeIsEmojiModifier(code));
  }

  static unicodeIsEmojiModifierBase(code: number): boolean {
    return Boolean(flutter.nativeUnicodeIsEmojiModifierBase(code));
  }

  static unicodeIsVariationSelector(code: number): boolean {
    return Boolean(flutter.nativeUnicodeIsVariationSelector(code));
  }

  static unicodeIsRegionalIndicatorSymbol(code: number): boolean {
    return Boolean(flutter.nativeUnicodeIsRegionalIndicatorSymbol(code));
  }

  SetDVsyncSwitch(isEnable: boolean): void {
    flutter.nativeSetDVsyncSwitch(this.nativeShellHolderId!, isEnable);
  }
}

export interface AccessibilityDelegate {
  updateCustomAccessibilityActions(buffer: ByteBuffer, strings: string[]): void;

  updateSemantics(buffer: ByteBuffer, strings: string[], stringAttributeArgs: ByteBuffer[]): void;

  accessibilityStateChange(state: Boolean): void;
}
