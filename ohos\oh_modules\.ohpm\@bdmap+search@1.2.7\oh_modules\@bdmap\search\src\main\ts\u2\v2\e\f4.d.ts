import { SearchResult } from "../../../d/e/h/f1";
import { WalkingRoutePlanOption } from "../../../d/e/h/m";
import { RoutePlanParser, SearchRequest } from '../../base/base';
import { UrlProvider } from "../../base/d3";
export declare class WalkRouteParser extends RoutePlanParser {
    parseResult(k31: string, l31: SearchResult): void;
    parseSearchResult(f31: string): SearchResult;
    /**
     * 步行路线结果解析
     * <p/>
     * ---taxi |__routes(目前只有一条数据) |__legs（单条规划方案） |__steps[] (方案路段)
     */
    private parseJsonToWalkingRouteResult;
    private parseWalkSteps;
    private parseRouteNodeWp;
    private parseRouteNode;
}
export declare class WalkRouteRequest extends SearchRequest {
    constructor(w29: WalkingRoutePlanOption);
    walkingSearchBuildParam(v29: WalkingRoutePlanOption): void;
    getUrlDomain(u29: UrlProvider): string;
}
