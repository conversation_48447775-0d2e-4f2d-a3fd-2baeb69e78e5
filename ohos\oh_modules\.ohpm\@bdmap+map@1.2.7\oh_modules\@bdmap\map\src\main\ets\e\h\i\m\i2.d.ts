import type { default as p29 } from "../j"; import type InfoWindow from "./u"; import type Overlay from "./m"; import type c46 from "../n1/v4";               export default class OverlayListener { private baidumap; constructor(map: p29); private layerCommit; onBmZIndexUpdate(i40?: Overlay): void; onBmLayerUpdate(h40?: c46): void; onOverlayUpdate(g40: Overlay): void; onMarkerInfoWindowAdd(f40: InfoWindow): void; onOverlayRemove(d40: Overlay, e40?: boolean): void; onOverlayAdd(c40: Overlay): void; isOverlayRemoved(a40: Overlay): boolean; getMapView(): p29; } 