import 'log_manager.dart';

/// 通知管理器，用于管理全局的通知订阅和发送。
/// 支持多订阅者，同时支持单独取消某个订阅者的订阅。
class NotificationManager {
  // 单例模式，确保全局只有一个 NotificationManager 实例。
  static final NotificationManager _instance = NotificationManager._internal();

  /// 使用 Map 存储订阅者列表。
  /// Key 为通知名称（String），Value 为对应的回调函数列表。
  final Map<String, List<void Function(CustomNotification)>> _subscribers = {};

  // 私有构造方法，禁止外部直接实例化。
  NotificationManager._internal();

  /// 工厂构造方法，返回单例实例。
  factory NotificationManager() {
    return _instance;
  }

  /// 订阅指定名称的通知。
  /// 
  /// [notificationName] 通知的名称，用于标识不同类型的通知。
  /// [onNotification] 回调函数，当通知被触发时调用。
  void subscribe(String notificationName, void Function(CustomNotification) onNotification) {
    // 如果通知名称不存在，初始化一个空列表
    final subscribers = _subscribers.putIfAbsent(notificationName, () => []);
    subscribers.add(onNotification); // 添加订阅者
  }

  /// 发送指定名称的通知，触发所有订阅者的回调。
  /// 
  /// [notificationName] 通知的名称，标识发送的通知类型。
  /// [payload] 可选参数，通知携带的主要数据。
  /// [userInfo] 可选参数，包含与通知相关的额外信息的键值对。
  void postNotification(String notificationName, {dynamic payload, Map<String, dynamic>? userInfo}) {
    final notification = CustomNotification(notificationName, payload, userInfo: userInfo);
    final subscribers = _subscribers[notificationName];
    if (subscribers != null) {
      // 遍历当前通知的所有订阅者并调用回调函数
      for (var callback in List.from(subscribers)) {
        callback(notification);
      }
    } else {
      // 如果没有订阅者，输出警告信息
      LogManager().debug("Warning: No subscribers for $notificationName");
    }
  }

  /// 取消指定通知名称和回调的订阅。
  /// 
  /// [notificationName] 通知的名称，用于标识要移除订阅的通知类型。
  /// [onNotification] 要移除的订阅回调函数。
  void unsubscribe(String notificationName, void Function(CustomNotification) onNotification) {
    final subscribers = _subscribers[notificationName];
    if (subscribers != null) {
      // 从订阅者列表中移除指定的回调
      subscribers.remove(onNotification);
      // 如果该通知的订阅者列表为空，则删除该通知名称
      if (subscribers.isEmpty) {
        _subscribers.remove(notificationName);
      }
    }
  }

  /// 清理所有资源，移除所有通知订阅者。
  /// 
  /// 调用此方法后，所有通知订阅将失效。
  void dispose() {
    _subscribers.clear(); // 清空所有订阅者列表
  }
}

/// 自定义通知类，用于封装通知的内容。
class CustomNotification {
  /// 通知的名称，用于标识通知类型。
  final String name;

  /// 通知携带的主要数据（可选）。
  final dynamic payload;

  /// 与通知相关的额外信息的键值对（可选）。
  final Map<String, dynamic>? userInfo;

  /// 创建一个新的自定义通知对象。
  /// 
  /// [name] 通知的名称。
  /// [payload] 可选参数，通知的主要数据。
  /// [userInfo] 可选参数，通知的额外信息。
  CustomNotification(this.name, this.payload, {this.userInfo});
}