// @keepTs
// @ts-nocheck
/**
 * Created on 2024/8/19
 * <AUTHOR>
 */
import { Message } from '@rongcloud/imlib';
import { UiMessage } from '../../conversation/model/UiMessage';
/**
 * 消息转化工具，将 lib Message 和 kit UiMessage 进行转化
 * @version 1.0.0
 */
export declare class MessageConverter {
    /**
     * 将 lib Message 转化为 kit UiMessage
     * @param message 消息对象
     * @returns UiMessage
     */
    static convertToUiMessage(z344: Message): Promise<UiMessage>;
    /**
     * 将 lib Message[] 转化为 kit UiMessage[]
     * @param messages
     * @returns
     */
    static convertToUiMessages(v344: Message[]): Promise<UiMessage[]>;
}
