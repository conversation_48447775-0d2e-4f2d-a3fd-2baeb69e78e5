export var SceneType;
(function (l21) {
    l21[l21["SCENE_NONE"] = -1] = "SCENE_NONE";
    l21[l21["SCENE_VERY_SHORT"] = 0] = "SCENE_VERY_SHORT";
    l21[l21["SCENE_SHORT"] = 1] = "SCENE_SHORT";
    l21[l21["SCENE_MIDDLE"] = 2] = "SCENE_MIDDLE";
    l21[l21["SCENE_LONG"] = 3] = "SCENE_LONG";
})(SceneType || (SceneType = {}));
export var StrategyType;
(function (k21) {
    k21[k21["STRATEGY_DYNAMIC_PRELOAD_DURATION"] = -1] = "STRATEGY_DYNAMIC_PRELOAD_DURATION";
})(StrategyType || (StrategyType = {}));
export var MultiBitratesMode;
(function (j21) {
    j21[j21["MultiBitratesMode_Default"] = 0] = "MultiBitratesMode_Default";
    j21[j21["MultiBitratesMode_FCPrio"] = 1] = "MultiBitratesMode_FCPrio";
    j21[j21["MultiBitratesMode_FC_AND_SMOOTH"] = 2] = "MultiBitratesMode_FC_AND_SMOOTH";
    j21[j21["MultiBitratesMode_SmoothPrio"] = 3] = "MultiBitratesMode_SmoothPrio";
})(MultiBitratesMode || (MultiBitratesMode = {}));
