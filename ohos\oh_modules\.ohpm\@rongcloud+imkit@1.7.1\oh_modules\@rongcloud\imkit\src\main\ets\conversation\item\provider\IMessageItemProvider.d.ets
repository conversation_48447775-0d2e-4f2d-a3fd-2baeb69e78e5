// @keepTs
// @ts-nocheck
import { MessageContent } from '@rongcloud/imlib';
import { UiMessage } from '../../model/UiMessage';
import { ItemProvider } from '../../../base/item/provider/ItemProvider';
import { Context } from '@kit.AbilityKit';
/**
 * Created on 2024/08/06
 *
 * 泛型中以 MessageContent 为主，object 用来占位
 *
 * <AUTHOR>
 * @version 1.0.0
 */
export interface IMessageItemProvider<T extends MessageContent | object> extends ItemProvider<UiMessage> {
    /**
     * 获取当前消息类型需要在会话列表展示的数据
     * @returns 自定义样式的字符串
     */
    getSummaryText(context: Context, messageContent: T): Promise<MutableStyledString>;
    isShowSummaryName(context: Context, messageContent: T): boolean;
}
