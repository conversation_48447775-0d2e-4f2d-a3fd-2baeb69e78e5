import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:dio/dio.dart';
import 'dart:async';
import 'package:quiver/core.dart';

import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:intl/intl.dart';
import 'package:wuling_flutter_app/models/car/car_ble_key_model.dart';
import 'dart:convert';
import 'package:wuling_flutter_app/generated/proto/sgmw_app_car_status.pb.dart';
import 'package:wuling_flutter_app/generated/proto/sgmw_app_control_result.pb.dart';
import 'package:wuling_flutter_app/models/car/car_mini_life_model.dart';

import 'package:wuling_flutter_app/models/index.dart';
import 'package:wuling_flutter_app/page/post/post_normal_detail_page.dart';
import 'package:wuling_flutter_app/page/use_car_page/car_remote_park/F511C/remote_park_f511c_call_page.dart';
import 'package:wuling_flutter_app/page/use_car_page/car_remote_park/F511C/remote_park_f511c_park_page.dart';
import 'package:wuling_flutter_app/page/use_car_page/car_remote_park/F511S/remote_park_f511s_call_page.dart';
import 'package:wuling_flutter_app/page/use_car_page/car_remote_park/F511S/remote_park_f511s_park_page.dart';
import 'package:wuling_flutter_app/page/use_car_page/car_remote_park/remote_park_guide_page.dart';
import 'package:wuling_flutter_app/page/use_car_page/travel_car_setting_page.dart';
import 'package:wuling_flutter_app/routes/app_routes.dart';
import 'package:wuling_flutter_app/utils/car_service_util.dart';
import 'package:wuling_flutter_app/utils/car_remote_park_util.dart';
import 'package:wuling_flutter_app/utils/http/api_exception.dart';
import 'package:wuling_flutter_app/utils/manager/loading_manager.dart';
import 'package:flutter/material.dart';
import 'dart:ui';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wuling_flutter_app/utils/manager/location_manager.dart';
import 'package:wuling_flutter_app/utils/str_util.dart';
import 'package:wuling_flutter_app/widgets/common/custom_refresher.dart';
import 'package:wuling_flutter_app/widgets/common/custom_timer.dart';
import 'package:wuling_flutter_app/page/base_page/base_page.dart';
import 'package:wuling_flutter_app/models/global_data.dart';
import 'package:wuling_flutter_app/models/car/car_control_item_model.dart';
import 'package:wuling_flutter_app/widgets/use_car_page_widgets/use_car_page_control_header.dart';
import 'package:wuling_flutter_app/widgets/use_car_page_widgets/navigation_list_widget.dart';
import 'package:wuling_flutter_app/widgets/use_car_page_widgets/car_control_ac_setting_dialog.dart';
import 'package:wuling_flutter_app/widgets/use_car_page_widgets/car_advertise_header.dart';
import 'package:wuling_flutter_app/widgets/use_car_page_widgets/no_car_widget.dart';
import 'package:wuling_flutter_app/widgets/common/custom_dialog.dart';
import 'package:wuling_flutter_app/constant/constant.dart';
import 'package:wuling_flutter_app/constant/service_constant.dart';
import 'package:wuling_flutter_app/constant/web_view_url_tool.dart';
import 'package:wuling_flutter_app/constant/storage.dart';
import 'package:wuling_flutter_app/utils/sp_util.dart';
import 'package:wuling_flutter_app/utils/cache/mini_life_cache_manager.dart';
import 'package:wuling_flutter_app/utils/manager/login_manager.dart';
import 'package:wuling_flutter_app/utils/manager/location_manager.dart';
import 'package:wuling_flutter_app/utils/manager/dialog_manager.dart';
import 'package:wuling_flutter_app/utils/manager/notification_manager.dart';
import 'package:wuling_flutter_app/utils/manager/log_manager.dart';
import 'package:wuling_flutter_app/utils/manager/phone_call_manager.dart';
import 'package:wuling_flutter_app/utils/manager/ble_manager.dart';
import 'package:wuling_flutter_app/utils/manager/mqtt_manager.dart';
import 'package:wuling_flutter_app/utils/car_status_checker.dart';
import 'package:wuling_flutter_app/utils/http/http_request.dart';
import 'package:wuling_flutter_app/api/car_api.dart';
import 'package:wuling_flutter_app/api/user_api.dart';
import 'package:wuling_flutter_app/api/common_api.dart';
import 'package:wuling_flutter_app/api/post_api.dart';
import 'package:wuling_flutter_app/api/shop_api.dart';
import 'package:wuling_flutter_app/routes/jump_tool.dart';
import 'package:wuling_flutter_app/page/use_car_page/dynamic_page.dart';

import '../../constant/car_constant.dart';
import '../../models/car/car_maintenance_model.dart';
import 'car_details_page.dart';
import '../../models/car/remote_park/remote_park_base_model.dart';
import 'news_page.dart';

import 'package:wuling_flutter_app/api/car.dart' as car_lib;

class UseCarPage extends BasePage {
  UseCarPage({Key? key})
      : super(
          key: key,
          hideAppBar: true, // 保留顶部标题栏
          hideBottomBar: false,
          appBarTitle: '', // 页面标题
          isWithinSafeArea: false, // 页面内容包含在SafeArea内
          pageBackgroundColor: const Color(0xFFF7F9FB),
        );
  @override
  UseCarPageState createState() => UseCarPageState();
}

class UseCarPageState extends BasePageState<UseCarPage> {
  // <editor-fold desc="Properties">
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  final log = LogManager();
  final BleManager _bleManager = BleManager();
  late StreamSubscription<BleStatus> _statusSubscription;
  BleStatus _currentBleStatus = BleStatus.bleDefault;
  String buttonTitle = GlobalData().isLogin ? '立即选购' : '登录/注册';
  bool _isCarUser = false;
  bool _isAutoRefreshing = false;
  bool _isNeedCheckTire = false;
  bool _needShowCarControlPage = true;
  bool _isAcSettingDialogVisible = false;
  int setbackStatus = 0; // 空调1的制暖制冷状态：1=制冷，2=制暖，0=关闭
  List<CarControlItemModel> _controlItemList = [];
  List<CarControlItemModel> _controlSubItemList = []; //车控item模型列表-新UI在下面显示的
  List<CarServiceModel> _ctrlServiceList = [];
  List<CarServiceModel> _toolServiceList = [];
  List<CarServiceModel> _newuiServiceArray = [];
  List<CarServiceModel> _activityList = [];

  ///mini人生位的广告列表
  List<CarServiceModel> _toolsEditedList = []; //筛选后显示在用车服务的list
  CarMiniLifeModel? _miniLifeModel;
  CarStatusModel? _statusModel;
  CarInfoModel? _carInfoModel;
  List<CarServiceModel>? _notifiList;
  CarMaintenanceModel? _mainModel;
  List<UserHandleModel> _useCarServiceList = [];
  List<AdvertisePositionModel> _positionModelList = [];
  List<Advertise> _advertiseList = [];
  List<DealerModel> _dealerList = [];
  UserHandleListModel? _servicesHandleList;
  late final CustomTimer _customTimer =
      CustomTimer(const Duration(seconds: 3), onTick: autoRefresh);
  ColumnModel? _columnModel;
  ScrollController _scrollController = ScrollController();
  bool _showTopWidget = false;
  bool _isDisconnected = false;
  bool _isRefresh = false;
  bool _showNetworkBanner = false;
  bool _hasInitialRefreshed = false; // 标记是否已经进行过初始刷新 // 控制网络提示条显示
  bool _isOfflineMode = false; // 控制是否进入离线模式
  late double _topWidgetHeight; // 顶部组件的高度，需要配合SafeArea高度计算
  late double _scrollThreshold; // 滚动阈值，值为广告页高度
  String _currentACStatus = "0"; //当前空调状态
  String _currentServiceCode = ""; //当前操作的功能
  String _currentControlServiceCode = ""; //异步用
  String _backStatus = "0"; //异步用状态
  String _MQTTBackStatus = "0";
  String _windCode = '';
  //座椅加热指令下发code
  String _hotCode = '';
  //座椅通风加热指令汇总
  String _seatCode = '';
  //座椅下发的等级
  String _workLevel = "";
  bool _isSeat1WindStatus = false;
  bool _isSeat1HotStatus = false;
  int _collectTime = 0;
  int _operateTime = 0;
  bool _userHandleDisConnectBle = false; //用户是否手动断开过蓝牙
  bool _isHandleConnectBle = false; //当前是否是手动连接蓝牙
  String _currentConnectBleVin = '';
  String _currentCarVin = ''; //当前显示的车辆vin
  int currentAccType = 0;
  //蓝牙泊车相关属性
  Function(bool isSuccess, String message)? _bleConnectFuntion;
  final carApi = car_lib.CarAPI();

  @override
  void initState() {
    super.initState();

    _initPageNetworkMonitoring(); // 初始化页面级网络监控

    _setupMQTTSubscription(); // 添加MQTT订阅
  }

  // 设置MQTT订阅
  void _setupMQTTSubscription() {
    // 订阅车辆控制全状态主题

    MQTTManager.shared.addListener(
      _onCarControlAllStatusReceived,
      MQTTTopic.carControlAllStatus,
      tag: 'UseCarPage_carControlAllStatus', // 用于标识，便于移除
    );

    // 订阅车辆远程异步结果主题

    MQTTManager.shared.addListener(
      _onCarRemoteAsyncResultReceived,
      MQTTTopic.carRemoteAsyncResult,
      tag: 'UseCarPage_carRemoteAsyncResult', // 用于标识，便于移除
    );

    //添加MQTT异步结果超时监听

    MQTTManager.shared.addReceiveMqttTimeOutListener(
      _onReceiveMqttTimeOutReceived,
      tag: 'UseCarPage_ReceiveMqttTimeOut', // 用于标识，便于移除
    );
  }

  // 处理接收到的车辆远程异步结果消息
  void _onCarRemoteAsyncResultReceived(dynamic data) {
    LogManager().debug('[MQTT]===处理接收到的车辆远程异步结果消息,data: $data');
    try {
      // 根据数据格式解析消息
      if (data != null) {
        // 如果是protobuf数据，需要解析
        if (data is List<int>) {
          // 解析protobuf数据
          final controlResult = SgmwAppControlResult.fromBuffer(data);

          _handleCarRemoteAsyncResult(controlResult);
        } else if (data is Map<String, dynamic>) {
          if (data.containsKey('error')) {
            // 处理错误情况

            return;
          }

          // 检查是否是从protobuf转换而来的Map数据
          if (data.containsKey('code') &&
              data.containsKey('message') &&
              data.containsKey('serviceCode')) {
            // 这是从protobuf转换来的Map数据或直接的JSON数据
            _handleCarRemoteAsyncResultFromMap(data);
          } else {
            // 其他JSON格式数据
            _handleCarRemoteAsyncResultFromJson(data);
          }
        } else {}
      } else {}
    } catch (e) {}
  }

  // 处理protobuf格式的车辆远程异步结果数据
  Future<void> _handleCarRemoteAsyncResult(
      SgmwAppControlResult sgmwAppControlResult) async {
    LogManager().debug(
        '[MQTT]===收到车辆远程异步结果MQTT消息,处理protobuf格式的车辆远程异步结果数据，_handleCarRemoteAsyncResultFromMap,sgmwAppControlResult:：${sgmwAppControlResult.toString()}');
    try {
      String code = sgmwAppControlResult.code;
      String message = sgmwAppControlResult.message;
      String serviceCode = sgmwAppControlResult.serviceCode;
      String collectTime = sgmwAppControlResult.collectTime.toString();
      String collectTimeStr;
      if (hasCurrentRemoteControlInPerformed(serviceCode)) {
        if (code == "0" && _backStatus != null && _backStatus!.isNotEmpty) {
          try {
            if (_carInfoModel?.vin?.isNotEmpty ?? false) {
              // 处理collectTime时间戳，转换为yyyy-MM-dd HH:mm:ss格式
              try {
                // 将时间戳转换为DateTime对象
                int timestamp = int.parse(collectTime);
                DateTime dateTime =
                    DateTime.fromMillisecondsSinceEpoch(timestamp);
                // 格式化为yyyy-MM-dd HH:mm:ss
                collectTimeStr =
                    DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
                LogManager().debug('[MQTT]===时间戳$timestamp转换为$collectTimeStr');
              } catch (e) {
                LogManager().debug('[MQTT]===时间戳转换失败: $e');
                collectTimeStr = DateTime.now().toString();
              }
              CarStatusChecker().saveCarStatusTimeWithTimeStr(
                  collectTimeStr, _carInfoModel!.vin!);
              setState(() {
                // 根据控制结果更新相关状态
                // 例如：显示操作结果提示、更新按钮状态等
                Map<String, dynamic> carStatus = _statusModel!.toJson();
                if (serviceCode == "seatWindStatus" ||
                    serviceCode == "seatHotStatus") {
                  if (carStatus.containsKey(_windCode)) {
                    carStatus[_windCode] = _workLevel;
                  } else if (carStatus.containsKey(_hotCode)) {
                    carStatus[_hotCode] = _workLevel;
                  } else {
                    carStatus[serviceCode] = _backStatus;
                  }

                  CarStatusModel newStatus = CarStatusModel.fromJson(carStatus);
                  checkAcSettingDialogWithStatus(
                      oldStatus: _statusModel, newStatus: newStatus);
                  // 检查是否设置空调的当前温度成功
                  if (_isAcSettingDialogVisible) {
                    NotificationManager().postNotification(
                        Constant
                            .NOTIFICATION_SLIDER_CHANGE_SEND_INSTRUCT_RESULT,
                        userInfo: {'success': true});
                  }
                  _statusModel = newStatus;
                  checkSeatSettingDialogWithStatus(statusModel: _statusModel);
                  updateCtrlItemList();
                  updateCtrlSubItemList();
                  LoadingManager.showSuccess(message);
                  onCarControlFinish(); // 远控结束
                  // 缓存更新后的车辆状态
                  _cacheCarStatus(newStatus);
                } else {
                  if (serviceCode == "acStatus") {
                    if (IS_MQTT_CAR_CONTROL || !IS_AC_HOT_WIND_SETTING) {
                      LoadingManager.showSuccess('操作成功');
                      IS_MQTT_CAR_CONTROL = false;
                    } else {
                      LoadingManager.showSuccess(message);
                    }
                    IS_AC_HOT_WIND_SETTING = false;

                    if (_isSeat1HotStatus) {
                      carStatus["seat1HotStatus"] = "3";
                      carStatus["seat1WindStatus"] = "7";
                    }

                    if (_isSeat1WindStatus) {
                      carStatus["seat1WindStatus"] = "3";
                      carStatus["seat1HotStatus"] = "7";
                    }

                    // 把空调状态写入到车况中
                    carStatus[serviceCode] = _backStatus;
                    LogManager().debug(
                        '[MQTT]===[CAR_STATUS]===$serviceCode的值从${carStatus[serviceCode]}更新为$_backStatus');
                    CarStatusModel newStatus =
                        CarStatusModel.fromJson(carStatus);
                    checkAcSettingDialogWithStatus(
                        oldStatus: _statusModel, newStatus: newStatus);
                    // 检查是否设置空调的当前温度成功
                    if (_isAcSettingDialogVisible) {
                      NotificationManager().postNotification(
                          Constant
                              .NOTIFICATION_SLIDER_CHANGE_SEND_INSTRUCT_RESULT,
                          userInfo: {'success': true});
                    }
                    _statusModel = newStatus;
                    updateCtrlItemList();
                    updateCtrlSubItemList();
                    _seatCode = "";
                    onCarControlFinish(); // 远控结束
                    // 缓存更新后的车辆状态
                    _cacheCarStatus(newStatus);
                  }
                }
              });
            }
          } catch (e) {
            log.info('[MQTT]===采集时间纪录发生异常：${e.toString()}');
          }
          MQTTManager.shared.stopMqttTimeOutTimer();
        } else {
          if (_isAcSettingDialogVisible) {
            NotificationManager().postNotification(
                Constant.NOTIFICATION_SLIDER_CHANGE_SEND_INSTRUCT_RESULT,
                userInfo: {'success': false});
          }
          LoadingManager.showError(message);
          onCarControlFinish(); //远控结束
          MQTTManager.shared.stopMqttTimeOutTimer();
        }
      }
    } catch (e) {
      LogManager().debug('[MQTT]===处理protobuf格式的车辆远程异步结果推送 解析错误：$e');
    }
  }

  //是否有本地远控异步指令正在执行，并且收到的异步车控结果通知是否是当前控制的功能
  bool hasCurrentRemoteControlInPerformed(String serviceCode) {
    bool result = serviceCode.isNotEmpty &&
        (_currentControlServiceCode?.isNotEmpty ?? false) &&
        serviceCode == _currentControlServiceCode;
    LogManager().debug('[MQTT]===是否有本地远控异步指令正在执行,result：$result');
    return result;
  }

  void onCarControlFinish() {
    _currentControlServiceCode = "";
    currentAccType = 0;
    _isSeat1WindStatus = false;
    _isSeat1HotStatus = false;
    _windCode = "";
    _hotCode = "";
    _seatCode = "";
    _workLevel = "";
    _backStatus = "";
  }

  // 处理从protobuf转换而来的Map格式数据
  Future<void> _handleCarRemoteAsyncResultFromMap(
      Map<String, dynamic> data) async {
    LogManager().debug(
        '[MQTT]===收到车辆远程异步结果MQTT消息,处理Map格式的车辆远程异步结果，_handleCarRemoteAsyncResultFromMap,data:：$data');
    LogManager().debug('  [MQTT]===代码: ${data['code']}');
    LogManager().debug('  [MQTT]===消息: ${data['message']}');
    LogManager().debug('  [MQTT]===服务代码: ${data['serviceCode']}');
    LogManager().debug('  [MQTT]===采集时间: ${data['collectTime']}');
    LogManager().debug('  [MQTT]===时间戳: ${data['timestamp']}');
    try {
      String code = data['code'].toString();
      String message = data['message'].toString();
      String serviceCode = data['serviceCode'].toString();
      String collectTime = data['collectTime'].toString();
      String collectTimeStr;
      if (hasCurrentRemoteControlInPerformed(serviceCode)) {
        if (code == "0" && _backStatus != null && _backStatus!.isNotEmpty) {
          try {
            if (_carInfoModel?.vin?.isNotEmpty ?? false) {
              // 处理collectTime时间戳，转换为yyyy-MM-dd HH:mm:ss格式
              try {
                // 将时间戳转换为DateTime对象
                int timestamp = int.parse(collectTime);
                DateTime dateTime =
                    DateTime.fromMillisecondsSinceEpoch(timestamp);
                // 格式化为yyyy-MM-dd HH:mm:ss
                collectTimeStr =
                    DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
                LogManager().debug('[MQTT]===时间戳$timestamp转换为$collectTimeStr');
              } catch (e) {
                LogManager().debug('[MQTT]===时间戳转换失败: $e');
                collectTimeStr = DateTime.now().toString();
              }
              CarStatusChecker().saveCarStatusTimeWithTimeStr(
                  collectTimeStr, _carInfoModel!.vin!);
              setState(() {
                // 根据控制结果更新相关状态
                // 例如：显示操作结果提示、更新按钮状态等
                Map<String, dynamic> carStatus = _statusModel!.toJson();
                if (serviceCode == "seatWindStatus" ||
                    serviceCode == "seatHotStatus") {
                  if (carStatus.containsKey(_windCode)) {
                    carStatus[_windCode] = _workLevel;
                  } else if (carStatus.containsKey(_hotCode)) {
                    carStatus[_hotCode] = _workLevel;
                  } else {
                    carStatus[serviceCode] = _backStatus;
                  }

                  CarStatusModel newStatus = CarStatusModel.fromJson(carStatus);
                  checkAcSettingDialogWithStatus(
                      oldStatus: _statusModel, newStatus: newStatus);
                  // 检查是否设置空调的当前温度成功
                  if (_isAcSettingDialogVisible) {
                    NotificationManager().postNotification(
                        Constant
                            .NOTIFICATION_SLIDER_CHANGE_SEND_INSTRUCT_RESULT,
                        userInfo: {'success': true});
                  }
                  _statusModel = newStatus;
                  checkSeatSettingDialogWithStatus(statusModel: _statusModel);

                  updateCtrlItemList();
                  updateCtrlSubItemList();
                  LoadingManager.showSuccess(message);
                  onCarControlFinish(); // 远控结束
                } else {
                  if (serviceCode == "acStatus") {
                    if (IS_MQTT_CAR_CONTROL || !IS_AC_HOT_WIND_SETTING) {
                      LoadingManager.showSuccess('操作成功');
                      IS_MQTT_CAR_CONTROL = false;
                    } else {
                      LoadingManager.showSuccess(message);
                    }
                    IS_AC_HOT_WIND_SETTING = false;

                    if (_isSeat1HotStatus) {
                      carStatus["seat1HotStatus"] = "3";
                      carStatus["seat1WindStatus"] = "7";
                    }

                    if (_isSeat1WindStatus) {
                      carStatus["seat1WindStatus"] = "3";
                      carStatus["seat1HotStatus"] = "7";
                    }

                    // 把空调状态写入到车况中
                    carStatus[serviceCode] = _backStatus;
                    LogManager().debug(
                        '[MQTT]===[CAR_STATUS]===$serviceCode的值从${carStatus[serviceCode]}更新为$_backStatus');
                    CarStatusModel newStatus =
                        CarStatusModel.fromJson(carStatus);
                    checkAcSettingDialogWithStatus(
                        oldStatus: _statusModel, newStatus: newStatus);
                    // 检查是否设置空调的当前温度成功
                    if (_isAcSettingDialogVisible) {
                      NotificationManager().postNotification(
                          Constant
                              .NOTIFICATION_SLIDER_CHANGE_SEND_INSTRUCT_RESULT,
                          userInfo: {'success': true});
                    }
                    _statusModel = newStatus;
                    updateCtrlItemList();
                    updateCtrlSubItemList();
                    _seatCode = "";
                    onCarControlFinish(); // 远控结束
                  }
                }
              });
            }
          } catch (e) {
            LogManager().debug('[MQTT]===采集时间纪录发生异常：${e.toString()}');
          }
          MQTTManager.shared.stopMqttTimeOutTimer();
        } else {
          if (_isAcSettingDialogVisible) {
            NotificationManager().postNotification(
                Constant.NOTIFICATION_SLIDER_CHANGE_SEND_INSTRUCT_RESULT,
                userInfo: {'success': false});
          }
          LoadingManager.showError(message);
          onCarControlFinish(); //远控结束
          MQTTManager.shared.stopMqttTimeOutTimer();
        }
      }
    } catch (e) {
      LogManager().debug('[MQTT]===处理Map格式的车辆远程异步结果 解析错误：$e');
    }
  }

  // 处理JSON格式的车辆远程异步结果数据
  Future<void> _handleCarRemoteAsyncResultFromJson(
      Map<String, dynamic> data) async {
    LogManager().debug(
        '[MQTT]===收到车辆远程异步结果MQTT消息,处理JSON格式的车辆远程异步结果，_handleCarRemoteAsyncResultFromJson：');
    LogManager().debug('[MQTT]===代码: ${data['code']}');
    LogManager().debug('[MQTT]===消息: ${data['message']}');
    LogManager().debug('[MQTT]===服务代码: ${data['serviceCode']}');
    LogManager().debug('[MQTT]===采集时间: ${data['collectTime']}');
    LogManager().debug('[MQTT]===时间戳: ${data['timestamp']}');

    try {
      String code = data['code'].toString();
      String message = data['message'].toString();
      String serviceCode = data['serviceCode'].toString();
      String collectTime = data['collectTime'].toString();
      String collectTimeStr;
      if (hasCurrentRemoteControlInPerformed(serviceCode)) {
        if (code == "0" && _backStatus != null && _backStatus!.isNotEmpty) {
          try {
            if (_carInfoModel?.vin?.isNotEmpty ?? false) {
              // 处理collectTime时间戳，转换为yyyy-MM-dd HH:mm:ss格式
              try {
                // 将时间戳转换为DateTime对象
                int timestamp = int.parse(collectTime);
                DateTime dateTime =
                    DateTime.fromMillisecondsSinceEpoch(timestamp);
                // 格式化为yyyy-MM-dd HH:mm:ss
                collectTimeStr =
                    DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
                LogManager().debug('[MQTT]===时间戳$timestamp转换为$collectTimeStr');
              } catch (e) {
                LogManager().debug('[MQTT]===时间戳转换失败: $e');
                collectTimeStr = DateTime.now().toString();
              }
              CarStatusChecker().saveCarStatusTimeWithTimeStr(
                  collectTimeStr, _carInfoModel!.vin!);
              setState(() {
                // 根据控制结果更新相关状态
                // 例如：显示操作结果提示、更新按钮状态等
                Map<String, dynamic> carStatus = _statusModel!.toJson();
                if (serviceCode == "seatWindStatus" ||
                    serviceCode == "seatHotStatus") {
                  if (carStatus.containsKey(_windCode)) {
                    carStatus[_windCode] = _workLevel;
                  } else if (carStatus.containsKey(_hotCode)) {
                    carStatus[_hotCode] = _workLevel;
                  } else {
                    carStatus[serviceCode] = _backStatus;
                  }

                  CarStatusModel newStatus = CarStatusModel.fromJson(carStatus);
                  checkAcSettingDialogWithStatus(
                      oldStatus: _statusModel, newStatus: newStatus);
                  // 检查是否设置空调的当前温度成功
                  if (_isAcSettingDialogVisible) {
                    NotificationManager().postNotification(
                        Constant
                            .NOTIFICATION_SLIDER_CHANGE_SEND_INSTRUCT_RESULT,
                        userInfo: {'success': true});
                  }
                  _statusModel = newStatus;
                  checkSeatSettingDialogWithStatus(statusModel: _statusModel);

                  updateCtrlItemList();
                  updateCtrlSubItemList();
                  LoadingManager.showSuccess(message);
                  onCarControlFinish(); // 远控结束
                } else {
                  if (serviceCode == "acStatus") {
                    if (IS_MQTT_CAR_CONTROL || !IS_AC_HOT_WIND_SETTING) {
                      LoadingManager.showSuccess('操作成功');
                      IS_MQTT_CAR_CONTROL = false;
                    } else {
                      LoadingManager.showSuccess(message);
                    }
                    IS_AC_HOT_WIND_SETTING = false;

                    if (_isSeat1HotStatus) {
                      carStatus["seat1HotStatus"] = "3";
                      carStatus["seat1WindStatus"] = "7";
                    }

                    if (_isSeat1WindStatus) {
                      carStatus["seat1WindStatus"] = "3";
                      carStatus["seat1HotStatus"] = "7";
                    }

                    // 把空调状态写入到车况中
                    carStatus[serviceCode] = _backStatus;
                    LogManager().debug(
                        '[MQTT]===[CAR_STATUS]===$serviceCode的值从${carStatus[serviceCode]}更新为$_backStatus');
                    CarStatusModel newStatus =
                        CarStatusModel.fromJson(carStatus);
                    checkAcSettingDialogWithStatus(
                        oldStatus: _statusModel, newStatus: newStatus);
                    // 检查是否设置空调的当前温度成功
                    if (_isAcSettingDialogVisible) {
                      NotificationManager().postNotification(
                          Constant
                              .NOTIFICATION_SLIDER_CHANGE_SEND_INSTRUCT_RESULT,
                          userInfo: {'success': true});
                    }
                    _statusModel = newStatus;
                    updateCtrlItemList();
                    updateCtrlSubItemList();
                    // 检查是否设置空调的当前温度成功
                    if (_isAcSettingDialogVisible) {
                      NotificationManager().postNotification(
                          Constant
                              .NOTIFICATION_SLIDER_CHANGE_SEND_INSTRUCT_RESULT,
                          userInfo: {'success': true});
                    }
                    _seatCode = "";
                    onCarControlFinish(); // 远控结束
                  }
                }
              });
            }
          } catch (e) {
            log.info('[MQTT]===采集时间纪录发生异常：${e.toString()}');
          }
          MQTTManager.shared.stopMqttTimeOutTimer();
        } else {
          if (_isAcSettingDialogVisible) {
            NotificationManager().postNotification(
                Constant.NOTIFICATION_SLIDER_CHANGE_SEND_INSTRUCT_RESULT,
                userInfo: {'success': false});
          }
          LoadingManager.showError(message);
          onCarControlFinish(); //远控结束
          MQTTManager.shared.stopMqttTimeOutTimer();
        }
      }
    } catch (e) {
      LogManager().debug('[MQTT]===处理JSON格式的车辆远程异步结果 解析错误：$e');
    }
  }

  // 处理车辆远程异步结果（兼容旧版本）
  void _handleCarRemoteAsyncResultLegacy(Map<String, dynamic> data) {
    // 解析异步结果数据
    String? resultCode = data['resultCode']?.toString();
    String? message = data['message']?.toString();
    String? serviceCode = data['serviceCode']?.toString();

    LogManager()
        .debug('远程异步结果 - 服务代码: $serviceCode, 结果代码: $resultCode, 消息: $message');

    // 根据结果代码处理不同情况
    if (resultCode == '0' || resultCode == '200') {
      // 成功情况
      LoadingManager.showSuccess(message ?? '操作成功');

      // 如果有服务代码，更新对应的状态
      if (serviceCode != null && serviceCode.isNotEmpty) {
        _updateStatusForAsyncResult(serviceCode, data);
      }
    } else {
      // 失败情况
      LoadingManager.showError(message ?? '操作失败');
    }

    // 如果空调设置对话框可见，发送指令结果通知
    if (_isAcSettingDialogVisible) {
      NotificationManager().postNotification(
          Constant.NOTIFICATION_SLIDER_CHANGE_SEND_INSTRUCT_RESULT,
          userInfo: {'success': resultCode == '0' || resultCode == '200'});
    }
  }

  // 根据异步结果更新状态
  void _updateStatusForAsyncResult(
      String serviceCode, Map<String, dynamic> data) {
    if (_statusModel == null) return;

    // 查找对应的控制项
    CarControlItemModel? targetItem;
    for (var item in _controlItemList) {
      if (item.serviceModel.serviceCode == serviceCode) {
        targetItem = item;
        break;
      }
    }

    if (targetItem != null) {
      setState(() {
        Map<String, dynamic> statusMap = _statusModel!.toJson();

        // 获取当前状态并更新为成功后的状态值
        CarServiceStatusModel? currentStatus = targetItem?.currentStatus();
        if (currentStatus != null) {
          String backStatusValue = currentStatus.backStatusValue;
          LogManager().debug(
              '[ASYNC_RESULT]===$serviceCode的值从${statusMap[serviceCode]}更新为$backStatusValue');
          statusMap[serviceCode] = backStatusValue;

          CarStatusModel newStatus = CarStatusModel.fromJson(statusMap);
          checkAcSettingDialogWithStatus(
              oldStatus: _statusModel, newStatus: newStatus);
          // 检查是否设置空调的当前温度成功
          if (_isAcSettingDialogVisible) {
            NotificationManager().postNotification(
                Constant.NOTIFICATION_SLIDER_CHANGE_SEND_INSTRUCT_RESULT,
                userInfo: {'success': true});
          }
          _statusModel = newStatus;
          updateCtrlItemList();
          updateCtrlSubItemList();
          // 保存车辆状态采集时间
          if (_carInfoModel?.vin?.isNotEmpty ?? false) {
            DateTime now = DateTime.now();
            String formattedDate =
                DateFormat('yyyy-MM-dd HH:mm:ss').format(now);
            CarStatusChecker().saveCarStatusTimeWithTimeStr(
                formattedDate, _carInfoModel!.vin!);
          }
        }
      });
    }
  }

  // 处理接收到的车辆控制全状态消息
  void _onCarControlAllStatusReceived(dynamic data) {
    LogManager().debug('[MQTT]===收到车辆控制全状态MQTT消息,data: $data');

    // try {
    // 根据数据格式解析消息
    if (data != null) {
      // 如果是protobuf数据，需要解析
      if (data is List<int>) {
        // 解析protobuf数据
        final carStatus = SgmwAppCarStatus.fromBuffer(data);
        _handleCarControlAllStatus(carStatus);
      } else if (data is Map<String, dynamic>) {
        if (data.containsKey('error')) {
          // 处理错误情况
          LogManager().debug('[MQTT]===消息解析错误: ${data['error']}');
          return;
        }

        // 检查是否是从protobuf转换而来的Map数据
        if (data.containsKey('collectTime') &&
            data.containsKey('doorLockStatus') &&
            data.containsKey('windowStatus')) {
          // 这是从protobuf转换来的Map数据
          _handleCarControlAllStatusFromMap(data);
        } else {
          // 其他JSON格式数据
          _handleCarControlAllStatusFromJson(data);
        }
      }
    }
    // } catch (e) {
    //   LogManager().debug('解析车辆控制全状态数据失败: $e');
    // }
  }

  // mqtt监听超时（未收到mqtt推送）
  void _onReceiveMqttTimeOutReceived(dynamic data) {
    LogManager().debug('=== 【MQTT超时】_onReceiveMqttTimeOutReceived 开始 ===');
    LogManager().debug('【MQTT超时】超时时间: ${DateTime.now()}');
    LogManager().debug('【MQTT超时】当前控制状态:');
    LogManager()
        .debug('  - _currentControlServiceCode: $_currentControlServiceCode');
    LogManager().debug('  - IS_MQTT_CAR_CONTROL: $IS_MQTT_CAR_CONTROL');
    LogManager().debug('  - _MQTTBackStatus: $_MQTTBackStatus');
    LogManager().debug('  - _backStatus: $_backStatus');

    String dataString;
    if (data is Map || data is List) {
      dataString = jsonEncode(data);
    } else {
      dataString = data.toString();
    }
    LogManager().debug('【MQTT超时】超时数据: $dataString');
    LogManager()
        .debug('【MQTT超时】未收到mqtt推送,_onReceiveMqttTimeOutReceived: $dataString');
    IS_MQTT_CAR_CONTROL = false;
    LoadingManager.showError(dataString);
    if (_isAcSettingDialogVisible) {
      NotificationManager().postNotification(
          Constant.NOTIFICATION_SLIDER_CHANGE_SEND_INSTRUCT_RESULT,
          userInfo: {'success': false});
    }
    onCarControlFinish(); //远控结束
    autoRefresh();
    MQTTManager.shared.stopMqttTimeOutTimer();

    LogManager().debug('=== 【MQTT超时】_onReceiveMqttTimeOutReceived 结束 ===');
  }

  // 处理protobuf格式的车辆控制全状态数据
  void _handleCarControlAllStatus(SgmwAppCarStatus carStatus) {
    LogManager().debug(
        '[MQTT]===处理protobuf格式的车辆控制全状态数据_handleCarControlAllStatus: $carStatus');
    setState(() {
      // 更新车辆状态模型
      if (_statusModel != null) {
        // 根据protobuf数据更新状态模型的相关字段
        // 这里需要根据具体的字段映射关系来更新
        _updateCarStatusFromProtobuf(carStatus);
      }
    });

    // 如果需要，可以触发UI刷新
    _refreshCarControlUI();
  }

  // 处理从protobuf转换而来的Map格式数据
  void _handleCarControlAllStatusFromMap(Map<String, dynamic> data) {
    LogManager().debug(
        '[MQTT]===处理从protobuf转换而来的Map格式数据，_handleCarControlAllStatusFromMap: $data');
    if (_statusModel != null) {
      // 更新车辆状态模型
      _updateCarStatusFromMap(data);
    }
    _refreshCarControlUI();
  }

  // 处理JSON格式的车辆控制全状态数据
  void _handleCarControlAllStatusFromJson(Map<String, dynamic> data) {
    LogManager().debug(
        '[MQTT]===处理JSON格式的车辆控制全状态数据_handleCarControlAllStatusFromJson: $data');
    setState(() {
      // 根据JSON数据更新状态
      if (_statusModel != null) {
        // 更新相关状态字段
        _updateCarStatusFromJson(data);
      }
    });

    _refreshCarControlUI();
  }

  // 从protobuf数据更新车辆状态
  void _updateCarStatusFromProtobuf(SgmwAppCarStatus carStatus) {}

  // 从Map数据更新车辆状态（用于处理protobuf转换的Map）
  Future<void> _updateCarStatusFromMap(Map<String, dynamic> data) async {
    if (_statusModel == null) {
      return;
    }

    String collectTimeStr;

    if (_carInfoModel?.vin?.isNotEmpty ?? false) {
      // 处理collectTime时间戳，转换为yyyy-MM-dd HH:mm:ss格式
      if (data.containsKey('collectTime') && data['collectTime'] != null) {
        try {
          // 将时间戳转换为DateTime对象
          int timestamp = int.parse(data['collectTime'].toString());
          DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
          // 格式化为yyyy-MM-dd HH:mm:ss
          collectTimeStr = DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
          LogManager().debug('[MQTT]===时间戳$timestamp转换为$collectTimeStr');
        } catch (e) {
          LogManager().debug('[MQTT]===时间戳转换失败: $e');
          collectTimeStr = DateTime.now().toString();
        }

        bool isNeedUpdate = await CarStatusChecker()
            .isCarStatusOutOfDateWithTimeStr(
                collectTimeStr, _carInfoModel!.vin!);
        LogManager().debug('[MQTT]===是否需要更新车辆数据: isNeedUpdate:$isNeedUpdate');
        if (isNeedUpdate) {
          // 将当前状态转换为Map
          Map<String, dynamic> statusMap = _statusModel!.toJson();
          // 遍历输入的Map数据
          data.forEach((key, value) {
            // 先过滤掉 null
            if (value == null) return;

            // 如果是字符串，要进一步判断是不是“空白串”
            if (value is String) {
              if (value.trim().isNotEmpty) {
                // 去掉前后空白后，如果长度 > 0 才算“有效”
                statusMap[key] = value;
              }
              // 如果 trim 后是空，就不赋值
              return;
            }
            // 如果是整型或浮点型，只要不为 null，就认为有效
            if (value is int || value is double) {
              if (key == 'collectTime') {
                statusMap[key] = collectTimeStr;
              } else {
                statusMap[key] = value;
              }
              return;
            }
          });
          setState(() {
            // 使用fromJson创建新的CarStatusModel实例
            CarStatusModel newStatus = CarStatusModel.fromJson(statusMap);

            LogManager().log(
                '[MQTT]===[更新车况]===异步车况变更推送，_currentServiceCode:$_currentServiceCode,statusMap[_currentServiceCode]:${statusMap[_currentServiceCode]} ,_MQTTBackStatus:$_MQTTBackStatus , _currentACStatus:$_currentACStatus,_statusModel?.acStatus:${_statusModel?.acStatus} ,_statusModel?.accCntTemp:${_statusModel?.accCntTemp} , IS_MQTT_CAR_CONTROL:$IS_MQTT_CAR_CONTROL , IS_AC_HOT_WIND_SETTING:$IS_AC_HOT_WIND_SETTING');
            //如果是异步车控的话，取消loadding,重置空调状态
            if (_MQTTBackStatus == '1' && IS_MQTT_CAR_CONTROL) {
              if ('0' == _currentACStatus && !IS_AC_HOT_WIND_SETTING) {
                //如果本地状态处于空调关闭,就不用等温度过来了
                if (_currentACStatus != statusMap[_currentServiceCode]) {
                  MQTTManager.shared.stopMqttTimeOutTimer();
                  checkAcSettingDialogWithStatus(
                      oldStatus: _statusModel, newStatus: newStatus);
                  // 检查是否设置空调的当前温度成功
                  if (_isAcSettingDialogVisible) {
                    NotificationManager().postNotification(
                        Constant
                            .NOTIFICATION_SLIDER_CHANGE_SEND_INSTRUCT_RESULT,
                        userInfo: {'success': true});
                  }
                  // 更新状态模型
                  _statusModel = newStatus;
                  checkSeatSettingDialogWithStatus(statusModel: _statusModel);
                  LoadingManager.showSuccess("操作成功");
                  IS_MQTT_CAR_CONTROL = false;
                  _currentACStatus = statusMap[_currentServiceCode];
                  onCarControlFinish();
                }
              } else if ('0' != _currentACStatus && !IS_AC_HOT_WIND_SETTING) {
                int accCntTemp = SpUtil()
                    .getDouble(SP_CURRENT_SETTING_ELECTRIC_AC_LEVEL_KEY)
                    .toInt();
                int targetAC = transformTemperature(accCntTemp);
                //本地空调状态与最新返回的不一致就代表成功或者操作的温度和返回的温度一致也代表成功了
                if (_currentACStatus != statusMap[_currentServiceCode] ||
                    AC_TEMPERATURE == ((_statusModel?.accCntTemp) ?? '') ||
                    ((currentAccType == 3 || currentAccType == 5) &&
                        targetAC == ((_statusModel?.accCntTemp) ?? ''))) {
                  MQTTManager.shared.stopMqttTimeOutTimer();
                  checkAcSettingDialogWithStatus(
                      oldStatus: _statusModel, newStatus: newStatus);
                  // 检查是否设置空调的当前温度成功
                  if (_isAcSettingDialogVisible) {
                    NotificationManager().postNotification(
                        Constant
                            .NOTIFICATION_SLIDER_CHANGE_SEND_INSTRUCT_RESULT,
                        userInfo: {'success': true});
                  }
                  // 更新状态模型
                  _statusModel = newStatus;
                  checkSeatSettingDialogWithStatus(statusModel: _statusModel);
                  LoadingManager.showSuccess("操作成功");
                  IS_MQTT_CAR_CONTROL = false;
                  _currentACStatus = statusMap[_currentServiceCode];
                  onCarControlFinish();
                }
              } else if (_workLevel == statusMap[_seatCode] &&
                  IS_AC_HOT_WIND_SETTING) {
                MQTTManager.shared.stopMqttTimeOutTimer();
                // 更新状态模型
                _statusModel = newStatus;
                checkSeatSettingDialogWithStatus(statusModel: _statusModel);
                LoadingManager.showSuccess("操作成功");
                IS_MQTT_CAR_CONTROL = false;
                onCarControlFinish();
              }
            } else {
              // 检查是否需要显示空调设置对话框
              checkAcSettingDialogWithStatus(
                  oldStatus: _statusModel, newStatus: newStatus);
              // 检查是否设置空调的当前温度成功
              if (_isAcSettingDialogVisible) {
                NotificationManager().postNotification(
                    Constant.NOTIFICATION_SLIDER_CHANGE_SEND_INSTRUCT_RESULT,
                    userInfo: {'success': true});
              }
              // 更新状态模型
              _statusModel = newStatus;
              checkSeatSettingDialogWithStatus(statusModel: _statusModel);
              LogManager()
                  .debug('[MQTT]===更新后的车辆数据: newStatus:${newStatus.toJson()}');
              // 缓存更新后的车辆状态
              _cacheCarStatus(newStatus);
            }
            // 更新控制项列表
            updateCtrlItemList();
            updateCtrlSubItemList();
          });
        }
      }
    } else {
      LogManager().debug('[MQTT]===刷新数据错误---当前无保存的vin数据');
    }
  }

  bool isNullOrBlank(String? s) {
    // 1. 判空：如果 s 为 null，直接返回 true
    if (s == null) return true;

    // 2. 去掉前后空白字符后判断长度：如果去掉空白后的结果长度为 0，就认为是“空白串”
    return s.trim().isEmpty;
  }

  // 从JSON数据更新车辆状态
  void _updateCarStatusFromJson(Map<String, dynamic> data) {
    // 根据JSON数据更新_statusModel
    // 例如：
    // if (data.containsKey('engineStatus')) {
    //   _statusModel?.engineStatus = data['engineStatus'];
    // }
  }

  // 刷新车辆控制UI
  void _refreshCarControlUI() {
    // 可以在这里添加特定的UI刷新逻辑
    // 比如更新控制按钮状态、显示状态变化动画等
    LogManager().debug('车辆控制状态已更新，刷新UI');
  }

  // 页面级网络监控相关变量
  StreamSubscription<ConnectivityResult>? _pageNetworkSubscription;

  // 初始化页面级网络监控
  void _initPageNetworkMonitoring() async {
    // 立即检测一次当前网络状态
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      setConnectionStatus(connectivityResult);
    } catch (e) {
      LogManager().debug('UseCarPage: 检测网络状态失败 - $e');
    }

    // 监听网络状态变化（仅针对此页面）
    _pageNetworkSubscription = Connectivity()
        .onConnectivityChanged
        .listen((ConnectivityResult result) {
      setConnectionStatus(result);
    });
  }

  // 初始化网络监听（保留原有方法以兼容）
  void initConnectivity() async {
    // 这个方法现在由 _initPageNetworkMonitoring 替代
    _initPageNetworkMonitoring();
  }

  // 页面级网络检测弹窗状态
  bool _isPageNetworkDialogShowing = false;
  // 首次进入APP标记符
  bool _isFirstTimeEntering = true;

  // 设置网络状态
  void setConnectionStatus(ConnectivityResult result) {
    bool wasDisconnected = _isDisconnected;

    switch (result) {
      case ConnectivityResult.wifi:
        if (_isDisconnected) {
          LogManager().log('UseCarPage: Connected - WiFi');
          _isDisconnected = false;
          _isOfflineMode = false; // 网络恢复时自动退出离线模式
          _hidePageNetworkDialog(); // 隐藏网络检测弹窗
        }
        break;
      case ConnectivityResult.mobile:
        if (_isDisconnected) {
          LogManager().log('UseCarPage: Connected - Mobile');
          _isDisconnected = false;
          _isOfflineMode = false; // 网络恢复时自动退出离线模式
          _hidePageNetworkDialog(); // 隐藏网络检测弹窗
        }
        break;
      case ConnectivityResult.none:
        LogManager().log('UseCarPage: Disconnected');
        _isDisconnected = true;
        _showPageNetworkDialog(); // 显示网络检测弹窗
        break;
      default:
        LogManager().log('UseCarPage: Unknown');
        _isDisconnected = true;
        _showPageNetworkDialog(); // 显示网络检测弹窗
    }

    setState(() {
      _showNetworkBanner = _isDisconnected;
    });
  }

  // 显示页面级网络检测弹窗
  void _showPageNetworkDialog() {
    if (_isPageNetworkDialogShowing || !mounted) return;

    // 在离线模式下，不显示网络检测弹窗
    if (_isOfflineMode) {
      LogManager().debug('UseCarPage: 离线模式下跳过网络检测弹窗');
      return;
    }

    // 当bleType为0时，不支持蓝牙功能，不显示离线模式弹窗
    if (_carInfoModel?.bleType == 0) {
      LogManager().debug('UseCarPage: 不支持蓝牙功能，跳过离线模式弹窗');
      return;
    }

    // 优化：如果支持蓝牙功能，直接跳过弹窗显示，因为会自动进入离线模式
    if (_carInfoModel?.bleType != 0) {
      LogManager().debug('UseCarPage: 支持蓝牙功能，跳过网络检测弹窗，等待自动进入离线模式');
      return;
    }

    _isPageNetworkDialogShowing = true;

    // 延迟一下确保上下文可用
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted && _isDisconnected) {
        LogManager().debug('UseCarPage: 显示网络检测弹窗');
        _showNetworkCheckDialog();
      } else {
        _isPageNetworkDialogShowing = false;
      }
    });
  }

  // 隐藏页面级网络检测弹窗
  void _hidePageNetworkDialog() {
    if (_isPageNetworkDialogShowing && mounted) {
      Navigator.of(context).popUntil((route) => route.isFirst);
      _isPageNetworkDialogShowing = false;
    }
  }

  // 显示网络检测弹窗
  void _showNetworkCheckDialog() {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false, // 不允许点击外部关闭
      builder: (BuildContext context) {
        return Dialog(
          backgroundColor: Colors.transparent,
          child: Container(
            width: 280,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 弹窗内容
                Padding(
                  padding: const EdgeInsets.fromLTRB(24, 32, 24, 24),
                  child: Text(
                    '系统检测到网络不好，是否进入离线模式使用车控功能？',
                    style: TextStyle(
                      fontSize: 16,
                      color: Color(0xFF333333),
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                // 分割线
                Container(
                  height: 0.5,
                  color: Color(0xFFE5E5E5),
                ),
                // 按钮区域
                Row(
                  children: [
                    // 我知道了按钮
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          Navigator.of(context).pop();
                          _isPageNetworkDialogShowing = false;
                          LogManager().debug('用户选择：我知道了');
                        },
                        child: Container(
                          height: 50,
                          alignment: Alignment.center,
                          child: Text(
                            '我知道了',
                            style: TextStyle(
                              fontSize: 16,
                              color: Color(0xFF666666),
                            ),
                          ),
                        ),
                      ),
                    ),
                    // 竖直分割线
                    Container(
                      width: 0.5,
                      height: 50,
                      color: Color(0xFFE5E5E5),
                    ),
                    // 立即进入按钮
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          Navigator.of(context).pop();
                          _isPageNetworkDialogShowing = false;
                          _enterOfflineMode();
                          LogManager().debug('用户选择：立即进入离线模式');
                        },
                        child: Container(
                          height: 50,
                          alignment: Alignment.center,
                          child: Text(
                            '立即进入',
                            style: TextStyle(
                              fontSize: 16,
                              color: Color(0xFF4AA3FF),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // 进入离线模式
  void _enterOfflineMode() {
    // 检查是否支持离线模式（bleType = 2）
    if (_carInfoModel?.bleType != 2) {
      LogManager().debug('当前车辆不支持离线模式，bleType: ${_carInfoModel?.bleType}');
      return;
    }

    setState(() {
      _isOfflineMode = true;
    });
    // 可以在这里添加离线模式的相关逻辑
  }

  // <editor-fold desc="Notifications ">
  void setupNotification() {
    NotificationManager().subscribe(
        Constant.NOTIFICATION_LOGIN_SUCCEED, _receivedLoginSucceedNotification);
    NotificationManager().subscribe(Constant.NOTIFICATION_LOGOUT_SUCCEED,
        _receivedLogoutSucceedNotification);
    NotificationManager().subscribe(Constant.NOTIFICATION_SWITCH_CAR_SUCCEED,
        _receivedSwitchCarSucceedNotification);
    NotificationManager().subscribe(
        Constant.NOTIFICATION_SERVICE_DATA_REFRESH, reGetNotificationData);
    NotificationManager()
        .subscribe(Constant.NOTIFICATION_SERVICE_UPDATE, _handleServiceUpdate);
  }

  void _receivedLogoutSucceedNotification(CustomNotification notification) {
    setState(() {
      _refreshController.requestRefresh();
      buttonTitle = '登录/注册';
    });
  }

  void _receivedLoginSucceedNotification(CustomNotification notification) {
    setState(() {
      _refreshController.requestRefresh();
      buttonTitle = '立即选购';
    });
  }

  void _receivedSwitchCarSucceedNotification(CustomNotification notification) {
    LogManager().debug(
        '收到切换爱车成功的通知，_receivedSwitchCarSucceedNotification,userInfo:${notification.userInfo.toString()}');
    setState(() {
      if ((notification.userInfo ?? {}).containsKey('vin')) {
        String vin = notification.userInfo!['vin'];
        // 如果切换的车辆是不同的车辆，并且是新能源可用蓝牙钥匙车型，则根据状态断开连接或者停止扫描
        if (vin != _carInfoModel?.vin) {
          LogManager().debug('收到切换爱车成功的通知，_receivedSwitchCarSucceedNotification：controlView:${_carInfoModel?.controlView} ，bleType:${_carInfoModel?.bleType} ，_currentBleStatus:$_currentBleStatus ,MQTT.isConnected:${MQTTManager.shared.isConnected}');
          _switchCarInfo();
        }
      }
    });
  }

  _switchCarInfo() {
    print('切换爱车成功,_switchCarInfo===');
    if (MQTTManager.shared.isConnected) {
      MQTTManager.shared.disconnect(); //切换爱车后，先断连上一辆车的MQTT
    }
    _isHandleConnectBle = false;
    _userHandleDisConnectBle = false;
    _currentConnectBleVin = '';
    _currentCarVin = '';
    if (_carInfoModel?.controlView == 3 && _carInfoModel?.bleType == 2) {
      if (_currentBleStatus == BleStatus.bleAuthorized) {
        // 如果已经连接，则断开连接
        _disconnect(false);
      } else if (_currentBleStatus == BleStatus.bleSearching) {
        // 如果正在扫描，则取消扫描
        _stopScan(false);
      }
      // 重设蓝牙钥匙获取标记
      _resetBlekeyStatus();
    }
    if (_customTimer.isRunning()) {
      _customTimer.dispose();
    }
    _notifiList = null;
    _mainModel = null;
    _refreshController.requestRefresh();
  }

  _handleServiceUpdate(CustomNotification data) {
    UserHandleListModel model = data.payload;
    setState(() {
      _servicesHandleList = model;
    });
  }
  // </editor-fold>

  // <editor-fold desc="BleManager Methods">
  void _subscribeToStatusStream() {
    _statusSubscription = _bleManager.statusStream.listen((status) {
      setState(() {
        _currentBleStatus = status;
      });

      // LogManager().debug('[BleKey]===蓝牙状态回调_subscribeToStatusStream，_userHandleDisConnectBle:$_userHandleDisConnectBle，_currentBleStatus：$_currentBleStatus , _bleConnectFuntion:$_bleConnectFuntion , bluetoothKeyConnectMark:${_isOpenBLEAutoConnect()} , _currentConnectBleVin:$_currentConnectBleVin');
      // if(_currentBleStatus == BleStatus.bleDefault && !_userHandleDisConnectBle && _isOpenBLEAutoConnect() == '1' && _currentConnectBleVin.isNotEmpty) {
      //   //如果是自动连接，并且连接失败了，2秒后会继续进行自动连接
      //   Future.delayed(const Duration(seconds: 2), () {

      //     if(_currentBleStatus == BleStatus.bleDefault && !_userHandleDisConnectBle && _isOpenBLEAutoConnect() == '1' && _currentConnectBleVin.isNotEmpty) {
      //       //2秒后再检测一次状态
      //       BluetoothAdapterState currentSystemBluetoothStatus =
      //       _bleManager.getCurrentSystemBluetoothStatus();
      //       if (currentSystemBluetoothStatus == BluetoothAdapterState.on) {
      //         LogManager().debug('[BleKey]===蓝牙状态回调_subscribeToStatusStream,_startScan()');
      //         _isHandleConnectBle = false;
      //         _startScan(false);
      //       }
      //     }
      //   });
      // }
      if (status == BleStatus.bleAuthorized && _bleConnectFuntion != null) {
        _bleConnectFuntion!(true, '');
        _bleConnectFuntion = null;
      } else if (status == BleStatus.bleDefault && _bleConnectFuntion != null) {
        _bleConnectFuntion!(false, '蓝牙连接失败，请下拉刷新重试');
        _bleConnectFuntion = null;
        LogManager().debug(
            '[BleKey]===蓝牙状态回调，isHandleConnect：${_bleManager.isHandleConnect}');
        if (_bleManager.isHandleConnect) {
          LoadingManager.showInfo('蓝牙连接失败，请下拉刷新重试',
              maskType: EasyLoadingMaskType.clear);
        }
      }
    });
  }

  void _clickBleButton() {
    BluetoothAdapterState currentSystemBluetoothStatus =
        _bleManager.getCurrentSystemBluetoothStatus();
    LogManager().debug(
        '[BleKey]===手动点击蓝牙按钮,_currentBleStatus:$_currentBleStatus ， currentSystemBluetoothStatus:$currentSystemBluetoothStatus');
    if (currentSystemBluetoothStatus == BluetoothAdapterState.on) {
      _userHandleDisConnectBle = false;
      if (_currentBleStatus == BleStatus.bleDefault) {
        if (!BleManager().hasBluetoothKey()) {
          LoadingManager.showInfo('未正确获取到蓝牙钥匙，请下拉刷新重新获取',
              maskType: EasyLoadingMaskType.clear);
        } else {
          // setState(() {
          //   _isHandleConnectBle = true;
          // });
          _startScan(true);
        }
      } else if (_currentBleStatus == BleStatus.bleSearching) {
        // _stopScan(true);
        // setState(() {
        //   _isHandleConnectBle = true;
        // });
        _bleManager.setIsHandleConnect(true); //这里需要将连接变成手动连接
      } else if (_currentBleStatus == BleStatus.bleAuthorizing ||
          _currentBleStatus == BleStatus.bleAuthHandshaking1 ||
          _currentBleStatus == BleStatus.bleAuthHandshaking2) {
        // setState(() {
        //   _isHandleConnectBle = true;
        // });
        _bleManager.setIsHandleConnect(true); //这里需要将连接变成手动连接
        // LoadingManager.showInfo('正在进行鉴权');
      } else if (_currentBleStatus == BleStatus.bleAuthorized) {
        _isHandleConnectBle = false;
        _userHandleDisConnectBle = true;
        _disconnect(true);
      }
    } else {
      String notice = '';
      switch (currentSystemBluetoothStatus) {
        case BluetoothAdapterState.off:
          notice = '当前系统蓝牙未打开 请打开后再尝试';
          break;
        case BluetoothAdapterState.unauthorized:
          notice = '当前系统未授权使用蓝牙 请授权后再尝试';
          break;
        case BluetoothAdapterState.unavailable:
          notice = '当前设备无法使用蓝牙功能';
          break;
        case BluetoothAdapterState.turningOn:
          notice = '当前系统蓝牙正在打开 请稍候';
          break;
        case BluetoothAdapterState.turningOff:
          notice = '当前系统蓝牙正在关闭 请稍候';
          break;
        default:
          notice = '未知蓝牙状态';
      }
      LoadingManager.showInfo(notice);
    }
  }

  void _stopScan(bool isHandle) {
    _bleManager.stopScan(isHandle);
  }

  String _isOpenBLEAutoConnect() {
    return GlobalData().carInfoModel?.bluetoothKeyConnectMark ?? '';
  }

  void _startScan(bool isHandle) {
    _bleManager.startScanWithTimeout(isHandle, 15, (bool found) {
      if (!found) {
        LogManager().debug(
            '[BleKey]===扫描超时startScanWithTimeout，_userHandleDisConnectBle:$_userHandleDisConnectBle，_isHandleConnectBle:$_isHandleConnectBle，_currentBleStatus：$_currentBleStatus , _bleConnectFuntion:$_bleConnectFuntion , bluetoothKeyConnectMark:${_isOpenBLEAutoConnect()}');
        // if(_isHandleConnectBle){
        showCarNotFoundAlertDialog(context);
        // }

        if (_currentBleStatus == BleStatus.bleDefault &&
            !_userHandleDisConnectBle &&
            _isOpenBLEAutoConnect() == '1') {
          //如果是自动连接，并且连接失败了，2秒后会继续进行自动连接
          // Future.delayed(const Duration(seconds: 2), () {
          //   BluetoothAdapterState currentSystemBluetoothStatus =
          //   _bleManager.getCurrentSystemBluetoothStatus();
          //   if (currentSystemBluetoothStatus == BluetoothAdapterState.on) {
          //     setState(() {
          //       _isHandleConnectBle = false;
          //     });
          //     _startScan(false);
          //   }
          // });
        }
        if (_bleConnectFuntion != null) {
          _bleConnectFuntion!(false, '未搜索到您的爱车，请尽量靠近您的爱车后再重试');
          _bleConnectFuntion = null;
        }
      }
    });
  }

  void _disconnect(bool isHandle) {
    _bleManager.disconnectDevice(isHandle);
  }

  void _resetBlekeyStatus() {
    _bleManager.resetHasBluetoothKey(false);
  }

  void doBleCarControl(
      {String? actionStr,
      required CarControlItemModel controlItemModel,
      required CarServiceStatusModel? statusModel}) {
    CarServiceModel? serviceModel = controlItemModel?.serviceModel;
    String serviceBleSkipTarget = serviceModel?.serviceBleSkipTarget ?? '';
    LoadingManager.show(
        status: '【蓝牙】$actionStr中...', maskType: EasyLoadingMaskType.black);
    Map<String, dynamic> params =
        controlItemModel.getCarBleServiceParamListForStatus(
            statusModel: statusModel,
            insideParamsMap: GlobalData().insideParamsMap);
    LogManager()
        .debug('serviceBleSkipTarget:$serviceBleSkipTarget, params:$params');
    _bleManager.sendCommandWithServiceBleSkipTarget(
        serviceBleSkipTarget, params, (error, isSuccess) {
      if (isSuccess) {
        LoadingManager.showSuccess('【蓝牙】$actionStr成功',
            maskType: EasyLoadingMaskType.black);
        setState(() {
          Map<String, dynamic> statusMap = _statusModel!.toJson();
          String serviceCode = controlItemModel.serviceModel.serviceCode;
          String backStatusValue = statusModel!.backStatusValue;
          LogManager().debug(
              '[CAR_STATUS]===$serviceCode的值从${statusMap[serviceCode]}更新为$backStatusValue');
          statusMap[serviceCode] = backStatusValue;
          _statusModel = CarStatusModel.fromJson(statusMap);
          updateCtrlItemList();
          updateCtrlSubItemList();
          if (_carInfoModel?.vin?.isNotEmpty ?? false) {
            //如果蓝牙指令执行成功，更新当前的车辆状态获取时间为当前时间防止跳变
            // 获取当前时间
            DateTime now = DateTime.now();
            String formattedDate =
                DateFormat('yyyy-MM-dd HH:mm:ss').format(now);
            LogManager().debug(
                '[BleKey]===蓝牙接口调用成功，当前返回时间为$formattedDate，保存为新的车控状态时间===');
            CarStatusChecker().saveCarStatusTimeWithTimeStr(
                formattedDate, _carInfoModel!.vin!);
          }
        });
      } else {
        LoadingManager.showError('【蓝牙】$actionStr失败 $error');
      }
    });
  }

  // </editor-fold>

  // <editor-fold desc="Refresher Methods">
  void _onRefresh() async {
    // 在离线模式下，跳过所有网络请求
    if (_isOfflineMode) {
      LogManager().debug('UseCarPage: 离线模式下跳过数据刷新');
      _refreshController.refreshCompleted();
      return;
    }

    // 检查网络状态
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      setConnectionStatus(connectivityResult);
    } catch (e) {
      LogManager().debug('UseCarPage: 下拉刷新时检测网络状态失败 - $e');
    }

    // 无网络时的处理逻辑
    if (_isDisconnected) {
      // 检查是否支持离线模式（bleType = 2）
      if (_carInfoModel?.bleType != 2) {
        _refreshController.refreshCompleted();
        return;
      }

      if (_isFirstTimeEntering) {
        // 首次进入APP：直接进入离线模式，不显示弹窗
        _enterOfflineMode();
        await _loadDataFromCache();
        _isFirstTimeEntering = false; // 标记首次进入完成
      } else {
        // 后续下拉刷新：显示弹窗让用户选择
        _showNetworkCheckDialog();
      }
      _refreshController.refreshCompleted();
      return;
    }

    if (GlobalData().isLogin) {
      try {
        if (_statusModel != null) {
          _isRefresh = true;
        }
        CarStatusResponseModel carStatusResponseModel =
            await carAPI.getDefaultCarInfoAndStatus();
        final carInfoModel = carStatusResponseModel.carInfo;
        final carStatusModel = carStatusResponseModel.carStatus;
        log.info('手动下拉刷新接口请求成功，carStatus：${carStatusResponseModel.toJson()}');
        setState(() {
          _carInfoModel = carInfoModel;
          _isCarUser = _carInfoModel?.vin?.isNotEmpty ?? false;
          _statusModel = carStatusModel;
        });

        if (_carInfoModel != null) {
          String key =
              '${SP_USER_DEFAULT_CAR_KEY}_${GlobalData().userModel?.userIdStr}';
          SpUtil().setJSON(key, _carInfoModel!.toJson());
          // String bluetoothKeyConnectMark = _carInfoModel?.bluetoothKeyConnectMark ?? '';
          // SpUtil().setString(BLUE_TOOTH_KEY_CONNECT_MARK, bluetoothKeyConnectMark);
          GlobalData().carInfoModel = _carInfoModel;
          log.info(
              '手动下拉刷新接口请求成功，_userHandleDisConnectBle：$_userHandleDisConnectBle ，_bleManager.currentStatus：${_bleManager.currentStatus}，_currentConnectBleVin:$_currentConnectBleVin , currentVin:${_carInfoModel?.vin},_carInfoModel?.bleType:${_carInfoModel?.bleType}');
          // if(!_userHandleDisConnectBle && _bleManager.currentStatus != BleStatus.bleAuthorized) {
          //   if(_carInfoModel?.bleType != 0 && bluetoothKeyConnectMark == '1' && _currentConnectBleVin != _carInfoModel?.vin) {
          //     //自动连接蓝牙
          //     _startScan(false);
          //   }
          // }
        }

        // 缓存车辆状态，传入carInfoModel确保有正确的vin
        if (carStatusModel != null && carInfoModel != null) {
          _cacheCarStatus(carStatusModel, carInfo: carInfoModel);
        }
        if (showCarControlPage()) {
          getHasCarListData();
        } else {
          getNoCarListData();
        }
      } catch (e) {
        if (_customTimer.isRunning()) {
          _customTimer.dispose();
        }
        if (_refreshController.isRefresh) {
          _refreshController.refreshCompleted();
        }
      }
    } else {
      if (_customTimer.isRunning()) {
        _customTimer.dispose();
      }
      setState(() {
        _isCarUser = false;
      });
      getNoCarListData();
    }
  }

  void _fetchBluetoothKey() {
    log.log('[BleKey]===获取蓝牙钥匙===');
    if (_carInfoModel?.bleType == 2) {
      if (_carInfoModel?.vin != null) {
        String vin = _carInfoModel?.vin ?? '';
        String mobile = GlobalData().userModel?.mobile ?? '';
        log.log(
            '[BleKey]===获取蓝牙钥匙===_currentConnectBleVin：$_currentConnectBleVin , vin:$vin , _currentBleStatus:$_currentBleStatus');
        if (_currentConnectBleVin != vin) {
          if (_currentBleStatus == BleStatus.bleAuthorized) {
            // 如果已经连接，则断开连接
            _disconnect(false);
          } else if (_currentBleStatus == BleStatus.bleSearching) {
            // 如果正在扫描，则取消扫描
            _stopScan(false);
          }
          // 重设蓝牙钥匙获取标记
          _resetBlekeyStatus();
        }
        _bleManager.getBluetoothKey(
          vin: vin,
          mobile: mobile,
          onResult: (CarBleKeyModel? bleKeyModel, String? error) {
            if (bleKeyModel != null) {
              log.info(
                  '[BleKey]===获取蓝牙钥匙成功，userHandleDisConnectBle：$_userHandleDisConnectBle ，_bleManager.currentStatus：${_bleManager.currentStatus}，_currentConnectBleVin:$_currentConnectBleVin , currentVin:${_carInfoModel?.vin},_carInfoModel?.bleType:${_carInfoModel?.bleType} ，bleKeyModel.vin:${bleKeyModel.vin}');
              log.info('[BleKey]===缓存蓝牙钥匙，bleMac: ${bleKeyModel.bleMac}');

              // 缓存蓝牙钥匙
              MiniLifeCacheManager.saveBleKeyModel(bleKeyModel);

              if (_currentConnectBleVin != bleKeyModel.vin) {
                _currentConnectBleVin = bleKeyModel.vin;
                if (!_userHandleDisConnectBle &&
                    _bleManager.currentStatus != BleStatus.bleAuthorized) {
                  if (_carInfoModel?.bleType == 2 &&
                      _isOpenBLEAutoConnect() == '1') {
                    BluetoothAdapterState currentSystemBluetoothStatus =
                        _bleManager.getCurrentSystemBluetoothStatus();
                    if (currentSystemBluetoothStatus ==
                        BluetoothAdapterState.on) {
                      log.log('[BleKey]===获取蓝牙钥匙===首次连接蓝牙===');
                      //_isHandleConnectBle = false;
                      //自动连接蓝牙
                      //_startScan(false);
                    }
                  }
                }
              }
            } else {
              log.log('[BleKey]===获取蓝牙钥匙失败: $error');

              // 尝试从缓存获取蓝牙钥匙
              _loadBleKeyFromCache();
            }
          },
        );
      }
    }
  }

  /// 从缓存加载蓝牙钥匙
  void _loadBleKeyFromCache() async {
    try {
      log.log('[BleKey]===尝试从缓存获取蓝牙钥匙===');
      CarBleKeyModel? cachedBleKey =
          await MiniLifeCacheManager.getCachedBleKeyModel();

      if (cachedBleKey != null) {
        log.info('[BleKey]===从缓存获取蓝牙钥匙成功，bleMac: ${cachedBleKey.bleMac}');

        // 设置BleManager的蓝牙钥匙标志为true
        _bleManager.resetHasBluetoothKey(true);
        log.info('[BleKey]===已设置BleManager蓝牙钥匙标志为true');

        // 直接设置连接VIN，让BleManager知道有蓝牙钥匙可用
        if (_currentConnectBleVin != cachedBleKey.vin) {
          _currentConnectBleVin = cachedBleKey.vin;
        }

        log.info('[BleKey]===缓存蓝牙钥匙已设置，可以尝试蓝牙连接===');
      } else {
        log.log('[BleKey]===缓存中没有蓝牙钥匙数据===');
      }
    } catch (e) {
      log.log('[BleKey]===从缓存获取蓝牙钥匙失败: $e===');
    }
  }

  Future<void> updateCarPosition(carStatus) async {
    // 检查 position 是否为空字符串或 null
    if (carStatus.position == null || carStatus.position!.isEmpty) {
      // 检查 latitude 和 longitude 是否为空字符串或 null
      if (carStatus.latitude != null && carStatus.longitude != null) {
        try {
          // 调用 LocationManager.getAddressFromLocation 方法
          var address = await LocationManager.getAddressFromLocation(
            carStatus.latitude!,
            carStatus.longitude!,
          );

          // 将返回对象里的 place 属性赋给 position，并格式化地址
          carStatus.position = _formatAddress(
              address.place ?? address); // 确保 address 类型有 place 属性
        } catch (e) {
          // 如果有异常，保持原值不变
          LogManager().debug("获取位置信息时发生异常: $e");
        }
      }
    } else {
      // 对已有的地址也进行格式化处理
      carStatus.position = _formatAddress(carStatus.position!);
    }
  }

  /// 格式化地址字符串，处理门牌号中的点号分隔问题
  /// 例如：将 "河西路18号*******号" 转换为 "河西路18号1234号"
  String _formatAddress(String address) {
    // 使用正则表达式匹配门牌号中的点号分隔数字模式
    // 匹配模式：数字.数字.数字.数字号 或类似的模式
    return address.replaceAllMapped(
      RegExp(r'(\d+(?:\.\d+)+)号'),
      (Match match) {
        String numberPart = match.group(1)!;
        // 去掉所有点号，保留数字和字母
        String cleanedNumber = numberPart.replaceAll('.', '');
        return '${cleanedNumber}号';
      },
    );
  }

  void getHasCarListData() async {
    try {
      // FetchResult<Map<String, List<CarServiceModel>>> serviceResult = await carApi.fetchCarServiceList();
      // final UserHandleListModel userListResult = await userAPI.getAppFunctionIconList('31');
      // final ctrlServiceList = serviceResult.data?['ctrlServiceList'] ?? [];
      // final toolServiceList = serviceResult.data?['toolServiceList'] ?? [];
      // final useCarServiceList = userListResult.appFunctionIcons ?? [];
      // if(_carInfoModel?.bleType == 2){
      //   if(_carInfoModel?.vin != null) {
      //     String vin = _carInfoModel?.vin ?? '';
      //     String mobile = GlobalData().userModel?.mobile ?? '';
      //     LogManager().debug("蓝牙钥匙获取中...");
      //     BleManager().getBluetoothKey(
      //         vin: vin,
      //         mobile: mobile,
      //         onResult: (CarBleKeyModel? bleKeyModel, String? error){
      //           if (bleKeyModel != null) {
      //             LogManager().debug("蓝牙钥匙获取成功: ${bleKeyModel.bleMac}");
      //             // 在这里处理钥匙信息
      //           } else {
      //             LogManager().debug("蓝牙钥匙获取失败: $error");
      //             // 在这里处理错误信息
      //           }
      //         });
      //   }
      // }
      _fetchBluetoothKey();
      loginMQTT();

      final List<String> servicePositionCodeList = [
        'service_control',
        'service_tools',
        'service_activity',
        'acStatus_Layout',
        'service_screen'
      ];
      List<CarServiceResponseModel> serviceResponses =
          await carAPI.getCarServiceList(servicePositionCodeList);
      List<CarServiceModel> ctrlServiceList = [];
      List<CarServiceModel> toolServiceList = [];
      List<CarServiceModel> newuiServiceArray = [];
      List<CarServiceModel> activityList = [];
      for (var serviceResponseModel in serviceResponses) {
        switch (serviceResponseModel.positionCode) {
          case 'service_control':
            ctrlServiceList = serviceResponseModel.serviceList ?? [];

            break;
          case 'service_tools':
            toolServiceList = serviceResponseModel.serviceList ?? [];

            break;
          case 'acStatus_Layout':
            newuiServiceArray = serviceResponseModel.serviceList ?? [];
            break;
          case 'service_activity':
            activityList = serviceResponseModel.serviceList ?? [];
            break;
        }
      }
      CarMiniLifeModel? miniLifeModel = await carAPI
          .getMiniLiveModelWithVin({'vin': _carInfoModel?.vin ?? ''});

      // 保存 MiniLife 数据到缓存
      LogManager().debug('UseCarPage: 开始保存MiniLife数据到缓存');
      await MiniLifeCacheManager.saveMiniLifeData(
        activityList: activityList,
        miniLifeModel: miniLifeModel,
      );
      LogManager().debug('UseCarPage: MiniLife数据缓存保存完成');

      setState(() {
        // 先设置变量，再调用方法，确保方法中能访问到正确的数据
        _ctrlServiceList = ctrlServiceList;
        _toolServiceList = toolServiceList;
        _newuiServiceArray = newuiServiceArray;
        _activityList = activityList;
        _miniLifeModel = miniLifeModel;
        LogManager().debug(
            'DEBUG: setState中设置变量完成 - _toolServiceList长度: ${_toolServiceList.length}');

        buildCtrlItemList(ctrlServiceList);
        updateCtrlItemList();
        updateCtrlSubItemList();
        _isNeedCheckTire = handleNeedCheckTire();
      });
      final UserHandleListModel userListResult =
          await userAPI.getAppFunctionIconList('31');
      final useCarServiceList = userListResult.appFunctionIcons ?? [];

      LogManager().debug('获取用车服务列表成功，数量: ${useCarServiceList.length}');
      for (int i = 0; i < useCarServiceList.length; i++) {
        final service = useCarServiceList[i];
        LogManager().debug(
            '  [$i] ${service.functionIconName}: ${service.functionIconUrl}');
      }

      setState(() {
        _useCarServiceList = useCarServiceList;
        LogManager().debug('设置_useCarServiceList完成，准备缓存');

        // 缓存服务列表数据（包括用车服务列表）
        _cacheServiceLists();
      });
      if (_carInfoModel != null) {
        _customTimer.dispose();
        if ((_carInfoModel?.controlView == 3 ||
                _carInfoModel?.controlView == 8) &&
            _carInfoModel?.supportCarConditionPoll == 1) {
          //如果是新能源或者PHEV类型，并且支持车况轮询，则开启计时器进行轮询
          int refreshTimeinterval = _carInfoModel!.conditionPollTime ?? 3;
          _customTimer.setInterval(Duration(seconds: refreshTimeinterval));
          _customTimer.start();
        }
      }

      if (_refreshController.isRefresh) {
        _refreshController.refreshCompleted();
      }

      // 有网络且数据加载成功：标记首次进入完成
      if (_isFirstTimeEntering) {
        _isFirstTimeEntering = false;
      }

      getNotificationData();
      //请求通知服务和保养信息
    } catch (e) {
      if (_isFirstTimeEntering) {
        // 首次进入APP：检查是否支持离线模式后再进入
        if (_carInfoModel?.bleType == 2) {
          _enterOfflineMode();
        } else {}
        _isFirstTimeEntering = false; // 标记首次进入完成
      }
      // 下拉刷新时网络请求失败：不显示弹窗，静默处理

      if (_refreshController.isRefresh) {
        _refreshController.refreshCompleted();
      }
    }
  }

  void reGetNotificationData(CustomNotification notification) {
    getNotificationData();
  }

  void getNotificationData() async {
    try {
      List<CarServiceModel>? notifiArr =
          await carAPI.getCarNotificationServiceData(
              _carInfoModel?.vsn ?? '',
              _carInfoModel?.vin ?? '',
              _carInfoModel?.relation ?? 1,
              _carInfoModel?.providerCode ?? '',
              _carInfoModel?.isAuthIdentity ?? 1);
      CarMaintenanceModel? mainModel =
          await carAPI.getCarMaintenanceData(_carInfoModel?.vin ?? '');
      setState(() {
        _notifiList = notifiArr;
        _mainModel = mainModel;
      });
      LogManager().debug('通知服务-- ${notifiArr?.length}');
      Future.delayed(const Duration(milliseconds: 500), () {
        setState(() {
          _isRefresh = false;
        });
      });
    } catch (e) {
      LogManager().debug('通知服务---${e.toString()}');
    }
  }

  /// MQTT登录方法 - 与iOS原版逻辑一致
  void loginMQTT() {
    String vin = _carInfoModel?.vin ?? '';
    LogManager().debug(
        'MQTT登录，loginMQTT: _currentCarVin - $_currentCarVin ， vin：$vin ， supportMqtt：${_carInfoModel?.supportMqtt} , isLogin:${GlobalData().isLogin}');
    // 如果是使用新界面的类型
    if (GlobalData().isLogin && (_carInfoModel?.supportMqtt == 1)) {
      // 检查VIN是否发生变化，如果变化则断开连接
      // if (_carInfoModel?.vin != MQTTManager.shared.prefix) {
      //   MQTTManager.shared.disconnect();
      // }
      if (vin.isNotEmpty &&
          _currentCarVin != vin &&
          MQTTManager.shared.isConnected) {
        //说明切换了爱车，当前爱车连接着MQTT，此时通过后台绑车或者他人授权了车，先将上一台车的MQTT断连，不然当前爱车无法连接MQTT
        MQTTManager.shared.disconnect();
      }
      // 检查MQTT连接状态
      if (!MQTTManager.shared.isConnected) {
        // 获取MQTT登录账号密码等信息
        MQTTManager.shared.setSupportMqtt(true);

        if (vin.isNotEmpty) {
          _currentCarVin = vin;
          MQTTManager.shared.getTokenAndLoginWithVin(
            vin ?? '',
            complete: (success) {
              if (success) {
                LogManager().log('MQTT: 登录成功');
              } else {
                LogManager().log('MQTT: 登录失败');
              }
            },
          );
        } else {
          LogManager().log('MQTT: VIN为空，无法登录');
        }
      }
    } else {
      _currentCarVin = vin;
      // 不支持mqtt则断开mqtt
      MQTTManager.shared.setSupportMqtt(false);
      MQTTManager.shared.disconnect();
      LogManager().log('MQTT: 用户未登录或车辆不支持MQTT，断开连接');
    }
  }

  void getNoCarListData() async {
    try {
      List<AdvertisePositionModel> list =
          await commonAPI.getAdvertisePositionList(2);
      setState(() {
        _positionModelList = list;
      });
      final UserHandleListModel servicesHandleList = await userAPI
          .getAppFunctionIconList('${UserHandleListType.goNoCar.value}');
      setState(() {
        _servicesHandleList = servicesHandleList;
      });
      List<Advertise> advertiseList =
          await commonAPI.getAdvertiseWithPosition(83);
      setState(() {
        _advertiseList = advertiseList;
      });
      List<ColumnModel> columnList =
          await postAPI.getHomeListWithColumnType(18);
      setState(() {
        if (columnList.isNotEmpty) {
          _columnModel = columnList.first;
        }
      });
      var location = await LocationManager.getLocation();

      if (location != null) {
        // LoadingManager.showToast(jsonEncode(location));
        List<DealerModel> dealerList =
            await shopAPI.downloadDealerListWithCityName(location['locality']!,
                location['longitude']!, location['latitude']!);
        setState(() {
          _dealerList = dealerList;
        });
      } else {
        List<DealerModel> dealerList = await shopAPI
            .downloadDealerListWithCityName('柳州市', 109.374563, 24.327330);
        setState(() {
          _dealerList = dealerList;
        });
      }

      if (_refreshController.isRefresh) {
        _refreshController.refreshCompleted();
      }

      // 有网络且数据加载成功：标记首次进入完成
      if (_isFirstTimeEntering) {
        _isFirstTimeEntering = false;
      }
    } catch (e) {
      if (_customTimer.isRunning()) {
        _customTimer.dispose();
      }

      if (_isFirstTimeEntering) {
        // 首次进入APP：检查是否支持离线模式后再进入
        if (_carInfoModel?.bleType == 2) {
          _enterOfflineMode();
        } else {}
        _isFirstTimeEntering = false; // 标记首次进入完成
      }
      // 下拉刷新时网络请求失败：不显示弹窗，静默处理

      if (_refreshController.isRefresh) {
        _refreshController.refreshCompleted();
      }
    }
  }

  void _onLoading() async {
    if (GlobalData().isLogin) {
      if (_refreshController.isLoading) {
        await Future.delayed(const Duration(milliseconds: 1000));
        _refreshController.loadNoData();
      }
    } else {
      if (_refreshController.isLoading) {
        await Future.delayed(const Duration(milliseconds: 1000));
        _refreshController.loadNoData();
      }
    }
  }
  // </editor-fold>

  // <editor-fold desc="Network Methods">

  void doSetDefaultCarNetWork(
      CarSimplifiedInfoModel? carSimplifiedInfoModel) async {
    if (carSimplifiedInfoModel != null) {
      try {
        LoadingManager.show();
        bool isSucceed =
            await carAPI.doSetDefaultCarWithVin(carSimplifiedInfoModel.vin);
        LoadingManager.dismiss();
        // _onRefresh();
        NotificationManager().postNotification(
            Constant.NOTIFICATION_SWITCH_CAR_SUCCEED,
            userInfo: {'vin': carSimplifiedInfoModel.vin});
      } catch (e) {
        if (e is APIException) {
          LoadingManager.showError('切换失败 ${e.message}');
        } else {
          LoadingManager.showError('切换失败 请稍后重试');
        }
      }
    }
  }

  void handleError(String message) {
    // 处理错误
    log.info("请求错误: $message");
    if (_refreshController.isRefresh) {
      _refreshController.refreshCompleted();
    }
  }

  void handlerSuccessForCarStatus({
    CarStatusModel? carStatus,
  }) async {
    if (carStatus != null) {
      if (_carInfoModel != null) {
        try {
          if (_carInfoModel!.vin != null) {
            bool isNeedUpdate = await CarStatusChecker()
                .isCarStatusOutOfDateWithTimeStr(
                    carStatus.collectTime, _carInfoModel!.vin!);
            if (isNeedUpdate) {
              setState(() {
                Map<String, dynamic>? statusMap = carStatus.toJson();
                // JsonEncoder encoder = const JsonEncoder.withIndent('  ');
                // String prettyPrint = encoder.convert(statusMap);
                // log.info('轮询车辆状态成功 _statusModel = $prettyPrint');
                LogManager().log(
                    '[更新车况]===轮询车辆状态成功，_currentServiceCode:$_currentServiceCode,statusMap[_currentServiceCode]:${statusMap[_currentServiceCode]} ,_MQTTBackStatus:$_MQTTBackStatus , _currentACStatus:$_currentACStatus,_statusModel?.acStatus:${_statusModel?.acStatus} ,_statusModel?.accCntTemp:${_statusModel?.accCntTemp}  ,IS_MQTT_CAR_CONTROL:$IS_MQTT_CAR_CONTROL , IS_AC_HOT_WIND_SETTING:$IS_AC_HOT_WIND_SETTING');
                //如果是异步车控的话，取消loadding,重置空调状态
                if (_MQTTBackStatus == '1' && IS_MQTT_CAR_CONTROL) {
                  if ('0' == _currentACStatus && !IS_AC_HOT_WIND_SETTING) {
                    //如果本地状态处于空调关闭,就不用等温度过来了
                    if (_currentACStatus != statusMap[_currentServiceCode]) {
                      MQTTManager.shared.stopMqttTimeOutTimer();
                      checkAcSettingDialogWithStatus(
                          oldStatus: _statusModel, newStatus: carStatus);
                      // 检查是否设置空调的当前温度成功
                      if (_isAcSettingDialogVisible) {
                        NotificationManager().postNotification(
                            Constant
                                .NOTIFICATION_SLIDER_CHANGE_SEND_INSTRUCT_RESULT,
                            userInfo: {'success': true});
                      }
                      // 更新状态模型
                      _statusModel = carStatus;
                      checkSeatSettingDialogWithStatus(
                          statusModel: _statusModel);
                      LoadingManager.showSuccess("操作成功");
                      IS_MQTT_CAR_CONTROL = false;
                      _currentACStatus = statusMap[_currentServiceCode];
                      onCarControlFinish();
                      // 缓存更新后的车辆状态
                      _cacheCarStatus(carStatus);
                    }
                  } else if ('0' != _currentACStatus &&
                      !IS_AC_HOT_WIND_SETTING) {
                    int accCntTemp = SpUtil()
                        .getDouble(SP_CURRENT_SETTING_ELECTRIC_AC_LEVEL_KEY)
                        .toInt();
                    int targetAC = transformTemperature(accCntTemp);
                    //本地空调状态与最新返回的不一致就代表成功或者操作的温度和返回的温度一致也代表成功了
                    if (_currentACStatus != statusMap[_currentServiceCode] ||
                        AC_TEMPERATURE == ((_statusModel?.accCntTemp) ?? '') ||
                        ((currentAccType == 3 || currentAccType == 5) &&
                            targetAC == ((_statusModel?.accCntTemp) ?? ''))) {
                      MQTTManager.shared.stopMqttTimeOutTimer();
                      checkAcSettingDialogWithStatus(
                          oldStatus: _statusModel, newStatus: carStatus);
                      // 检查是否设置空调的当前温度成功
                      if (_isAcSettingDialogVisible) {
                        NotificationManager().postNotification(
                            Constant
                                .NOTIFICATION_SLIDER_CHANGE_SEND_INSTRUCT_RESULT,
                            userInfo: {'success': true});
                      }
                      // 更新状态模型
                      _statusModel = carStatus;
                      checkSeatSettingDialogWithStatus(
                          statusModel: _statusModel);
                      LoadingManager.showSuccess("操作成功");
                      IS_MQTT_CAR_CONTROL = false;
                      _currentACStatus = statusMap[_currentServiceCode];
                      onCarControlFinish();
                      // 缓存更新后的车辆状态
                      _cacheCarStatus(carStatus);
                    }
                  } else if (_workLevel == statusMap[_seatCode] &&
                      IS_AC_HOT_WIND_SETTING) {
                    MQTTManager.shared.stopMqttTimeOutTimer();
                    // 更新状态模型
                    _statusModel = carStatus;
                    checkSeatSettingDialogWithStatus(statusModel: _statusModel);
                    LoadingManager.showSuccess("操作成功");
                    IS_MQTT_CAR_CONTROL = false;
                    onCarControlFinish();
                    // 缓存更新后的车辆状态
                    _cacheCarStatus(carStatus);
                  }
                } else {
                  // 检查是否需要显示空调设置对话框
                  checkAcSettingDialogWithStatus(
                      oldStatus: _statusModel, newStatus: carStatus);
                  // 更新状态模型
                  _statusModel = carStatus;
                  checkSeatSettingDialogWithStatus(statusModel: _statusModel);
                  // 缓存更新后的车辆状态
                  _cacheCarStatus(carStatus);
                }
                // 更新控制项列表
                updateCtrlItemList();
                updateCtrlSubItemList();
              });
            }
          }
        } catch (e) {
          log.info('采集时间纪录发生异常：${e.toString()}');
        }
      }
    }
  }

  void fetchDefaultCarStatus({
    required Function({
      CarStatusModel? carStatus,
    })
        onSuccess,
    required Function(String) onError,
  }) async {
    if (_isAutoRefreshing) {
      log.info('自动刷新接口还在请求中，取消本次请求');
      return;
    }
    _isAutoRefreshing = true;
    try {
      // final carStatusResult = await CarAPI.fetchDefaultCarInfoAndStatus();
      // final carStatus = carStatusResult.data?['carStatus'];
      CarStatusResponseModel carStatusResponseModel =
          await carAPI.getDefaultCarInfoAndStatus();
      final carStatus = carStatusResponseModel.carStatus;
      final carInfo = carStatusResponseModel.carInfo;
      _carInfoModel = carInfo;
      if (carInfo != null) {
        if ((_currentConnectBleVin != _carInfoModel?.vin &&
                _carInfoModel?.bleType == 2) ||
            (_currentCarVin != _carInfoModel?.vin &&
                _carInfoModel?.supportMqtt == 1)) {
          _switchCarInfo();
        }
      }
      _isAutoRefreshing = false;
      log.info('自动刷新接口请求成功，carStatus：${carStatusResponseModel.toJson()}');
      onSuccess(carStatus: carStatus);
    } catch (e) {
      _isAutoRefreshing = false;
      log.info('自动刷新接口请求出现异常');
      onError(e is Exception ? e.toString() : '发生未知错误');
    }
  }

  void autoRefresh() {
    log.info('开始刷新车况，isLogin：${GlobalData().isLogin}');
    if (GlobalData().isLogin) {
      //登录状态下才轮询车况
      // 轮询时禁用错误吐司，避免无网络时频繁弹出错误提示
      HttpRequest.suppressErrorToast();
      fetchDefaultCarStatus(onSuccess: ({CarStatusModel? carStatus}) {
        // 成功后恢复错误吐司显示
        HttpRequest.enableErrorToast();
        handlerSuccessForCarStatus(carStatus: carStatus);
      }, onError: (error) {
        // 失败后也恢复错误吐司显示
        HttpRequest.enableErrorToast();
        handleError(error);
      });
    }
  }

  bool handleNeedCheckTire() {
    bool isNeedCheckTire = false;
    for (CarServiceModel serviceModel in _toolServiceList) {
      if (serviceModel.serviceSkipParamList != null) {
        for (CarServiceParamModel paramModel
            in serviceModel.serviceSkipParamList!) {
          if (paramModel.paramName == 'tair' && paramModel.paramValue == '1') {
            setState(() {
              isNeedCheckTire = true;
            });
          }
        }
      }
    }
    return isNeedCheckTire;
  }
  // </editor-fold>

  // 获取空调类型
  int _getACType() {
    // 检查数据是否已加载
    if (_controlSubItemList.isEmpty && _controlItemList.isEmpty) {
      return 0;
    }

    // 首先在_controlSubItemList中查找serviceCode为'acStatus'的项
    for (CarControlItemModel item in _controlSubItemList) {
      if (item.serviceModel.serviceCode == 'acStatus') {
        // 空调类型应该是固定的，不应该根据当前状态变化
        // 我们查找所有可用的serviceStatusAction，取最大值作为空调类型
        if (item.serviceModel.serviceStatusList != null) {
          int maxACType = 0;

          for (CarServiceStatusModel statusModel
              in item.serviceModel.serviceStatusList!) {
            int acType =
                int.tryParse(statusModel.serviceStatusAction ?? '0') ?? 0;

            if (acType > maxACType) {
              maxACType = acType;
            }
          }

          return maxACType;
        }
        break;
      }
    }

    // 如果在_controlSubItemList中没找到，再从_controlItemList中查找
    for (CarControlItemModel item in _controlItemList) {
      if (item.serviceModel.serviceCode == 'acStatus') {
        if (item.serviceModel.serviceStatusList != null) {
          for (CarServiceStatusModel statusModel
              in item.serviceModel.serviceStatusList!) {
            if (item.status == statusModel.serviceStatusValue) {
              int acType =
                  int.tryParse(statusModel.serviceStatusAction ?? '0') ?? 0;

              return acType;
            }
          }
        }
        break;
      }
    }

    // 默认返回0（关闭状态）
    return 0;
  }

  // <editor-fold desc="Car Control Actions">
  void handleControlActionWithItem(
      {CarControlItemModel? carControlItemModel,
      CarServiceModel? serviceModel}) {
    if (carControlItemModel != null) {
      int index = carControlItemModel.index;
      String serviceName = carControlItemModel.serviceModel.serviceName;
      CarServiceStatusModel? currentStatus =
          carControlItemModel.currentStatus();
      log.info(
        '点击了第${index}个车控按钮:${serviceName} 当前状态:${currentStatus != null ? currentStatus.serviceStatusName : '未知状态'}',
      );
      CarServiceModel serviceModel = carControlItemModel.serviceModel;
      int skipTypeCode = int.parse(serviceModel.serviceSkipType);
      UnifiedSkipType? skipType =
          UnifiedSkipTypeExtension.fromInt(skipTypeCode);

      //智慧泊车单独处理
      if (serviceModel.serviceCode.contains('SmartParking')) {
        _shouldShowRemoteParkBottomSheet(carControlItemModel);
        return;
      }

      if (skipType != null) {
        switch (skipType) {
          case UnifiedSkipType.H5:
            log.info('处理 H5 类型');
            jumpToWebViewPageWithServiceModel(
                carControlItemModel.serviceModel, false);
            break;
          case UnifiedSkipType.NBJOrigin:
            log.info('处理 NbjOrigin 类型');
            jumpToNativePage(
                serviceModel, carControlItemModel.serviceModel.serviceCode);
            break;
          case UnifiedSkipType.LLBOrigin:
            log.info('处理 LlbOrigin 类型');
            if (carControlItemModel.serviceModel.serviceCode ==
                ServiceConstant.jumpToMap) {
              String carName = _carInfoModel?.carName ??
                  _carInfoModel?.carTypeName ??
                  '我的爱车';
              jumpToMap(
                  carName: carName,
                  carLatitude: _statusModel?.latitude ?? '',
                  carLongitude: _statusModel?.longitude ?? '');
            } else {
              jumpToNativePage(
                  serviceModel, carControlItemModel.serviceModel.serviceCode);
            }
            break;
          case UnifiedSkipType.NBJTopic:
            log.info('处理 NbjTopic 类型');
            break;
          case UnifiedSkipType.LLBTopic:
            {
              try {
                int postId = int.parse(serviceModel.serviceBleSkipTarget);
                JumpTool().jumpToPostDetail(
                    context: context,
                    postId: postId,
                    postTypeId: PostType.normal,
                    referrerPage: serviceModel.serviceName);
              } catch (e) {
                LogManager().debug('[车型互动]======错误：$e');
              }
            }
            break;
          case UnifiedSkipType.CarControl:
          case UnifiedSkipType.AsyncCarControl:
            log.info(
                '处理 车控 类型 ${currentStatus?.serviceStatusAction} , serviceCode =${carControlItemModel.serviceModel.serviceCode} , acStatus = ${_statusModel?.acStatus}');
            // doCarControlAction(controlItemModel: carControlItemModel, statusModel: currentStatus);
            if (carControlItemModel.serviceModel.serviceCode == 'acStatus' &&
                _statusModel?.acStatus != '0' &&
                (currentStatus?.serviceStatusAction == '3' ||
                    currentStatus?.serviceStatusAction == '5')) {
              // 获取空调类型
              int acType = _getACType();
              log.info(
                  '【UseCarPage】空调类型检查 - acType: $acType, serviceStatusAction: ${currentStatus?.serviceStatusAction}');

              if (acType == 3) {
                // acType=3的电动空调：开启时点击应该打开弹窗，而不是直接关闭
                log.info('【UseCarPage】✅ acType=3电动空调开启状态，打开弹窗而不是直接关闭');
                handleCarControlAction(
                    controlItemModel: carControlItemModel,
                    statusModel: currentStatus);
              } else {
                // 其他类型的档位空调：开启时直接关闭空调
                log.info('【UseCarPage】其他类型档位空调开启状态，直接关闭空调');
                log.info(
                    '处理 车控 类型 --> 若当前是档位空调，并且空调处于打开状态，直接调用接口关闭空调，不再弹窗,直接关闭空调');
                doCarControlNetwork(
                    controlItemModel: carControlItemModel,
                    statusModel: currentStatus,
                    serviceStatusAction:
                        int.parse(currentStatus?.serviceStatusAction ?? '0'),
                    accParameter: '');
              }
            } else {
              handleCarControlAction(
                  controlItemModel: carControlItemModel,
                  statusModel: currentStatus);
            }
            break;
          case UnifiedSkipType.SecondLevelCarControl:
            log.info('处理 SecondLevelCarControl 类型');
            break;
          case UnifiedSkipType.HasNavH5:
            log.info('处理 HasNavH5 类型');
            jumpToWebViewPageWithServiceModel(
                carControlItemModel.serviceModel, true);
            break;
          case UnifiedSkipType.PopHud:
            log.info('处理 PopHud 类型');
            break;
        }
      } else {
        log.info('无效的 SkipTypeCode: $skipTypeCode');
      }
    }
  }

  void handleControlActionWithStatus(
      {CarControlItemModel? controlItemModel,
      CarServiceStatusModel? statusModel,
      CarServiceModel? serviceModel}) {
    if (controlItemModel != null && statusModel != null) {
      CarServiceModel serviceModel = controlItemModel.serviceModel;
      int skipTypeCode = int.parse(statusModel.serviceStatusSkipType);
      UnifiedSkipType? skipType =
          UnifiedSkipTypeExtension.fromInt(skipTypeCode);

      if (skipType != null) {
        switch (skipType) {
          case UnifiedSkipType.H5:
            log.info('处理 H5 类型');
            jumpToWebViewPageWithServiceModel(
                controlItemModel.serviceModel, false);
            break;
          case UnifiedSkipType.NBJOrigin:
            log.info('处理 NbjOrigin 类型');
            jumpToNativePage(serviceModel, statusModel.serviceStatusSkipTarget);
            break;
          case UnifiedSkipType.LLBOrigin:
            log.info('处理 LlbOrigin 类型');
            break;
          case UnifiedSkipType.NBJTopic:
            log.info('处理 NbjTopic 类型');
            break;
          case UnifiedSkipType.LLBTopic:
            {
              try {
                int postId = int.parse(serviceModel.serviceBleSkipTarget);
                JumpTool().jumpToPostDetail(
                    context: context,
                    postId: postId,
                    postTypeId: PostType.normal,
                    referrerPage: serviceModel.serviceName);
              } catch (e) {
                LogManager().debug('[车型互动]======错误：$e');
              }
            }
            break;
          case UnifiedSkipType.CarControl:
          case UnifiedSkipType.AsyncCarControl:
            doCarControlAction(
                controlItemModel: controlItemModel, statusModel: statusModel);
            break;
          case UnifiedSkipType.SecondLevelCarControl:
            log.info('处理 SecondLevelCarControl 类型');
            if (serviceModel.serviceCode == 'CarSearch') {
              doCarControlAction(
                  controlItemModel: controlItemModel, statusModel: statusModel);
            }
            break;
          case UnifiedSkipType.HasNavH5:
            log.info('处理 HasNavH5 类型');
            jumpToWebViewPageWithServiceModel(
                controlItemModel.serviceModel, true);
            break;
          case UnifiedSkipType.PopHud:
            log.info('处理 PopHud 类型');
            break;
        }
      } else {
        log.info('无效的 SkipTypeCode: $skipTypeCode');
      }
    }
  }

  void handleCarControlAction(
      {CarControlItemModel? controlItemModel,
      CarServiceStatusModel? statusModel}) {
    CarServiceModel? serviceModel = controlItemModel?.serviceModel;
    if (_carInfoModel?.bleType == 2 &&
        _currentBleStatus == BleStatus.bleAuthorized &&
        !StrUtil.isNullOrEmpty((serviceModel?.serviceBleSkipTarget ?? ''))) {
      doCarBleControlAction(
          controlItemModel: controlItemModel, statusModel: statusModel);
    } else {
      doCarControlAction(
          controlItemModel: controlItemModel, statusModel: statusModel);
    }
  }

  void handleCarControlActionNew(
      {CarControlItemModel? controlItemModel,
      CarServiceStatusModel? statusModel}) {
    CarServiceModel? serviceModel = controlItemModel?.serviceModel;
    if (_carInfoModel?.bleType == 2 &&
        _currentBleStatus == BleStatus.bleAuthorized &&
        (serviceModel?.serviceBleSkipTarget ?? '').isNotEmpty) {
      doCarBleControlAction(
          controlItemModel: controlItemModel, statusModel: statusModel);
    } else {
      if (controlItemModel == null || statusModel == null) {
        return;
      }
      doCarControlNetwork(
          controlItemModel: controlItemModel,
          statusModel: statusModel,
          serviceStatusAction: 0,
          accParameter: '');
    }
  }

  void doCarBleControlAction(
      {CarControlItemModel? controlItemModel,
      CarServiceStatusModel? statusModel}) {
    if (controlItemModel == null || statusModel == null) {
      return;
    }
    CarServiceModel? serviceModel = controlItemModel?.serviceModel;
    if ((serviceModel?.serviceBleSkipTarget ?? '').isNotEmpty) {
      doBleCarControl(
          actionStr: '操作',
          controlItemModel: controlItemModel,
          statusModel: statusModel);
    }
  }

  void doCarControlAction(
      {CarControlItemModel? controlItemModel,
      CarServiceStatusModel? statusModel}) {
    if (controlItemModel == null || statusModel == null) {
      return;
    }
    String serviceStatusAction = statusModel.serviceStatusAction;
    if (int.parse(serviceStatusAction) == 0) {
      //如果是直接调用接口类型
      doCarControlNetwork(
          controlItemModel: controlItemModel,
          statusModel: statusModel,
          serviceStatusAction: 0,
          accParameter: '');
    } else {
      currentAccType = int.parse(serviceStatusAction);
      if (int.parse(serviceStatusAction) == 2 ||
          int.parse(serviceStatusAction) == 3 ||
          int.parse(serviceStatusAction) == 4 ||
          int.parse(serviceStatusAction) == 5) {
        // List<CarServiceStatusModel> statusList = CarControlItemModel.getStatusListByLevel(serviceModel: controlItemModel.serviceModel, level: serviceStatusAction);
        _isAcSettingDialogVisible = true;

        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          enableDrag: false, // 禁用拖动关闭
          backgroundColor: Colors.transparent,
          builder: (BuildContext context) {
            return AcSettingDialog(
              carControlItemModel: controlItemModel,
              carStatusModel: _statusModel,
              isCarOwner: _carInfoModel?.relation == 1,
              keyStatus: _statusModel?.keyStatus == '2',
              interiorTemperature: _statusModel?.interiorTemperature,
              serviceStatusAction: int.parse(serviceStatusAction),
              onControlButtonClicked: doAcAction,
              onSeatBtnClicked: doSeatControlNetwork,
            );
          },
        ).then((_) {
          _isAcSettingDialogVisible = false;
          // 弹窗关闭时刷新空调控件的显示值
          _refreshACControlWidget();
        });
      } else {
        List<CarServiceStatusModel> statusList =
            CarControlItemModel.getStatusListByLevel(
                serviceModel: controlItemModel.serviceModel,
                level: serviceStatusAction);
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          enableDrag: false, // 禁用拖动关闭
          backgroundColor: Colors.transparent,
          builder: (BuildContext context) {
            return NormalAcSettingDialog(
              carControlItemModel: controlItemModel,
              onControlButtonClicked: handleCarControlAction,
              onControlButtonClickedNew: handleCarControlActionNew,
              isNewAir: int.parse(serviceStatusAction) == 3 ||
                  int.parse(serviceStatusAction) == 5,
            );
          },
        );
      }
    }
  }

  // 刷新空调控件的显示值
  void _refreshACControlWidget() {
    setState(() {
      // 触发重新构建，让空调控件重新从SharedPreferences读取最新值
    });
  }

  void doAcAction(
      {required CarControlItemModel? controlItemModel,
      required CarServiceStatusModel? statusModel,
      required AcButtonActionType? actionType,
      required int serviceStatusAction,
      required String? accParameter}) {
    if (controlItemModel == null || statusModel == null || actionType == null) {
      return;
    }
    if (actionType == AcButtonActionType.cancel) {
      return;
    } else if (actionType == AcButtonActionType.turnOnAppointment) {
      JumpTool().jumpToNativePageWithServiceCode(context, 'AcAppointment');
    } else {
      doCarControlNetwork(
          controlItemModel: controlItemModel,
          statusModel: statusModel,
          serviceStatusAction: serviceStatusAction,
          accParameter: accParameter);
    }
  }

  void doDirectAcAction(
      {required CarControlItemModel controlItemModel,
      required CarServiceStatusModel? statusModel}) {
    if (statusModel == null) {
      return;
    }

    int serviceStatusAction =
        int.tryParse(statusModel.serviceStatusAction) ?? 0;

    // 普通空调关闭时需要特殊处理参数
    if (statusModel.serviceStatusValue == '0' && serviceStatusAction == 1) {
      // 普通空调关闭，直接调用doCarControlNetwork，传递正确的参数格式
      LogManager().info('【doDirectAcAction】普通空调关闭，直接调用doCarControlNetwork');

      // 构建包含vin的正确参数格式
      String vin = _carInfoModel?.vin ?? '';
      String accParameter =
          '{"accOnOff":"0","vin":"$vin","buttonLayout":"1","status":"0"}';
      LogManager().info('【doDirectAcAction】构建的accParameter: $accParameter');

      doCarControlNetwork(
        controlItemModel: controlItemModel,
        statusModel: statusModel,
        serviceStatusAction: serviceStatusAction,
        accParameter: accParameter,
      );
    } else {
      // 其他情况使用原有逻辑
      String accParameter = statusModel.serviceStatusValue == '0'
          ? ''
          : statusModel.serviceStatusValue;
      doAcAction(
        controlItemModel: controlItemModel,
        statusModel: statusModel,
        actionType: AcButtonActionType.switchTurnStatus,
        serviceStatusAction: serviceStatusAction,
        accParameter: accParameter,
      );
    }
  }

  void doCarControlNetwork(
      {required CarControlItemModel controlItemModel,
      required CarServiceStatusModel? statusModel,
      required int serviceStatusAction,
      required String? accParameter}) async {
    currentAccType = serviceStatusAction;
    LoadingManager.show(status: '车控中...', maskType: EasyLoadingMaskType.black);
    Map<String, dynamic> params =
        controlItemModel.getCarServiceParamListForStatus(
            statusModel: statusModel,
            insideParamsMap: GlobalData().insideParamsMap);

    // 普通空调关闭时，确保params包含完整的参数
    if (controlItemModel.serviceModel.serviceCode == 'acStatus' &&
        statusModel?.serviceStatusValue == '0' &&
        serviceStatusAction == 1 &&
        accParameter != null &&
        accParameter!.isNotEmpty) {
      try {
        // 解析accParameter并合并到params中
        Map<String, dynamic> accParams = json.decode(accParameter!);
        params.addAll(accParams);
        LogManager().info('【网络请求】普通空调关闭，合并accParameter到params: $params');
      } catch (e) {
        LogManager().info('【网络请求】解析accParameter失败: $e');
      }
    }
    if (serviceStatusAction == 4) {
      if ("3" == accParameter) {
        //快速升温
        params.addAll({'acType': 4, 'quickUpTemperature': 1});
        _isSeat1HotStatus = true;
      } else if ("4" == accParameter) {
        //快速降温
        params.addAll({'acType': 4, 'quickDownTemperature': 1});
        _isSeat1WindStatus = true;
      }
    }

    String url =
        'junApi${controlItemModel.getCarServiceSkipTargetUrlForStatus(statusModel: statusModel, insideParamsMap: GlobalData().insideParamsMap)}';
    try {
      String serviceSkipType = controlItemModel.serviceModel.serviceSkipType;
      String serviceCode = controlItemModel.serviceModel.serviceCode;
      if (serviceCode == 'acStatus') {
        AC_TEMPERATURE = params['temperature'] ?? '';
        IS_AC_HOT_WIND_SETTING = false;
        String accOnOff = params['accOnOff'] ?? '';
        if (accOnOff == '0') {
          //如果下发0的指令，代表空调处于开启中，本地空调状态就用3来表示空调开启中
          _currentACStatus = '3';
        } else {
          //如果下发非0的指令，代表空调处于关闭中，本地空调状态就用3来表示空调开启中
          _currentACStatus = '0';
        }
      }
      int skipTypeCode = int.parse(serviceSkipType);
      UnifiedSkipType? skipType =
          UnifiedSkipTypeExtension.fromInt(skipTypeCode);
      String backStatusValue = '';
      if (skipType == UnifiedSkipType.CarControl &&
          statusModel?.serviceStatusResultType == '0') {
        CarServiceStatusModel? currentStatus = controlItemModel.currentStatus();
        backStatusValue = currentStatus!.backStatusValue;
        if (serviceCode == 'acStatus') {
          _currentControlServiceCode = '';
        }
      } else {
        if (statusModel?.serviceStatusResultType == '1' &&
            controlItemModel.serviceModel.serviceCode == 'acStatus') {
          //先处理空调的异步结果推送
          backStatusValue = statusModel!.backStatusValue;
        }
      }

      // 为空调外面开关按钮设置backStatusValue：根据当前档位判断制暖/制冷
      if (controlItemModel.serviceModel.serviceCode == 'acStatus' &&
          serviceStatusAction > 1) {
        // 获取当前档位参数
        String? currentGearStr;
        if (params.containsKey('temperature')) {
          currentGearStr = params['temperature']?.toString();
        }

        if (currentGearStr != null) {
          double currentGear = double.tryParse(currentGearStr) ?? 0;
          if (currentGear > 3) {
            backStatusValue = '2'; // 制暖
          } else {
            backStatusValue = '1'; // 制冷
          }
        }
      }

      LogManager().info(
          "开始车控，serviceStatusResultType: ${statusModel?.serviceStatusResultType} ，backStatusValue：$backStatusValue , serviceCode: ${controlItemModel.serviceModel.serviceCode} , mAccType:$serviceStatusAction , 车控accParameter:$accParameter ,车控params:$params");
      //重置空调状态
      CarControlResponseModel carControlResponseModel =
          await carAPI.requestCarControlServiceWithURLStr(url, params);
      //调用成功，更新状态模型中对应的状态数据为成功后的状态值
      // LoadingManager.dismiss();
      if (skipType == UnifiedSkipType.CarControl &&
          statusModel?.serviceStatusResultType == '0') {
        _MQTTBackStatus = "0";
        IS_MQTT_CAR_CONTROL = false;
        LoadingManager.showSuccess("操作成功");
        setState(() {
          Map<String, dynamic> statusMap = _statusModel!.toJson();
          statusMap[controlItemModel.serviceModel.serviceCode] =
              backStatusValue;
          CarStatusModel newStatus = CarStatusModel.fromJson(statusMap);
          checkAcSettingDialogWithStatus(
              oldStatus: _statusModel, newStatus: newStatus);
          _statusModel = CarStatusModel.fromJson(statusMap);
          updateCtrlItemList();
          updateCtrlSubItemList();
          if (_carInfoModel?.vin?.isNotEmpty ?? false) {
            //保存车辆状态采集时间
            CarStatusChecker().saveCarStatusTimeWithTimeStr(
                carControlResponseModel.collectTime ?? '', _carInfoModel!.vin!);
          }
          onCarControlFinish();
        });
      } else {
        if (statusModel?.serviceStatusResultType == '1' &&
            serviceCode == 'acStatus') {
          _MQTTBackStatus = "1";
          IS_MQTT_CAR_CONTROL = true;
          _backStatus = backStatusValue ?? '';
          _currentControlServiceCode = serviceCode;
          _currentServiceCode = serviceCode;
          MQTTManager.shared.startMqttTimeOutTimer();
        } else {
          _MQTTBackStatus = "0";
          IS_MQTT_CAR_CONTROL = false;
          LoadingManager.showSuccess("操作成功");
          if (_isAcSettingDialogVisible) {
            NotificationManager().postNotification(
                Constant.NOTIFICATION_SLIDER_CHANGE_SEND_INSTRUCT_RESULT,
                userInfo: {'success': true});
          }
        }
      }
    } catch (e) {
      onError(e as Exception);
    }
  }

  void onSuccessForCarControlResponse(
      {required CarControlResponseModel carControlResponseModel}) {}
  void onError(Exception exception) {
    log.info(exception.toString());
    if (_isAcSettingDialogVisible) {
      NotificationManager().postNotification(
          Constant.NOTIFICATION_SLIDER_CHANGE_SEND_INSTRUCT_RESULT,
          userInfo: {'success': false});
    }

    // 根据网络状态显示友好的错误提示
    String errorMessage = '啊哦，网络似乎出了点问题…';
    if (!_isDisconnected) {
      // 有网络但操作失败
      errorMessage = '操作失败，请稍后重试';
    }

    LoadingManager.showError(errorMessage);
  }

  void doSeatControlNetwork(
      {required Map<String, dynamic> paramJsonMap,
      required String serviceCode}) async {
    LoadingManager.show(status: '车控中...', maskType: EasyLoadingMaskType.black);
    IS_AC_HOT_WIND_SETTING = true;
    String url = '';
    if (serviceCode.contains(SEAT1_WIND_STATUS) ||
        serviceCode.contains(SEAT2_WIND_STATUS) ||
        serviceCode.contains(SEAT3_WIND_STATUS) ||
        serviceCode.contains(SEAT4_WIND_STATUS)) {
      url = 'junApi/sgmw/car/control/seat/ventilation';
    } else if (serviceCode.contains(SEAT1_HOT_STATUS) ||
        serviceCode.contains(SEAT2_HOT_STATUS) ||
        serviceCode.contains(SEAT3_HOT_STATUS) ||
        serviceCode.contains(SEAT4_HOT_STATUS)) {
      url = 'junApi/sgmw/car/control/seat/heat';
    }
    _workLevel = paramJsonMap['workLevel'].toString();
    IS_MQTT_CAR_CONTROL = false;
    LogManager()
        .info("开始车控，paramJsonMap: $paramJsonMap ， serviceCode：$serviceCode");
    try {
      CarControlResponseModel carControlResponseModel =
          await carAPI.requestCarControlServiceWithURLStr(url, paramJsonMap);
      //调用成功，更新状态模型中对应的状态数据为成功后的状态值
      LogManager().debug(
          "车控Message:${carControlResponseModel.message} ， _workLevel：$_workLevel");
      setState(() {
        if (_workLevel == "7") {
          Map<String, dynamic> statusMap = _statusModel!.toJson();
          LoadingManager.showSuccess("操作成功");
          IS_AC_HOT_WIND_SETTING = false;
          LogManager().debug(
              '车控[CAR_STATUS]===$serviceCode的值从${statusMap[serviceCode]}更新为${paramJsonMap['workLevel']}');
          statusMap[serviceCode] = paramJsonMap['workLevel'];
          _statusModel = CarStatusModel.fromJson(statusMap);
          checkSeatSettingDialogWithStatus(
              statusModel: CarStatusModel.fromJson(statusMap));

          if (_carInfoModel?.vin?.isNotEmpty ?? false) {
            LogManager().debug(
                "车控保存车辆状态采集时间collectTime:${carControlResponseModel.collectTime}");
            //保存车辆状态采集时间
            CarStatusChecker().saveCarStatusTimeWithTimeStr(
                carControlResponseModel.collectTime ?? '', _carInfoModel!.vin!);
          }
        } else {
          IS_MQTT_CAR_CONTROL = true;
          _currentServiceCode = serviceCode;
          if (serviceCode.contains(SEAT1_WIND_STATUS) ||
              serviceCode.contains(SEAT2_WIND_STATUS) ||
              serviceCode.contains(SEAT3_WIND_STATUS) ||
              serviceCode.contains(SEAT4_WIND_STATUS)) {
            _MQTTBackStatus = "1";
            _backStatus = "1";
            _windCode = serviceCode;
            _seatCode = serviceCode;
            _currentControlServiceCode = "seatWindStatus";
          } else if (serviceCode.contains(SEAT1_HOT_STATUS) ||
              serviceCode.contains(SEAT2_HOT_STATUS) ||
              serviceCode.contains(SEAT3_HOT_STATUS) ||
              serviceCode.contains(SEAT4_HOT_STATUS)) {
            _MQTTBackStatus = "1";
            _backStatus = "1";
            _hotCode = serviceCode;
            _seatCode = serviceCode;
            _currentControlServiceCode = "seatHotStatus";
          }
          MQTTManager.shared.startMqttTimeOutTimer();
        }
      });
    } catch (e) {
      onError(e as Exception);
      IS_AC_HOT_WIND_SETTING = false;
    }
  }

  void handleOperActionWithServiceModel({CarServiceModel? serviceModel}) {
    if (serviceModel != null) {
      int skipTypeCode = int.parse(serviceModel.serviceSkipType);
      UnifiedSkipType? skipType =
          UnifiedSkipTypeExtension.fromInt(skipTypeCode);

      if (serviceModel.serviceCode == ServiceConstant.jumpToMore) {
        //更多按钮单独处理
        JumpTool().jumpToWulingAllCarService(context, _useCarServiceList);
        return;
      }

      // 网上展厅按钮特殊处理
      if (serviceModel.serviceCode == "showroom") {
        log.info('点击了网上展厅按钮');
        // 切换到网上展厅页面（主页面视图）
        setState(() {
          _needShowCarControlPage = false;
          _refreshController.requestRefresh();
        });
        return;
      }

      if (skipType != null) {
        switch (skipType) {
          case UnifiedSkipType.H5:
            log.info('处理 H5 类型');
            jumpToWebViewPageWithServiceModel(serviceModel, false);
            break;
          case UnifiedSkipType.NBJOrigin:
            log.info('处理 NbjOrigin 类型');
            jumpToNativePage(serviceModel, serviceModel.serviceCode);
            break;
          case UnifiedSkipType.HasNavH5:
            log.info('处理 HasNavH5 类型');
            jumpToWebViewPageWithServiceModel(serviceModel, true);
            break;
          case UnifiedSkipType.CarControl:
            // TODO: Handle this case.
            break;
          case UnifiedSkipType.LLBOrigin:
            jumpToNativePage(serviceModel, serviceModel.serviceCode);
            break;
          case UnifiedSkipType.NBJTopic:
            // TODO: Handle this case.
            break;
          case UnifiedSkipType.LLBTopic:
            {
              try {
                int postId = int.parse(serviceModel.serviceSkipTarget);
                JumpTool().jumpToPostDetail(
                    context: context,
                    postId: postId,
                    postTypeId: PostType.normal,
                    referrerPage: serviceModel.serviceName);
              } catch (e) {
                LogManager().debug('[车型互动]======错误：$e');
              }
            }
            break;
          case UnifiedSkipType.AsyncCarControl:
            // TODO: Handle this case.
            break;
          case UnifiedSkipType.SecondLevelCarControl:
            // TODO: Handle this case.
            break;
          case UnifiedSkipType.PopHud:
            // TODO: Handle this case.
            break;
        }
      } else {
        log.info('无效的 SkipTypeCode: $skipTypeCode');
      }
    }
  }

  handleUseCarServiceWithUserHandleModel(
      {required UserHandleModel userHandleModel}) {
    // 检查是否在离线模式下
    if (_isOfflineMode) {
      _showOfflineModeDialog();
      return;
    }

    if (userHandleModel?.needLogin == 1) {
      if (!GlobalData().isLogin) {
        showNotLoginAlertDialog(context);
        return;
      }
    }
    JumpTool().jumpToHandlePage(context, handleModel: userHandleModel);
  }

  // 显示离线模式提示弹窗
  void _showOfflineModeDialog() {
    DialogManager().showSingleButtonDialog(
      '', // 不显示标题
      '当前APP处于【离线模式】，暂不支持该功能',
      '我知道了',
      () {
        // 点击我知道了按钮的回调
      },
    );
  }

  void jumpToWebViewPageWithServiceModel(
      CarServiceModel serviceModel, bool needTitleBar) {
    String url = CarControlItemModel.getCarServiceSkipTargetUrlForServiceModel(
        serviceModel: serviceModel,
        insideParamsMap: GlobalData().insideParamsMap);
    jumpToWebViewPage(url, needTitleBar);
  }

  void jumpToWebViewPage(String url, bool needTitleBar) {
    JumpTool().openWeb(context, url, needTitleBar);
  }

  void jumpToNativePage(CarServiceModel? carServiceModel, String serviceCode) {
    // 旧新能源车型使用旧版页面
    if ((_carInfoModel?.controlView ?? 0) == 2) {
      JumpTool().jumpToOlderEVNativePageWithServiceCode(context, serviceCode);
      return;
    }
    Map<String, dynamic>? extraData = {};
    if (serviceCode == 'CarHealth') {
      extraData['needCheckTire'] = _isNeedCheckTire;
    }
    if (serviceCode == ServiceConstant.jumpToCycleOrderCharging) {
      extraData['carServiceModel'] = carServiceModel;
      extraData['carInfoModel'] = _carInfoModel;
      extraData['statusModel'] = _statusModel;
    }
    JumpTool().jumpToNativePageWithServiceCode(context, serviceCode,
        extraData: extraData);
  }

  void _onButtonTab() {
    if (!GlobalData().isLogin) {
      LoginManager().showLoginModal();
    } else {
      NotificationManager().postNotification(
          Constant.NOTIFICATION_MAIN_PAGE_SELECT_PAGE,
          userInfo: {'pageIndex': 1});
    }
  }

  void jumpToMap(
      {required String carName,
      required String carLatitude,
      required String carLongitude}) async {
    if (carLatitude != "null" &&
        carLongitude != "null" &&
        carLatitude.isNotEmpty &&
        carLongitude.isNotEmpty) {
      try {
        LoadingManager.show();
        String address = await LocationManager.getAddressFromLocation(
          carLatitude,
          carLongitude,
        );
        // 格式化地址：处理门牌号中的点号分隔问题
        String formattedAddress = _formatAddress(address);
        LoadingManager.dismiss().then((value) => {
              LocationManager.jumpMap(
                  carName, formattedAddress, carLatitude, carLongitude)
            });
      } catch (e) {
        LoadingManager.showInfo("暂时无法获取车况位置信息");
      }
    } else {
      LoadingManager.showInfo("暂时无法获取车况位置信息");
    }
  }
  // </editor-fold>

  // <editor-fold desc="Data Handle Methods">
  /// 创建车控按钮列表
  void buildCtrlItemList(List<CarServiceModel>? ctrlServiceList) {
    _controlItemList.clear();
    _controlSubItemList.clear();
    _toolsEditedList.clear();

    List<CarServiceModel> tempCtlArr =
        buildSubCtrlItemArrayWithCtrlArray(ctrlServiceList!);

    if (tempCtlArr.isNotEmpty) {
      for (var service in tempCtlArr) {}
    }

    if (tempCtlArr != null) {
      for (int i = 0; i < tempCtlArr.length; i++) {
        CarServiceModel serviceModel = tempCtlArr[i];
        String currentStatusValue = '0';
        if (serviceModel.serviceStatusList?.isNotEmpty ?? false) {
          if (serviceModel.serviceStatusList!.length == 1) {
            //只有一个状态则只取唯一一个状态的状态值作为初始值
            CarServiceStatusModel statusModel =
                serviceModel.serviceStatusList![0];
            currentStatusValue = statusModel.serviceStatusValue;
          } else {
            //有多个状态，则取'0'状态作为状态值
            currentStatusValue = '0';
          }
        }
        CarControlItemModel carControlItemModel = CarControlItemModel(
            serviceModel: serviceModel,
            index: i,
            status: currentStatusValue,
            isControling: false);

        _controlItemList.add(carControlItemModel);
      }
    } else {}
  }

  List<CarServiceModel> buildSubCtrlItemArrayWithCtrlArray(
      List<CarServiceModel> serviceArray) {
    List<CarServiceModel> tempCtlArr = serviceArray.toList();
    List<CarServiceModel> tempToolArr = _toolServiceList.toList();
    List<CarServiceModel> tempNewUIArr = _newuiServiceArray.toList();
    List<CarControlItemModel> tempSubItemList = [];

    if (_newuiServiceArray.isNotEmpty) {
      List<CarServiceModel> acArr =
          checkServiceControlArrHasAcControl(_newuiServiceArray);
      CarServiceModel? acModel;
      bool hasACButtonLayout = false;
      if (acArr.isNotEmpty) {
        for (int i = 0; i < acArr.length; i++) {
          CarServiceModel tempAc = acArr[i];
          if (CarServiceUtil.getButtonLayoutTypeWithCarServiceModel(tempAc) ==
              ServiceButtonLayoutType.ac) {
            hasACButtonLayout = true;
            acModel = tempAc;
            break;
          }
        }
        //删除掉空调model
        Set<CarServiceModel> acSet = Set.from(acArr);
        tempNewUIArr.removeWhere((element) => acSet.contains(element));
      }
      if (hasACButtonLayout && acModel != null) {
        //有空调直接取空调
        String currentStatusValue = '0';

        //取第一个状态值作为初始状态
        if (acModel.serviceStatusList?.isNotEmpty ?? false) {
          if (acModel.serviceStatusList!.length == 1) {
            CarServiceStatusModel statusModel =
                acModel.serviceStatusList!.first;
            currentStatusValue = statusModel.serviceStatusValue;
          } else {
            currentStatusValue = '0';
          }
        }
        CarControlItemModel acItemModel = CarControlItemModel(
            serviceModel: acModel,
            status: currentStatusValue,
            index: 0,
            isControling: false);
        tempSubItemList.add(acItemModel);

        CarServiceStatusModel? acTypeStatus =
            CarServiceUtil.getCurrentStatusModelWithCarStatusValue(
                acModel, '0');
        if (acTypeStatus != null && acTypeStatus.serviceStatusAction == "4") {
          //空调4再添加一次，座椅通风加热使用
          tempSubItemList.add(acItemModel);
        }

        //删除在车控列表里的空调
        checkServiceArrayHasModelAndRemoveModelWithCtrlModel(
            tempCtlArr, [], acModel);
      }
      //继续遍历剩余项目
      for (int i = 0; i < tempNewUIArr.length; i++) {
        CarServiceModel sModel = tempNewUIArr[i];
        bool modelhasBtnLayout =
            (CarServiceUtil.getButtonLayoutTypeWithCarServiceModel(sModel) ==
                    ServiceButtonLayoutType.other)
                ? true
                : false;

        if (modelhasBtnLayout) {
          //先删除其他服务数组里面的model
          checkServiceArrayHasModelAndRemoveModelWithCtrlModel(
              tempCtlArr, tempToolArr, sModel);

          //取第一个状态值作为初始状态
          String currentStatusValue = '0';
          if (sModel.serviceStatusList!.isNotEmpty) {
            if (sModel.serviceStatusList!.length == 1) {
              CarServiceStatusModel statusModel =
                  sModel.serviceStatusList!.first;
              currentStatusValue = statusModel.serviceStatusValue;
            } else {
              currentStatusValue = '0';
            }
          }

          CarControlItemModel itemModel = CarControlItemModel(
              serviceModel: sModel,
              status: currentStatusValue,
              index: 0,
              isControling: false);

          if (i == 0 && tempSubItemList.length >= 2) {
            tempSubItemList.insert(1, itemModel);
          } else {
            tempSubItemList.add(itemModel);
          }
        } else {
          continue;
        }
      }

      //重新整理Item的Index

      for (int i = 0; i < tempSubItemList.length; i++) {
        CarControlItemModel tempModel = tempSubItemList[i];

        CarControlItemModel itemModel = CarControlItemModel(
            serviceModel: tempModel.serviceModel,
            index: i,
            status: tempModel.status,
            isControling: tempModel.isControling);
        _controlSubItemList.add(itemModel);
      }

      //[self calculateCellCenterHeightWithHasAC:hasACButtonLayout subCtlArrayCount:self.subCtrlItemArr.count toolsArray:tempToolArr ctlArray:tempCtlArr];
    } else {
      // 当没有新UI服务时，确保原始服务列表被正确处理
      // tempCtlArr已经是serviceArray的副本，保持不变
      //[self calculateCellCenterHeightWithHasAC:NO subCtlArrayCount:0 toolsArray:tempToolArr ctlArray:tempCtlArr];
    }

    //重新赋值到toolServiceArray
    _toolsEditedList = tempToolArr.toList();
    //self.moduleBgView.hidden = _toolsEditedArr.count > 0 ? NO : YES;

    return tempCtlArr;
  }

  List<CarServiceModel> checkServiceControlArrHasAcControl(
      List<CarServiceModel> serviceArr) {
    List<CarServiceModel> tempArr = [];
    for (CarServiceModel model in serviceArr) {
      if (model.serviceCode == "acStatus") {
        tempArr.add(model);
      }
    }
    return tempArr;
  }

  void checkServiceArrayHasModelAndRemoveModelWithCtrlModel(
      List<CarServiceModel> ctrlArr,
      List<CarServiceModel> toolsArr,
      CarServiceModel targetModel) {
    for (CarServiceModel model in ctrlArr) {
      if (model.serviceId == targetModel.serviceId) {
        ctrlArr.remove(model);
        return;
      }
    }
    for (CarServiceModel model in toolsArr) {
      if (model.serviceId == targetModel.serviceId) {
        toolsArr.remove(model);
        return;
      }
    }
  }

  bool getSeatVentilationIsOpen() {
    try {
      int seat1WindStatus;
      int seat2WindStatus;
      int seat3WindStatus;
      int seat4WindStatus;
      try {
        seat1WindStatus = int.parse(_statusModel?.seat1WindStatus ?? '7');
      } on FormatException {
        seat1WindStatus = 7; // 格式错误时的默认值
      } on TypeError {
        seat1WindStatus = 7; // 类型错误时的默认值
      }
      try {
        seat2WindStatus = int.parse(_statusModel?.seat2WindStatus ?? '7');
      } on FormatException {
        seat2WindStatus = 7; // 格式错误时的默认值
      } on TypeError {
        seat2WindStatus = 7; // 类型错误时的默认值
      }
      try {
        seat3WindStatus = int.parse(_statusModel?.seat3WindStatus ?? '7');
      } on FormatException {
        seat3WindStatus = 7; // 格式错误时的默认值
      } on TypeError {
        seat3WindStatus = 7; // 类型错误时的默认值
      }
      try {
        seat4WindStatus = int.parse(_statusModel?.seat4WindStatus ?? '7');
      } on FormatException {
        seat4WindStatus = 7; // 格式错误时的默认值
      } on TypeError {
        seat4WindStatus = 7; // 类型错误时的默认值
      }
      LogManager().log(
          '获取座椅通风状态：seat1WindStatus = $seat1WindStatus ，seat2WindStatus = $seat2WindStatus ，seat3WindStatus = $seat3WindStatus ，seat4WindStatus = $seat4WindStatus');
      if ((seat1WindStatus == 1 ||
              seat1WindStatus == 2 ||
              seat1WindStatus == 3) ||
          (seat2WindStatus == 1 ||
              seat2WindStatus == 2 ||
              seat2WindStatus == 3) ||
          (seat3WindStatus == 1 ||
              seat3WindStatus == 2 ||
              seat3WindStatus == 3) ||
          (seat4WindStatus == 1 ||
              seat4WindStatus == 2 ||
              seat4WindStatus == 3)) {
        //有一个座椅通风是打开的，则座椅通风就是打开的
        return true;
      } else {
        return false;
      }
    } on FormatException catch (e) {
      LogManager().log('座椅通风状态转换异常：$e');
      return false;
    }
  }

  //获取是否有座椅加热打开
  bool getSeatHeatingIsOpen() {
    try {
      int seat1HotStatus;
      int seat2HotStatus;
      int seat3HotStatus;
      int seat4HotStatus;
      try {
        seat1HotStatus = int.parse(_statusModel?.seat1HotStatus ?? '7');
      } on FormatException {
        seat1HotStatus = 7; // 格式错误时的默认值
      } on TypeError {
        seat1HotStatus = 7; // 类型错误时的默认值
      }
      try {
        seat2HotStatus = int.parse(_statusModel?.seat2HotStatus ?? '7');
      } on FormatException {
        seat2HotStatus = 7; // 格式错误时的默认值
      } on TypeError {
        seat2HotStatus = 7; // 类型错误时的默认值
      }
      try {
        seat3HotStatus = int.parse(_statusModel?.seat3HotStatus ?? '7');
      } on FormatException {
        seat3HotStatus = 7; // 格式错误时的默认值
      } on TypeError {
        seat3HotStatus = 7; // 类型错误时的默认值
      }
      try {
        seat4HotStatus = int.parse(_statusModel?.seat4HotStatus ?? '7');
      } on FormatException {
        seat4HotStatus = 7; // 格式错误时的默认值
      } on TypeError {
        seat4HotStatus = 7; // 类型错误时的默认值
      }
      LogManager().log(
          '获取座椅加热状态：seat1HotStatus = $seat1HotStatus ，seat2HotStatus = $seat2HotStatus ，seat3HotStatus = $seat3HotStatus ，seat4HotStatus = $seat4HotStatus');
      if ((seat1HotStatus == 1 || seat1HotStatus == 2 || seat1HotStatus == 3) ||
          (seat2HotStatus == 1 || seat2HotStatus == 2 || seat2HotStatus == 3) ||
          (seat3HotStatus == 1 || seat3HotStatus == 2 || seat3HotStatus == 3) ||
          (seat4HotStatus == 1 || seat4HotStatus == 2 || seat4HotStatus == 3)) {
        //有一个座椅加热是打开的，则座椅加热就是打开的
        return true;
      } else {
        return false;
      }
    } on FormatException catch (e) {
      LogManager().log('座椅加热状态转换异常：$e');
      return false;
    }
  }

  // 根据车辆状态数据更新按钮状态
  void updateCtrlItemList() {
    for (CarControlItemModel carControlItemModel in _controlItemList) {
      if (carControlItemModel.serviceModel.serviceSkipType == '1' ||
          carControlItemModel.serviceModel.serviceSkipType == '6') {
        if (_statusModel != null) {
          Map<String, dynamic> statusMap = _statusModel!.toJson();
          String serviceCode = carControlItemModel.serviceModel.serviceCode;
          if (statusMap.containsKey(serviceCode)) {
            CarServiceStatusModel statusModel =
                carControlItemModel.serviceModel.serviceStatusList![0];
            if (serviceCode == 'acStatus' &&
                int.parse(statusModel.serviceStatusAction) == 4 &&
                (getSeatVentilationIsOpen() || getSeatHeatingIsOpen())) {
              //判断座椅通风加热是否打开，如果打开则显示座椅通风/加热的图标
              //移除空调状态判断，让座椅通风/加热可以独立显示
              if (getSeatVentilationIsOpen()) {
                if (getSeatHeatingIsOpen()) {
                  //座椅通风加热同时打开
                  carControlItemModel.status = '21';
                } else {
                  //座椅通风打开,座椅加热关闭
                  carControlItemModel.status = '19';
                }
              } else {
                if (getSeatHeatingIsOpen()) {
                  //座椅通风关闭，座椅加热打开
                  carControlItemModel.status = '20';
                } else {
                  //座椅通风和座椅加热都关闭
                  carControlItemModel.status = statusMap[serviceCode];
                }
              }
            } else {
              String? currentStatusValue = statusMap[serviceCode];
              if (currentStatusValue != null && currentStatusValue.isNotEmpty) {
                if (carControlItemModel.containsStatus(currentStatusValue)) {
                  carControlItemModel.status = currentStatusValue;
                }
              } else {
                if (carControlItemModel
                        .serviceModel.serviceStatusList?.isNotEmpty ??
                    false) {
                  if (carControlItemModel
                          .serviceModel.serviceStatusList!.length ==
                      1) {
                    //只有一个状态则只取唯一一个状态的状态值作为初始值
                    CarServiceStatusModel statusModel =
                        carControlItemModel.serviceModel.serviceStatusList![0];
                    carControlItemModel.status = statusModel.serviceStatusValue;
                  } else {
                    //有多个状态，则取'0'状态作为状态值
                    carControlItemModel.status = '0';
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  void updateCtrlSubItemList() {
    for (CarControlItemModel carControlItemModel in _controlSubItemList) {
      if (carControlItemModel.serviceModel.serviceSkipType == '1' ||
          carControlItemModel.serviceModel.serviceSkipType == '6') {
        if (_statusModel != null) {
          Map<String, dynamic> statusMap = _statusModel!.toJson();
          String serviceCode = carControlItemModel.serviceModel.serviceCode;
          if (statusMap.containsKey(serviceCode)) {
            CarServiceStatusModel statusModel =
                carControlItemModel.serviceModel.serviceStatusList![0];
            if (serviceCode == 'acStatus' &&
                int.parse(statusModel.serviceStatusAction) == 4 &&
                (getSeatVentilationIsOpen() || getSeatHeatingIsOpen())) {
              //判断座椅通风加热是否打开，如果打开则显示座椅通风/加热的图标
              //移除空调状态判断，让座椅通风/加热可以独立显示
              if (getSeatVentilationIsOpen()) {
                if (getSeatHeatingIsOpen()) {
                  //座椅通风加热同时打开
                  carControlItemModel.status = '21';
                } else {
                  //座椅通风打开,座椅加热关闭
                  carControlItemModel.status = '19';
                }
              } else {
                if (getSeatHeatingIsOpen()) {
                  //座椅通风关闭，座椅加热打开
                  carControlItemModel.status = '20';
                } else {
                  //座椅通风和座椅加热都关闭
                  carControlItemModel.status = statusMap[serviceCode];
                }
              }
            } else {
              String? currentStatusValue = statusMap[serviceCode];
              if (currentStatusValue != null && currentStatusValue.isNotEmpty) {
                if (carControlItemModel.containsStatus(currentStatusValue)) {
                  carControlItemModel.status = currentStatusValue;
                }
              } else {
                if (carControlItemModel
                        .serviceModel.serviceStatusList?.isNotEmpty ??
                    false) {
                  if (carControlItemModel
                          .serviceModel.serviceStatusList!.length ==
                      1) {
                    //只有一个状态则只取唯一一个状态的状态值作为初始值
                    CarServiceStatusModel statusModel =
                        carControlItemModel.serviceModel.serviceStatusList![0];
                    carControlItemModel.status = statusModel.serviceStatusValue;
                  } else {
                    //有多个状态，则取'0'状态作为状态值
                    carControlItemModel.status = '0';
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  /// 检查空调设置对话框状态，并根据新旧状态的变化发送通知。
  /// - [oldStatus]：旧的车辆状态模型，可选，包含当前空调状态等信息。
  /// - [newStatus]：新的车辆状态模型，可选，包含最新的空调状态等信息。
  void checkAcSettingDialogWithStatus({
    required CarStatusModel? oldStatus,
    required CarStatusModel? newStatus,
  }) {
    // 检查是否显示空调设置对话框
    if (_isAcSettingDialogVisible) {
      // 获取旧空调状态
      String? lastAcStatus = oldStatus?.acStatus;
      // 获取新空调状态
      String? newAcStatus = newStatus?.acStatus;

      String? lastSeatStatus = oldStatus?.seat1HotStatus;
      String? newSeatStatus = newStatus?.seat1HotStatus;

      // 打印旧状态和新状态日志
      LogManager().debug("旧空调状态：$lastAcStatus, 新空调状态：$newAcStatus");

      // 比较旧状态和新状态
      if (lastAcStatus != newAcStatus) {
        LogManager().debug("空调状态发生了改变");
        // 如果状态发生变化，发送通知
        NotificationManager().postNotification(
          Constant.NOTIFICATION_AC_STATUS_CHANGED,
          userInfo: {
            'acStatus': newStatus?.acStatus ?? '0', // 新空调状态，默认为 '0'
            'interiorTemperature':
                newStatus?.interiorTemperature ?? '0', // 内部温度，默认为 '0'
          },
        );
      } else {
        // 状态未发生变化
        LogManager().debug("空调状态没有发生改变");
      }
    }
  }

  void checkSeatSettingDialogWithStatus(
      {required CarStatusModel? statusModel}) {
    // 检查是否显示空调设置对话框
    if (_isAcSettingDialogVisible) {
      // 打印旧状态和新状态日志
      NotificationManager().postNotification(
        Constant.NOTIFICATION_SEAT_STATUS_CHANGED,
        userInfo: {"statusModel": statusModel},
      );
    }
  }
  // </editor-fold>

  // <editor-fold desc="Initiate">

  void _scrollListener() {
    if (_scrollController.offset > _scrollThreshold && !_showTopWidget) {
      setState(() => _showTopWidget = true);
    } else if (_scrollController.offset <= _scrollThreshold && _showTopWidget) {
      setState(() => _showTopWidget = false);
    }
  }

  @override
  void pageInitState() {
    double screenHeight = MediaQueryData.fromWindow(window).size.height;
    double safeAreaBottom = MediaQueryData.fromWindow(window).padding.bottom;
    double safeAreaTop = MediaQueryData.fromWindow(window).padding.top;
    double height = screenHeight - safeAreaBottom - 56;

    _topWidgetHeight = safeAreaTop + 52;
    _scrollThreshold = height;
    _scrollController.addListener(_scrollListener);

    setupNotification();

    // 立即加载本地缓存的车辆信息
    String key =
        '${SP_USER_DEFAULT_CAR_KEY}_${GlobalData().userModel?.userIdStr}';
    var carInfoMap = SpUtil().getJSON(key);
    if (carInfoMap != null) {
      setState(() {
        CarInfoModel carInfo = CarInfoModel.fromJson(carInfoMap);
        _carInfoModel = carInfo;
        _isCarUser = true;
        GlobalData().carInfoModel = _carInfoModel;
      });
    }

    // 缓存优先：立即加载所有缓存数据
    _loadAllCacheDataOnInit();

    // 延时执行是因为要等待页面构建完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _subscribeToStatusStream();
      // 只在第一次进入页面时刷新，避免从子页面返回时重复刷新
      if (!_hasInitialRefreshed) {
        _hasInitialRefreshed = true;
        _refreshController.requestRefresh();
      }
    });
  }

  bool showCarControlPage() {
    bool isLogin = GlobalData().isLogin;

    if (isLogin && _isCarUser && _needShowCarControlPage) {
      return true;
    }
    return false;
  }

  List<Widget> getWidgetList() {
    List<Widget> list = [];

    bool shouldShowCarControl = showCarControlPage();

    if (shouldShowCarControl) {
      LogManager()
          .debug('DEBUG:---- controlView: ${_carInfoModel?.controlView}');
      changeStatusBarBrightness(Brightness.dark);
      if (_carInfoModel?.controlView == 2 ||
          _carInfoModel?.controlView == 3 ||
          _carInfoModel?.controlView == 8) {
        list.add(UseCarPageControlHeaderEV(
          currentBleStatus: _currentBleStatus,
          isHandleConnectBle: true,
          carInfoModel: _carInfoModel,
          statusModel: _statusModel,
          controlItemList: _controlItemList,
          controlSubItemList: _controlSubItemList,
          ctrlServiceList: _ctrlServiceList,
          toolServiceList: _toolsEditedList,
          mainModel: _mainModel,
          isNetworkConnected: !_isDisconnected, // 传递网络连接状态
          notifiList: _notifiList,
          isRefresh: _isRefresh,
          onControlButtonClicked: handleControlActionWithItem,
          onDirectACControlButtonClicked: (
              {required CarControlItemModel carControlItemModel,
              required CarServiceStatusModel? statusModel}) {
            // 调用原来的直接空调控制方法
            doDirectAcAction(
                controlItemModel: carControlItemModel,
                statusModel: statusModel);
          },
          onServiceButtonClicked: handleOperActionWithServiceModel,
          onUseCarServiceButtonClicked: handleUseCarServiceWithUserHandleModel,
          onMyCarButtonClicked: () {
            if (_carInfoModel?.hasMoreCar == 1) {
              showSwitchCarDialog(context);
            } else {
              String url = '${Constant.CAR_ADD_CAR}love-car/add';
              JumpTool().openWeb(context, url, false);
            }
          },
          onSwitchButtonClicked: () {
            setState(() {
              _needShowCarControlPage = false;
              _refreshController.requestRefresh();
            });
          },

          onSettingButtonClicked: () {
            JumpTool().openNativePage(context, "1012");
            //LoadingManager.showToast('功能开发中 敬请期待');
          },
          onBleButtonClicked: () {
            // JumpTool().jumpToBleScanPage(context);
            _clickBleButton();
          },
          //车图点击
          onCarImageClicked: () {
            Navigator.of(context).push(CustomCupertinoPageRoute(
              builder: (context) => CarDetailsPage(),
            ));
          },
          useCarServiceList: _useCarServiceList,
          navigationList: _getNavigationList(), // 使用动态生成的导航列表
          onBatteryLayoutClicked: () {
            // 充电地图按钮点击跳转到充电地图页面
            if (GlobalData().isLogin) {
              String url = WebViewURLTool.goChargeMapUrl();
              JumpTool().openWeb(context, url, false);
            } else {
              showNotLoginAlertDialog(context);
            }
          },
          onCarDetailsClicked: () {
            // 电池布局点击跳转到车辆详情页面（和点击车图一样）
            Navigator.of(context).push(CustomCupertinoPageRoute(
              builder: (context) => CarDetailsPage(),
            ));
          },
          showNetworkBanner: _showNetworkBanner,
          isOfflineMode: _isOfflineMode,
          onEnterOfflineModeClicked: () {
            setState(() {
              _isOfflineMode = true;
            });
          },
          onBleControlButtonClicked: (
              {required CarControlItemModel carControlItemModel,
              required CarServiceStatusModel statusModel}) {
            // 离线模式下使用蓝牙控制方法
            LogManager().debug(
                '离线模式：调用蓝牙控制方法 - ${carControlItemModel.serviceModel.serviceCode}');
            doBleCarControl(
              actionStr: '离线操作',
              controlItemModel: carControlItemModel,
              statusModel: statusModel,
            );
          },
          activityList: _activityList,
          miniLifeModel: _miniLifeModel,
          onMiniLifeClicked: (url) {
            if (!StrUtil.isNullEmptyOrWhitespace(url)) {
              jumpToWebViewPage(url ?? '', false);
            }
          },
        ));
      } else {
        list.add(UseCarPageControlHeader(
          currentBleStatus: _currentBleStatus,
          isHandleConnectBle: true,
          carInfoModel: _carInfoModel,
          statusModel: _statusModel,
          controlItemList: _controlItemList,
          ctrlServiceList: _ctrlServiceList,
          toolServiceList: _toolServiceList,
          mainModel: _mainModel,
          notifiList: _notifiList,
          isRefresh: _isRefresh,
          onControlButtonClicked: handleControlActionWithItem,
          onServiceButtonClicked: handleOperActionWithServiceModel,
          onUseCarServiceButtonClicked: handleUseCarServiceWithUserHandleModel,
          onMyCarButtonClicked: () {
            if (_carInfoModel?.hasMoreCar == 1) {
              showSwitchCarDialog(context);
            } else {
              String url = '${Constant.CAR_ADD_CAR}love-car/add';
              JumpTool().openWeb(context, url, false);
            }
          },
          onSwitchButtonClicked: () {
            setState(() {
              _needShowCarControlPage = false;
              _refreshController.requestRefresh();
            });
          },
          onChargeButtonClicked: () {
            if (GlobalData().isLogin) {
              // String url = WebViewURLTool.goChargeMapUrl();
              // JumpTool().openWeb(context, url, true);
              LoadingManager.showToast('功能开发中 敬请期待');
            } else {
              showNotLoginAlertDialog(context);
            }
          },
          onSettingButtonClicked: () {
            JumpTool().openNativePage(context, "1012");
            //LoadingManager.showToast('功能开发中 敬请期待');
          },
          onBleButtonClicked: () {
            // JumpTool().jumpToBleScanPage(context);
            _clickBleButton();
          },
          //车图点击
          onCarImageClicked: () {
            Navigator.of(context).push(CustomCupertinoPageRoute(
              builder: (context) => CarDetailsPage(),
            ));
          },
          useCarServiceList: _useCarServiceList,
        ));
      }
    } else {
      changeStatusBarBrightness(Brightness.light);
      list.add(CarAdvertiseHeader(
        positionModelList: _positionModelList,
        onLeftButtonClicked: ({required Advertise advertise}) {
          // JumpTool().jumpToAdvertisePage(context, linkType: advertise.otherLinkType ?? 0, linkUrl: advertise.otherLinkUrl ?? '', eventPage: '',);
          JumpTool().jumpToAdvertisePage(context,
              linkType: advertise.otherLinkType ?? 0,
              linkUrl: advertise.otherLinkUrl ?? '',
              eventPage: '',
              channelCode: 0,
              urlPre: advertise.advertiseImage ?? '');
        },
        onRightButtonClicked: ({required Advertise advertise}) {
          // JumpTool().jumpToAdvertisePage(context, linkType: advertise.linkType ?? 0, linkUrl: advertise.linkUrl ?? '', eventPage: '',);
          JumpTool().jumpToAdvertisePage(context,
              linkType: advertise.linkType ?? 0,
              linkUrl: advertise.linkUrl ?? '',
              eventPage: '',
              channelCode: 0,
              urlPre: advertise.advertiseImage ?? '');
        },
        onAllCarButtonClicked: () {
          String url = WebViewURLTool.goSGMWCarListURL();
          JumpTool().openWeb(context, url, false);
        },
      ));
      // list.add(DiscountWidget());
      if (_dealerList.isNotEmpty) {
        list.add(SectionHeader(
          sectionTitle: '附近门店',
          needMoreHeight: false,
          showMoreButton: true,
          onMoreButtonPressed: () {
            String linkUrl = WebViewURLTool.carShopURL();
            JumpTool().openWeb(context, linkUrl, false);
          },
        ));
        for (DealerModel dealerModel in _dealerList) {
          list.add(StoreInfoWidget(
            dealerModel: dealerModel,
            onTrialButtonClicked: () {
              String url = WebViewURLTool
                  .newAskPriceOrAppointmentDriveWithPageAskPriceOrAppointmentDrive(
                      PageType.testDrive.value, 0, dealerModel.dealerId);
              JumpTool().openWeb(context, url, false);
            },
            onPhoneButtonClicked: ({String? phoneNum}) {
              if (phoneNum != null && phoneNum.isNotEmpty) {
                PhoneCallManager().callTelWithPhoneNumber(context, phoneNum);
              }
            },
            onChatButtonClicked: () {
              LoadingManager.showToast('功能开发中 敬请期待');
            },
            onStorePostTapped: () {
              String url = WebViewURLTool.my4SDetail(dealerModel.dealerId);
              JumpTool().openWeb(context, url, false);
            },
          ));
        }
      }
      List<PostModel> postList = _columnModel?.posts ?? [];
      if (postList.isNotEmpty) {
        list.add(SectionHeader(
          sectionTitle: '相关资讯',
          needMoreHeight: true,
          showMoreButton: true,
          onMoreButtonPressed: () {
            // 跳转到新的资讯列表页面
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const NewsPage(),
              ),
            );
          },
        ));
        for (PostModel postModel in postList) {
          list.add(NewsWidget(
            postModel: postModel,
            onTap: () {
              JumpTool().jumpToAdvertisePage(
                context,
                linkType: LinkType.post.value,
                linkUrl: postModel.postId.toString(),
                eventPage: '资讯',
              );
              // LoadingManager.showToast('帖子功能开发中 敬请期待');
            },
          ));
        }
      }
      if (_servicesHandleList != null &&
          (_servicesHandleList!.appFunctionIcons ?? []).isNotEmpty) {
        list.add(SectionHeader(
          sectionTitle: '购车服务',
          needMoreHeight: true,
        ));
        list.add(ServiceWidget(
          userHandleListModel: _servicesHandleList,
          onButtonClicked: ({required UserHandleModel? userHandleModel}) {
            if (userHandleModel?.needLogin == 1) {
              if (!GlobalData().isLogin) {
                showNotLoginAlertDialog(context);
                return;
              }
            }
            JumpTool().jumpToHandlePage(context,
                handleModel: userHandleModel, listModel: _servicesHandleList);
          },
        ));
      }

      if (_advertiseList.isNotEmpty) {
        for (Advertise advertise in _advertiseList) {
          list.add(AdvertiseWidget(
            advertise: advertise,
            onTap: () {
              // LoadingManager.showError('地址为空 请重新刷新重试');
              JumpTool().jumpToAdvertisePage(
                context,
                linkType: advertise.linkType ?? 0,
                linkUrl: advertise.linkUrl ?? '',
                eventPage: '',
              );
            },
          ));
        }
      }

      //底部缓冲高度，不然上面广告 会被 遮罩
      list.add(const SizedBox(
        height: 50,
      ));
    }
    return list;
  }

  @override
  Widget buildPageContent(
    BuildContext context,
  ) {
    // 实现UseCarPage特定的页面内容
    double safeAreaTop = MediaQueryData.fromWindow(window).padding.top;
    return Stack(
      children: [
        SmartRefresher(
          controller: _refreshController,
          onRefresh: _onRefresh,
          onLoading: _onLoading,
          header: AnimatedRefreshHeader(paddingTop: 20, headerHeight: 76),
          child: ListView(
            controller: _scrollController,
            children: getWidgetList(),
          ),
        ),
        AnimatedPositioned(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          top: _showTopWidget ? 0 : -_topWidgetHeight,
          left: 0,
          right: 0,
          height: _topWidgetHeight,
          child: Container(
            padding: EdgeInsets.only(top: safeAreaTop),
            color: const Color(0xff384967).withOpacity(0.9),
            child: TopAnimatedWidget(
              onRightButtonClicked: () {
                String url = WebViewURLTool.goSGMWCarListURL();
                JumpTool().openWeb(context, url, false);
              },
              onLeftButtonClicked: () {
                String url = WebViewURLTool
                    .newAskPriceOrAppointmentDriveWithPageAskPriceOrAppointmentDrive(
                        PageType.testDrive.value, 0, 0);
                JumpTool().openWeb(context, url, true);
              },
            ),
          ),
        ),
        if (!showCarControlPage() && _isCarUser)
          Positioned(
            top: safeAreaTop + 50,
            right: 0,
            child: CarOwnerTapWidget(
              onPressed: () {
                setState(() {
                  _needShowCarControlPage = true;
                  _refreshController.requestRefresh();
                });
              },
            ),
          )
      ],
    );
  }

  @override
  void dispose() {
    _statusSubscription.cancel();
    _refreshController?.dispose();
    _scrollController?.dispose();
    _statusSubscription?.cancel();
    _pageNetworkSubscription?.cancel(); // 清理页面级网络监控
    _unsubscribe();
    _removeMQTTSubscription(); // 添加MQTT订阅移除
    super.dispose();
  }

  // 移除MQTT订阅
  void _removeMQTTSubscription() {
    MQTTManager.shared.removeListener(
      tag: 'UseCarPage_carControlAllStatus',
    );
    MQTTManager.shared.removeListener(
      tag: 'UseCarPage_carRemoteAsyncResult',
    );
    MQTTManager.shared.removeReceiveMqttTimeOutListener(
      tag: 'UseCarPage_ReceiveMqttTimeOut',
    );
  }

  _unsubscribe() {
    NotificationManager().unsubscribe(
        Constant.NOTIFICATION_LOGIN_SUCCEED, _receivedLoginSucceedNotification);
    NotificationManager().unsubscribe(Constant.NOTIFICATION_LOGOUT_SUCCEED,
        _receivedLogoutSucceedNotification);
    NotificationManager().unsubscribe(Constant.NOTIFICATION_SWITCH_CAR_SUCCEED,
        _receivedSwitchCarSucceedNotification);
    NotificationManager().unsubscribe(
        Constant.NOTIFICATION_SERVICE_DATA_REFRESH, reGetNotificationData);
  }

  // </editor-fold>

  // <editor-fold desc="Show Alert">

  void showCarNotFoundAlertDialog(BuildContext context) {
    DialogManager().showCustomDialog(
      CustomDialog(
        content: "未搜索到您的爱车，请尽量靠近您的爱车后再重试",
        contentFontSize: 15,
        contentColor: Colors.black,
        buttonHeight: 63,
        horizontalPadding: 20,
        buttons: [
          DialogButton(
              label: "我知道了",
              onPressed: () {
                LogManager().debug("我知道了被点击");
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xFF008DFF)),
        ],
      ),
    );
  }

  void showNotLoginAlertDialog(BuildContext context) {
    DialogManager().showCustomDialog(
      CustomDialog(
        content: "您还没有登录",
        contentFontSize: 18,
        contentColor: Colors.black,
        buttonHeight: 70,
        horizontalPadding: 20,
        buttons: [
          DialogButton(
              label: "继续逛逛",
              onPressed: () {
                LogManager().debug("取消按钮被点击");
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xFF9B9DA9)),
          DialogButton(
              label: "去登录",
              onPressed: () {
                LoginManager().showLoginModal();
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xffea0029)),
        ],
      ),
    );
  }

  void showSwitchCarDialog(BuildContext context) async {
    try {
      LoadingManager.show();
      List<CarSimplifiedInfoModel> list =
          await carAPI.downloadCarListForChoose([]);
      LoadingManager.dismiss();
      DialogManager().showCarSwitchDialog(
        onCarSelected: ({CarSimplifiedInfoModel? carSimplifiedInfoModel}) {
          if (carSimplifiedInfoModel != null) {
            LogManager().debug(
                '选择了vin为${carSimplifiedInfoModel.vin}的${carSimplifiedInfoModel.carTypeName}');
            doSetDefaultCarNetWork(carSimplifiedInfoModel);
          }
        },
        carInfoList: list,
      );
    } catch (e) {
      LoadingManager.dismiss();
      if (e is APIException) {
        LoadingManager.showError('${e.message}');
      }
    }
  }
  // </editor-fold>

  // <editor-fold desc="智慧泊车相关">
  void _shouldShowRemoteParkBottomSheet(CarControlItemModel itemModel) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return RemoteParkActionDialog(
          carControlItemModel: itemModel,
          onControlButtonClicked: (
              {CarControlItemModel? controlItemModel,
              CarServiceStatusModel? statusModel}) {
            Navigator.pop(context);
            if (statusModel?.serviceStatusName == '泊入') {
              _carIntelligentParkInBtnClicked(itemModel.serviceModel);
            } else if (statusModel?.serviceStatusName == '泊出') {
              _carIntelligentParkOutBtnClicked(itemModel.serviceModel);
            } else {}
          },
        );
      },
    );
  }

  _carIntelligentParkInBtnClicked(CarServiceModel serviceModel) {
    LoadingManager.show(
        status: '查询泊车状态中...', maskType: EasyLoadingMaskType.black);
    String vin = GlobalData().carInfoModel?.vin ?? '';
    LogManager().debug('E300BLE]---点击了记忆泊入----vin:$vin');
    carApi.getParkingStatusWithVIN(
        vin: vin,
        onSucceed: ({required RemoteParkBaseModel result}) {
          LogManager().debug('E300BLE]-------result:${result.data}');
          LoadingManager.dismiss();
          if (result.data.appInfcSw == '00') {
            Navigator.push(
                context,
                CustomCupertinoPageRoute(
                    builder: (context) => RemoteParkGuidePage()));
          } else if (result.data.appInfcSw == '01') {
            _goAssistantParkInView(true, serviceModel);
          } else if (result.data.appInfcSw == '02') {
          } else if (result.data.appInfcSw == '03' ||
              result.data.appInfcSw == '04') {
          } else if (result.data.appInfcSw == '05') {
          } else if (result.data.appInfcSw == '06') {
          } else {
            _goAssistantParkInView(false, serviceModel);
          }
        },
        onError: (Exception exception) {
          // 显示从API层传来的友好提示
          String errorMessage = exception.toString();
          // 如果是我们自定义的Exception，提取友好提示
          if (errorMessage.startsWith('Exception: ')) {
            errorMessage = errorMessage.substring(11); // 去掉 "Exception: " 前缀
          }
          LoadingManager.showError(
              errorMessage.isNotEmpty ? errorMessage : '啊哦，网络似乎出了点问题…');
          LogManager().debug('远控泊车状态查询失败: ${exception.toString()}');
          Future.delayed(const Duration(milliseconds: 500), () {
            _goAssistantParkInView(false, serviceModel);
          });
        });
  }

  _carIntelligentParkOutBtnClicked(CarServiceModel serviceModel) {
    LogManager().debug('E300BLE]---点击了记忆泊出----');

    //  Navigator.push(
    //         context,
    //         CustomCupertinoPageRoute(
    //             builder: (context) =>
    //                 RemoteParkAnimationTestPage()));
    // return;

    LoadingManager.show(
        status: '蓝牙连接中...', maskType: EasyLoadingMaskType.black);
    _connectBluetooth(() {
      //success
      LoadingManager.dismiss();
      if (serviceModel.serviceCode == ServiceConstant.jumpToSmartParkingF511C) {
        Navigator.push(
            context,
            CustomCupertinoPageRoute(
                builder: (context) => RemoteParkF511CCallPage(
                      hasStraightCtrl:
                          CarRemoteParkUtil.checkSupportStraightControl(
                              serviceModel),
                    ),
                canSwipeBack: false));
      } else if (serviceModel.serviceCode ==
          ServiceConstant.jumpToSmartParkingF511S) {
        Navigator.push(
            context,
            CustomCupertinoPageRoute(
                builder: (context) => RemoteParkF511SCallPage(
                      hasStraightCtrl:
                          CarRemoteParkUtil.checkSupportStraightControl(
                              serviceModel),
                    ),
                canSwipeBack: false));
      } else {
        LoadingManager.showToast('功能开发中 敬请期待');
      }
    }, () {
      //failure
      LoadingManager.dismiss();
    });
  }

  _goAssistantParkInView(bool correctly, CarServiceModel serviceModel) {
    LoadingManager.show(
        status: '蓝牙连接中...', maskType: EasyLoadingMaskType.black);

    _connectBluetooth(() {
      //success
      LoadingManager.dismiss();
      if (serviceModel.serviceCode == ServiceConstant.jumpToSmartParkingF511C) {
        Navigator.push(
            context,
            CustomCupertinoPageRoute(
                builder: (context) =>
                    RemoteParkF511CParkPage(incorrect: !correctly),
                canSwipeBack: false));
      } else if (serviceModel.serviceCode ==
          ServiceConstant.jumpToSmartParkingF511S) {
        Navigator.push(
            context,
            CustomCupertinoPageRoute(
                builder: (context) => RemoteParkF511SParkPage(
                      incorrect: !correctly,
                    ),
                canSwipeBack: false));
      } else {
        LoadingManager.showToast('功能开发中 敬请期待');
      }
    }, () {
      //failure
      LoadingManager.dismiss();
    });
  }

  _connectBluetooth(Function() success, Function() failure) {
    _bleConnectFuntion = null;
    _bleConnectFuntion = (isSuccess, message) {
      LogManager().debug(
          'E300BLE]-----connectBluetooth->success:$isSuccess ,msg:$message');
      if (isSuccess) {
        success();
      } else {
        failure();
      }
    };

    if (!BleManager().hasBluetoothKey()) {
      _bleConnectFuntion!(false, '未正确获取到蓝牙钥匙，请下拉刷新重新获取');
      LoadingManager.showInfo('未正确获取到蓝牙钥匙，请下拉刷新重新获取',
          maskType: EasyLoadingMaskType.clear);
    } else {
      BluetoothAdapterState currentSystemBluetoothStatus =
          _bleManager.getCurrentSystemBluetoothStatus();
      if (currentSystemBluetoothStatus == BluetoothAdapterState.on) {
        if (_currentBleStatus == BleStatus.bleDefault) {
          // setState(() {
          //   _isHandleConnectBle = true;
          // });
          _startScan(true);
        } else if (_currentBleStatus == BleStatus.bleAuthorizing ||
            _currentBleStatus == BleStatus.bleAuthHandshaking1 ||
            _currentBleStatus == BleStatus.bleAuthHandshaking2 ||
            _currentBleStatus == BleStatus.bleSearching) {
        } else if (_currentBleStatus == BleStatus.bleAuthorized) {
          _bleConnectFuntion!(true, '');
          LoadingManager.dismiss();
        }
      } else {
        String notice = '';
        switch (currentSystemBluetoothStatus) {
          case BluetoothAdapterState.off:
            notice = '当前系统蓝牙未打开 请打开后再尝试';
            break;
          case BluetoothAdapterState.unauthorized:
            notice = '当前系统未授权使用蓝牙 请授权后再尝试';
            break;
          case BluetoothAdapterState.unavailable:
            notice = '当前设备无法使用蓝牙功能';
            break;
          case BluetoothAdapterState.turningOn:
            notice = '当前系统蓝牙正在打开 请稍候';
            break;
          case BluetoothAdapterState.turningOff:
            notice = '当前系统蓝牙正在关闭 请稍候';
            break;
          default:
            notice = '未知蓝牙状态';
        }
        _bleConnectFuntion!(false, notice);
        LoadingManager.showInfo(notice, maskType: EasyLoadingMaskType.clear);
      }
    }

    // Future.delayed(const Duration(seconds: 1),(){
    //   _bleConnectFuntion!(true,'');
    // });
  }
  // </editor-fold>

  // 获取导航列表数据 - 从服务器数据生成
  List<MenuItemCard> _getNavigationList() {
    List<MenuItemCard> navigationList = [];

    // 从toolServiceList中查找导航相关的服务
    for (CarServiceModel service in _toolServiceList) {
      String icon = 'help_outline'; // 默认图标
      String info = '';

      // 根据serviceCode确定图标和状态信息
      switch (service.serviceCode) {
        case 'Map': // 导航到车
          icon = 'location';
          if (_statusModel?.position != null &&
              _statusModel!.position.isNotEmpty) {
            info = _statusModel!.position;
          } else {
            info = "未知位置";
          }
          break;
        case 'AcAppointment': // 空调预约
        case 'CarAcAppointment': // 空调预约
        case 'OrderAC': // 预约空调
          icon = 'ventilation';
          if (_statusModel?.acStatus == "1") {
            info = "运行中";
          } else {
            info = "已关闭";
          }
          break;
        default:
          // 其他服务使用默认图标，状态信息为服务描述
          info = service.serviceDesc.isNotEmpty ? service.serviceDesc : "可用";
          break;
      }

      navigationList.add(MenuItemCard(
        icon: icon,
        label: service.serviceName,
        info: info,
      ));
    }

    return navigationList;
  }

  /**
   * 将温度档位转换为具体的温度
   *
   *电动空调处理 电动空调返回的acTemperature为1~6（单位：档），需要转为对应温度
   * 1档-17℃，2档-19℃，3档-22℃，4档-25℃，5档-27℃，6档-29℃
   *
   * @param targetAC 车况返回数值
   * @return 转换的温度
   */
  int transformTemperature(int targetAC) {
    switch (targetAC) {
      case 1:
        return 17;
      case 2:
        return 19;
      case 3:
        return 22;
      case 4:
        return 25;
      case 5:
        return 27;
      case 6:
        return 29;
      default:
        return 0; // 保持与原逻辑一致的默认值
    }
  }

  // <editor-fold desc="缓存相关方法">

  /// 缓存服务列表数据
  Future<void> _cacheServiceLists() async {
    try {
      final userIdStr = GlobalData().userModel?.userIdStr;
      final vin = _carInfoModel?.vin;
      if (userIdStr != null &&
          userIdStr.isNotEmpty &&
          vin != null &&
          vin.isNotEmpty) {
        String key = 'travel_service_lists_${userIdStr}_$vin';
        Map<String, dynamic> serviceData = {
          'ctrlServiceList': _ctrlServiceList.map((e) => e.toJson()).toList(),
          'toolServiceList': _toolServiceList.map((e) => e.toJson()).toList(),
          'newuiServiceArray':
              _newuiServiceArray.map((e) => e.toJson()).toList(),
          'activityList': _activityList.map((e) => e.toJson()).toList(),
          'useCarServiceList':
              _useCarServiceList.map((e) => e.toJson()).toList(),
        };
        await SpUtil().setJSON(key, serviceData);
        LogManager().debug('服务列表缓存成功，key: $key');
      }
    } catch (e) {
      LogManager().debug('缓存服务列表异常: $e');
    }
  }

  /// 从缓存加载服务列表数据
  Future<bool> _loadServiceListsFromCache() async {
    try {
      final userIdStr = GlobalData().userModel?.userIdStr;
      final vin = _carInfoModel?.vin;
      if (userIdStr != null &&
          userIdStr.isNotEmpty &&
          vin != null &&
          vin.isNotEmpty) {
        String key = 'travel_service_lists_${userIdStr}_$vin';
        var serviceData = SpUtil().getJSON(key);
        if (serviceData != null) {
          _ctrlServiceList = (serviceData['ctrlServiceList'] as List?)
                  ?.map((e) => CarServiceModel.fromJson(e))
                  .toList() ??
              [];
          _toolServiceList = (serviceData['toolServiceList'] as List?)
                  ?.map((e) => CarServiceModel.fromJson(e))
                  .toList() ??
              [];
          _newuiServiceArray = (serviceData['newuiServiceArray'] as List?)
                  ?.map((e) => CarServiceModel.fromJson(e))
                  .toList() ??
              [];
          _activityList = (serviceData['activityList'] as List?)
                  ?.map((e) => CarServiceModel.fromJson(e))
                  .toList() ??
              [];
          _useCarServiceList = (serviceData['useCarServiceList'] as List?)
                  ?.map((e) => UserHandleModel.fromJson(e))
                  .toList() ??
              [];

          return true;
        } else {
          LogManager().debug('缓存中没有找到服务列表数据');
        }
      }
    } catch (e) {
      LogManager().debug('从缓存加载服务列表异常: $e');
    }
    return false;
  }

  /// 缓存车辆状态
  Future<void> _cacheCarStatus(CarStatusModel? carStatus,
      {CarInfoModel? carInfo}) async {
    if (carStatus == null) {
      LogManager().debug('缓存车辆状态失败：carStatus为空');
      return;
    }
    try {
      final userIdStr = GlobalData().userModel?.userIdStr;
      final vin = carInfo?.vin ?? _carInfoModel?.vin;
      LogManager().debug('_cacheCarStatus - userIdStr: $userIdStr, vin: $vin');

      if (userIdStr != null &&
          userIdStr.isNotEmpty &&
          vin != null &&
          vin.isNotEmpty) {
        String key = 'travel_car_status_${userIdStr}_$vin';
        await SpUtil().setJSON(key, carStatus.toJson());
        LogManager().debug('车辆状态缓存成功，key: $key, 电量: ${carStatus.batterySoc}%');
      } else {
        LogManager().debug('缓存车辆状态失败：userIdStr或vin为空');
      }
    } catch (e) {
      LogManager().debug('缓存车辆状态异常: $e');
    }
  }

  /// 从缓存加载车辆状态
  Future<CarStatusModel?> _loadCarStatusFromCache(
      {CarInfoModel? carInfo}) async {
    try {
      final userIdStr = GlobalData().userModel?.userIdStr;
      final vin = carInfo?.vin ?? _carInfoModel?.vin;

      if (userIdStr != null &&
          userIdStr.isNotEmpty &&
          vin != null &&
          vin.isNotEmpty) {
        String key = 'travel_car_status_${userIdStr}_$vin';

        var carStatusMap = SpUtil().getJSON(key);
        if (carStatusMap != null) {
          return CarStatusModel.fromJson(carStatusMap);
        }
      }
    } catch (e) {
      LogManager().debug('从缓存加载车辆状态异常: $e');
    }
    return null;
  }

  /// 统一从缓存加载数据（仅在无网络时调用）
  Future<void> _loadDataFromCache() async {
    try {
      // 如果当前没有车辆信息，先尝试从缓存加载
      CarInfoModel? carInfo = _carInfoModel;

      if (carInfo == null) {
        final userIdStr = GlobalData().userModel?.userIdStr;
        LogManager().debug('尝试从缓存加载车辆信息，userIdStr: $userIdStr');
        if (userIdStr != null && userIdStr.isNotEmpty) {
          String key = '${SP_USER_DEFAULT_CAR_KEY}_$userIdStr';
          var carInfoMap = SpUtil().getJSON(key);
          if (carInfoMap != null) {
            carInfo = CarInfoModel.fromJson(carInfoMap);
            LogManager().debug('从缓存加载到车辆信息: ${carInfo.vin}');
            setState(() {
              _carInfoModel = carInfo;
              _isCarUser = carInfo?.vin?.isNotEmpty ?? false;
              GlobalData().carInfoModel = carInfo;
            });
          } else {
            LogManager().debug('缓存中没有车辆信息');
          }
        }
      }

      // 加载服务列表
      bool serviceListLoaded = await _loadServiceListsFromCache();

      // 加载 MiniLife 缓存数据
      LogManager().debug('UseCarPage: 开始从缓存加载MiniLife数据');
      final miniLifeCacheData =
          await MiniLifeCacheManager.getCachedMiniLifeData();
      if (miniLifeCacheData.isFromCache) {
        setState(() {
          if (miniLifeCacheData.activityList.isNotEmpty) {
            _activityList = miniLifeCacheData.activityList;
            LogManager()
                .debug('UseCarPage: 从缓存加载活动列表成功，数量: ${_activityList.length}');
          }
          if (miniLifeCacheData.miniLifeModel != null) {
            _miniLifeModel = miniLifeCacheData.miniLifeModel;
            LogManager().debug('UseCarPage: 从缓存加载MiniLife模型成功');
          }
        });
      } else {
        LogManager().debug('UseCarPage: 缓存中没有MiniLife数据');
      }

      // 尝试从缓存加载蓝牙钥匙
      _loadBleKeyFromCache();

      // 加载车辆状态
      final cachedCarStatus = await _loadCarStatusFromCache(carInfo: carInfo);
      if (cachedCarStatus != null) {
        setState(() {
          _statusModel = cachedCarStatus;

          // 如果服务列表也加载成功，重新构建车控按钮
          if (serviceListLoaded) {
            buildCtrlItemList(_ctrlServiceList);
            updateCtrlItemList();
            updateCtrlSubItemList();
            _isNeedCheckTire = handleNeedCheckTire();
          }
        });
      }

      // 缓存数据加载完成后，检查是否需要自动进入离线模式
      _checkAutoEnterOfflineMode();
    } catch (e) {
      LogManager().debug('从缓存加载数据失败: $e');
    }
  }

  /// 检查是否需要自动进入离线模式
  void _checkAutoEnterOfflineMode() {
    // 只有在无网络、支持蓝牙功能且未处于离线模式时才自动进入
    if (_isDisconnected && _carInfoModel?.bleType != 0 && !_isOfflineMode) {
      LogManager().debug(
          'UseCarPage: 缓存数据加载完成，检测到无网络且支持蓝牙功能(bleType=${_carInfoModel?.bleType})，自动进入离线模式');
      _enterOfflineMode();
    }
  }

  /// 页面初始化时加载所有缓存数据
  Future<void> _loadAllCacheDataOnInit() async {
    try {
      LogManager().debug('缓存优先：页面初始化时开始加载所有缓存数据');

      if (showCarControlPage()) {
        // 有车用户：加载车控相关缓存
        // 1. 加载用车服务列表缓存
        await _loadUseCarServiceFromCache();

        // 2. 加载车控服务列表缓存
        bool serviceListLoaded = await _loadServiceListsFromCache();

        // 3. 加载车辆状态缓存
        final cachedCarStatus =
            await _loadCarStatusFromCache(carInfo: _carInfoModel);
        if (cachedCarStatus != null) {
          setState(() {
            _statusModel = cachedCarStatus;

            // 如果服务列表也加载成功，重新构建车控按钮
            if (serviceListLoaded) {
              buildCtrlItemList(_ctrlServiceList);
              updateCtrlItemList();
              updateCtrlSubItemList();
              _isNeedCheckTire = handleNeedCheckTire();
            }
          });
        }
      } else {
        // 无车用户：加载无车服务列表缓存
        await _loadNoCarServiceFromCache();

        // 无车用户也加载 MiniLife 缓存数据
        LogManager().debug('UseCarPage: 无车用户开始从缓存加载MiniLife数据');
        final miniLifeCacheData =
            await MiniLifeCacheManager.getCachedMiniLifeData();
        if (miniLifeCacheData.isFromCache) {
          setState(() {
            if (miniLifeCacheData.activityList.isNotEmpty) {
              _activityList = miniLifeCacheData.activityList;
              LogManager().debug(
                  'UseCarPage: 无车用户从缓存加载活动列表成功，数量: ${_activityList.length}');
            }
            if (miniLifeCacheData.miniLifeModel != null) {
              _miniLifeModel = miniLifeCacheData.miniLifeModel;
              LogManager().debug('UseCarPage: 无车用户从缓存加载MiniLife模型成功');
            }
          });
        } else {
          LogManager().debug('UseCarPage: 无车用户缓存中没有MiniLife数据');
        }
      }

      // 缓存数据加载完成后，检查是否需要自动进入离线模式
      _checkAutoEnterOfflineMode();

      LogManager().debug('缓存优先：页面初始化时缓存数据加载完成');
    } catch (e) {
      LogManager().debug('缓存优先：页面初始化时加载缓存数据失败: $e');
    }
  }

  /// 加载无车用户服务列表缓存数据
  Future<void> _loadNoCarServiceFromCache() async {
    try {
      final userIdStr = GlobalData().userModel?.userIdStr;
      if (userIdStr != null && userIdStr.isNotEmpty) {
        // 加载无车服务列表缓存
        String key = 'no_car_service_list_$userIdStr';
        var serviceJson = SpUtil().getJSON(key);
        if (serviceJson != null) {
          UserHandleListModel serviceModel =
              UserHandleListModel.fromJson(serviceJson);
          setState(() {
            _servicesHandleList = serviceModel;
          });
          LogManager().debug('从缓存加载无车服务列表成功');
        } else {
          LogManager().debug('缓存中没有找到无车服务列表数据');
        }
      }
    } catch (e) {
      LogManager().debug('从缓存加载无车服务列表失败: $e');
    }
  }

  /// 加载用车服务缓存数据
  Future<void> _loadUseCarServiceFromCache() async {
    try {
      final userIdStr = GlobalData().userModel?.userIdStr;
      final vin = _carInfoModel?.vin;
      if (userIdStr != null &&
          userIdStr.isNotEmpty &&
          vin != null &&
          vin.isNotEmpty) {
        // 使用与车控服务相同的缓存key
        String key = 'travel_service_lists_${userIdStr}_$vin';
        var serviceData = SpUtil().getJSON(key);
        if (serviceData != null) {
          _useCarServiceList = (serviceData['useCarServiceList'] as List?)
                  ?.map((e) => UserHandleModel.fromJson(e))
                  .toList() ??
              [];
          LogManager().debug('从缓存加载用车服务列表成功，共${_useCarServiceList.length}项');
        } else {
          LogManager().debug('缓存中没有找到用车服务列表数据');
        }
      }
    } catch (e) {
      LogManager().debug('从缓存加载用车服务列表失败: $e');
    }
  }

  // </editor-fold>
}
