// @keepTs
// @ts-nocheck
import { ConnectionStatus, ConnectionStatusListener, IAsyncResult } from '@rongcloud/imlib';
import { ConnectionService } from '../../connect/ConnectionService';
export declare class InnerConnectionServiceImpl implements ConnectionService {
    onInit(): void;
    addConnectionStatusListener(j63: ConnectionStatusListener): void;
    removeConnectionStatusListener(i63: ConnectionStatusListener): void;
    getCurrentUserId(): string;
    /**
     * 获取当前连接状态
     * @returns 连接状态
     */
    getCurrentConnectionStatus(): Promise<IAsyncResult<ConnectionStatus>>;
}
