import web_webview from '@ohos.web.webview';
import { Params } from '@ohos/flutter_ohos/src/main/ets/plugin/platform/PlatformView';
import { WebViewPlatformView } from './WebViewHostApiImpl';
@Component
export declare struct OhosWebView {
    @Prop
    params: Params;
    webView: WebViewPlatformView;
    controller: web_webview.WebviewController;
    @State
    textZoomRatio: number;
    @State
    disposed: boolean;
    aboutToAppear(): void;
    build(): void;
}
@Builder
export declare function WebBuilder(params: Params): void;
