import { SourceBase } from './SourceBase';
import { MediaFormat } from './MediaFormat';
import { Definition } from './Definition';
import { VidPlayerConfigGen } from '../VidPlayerConfigGen';
export declare class VidSourceBase extends SourceBase {
    private mFormats;
    private mDefinitions;
    private mPlayConfig;
    private mOutputType;
    private mStreamTypes;
    private mReAuthInfo;
    private mResultType;
    private mAuthTimeout;
    private mTrace;
    private mDigitalWatermarkType;
    constructor();
    protected nativeGetOutputTypeStr: Function;
    protected nativeGetReAuthInfoStr: Function;
    protected nativeGetResultTypeStr: Function;
    protected nativeGetStreamTypeStr: Function;
    protected nativeGetDefinitionStr: Function;
    protected nativeGetPlayerConfigStr: Function;
    protected nativeGetFormatStr: Function;
    protected nativeGetAuthTimeout: Function;
    protected nativeGetTrace: Function;
    protected nativeGetDigitalWatermarkTypeStr: Function;
    /**
     * 获取播放参数
     *
     * @return 播放参数
     */
    /****
     * Query playback parameters.
     *
     * @return The playback parameters.
     */
    getPlayerConfig(): string;
    /**
     * 设置播放参数
     *
     * @param playConfig 播放参数
     */
    /****
     * Set playback parameters.
     *
     * @param playConfig The playback parameters.
     */
    setPlayerConfig(h39: VidPlayerConfigGen): void;
    /**
     * 获取服务器返回的格式
     * @return 服务器返回的格式
     */
    /****
     * Query the formats of the returned data.
     * @return The formats of the returned data.
     */
    getFormats(): MediaFormat[];
    /**
     * 设置服务器返回的格式。可以不设置。默认为 mp4,m3u8,mp3,flv
     * @param formats 服务器返回的格式
     */
    /****
     * Specify the formats of the returned data. This parameter is optional. Default: mp4,m3u8,mp3,flv.
     * @param formats The formats of the returned data.
     */
    setFormats(g39: MediaFormat[]): void;
    getFormatStr(): string;
    /**
     * 设置点播服务器返回的码率清晰度类型。
     *
     * @param definitions 类型集合。注意：如果类型集合中包含有{@linkplain Definition#DEFINITION_AUTO},那么只会返回{@linkplain Definition#DEFINITION_AUTO}的清晰度。
     */
    setDefinition(d39: Definition[]): void;
    getOutputType(): OutputType | null;
    setOutputType(c39: OutputType): void;
    setAuthTimeout(b39: number): void;
    setTrace(a39: string): void;
    setDigitalWatermarkType(z38: DigitalWatermarkType): void;
    getTrace(): string;
    getDigitalWatermarkType(): DigitalWatermarkType | null;
}
export declare enum OutputType {
    /**
     * 回源地址。仅支持播放格式为MP4的OSS地址。
     */
    oss = "oss",
    /**
     * 加速地址。(默认)
     */
    cdn = "cdn"
}
export declare enum StreamType {
    /**
     * 视频
     */
    video = "video",
    /**
     * 音频
     */
    audio = "audio"
}
export declare enum ResultType {
    /**
     * 每种清晰度和格式只返回一路最新转码完成的流。(默认)
     */
    Single = "Single",
    /**
     * 每种清晰度和格式返回所有转码完成的流。
     */
    Multiple = "Multiple"
}
export declare enum DigitalWatermarkType {
    /**
     * 溯源水印
     */
    TraceMark = "TraceMark",
    /**
     * 版权水印
     */
    CopyrightMark = "CopyrightMark"
}
