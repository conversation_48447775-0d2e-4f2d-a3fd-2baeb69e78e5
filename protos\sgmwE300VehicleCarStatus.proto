syntax = "proto3";

option java_outer_classname = "KiwiDji";
//主题： F710C/{env}/vehicle/status/{vin} ,其中测试环境env为pre，生产环境env为prod.主题名找对应的产品负责人确认。根据不同车型名会有变更
//如若UCU在整车CAN采集不到相关数据，默认填充255上报(string格式，内容为255)
//2024-09-05 F710C 增加27-30 座椅通风和加热字段
//2024-09-09 F710C 增加31-34 后排座椅通风和加热字段
message SgmwE300VehicleCarStatus {
  int64 collectTime = 1; //状态更新时间，unix时间戳，毫秒级
  string dipHeadLight = 2; //远光灯灯是否开启，0关1开
  string lowBeamLight = 3; //近光灯是否开启，0关1开
  string psWdronDgr = 4; //副驾驶车窗开度，0-100
  string drWdronDgr = 5; //驾驶员侧车窗开度，0-100
  string sntrModSetSts = 6; //哨兵模式状态，0关1开
  string VecChrgStsIndOn1 = 7; //快充充电状态，0未充电，1充电中
  string VecChrgStsIndOn2 = 8; //慢充充电状态，0未充电，1充电中
  string DDAjrSwAtv = 9; //驾驶员侧门碰开关状态，0关，1开
  string TDAjrSwAtv = 10; //尾门门碰开关状态，0关,1开
  string KyPstn = 11; //钥匙状态, 0:OFF 1:ACC 2:ON
  string PsDoorOpenSwAct = 12; //右前门锁开关状态，0关,1开
  string PDAjrSwAtv = 13; //右前门门碰开关状态，0关,1开
  string DrDoorOpenSwAct = 14; //驾驶员侧门锁开关状态，0关,1开
  string ACACButCntSt = 15; //空调AC 按键控制状态,1: 关闭 2:打开
  string ACCntTmpOLfCntSt = 16; //空调左温区控制温度控制状态,单位:℃
  string ACBLwLvlCntSt = 17; //空调风量,0:未开启 1:Level 1（1 档 ） 2:Level 2（2 档 ） 3:Level 3（3 档 ） 4:Level 4（4 档 ） 5:Level 5（5 档 ）6:Level 6（6 档 ） 7:Level 7（7 档 ） 8:Level 8（8 档 ） 9:Level 9（9 档 ）
  string chargeLoVolBatSts = 18; //低压蓄电池补电状态，0正常工作，1停止补电
  string RRDoorOpenSwAct = 19; //右后门锁开关状态，0:False（关闭），1:True（开启）
  string RSDAjrSwAtv = 20; //右后门门碰开关状态，0:False（关闭），1:True（开启）
  string RLDoorOpenSwAct = 21; //左后门锁开关状态，0:False（关闭），1:True（开启）
  string LSDAjrSwAtv = 22; //左后门门碰开关状态，0:False（关闭），1:True（开启）
  string ACPwrModCntSt = 23; //空调电源模式，0:UnusedandReserved（预留）1:Off（关闭）2:On（打开）3:UnusedandReserved（预留）
  string SecRwLtWdwOpenDgr = 24; //第二排左窗开度，0-100
  string SecRwRtWdwOpenDgr = 25; //第二排右窗开度，0-100
  string PLGSysMSt = 26; //电动尾门开关状态 ，0-100；//0x0:Unknown（未知）0x1:Full Open（全开状态）0x2:Closed（全关状态）0x3:Opening（正在开门）0x4:Closing（正在关门）0x5:Stopped（停止状态）0x6:Unused and Reserved（预留）0x7:Unused and Reserved（预留）
  string drSeatVentRCC = 27; //驾驶员侧座椅远程通风控制请求		//0 无控制请求;1:通风1;2通风2;3通风3;4-6预留;7 通风关闭
  string prSeatVentRCC = 28; //副驾驶侧座椅远程通风控制请求		//0 无控制请求;1:通风1;2:通风2;3:通风3；4-6预留；7 通风关闭
  string drSeatHeatRCC = 29;  //驾驶员侧座椅加热远程控制请求		//0 无控制请求;1:加热1;2:加热2;3:加热3；4-6预留；7 加热关闭
  string prSeatHeatRcc = 30;  //副驾驶员侧座椅加热远程控制请求	//0 无控制请求;1:加热1;2:加热2;3:加热3；4-6预留；7 加热关闭
  string rlSeatVentRCC = 31; //后排左侧座椅远程通风控制请求		//0 无控制请求;1:通风1;2通风2;3通风3;4-6预留;7 通风关闭
  string rrSeatVentRCC = 32; //后排右驶侧座椅远程通风控制请求		//0 无控制请求;1:通风1;2:通风2;3:通风3；4-6预留；7 通风关闭
  string rlSeatHeatRCC = 33;  //后排左侧座椅加热远程控制请求		//0 无控制请求;1:加热1;2:加热2;3:加热3；4-6预留；7 加热关闭
  string rrSeatHeatRcc = 34;  //后排右侧座椅加热远程控制请求	//0 无控制请求;1:加热1;2:加热2;3:加热3；4-6预留；7 加热关闭
}