import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_platform_utils/flutter_platform_utils.dart';

import 'log_manager.dart';

class GetQuickLoginInfoManager {
  // 定义MethodChannel，通道名称为 com.sgmw.flutter/get_quick_login_info_manager
  static const MethodChannel _channel = MethodChannel('com.sgmw.flutter/get_quick_login_info_manager');
  static Future<dynamic> getQuickLoginInfo() async {
    if (PlatformUtils.isOhos) {
      try {
        // 调用原生方法 'getQuickLoginInfo'
        final result = await _channel.invokeMethod('getQuickLoginInfo');
        return result;
      } on PlatformException catch (e) {
        LogManager().debug("Failed to open app detail page: ${e.message}");
      }
    }
  }
}