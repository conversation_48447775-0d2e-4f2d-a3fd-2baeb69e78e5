import { LiveTimeUpdater } from "../liveshift/LiveTimeUpdater";
import { AVPBase } from "./AVPBase";
import { OhosSaasPlayer } from "./nativeclass/OhosSaasPlayer";
import { UrlSource } from "./source/UrlSource";
import url from "@ohos.url";
import { prepared, started } from "../player/IPlayer";
const onTimeShiftUpdaterListenerMap = new WeakMap();
const liveOnPreparedListenerMap = new WeakMap();
const liveOnLoadingStatusListenerMap = new WeakMap();
const liveOnStateChangedListenerMap = new WeakMap();
export class ApsaraLiveShiftPlayer extends AVPBase {
    constructor(o9, p9) {
        super(o9, p9);
        this.status = -1;
        this.statusWhenSeek = -1;
        this.liveSeekToTime = -1;
        this.liveSeekOffset = -1;
        this.mLiveShiftSource = null;
        this.mLiveTimeUpdater = null;
        this.mOutOnTimeShiftUpdaterListener = null;
        this.mInnerOnTimeShiftUpdaterListener = new InnerTimeShiftUpdateListener(this);
        this.mOutLiveOnPreparedListener = null;
        this.mInnerLiveOnPreparedListener = new InnerLivePreparedListener(this);
        this.mOutLiveOnStateChangedListener = null;
        this.mInnerLiveOnStateChangedListener = new InnerLiveStateChangedListener(this);
        this.mOutLiveOnLoadingStatusListener = null;
        this.mInnerLiveOnLoadingStatusListener = new InnerLiveLoadingStatusListener(this);
        this.mOutLiveSeekLiveCompletionListener = null;
        onTimeShiftUpdaterListenerMap.set(this, this.mInnerOnTimeShiftUpdaterListener);
        liveOnPreparedListenerMap.set(this, this.mInnerLiveOnPreparedListener);
        liveOnLoadingStatusListenerMap.set(this, this.mInnerLiveOnLoadingStatusListener);
        liveOnStateChangedListenerMap.set(this, this.mInnerLiveOnStateChangedListener);
    }
    createAlivcMediaPlayer(m9) {
        let n9 = new OhosSaasPlayer(m9);
        return n9;
    }
    setLiveShiftDataSource(j9) {
        this.mLiveShiftSource = j9;
        let k9 = new UrlSource();
        k9.setUri(this.mLiveShiftSource.getUrl());
        let l9 = this.getCorePlayer();
        if (l9 instanceof OhosSaasPlayer) {
            l9.setUrlDataSource(k9);
        }
    }
    getCurrentLiveTime() {
        if (this.mLiveTimeUpdater) {
            return this.mLiveTimeUpdater.getLiveTime();
        }
        return 0;
    }
    getCurrentTime() {
        if (this.mLiveTimeUpdater) {
            return this.mLiveTimeUpdater.getPlayTime();
        }
        return 0;
    }
    seekToLiveTime(b9) {
        if (this.status == ApsaraLiveShiftPlayer.SeekLive) {
            return;
        }
        if (this.mLiveShiftSource == null) {
            return;
        }
        this.statusWhenSeek = this.status;
        this.status = ApsaraLiveShiftPlayer.SeekLive;
        this.liveSeekToTime = b9;
        this.liveSeekOffset = this.getCurrentLiveTime() - this.liveSeekToTime;
        if (this.liveSeekToTime < 0) {
            this.liveSeekToTime = 0;
            this.liveSeekToTime = this.getCurrentLiveTime();
        }
        let c9 = this.mLiveShiftSource?.getUrl();
        if (this.liveSeekToTime > 0 && this.liveSeekOffset > 0) {
            let d9 = url.URL.parseURL(c9);
            let e9 = new url.URLParams(d9.search);
            let f9 = e9.entries();
            let g9 = Array.from(f9);
            if (c9.endsWith("?") || c9.endsWith("&")) {
                c9 += ("lhs_offset_unix_s_0=" + this.liveSeekOffset + "&lhs_start=1&aliyunols=on");
            }
            else {
                if (g9.length <= 0) {
                    c9 += ("?lhs_offset_unix_s_0=" + this.liveSeekOffset + "&lhs_start=1&aliyunols=on");
                }
                else {
                    c9 += ("&lhs_offset_unix_s_0=" + this.liveSeekOffset + "&lhs_start=1&aliyunols=on");
                }
            }
            let h9 = new UrlSource();
            h9.setUri(c9);
            let i9 = this.getCorePlayer();
            if (i9 instanceof OhosSaasPlayer) {
                i9.setUrlDataSource(h9);
                i9.prepare();
            }
        }
    }
    start() {
        super.start();
        if (this.mLiveTimeUpdater != null) {
            this.mLiveTimeUpdater.resumeUpdater();
        }
    }
    pause() {
        super.pause();
        this.mLiveTimeUpdater?.pauseUpdater();
    }
    stop() {
        super.stop();
        this.mLiveTimeUpdater?.stopUpdater();
    }
    onUpdater(y8, z8, a9) {
        console.info(ApsaraLiveShiftPlayer.tag + `onUpdater, currentTime: ${y8}, shiftStartTime: ${z8}, shiftEndTime: ${a9}`);
        if (this.mOutOnTimeShiftUpdaterListener) {
            this.mOutOnTimeShiftUpdaterListener.onUpdater(y8, z8, a9);
        }
    }
    onPrepared() {
        console.info(ApsaraLiveShiftPlayer.tag + " onPrepared");
        if (this.mLiveTimeUpdater != null) {
            this.mLiveTimeUpdater.stopUpdater();
        }
        else {
            if (this.mLiveShiftSource) {
                this.mLiveTimeUpdater = new LiveTimeUpdater(this.mLiveShiftSource);
                this.mLiveTimeUpdater.setUpdaterListener(this.mInnerOnTimeShiftUpdaterListener);
            }
        }
        let x8 = this.getConfig();
        if (x8 != undefined) {
            this.mLiveTimeUpdater?.setConfig(x8);
            this.mLiveTimeUpdater?.setStartPlayTime(this.liveSeekToTime);
            this.mLiveTimeUpdater?.startUpdater();
        }
        if (this.status == ApsaraLiveShiftPlayer.SeekLive) {
            this.status = prepared;
            if (this.status == started) {
                this.start();
            }
            else {
                if (this.isAutoPlay()) {
                    this.mLiveTimeUpdater?.resumeUpdater();
                }
                else {
                    this.mLiveTimeUpdater?.pauseUpdater();
                }
            }
            if (this.mOutLiveSeekLiveCompletionListener != null) {
                console.info(ApsaraLiveShiftPlayer.tag + ` onSeekLiveCompletion, playTime: ${this.liveSeekToTime}`);
                this.mOutLiveSeekLiveCompletionListener.onSeekLiveCompletion(this.liveSeekToTime);
            }
            this.liveSeekToTime = -1;
        }
        else {
            this.status = prepared;
            if (this.mOutLiveOnPreparedListener != null) {
                this.mOutLiveOnPreparedListener.onPrepared();
            }
        }
    }
    onStateChanged(w8) {
        console.info(ApsaraLiveShiftPlayer.tag + " onStateChanged, newState: " + w8);
        if (w8 != prepared) {
            this.status = w8;
        }
        if (this.mOutLiveOnStateChangedListener != null) {
            this.mOutLiveOnStateChangedListener.onStateChanged(w8);
        }
    }
    onLoadingBegin() {
        console.info(ApsaraLiveShiftPlayer.tag + " onLoadingBegin");
        if (this.mLiveTimeUpdater != null) {
            this.mLiveTimeUpdater.pauseUpdater();
        }
        if (this.mOutLiveOnLoadingStatusListener != null) {
            this.mOutLiveOnLoadingStatusListener.onLoadingBegin();
        }
    }
    onLoadingProgress(u8, v8) {
        console.info(ApsaraLiveShiftPlayer.tag + " onLoadingProgress, percent: " + u8);
        if (this.mOutLiveOnLoadingStatusListener != null) {
            this.mOutLiveOnLoadingStatusListener.onLoadingProgress(u8, v8);
        }
    }
    onLoadingEnd() {
        console.info(ApsaraLiveShiftPlayer.tag + " onLoadingEnd");
        if (this.mLiveTimeUpdater != null) {
            this.mLiveTimeUpdater.resumeUpdater();
        }
        if (this.mOutLiveOnLoadingStatusListener != null) {
            this.mOutLiveOnLoadingStatusListener.onLoadingEnd();
        }
    }
    setOnTimeShiftUpdaterListener(t8) {
        this.mOutOnTimeShiftUpdaterListener = t8;
    }
    setOnSeekLiveCompletionListener(s8) {
        this.mOutLiveSeekLiveCompletionListener = s8;
    }
    setOnStateChangedListener(r8) {
        this.mOutLiveOnStateChangedListener = r8;
        super.setOnStateChangedListener(this.mInnerLiveOnStateChangedListener);
    }
    setOnPreparedListener(q8) {
        this.mOutLiveOnPreparedListener = q8;
        super.setOnPreparedListener(this.mInnerLiveOnPreparedListener);
    }
    setOnLoadingStatusListener(p8) {
        this.mOutLiveOnLoadingStatusListener = p8;
        super.setOnLoadingStatusListener(this.mInnerLiveOnLoadingStatusListener);
    }
}
ApsaraLiveShiftPlayer.tag = "ApsaraLiveShiftPlayer";
ApsaraLiveShiftPlayer.SeekLive = 10;
class InnerTimeShiftUpdateListener {
    constructor(o8) {
        this.avPlayerRef = o8;
    }
    onUpdater(l8, m8, n8) {
        console.log(`currentTime: ${l8}}, shiftStartTime: ${m8}, shiftEndTime: ${n8} `);
        if (this.avPlayerRef != null) {
            this.avPlayerRef.onUpdater(l8, m8, n8);
        }
    }
}
class InnerLivePreparedListener {
    constructor(k8) {
        this.avPlayerRef = k8;
    }
    onPrepared() {
        if (this.avPlayerRef != null) {
            this.avPlayerRef.onPrepared();
        }
    }
}
class InnerLiveStateChangedListener {
    constructor(j8) {
        this.avPlayerRef = j8;
    }
    onStateChanged(i8) {
        if (this.avPlayerRef != null) {
            this.avPlayerRef.onStateChanged(i8);
        }
    }
}
class InnerLiveLoadingStatusListener {
    constructor(h8) {
        this.avPlayerRef = h8;
    }
    onLoadingBegin() {
        if (this.avPlayerRef != null) {
            this.avPlayerRef.onLoadingBegin();
        }
    }
    onLoadingProgress(f8, g8) {
        if (this.avPlayerRef != null) {
            this.avPlayerRef.onLoadingProgress(f8, g8);
        }
    }
    onLoadingEnd() {
        if (this.avPlayerRef != null) {
            this.avPlayerRef.onLoadingEnd();
        }
    }
    ;
}
