// @keepTs
// @ts-nocheck
/**
 * Created on 2024/9/6
 * <AUTHOR>
 */
import { InformationNotificationMessage } from '@rongcloud/imlib';
import { BaseNotificationMessageItemProvider } from '../../../../conversation/item/provider/BaseNotificationMessageItemProvider';
import { UiMessage } from '../../../../conversation/model/UiMessage';
export declare class InformationNotificationMessageItemProvider extends BaseNotificationMessageItemProvider<InformationNotificationMessage> {
    getMessageWrapBuilder(): WrappedBuilder<[
        Context,
        UiMessage,
        number
    ]>;
    getSummaryTextByMessageContent(n183: Context, o183: InformationNotificationMessage): Promise<MutableStyledString>;
}
@Builder
export declare function bindMessageData(c183: Context, d183: UiMessage, e183: number): void;
