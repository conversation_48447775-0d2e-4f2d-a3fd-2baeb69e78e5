import 'dart:convert';
import 'dart:math';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_platform_utils/flutter_platform_utils.dart';
import 'package:flutter_uikit/flutter_uikit.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wuling_flutter_app/models/community/comment.dart';
import 'package:wuling_flutter_app/widgets/common/image_widget.dart';
import 'package:wuling_flutter_app/widgets/community/bottom_report_widget.dart';
import '../../api/community_api.dart';
import '../../models/index.dart';
import '../../utils/manager/log_manager.dart';
import '../../utils/wechat_shared_util.dart';
import '../../widgets/common/custom_refresher.dart';
import '../../widgets/community/author_widget.dart';
import '../../widgets/community/comment_item_widget.dart';
import '../../widgets/community/post_bottom_comment_widget.dart';
import '../post/banner_image_view.dart';

class ImgTextVideoPostDetailPage extends StatefulWidget {
  const ImgTextVideoPostDetailPage(
      {super.key, required this.postId, required this.postTypeId});
  final int postId;
  final int postTypeId;
  @override
  State<ImgTextVideoPostDetailPage> createState() =>
      _ImgTextVideoPostDetailPageState();
}

class _ImgTextVideoPostDetailPageState
    extends State<ImgTextVideoPostDetailPage> {
  final RefreshController _refreshController = RefreshController();
  late PostDetails postDetails;
  bool isRefresh = false;
  List<Comment> topCommentList = [];
  List<Comment> commentList = [];
  int pageNo = 0;
  int pageSize = 20;
  bool isShowAppbar = true;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _refreshController.requestRefresh();
    });
  }

  //评论数据回调
  void _onHandlerComment(Comment comment) {
    setState(() {
      commentList.insert(0, comment);
    });
  }

  //回复评论
  void _onHandlerReplyComment(Map<String, dynamic> map) {
    Comment comment = Comment.fromJson(map['comment']);
    String commentId = map['commentId'].toString();
    addComment(topCommentList, comment, commentId);
    addComment(commentList, comment, commentId);


  }

  void addComment(
      List<Comment> commentList, Comment comment, String commentId) {
    for (Comment com in commentList) {
      if (com.commentId == int.parse(commentId)) {
        if (com.subWebComments != null) {
          com.subWebComments!.add(comment);
        } else {
          com.subWebComments = [];
          com.subWebComments!.add(comment);
        }
        break;
      }
    }
    setState(() {});
  }

  void refreshCommentData(int topStatus) async {
    if (topStatus == 1) {
      topCommentList =
          await communityAPI.getCommentList(widget.postId, 1, topStatus, 20);
    } else {
      commentList =
          await communityAPI.getCommentList(widget.postId, 1, topStatus, 20);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          SmartRefresher(
              enablePullDown: true,
              enablePullUp: true,
              controller: _refreshController,
              onRefresh: _onRefresh,
              onLoading: _onLoading,
              header: AnimatedRefreshHeader(paddingTop: 20,headerHeight: 76),
              footer: AnimatedRefreshFooter(),
              child: isRefresh
                  ? CustomScrollView(
                slivers: [
                  _appBar(),
                  SliverToBoxAdapter(
                    child: Column(
                      children: [
                        if (postDetails.author != null)
                          AuthorWidget(
                            author: postDetails.author!,
                          ),
                        _title(),
                        _content(),
                        _mentionUserName(),
                        _address(),
                        BottomReportWidget(
                          postDetails: postDetails,
                        )
                      ],
                    ),
                  ),
                  (topCommentList.isNotEmpty) ?
                  _subtitle("置顶评论") : const SliverToBoxAdapter(),
                  _commentWidget(topCommentList),
                  (commentList.isNotEmpty) ?
                  _subtitle("全部评论(${commentList.length}楼)") : const SliverToBoxAdapter(),
                  _commentWidget(commentList),
                  const SliverToBoxAdapter(
                    child: SizedBox(
                      height: 50,
                    ),
                  )
                ],
              )
                  : Container()),
          isRefresh
              ? Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: PostBottomCommentWidget(
                  postId: widget.postId.toString(),
                  onHandlerComment: _onHandlerComment,
                  onHandlerReplyComment: _onHandlerReplyComment,
                  postPraiseCount: postDetails.postPraiseCount ?? 0,
                  postCollectCount: postDetails.postCollectCount ?? 0,
                  postCommentCount: postDetails.postCommentCount ?? 0,
                  isPraise: postDetails.isPraise ?? 0,
                  isCollect: postDetails.isFavorite ?? 0))
              : Container(),
        ],
      ),
    );
  }

  void _onRefresh() async {
    final requestList = [
      communityAPI.getPostDetailsData(widget.postId),
      communityAPI.getCommentList(widget.postId, 1, 1, 20),
      communityAPI.getCommentList(widget.postId, 0, pageNo, pageSize)
    ];
    List responseList = await Future.wait(requestList);
    postDetails = responseList[0];
    topCommentList = responseList[1];
    commentList = responseList[2];
    if(postDetails.coverImage != null && postDetails.coverImage!.isNotEmpty) {
      isShowAppbar = false;
    }else {
      isShowAppbar = true;
    }
    setState(() {
      isRefresh = true;
      _refreshController.refreshCompleted();
    });
  }
  void _onLoading() async {
    if(commentList.length >= 20) {
      pageNo++;
      List<Comment> list = await communityAPI.getCommentList(widget.postId, 0, pageNo, pageSize);
      if(list.isNotEmpty) {
        commentList.addAll(list);
        _refreshController.loadComplete();
      }else {
        _refreshController.loadNoData();
      }
      setState(() {

      });
    }else {
      _refreshController.loadNoData();
    }
  }

  _commentWidget(List<Comment> commentList) {
    if(commentList.isNotEmpty) {
      return SliverList(
          delegate:
          SliverChildBuilderDelegate((context, index) {
            return CommentItemWidget(
              comment: commentList[index],
              index: index,
              isTopComment: true,
              commentCount: commentList[index].subWebComments?.length ?? 0,
            );
          }, childCount: commentList.length));
    }
    return const SliverToBoxAdapter();
  }

  //顶部图标
  Widget _topIcon(String img, int index) {
    return GestureDetector(
      onTap: () {
        if (index == 1) {
          Navigator.of(context).pop();
        } else {
          //_test();
        }
      },
      child: Container(
        width: 40,
        height: 40,
        padding: const EdgeInsets.all(5),
        alignment: Alignment.center,
        decoration: const BoxDecoration(
            color: Color(0xFF5C6B76),
            borderRadius: BorderRadius.all(Radius.circular(20))),
        child: Image.asset(img),
      ),
    );
  }


  Widget _appBar() {
    if(isShowAppbar) {
      return SliverAppBar(
        title: const Text("帖子详情"),
        centerTitle: true,
        elevation: 0,
        pinned: true,
        actions: [
          _shared()
        ],
      );
    }else {
      return SliverPersistentHeader(
        pinned: true,
          delegate: CustomSliverHeaderDelegate(
            max: 280,
              min: 80,
              buildView: (offset,content) {
                return LayoutBuilder(
                    builder: (context,constraints) {
                      // 计算滚动比例：1（完全展开）到0（完全收缩）
                      double expandRatio = (constraints.maxHeight - 80) / 200;
                      return Stack(
                        children: [
                          ImageWidget(width: double.infinity, height: 280,url: postDetails.coverImage,),
                          Opacity(opacity: (1- expandRatio),
                            child: Container(
                              height: 80,
                             padding: const EdgeInsets.only(top: 30),
                             decoration: const BoxDecoration(
                               color: Colors.white
                             ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  GestureDetector(
                                    onTap: () {
                                      Navigator.pop(context);
                                    },
                                    child: Container(
                                      width: 60,
                                      alignment: Alignment.center,
                                      child: const Icon(Icons.arrow_back,color: Colors.black87,size: 24,),
                                    ),
                                  ),
                                  const Text("帖子详情",style: TextStyle(
                                      color: Colors.black87,
                                      fontSize: 18,
                                      fontWeight: FontWeight.w500
                                  ),),
                                  _shared()
                                ],
                              ),
                            ),)
                        ],
                      );
                    },
                );
              }
          )
      );
    }
  }

  Widget _shared() {
    return  TextButton(onPressed: () {
      WechatSharedUtil().share(widget.postId.toString(),widget.postTypeId, postDetails?.author?.userIdStr ?? "",
          postDetails.postTitle ?? "", postDetails.images?[0] ?? "",textJoint());
    }, child: Container(
      padding: const EdgeInsets.symmetric(vertical: 2,horizontal: 15),
      child: const Text("分享",style: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
          color: Colors.black87
      ),),
    ));
  }

  Widget _topBar() {
    return SliverAppBar(
      expandedHeight: isShowAppbar ? 60 : 260,
      pinned: true,
      title: const Text("帖子详情"),
      elevation: 0,
      centerTitle: true,
      flexibleSpace: FlexibleSpaceBar(
          background: LayoutBuilder(builder: (context,constraints) {
            // 计算滚动比例：1（完全展开）到0（完全收缩）
            double expandRatio = (constraints.maxHeight - 60) / constraints.maxHeight;
            if (postDetails.coverImage != null && postDetails.coverImage!.isNotEmpty) {
              return Stack(
                children: [
                  UIImage(imgStr: postDetails.coverImage!,height: 260,),
                ],
              );
            }else {
              return Container();
            }
          })
      ),
    );
  }

  //顶部图片
  Widget _topImg() {
    if (isShowAppbar) {
      return const SliverAppBar(
        title: Text("帖子详情"),
        centerTitle: true,
        elevation: 0,
        pinned: true,
      );
    }else {
      return SliverToBoxAdapter(
        child: SizedBox(
          height: 280,
          width: double.infinity,
          child: CachedNetworkImage(
            imageUrl: postDetails.coverImage!,
            fit: BoxFit.cover,
          ),
        ),
      );
    }

  }

  Widget _title() {
    return Container(
      padding: const EdgeInsets.only(left: 20, right: 20, bottom: 10),
      child: Row(
        children: [
          _isRecommend(),
          _isElite(),
          Text(
            "${postDetails.postTitle}",
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ],
      ),
    );
  }

  ///帖子内容拼接
  String textJoint() {
    String str = "发个帖子快来五菱汽车围观";
    if(postDetails.texts != null && postDetails.texts!.isNotEmpty) {
      for(String text in postDetails.texts!) {
        str += text;
      }
    }
    return str;
  }

  //是否推荐
  Widget _isRecommend() {
    if (postDetails.isRecommend != null && postDetails.isRecommend == 1) {
      return Container(
        height: 20,
        width: 20,
        alignment: Alignment.center,
        decoration: const BoxDecoration(
            color: Color(0xFFF5F1F0),
            borderRadius: BorderRadius.all(Radius.circular(10))),
        child: const Text(
          "荐",
          style: TextStyle(color: Color(0xFFBE958A), fontSize: 10),
        ),
      );
    }
    return Container();
  }

  //是否加精
  Widget _isElite() {
    if (postDetails.isElite != null && postDetails.isElite == 1) {
      return Container(
        height: 20,
        width: 20,
        alignment: Alignment.center,
        margin: const EdgeInsets.only(left: 5, right: 5),
        decoration: const BoxDecoration(
            color: Color(0xFFF7CFD1),
            borderRadius: BorderRadius.all(Radius.circular(10))),
        child: const Text(
          "精",
          style: TextStyle(color: Color(0xFFD7575D), fontSize: 10),
        ),
      );
    }
    return Container();
  }

  Widget _subtitle(String title) {
    return SliverToBoxAdapter(
      child: Column(
        children: [
          const Divider(color: Color(0xFFEEEEEE), thickness: 10),
          Container(
            height: 40,
            padding: const EdgeInsets.only(left: 20),
            child: Row(
              children: [
                Container(
                  width: 5,
                  height: 16,
                  margin: const EdgeInsets.only(right: 5),
                  decoration: const BoxDecoration(
                      color: Color(0xFF3B6945),
                      borderRadius: BorderRadius.all(Radius.circular(2))),
                ),
                Text(
                  title,
                  style: const TextStyle(fontSize: 14),
                )
              ],
            ),
          ),
          const Divider(
            color: Color(0xFFF2F2F2),
            thickness: 1,
            indent: 20,
            endIndent: 20,
          ),
        ],
      ),
    );
  }

  Widget _content() {
    if (postDetails.imgTexts == null || postDetails.imgTexts!.isEmpty) {
      return Container();
    }
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Column(
        children: postDetails.imgTexts!.map((imgTexts) {
          return Container(
            child: _item(imgTexts),
          );
        }).toList(),
      ),
    );
  }

  //@用户
  Widget _mentionUserName() {
    if(postDetails.mentionUserNameListStr != null && postDetails.mentionUserNameListStr!.isNotEmpty) {
      return Container(
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.only(left: 20,right: 20,top: 20),
        child: Text("${postDetails.mentionUserNameListStr}",style: const TextStyle(
            color: Color(0xFF434F67)
        ),softWrap: true,),
      );
    }
    return Container();
  }

  //位置
  Widget _address() {
    if(postDetails.locationName != null && postDetails.locationName!.isNotEmpty) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 20,vertical: 20),
        child: Row(
          children: [
            Image.asset("assets/images/community/location.png",width: 14,),
            Text("${postDetails.locationName}",style: const TextStyle(
                fontSize: 11,
                color: Colors.grey
            ),)
          ],
        ),
      );
    }
    return const SizedBox(height: 20,);
  }

  Widget _item(ImgTexts imgTexts) {
    List<Widget> list = [];
    if (imgTexts.text != null && imgTexts.text!.isNotEmpty) {
      list.add(Container(
        alignment: Alignment.centerLeft,
        padding: const EdgeInsets.symmetric(vertical: 10),
        child: Text(
          imgTexts.text!,
          softWrap: true,
        ),
      ));
    }
    if (imgTexts.img != null && imgTexts.img!.isNotEmpty) {
      if (widget.postTypeId == 6 || widget.postTypeId == 5 ) {
        list.add(Stack(
          alignment: Alignment.center,
          children: [
            SizedBox(
              //       height: pxToDpDevice(double.parse(imgTexts.height!),context),
              width: double.infinity,
              height: 260,
              child: ImageWidget(width: double.infinity, height: double.infinity,url: imgTexts.img,) /*CachedNetworkImage(
                imageUrl: imgTexts.img!,
                fit: BoxFit.cover,
              )*/,
            ),
             /* GestureDetector(
              onTap: () {
                if(imgTexts.videoId != null && imgTexts.videoId!.isNotEmpty) {
                  _player(imgTexts.videoId!);
                }
              },
              child: Image.asset("assets/images/community/play.png",width: 64,),
            )*/
          ],
        ));
      } else {
        list.add(GestureDetector(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => BannerImageViewer(
                    imageUrls: _imageList(), initialIndex: 0),
              ),
            );
          },
          child: ImageWidget(width: double.infinity,url: imgTexts.img,) /*CachedNetworkImage(
              imageUrl: imgTexts.img!,
              fit: BoxFit.cover,
            )*/,
        ));
      }
    }

    return Column(
      children: list,
    );
  }

  List<String> _imageList() {
    List<String> imageList = [];
    for (var element in postDetails.imgTexts!) {
      if (element.img != null) {
        imageList.add(element.img!);
      }
    }
    return imageList;
  }

  double pxToDpDevice(double px, BuildContext context) {
    final deviceRatio = MediaQuery.of(context).devicePixelRatio;
    return px / deviceRatio;
  }

  final MethodChannel _channel =
      const MethodChannel('com.sgmw.flutter/harmony_video');

  Future<void> _player(String videoId) async {
    LogManager().debug("videoId: $videoId");
    //获取阿里云配置信息
    AliyunAccessKey aliyun = await communityAPI.getAliyunAccessKey();
    if (PlatformUtils.isOhos) {
      try {
        await _channel.invokeMethod('openHarmony', {
          'videoId': videoId,
          'accessKeyId': aliyun.accessKeyId,
          'accessKeySecret': aliyun.accessKeySecret,
          'securityToken': aliyun.securityToken
        });
      } on PlatformException catch (e) {
        LogManager().debug("Failed to open app detail page: ${e.message}");
      }
    }
  }
}
