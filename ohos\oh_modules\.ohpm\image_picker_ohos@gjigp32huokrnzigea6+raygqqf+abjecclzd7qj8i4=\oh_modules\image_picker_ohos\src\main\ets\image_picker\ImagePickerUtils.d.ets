import { AsyncCallback } from '@ohos.base';
export default class ImagePickerUtils {
    private static isPermissionPresentInManifest;
    /**
     * Camera permission need request if it present in manifest
     *
     * <p>Camera permission may be used in another package, as example flutter_barcode_reader.
     * https://github.com/flutter/flutter/issues/29837
     *
     * @param callback return true, if need request camera permission, otherwise false
     */
    static needRequestCameraPermission(callback: AsyncCallback<boolean>): void;
}
