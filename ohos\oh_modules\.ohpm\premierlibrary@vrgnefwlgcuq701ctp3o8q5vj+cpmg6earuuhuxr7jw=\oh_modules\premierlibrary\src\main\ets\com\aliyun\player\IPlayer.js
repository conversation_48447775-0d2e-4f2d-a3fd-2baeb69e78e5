export const SET_MEDIA_TYPE = 0;
export const ALLOW_PRE_RENDER = 2;
export const PLAYED_DURATION_INCLUDE_SPEED = 3;
export const unKnow = -1;
export const idle = 0;
export const initalized = 1;
export const prepared = 2;
export const started = 3;
export const paused = 4;
export const stopped = 5;
export const completion = 6;
export const error = 7;
export var IPResolveType;
(function (t21) {
    t21[t21["IpResolveWhatEver"] = 0] = "IpResolveWhatEver";
    t21[t21["IpResolveV4"] = 1] = "IpResolveV4";
    t21[t21["IpResolveV6"] = 2] = "IpResolveV6";
})(IPResolveType || (IPResolveType = {}));
export var ScaleMode;
(function (s21) {
    s21[s21["SCALE_ASPECT_FIT"] = 0] = "SCALE_ASPECT_FIT";
    s21[s21["SCALE_ASPECT_FILL"] = 1] = "SCALE_ASPECT_FILL";
    s21[s21["SCALE_TO_FILL"] = 2] = "SCALE_TO_FILL";
})(ScaleMode || (ScaleMode = {}));
export var MirrorMode;
(function (r21) {
    r21[r21["MIRROR_MODE_NONE"] = 0] = "MIRROR_MODE_NONE";
    r21[r21["MIRROR_MODE_HORIZONTAL"] = 1] = "MIRROR_MODE_HORIZONTAL";
    r21[r21["MIRROR_MODE_VERTICAL"] = 2] = "MIRROR_MODE_VERTICAL";
})(MirrorMode || (MirrorMode = {}));
export var RotateMode;
(function (q21) {
    q21[q21["ROTATE_0"] = 0] = "ROTATE_0";
    q21[q21["ROTATE_90"] = 90] = "ROTATE_90";
    q21[q21["ROTATE_180"] = 180] = "ROTATE_180";
    q21[q21["ROTATE_270"] = 270] = "ROTATE_270";
})(RotateMode || (RotateMode = {}));
export var Option;
(function (p21) {
    p21["RenderFPS"] = "renderFps";
    p21["DownloadBitrate"] = "downloadBitrate";
    p21["VideoBitrate"] = "videoBitrate";
    p21["AudioBitrate"] = "audioBitrate";
    p21["isAbrSwitching"] = "isAbrSwitching";
})(Option || (Option = {}));
export var AudioStatus;
(function (o21) {
    o21[o21["AUDIO_STATUS_DEFAULT"] = 0] = "AUDIO_STATUS_DEFAULT";
    o21[o21["AUDIO_STATUS_RESUME"] = 1] = "AUDIO_STATUS_RESUME";
    o21[o21["AUDIO_STATUS_PAUSE"] = 2] = "AUDIO_STATUS_PAUSE";
    o21[o21["AUDIO_STATUS_STOP"] = 3] = "AUDIO_STATUS_STOP";
    o21[o21["AUDIO_STATUS_DUCK"] = 4] = "AUDIO_STATUS_DUCK";
    o21[o21["AUDIO_STATUS_UNDUCK"] = 5] = "AUDIO_STATUS_UNDUCK";
})(AudioStatus || (AudioStatus = {}));
export var PropertyKey;
(function (n21) {
    n21[n21["RESPONSE_INFO"] = 0] = "RESPONSE_INFO";
    n21[n21["CONNECT_INFO"] = 1] = "CONNECT_INFO";
})(PropertyKey || (PropertyKey = {}));
export var AlphaRenderMode;
(function (m21) {
    m21[m21["RENDER_MODE_ALPHA_NONE"] = 0] = "RENDER_MODE_ALPHA_NONE";
    m21[m21["RENDER_MODE_ALPHA_AT_RIGHT"] = 1] = "RENDER_MODE_ALPHA_AT_RIGHT";
    m21[m21["RENDER_MODE_ALPHA_AT_LEFT"] = 2] = "RENDER_MODE_ALPHA_AT_LEFT";
    m21[m21["RENDER_MODE_ALPHA_AT_TOP"] = 3] = "RENDER_MODE_ALPHA_AT_TOP";
    m21[m21["RENDER_MODE_ALPHA_AT_BOTTOM"] = 4] = "RENDER_MODE_ALPHA_AT_BOTTOM";
})(AlphaRenderMode || (AlphaRenderMode = {}));
