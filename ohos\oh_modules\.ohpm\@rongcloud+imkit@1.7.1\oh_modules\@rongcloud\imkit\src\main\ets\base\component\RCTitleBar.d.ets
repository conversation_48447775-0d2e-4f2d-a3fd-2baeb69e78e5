// @keepTs
// @ts-nocheck
/**
 * @desc: 标题栏
 * <AUTHOR>
 * @date: 2023-11-08 14:05
 * @version 1.0.0
 */
@Component
export declare struct RCTitleBar {
    @Link
    model: RCTitleBar.Model;
    @BuilderParam
    customLeftView?: CustomBuilder;
    @BuilderParam
    customCenterView?: CustomBuilder;
    @BuilderParam
    customRightView?: CustomBuilder;
    private multipleTitleSwiperController;
    private multipleTitleDataSource;
    /**
     * 标题栏两边普通布局
     * @param type
     */
    @Builder
    normalBothSidesLayout(s5: RCTitleBar.BothSidesLayoutType): void;
    /**
     * 标题布局
     */
    @Builder
    normalCenterLayout(): void;
    @Builder
    blankLayout(): void;
    textOverflowConvert(q3: RCTitleBar.TitleTextOverflowType): TextOverflow;
    aboutToAppear(): void;
    build(): void;
}
export declare namespace RCTitleBar {
    /**
     * 标题栏样式
     */
    enum BarStyle {
        LIGHT = 1,
        NIGHT = 2,
        TRANSPARENT = 3,
        LINEARGRADIENT = 4
    }
    /**
     * 标题文本超长时处理显示策略
     */
    enum TitleTextOverflowType {
        NONE = 0,
        ELLIPSIS = 1,
        CLIP = 2,
        MARQUEE = 3,
        MULTIPLE = 4
    }
    /**
     * 左右两边的操作布局类型 左 右
     */
    enum BothSidesLayoutType {
        LEFT = 1,
        RIGHT = 2
    }
    /**
     * 左右图标位置 上 下 左 右
     */
    enum IconGravity {
        TOP = 1,
        BOTTOM = 2,
        LEFT = 3,
        RIGHT = 4
    }
    class Model {
        titleBarNormalPadding: number | Resource;
        titleBarMinHeight: number | Resource;
        titleBarStyle: BarStyle;
        titleBarBackground: ResourceColor;
        titleBarBottomLineColor: ResourceColor;
        titleBarBottomLineHeight: number | Resource;
        titleBarLinearGradient: TitleBarLinearGradient;
        /***************************************************************
         * 中间标题
         ***************************************************************/
        titleSpace: string | number;
        titleName: string;
        titleFontColor: ResourceColor;
        titleFontSize: number | string | Resource;
        titleFontWeight: number | FontWeight | string;
        titleTextOverflowType: TitleTextOverflowType;
        titleTextStyle: FontStyle;
        subTitleName: string;
        subTitleFontColor: ResourceColor;
        subTitleFontSize: number | string | Resource;
        subTitleFontWeight: number | FontWeight | string;
        subTitleTextOverflowType: TitleTextOverflowType;
        subTitleTextStyle: FontStyle;
        onTitleClickListener: () => void;
        /***************************************************************
         * 垂直跑马灯多文本动态显示效果
         ***************************************************************/
        multipleTitleList: string[];
        multipleTitleFontColor: ResourceColor;
        multipleTitleFontSize: number | string | Resource;
        multipleTitleFontWeight: number | FontWeight | string;
        multipleTitleTextOverflowType: TitleTextOverflowType;
        multipleTitleTextStyle: FontStyle;
        multipleTitleTextAlign: TextAlign;
        multipleTitlePlayInterval: number;
        multipleTitlePlayDuration: number;
        multipleTitlePlayItemSpace: number;
        /***************************************************************
         * 左边按钮
         ***************************************************************/
        leftTitleSpace: string | number;
        leftTitleStateNormalStyleColor: ResourceColor;
        leftTitleStatePressedStyleColor: ResourceColor;
        showLeftSideLayout: boolean;
        leftTitleBackground: ResourceColor;
        leftTitleName: string;
        leftTitleFontColor: ResourceColor;
        leftTitleFontSize: number | string | Resource;
        leftTitleFontWeight: number | FontWeight | string;
        leftIcon: string | PixelMap | Resource | null;
        leftIconWidth: number | string | Resource;
        leftIconHeight: number | string | Resource;
        leftIconPadding: Padding | Length;
        leftIconGravity: IconGravity;
        onLeftClickListener: () => void;
        /***************************************************************
         * 右边按钮
         ***************************************************************/
        rightTitleSpace: string | number;
        rightTitleStateNormalStyleColor: ResourceColor;
        rightTitleStatePressedStyleColor: ResourceColor;
        showRightSideLayout: boolean;
        rightTitleBackground: ResourceColor;
        rightTitleName: string;
        rightTitleFontColor: ResourceColor;
        rightTitleFontSize: number | string | Resource;
        rightTitleFontWeight: number | FontWeight | string;
        rightIcon: string | PixelMap | Resource | null;
        rightIconWidth: number | string | Resource;
        rightIconHeight: number | string | Resource;
        rightIconPadding: Padding | Length;
        rightIconGravity: IconGravity;
        onRightClickListener: () => void;
        constructor();
        setTitleBarNormalPadding(n2: number | Resource): Model;
        setTitleBarMinHeight(m2: number | Resource): Model;
        setTitleBarStyle(l2: BarStyle): Model;
        setTitleBarBackground(k2: ResourceColor): Model;
        setTitleBarBottomLineHeight(j2: number | Resource): Model;
        setTitleBarBottomLineColor(i2: ResourceColor): Model;
        setShowTitleBarBottomLine(h2: boolean): Model;
        setTitleBarLinearGradient(g2: TitleBarLinearGradient): Model;
        /***************************************************************
         * 中间标题
         ***************************************************************/
        setTitleSpace(f2: string | number): Model;
        setTitleName(e2: string): Model;
        setTitleFontColor(d2: ResourceColor): Model;
        setTitleFontSize(c2: number | string | Resource): Model;
        setTitleFontWeight(b2: number | FontWeight | string): Model;
        setTitleTextOverflowType(a2: TitleTextOverflowType): Model;
        setTitleTextStyle(z1: FontStyle): Model;
        setSubTitleName(y1: string): Model;
        setSubTitleFontColor(x1: ResourceColor): Model;
        setSubTitleFontSize(w1: number | string | Resource): Model;
        setSubTitleFontWeight(v1: number | FontWeight | string): Model;
        setSubTitleTextOverflowType(u1: TitleTextOverflowType): Model;
        setSubTitleTextStyle(t1: FontStyle): Model;
        setOnTitleClickListener(s1: () => void): Model;
        /***************************************************************
         * 垂直跑马灯多文本动态显示效果
         ***************************************************************/
        setMultipleTitleList(r1: string[]): Model;
        setMultipleTitleFontColor(q1: ResourceColor): Model;
        setMultipleTitleFontSize(p1: number | string | Resource): Model;
        setMultipleTitleFontWeight(o1: number | FontWeight | string): Model;
        setMultipleTitleTextOverflowType(n1: TitleTextOverflowType): Model;
        setMultipleTitleTextStyle(m1: FontStyle): Model;
        setMultipleTitleTextAlign(l1: TextAlign): Model;
        setMultipleTitlePlayInterval(k1: number): Model;
        setMultipleTitlePlayDuration(j1: number): Model;
        setMultipleTitlePlayItemSpace(i1: number): Model;
        /***************************************************************
         * 左边按钮
         ***************************************************************/
        setLeftTitleStateNormalStyleColor(h1: ResourceColor): Model;
        setLeftTitleStatePressedStyleColor(g1: ResourceColor): Model;
        setLeftTitleSpace(f1: string | number): Model;
        setShowLeftSideLayout(e1: boolean): Model;
        setLeftTitleName(d1: string): Model;
        setLeftTitleFontColor(c1: ResourceColor): Model;
        setLeftTitleFontSize(b1: number | string | Resource): Model;
        setLeftTitleFontWeight(a1: number | FontWeight | string): Model;
        setLeftIcon(z: string | PixelMap | Resource | null): Model;
        setLeftIconWidth(y: number | string | Resource): Model;
        setLeftIconHeight(x: number | string | Resource): Model;
        setLeftIconPadding(w: Padding | Length): Model;
        setLeftIconGravity(v: IconGravity): Model;
        setOnLeftClickListener(u: () => void): Model;
        setLeftTitleBackground(t: ResourceColor): Model;
        /***************************************************************
         * 右边按钮
         ***************************************************************/
        setRightTitleStateNormalStyleColor(s: ResourceColor): Model;
        setRightTitleStatePressedStyleColor(r: ResourceColor): Model;
        setRightTitleSpace(q: string | number): Model;
        setShowRightSideLayout(p: boolean): Model;
        setRightTitleName(o: string): Model;
        setRightTitleFontColor(n: ResourceColor): Model;
        setRightTitleFontSize(m: number | string | Resource): Model;
        setRightTitleFontWeight(l: number | FontWeight | string): Model;
        setRightIcon(k: string | PixelMap | Resource | null): Model;
        setRightIconWidth(j: number | string | Resource): Model;
        setRightIconHeight(i: number | string | Resource): Model;
        setRightIconPadding(h: Padding | Length): Model;
        setRightIconGravity(g: IconGravity): Model;
        setOnRightClickListener(f: () => void): Model;
        setRightTitleBackground(e: ResourceColor): Model;
        /**
         * 标题样式转换
         * @param value
         */
        private barStyleTransform;
    }
    class TitleBarLinearGradient {
        angle?: number | string;
        direction?: GradientDirection;
        colors?: Array<[
            ResourceColor,
            number
        ]>;
        repeating?: boolean;
    }
    class MultipleTitleDataSource implements IDataSource {
        private multipleTitleList;
        private listener?;
        constructor(c: string[]);
        totalCount(): number;
        getData(b: number): Object;
        registerDataChangeListener(a: DataChangeListener): void;
        unregisterDataChangeListener(): void;
    }
}
export default RCTitleBar;
