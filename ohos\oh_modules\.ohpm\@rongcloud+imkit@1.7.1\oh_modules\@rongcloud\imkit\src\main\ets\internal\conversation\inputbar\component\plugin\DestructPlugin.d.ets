// @keepTs
// @ts-nocheck
import { ConversationIdentifier } from '@rongcloud/imlib';
import { IBoardPlugin } from '../../../../../conversation/inputbar/component/plugin/IBoardPlugin';
/**
 * 阅后即焚插件标识
 * @version 1.0.0
 */
declare const DestructPluginName: string;
/**
 * 阅后即焚插件
 * @version 1.0.0
 */
declare class DestructPlugin implements IBoardPlugin {
    pluginName(): string;
    private onClickDestructPlugin?;
    constructor(w143?: () => void);
    obtainTitle(v143: Context): ResourceStr;
    obtainImage(u143: Context): ResourceStr;
    onClick(s143: Context, t143: ConversationIdentifier): void;
    onFilter(r143: ConversationIdentifier): boolean;
}
export { DestructPlugin, DestructPluginName };
