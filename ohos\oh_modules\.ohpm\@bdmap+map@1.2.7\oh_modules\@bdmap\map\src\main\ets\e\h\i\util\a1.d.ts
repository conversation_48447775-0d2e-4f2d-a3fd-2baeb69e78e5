import { LatLng } from '@bdmap/base'; export declare class PrismBuildingInfo { private static readonly TAG; constructor(); private getBuildingGeomAsync;             private getBuildingGeom;             private getBuildingCenter; private resolveBuildingGeomStr; getGeomPointList(h49: string): Promise<LatLng[]>; getGeomPointList(h49: string, callback: Function): void;             getAoiPointList(x48: string, callback: Function): void;             getRecognizeAoiPointList(t48: string): LatLng[] | null;       private deGenerateLatlngString;               private onDiffLatLngs; getBuildingInfoCenter(z47: string): Promise<LatLng>; getBuildingInfoCenter(z47: string, callback: Function): void; private parseBuildingInfoCenter; } 