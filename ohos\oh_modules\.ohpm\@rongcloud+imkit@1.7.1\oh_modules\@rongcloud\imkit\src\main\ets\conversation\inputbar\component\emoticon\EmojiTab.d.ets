// @keepTs
// @ts-nocheck
import { IEmoticonTab } from './IEmoticonTab';
import { ConversationIdentifier } from '@rongcloud/imlib';
/**
 * emoji表情扩展名字
 * @version 1.5.1
 */
declare const EmojiTabName = "RCEmoji";
/**
 * emoji表情扩展
 */
declare class EmojiTab extends IEmoticonTab {
    obtainTabName(): string;
    obtainTabDrawable(t25: Context): ResourceStr;
    obtainTabPager(s25: Context): WrappedBuilder<[
        Context,
        ConversationIdentifier,
        IEmoticonTab
    ]>;
    onTableSelected(q25: Context, r25: number): void;
}
export { EmojiTabName, EmojiTab };
