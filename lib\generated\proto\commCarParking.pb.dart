//
//  Generated code. Do not modify.
//  source: commCarParking.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

/// 主题： comm/{env}/vehicle/parking/status/{vin} ,其中测试环境env为pre，生产环境env为prod
/// 如若UCU在整车CAN采集不到相关数据，默认填充255上报(string格式，内容为255)
class CarParkingStatus extends $pb.GeneratedMessage {
  factory CarParkingStatus({
    $fixnum.Int64? collectTime,
    $core.String? aPPInfcSw,
    $core.String? vehCntrlSt,
    $core.String? iPAOperSts,
    $core.String? vPAOperSts,
    $core.String? iPAFailrReas,
    $core.String? vPAgFailrReas,
  }) {
    final $result = create();
    if (collectTime != null) {
      $result.collectTime = collectTime;
    }
    if (aPPInfcSw != null) {
      $result.aPPInfcSw = aPPInfcSw;
    }
    if (vehCntrlSt != null) {
      $result.vehCntrlSt = vehCntrlSt;
    }
    if (iPAOperSts != null) {
      $result.iPAOperSts = iPAOperSts;
    }
    if (vPAOperSts != null) {
      $result.vPAOperSts = vPAOperSts;
    }
    if (iPAFailrReas != null) {
      $result.iPAFailrReas = iPAFailrReas;
    }
    if (vPAgFailrReas != null) {
      $result.vPAgFailrReas = vPAgFailrReas;
    }
    return $result;
  }
  CarParkingStatus._() : super();
  factory CarParkingStatus.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory CarParkingStatus.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'CarParkingStatus', createEmptyInstance: create)
    ..aInt64(1, _omitFieldNames ? '' : 'collectTime', protoName: 'collectTime')
    ..aOS(2, _omitFieldNames ? '' : 'APPInfcSw', protoName: 'APPInfcSw')
    ..aOS(3, _omitFieldNames ? '' : 'VehCntrlSt', protoName: 'VehCntrlSt')
    ..aOS(4, _omitFieldNames ? '' : 'IPAOperSts', protoName: 'IPAOperSts')
    ..aOS(5, _omitFieldNames ? '' : 'VPAOperSts', protoName: 'VPAOperSts')
    ..aOS(6, _omitFieldNames ? '' : 'IPAFailrReas', protoName: 'IPAFailrReas')
    ..aOS(7, _omitFieldNames ? '' : 'VPAgFailrReas', protoName: 'VPAgFailrReas')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  CarParkingStatus clone() => CarParkingStatus()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  CarParkingStatus copyWith(void Function(CarParkingStatus) updates) => super.copyWith((message) => updates(message as CarParkingStatus)) as CarParkingStatus;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static CarParkingStatus create() => CarParkingStatus._();
  CarParkingStatus createEmptyInstance() => create();
  static $pb.PbList<CarParkingStatus> createRepeated() => $pb.PbList<CarParkingStatus>();
  @$core.pragma('dart2js:noInline')
  static CarParkingStatus getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<CarParkingStatus>(create);
  static CarParkingStatus? _defaultInstance;

  @$pb.TagNumber(1)
  $fixnum.Int64 get collectTime => $_getI64(0);
  @$pb.TagNumber(1)
  set collectTime($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasCollectTime() => $_has(0);
  @$pb.TagNumber(1)
  void clearCollectTime() => clearField(1);

  @$pb.TagNumber(2)
  $core.String get aPPInfcSw => $_getSZ(1);
  @$pb.TagNumber(2)
  set aPPInfcSw($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasAPPInfcSw() => $_has(1);
  @$pb.TagNumber(2)
  void clearAPPInfcSw() => clearField(2);

  @$pb.TagNumber(3)
  $core.String get vehCntrlSt => $_getSZ(2);
  @$pb.TagNumber(3)
  set vehCntrlSt($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasVehCntrlSt() => $_has(2);
  @$pb.TagNumber(3)
  void clearVehCntrlSt() => clearField(3);

  @$pb.TagNumber(4)
  $core.String get iPAOperSts => $_getSZ(3);
  @$pb.TagNumber(4)
  set iPAOperSts($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasIPAOperSts() => $_has(3);
  @$pb.TagNumber(4)
  void clearIPAOperSts() => clearField(4);

  @$pb.TagNumber(5)
  $core.String get vPAOperSts => $_getSZ(4);
  @$pb.TagNumber(5)
  set vPAOperSts($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasVPAOperSts() => $_has(4);
  @$pb.TagNumber(5)
  void clearVPAOperSts() => clearField(5);

  @$pb.TagNumber(6)
  $core.String get iPAFailrReas => $_getSZ(5);
  @$pb.TagNumber(6)
  set iPAFailrReas($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasIPAFailrReas() => $_has(5);
  @$pb.TagNumber(6)
  void clearIPAFailrReas() => clearField(6);

  @$pb.TagNumber(7)
  $core.String get vPAgFailrReas => $_getSZ(6);
  @$pb.TagNumber(7)
  set vPAgFailrReas($core.String v) { $_setString(6, v); }
  @$pb.TagNumber(7)
  $core.bool hasVPAgFailrReas() => $_has(6);
  @$pb.TagNumber(7)
  void clearVPAgFailrReas() => clearField(7);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
