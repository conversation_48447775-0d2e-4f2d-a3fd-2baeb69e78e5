{"name": "@bdmap/base", "author": "baidu_openmap", "version": "1.2.7", "license": "Apache-2.0", "keywords": ["百度", "地图", "基础库"], "description": "适配HarmonyOS环境的一款地图SDK base包", "dependencies": {"@types/libbaidumapsdk_base_for_js_v1_0_0.so": "file:./src/main/cpp/type", "@bdmap/verify": "1.0.2"}, "types": "Index.d.ts", "artifactType": "obfuscation", "metadata": {"byteCodeHar": true, "sourceRoots": ["./src/main"], "debug": false, "dependencyPkgVersion": {"@bdmap/verify": "1.0.2"}, "declarationEntry": [], "nativeDebugSymbol": true, "useNormalizedOHMUrl": true}, "compatibleSdkVersion": 12, "compatibleSdkType": "HarmonyOS", "obfuscated": true}