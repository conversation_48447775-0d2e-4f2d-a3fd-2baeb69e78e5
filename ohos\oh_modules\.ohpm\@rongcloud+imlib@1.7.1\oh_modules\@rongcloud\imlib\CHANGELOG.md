# 更新日志

## 1.7.1-enterprise
发版时间：2025/7/25
- 公有云 SDK 1.7.1 的私有云版本。

## 1.7.1
发版时间：2025/7/25
### Lib
- UserInfo 增加 `userType`

## 1.7.0-enterprise
发版时间：2025/7/15
- 解决了部分Bug。

## 1.6.0-enterprise
发版时间：2025/6/27
- 公有云 SDK 1.6.0 的私有云版本。

## 1.6.0
发版时间：2025/6/27
- 消息已读 V5 新增批量查询消息已读回执状态接口。
- 增加 `CombineV2Message` 内置消息。
- 增加 `ReadReceiptMessage` 内置消息。
- 增加 `setReadTimestamp` 接口。
- 增加 `removeRemoteConversations` 接口。

## 1.5.3-enterprise
发版时间：2025/6/20
- `RC:ReadNtf` 消息的 `last_message_type` 字段兼容其他端发来的字符串类型（rust）。

## 1.5.2-enterprise
发版时间：2025/6/13
- unwrap 优化（rust）。

## 1.5.1-enterprise
发版时间：2025/6/10
- 日志数据库存储条数上限修改为 8 万条（rust）。

## 1.5.0-enterprise
发版时间：2025/5/29
- 公有云 SDK 1.5.0 的私有云版本。

## 1.5.0
发版时间：2025/5/29
- （对外）新增：批量获取本地和远端消息接口`getMessages`。
- （对外）新增：已读回执 V5 功能。

## 1.4.5-enterprise
发版时间：2025/5/27
- 离线消息拉取问题修复，参数调整版。

## 1.4.4-enterprise
发版时间：2025/5/15
- 离线消息拉取问题修复。

## 1.4.3-enterprise
发版时间：2025/4/29
- 公有云 SDK 1.4.3 的私有云版本。

## 1.4.3
发版时间：2025/4/29
- 新增：图片消息的消息体增加缩略图宽高字段
- 优化：多媒体消息下载后的文件名默认为消息中自带的文件名字段

## 1.4.2-enterprise
发版时间：2025/4/15
- 修复了若干个 Bug。

## 1.4.1-enterprise
发版时间：2025/4/2
- 增加了设置消息附加信息的接口

## 1.4.0-enterprise.3
发版时间：2025/3/28
- 增加了媒体文件自定义上传下载功能
- 增加了媒体消息取消发送功能
- 修复了若干个 Bug。

## 1.4.0-enterprise.2
发版时间：2025/3/5
- 修复了若干个 Bug。

## 1.4.0-enterprise.1
发版时间：2025/2/28
- 公有云 SDK 1.4.0 的私有云版本。

## 1.3.4
发版时间：未发布
- `InitOption` 增加属性 `env`
- 新增：获取本地时间与服务器时间的时间差接口 `getDeltaTime`

## 1.3.2-enterprise.1
发版时间：未发布
- 公有云 SDK 1.3.2 的私有云版本。

## 1.3.3
发版时间：2025/1/14
- 修复了若干个 Bug 。

## 1.3.2
发版时间：2025/1/10
- 修复了在会话没有未读数情况下，再次拉取该会话远端历史消息，该会话可能出现未读数异常的问题。
- 修复了聊天室成员变化监听 `setChatroomMemberListener` 、 `addChatroomMemberListener` 不生效的问题。

## 1.3.1-enterprise.1
发版时间：2024/12/30
- 基于 1.3.1 版本发布的私有云版本。

## 1.3.1
发版时间：2024/12/27
- 处理 32061 错误码逻辑，调整为清空本地导航缓存并自动重连。
- 修复单聊 `Message` 的 `senderId` 字段的错乱问题。
- 修复连接方法回调之前，调用断开连接可能会发生死锁的问题。
- 修复同步单聊消息未读数时会清除会话对方的未读数的问题。
- 修复偶现的进入聊天室后切换到后台一分钟以上，再切回前台后无法再接收聊天室消息的问题。
- 修复发消息过程中杀进程，再次打开后获取消息的发送状态依然是发送中的问题。
- 修复 `connect` 接口回调连接成功之前直接调用 `disconnect` 接口导致偶现的崩溃问题。

## 1.3.0
发版时间：2024/12/12
- 修改：加入聊天室接口和加入已存在聊天室接口的消息个数保持和 iOS Android 一致

### 消息
- 新增：发送定义消息方法 `sendDirectionalMessage`
- 新增：发送定义媒体消息方法 `sendDirectionalMediaMessage`
- 新增：下载媒体消息方法（含下载进度）`downloadMediaMessageWithProgress`
- 新增：取消下载方法 `cancelDownloadMediaMessage`
- 新增：下载媒体文件方法（含下载进度）`downloadFileWithProgress`
- 新增：取消下载文件方法 `cancelDownloadFile`
- 新增：设置消息接收状态 `setMessageReceivedStatus`

### 用户配置
- 新增：显示推送详情 `setPushContentShowStatus` & `getPushContentShowStatus`
- 新增：Web 端在线时，手机端是否接收推送 `setPushReceiveStatus` & `getPushReceiveStatus`

### 搜索
- 新增：在指定的一批会话中搜索消息 `searchMessagesByConversations`
- 新增：在本地指定会话中搜索多个成员指定的消息类型 `searchMessagesByUsers`

### 会话
- 新增属性：`ISetConversationTopOption` 增加属性 `isNeedUpdateTime` 标识是否更新会话时间

### 监听
IMEngine 监听支持多实例
- 废弃 `setConnectionStatusListener`
  - 新增 `addConnectionStatusListener` & `removeConnectionStatusListener`
  - 新增监听 `ConnectionStatusListener`

- 废弃 `setMessageReceivedListener`
  - 新增 `addMessageReceivedListener` & `removeMessageReceivedListener`
  - 新增监听 `MessageReceivedListener`

- 废弃 `setMessageRecalledListener`
  - 新增 `addMessageRecalledListener` & `removeMessageRecalledListener`
  - 新增监听 `MessageRecalledListener`

- 废弃 `setMessageBlockedListener`
  - 新增 `addMessageBlockedListener` & `removeMessageBlockedListener`
  - 新增监听 `MessageBlockedListener`

- 废弃 `setTypingStatusListener`
  - 新增 `addTypingStatusListener` & `removeTypingStatusListener`
  - 新增监听 `TypingStatusListener`

- 废弃 `setConversationStatusListener`
  - 新增 `addConversationStatusListener` & `removeConversationStatusListener`
  - 新增监听 `ConversationStatusListener`

- 废弃 `setMessageExpansionListener`
  - 新增 `addMessageExpansionListener` & `addMessageExpansionListener`

- 废弃 `setDatabaseStatusListener`
  - 新增 `addDatabaseStatusListener` & `removeDatabaseStatusListener`
  - 新增监听 `DatabaseStatusListener`

- 废弃 `setSyncConversationReadStatusListener`
  - 新增 `addSyncConversationReadStatusListener` & `removeSyncConversationReadStatusListener`


- 废弃 `setChatroomStatusListener`
  - 新增 `addChatroomStatusListener` & `removeChatroomStatusListener`

- 废弃 `setChatroomKVStatusListener`
  - 新增 `addChatroomKVStatusListener` & `removeChatroomKVStatusListener`

- 废弃 `setChatroomMemberListener`
  - 新增 `addChatroomMemberListener` & `removeChatroomMemberListener`

- 废弃 `setChatroomNotifyEventListener`
  - 新增 `addChatroomNotifyEventListener` & `removeChatroomNotifyEventListener`

### 消息已读V1
- Message 新增 `ReadReceiptInfo`
- SentStatus 新增 `Read`
- 新增 `sendReadReceiptMessage`
- 新增 `sendReadReceiptRequest` & `sendReadReceiptResponse`
- 新增 `addMessageReadReceiptListener` & `removeMessageReadReceiptListener`
  - 新增监听 `MessageReadReceiptListener`

## 1.2.0
发版时间：2024/11/01

- 新增：完全支持API12 https://developer.huawei.com/consumer/cn/notice/20241010/
- 修复：修复删除本地消息接口崩溃问题
- 新增：新增查询会话接口 `searchConversationsWithResult`
- 废弃：废弃接口 `searchConversations`，用 `searchConversationsWithResult` 替代
- 新增：channelId
- 修复：无法清空草稿

### 连接
- 新增：设置断线重连时是否踢出重连设备接口 `setReconnectKickEnable`

### 消息
- Message 扩展
  - 新增字段 `canIncludeExpansion` & `expansion`
  - 新增：消息扩展监听 `MessageExpansionListener`
  - IMEngine 新增方法
    - 设置扩展监听：`setMessageExpansionListener`
    - 更新扩展：`updateMessageExpansion`
    - 移除扩展：`removeMessageExpansion`
- 新增：插入单条消息方法 `insertMessage`
- 新增普通消息：
  - 命令消息：`CommandMessage`
  - 命令提醒消息类（小灰条）：`CommandNotificationMessage`
  - 通知类消息（小灰条）：`InformationNotificationMessage`
  - 引用消息：`ReferenceMessage`
  - 图文消息：`RichContentMessage`
- 新增媒体消息：
  - GIF 消息：`GIFMessage`
  - 小视频消息：`SightMessage`
- 撤回消息新增字段 `originalMessageContent`

### 会话
- 新增： `SearchConversationResult`

## 1.1.0
发布日期：2024/08/29

**重要**
- 枚举值：`NaviRespLicenseExpired` 改为 `ConnectLicenseExpired`
- 枚举值：`CmpRecvTimeOut` 改为 `SocketRecvTimeout`
- ChatroomStatusListener 从抽象类改为 interface，如果报错，需要删除 entry/build 目录重新编译
- 修复：IM 方法回调中再调用 IM 方法，偶现的卡死情况
- 修复：IM 连接偶现的 31002。原因是偶现读取的系统版本号类似 `OpenHarmony-5.0.0.36` 这类包含 `-` 的特殊字符无法被 IM 服务识别
- 修复：https 请求偶现的 openssl 崩溃
- 修复：手机网络切换时偶现的崩溃


### 数据库
**新增功能**
- 新增枚举值
  - DatabaseStatus
- 新增监听
  - setDatabaseStatusListener

### 消息
**新增功能**
- 新增位置消息：LocationMessage
- 新增方法
  - deleteRemoteMessages

**问题修复**
- 修复：发送失败的消息进行重发，出现一条失败消息一条成功消息
- 修复：发送失败的消息，Message 对象为空
- 修复：媒体消息发送失败，从数据库获取的发送状态为发送中，期望为发送失败
- 修复：IGetRemoteMsgOption isCheckDup 错误
- 修复：UserInfo decode 时错误的将 userId 解析为 id


### 推送
**新增功能**
- 新增类
  - IosConfig
  - AndroidConfig
  - HarmonyConfig

### 会话

**新增功能**
- 新增方法
  - getUnreadConversations
  
**问题修复**
  - 修复：置顶或者免打扰之后，Conversation lastSentTime 被错误的更新为 lastOperateTime 

### 搜索
**新增功能**

- 新增方法
  - searchConversations
  - searchMessages
  - searchMessagesInTimeRange
  - searchMessagesByUser
- 新增类
  - ISearchMessageInTimeRangeOption

### 聊天室
**新增功能**

- 新增方法
  - setChatroomEntries
  - deleteChatroomEntries
  - getChatroomEntries
  - getAllChatroomEntries
  - setChatroomKVStatusListener
  - setChatroomMemberListener
- 新增类
  - ChatroomKVStatusListener
  - ChatroomMemberAction
  - ChatroomMemberActionListener
  - ChatroomNotifyEventListener
  - ChatroomSyncEvent
  - ChatroomMemberBlockEvent
  - ChatroomMemberBanEvent
- 新增枚举值
  - ChatroomMemberActionType


### 公众号
**公众号功能仅支持私有云**

**新增功能**

- 新增会话类型
  - 应用公众服务：AppPublicService
- 新增类
  - PublicServiceMenuItem
  - PublicServiceInfo
- 新增枚举值
  - PublicServiceMenuItemType
- 新增方法
  - getPublicServiceList
  - getPublicService
  

### 输入状态
**新增功能**
- 新增方法
  - setTypingStatusListener
  - sendTypingStatus
  - setTypingStatusInterval

## 1.0.3

发布日期：2024/08/06

**新增功能**
- 鸿蒙要求上架的 SDK 支持字节码，1.0.3 版本开始支持字节码
  - app 需要修改配置 **useNormalizedOHMUrl**，详细见融云官网鸿蒙 FAQ 文档

**问题修复**
- 修复：置顶或者免打扰之后，Conversation lastSentTime 被错误的更新为 lastOperateTime

## 1.0.2

发布日期：2024/07/10

**新增功能**
- 新增加入聊天室方法：**joinChatroom**
- 新增错误码：
    - InvalidArgumentSenderId = 34421
    - InvalidArgumentPushNotificationMuteLevelVec = 34422,
    - NaviLicenseMismatch = 30026
    - SocketConnectionFailed = 31014
    - SocketShutdownFailed = 31015
    - ConnectionCancel = 31016
    - RequestUploadTokenSizeError = 26107
- 新增连接状态
    - DisconnectLicenseMismatch

**问题修复**
- TS 层 bool 值被错误转化为 0、1，而非 bool 值
- 平台统一性：为保持与其他平台统一，UserInfo 废弃 portraitUrl，新增 portraitUri
- 私有云：需要根据导航下发信息，判断当前 License 是否支持鸿蒙平台，如不支持则报错：NaviLicenseMismatch
- 私有云：支持导航 V2。


## 1.0.1

发布日期：2024/07/02

**新增功能**

- 增加 **x86_64** 架构，支持 **Windows(64-bit)-模拟器** 和 **Mac(x86)-模拟器**

## 1.0.0

发布日期：2024/07/01

- **鸿蒙 SDK 初版**
