import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import { Reply, WebViewFlutterApi, WebViewHostApi } from './GeneratedOhosWebView';
import { InstanceManager } from './InstanceManager';
import { WebViewPlatformView } from './WebViewHostApiImpl';
export declare class WebViewFlutterApiImpl {
    private binaryMessenger;
    private instanceManager;
    private api;
    constructor(binaryMessenger: BinaryMessenger, instanceManager: InstanceManager);
    create(instance: WebViewPlatformView, callback: Reply<void>): void;
    setApi(api: WebViewFlutterApi): void;
    onScrollChanged(instance: WebViewHostApi, left: number, top: number, oldLeft: number, oldTop: number, callback: Reply<void>): void;
}
