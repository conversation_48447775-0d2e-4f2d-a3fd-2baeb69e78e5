import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_uikit/ui_image.dart';
import 'package:wuling_flutter_app/page/store/store_navbar.dart';
import 'package:wuling_flutter_app/widgets/webview/webview.dart';

import '../../../../api/report_api/report_api.dart';
import '../../../../common/action.dart';
import '../../../../models/community/report/report_order_item_model.dart';
import '../../../../utils/manager/log_manager.dart';
import '../report_navbar.dart';

import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wuling_flutter_app/widgets/common/custom_refresher.dart';

class ReportMessageOrderPage extends StatefulWidget {
  const ReportMessageOrderPage({super.key});

  @override
  State<ReportMessageOrderPage> createState() => _ReportMessageOrderPageState();
}

class _ReportMessageOrderPageState extends State<ReportMessageOrderPage> {
  List<dynamic> dataSource = [];
  final RefreshController _refreshController =
  RefreshController(initialRefresh: false);
  int _pageNo = 1;

  @override
  void initState() {
    _onRefresh();
    super.initState();
  }

  void _onRefresh() async {
    // monitor network fetch
    _pageNo = 1;
    reportApi.getReportOrderReminding(secondMessageType: "order_reminding",pageNo: _pageNo).then((value) {
      setState(() {
        dataSource = value;
      });
      LogManager().debug('${dataSource.length}');
    });
    await Future.delayed(Duration(milliseconds: 1000));
    _refreshController.refreshCompleted();
  }

  void _onLoading() async {
    _pageNo += 1;
    reportApi.getReportOrderReminding(secondMessageType: "order_reminding",pageNo: _pageNo).then((value) {
      setState(() {
        dataSource.addAll(value);
      });
    });
    await Future.delayed(Duration(milliseconds: 1000));
    _refreshController.loadComplete();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: UINavbar(text: "订单提醒",),
      backgroundColor: Color(0xFFF8F8F8),
      body: SmartRefresher(
        enablePullDown: true,
        enablePullUp: true,
        controller: _refreshController,
        onRefresh: _onRefresh,
        onLoading: _onLoading,
        header: AnimatedRefreshHeader(),
        footer: AnimatedRefreshFooter(),
        child:dataSource.isEmpty ? const Center(
          child: UIImage(width: 200,imgStr: "assets/images/community/normal.jpg",),
        ) : ListView.builder(itemBuilder: (ctx,i)=>ReportMessageOrderItem(model: dataSource[i],),itemCount: dataSource.length,)
      ),
    );
  }

  @override
  void dispose() {
    // TODO: implement dispose
    _refreshController?.dispose();
    super.dispose();
  }
}


class ReportMessageOrderItem extends StatelessWidget{

  final ReportOrderItemModel model;

  const ReportMessageOrderItem({super.key, required this.model});

  String getContent(){
    String template = model.messageContentVo["contentTemplate"];
    Map param = model.messageContentVo["paramMap"] ?? {};
    if(param.length>1){
      String text = template;
      if(param.containsKey("channel")){
        text = text.replaceAll("{channel}", param['channel']);
      }
      if(param.containsKey("name")){
        text = text.replaceAll("{name}", param['name']);
      }
      if(param.containsKey("time")){
        text = text.replaceAll("{time}", param['time']);
      }
      if(param.containsKey("assistant")){
        text = text.replaceAll("{assistant}", param['assistant']);
      }
      if(param.containsKey("technician")){
        text = text.replaceAll("{technician}", param['technician']);
      }
      if(param.containsKey("dealerFullname")){
        text = text.replaceAll("{dealerFullname}", param['dealerFullname']);
      }
      if(param.containsKey("nextMaintenanceTime")){
        text = text.replaceAll("{nextMaintenanceTime}", param['nextMaintenanceTime']);
      }
      if(param.containsKey("customer")){
        text = text.replaceAll("{customer}", param['customer']);
      }
      if(param.containsKey("plateNumber")){
        text = text.replaceAll("{plateNumber}", param['plateNumber']);
      }
      if(param.containsKey("serialNumber")){
        text = text.replaceAll("{serialNumber}", param['serialNumber']);
      }
      if(param.containsKey("storeName")){
        text = text.replaceAll("{storeName}", param['storeName']);
      }
      return text;
    }else{
      if(param.containsKey("comment")){
        return template.replaceAll("{comment}", param['comment']);
      }else if(param.containsKey("postContent")){
        return template.replaceAll("{postContent}", param['postContent']);
      }else if(param.containsKey("nickName")){
        return template.replaceAll("{nickName}", param['nickName']);
      }else if(param.containsKey("firstGoodsName")){
        return template.replaceAll("{firstGoodsName}", param['firstGoodsName']);
      }else if(param.containsKey("count")){
        return template.replaceAll("{count}", param['count'].toString());
      }else if(param.containsKey("callBackTime")){
        return template.replaceAll("{callBackTime}", param['callBackTime']);
      }else{
        return template;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 120,
      decoration: const BoxDecoration(
          color: Colors.white,
          border: Border(bottom: BorderSide(width: 1,color: Color(0xfff3f3f3)))
      ),
      padding: const EdgeInsets.symmetric(vertical: 10,horizontal: 20),
      child: Column(
        children: [
          Expanded(
            flex: 1,
            child: Row(
              children: [
                UIImage(imgStr: model.userBaseInfo['photo'],radius: 10,width: 20,height: 20,margin: EdgeInsets.only(right: 10),),
                Expanded(flex: 1,child: Text(model.userBaseInfo['nickname']),),
                Text('${DateTime.fromMillisecondsSinceEpoch(model.time).toLocal()}'),
              ],
            ),
          ),
          Row(
            children: [
              Expanded(child: GestureDetector(
                onTap: (){
                  if(model.skipType == 3){
                    // 跳转到网页
                    NavigatorAction.init(context,view: WebViewPage(url: model.skipTarget,));
                  }
                },
                child: Container(
                  color: Colors.white,
                  child: Text(getContent(),maxLines: 2,),
                ),
              )),
              // UIImage(imgStr: model.image,width: 120,height: 80,),
            ],
          )
        ],
      ),
    );
  }
}