import BmBaseMarker from "./c3"; import type BmDrawableResource from "./f2/l3"; export default class BmIconMarker extends BmBaseMarker { private mBmpResId; private mDrawableRes; constructor();                     setDrawableResource(p11: BmDrawableResource): void;           setBmpResourceId(o11: number): void;           setAnimationType(type: number): void;           setColor(n11: number): void; destroy(delay?: boolean): void; } 