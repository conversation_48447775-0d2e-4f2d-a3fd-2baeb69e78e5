export class AIOCrashTimeUtils {
    static formatTimestamp(q6) {
        const r6 = new Date(q6);
        const s6 = r6.getFullYear();
        const t6 = (r6.getMonth() + 1).toString().padStart(2, '0');
        const u6 = r6.getDate().toString().padStart(2, '0');
        const v6 = r6.getHours().toString().padStart(2, '0');
        const w6 = r6.getMinutes().toString().padStart(2, '0');
        const x6 = r6.getSeconds().toString().padStart(2, '0');
        return `${s6}${t6}${u6}${v6}${w6}${x6}`;
    }
}
