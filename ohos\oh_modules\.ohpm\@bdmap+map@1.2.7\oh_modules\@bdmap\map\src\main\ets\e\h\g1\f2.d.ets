import type { LatLng } from '@bdmap/base'; import { MapEvent, Event } from "./h1"; interface border { width?: Length; color?: ResourceColor; radius?: Length; style?: BorderStyle; } export interface displayClass { densityDPI?: number; } export interface component { show?: boolean; width?: Length; height?: Length; x?: Length; y?: Length; left?: Length; right?: Length; top?: Length; bottom?: Length; }  @Observed export declare class BoxRect { constructor(width: Length, height: Length); width: Length; height: Length; }  @Observed export declare class IconStyleObj { constructor(u: iconStyleObj); width: Length | undefined; height: Length | undefined; margin: Margin | undefined; padding: Padding | undefined; border: border | undefined; }  @Observed export declare class PanelStyleObj { constructor(t: panelStyleObj); flexDirection: string | undefined; alignContent: string | undefined; justifyContent: FlexAlign | undefined; alignItems: string | undefined | VerticalAlign | HorizontalAlign; minWidth: Length | undefined; maxWidth: Length | undefined; minHeight: Length | undefined; margin: Margin | undefined; padding: Padding | undefined; border: border | undefined; backgroundColor: ResourceColor | undefined; zIndex: number | undefined; }  @Observed export declare class TitleStyleObj { constructor(o: titleStyleObj); minWidth: Length | undefined; maxWidth: Length | undefined; margin: Margin | undefined; padding: Padding | undefined; border: border | undefined; textAlign: TextAlign | undefined; fontSize: number | undefined; color: ResourceColor | undefined; fontWeight: FontWeight | number | undefined; }  @Observed export declare class ControlStyleObj { constructor(m: controlStyleObj); Alignment: Alignment | undefined; mapViewId: string; x: Length | undefined; y: Length | undefined; show: boolean | undefined; } export interface controlStyleObj { mapViewId?: string; Alignment?: Alignment; x?: Length; y?: Length; show?: boolean; } export interface mapBoxRect { width: Length; height: Length; } export interface iconStyleObj { width?: Length; height?: Length; margin?: Margin; padding?: Padding; border?: border; } export interface titleStyleObj { minWidth?: Length; maxWidth?: Length; padding?: Padding; margin?: Margin; textAlign?: TextAlign; fontSize?: number; color?: ResourceColor; fontWeight?: FontWeight | number; } export interface panelStyleObj { flexDirection?: string; justifyContent?: FlexAlign; alignItems?: string | VerticalAlign | HorizontalAlign; alignContent?: string; minWidth?: Length; maxWidth?: Length; minHeight?: Length; margin?: Margin; padding?: Padding; border?: border; backgroundColor?: ResourceColor; zIndex?: number; } export interface gestureMeta { stopTime: number; global: boolean; pan: boolean; rotate: boolean; pinch: boolean; panTwo: boolean; } export interface infoPanel { uuid: string; icon: string; title: string; type: number; iconStyle: IconStyleObj; titleStyle: TitleStyleObj; panelStyle: PanelStyleObj; mapBox: BoxRect; position: LatLng; width: number; height: number; yOffset: number; left: number; top: number; } export { MapEvent, Event }; 