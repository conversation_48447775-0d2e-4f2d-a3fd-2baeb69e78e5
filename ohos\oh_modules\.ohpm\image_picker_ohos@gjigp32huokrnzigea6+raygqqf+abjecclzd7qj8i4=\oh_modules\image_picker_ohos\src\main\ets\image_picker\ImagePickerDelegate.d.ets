import ArrayList from '@ohos.util.ArrayList';
import common from '@ohos.app.ability.common';
import ImagePickerCache from './ImagePickerCache';
import FileUtils from './FileUtils';
import { ImageSelectionOptions, VideoSelectionOptions, Result, CacheRetrievalResult, MediaSelectionOptions, GeneralOptions } from './Messages';
import ImageResizer from './ImageResizer';
import UIAbility from '@ohos.app.ability.UIAbility';
import photoAccessHelper from "@ohos.file.photoAccessHelper";
export default class ImagePickerDelegate {
    readonly REQUEST_CODE_CHOOSE_IMAGE_FROM_GALLERY = 2342;
    readonly REQUEST_CODE_TAKE_IMAGE_WITH_CAMERA = 2343;
    readonly REQUEST_CAMERA_IMAGE_PERMISSION = 2345;
    readonly REQUEST_CODE_CHOOSE_MULTI_IMAGE_FROM_GALLERY = 2346;
    readonly REQUEST_CODE_CHOOSE_MEDIA_FROM_GALLERY = 2347;
    readonly REQUEST_CODE_CHOOSE_VIDEO_FROM_GALLERY = 2352;
    readonly REQUEST_CODE_TAKE_VIDEO_WITH_CAMERA = 2353;
    readonly REQUEST_CAMERA_VIDEO_PERMISSION = 2355;
    private static TAG;
    private imageResizer;
    private cache;
    private pendingCameraMediaUri;
    private pendingCallState;
    private context;
    private photoPicker;
    private cameraPosition;
    constructor(ability: UIAbility, imageResizer: ImageResizer, cache: ImagePickerCache, context?: common.UIAbilityContext, pendingImageOptions?: ImageSelectionOptions, pendingVideoOptions?: VideoSelectionOptions, result?: Result<ArrayList<string>>, fileUtils?: FileUtils);
    setCameraDevice(device: CameraDevice): void;
    saveStateBeforeResult(): void;
    retrieveLostImage(): Promise<CacheRetrievalResult | null>;
    chooseMedia(maxMultiple: number, handleType: string, type?: photoAccessHelper.PhotoViewMIMETypes): void;
    handleResultType(handleType: string, code: number, uris: Array<string>): void;
    chooseMediaFromGallery(options: MediaSelectionOptions, generalOptions: GeneralOptions, result: Result<ArrayList<string>>): void;
    handleChooseMediaResult(code: number, uris: Array<string>): void;
    handleMediaResultTwo(uris: Array<string>): Promise<void>;
    chooseVideoFromGallery(options: VideoSelectionOptions, usePhotoPicker: boolean, result: Result<ArrayList<string>>): void;
    handleChooseVideoResult(code: number, uris: Array<string>): void;
    takeVideoWithCamera(options: VideoSelectionOptions, result: Result<ArrayList<string>>): void;
    launchTakeVideoWithCameraWant(duration: number): Promise<void>;
    chooseImageFromGallery(options: ImageSelectionOptions, usePhotoPicker: boolean, result: Result<ArrayList<string>>): void;
    handleChooseImageResult(code: number, uris: Array<string>): void;
    chooseMultiImagesFromGallery(options: ImageSelectionOptions, usePhotoPicker: boolean, result: Result<ArrayList<string>>): void;
    takeImageWithCamera(options: ImageSelectionOptions, result: Result<ArrayList<string>>): void;
    launchTakeImageWithCameraWant(): void;
    setPendingOptionsAndResult(imageOptions: ImageSelectionOptions | null, videoOptions: VideoSelectionOptions | null, result: Result<ArrayList<string>>): boolean;
    finishWithAlreadyActiveError(result: Result<ArrayList<string>>): void;
    handlerCaptureImageResult(code: number, path: string): void;
    handlerCaptureVideoResult(code: number, path: string): void;
    handleImageResult(path: string, shouldDeleteOriginalIfScaled: boolean): Promise<void>;
    handleMediaResult(paths: Array<string>): Promise<void>;
    handleVideoResult(path: string | null, shouldDeleteOriginalIfScaled: boolean): void;
    finishWithSuccess(path: string | null): void;
    finishWithListSuccess(path: ArrayList<string> | null): void;
    getResizedImagePath(path: string, outputOptions: ImageSelectionOptions): Promise<string>;
}
export declare enum CameraDevice {
    REAR = 0,
    FRONT = 1
}
