# 更新日志

## 1.7.1-enterprise
发版时间：2025/7/25
- 公有云 SDK 1.7.1 的私有云版本。

## 1.7.1
发版时间：2025/7/25

**新增功能**
- UserInfoModel 新增 `userType` 字段。
- MessageService 新增 `getMessageEventListeners` 方法。
- UIMessage 新增 `extra` 字段。
- ConversationComponent 新增 `ConversationComponentDelegate` 设置支持。

**问题修复与优化**
- 优化：统一会话列表页、会话页、用户详情页的用户昵称展示逻辑。
- 优化：引用消息点击后跳转定位体验。
- 优化：网络不可用时，设置置顶或免打扰失败时增加提示并重置状态。
- 优化：网络不可用时，点击已读回执请求增加提示。
- 优化：录制语音消息倒计时 10 秒时，手指上滑不再显示数字。
- 优化：语音消息录制体验。
- 修复：草稿内容为多行换行符时，会话列表中显示为 '...' 的问题。
- 修复：输入框高度变化时遮挡消息列表的问题。
- 修复：输入消息后点击文字/语音切换按钮，右侧依然显示发送按钮的问题。

## 1.7.0-enterprise
发版时间：2025/7/15
### Kit
**新增功能**
- 合并转发支持自定义类型消息与SDK不支持的消息。
- 合并转发支持设置水印组件。
- 群消息回执支持配置每条消息都显示已读未读信息。
- 输入框支持设置展开输入按钮。
- 会话页面消息列表的消息定位优化。
- 优化媒体消息气泡组件展示后页面跳动问题。

**问题修复**
- 优化：会话页面消息列表的消息定位优化。
- 优化：媒体消息气泡组件展示后页面跳动问题优化。
- 修复：音视频通话中还能发送语音消息的问题。
- 修复：录制语音消息时正在播放的语音消息不会停止的问题。
- 修复：点击语音消息后会话列表的[语音]字样依然显示红色的问题。
- 修复：合并转发偶现白屏问题。
- 修复：修复单聊发送消息后接收端一直显示 "对方正在输入"。
- 修复：发送消息后头像偶现闪烁问题。
- 修复：移除会话列表事件监听失效问题。
- 修复：优化打开多个会话页面后。
- 修复：会话列表偶现长按后操作失效问题。
- 修复：会话列表不展示单聊会话已读状态的问题。

## 1.6.0-enterprise
发版时间：2025/6/27
- 公有云 SDK 1.6.0 的私有云版本。

## 1.6.0
发版时间：2025/6/27

**新增功能**
- 支持录制和播放普通语音消息。
- 撤回带 @ 的消息重新编辑后保留 @ 信息。
- 支持自定义输入框 UI 组件。
- 支持自定义插件 UI 组件。
- 支持添加自定义表情管理面板。
- 支持自定义会话页面未读消息、@我未读消息、新未读消息的 UI 组件。
- 文本消息和引用消息的内容支持自定义渲染。
- 引用消息支持配置点击后定位到原消息。
- 支持使用 `ConversationComponentData` 的`timestamp` 字段跳转会话页面。
- 用户信息提供者新增配置支持是否持久化存储到本地数据库中，支持发消息时携带用户信息，支持收到消息后更新本地用户信息。

**问题修复**
- 修复：输入 500 以上个表情发送消息崩溃。
- 修复：收到新消息没有提示音的问题。
- 优化：会话页面监听 `applicationStateChange` 事件逻辑。
- 修复：播放小视频时,点击拖拽小视频进度不灵敏。
- 优化：会话列表发送状态图标闪烁的问题。
- 优化：点击媒体消息跳转页面闪烁的问题。

## 1.5.1-enterprise
发版时间：2025/6/10
### Kit
**新增功能**
- 支持录制和播放普通语音消息。
- 撤回带 @ 的消息重新编辑后保留 @ 信息。
- 支持自定义输入框 UI 组件。
- 支持自定义插件 UI 组件。
- 支持添加自定义表情管理面板。

**问题修复**
- 修复：输入 500 以上个表情发送消息崩溃。
- 修复：收到新消息没有提示音的问题。
- 优化：会话页面监听 `applicationStateChange` 事件逻辑。

## 1.5.0-enterprise
发版时间：2025/5/29
- 公有云 SDK 1.5.0 的私有云版本。

## 1.5.0
发版时间：2025/5/29

**问题修复**
- 修复：会话页面点击右上角 "x条消息" 后点击底部 `InputBar` 组件，未滚动到底部的问题
- 修复：群会话发送@所有人后未高亮显示得问题
- 修复：公众号会话消息不应该支持引用
- 修复：消息被撤回后未正确提示的问题
- 修复：引用长文本消息显示异常问题
- 修复：小视频播放完后播放时间未重置问题
- 修复：会话页面多选状态下撤回消息展示异常问题

## 1.4.3-enterprise
发版时间：2025/4/29
- 公有云 SDK 1.4.3 的私有云版本。

## 1.4.3
发版时间：2025/4/29

**新增功能**
- 新增媒体消息：合并转发消息 `CombineMessage`
- 新增：构建合并转发消息接口 `obtainCombineMessage`
- 新增：聊天页面配置 `ConversationConfig` 新增配置
    - 设置合并转发Html样式：`setCombineHtmlStyle`
    - 设置是否连续播放未收听的语音消息：`setEnablePlayAudioContinuous`
    - 设置是否自动下载高清语音消息：`enableAutoDownloadHQVoice`
    - 设置是否消息自动重发开关：`enableResendMessage`
    - 设置消息气泡圆角大小、边框色、背景色
- 新增：插入本地消息接口 `insertMessage`
- 新增：会话页面组件 `ConversationComponent` 支持隐藏输入框
- 新增：语音消息和高清语音消息点击事件支持 `onMessageClick` 回调
- 新增：支持消息发送失败自动重发功能
- 新增：会话列表页面支持设置点击监听与长按监听
- 新增：文件消息支持取消
- 新增：支持消息发送失败自动重发功能，
- 新增：消息拦截器 `MessageInterceptor` 支持同步拦截接口 `onWillSendMessageSync`，新增媒体消息上传下载拦截接口
- 新增：支持动态配置扩展面板插件
- 新增：消息点击事件接口 `MessageClickListener` 的所有方法均新增 `Context` 与点击长按事件参数
- 新增：新增获取消息长按事件列表接口 `getMessageItemLongClickActionArray`
- 新增：会话页面 `ConversationPage` 对外暴露，支持不通过会话列表直接调用
- 优化：优化头像裁剪规则
- 优化：优化图片插件、文件插件发送多个媒体消息的策略，展示待发送消息

**问题修复**

- 移除 `LocationButton` 组件
- 修复：文件插件不支持 `AppPublicService` 类型
- 修复：群会话长按用户头像，输入框显示由备注名改为用户名
- 修复：失败消息不应该支持引用
- 修复：修复会话页面长按消息选择后会展示上次选择的 `Item` 的问题
- 修复：解决文件消息发送失败后点击重新发送不成功的问题
- 修复：发送语音消息时收到来电，则停止录制直接发送
- 修复：阅后即焚点击相册支持选择支持图片和视频
- 修复：打开多个会话，功能栏显示两个阅后即焚的问题
- 修复：用户信息提供者的数据库存储读取异常问题
- 修复：键盘输入两行以上的文本消息会遮挡最后一条消息
- 修复：会话页面拉消息逻辑优化，如本地消息没拉到则返回远端拉取的消息
- 修复：播放语音消息时可能造成会话未读数是1的问题
- 修复：没有正确同步其他端免打扰状态的问题
- 修复：接收系统消息的撤回消息,会话列表最后一条消息显示空的问题
- 修复：偶现 @用户名 没有高亮的问题
- 修复：引用消息 @用户名 没有高亮的问题
- 修复：当一次发送的内容较多时，url链接没有显示高亮的问题
- 修复：断网后会话列表上方没有断网提示条
- 修复：前台收到消息不展示通知的问题


## 1.4.0-enterprise.3
发版时间：2025/3/28
- 修复了若干个 Bug。

## 1.4.0-enterprise.2
发版时间：2025/3/5
- 修复了若干个 Bug。

## 1.4.0-enterprise.1
发版时间：2025/2/28
- 公有云 SDK 1.4.0 的私有云版本。

## 1.3.2-enterprise.1
发版时间：2025/1/14
- 公有云 SDK 1.3.2 的私有云版本。

## 1.3.3
发版时间：2025/1/14
- 修复了若干个 Bug 。

## 1.3.2
发版时间：2025/1/10
- 修复了若干个 Bug 。

## 1.3.1-enterprise.1
发版时间：2024/12/30
- 基于 1.0.1 版本发布的私有云版本。

## 1.0.1
发版时间：2024/12/27
- 替换 IMLib SDK 1.3.1 版本。

## 1.0.0

发布日期：2024/12/20

- **鸿蒙 IMKit SDK 初版**
