import BaseLayer from "./j1"; import Overlay from "../m/m"; import type BaseMap from "../o2"; import type OverlayMgr from "../m/j2"; import type { OverlayType } from "../../util/b1/c1";             export default class OverlayLayer extends BaseLayer { private overlayMgr; private oldLayerId; private canUpdate;               constructor(name: string, id: string, o29: BaseMap);         setOldLayerId(id: string): void;         getOldLayerId(): string;         register(n29: OverlayMgr): void;           addOverlay(overlay: Overlay): void;           removeOverlay(overlay: Overlay): void;           layerCommitOnce(): void;         getCommitStatus(): boolean;         pauseCommit(): void;         resumeCommit(): void;         removeOverlays(type?: OverlayType): void;         setVisible(visible: boolean): void; } 