//
//  Generated code. Do not modify.
//  source: sgmwParkingNotify.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use sgmwParkingNotifyDescriptor instead')
const SgmwParkingNotify$json = {
  '1': 'SgmwParkingNotify',
  '2': [
    {'1': 'expireAtTimestamp', '3': 1, '4': 1, '5': 3, '10': 'expireAtTimestamp'},
    {'1': 'vin', '3': 2, '4': 1, '5': 9, '10': 'vin'},
    {'1': 'msgId', '3': 3, '4': 1, '5': 9, '10': 'msgId'},
    {'1': 'businessType', '3': 4, '4': 1, '5': 9, '10': 'businessType'},
  ],
};

/// Descriptor for `SgmwParkingNotify`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List sgmwParkingNotifyDescriptor = $convert.base64Decode(
    'ChFTZ213UGFya2luZ05vdGlmeRIsChFleHBpcmVBdFRpbWVzdGFtcBgBIAEoA1IRZXhwaXJlQX'
    'RUaW1lc3RhbXASEAoDdmluGAIgASgJUgN2aW4SFAoFbXNnSWQYAyABKAlSBW1zZ0lkEiIKDGJ1'
    'c2luZXNzVHlwZRgEIAEoCVIMYnVzaW5lc3NUeXBl');

