// @keepTs
// @ts-nocheck
import { ApplicationStateChangeCallback, common } from '@kit.AbilityKit';
import { observer } from '@kit.TelephonyKit';
export { observer, call } from '@kit.TelephonyKit';
export declare class SystemUtil {
    /**
     * 在后台运行
     * @returns
     */
    static inBackground(): Promise<boolean>;
    /**
     * 获取BundleName
     * @returns
     */
    static getBundleName(): string;
    /**
     * 启动浏览器打开制定url
     * @param uri 'https://www.huawei.com/'
     */
    static startOpenLink(t356: common.UIAbilityContext, u356: string): void;
    /**
     * 拨打电话
     * @param phone
     */
    static startCallPhone(o356: string): void;
    /**
     * 发送邮件
     * @param email
     */
    static startSendEmail(f356: common.UIAbilityContext, g356: string): void;
    static isEmail(w355: string, x355: (isEmail: boolean) => void): void;
    /**
     * 监听电话拨打状态
     * @param ob 监听callback
     */
    static observerOnCallStateChange(u355: (data: observer.CallStateInfo) => void): void;
    /**
     * 取消监听电话拨打状态
     * @param ob 监听callback
     */
    static observerOffCallStateChange(s355: (data: observer.CallStateInfo) => void): void;
    /**
     * 监听前后台状态变更
     * @param ob 监听callback
     */
    static observerOnApplicationStateChange(q355: common.Context, r355: ApplicationStateChangeCallback): void;
    /**
     * 取消监听前后台状态变更
     * @param ob 监听callback
     */
    static observerOffApplicationStateChange(o355: common.Context, p355: ApplicationStateChangeCallback): void;
}
