import HttpClient from "./p"; import { Request } from "./r"; import { rcp } from "@kit.RemoteCommunicationKit";         export declare class HttpCall { private _client; private _request;         get request(): Request;         get client(): HttpClient;           constructor(h6: HttpClient, request: Request);       private _onSuccessCallback;         private get onSuccessCallback(); private _onFailureCallback;           private get onFailureCallback();           enqueue(onSuccess: Function, onFailure: Function): void;         getResponse(session: rcp.Session): Promise<rcp.Response>;       executeOn(): void;           private onError;           private onComplete; } export default HttpCall; 