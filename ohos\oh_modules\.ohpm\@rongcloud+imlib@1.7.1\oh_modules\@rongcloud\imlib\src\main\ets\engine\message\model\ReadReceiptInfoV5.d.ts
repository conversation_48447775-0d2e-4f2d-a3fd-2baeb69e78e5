import { ConversationIdentifier } from "../../conversation/ConversationIdentifier";
/**
 * 已读回执信息 V5
 *
 *```
 // sequenceDiagram start
 title 消息已读 V5
 发送方->> 接收方: 1.发送消息
 发送方->> 接收方: 2.注册已读回执事件监听
 activate 接收方
 接收方-->> 接收方: 3.进入聊天页面，遍历所有的消息
 接收方-->> 发送方: 4.发送已读回执V5响应
 deactivate 接收方
 activate 发送方
 发送方->> 发送方:  5.将聊天页面对应消息更新已读状态
 deactivate 发送方
 // sequenceDiagram end

 群聊具体流程描述：
 1. 发送方发送消息：发送方 App/IMKit 发送任何的记未读消息，比如文本、图片等
 2. 发送方注册已读回执时间监听器：发送方 App/IMKit 调用 IMEngine.addMessageReadReceiptV5Listener()
 3. 接收方进入聊天页面，遍历所有的消息
 4. 接收方发送群聊已读回执响应：接收方 App/IMKit 进入聊天页面内对要回执的消息调用 IMEngine.sendReadReceiptResponseV5() 发送已读回执响应，证明接收方这些消息已读过
 5. 发送方接收群聊已读回执响应：发送方 SDK 接收到已读响应时，将本地消息的 respondUserIdList 更新，然后触发 MessageReadReceiptV5Listener.onMessageReceiptResponse()
 6. 发送方将聊天页面对应消息更新已读状态：发送方 App/IMKit 在对应的聊天页面，找到对应的消息，更新对应的 UI
 *```
 * @version 1.5.0
 * @discussion 使用 https://sequencediagram.org/ 打开时序图
 */
export declare class ReadReceiptInfoV5 {
    /**
     * 会话信息
     * 从 1.6.0 版本起，消息已读 V5 返回信息中新增会话信息。
     *
     * @version 1.6.0
     * */
    conIdentifier: ConversationIdentifier;
    /**
     * 消息 UID
     */
    messageUid: string;
    /**
     * 消息发送时间
     */
    messageTime: number;
    /**
     * 已读回执成员数
     * */
    readCount: number;
    /**
     * 未读回执成员数
     * */
    unreadCount: number;
    /**
     * 已读 + 未读 成员总数
     * */
    totalCount: number;
}
