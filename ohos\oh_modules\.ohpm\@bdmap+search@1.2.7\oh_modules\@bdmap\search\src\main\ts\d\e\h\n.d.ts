import type { PlanNode, RouteLine, RouteNode, RouteStep, SearchResult, SuggestAddrInfo, TaxiInfo } from "./f1";
/**
 * 公交路线规划结果封装
 */
export interface TransitRouteResult extends SearchResult {
    /**
     * 打车信息
     */
    taxiInfo?: TaxiInfo;
    /**
     * 所有换乘路线方案
     */
    routeLines?: TransitRouteLine[];
    /**
     * 建议信息,当{@link #error} 为 {@link ERRORNO#AMBIGUOUS_ROURE_ADDR} 时
     * 可通过此接口获取建议信息
     */
    suggestAddrInfo?: SuggestAddrInfo;
}
/**
 * 表示一个换乘路线，换乘路线将根据既定策略调配多种交通工具。
 * <p>
 * 换乘路线可能包含：城市公交路段，地铁路段，步行路段
 * </p>
 */
export interface TransitRouteLine extends RouteLine<TransitStep> {
}
/**
 * 表示一个换乘路段
 */
export interface TransitStep extends RouteStep {
    /**
     * 该路段换乘说明
     */
    instructions?: string;
    /**
     * 交通工具信息
     */
    vehicleInfo?: VehicleInfo;
    /**
     * 路段入口信息
     */
    entrance?: RouteNode;
    /**
     * 路段出口信息
     */
    exit?: RouteNode;
    /**
     * 路段类型
     */
    stepType?: TransitRouteStepType;
    /**
     * 出于性能考虑，缓存path坐标点，当需要用到时再解析
     */
    pathString?: string;
}
/**
 * 公交路线规划参数
 */
export interface TransitRoutePlanOption {
    /**
     * 起点
     */
    from: PlanNode;
    /**
     * 终点
     */
    to: PlanNode;
    /**
     * 所在城市名，默认为北京
     */
    cityName?: string;
    /**
     * 换乘策略,默认时间优先
     */
    policy?: TransitPolicy;
}
/**
 * 换乘策略
 */
export declare enum TransitPolicy {
    /**
     * 公交检索策略常量：时间优先
     */
    EBUS_TIME_FIRST = 0,
    /**
     * 公交检索策略常量：最少换乘
     */
    EBUS_TRANSFER_FIRST = 2,
    /**
     * 公交检索策略常量：最少步行距禀
     */
    EBUS_WALK_FIRST = 3,
    /**
     * 公交检索策略常量：不含地铁
     */
    EBUS_NO_SUBWAY = 4
}
/**
 * 路段类型枚举
 */
export declare enum TransitRouteStepType {
    /**
     * 公交路段
     */
    BUSLINE = 0,
    /**
     * 地铁路段
     */
    SUBWAY = 1,
    /**
     * 步行路段
     */
    WALKING = 2
}
/**
 * 路线换乘方案里的交通工具信息
 * <p>
 * 交通工具包括： 公交，地铁
 * </p>
 */
export interface VehicleInfo {
    /**
     * 交通路线的标识
     */
    uid: string;
    /**
     * 交通路线的所乘站数
     */
    passStationNum: number;
    /**
     * 该交通路线的名称
     */
    title: string;
    /**
     * 该交通路线的所乘区间的区间价格
     */
    zonePrice: number;
    /**
     * 该交通路线的全程价格
     */
    totalPrice: number;
    /**
     * 开始时间
     */
    startTime: string;
    /**
     * 结束事件
     */
    endTime: string;
    /**
     * 发车间隔时间
     */
    headWay: string;
    /**
     * 线路方向
     */
    directText: string;
}
