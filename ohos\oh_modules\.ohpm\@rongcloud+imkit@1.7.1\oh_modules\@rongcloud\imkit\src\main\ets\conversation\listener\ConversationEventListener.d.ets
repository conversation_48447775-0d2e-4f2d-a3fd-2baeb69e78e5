// @keepTs
// @ts-nocheck
import { UserInfoModel } from '../../user/model/UserInfoModel';
/**
 * 聊天页面的各种事件监听
 * @version 1.0.0
 */
export interface ConversationEventListener {
    /**
     * 输入@时，跳转用户列表选择用户
     * @param select 选中的用户信息
     */
    onInputMention?: (select: (user: UserInfoModel) => void) => void;
    /**
     * 当输入状态变化时
     * @param isEditing
     */
    onEditChange?: (isEditing: boolean) => void;
}
