import 'dart:async';
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:crypto/crypto.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
// 添加必要的导入
import 'package:wuling_flutter_app/api/car_api.dart';
import 'package:wuling_flutter_app/models/global_data.dart';
// 添加protobuf导入
import 'package:wuling_flutter_app/generated/proto/sgmw_app_car_status.pb.dart';
import 'package:wuling_flutter_app/generated/proto/sgmw_app_control_result.pb.dart';

import '../../widgets/common/custom_timer.dart';
import 'log_manager.dart';

/// MQTT订阅主题枚举
enum MQTTTopic {
  carStatus,
  carPortPosition,
  parkingInStatus,
  parkingOutStatus,
  remoteControlStatus,
  summonLocation,
  summonStatus,
  unblockStatus,
  carControlResult,
  carRemoteAsyncResult,
  carControlAllStatus,
  carCheckAuthorizeBusiness,
  carParkingNotifyBusiness,
}

/// MQTT消息监听器
typedef MQTTMessageListener = void Function(dynamic data);

/// 监听器配置类
class MQTTListenerPair {
  final MQTTMessageListener listener;
  final MQTTTopic topic;
  final String? tag; // 用于标识监听器，便于移除

  MQTTListenerPair({
    required this.listener,
    required this.topic,
    this.tag,
  });
}

/// MQTT异步结果推送超时监听
typedef ReceiveMqttTimeOutListener = void Function(dynamic data);

/// 监听器配置类
class ReceiveMqttTimeOutListenerPair {
  final ReceiveMqttTimeOutListener listener;
  final String? tag; // 用于标识监听器，便于移除

  ReceiveMqttTimeOutListenerPair({
    required this.listener,
    this.tag,
  });
}

/// MQTT管理器 - 通过鸿蒙端MqttHarmonyPlugin实现
class MQTTManager {
  static MQTTManager? _instance;
  static MQTTManager get shared {
    _instance ??= MQTTManager._internal();
    return _instance!;
  }

  MQTTManager._internal() {
    _initializeConnectivity();
    _setupMethodChannel();
  }

  // MethodChannel用于与鸿蒙端通信
  static const MethodChannel _channel = MethodChannel('mqtt_harmony_channel');
  
  // 连接参数
  String? _vin;
  String? _username;
  String? _password;
  String? _clientId;
  String? _prefix;
  
  // 状态管理
  bool _isNetwork = true;
  bool _supportMqtt = true;
  bool _isCancelReconnect = false;
  bool _isConnected = false;
  
  // 监听器列表
  final List<MQTTListenerPair> _listeners = [];

  // 监听器列表
  final List<ReceiveMqttTimeOutListenerPair> _mqttTimeOutListeners = [];

  // 定时器
  Timer? _statusTimer;

// 接收MQTT异步结果推送超时定时器
  late final CustomTimer _mqttTimeOutTimer =
  CustomTimer(const Duration(seconds: 18), onTick: onReceiveMqttTimeOut);
  
  // 网络连接监听
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  
  /// 设置MethodChannel回调处理
  void _setupMethodChannel() {
    _channel.setMethodCallHandler((call) async {
      switch (call.method) {
        case 'onConnected':
          _handleConnected();
          break;
        case 'onDisconnected':
          _handleDisconnected();
          break;
        case 'onMessageReceived':
          _handleMessageReceived(call.arguments);
          break;
        case 'onSubscribeSuccess':
          _handleSubscribeSuccess();
          break;
        case 'onError':
          _handleError(call.arguments['error']);
          break;
      }
    });
  }
  
  /// 处理连接成功事件
  void _handleConnected() {
    LogManager().debug('MQTTManager: 连接成功');
    _isConnected = true;
    _startStatusTimer();
  }
  
  /// 处理断开连接事件
  void _handleDisconnected() {
    LogManager().debug('MQTTManager: 连接断开');
    _isConnected = false;
    _stopStatusTimer();
  }
  
  /// 处理消息接收事件
  void _handleMessageReceived(Map<dynamic, dynamic> arguments) {
    final topic = arguments['topic'] as String;
    final payload = arguments['payload'] as String;
    final qos = arguments['qos'] as int;
    final isBase64Encoded = arguments['isBase64Encoded'] as bool? ?? false; // 新增标识符
    
    LogManager().debug('MQTTManager: 接收消息参数');
    LogManager().debug('MQTTManager:   主题: $topic');
    LogManager().debug('MQTTManager:   数据长度: ${payload.length}');
    LogManager().debug('MQTTManager:   QoS级别: $qos');
    LogManager().debug('MQTTManager:   是否Base64编码: $isBase64Encoded'); // 新增日志
    
    // 确定消息主题类型
    final topicType = _getTopicTypeFromString(topic);
    LogManager().debug('MQTTManager:   匹配的主题枚举: $topicType');
    
    if (topicType != null) {
      // 解析消息数据，传递isBase64Encoded标识符
      final parsedData = _parseMessageData(topicType, payload, isBase64Encoded);
      if (parsedData != null) {
        _notifyListeners(topicType, parsedData);
      }
    }
  }
  
  /// 处理订阅成功事件
  void _handleSubscribeSuccess() {
    LogManager().debug('MQTTManager: 订阅成功');
  }
  
  /// 处理错误事件
  void _handleError(String error) {
    LogManager().debug('MQTTManager: 错误 - $error');
  }

  /// 获取Token并登录MQTT
  /// [vin] 车辆VIN码
  /// [complete] 完成回调，返回是否成功
  Future<void> getTokenAndLoginWithVin(
    String vin, {
    Function(bool success)? complete,
  }) async {
    if (_isConnected) {
      complete?.call(true);
      return;
    }

    try {
      // 获取MQTT登录凭据
      final credentials = await _getTokenWithVin(vin);
      if (credentials != null) {
        LogManager().debug('MQTT: 获取账号密码成功');
        // 通过MethodChannel调用鸿蒙端连接MQTT
        _prefix = vin;
        await _connectWithCredentials(
          credentials['username']!,
          credentials['password']!,
          credentials['clientId']!,
          onSuccess: () {
            complete?.call(true);
            LogManager().debug('MQTT: 登录成功');
          },
          onFailure: () {
            complete?.call(false);
            LogManager().debug('MQTT: 登录失败');
          },
        );
      } else {
        complete?.call(false);
        LogManager().debug('MQTT: 获取账号密码失败');
      }
    } catch (e) {
      complete?.call(false);
      LogManager().debug('MQTT: 获取Token异常: $e');
    }
  }

  /// 获取MQTT登录Token
  /// [vin] 车辆VIN码
  /// 返回包含username、password、clientId的Map
  Future<Map<String, String>?> _getTokenWithVin(String vin) async {
    try {
      // 获取当前用户手机号
      final phone = _getCurrentUserPhone();
      if (phone == null || phone.length < 4) {
        LogManager().debug('MQTT: 获取用户手机号失败或手机号长度不足');
        return null;
      }
      
      // 调用API获取MQTT Token
      final tokenModel = await carAPI.getMQTTTokenWithVin(vin, '3');
      final token = tokenModel.token;
      if (token == null || token.isEmpty) {
        LogManager().debug('MQTT: 获取Token失败');
        return null;
      }
      
      // 生成客户端ID（按照iOS逻辑：vin + "_" + 手机号后4位）
      final clientId = '${vin}_${phone.substring(phone.length - 4)}';
      
      // 生成用户名和密码（按照iOS逻辑）
      // username = md5(clientId前6位 + token)
      final usernameOrg = '${clientId.substring(0, 6)}$token';
      final username = _md5(usernameOrg);
      
      // password = md5(clientId后6位 + token)
      final passwordOrg = '${clientId.substring(clientId.length - 6)}$token';
      final password = _md5(passwordOrg);
      
      LogManager().debug('MQTT: Token获取成功 - ClientId: $clientId');
      
      return {
        'username': username,
        'password': password,
        'clientId': clientId,
        'vin': vin,
      };
    } catch (e) {
      LogManager().debug('MQTT: 获取Token异常: $e');
      return null;
    }
  }

  /// 获取当前用户手机号
  String? _getCurrentUserPhone() {
    // 从全局数据中获取当前登录用户的手机号
    return GlobalData().userModel?.mobile;
  }

  /// MD5加密
  String _md5(String input) {
    return md5.convert(utf8.encode(input)).toString();
  }

  /// 使用凭据登录MQTT
  Future<void> _loginWithCredentials(
    String username,
    String password,
    String clientId,
    String vin, {
    void Function()? onSuccess,
    void Function()? onFailure,
  }) async {
    // 保存连接参数
    _username = username;
    _password = password;
    _clientId = clientId;
    _prefix = vin;
    _vin = vin;
    _isCancelReconnect = false;

    try {
      await _connectWithCredentials(
        username,
        password,
        clientId,
        onSuccess: onSuccess,
        onFailure: () {
          // 5秒后重试连接
          Timer(const Duration(seconds: 5), () {
            _reconnectMQTT();
          });
          onFailure?.call();
        },
      );
    } catch (e) {
      LogManager().debug('MQTT登录异常: $e');
      onFailure?.call();
    }
  }

  /// 建立MQTT连接
  Future<void> _connectWithCredentials(
    String username,
    String password,
    String clientId, {
    void Function()? onSuccess,
    void Function()? onFailure,
  }) async {
    // 打印连接参数
    LogManager().debug('MQTTManager: 连接参数');
    LogManager().debug('MQTTManager:   username: $username');
    LogManager().debug('MQTTManager:   password: $password');
    LogManager().debug('MQTTManager:   clientId: $clientId');
    
    try {
      // 获取订阅主题
      final topics = _getTopicDictionary();
      final topicList = topics.values.toList();
      
      // 通过MethodChannel调用鸿蒙端连接MQTT
      final result = await _channel.invokeMethod('connectMqtt', {
        'url': 'tcp://parkingdata.sgmwcloud.com.cn:1883',
        'clientId': clientId,
        'username': username,
        'password': password,
        'topics': topicList,
      });
      
      LogManager().debug('MQTTManager: 连接请求已发送 - $result');
      // 连接结果将通过回调处理
    } catch (e) {
      LogManager().debug('MQTTManager：MQTT连接失败: $e');
      onFailure?.call();
    }
  }

  /// 获取主题字典
  Map<MQTTTopic, String> _getTopicDictionary() {
    const environmentPrefix = 'prod'; // 根据实际环境配置
    // 确保_prefix不为空
    final prefix = _prefix ?? 'default_prefix';
    return {
      MQTTTopic.carRemoteAsyncResult: '$prefix/$environmentPrefix/sgmw/vehicle/control',//异步车控结果推送主题
      MQTTTopic.carControlAllStatus: '$prefix/$environmentPrefix/sgmw/vehicle/app/status',//车况变更推送主题
      MQTTTopic.carCheckAuthorizeBusiness: '$prefix/$environmentPrefix/sgmw/vehicle/car_check_authorize/business',//车辆远程连接申请授权
      MQTTTopic.carParkingNotifyBusiness: '$prefix/$environmentPrefix/sgmw/vehicle/car_parking_notify/business',//泊车通知
    };
  }

  /// 根据主题字符串获取主题类型
  MQTTTopic? _getTopicTypeFromString(String topicString) {
    final topics = _getTopicDictionary();
    for (final entry in topics.entries) {
      if (topicString.contains(entry.value)) {
        return entry.key;
      }
    }
    return null;
  }

  /// 解析消息数据
  Map<String, dynamic> _parseMessageData(MQTTTopic topic, String payload, bool isBase64Encoded) {
    try {
      switch (topic) {
        case MQTTTopic.carControlAllStatus:
          // 根据isBase64Encoded标识符决定解析方式
          if (isBase64Encoded) {
            // 如果标识为true，说明payload是base64编码的protobuf数据
            try {
              final data = base64.decode(payload);
              final carStatus = SgmwAppCarStatus.fromBuffer(data);
              return _convertCarStatusToMap(carStatus);
            } catch (e) {
              LogManager().debug('Error parsing base64 protobuf data: $e');
              // 如果protobuf解析失败，返回错误信息
              return {
                'error': 'Failed to parse protobuf data',
                'originalPayload': payload,
                'timestamp': DateTime.now().millisecondsSinceEpoch,
              };
            }
          } else {
            // 如果标识为false，说明payload是普通字符串，尝试作为JSON解析
            try {
              return json.decode(payload) as Map<String, dynamic>;
            } catch (e) {
              LogManager().debug('Error parsing as JSON: $e');
              // JSON解析失败，返回原始数据
              return {
                'topic': topic.toString(),
                'data': payload,
                'timestamp': DateTime.now().millisecondsSinceEpoch,
              };
            }
          }
        case MQTTTopic.carRemoteAsyncResult:
          // 根据isBase64Encoded标识符决定解析方式
          if (isBase64Encoded) {
            // 如果标识为true，说明payload是base64编码的protobuf数据
            try {
              final data = base64.decode(payload);
              final controlResult = SgmwAppControlResult.fromBuffer(data);
              return _convertControlResultToMap(controlResult);
            } catch (e) {
              LogManager().debug('Error parsing base64 protobuf data for carRemoteAsyncResult: $e');
              // 如果protobuf解析失败，返回错误信息
              return {
                'error': 'Failed to parse protobuf data',
                'originalPayload': payload,
                'timestamp': DateTime.now().millisecondsSinceEpoch,
              };
            }
          } else {
            // 如果标识为false，说明payload是普通字符串，尝试作为JSON解析
            try {
              return json.decode(payload) as Map<String, dynamic>;
            } catch (e) {
              LogManager().debug('Error parsing as JSON: $e');
              // JSON解析失败，返回原始数据
              return {
                'topic': topic.toString(),
                'data': payload,
                'timestamp': DateTime.now().millisecondsSinceEpoch,
              };
            }
          }
        case MQTTTopic.carCheckAuthorizeBusiness:
        case MQTTTopic.carParkingNotifyBusiness:
          // 这些主题通常是JSON格式，但也要考虑isBase64Encoded标识
          if (isBase64Encoded) {
            // 如果是base64编码，先解码再解析JSON
            try {
              final decodedBytes = base64.decode(payload);
              final decodedString = utf8.decode(decodedBytes);
              return json.decode(decodedString) as Map<String, dynamic>;
            } catch (e) {
              LogManager().debug('Error parsing base64 JSON data: $e');
              return {
                'error': 'Failed to parse base64 JSON data',
                'originalPayload': payload,
                'timestamp': DateTime.now().millisecondsSinceEpoch,
              };
            }
          } else {
            // 直接作为JSON解析
            try {
              return json.decode(payload) as Map<String, dynamic>;
            } catch (e) {
              LogManager().debug('Error parsing as JSON: $e');
              return {
                'error': 'Failed to parse JSON data',
                'originalPayload': payload,
                'timestamp': DateTime.now().millisecondsSinceEpoch,
              };
            }
          }
        default:
          break;
      }
    } catch (e) {
      LogManager().debug('Error parsing message data for topic $topic: $e');
    }
    
    // 临时返回原始数据
    return {
      'topic': topic.toString(),
      'data': payload,
      'isBase64Encoded': isBase64Encoded,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
  }

  /// 通知监听器
  void _notifyListeners(MQTTTopic topic, dynamic data) {
    final listenersToNotify = _listeners.where((pair) => pair.topic == topic).toList();
    LogManager().debug('MQTTManager: 通知监听器 - 主题: $topic, 监听器数量: ${listenersToNotify.length}');
    
    for (final pair in listenersToNotify) {
      try {
        pair.listener(data);
      } catch (e) {
        LogManager().debug('MQTTManager: 监听器回调异常 - ${e.toString()}');
      }
    }
  }

  /// 添加消息监听器
  /// [listener] 消息监听回调
  /// [topic] 要监听的主题
  /// [tag] 监听器标识，用于后续移除
  void addListener(MQTTMessageListener listener, MQTTTopic topic, {String? tag}) {
    // 检查是否已存在相同的监听器
    final exists = _listeners.any((pair) => 
        pair.listener == listener && 
        pair.topic == topic && 
        pair.tag == tag);
    
    if (!exists) {
      _listeners.add(MQTTListenerPair(
        listener: listener,
        topic: topic,
        tag: tag,
      ));
    }
  }

  /// 移除监听器
  /// [tag] 监听器标识，如果为null则移除所有监听器
  void removeListener({String? tag}) {
    if (tag == null) {
      _listeners.clear();
    } else {
      _listeners.removeWhere((pair) => pair.tag == tag);
    }
  }

  void addReceiveMqttTimeOutListener(ReceiveMqttTimeOutListener listener,{String? tag}) {
    // 检查是否已存在相同的监听器
    final exists = _mqttTimeOutListeners.any((pair) =>
    pair.listener == listener &&
        pair.tag == tag);

    if (!exists) {
      _mqttTimeOutListeners.add(ReceiveMqttTimeOutListenerPair(
        listener: listener,
        tag: tag,
      ));
      }
  }

  void removeReceiveMqttTimeOutListener({String? tag}) {
    if (tag == null) {
      _mqttTimeOutListeners.clear();
    } else {
      _mqttTimeOutListeners.removeWhere((pair) => pair.tag == tag);
    }
  }

  /// 断开MQTT连接
  void disconnect() {
    LogManager().debug('MQTTManager: 断开MQTT连接-disconnect()');
    _isCancelReconnect = true;
    _stopStatusTimer();
    
    // 通过MethodChannel调用鸿蒙端断开连接
    _channel.invokeMethod('disconnectMqtt').then((result) {
      LogManager().debug('MQTTManager: 断开连接请求已发送 - $result');
      _isConnected = false;
    }).catchError((error) {
      LogManager().debug('MQTTManager: 断开连接失败 - $error');
    });
  }

  /// 检查MQTT连接状态并尝试连接
  void checkAndConnectMQTT() {
    LogManager().debug('MQTTManager: 当前状态 - ${_isConnected ? "已连接" : "未连接"}');
    
    if (_supportMqtt && !_isConnected) {
      if (_prefix != null) {
        getTokenAndLoginWithVin(_prefix!);
      }
    }
  }

  /// 重连MQTT
  void _reconnectMQTT() {
    if (!_isConnected && _supportMqtt && _isNetwork) {
      LogManager().debug('MQTTManager: 开始重连...');
      
      if (_prefix != null) {
        getTokenAndLoginWithVin(_prefix!, complete: (success) {
          if (success) {
            _isCancelReconnect = false;
            LogManager().debug('MQTTManager: 重连成功');
          } else {
            _isCancelReconnect = false;
            // 5秒后再次尝试重连
            Timer(const Duration(seconds: 5), () {
              _reconnectMQTT();
            });
            LogManager().debug('MQTTManager: 重连失败');
          }
        });
      }
    }
  }

  /// 启动状态检查定时器
  void _startStatusTimer() {
    _stopStatusTimer();
    _statusTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      _checkConnectionStatus();
    });
  }

  /// 停止状态检查定时器
  void _stopStatusTimer() {
    _statusTimer?.cancel();
    _statusTimer = null;
  }

  /// 启动接收MQTT异步结果推送超时定时器
  void startMqttTimeOutTimer() {
    LogManager().debug('MQTTManager: 启动接收MQTT异步结果推送超时定时器');
    stopMqttTimeOutTimer();
    _mqttTimeOutTimer.start();
  }

  /// 停止接收MQTT异步结果推送超时定时器
  void stopMqttTimeOutTimer() {
    LogManager().debug('MQTTManager: 停止接收MQTT异步结果推送超时定时器');
    if (_mqttTimeOutTimer.isRunning()) {
      _mqttTimeOutTimer.dispose();
    }
  }

  void onReceiveMqttTimeOut() {
    LogManager().debug('MQTTManager: MQTT异步结果推送超时,onReceiveMqttTimeOut - 监听器数量: ${_mqttTimeOutListeners.length}');
    for (final pair in _mqttTimeOutListeners) {
      try {
        pair.listener('车控响应超时,请重试');
      } catch (e) {
        LogManager().debug('MQTTManager: 监听器回调异常 - ${e.toString()}');
      }
    }
  }

  /// 检查连接状态
  void _checkConnectionStatus() {
    // 通过MethodChannel获取连接状态
    _channel.invokeMethod('getConnectionStatus').then((isConnected) {
      _isConnected = isConnected as bool;
      LogManager().debug('MQTTManager: 连接状态检查 - ${_isConnected ? "已连接" : "未连接"}');
      
      if (_supportMqtt && !_isConnected && _isNetwork && !_isCancelReconnect) {
        _isCancelReconnect = true;
        _reconnectMQTT();
      }
    }).catchError((error) {
      LogManager().debug('MQTTManager: 获取连接状态失败 - $error');
    });
  }

  /// 初始化网络连接监听
  void _initializeConnectivity() {
    _connectivitySubscription = Connectivity().onConnectivityChanged.listen((result) {
      final hasNetwork = result != ConnectivityResult.none;
      LogManager().debug('MQTTManager 网络状态变化: ${hasNetwork ? "已连接" : "无网络"}');
      
      if (!_isNetwork && hasNetwork && !_isConnected) {
        // 网络恢复，尝试重连
        _reconnectMQTT();
      }
      _isNetwork = hasNetwork;
    });
  }

  /// 获取当前VIN码（对应iOS的prefix属性）
  String? get currentVin => _prefix;
  
  /// 获取prefix（与iOS保持一致的命名）
  String? get prefix => _prefix;
  
  /// 获取连接状态
  bool get isConnected => _isConnected;
  
  /// 设置是否支持MQTT
  void setSupportMqtt(bool support) {
    _supportMqtt = support;
  }

  /// 销毁资源
  void dispose() {
    _stopStatusTimer();
    _connectivitySubscription?.cancel();
    disconnect();
    _listeners.clear();
  }

  /// 将SgmwAppCarStatus protobuf对象转换为Map
  Map<String, dynamic> _convertCarStatusToMap(SgmwAppCarStatus carStatus) {
    LogManager().debug('MQTTManager:将SgmwAppCarStatus protobuf对象转换为Map ,carStatus.collectTime:${carStatus.collectTime} , carStatus.collectTime.toInt():${carStatus.collectTime.toInt()}');
    return {
      'collectTime': carStatus.collectTime.toInt(),
      'acStatus': carStatus.acStatus,
      'doorLockStatus': carStatus.doorLockStatus,
      'windowStatus': carStatus.windowStatus,
      'engineStatus': carStatus.engineStatus,
      'tailDoorLockStatus': carStatus.tailDoorLockStatus,
      'lowBeamLight': carStatus.lowBeamLight,
      'dipHeadLight': carStatus.dipHeadLight,
      'sentinelModeStatus': carStatus.sentinelModeStatus,
      'tailDoorOpenStatus': carStatus.tailDoorOpenStatus,
      'door1LockStatus': carStatus.door1LockStatus,
      'door2LockStatus': carStatus.door2LockStatus,
      'door3LockStatus': carStatus.door3LockStatus,
      'door4LockStatus': carStatus.door4LockStatus,
      'doorOpenStatus': carStatus.doorOpenStatus,
      'door1OpenStatus': carStatus.door1OpenStatus,
      'door2OpenStatus': carStatus.door2OpenStatus,
      'door3OpenStatus': carStatus.door3OpenStatus,
      'door4OpenStatus': carStatus.door4OpenStatus,
      'window1Status': carStatus.window1Status,
      'window2Status': carStatus.window2Status,
      'window3Status': carStatus.window3Status,
      'window4Status': carStatus.window4Status,
      'topWindowStatus': carStatus.topWindowStatus,
      'autoGearStatus': carStatus.autoGearStatus,
      'manualGearStatus': carStatus.manualGearStatus,
      'keyStatus': carStatus.keyStatus,
      'acTemperatureGear': carStatus.acTemperatureGear,
      'acWindGear': carStatus.acWindGear,
      'leftBatteryPower': carStatus.leftBatteryPower,
      'leftFuel': carStatus.leftFuel,
      'mileage': carStatus.mileage,
      'leftMileage': carStatus.leftMileage,
      'batterySoc': carStatus.batterySoc,
      'current': carStatus.current,
      'voltage': carStatus.voltage,
      'batAvgTemp': carStatus.batAvgTemp,
      'batMaxTemp': carStatus.batMaxTemp,
      'batMinTemp': carStatus.batMinTemp,
      'tmActTemp': carStatus.tmActTemp,
      'invActTemp': carStatus.invActTemp,
      'accActPos': carStatus.accActPos,
      'brakPedalPos': carStatus.brakPedalPos,
      'strWhAng': carStatus.strWhAng,
      'vehSpdAvgDrvn': carStatus.vehSpdAvgDrvn,
      'obcOtpCur': carStatus.obcOtpCur,
      'vecChrgingSts': carStatus.vecChrgingSts,
      'vecChrgStsIndOn': carStatus.vecChrgStsIndOn,
      'obcTemp': carStatus.obcTemp,
      'batSOH': carStatus.batSOH,
      'lowBatVol': carStatus.lowBatVol,
      'leftTurnLight': carStatus.leftTurnLight,
      'rightTurnLight': carStatus.rightTurnLight,
      'positionLight': carStatus.positionLight,
      'frontFogLight': carStatus.frontFogLight,
      'rearFogLight': carStatus.rearFogLight,
      'latitude': carStatus.latitude,
      'longitude': carStatus.longitude,
      'position': carStatus.position,
      'charging': carStatus.charging,
      'wireConnect': carStatus.wireConnect,
      'rechargeStatus': carStatus.rechargeStatus,
      'seat1WindStatus': carStatus.seat1WindStatus,
      'seat2WindStatus': carStatus.seat2WindStatus,
      'seat31WindStatus': carStatus.seat3WindStatus,
      'seat4WindStatus': carStatus.seat4WindStatus,
      'seat1HotStatus': carStatus.seat1HotStatus,
      'seat2HotStatus': carStatus.seat2HotStatus,
      'seat3HotStatus': carStatus.seat3HotStatus,
      'seat4HotStatus': carStatus.seat4HotStatus,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
  }

  /// 将SgmwAppControlResult protobuf对象转换为Map
  Map<String, dynamic> _convertControlResultToMap(SgmwAppControlResult controlResult) {
    return {
      'code': controlResult.code,
      'message': controlResult.message,
      'serviceCode': controlResult.serviceCode,
      'collectTime': controlResult.collectTime.toInt(),
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
  }
}