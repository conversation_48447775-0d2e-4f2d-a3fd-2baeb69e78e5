import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wuling_flutter_app/api/car_api.dart';
import 'package:wuling_flutter_app/constant/web_view_url_tool.dart';
import 'package:wuling_flutter_app/models/car/car_service_status_model.dart';
import 'package:wuling_flutter_app/page/base_page/base_page.dart';
import 'package:wuling_flutter_app/routes/jump_tool.dart';
import 'package:wuling_flutter_app/utils/manager/loading_manager.dart';
import 'package:wuling_flutter_app/widgets/common/custom_refresher.dart';
import 'package:wuling_flutter_app/widgets/common/custom_dialog.dart';
import 'package:wuling_flutter_app/widgets/use_car_page_widgets/older_ev_page/older_ev_page_widgets.dart';
import 'package:wuling_flutter_app/models/car/car_info_model.dart';
import 'package:wuling_flutter_app/models/car/car_status_model.dart';
import 'package:wuling_flutter_app/models/car/car_new_energy_battery_model .dart';
import 'package:wuling_flutter_app/models/car/car_energy_speed_battery_model.dart';
import 'package:wuling_flutter_app/models/car/car_status_response_model.dart';
import 'package:wuling_flutter_app/models/global_data.dart';
import 'package:wuling_flutter_app/utils/manager/log_manager.dart';
import 'package:wuling_flutter_app/utils/manager/dialog_manager.dart';

import '../../models/car/car_charge_status_model.dart';
import '../../models/car/car_mileage_response_model.dart';
import '../../models/car/car_service_model.dart';
import '../../models/car/car_service_response_model.dart';
import '../../utils/http/api_exception.dart';
class OlderEvPage extends BasePage {
  final String pageTitle;
  OlderEvPage({
    Key? key,
    bool hideAppBar = false,
    bool isWithinSafeArea = true,
    String appBarTitle = '爱车信息',
    Color appBarColor = Colors.transparent,
    Color pageBackgroundColor = Colors.white,
    this.pageTitle = '爱车信息',
  }) : super(
          key: key,
          hideAppBar: hideAppBar,
          isWithinSafeArea: isWithinSafeArea,
          appBarTitle: pageTitle,
          appBarColor: appBarColor,
          pageBackgroundColor: pageBackgroundColor,
        );

  @override
  _OlderEvPageState createState() => _OlderEvPageState();
}

class _OlderEvPageState extends BasePageState<OlderEvPage> {
  final RefreshController _refreshController =
  RefreshController(initialRefresh: false);
  CarInfoModel? _carInfoModel;
  CarStatusModel? _carStatusModel;
  CarNewEnergyBatteryModel? _newEnergyBatteryModel;
  CarEnergySpeedBatteryModel? _energySpeedBatteryModel;
  CarServiceModel? _chargeModel;
  CarServiceModel? _batteryModel;
  List<CarServiceModel> _newEnergyServiceList = [];
  String _yesterdayMileage = '0';
  String _chargeStatus = '0';
  bool _isAutoChargeSwitchOn = false;
  bool _isHeatSwitchOn = false;
  void onRefresh() async {
    try{
      CarStatusResponseModel carStatusResponseModel = await carAPI.getDefaultCarInfoAndStatus();
      CarInfoModel? carInfoModel = carStatusResponseModel.carInfo;
      CarStatusModel? carStatusModel = carStatusResponseModel.carStatus;
      CarMileageResponseModel? response = await carAPI.getYesterdayMileFromSevers({});
      final List<String> servicePositionCodeList = ['service_new_energy'];
      List<CarServiceResponseModel> serviceResponses = await carAPI.getCarServiceList(servicePositionCodeList);
      List<CarServiceModel> newEnergyServiceList = [];
      for (var serviceResponseModel in serviceResponses) {
        if(serviceResponseModel.positionCode == 'service_new_energy'){
          newEnergyServiceList = serviceResponseModel.serviceList ?? [];
        }
      }
      CarChargeStatusModel chargeStatusModel = await carAPI.getAutoChargeStatusFromSevers(_carInfoModel?.vin ?? '');
      CarNewEnergyBatteryModel batteryModel = await carAPI.getNewEnergyCarBatteryStatusWithVin(_carInfoModel?.vin ?? '');
      CarEnergySpeedBatteryModel energySpeedBatteryModel = CarEnergySpeedBatteryModel(
          limitFeedback: '',
          batteryStatus: carStatusModel?.batteryStatus,
          status: '', collectTime: carStatusModel?.collectTime,
          vin: carInfoModel?.vin
      );
      setState(() {
        _carInfoModel = carInfoModel;
        _carStatusModel = carStatusModel;
        _yesterdayMileage = response.trip ?? '0';
        _newEnergyServiceList = newEnergyServiceList;
        _energySpeedBatteryModel = energySpeedBatteryModel;
        _newEnergyBatteryModel = batteryModel;
        for(CarServiceModel serviceModel in _newEnergyServiceList){
          if(serviceModel.serviceCode == 'autoCharge'){
            _chargeModel = serviceModel;
          }
          if(serviceModel.serviceCode == 'autoHeat'){
            _batteryModel = serviceModel;
          }
        }
        _chargeStatus = chargeStatusModel.chargingStatus ?? '0';
        String batteryStatus = _energySpeedBatteryModel?.batteryStatus ?? '1';
        if(int.parse(_chargeStatus) > 0){
          _isAutoChargeSwitchOn = true;
        }else{
          _isAutoChargeSwitchOn = false;
        }
        if(int.parse(batteryStatus) == 0){
          _isHeatSwitchOn = true;
        }else{
          _isHeatSwitchOn = false;
        }
      });

      if (_refreshController.isRefresh) {
        _refreshController.refreshCompleted();
      }
    }catch(e){
      if (_refreshController.isRefresh) {
        _refreshController.refreshCompleted();
      }
    }
  }


  List<Widget> getWidgetList() {
    List<Widget> list = [];
    list.add(CarImageWidget(imageUrl: _carInfoModel?.image ?? '', newEnergyBatteryModel: _newEnergyBatteryModel,));
    list.add(CarInfoWidget(statusModel: _carStatusModel,yesterdayMileage: _yesterdayMileage,));
    list.add(SizedBox(height: 20,));
    if(_chargeModel != null){
      CarServiceStatusModel? serviceStatusModel = (_chargeModel!.serviceStatusList ?? []).isNotEmpty ?
      _chargeModel!.serviceStatusList!.first : null;
      list.add(SettingItemWidget(
        leftIconPath: serviceStatusModel?.serviceStatusImage ?? 'assets/images/use_car_page/older_ev_page/ev_charge_auto_icon.png',
        title: serviceStatusModel?.serviceStatusName ?? '---',
        rightType: SettingItemType.toggle,
        toggleOffImageUrl: 'assets/images/use_car_page/older_ev_page/energy_switch_off.png',
        toggleOnImageUrl: 'assets/images/use_car_page/older_ev_page/energy_switch_on.png',
        isToggleOn: _isAutoChargeSwitchOn,
        onToggle: (){
          if(!_isAutoChargeSwitchOn){
            showWillOpenSmartChargeAlertDialog(context);
          }else{
            // setState(() {
            //   _isAutoChargeSwitchOn = !_isAutoChargeSwitchOn;
            // });
            setSmartChargeWithAction(0);
          }
        },
        )
      );
      if(!_isAutoChargeSwitchOn){
        list.add(SettingItemWidget(
          leftIconPath: 'assets/images/use_car_page/older_ev_page/ev_charge_manual_icon.png',
          title: '12V蓄电池手动补电',
          rightType: SettingItemType.button,
          buttonColor: Color(0xff384967),
          buttonText: '一键补电',
          isButtonEnable: _newEnergyBatteryModel?.banRecharge == '0',
          onToggle: (){
            sendManualChargeRequest();
          },
        )
        );
      }
    }
    if(_batteryModel != null){
      CarServiceStatusModel? serviceStatusModel = (_batteryModel!.serviceStatusList ?? []).isNotEmpty ?
      _batteryModel!.serviceStatusList!.first : null;
      list.add(SettingItemWidget(
        leftIconPath: serviceStatusModel?.serviceStatusImage ?? 'assets/images/use_car_page/older_ev_page/newEnergy.warm.icon.png',
        title: serviceStatusModel?.serviceStatusName ?? '---',
        rightType: SettingItemType.toggle,
        toggleOffImageUrl: 'assets/images/use_car_page/older_ev_page/energy_switch_off.png',
        toggleOnImageUrl: 'assets/images/use_car_page/older_ev_page/energy_switch_on.png',
        isToggleOn: _isHeatSwitchOn,
        onToggle: (){
          showSureAlertViewWithAction(context, _isHeatSwitchOn ? 0:1);
        },
      )
      );
    }
    return list;
  }

  /// 比较传入的时间和保存时间，判定状态是否过期
  /// [timeStr]：输入的时间字符串，格式为yyyy-MM-dd HH:mm:ss
  /// [oldTime]：保存的时间字符串，格式为yyyy-MM-dd HH:mm:ss
  bool isCarStatusOutOfDate(String timeStr, String? oldTime) {
    if (oldTime == null || oldTime.isEmpty) {
      // 如果没有保存的时间，则直接返回true，表示过期，需要保存当前的时间
      LogManager().log("[Bat_Collect]===保存时间为空，输入时间为$timeStr，保存输入时间为状态时间");
      return true;
    } else {
      // 解析输入时间和保存时间
      DateFormat format = DateFormat('yyyy-MM-dd HH:mm:ss');
      DateTime newTime = format.parse(timeStr);
      DateTime savedTime = format.parse(oldTime);

      // 转换为Unix时间戳（毫秒）
      int newTimeInterval = newTime.millisecondsSinceEpoch;
      int savedTimeInterval = savedTime.millisecondsSinceEpoch;

      // 计算时间差
      int dur = newTimeInterval - savedTimeInterval;

      if (dur < 0) {
        // 输入的时间不比保存的时间晚，状态未过期
        LogManager().log("[TIME_TEST]===保存时间为$oldTime，输入时间为$timeStr，相差$dur毫秒，状态未过期");
        return false;
      } else {
        // 输入的时间比保存的时间晚，状态已过期
        LogManager().log("[TIME_TEST]===保存时间为$oldTime，输入时间为$timeStr，相差$dur毫秒，状态已过期，更新输入时间为状态时间");
        return true;
      }
    }
  }


  void setSmartChargeWithAction(int action) async {
    if(_carInfoModel == null || (_carInfoModel?.vin ?? '').isEmpty ){
      return;
    }
    String vin = _carInfoModel?.vin ?? '';
    try {
      String address = 'junApi/sgmw/car/warning/charging/status/change';
      Map<String, dynamic> params = {};
      params['chargingStatus'] = action;
      params['vin'] = vin;
      String actionDesc = action == 1 ? '开启' : '关闭';
      String requestStatus = '正在$actionDesc 12V 蓄电池自动补电';
      LoadingManager.show(status: requestStatus);
      var response = await carAPI.requestCarControlServiceWithURLStr(
          address, params);
      CarNewEnergyBatteryModel newEnergyBatteryModel = await carAPI
          .getNewEnergyCarBatteryStatusWithVin(vin);
      LoadingManager.showSuccess('设置成功\n12V蓄电池自动补电已$actionDesc');
      setState(() {
        _isAutoChargeSwitchOn = action == 1 ? true : false;
      });
    }catch( e){
      if(e is APIException){
        LoadingManager.showError('设置失败\n ${e.message}');
      }
    }
  }

  void sendManualChargeRequest() async{
    if(_carInfoModel == null || (_carInfoModel?.vin ?? '').isEmpty ){
      return;
    }
    String vin = _carInfoModel?.vin ?? '';
    String actionDesc = '手动一键补电';
    try {
      String address = 'junApi/sgmw/car/control/recharge';
      Map<String, dynamic> params = {};
      params['status'] = 1;
      params['vin'] = vin;
      String requestStatus = '$actionDesc...';
      LoadingManager.show(status: requestStatus);
      var response = await carAPI.requestCarControlServiceWithURLStr(
          address, params);
      LoadingManager.showSuccess('$actionDesc成功');
      Map<String, dynamic> jsonMap = _newEnergyBatteryModel?.toJson() ?? {};
      if(jsonMap.containsKey('banRecharge')) {
        jsonMap['banRecharge'] = 1;
      }
      setState(() {
        _newEnergyBatteryModel = CarNewEnergyBatteryModel.fromJson(jsonMap);
      });

    }catch( e){
      if(e is APIException){
        LoadingManager.showError('$actionDesc失败\n ${e.message}');
      }
    }
  }

  void setEnergyCarParkHeatWithAction(int action) async {
    if(_carInfoModel == null || (_carInfoModel?.vin ?? '').isEmpty ){
      return;
    }
    String vin = _carInfoModel?.vin ?? '';
    try {
      String address = 'junApi/sgmw/car/option/keepWarn';
      Map<String, dynamic> params = {};
      params['status'] = action;
      params['vin'] = vin;
      String actionDesc = action == 1 ? '开启' : '关闭';
      String requestStatus = '正在发起$actionDesc电池智能保温设置 请稍候...';
      LoadingManager.show(status: requestStatus);
      var response = await carAPI.requestCarControlServiceWithURLStr(
          address, params);
      LoadingManager.showSuccess('设置成功\n电池智能保温功能已$actionDesc');
      setState(() {
        _isHeatSwitchOn = action == 1 ? true : false;
      });
    }catch( e){
      if(e is APIException){
        LoadingManager.showError('设置失败\n ${e.message}');
      }
    }
  }

  void showWillOpenSmartChargeAlertDialog(BuildContext context) {
    DialogManager().showCustomDialog(
      CustomDialog(
        title: "12V蓄电池自动补电",
        content: "1、开启自动补电后,当您的爱车12V蓄电池电压过低时，车机将自动使用动力蓄电池给12V蓄电池补电，以避免因12V蓄电池亏电影响您的出行。\n2、如果您不想使用自动补电功能，也可以进行手动“一键补电”进行补电。",
        agreementText: '我已阅读并同意《12V蓄电池自动补电免责声明》',
        agreementLinkText: '《12V蓄电池自动补电免责声明》',
        forceAgree: true,
        buttonHeight: 70,
        onLinkPressed: (){
          String url = WebViewURLTool.disclaimerURL();
          JumpTool().openWeb(context, url, false);
        },
        buttons: [
          DialogButton(
              label: "暂不开启",
              onPressed: () {
                LogManager().debug("暂不开启按钮被点击");
              },
              backgroundColor: Colors.white,
              textColor: Colors.black),
          DialogButton(
              label: "立即开启",
              onPressed: () {
                // setState(() {
                //   _isAutoChargeSwitchOn = !_isAutoChargeSwitchOn;
                // });
                setSmartChargeWithAction(1);
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xffea0029)),
        ],
      ),
    );
  }

  void showSureAlertViewWithAction(BuildContext context, int heatAction) {
    String message = heatAction == 1
        ? '开启智能保温功能后，在插枪充满电后会利用外部电源将动力电池温度维持在最佳工作温度，期间会产生额外耗电，请知悉！'
        : '关闭智能保温功能后，在插枪充满电后不再进行保温。严寒天气用车时建议提前打开智能保温功能进行保温操作。';

    String leftTitle = heatAction == 1 ? '暂不开启' : '暂不关闭';
    String rightTitle = heatAction == 1 ? '确认开启' : '确认关闭';
    DialogManager().showCustomDialog(
      CustomDialog(
        title: "电池智能保温提示",
        content: message,
        buttonHeight: 70,
        onLinkPressed: (){
          String url = WebViewURLTool.disclaimerURL();
          JumpTool().openWeb(context, url, false);
        },
        buttons: [
          DialogButton(
              label: leftTitle,
              onPressed: () {
              },
              backgroundColor: Colors.white,
              textColor: Colors.black),
          DialogButton(
              label: rightTitle,
              onPressed: () {
                setEnergyCarParkHeatWithAction(heatAction);
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xffea0029)),
        ],
      ),
    );
  }

  @override
  void pageInitState() {
    _carInfoModel = GlobalData().carInfoModel;
    _carStatusModel = null;
    String pageTitle = _carInfoModel?.carTypeName ?? '爱车信息';
    if((_carInfoModel?.carName ?? '').isNotEmpty){
      pageTitle = _carInfoModel?.carName ?? '爱车信息';
    }
    WidgetsBinding.instance.addPostFrameCallback((_) {
      updateAppBarTitle(pageTitle);
      _refreshController.requestRefresh();
    });
  }

  @override
  Widget buildPageContent(BuildContext context) {
    // 在此处添加页面ui设置，默认添加一个center text组件
    return CustomSmartRefresher(
      controller: _refreshController,
      onRefresh: onRefresh,
      child: ListView(
        children: getWidgetList(),
      ),
    );
  }

  @override
  void dispose() {
    // 在此处添加页面销毁逻辑
    _refreshController?.dispose();
    super.dispose();
  }
}