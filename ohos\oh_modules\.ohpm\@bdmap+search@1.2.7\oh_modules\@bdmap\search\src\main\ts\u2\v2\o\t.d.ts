import { LocationShareURLOption } from "../../../d/o/p";
import { PoiDetailShareURLOption } from "../../../d/o/q";
import { RouteShareURLOption } from "../../../d/o/r";
import { ShareUrlResult } from "../../../d/o/s";
export interface IShareUrlSearch {
    requestPoiDetailShareUrl(option: PoiDetailShareURLOption): Promise<ShareUrlResult>;
    requestLocationShareUrl(option: LocationShareURLOption): Promise<ShareUrlResult>;
    requestRouteShareUrl(option: RouteShareURLOption): Promise<ShareUrlResult>;
}
export declare class ShareUrlSearchImp implements IShareUrlSearch {
    requestPoiDetailShareUrl(r31: PoiDetailShareURLOption): Promise<ShareUrlResult>;
    requestLocationShareUrl(q31: LocationShareURLOption): Promise<ShareUrlResult>;
    requestRouteShareUrl(p31: RouteShareURLOption): Promise<ShareUrlResult>;
}
