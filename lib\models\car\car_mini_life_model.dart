import 'package:flutter/foundation.dart';
import 'package:quiver/core.dart';

import 'index.dart';

@immutable
class CarMiniLifeModel {

  const CarMiniLifeModel({
    required this.ranking,
    required this.cutEmission,
    required this.save,
    required this.image,
    required this.url,
  });

  final String ranking;
  final String cutEmission;
  final String save;
  final String image;
  final String url;

  factory CarMiniLifeModel.fromJson(Map<String,dynamic> json) => CarMiniLifeModel(
    ranking: json['ranking'].toString(),
    cutEmission: json['cutEmission'].toString(),
    save: json['save'].toString(),
    image: json['image'].toString(),
    url: json['url'].toString()
  );
  
  Map<String, dynamic> toJson() => {
    'ranking': ranking,
    'cutEmission': cutEmission,
    'save': save,
    'image': image,
    'url': url
  };

  CarMiniLifeModel clone() => CarMiniLifeModel(
    ranking: ranking,
    cutEmission: cutEmission,
    save: save,
    image: image,
    url: url
  );


  CarMiniLifeModel copyWith({
    String? ranking,
    String? cutEmission,
    String? save,
    String? image,
    String? url
  }) => CarMiniLifeModel(
    ranking: ranking ?? this.ranking,
    cutEmission: cutEmission ?? this.cutEmission,
    save: save ?? this.save,
    image: image ?? this.image,
    url: url ?? this.url,
  );

  @override
  bool operator ==(Object other) => identical(this, other)
    || other is CarMiniLifeModel && ranking == other.ranking && cutEmission == other.cutEmission && save == other.save && image == other.image && url == other.url;

  @override
  int get hashCode => ranking.hashCode ^ cutEmission.hashCode ^ save.hashCode ^ image.hashCode ^ url.hashCode;
}
