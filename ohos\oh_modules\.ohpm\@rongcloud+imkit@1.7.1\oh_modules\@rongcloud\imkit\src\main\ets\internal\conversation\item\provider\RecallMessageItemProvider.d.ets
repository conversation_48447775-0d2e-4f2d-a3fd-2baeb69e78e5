// @keepTs
// @ts-nocheck
/**
 * Created on 2024/9/6
 * <AUTHOR>
 */
import { RecallNotificationMessage } from '@rongcloud/imlib';
import { BaseNotificationMessageItemProvider } from '../../../../conversation/item/provider/BaseNotificationMessageItemProvider';
import { UiMessage } from '../../../../conversation/model/UiMessage';
export declare class RecallMessageItemProvider extends BaseNotificationMessageItemProvider<RecallNotificationMessage> {
    getMessageWrapBuilder(): WrappedBuilder<[
        Context,
        UiMessage,
        number
    ]>;
    getSummaryTextByMessageContent(w188: Context, x188: RecallNotificationMessage): Promise<MutableStyledString>;
}
@Builder
export declare function bindMessageData(l188: Context, m188: UiMessage, n188: number): void;
@Component
export declare struct RecallMessageView {
    @Require
    @Prop
    context: Context;
    @Require
    @Prop
    index: number;
    @ObjectLink
    uiMessage: UiMessage;
    private messageContent;
    @State
    operatorName: string | Resource;
    @State
    editTitle: string | Resource;
    aboutToAppear(): void;
    build(): void;
    onMessageRecallEditClick(r186: UiMessage, s186: ClickEvent): void;
}
