// @keepTs
// @ts-nocheck
/**
 * Created on 2024/08/16
 *
 * 消息发送时状态变化的通知事件
 *
 * <AUTHOR>
 */
import { EngineError, Message } from '@rongcloud/imlib';
import { MediaMessageSendState } from '../../base/enum/MediaMessageSendState';
import { MessageOperationEvent } from './MessageOperationEvent';
export declare class SendMediaMessageEvent extends MessageOperationEvent {
    private _state;
    private _error?;
    private _progress;
    private _message;
    set message(j25: Message);
    get message(): Message;
    getProgress(): number;
    setProgress(i25: number): void;
    setState(h25: MediaMessageSendState): void;
    getState(): MediaMessageSendState;
    setError(g25: EngineError | undefined): void;
    getError(): EngineError | undefined;
    constructor(d25: MediaMessageSendState, e25: Message);
}
