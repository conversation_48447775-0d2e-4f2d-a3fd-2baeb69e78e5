import type { CommonEvent, TOverlayListener } from "../../g1/h1"; import type { ColorString, INinePatch } from "../../g1/a2"; import { Gravity, Visibility } from "../../util/b1/c1"; import type BmBitmapResource from "../c2/f2/t3"; import type Bm<PERSON>ase<PERSON> from "../c2/n1/a4"; import type ImageEntity from "../o/s"; import type OverlayListener from "../m/i2"; import c46 from "./v4"; import type PopView from "./o1";         export default class BaseUI extends c46 { private mClickAction; private mDescription; private mHasBackgroundResource; private mHasBackgroundColor;   owner: PopView;   eventListener: TOverlayListener; private mInstance; constructor(l61: number, instance: BmBaseUI);         getBmBaseUI(): BmBaseUI;         addEventListener(model: CommonEvent, p46: Function): void;         removeEventListener(model: CommonEvent, n46: Function): void;       createEvent(model: CommonEvent, m46: Function): { event: { [x: symbol]: Function; }; };       setOwner(l46: PopView): void;       getInstanceUI(): BmBaseUI;         setListener(listener: OverlayListener): void;           findViewByName(name: string): BmBaseUI;           findViewByShell(k46: number): BmBaseUI | import("../bmsdk/ui/BmGroupUI").default;         setBackground(i46: ImageEntity, j46?: INinePatch): void;       updateBitMap(bitmap: BmBitmapResource): void;         hasBackgroundResource(): boolean;           setBackgroundColor(g46: ColorString | number): void;           setBkColorOfLeft(f46: number): void;           setBkColorOfRight(e46: number): void; hasBackgroundColor(): boolean;         setWidth(width: number): void;         setHeight(height: number): void;         setGravity(gravity: Gravity): void;         setPadding(left: number, top: number, right: number, bottom: number): void;         setMargin(left: number, top: number, right: number, bottom: number): void;         setVisibility(visibility: Visibility): void;           setPBVisibility(d46: number): void;           setClickable(clickable: boolean): void;         setClickAction(clickAction: string): void;         getClickAction(): string;         setDescription(description: string): void;         getDescription(): string; } 