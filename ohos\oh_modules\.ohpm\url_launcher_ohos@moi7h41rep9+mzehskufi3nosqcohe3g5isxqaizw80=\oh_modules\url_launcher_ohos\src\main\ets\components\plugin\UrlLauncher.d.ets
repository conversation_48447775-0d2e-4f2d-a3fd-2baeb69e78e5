import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import MessageCodec from '@ohos/flutter_ohos/src/main/ets/plugin/common/MessageCodec';
import { UrlLauncherApi, WebViewOptions } from './Messages';
import common from '@ohos.app.ability.common';
export declare class Url<PERSON>auncher implements UrlLauncherApi {
    static LAUNCH_TYPE_TEL: string;
    static LAUNCH_TYPE_WEB_HTTP: string;
    static LAUNCH_TYPE_WEB_HTTPS: string;
    static LAUNCH_TYPE_MAILTO: string;
    static LAUNCH_TYPE_SMS: string;
    static LAUNCH_TYPE_FILE: string;
    static LAUNCH_TYPE_APP_GALLERY: string;
    static MMS_BUNDLE_NAME: string;
    static MMS_ABILITY_NAME: string;
    static MMS_ENTITIES: string;
    private context;
    constructor(ctx: common.UIAbilityContext);
    getPermission(): void;
    canLaunchUrl(url: string): boolean;
    launchUrl(url: string, headers: Map<string, string>): boolean;
    format(number: string): string;
    launchSms(url: string): boolean;
    launchTel(url: string): boolean;
    launchFile(url: string): boolean;
    launchWeb(url: string, headers: Map<string, string>): boolean;
    launchMail(url: string): boolean;
    /**
     * 跳转到应用商店详情页
     * 文档参考：https://developer.huawei.com/consumer/cn/doc/harmonyos-faqs/faqs-ability-kit#section42001122242
     * @param url
     * @returns
     */
    launchAppGallery(url: string): boolean;
    launchOther(url: string): boolean;
    parseUrl(url: string, prefix: string): string;
    private ensureContext;
    openUrlInWebView(url: string, options: WebViewOptions): boolean;
    closeWebView(): boolean;
    getCodec(): MessageCodec<ESObject>;
    setup(binaryMessenger: BinaryMessenger, api: UrlLauncherApi): void;
}
