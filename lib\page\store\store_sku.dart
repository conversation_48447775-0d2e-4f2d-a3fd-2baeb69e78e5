import 'package:flutter/material.dart';
import 'package:flutter_uikit/flutter_uikit.dart';
import 'package:wuling_flutter_app/api/store/store_api.dart';
import 'package:wuling_flutter_app/common/action.dart';
import 'package:wuling_flutter_app/models/store/store_detail/store_detail_model.dart';
import 'package:wuling_flutter_app/models/store/store_detail/store_sku_model.dart';
import 'package:wuling_flutter_app/plugins/base_plugin/base_plugin.dart';
import 'package:wuling_flutter_app/plugins/share_plugin/share_plugin.dart';
import 'package:wuling_flutter_app/utils/sku_util.dart';

import '../../models/store/store_detail/store_goods_sku_model.dart';
import '../../models/store/store_detail/store_select_shop_model.dart';
import '../../utils/manager/loading_manager.dart';
import '../../utils/manager/location_manager.dart';
import '../../utils/manager/log_manager.dart';

class StoreSKUA extends StatefulWidget {
  const StoreSKUA({
    super.key,
    required this.id,
    required this.code,
    required this.image,
    required this.name,
    required this.commodityClassifyId,
    this.currentSkuId, // 当前SKU ID参数
    this.currentQuantity, // 当前数量参数
  });
  final int id;
  final int code;
  final String image;
  final String name;
  final int commodityClassifyId;
  final int? currentSkuId; // 当前选中的SKU ID
  final int? currentQuantity; // 当前商品数量

  @override
  State<StoreSKUA> createState() => _StoreSKUAState();
}

class _StoreSKUAState extends State<StoreSKUA> {
  StoreSKUModel model = StoreSKUModel.fromJson({});

  int number = 1;

  StoreGoodsSKUModel selectSku = StoreGoodsSKUModel.fromJson({});

  List<List<StoreGoodsSKUModel>> attrmodels = [];

  List<StoreGoodsSKUModel> attrList = [];

  List<String> selectAtter = [];

  Map<String, StoreGoodsSKUModel> mSkuCollection = {};

  int sort = 0;

  StoreSelectShopModel shopModel = StoreSelectShopModel.fromJson({});

  @override
  void initState() {
    // 如果有传入的当前数量，使用传入的数量，否则默认为1
    if (widget.currentQuantity != null && widget.currentQuantity! > 0) {
      number = widget.currentQuantity!;
    }

    // 获取sku
    storeApi.storeGoodsSKU(
        {"commodityId": widget.id, "channelCode": widget.code}).then((value) {
      setState(() {
        model = value;
        attrList = model.attrList;

        // 如果有传入的当前SKU ID，尝试找到对应的SKU
        StoreGoodsSKUModel? currentSku;
        if (widget.currentSkuId != null) {
          try {
            currentSku = model.skuList.firstWhere(
              (sku) => sku.skuId == widget.currentSkuId,
            );
          } catch (e) {
            currentSku = model.skuList.first;
          }
        } else {
          currentSku = model.skuList.first;
        }

        selectSku = StoreGoodsSKUModel.fromJson({
          "skuAttributeValueNames": currentSku.name,
          "skuImage": currentSku.image,
          "originalPrice": currentSku.originalPrice,
          "commodityId": currentSku.id,
          "commoditySkuId": widget.currentSkuId,
          "skuStock": currentSku.skuStock,
          "skuAttributeValueIds": currentSku.skuAttributeValueIds,
        });

        mSkuCollection = SkuUtil.skuCollection(model);
        attrmodels.clear();
        selectAtter.clear();

        for (int index = 0; index < attrList.length; index++) {
          List<StoreGoodsSKUModel> models = [];
          for (StoreGoodsSKUModel lmAttributeValueList
              in attrList[index].lmAttributeValueList) {
            models.add(lmAttributeValueList);
          }
          selectAtter.add("");
          attrmodels.add(models);
        }

        // 如果有当前选中的SKU，解析并设置对应的属性选中状态
        if (widget.currentSkuId != null &&
            currentSku != null &&
            currentSku.skuAttributeValueIds.isNotEmpty) {
          // 获取当前SKU的属性值ID列表
          List<String> skuAttributeIds =
              currentSku.skuAttributeValueIds.split(',');

          // 根据属性值ID设置选中状态
          for (String attrValueId in skuAttributeIds) {
            int attrId = int.tryParse(attrValueId) ?? 0;
            if (attrId == 0) continue;

            // 遍历所有属性组，找到对应的属性值并设置为选中
            for (int i = 0; i < attrmodels.length; i++) {
              for (int j = 0; j < attrmodels[i].length; j++) {
                if (attrmodels[i][j].attributeValueId == attrId) {
                  // 设置选中状态
                  attrmodels[i][j].clickType = 1;
                  // 更新选中的属性名称
                  selectAtter[i] = attrmodels[i][j].attributeValueName;
                  break;
                }
              }
            }
          }

          // 更新其他属性的可选状态
          _updateAttributeStates();
        }
      });
    });

    if (widget.commodityClassifyId == 0 || widget.commodityClassifyId == 3) {
      BasePlugin.getLocationGCJ02().then((value) {
        if (value != null) {
          storeApi.storeSelectShop({
            "cityName": value['locality'],
            "lat": value['latitude'].toString(),
            "lng": value['longitude'].toString(),
            "commodityId": widget.id.toString(),
            "channelCode": widget.code,
            "sort": sort.toString()
          }).then((data) {
            if (data.isNotEmpty) {
              setState(() {
                shopModel = data[0];
              });
            }
          });
        }
      });
    }

    super.initState();
  }

  // 添加一个辅助方法来更新属性状态
  void _updateAttributeStates() {
    List<StoreGoodsSKUModel> cacheSelected = [];
    for (int i = 0; i < attrmodels.length; i++) {
      for (int j = 0; j < attrmodels[i].length; j++) {
        if (attrmodels[i][j].clickType == 1) {
          cacheSelected.add(attrmodels[i][j]);
        }
      }
    }

    if (cacheSelected.isNotEmpty) {
      List<StoreGoodsSKUModel> attributeValueIds = [];
      for (int i = 0; i < attrmodels.length; i++) {
        for (int j = 0; j < attrmodels[i].length; j++) {
          attributeValueIds.clear();
          attributeValueIds.addAll(cacheSelected);
          StoreGoodsSKUModel? hasValue;
          for (StoreGoodsSKUModel attributeValue in attributeValueIds) {
            if (attributeValue.attributeId == attrmodels[i][j].attributeId) {
              hasValue = attributeValue;
              break;
            }
          }
          attributeValueIds.remove(hasValue);
          attributeValueIds.add(attrmodels[i][j]);

          StringBuffer tempbuffer = StringBuffer();
          sortByAttributeId(attributeValueIds);
          attributeValueIds.forEach((element) {
            tempbuffer.write(element.attributeValueId);
            tempbuffer.write(',');
          });
          String str = tempbuffer.toString();
          if (str != "") {
            if (mSkuCollection[str.substring(0, str.length - 1)] != null) {
              attrmodels[i][j].clickType =
                  attrmodels[i][j].clickType == 1 ? 1 : 0;
            } else {
              attrmodels[i][j].clickType = 2;
            }
          }
        }
      }
    }

    // 更新当前选中的SKU
    for (StoreGoodsSKUModel element in model.skuList) {
      bool isSelect = true;

      // 获取当前选中的所有属性值ID
      List<String> selectedAttributeValueIds = [];
      for (int i = 0; i < attrmodels.length; i++) {
        for (int j = 0; j < attrmodels[i].length; j++) {
          if (attrmodels[i][j].clickType == 1) {
            selectedAttributeValueIds
                .add(attrmodels[i][j].attributeValueId.toString());
          }
        }
      }

      // 如果没有选中任何属性，跳过
      if (selectedAttributeValueIds.isEmpty) {
        isSelect = false;
      } else {
        // 检查当前SKU的属性值ID是否包含所有选中的属性值ID
        List<String> skuAttributeValueIds =
            element.skuAttributeValueIds.split(',');
        for (String selectedId in selectedAttributeValueIds) {
          if (!skuAttributeValueIds.contains(selectedId)) {
            isSelect = false;
            break;
          }
        }

        // 还要确保选中的属性数量和SKU的属性数量一致（完全匹配）
        if (isSelect &&
            selectedAttributeValueIds.length != skuAttributeValueIds.length) {
          isSelect = false;
        }
      }

      // for (var str in selectAtter) {
      //   List<String> l = element.name.split(",");
      //   if (str != "" && !l.contains(str)) {
      //     isSelect = false;
      //   }
      // }

      if (isSelect) {
        selectSku = element;
        break; // 找到匹配的SKU后立即退出
      }
    }
  }

  toast(int position, int index){
    ///第一点判断一个下 是否越前选择下面的，要重头选到下面
    if (position > 0) {
      for (int i = 0; i < position;i++) {
        if(selectAtter[i] == ""){
          LoadingManager.showToast('请选择${attrList[i].attributeName}');
          throw "";
        }
      }
    }
  }

  skuAction(int position, int index) {
    if (attrmodels[position][index].clickType == 2) {
      return;
    }

    toast(position,index);
    // ///第一点判断一个下 是否越前选择下面的，要重头选到下面
    // if (position > 0) {
    //   if (selectAtter[position - 1] == '') {
    //     LoadingManager.showToast('请选择${attrList[position - 1].attributeName}');
    //     return;
    //   }
    // }

    ///首先记录，我第一项选择的
    if (attrmodels[position][index].clickType == 1) {
      for (int i = position; i < attrmodels.length; i++) {
        for (int j = 0; j < attrmodels[i].length; j++) {
          attrmodels[i][j].clickType = 0;
        }
        selectAtter[i] = '';
      }
      attrmodels[position][index].clickType = 0;
      selectAtter[position] = '';
    } else {
      for (int i = position; i < attrmodels.length; i++) {
        for (int j = 0; j < attrmodels[i].length; j++) {
          if (attrmodels[i][j].clickType == 2) {
            attrmodels[i][j].clickType = 2;
          } else {
            attrmodels[i][j].clickType = 0;
          }
        }
        selectAtter[i] = '';
      }
      attrmodels[position][index].clickType = 1;
      selectAtter[position] = attrmodels[position][index].attributeValueName;
    }

    List<StoreGoodsSKUModel> cacheSelected = [];
    for (int i = 0; i < attrmodels.length; i++) {
      for (int j = 0; j < attrmodels[i].length; j++) {
        if (attrmodels[i][j].clickType == 1) {
          cacheSelected.add(attrmodels[i][j]);
        }
      }
    }
    if (cacheSelected.isNotEmpty) {
      List<StoreGoodsSKUModel> attributeValueIds = [];
      for (int i = 0; i < attrmodels.length; i++) {
        for (int j = 0; j < attrmodels[i].length; j++) {
          attributeValueIds.clear();
          attributeValueIds.addAll(cacheSelected);
          StoreGoodsSKUModel? hasValue;
          for (StoreGoodsSKUModel attributeValue in attributeValueIds) {
            if (attributeValue.attributeId == attrmodels[i][j].attributeId) {
              hasValue = attributeValue;
              break;
            }
          }
          attributeValueIds.remove(hasValue);
          attributeValueIds.add(attrmodels[i][j]);

          StringBuffer tempbuffer = StringBuffer();
          sortByAttributeId(attributeValueIds);
          attributeValueIds.forEach((element) {
            tempbuffer.write(element.attributeValueId);
            tempbuffer.write(',');
          });
          String str = tempbuffer.toString();
          if (str != "") {
            if (mSkuCollection[str.substring(0, str.length - 1)] != null) {
              attrmodels[i][j].clickType =
                  attrmodels[i][j].clickType == 1 ? 1 : 0;
            } else {
              attrmodels[i][j].clickType = 2;
            }
          }
        }
      }
    }
    for (StoreGoodsSKUModel element in model.skuList) {
      bool isSelect = true;

      // 获取当前选中的所有属性值ID
      List<String> selectedAttributeValueIds = [];
      for (int i = 0; i < attrmodels.length; i++) {
        for (int j = 0; j < attrmodels[i].length; j++) {
          if (attrmodels[i][j].clickType == 1) {
            selectedAttributeValueIds
                .add(attrmodels[i][j].attributeValueId.toString());
          }
        }
      }

      // 如果没有选中任何属性，跳过
      if (selectedAttributeValueIds.isEmpty) {
        isSelect = false;
      } else {
        // 检查当前SKU的属性值ID是否包含所有选中的属性值ID
        List<String> skuAttributeValueIds =
            element.skuAttributeValueIds.split(',');
        for (String selectedId in selectedAttributeValueIds) {
          if (!skuAttributeValueIds.contains(selectedId)) {
            isSelect = false;
            break;
          }
        }

        // 还要确保选中的属性数量和SKU的属性数量一致（完全匹配）
        if (isSelect &&
            selectedAttributeValueIds.length != skuAttributeValueIds.length) {
          isSelect = false;
        }
      }

      // for (var str in selectAtter) {
      //   List<String> l = element.name.split(",");
      //   if (str != "" && !l.contains(str)) {
      //     isSelect = false;
      //   }
      // }

      if (isSelect) {
        selectSku = element;
        break; // 找到匹配的SKU后立即退出
      }
    }
  }

  void sortByAttributeId(List<StoreGoodsSKUModel> list) {
    for (int j = 0; j < list.length - 1; j++) {
      for (int k = 0; k < list.length - 1 - j; k++) {
        StoreGoodsSKUModel cacheEntity = list[k];
        if (list[k].attributeId > list[k + 1].attributeId) {
          // 交换数据
          cacheEntity = list[k];
          list[k] = list[k + 1];
          list[k + 1] = cacheEntity;
        }
      }
    }
  }

  skuViews() {
    List<Widget> views = [];
    for (int i = 0; i < attrmodels.length; i++) {
      List<StoreGoodsSKUModel> models = attrmodels[i];
      views.add(
        UIText(
          data: attrList[i].attributeName,
          fontSize: 13,
          padding: const EdgeInsets.only(left: 10),
        ),
      );
      List<Widget> children = [];
      for (int j = 0; j < models.length; j++) {
        StoreGoodsSKUModel e = models[j];
        if(e.clickType != 2){
          children.add(UIButton(
            onPressed: () {
              selectSku = StoreGoodsSKUModel.fromJson({
                "skuAttributeValueNames": selectSku.name,
                "skuImage": selectSku.image,
                "originalPrice": selectSku.originalPrice,
              });
              if (e.clickType != 2) {
                skuAction(i, j);
              }
              setState(() {});
            },
            color: e.clickType == 1
                ? 0xFFFF0000
                : (e.clickType == 2 ? 0xFFEFEFEF : 0xFFEEEEEE),

            radius: 0,
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            child: UIText(
              data: e.attributeValueName,
              color: e.clickType == 1
                  ? 0xFFFFFFFF
                  : (e.clickType == 2 ? 0xFF999999 : 0xFF333333),
              fontSize: 13,
            ),
          ));
        }
      }

      views.add(Container(
        margin: const EdgeInsets.symmetric(vertical: 5),
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
        child: Wrap(
          spacing: 10,
          runSpacing: 10,
          children: children,
        ),
      ));
    }

    return views;
  }

  String getAmount() {
    if (selectSku.originalPrice == "暂无售价" || double.parse(selectSku.originalPrice) >= 2000000) {
      return "暂无售价";
    } else {
      return selectSku.originalPrice;
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        NavigatorAction.init(context);
      },
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: GestureDetector(
          onTap: () {},
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Container(
                alignment: Alignment.bottomCenter,
                color: Colors.white,
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.8,
                  minHeight: 400,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      height: 120,
                      padding:
                          EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          UIImage(
                            imgStr: selectSku.image,
                            radius: 0,
                            margin: EdgeInsets.only(right: 10),
                            width: 100,
                            height: 100,
                            fit: BoxFit.cover,
                          ),
                          Expanded(
                              flex: 1,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Row(
                                    children: [
                                      Container(
                                        margin: const EdgeInsets.only(right: 8),
                                        decoration: BoxDecoration(
                                            color: Colors.red,
                                            borderRadius:
                                                BorderRadius.circular(2)),
                                        child: UIText(
                                          data: "平台",
                                          color: 0xFFFFFFFF,
                                          fontSize: 9,
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 5, vertical: 1),
                                        ),
                                      ),
                                      Expanded(
                                        child: UIText(
                                          data: model.name,
                                          overflow: TextOverflow.ellipsis,
                                          fontSize: 15,
                                          maxLines: 2,
                                        ),
                                        flex: 1,
                                      )
                                    ],
                                  ),
                                  UIText(
                                    data: "库存 ${selectSku.skuStock} 件",
                                    color: 0xFF999999,
                                    fontSize: 12,
                                  ),
                                  UIText(
                                    data: "￥${getAmount()}",
                                    color: 0xFFFF0000,
                                  ),
                                  UIText(
                                    data: "已选：${selectAtter.join(" ")}",
                                    color: 0xFF999999,
                                    fontSize: 12,
                                  ),
                                ],
                              )),
                          IconButton(
                              onPressed: () {
                                NavigatorAction.init(context);
                              },
                              icon: Icon(Icons.cancel)),
                        ],
                      ),
                    ),
                    Expanded(
                      child: SingleChildScrollView(
                        padding: EdgeInsets.only(bottom: 10),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ...skuViews(),
                            Row(
                              children: [
                                const Expanded(
                                    flex: 1,
                                    child: UIText(
                                      data: "数量",
                                      fontSize: 13,
                                      padding: EdgeInsets.only(left: 10),
                                    )),
                                UIButton(
                                  onPressed: () {
                                    if (number > 1) {
                                      setState(() {
                                        number -= 1;
                                      });
                                    }
                                  },
                                  child: UIText(
                                    data: "-",
                                    fontSize: 19,
                                  ),
                                  padding: EdgeInsets.symmetric(horizontal: 10),
                                ),
                                Container(
                                  width: 30,
                                  height: 20,
                                  color: Color(0xFFEEEEEE),
                                  alignment: Alignment.center,
                                  child: UIText(
                                    data: "$number",
                                    fontSize: 15,
                                  ),
                                ),
                                UIButton(
                                  onPressed: () {
                                    if (widget.commodityClassifyId != 0) {
                                      if (number < 99) {
                                        setState(() {
                                          number += 1;
                                        });
                                      }
                                    }
                                  },
                                  child: UIText(
                                    data: "+",
                                    fontSize: 19,
                                  ),
                                  margin: EdgeInsets.only(right: 10),
                                  padding: EdgeInsets.symmetric(horizontal: 10),
                                ),
                              ],
                            ),
                            (widget.commodityClassifyId == 0 ||
                                    widget.commodityClassifyId == 3)
                                ? Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const UIText(
                                        data: "选择服务门店",
                                        fontSize: 13,
                                        padding: EdgeInsets.only(
                                            left: 10, top: 30, bottom: 10),
                                      ),
                                      UIButton(
                                        onPressed: () {
                                          ShowAction.init(context)
                                              .showShop(widget.id, widget.code,
                                                  shopModel.id)
                                              .then((value) {
                                            if (value != null) {
                                              setState(() {
                                                shopModel = value;
                                              });
                                            }
                                          });
                                        },
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 10),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            UIText(
                                                data: shopModel.shortName,
                                                fontSize: 13),
                                            const Icon(
                                              Icons.arrow_drop_down,
                                              color: Colors.grey,
                                            )
                                          ],
                                        ),
                                      )
                                    ],
                                  )
                                : Container(),
                          ],
                        ),
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(
                          bottom: MediaQuery.of(context).padding.bottom + 10),
                      alignment: Alignment.center,
                      child: UIButton(
                        onPressed: () {
                          if (selectSku.id == 0 || selectSku.skuId == 0) {
                            OverlayCreate.context = context;
                            UIOverlay.toast("请选择商品参数");
                          } else if (selectSku.skuStock == 0) {
                            OverlayCreate.context = context;
                            UIOverlay.toast("暂时没有库存");
                          } else if (selectSku.id != 0) {
                            LogManager().debug('123');
                            NavigatorAction.init(context, data: {
                              "sku": selectSku,
                              "quantity": number,
                              "shopId": shopModel.id,
                              "commodityClassifyId": model.commodityClassifyId,
                            });
                          } else {
                            OverlayCreate.context = context;
                            UIOverlay.toast("请选择车型");
                          }
                        },
                        radius: 0,
                        color: 0xFFFF0000,
                        width: 350,
                        height: 45,
                        child: UIText(
                          data: "确定",
                          color: 0xFFFFFFFF,
                        ),
                      ),
                    )
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
