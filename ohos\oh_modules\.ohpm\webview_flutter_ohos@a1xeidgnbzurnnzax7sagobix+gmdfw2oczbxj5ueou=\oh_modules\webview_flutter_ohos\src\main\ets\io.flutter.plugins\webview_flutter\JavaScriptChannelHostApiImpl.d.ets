import { JavaScriptChannelHostApi } from './GeneratedOhosWebView';
import { InstanceManager } from './InstanceManager';
import { JavaScriptChannel } from './JavaScriptChannel';
import { JavaScriptChannelFlutterApiImpl } from './JavaScriptChannelFlutterApiImpl';
export declare class JavaScriptChannelHostApiImpl extends JavaScriptChannelHostApi {
    private instanceManager;
    private javaScriptChannelCreator;
    private flutterApi;
    /** Handles creating {@link JavaScriptChannel}s for a {@link JavaScriptChannelHostApiImpl}. */
    /**
     * Creates a host API that handles creating {@link JavaScriptChannel}s.
     *
     * @param instanceManager maintains instances stored to communicate with Dart
     *        objects
     * @param javaScriptChannelCreator handles creating {@link JavaScriptChannel}s
     * @param flutterApi handles sending messages to Dart
     * @param platformThreadHandler handles making callbacks on the desired thread
     */
    constructor(instanceManager: InstanceManager, javaScriptChannelCreator: JavaScriptChannelCreator, flutterApi: JavaScriptChannelFlutterApiImpl);
    create(instanceId: number, channelName: string): void;
}
export declare class JavaScriptChannelCreator {
    createJavaScriptChannel(flutterApi: JavaScriptChannelFlutterApiImpl, channelName: string): JavaScriptChannel;
}
