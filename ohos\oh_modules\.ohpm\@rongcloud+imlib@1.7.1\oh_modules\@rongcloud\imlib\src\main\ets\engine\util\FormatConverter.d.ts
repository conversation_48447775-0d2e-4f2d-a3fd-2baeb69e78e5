declare class FormatConverter {
    private constructor();
    /**
     * 将 number 数字类型转换为 uuid 字符串类型
     *
     * 接口中使用了 bigint 类型作为中间类型，由于 bigint 能表示任意大的整数，
     * 而 number 类型采用 IEEE 754 双精度 64 位浮点数表示，其能精确表示的整数范围是有限的，
     * 所以建议使用 32 表示的正整数，否则可能造成精度丢失。
     * */
    static numberToUuidString(num: number): string;
    /**
     * 将 uuid 字符串类型 转换为 number 数字类型
     *
     * 接口中使用了 bigint 类型作为中间类型，由于 bigint 能表示任意大的整数，
     * 而 number 类型采用 IEEE 754 双精度 64 位浮点数表示，其能精确表示的整数范围是有限的，
     * 所以建议使用 32 表示的正整数，否则可能造成精度丢失。
     * */
    static uuidToNumber(uuidStr: string): number;
}
export { FormatConverter };
