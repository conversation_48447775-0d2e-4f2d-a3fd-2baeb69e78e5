// @keepTs
// @ts-nocheck
import { ConversationIdentifier } from '@rongcloud/imlib';
import { UserInfoModel } from '../../user/model/UserInfoModel';
/**
 * 会话页面
 * Created on 2024/10/21
 * <AUTHOR>
 * @version 1.0.0
 */
@Entry({ routeName: 'ConversationPage' })
@Component
export declare struct ConversationPage {
    conId: ConversationIdentifier;
    private msgClickListener;
    private conversationEventListener;
    private pathStack;
    @State
    private userInGroup;
    @State
    private viewModel;
    @State
    private pageShow;
    @State
    private isEdit;
    @State
    private model;
    aboutToAppear(): void;
    aboutToDisappear(): void;
    onPageShow(): Promise<void>;
    onPageHide(): void;
    build(): void;
    @Builder
    LimitBuilder(): void;
    @Builder
    UserInGroupUnavailableBuilder(): void;
    onInputMention: (select: (user: UserInfoModel) => void) => void;
    onIsEditChange(): void;
}
