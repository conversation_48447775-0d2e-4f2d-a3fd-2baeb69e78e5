import Bundle from "../o/i1"; import ImageEntity from "../o/s"; import { LatLng, Point } from '@bdmap/base'; import type { AnyObject, IOverlayOption } from "../../g1/a2"; import type OverlayListener from "./i2"; import type { Callback, OverlayEvent, TOverlayListener } from "../../g1/h1"; import type { ImageOverlayData } from "../../g1/i1"; import type LRUCache from "../../util/m2/t4"; import type OverlayMgr from "./j2"; import type BmDrawItem from "../c2/a3"; import type BmBitmapResource from "../c2/f2/t3";             export default class Overlay { uuid: string; type: number; eventListener: TOverlayListener; private mLayerAddr; private extraInfo; private noBundle; protected mVisible: boolean; protected mZIndex: number; protected mIsClickable: boolean; protected mAlpha: number; protected mStartLevel: number; protected mEndLevel: number; protected listener: OverlayListener; private mBmDrawItem; isDestroyed: boolean;       constructor(z39: IOverlayOption, type?: number);       createBmDrawItem(type: number): void; get typeName(): string;                 addEventListener(model: OverlayEvent, w39: Function): void;                 removeEventListener(model: OverlayEvent, u39: Function): void;       createEvent(model: OverlayEvent, t39: Function): { event: { [x: symbol]: Function; }; };       fireEvent(model: OverlayEvent): void; getType(): number;       setListener(listener: OverlayListener): void;       setNoBundle(s39: string): this;         get visible(): boolean;         set visible(val: boolean);         setVisible(val: boolean): void;         getVisible(): boolean;           alpha(alpha: number): this;         getAlpha(): number;           setAlpha(alpha: number): void;         startLevel(r39: number): this;         getStartLevel(): number;         setStartLevel(q39: number): void;         endLevel(p39: number): this;         getEndLevel(): number;         setEndLevel(o39: number): void;         clickable(isClickable: boolean): this;         getClickable(): boolean;         setClickable(isClickable: boolean): void;       get layerAddr(): string;       set layerAddr(val: string);       getLayerAddr(): string;       setLayerAddr(val: string): void;         get zIndex(): number;         set zIndex(val: number);         setZIndex(val: number): void;         getZIndex(): number;         setExtraInfo(extraInfo: AnyObject): void;         getExtraInfo(): AnyObject;       getBmDrawItem(): BmDrawItem;       setBmDrawItem(item: BmDrawItem): void;         ll2mc(m39: LatLng): Point;       baseFormat(bundle: Bundle): Bundle;       imageFormat(i39: LRUCache, images: Array<ImageEntity>, callback: Callback<Array<ImageOverlayData>>): void;       getImageBitMap(image: ImageEntity, callback: Callback<BmBitmapResource | undefined>): void;       toRemoveBundle(): Bundle; update(): void;         remove(g39?: boolean): void;         isRemoved(): boolean;       preUpdatePoint(): void;       destroy(): void; toBundle(d39?: OverlayMgr): Promise<Bundle>; toString(): string; } 