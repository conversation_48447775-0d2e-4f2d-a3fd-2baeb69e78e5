/**
 * 已读回执信息，只有群聊的消息需要此属性，其他类型的会话中此属性为 null
 *
 *```
 // sequenceDiagram start
 title 消息已读 V2：群聊流程
 发送方->> 接收方: 1.发送消息
 发送方->> 接收方: 2.注册已读回执事件监听
 activate 接收方
 接收方-->> 接收方: 3.进入聊天页面，遍历所有的消息
 接收方-->> 发送方: 4.发送群聊已读回执响应
 deactivate 接收方
 activate 发送方
 发送方->> 发送方:  5.将聊天页面对应消息更新已读状态
 deactivate 发送方
 // sequenceDiagram end

 群聊具体流程描述：
 1. 发送方发送消息：发送方 App/IMKit 发送任何的记未读消息，比如文本、图片等
 2. 发送方注册已读回执时间监听器：发送方 App/IMKit 调用 IMEngine.addMessageReadReceiptV2Listener()
 3. 接收方进入聊天页面，遍历所有的消息：接收方 App/IMKit 进入聊天页面，查到当前页面 isReadReceiptMessage 为 true 的消息
 4. 接收方发送群聊已读回执响应：接收方 App/IMKit 过滤聊天页面内所有 (isReadReceiptMessage == true && hasRespond == false) 的消息，调用 IMEngine.sendReadReceiptResponseV2() 发送已读回执响应，证明接收方这些消息已读过
 5. 发送方接收群聊已读回执响应：发送方 SDK 接收到已读响应时，将本地消息的 respondUserIdList 更新，然后触发 MessageReadReceiptListener.onMessageReceiptResponse()
 6. 发送方将聊天页面对应消息更新已读状态：发送方 App/IMKit 在对应的聊天页面，找到对应的消息，更新对应的 UI
 备注：群聊如何查看消息已读？看 Message ReadReceiptInfoV2 的具体数据
 *```
 * @version 1.4.0
 * @discussion 使用 https://sequencediagram.org/ 打开时序图
 */
export declare class ReadReceiptInfoV2 {
    /**
     * 是否发送过消息回执响应。 仅对消息接收方有效。默认 false
     */
    hasRespond: boolean;
    /**
     * 会话中响应过该消息回执的成员 userId 列表
     *
     * key 用户 Id， value 响应时间（毫秒时间戳）
     */
    respondUserList: Map<string, number>;
    /**
     * 已读成员数
     * */
    readCount: number;
    /**
     * 所有成员数
     * */
    totalCount: number;
}
