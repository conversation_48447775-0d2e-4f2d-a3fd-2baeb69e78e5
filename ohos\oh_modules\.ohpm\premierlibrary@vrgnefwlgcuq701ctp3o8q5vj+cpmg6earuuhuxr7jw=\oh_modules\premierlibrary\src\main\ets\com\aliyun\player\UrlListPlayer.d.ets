import { IListPlayer } from './IListPlayer';
import { IPlayer } from './IPlayer';
import { UrlPlayer } from './UrlPlayer';
export interface UrlListPlayer extends IListPlayer, UrlPlayer {
    /**
     * 添加url源
     * @param url
     * @param uid 需保证每个url都是不一样的uid
     */
    /****
     * Add the URL source
     * @param url
     * @param uid Ensure that each URL is a unique UID
     */
    addUrl: (url: string, uid: string) => void;
    /**
     * 添加url源和相关的预加载配置
     * @param url
     * @param uid 需保证每个url都是不一样的uid
     * @param config 预加载配置
     */
    /****
     * Add the URL source and related preload count
     * @param url
     * @param uid Ensure that each URL is a unique UID
     * @param config preload config
     */
    /**
     * 更新预加载配置
     * @param config 预加载配置
     */
    /****
     * update preload config
     * @param config preload config
     */
    /**
     * 更新uid相关的预加载配置
     * @param uid 每个url都是不一样的uid
     * @param config 预加载配置
     */
    /****
     * update preload config related to uid
     * @param uid each URL is a unique UID
     * @param config preload config
     */
    /**
     * 获取预渲染的播放器实例。listPlayer在播放当前视频时，会去预渲染下一个视频，用户可以用该预渲染的实例去提前播放下一个视频
     * @return
     */
    /****
     * Move to the next
     * @return
     */
    getPreRenderPlayer: () => IPlayer | undefined;
    /**
     * 移动到下一个去播放
     * @return
     */
    /****
     * Move to the next
     * @return
     */
    moveToNext: () => boolean;
    /**
     * 移动到下一个去播放；该接口只在使用了预渲染的player（getPreRenderPlayer返回的player）去播放的时候去调用，listPlayer内部不再去播放
     * @return
     */
    /****
     * Move to the next
     * @return
     */
    moveToNextWithPrerendered: () => boolean;
    /**
     * 移动到上一个去播放
     * @return
     */
    /****
     * Move to the previous
     * @return
     */
    moveToPrev: () => boolean;
    /**
     * 移动到uid去播放
     * @param uid
     * @return
     */
    /****
     * move to uid
     * @param uid
     * @return
     */
    moveTo: (uid: string) => boolean;
}
