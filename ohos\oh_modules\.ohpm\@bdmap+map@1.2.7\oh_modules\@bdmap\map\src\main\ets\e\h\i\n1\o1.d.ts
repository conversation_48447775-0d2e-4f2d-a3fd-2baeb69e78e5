import type { CommonEvent, OverlayEvent, TOverlayListener } from "../../g1/h1"; import { CollisionBehavior, Located, ScaleMode, Visibility } from "../../util/b1/c1"; import BmRichView from "../c2/n1/g4"; import type OverlayListener from "../m/i2"; import c46 from "./v4"; import type Base<PERSON> from "./q1";         export default class PopView extends c46 { private mVisibility; private mLocated; private opacity; private scaleX; private scaleY; private mDescription; private mRootUI;   eventListener: TOverlayListener; private mInstance; constructor();         setListener(listener: OverlayListener): void;         addEventListener(model: CommonEvent, j47: Function): void;         removeEventListener(model: CommonEvent, h47: Function): void;       createEvent(model: CommonEvent, g47: Function): { event: { [x: symbol]: Function; }; };       fireEvent(model: OverlayEvent): void;         setView(f47: BaseUI): void;         getView(): BaseUI;           findRichViewByName(name: string): BmRichView;           findRichViewByShell(e47: number): BmRichView;           findViewByName(name: string): import("../bmsdk/ui/BmBaseUI").default;           findViewByShell(d47: number): import("../bmsdk/ui/BmBaseUI").default;           setLocated(c47: Located): void;         setOffsetX(offsetX: number, scaleMode?: ScaleMode): void;         setOffsetY(offsetY: number, scaleMode?: ScaleMode): void;         setVisibility(visibility: Visibility): void;           setPBVisibility(b47: number): void;         setShowLevel(from: number, to: number): void;         setCollisionBehavior(a47: CollisionBehavior): void;             setCollisionPriority(priority: number): void;           setCollisionLineTagId(z46: number): void;         setCollisionBorder(left: number, top: number, right: number, bottom: number): void;                  setScale(scale: number): void;         setScaleX(scaleX: number): void;         setScaleY(scaleY: number): void;         setOpacity(opacity: number): void;         setDescription(description: string): void;         getDescription(): string;       setDrawFullscreenMaskFlag(flag: boolean): void;         getInstanceView(): BmRichView; } 