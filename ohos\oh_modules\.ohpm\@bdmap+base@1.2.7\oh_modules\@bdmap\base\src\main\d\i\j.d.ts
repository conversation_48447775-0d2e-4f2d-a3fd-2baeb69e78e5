import { Permission<PERSON><PERSON>ckR<PERSON>ult, PermissionCheckResultListener } from "../s"; import { Context } from '@kit.AbilityKit'; import { CoordType } from "./t/h1/i1";       export declare class Initializer implements PermissionCheckResultListener { private static instance; private static _coordType;       static context: Context | null; private constructor();         static getInstance(): Initializer;           onGetPermissionCheckResult(result: PermissionCheckResult): void;         initialize(h5: string, context?: Context): void;           static get coordType(): CoordType;           static set coordType(value: CoordType); } 