// @keepTs
// @ts-nocheck
export declare class EncryptUtil {
    private static readonly CLIENT_ARGUMENT_ERROR;
    /**
     * 对字符串使用 RSA 加密
     * @param originString 原文
     * @param publicKey 公钥，如果公钥为空则通过 resolve 直接返回原文
     * @returns 如果成功则返回加密后的字符，失败则返回 BusinessErrorInfo
     */
    static encryptByRSA(s104: string, t104: string): Promise<string>;
    /**
     * 字符串转成 MD5
     * @param input 要转换的字符串
     * @returns
     */
    static md5(h104: string): Promise<string>;
    private static stringToUint8Array;
}
export declare class BusinessErrorInfo {
    code: number;
    getCode(): number;
    msg: string;
    getMsg(): string;
    constructor(e104: number, f104?: string);
}
