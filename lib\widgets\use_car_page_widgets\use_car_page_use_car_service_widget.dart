import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/widgets/common/image_view.dart';
import 'package:wuling_flutter_app/models/user/user_handle_model.dart';

class UseCarServicButton extends StatelessWidget {
  final UserHandleModel serviceModel;
  final VoidCallback? onPressed; // 添加点击事件处理回调

  const UseCarServicButton({
    Key? key,
    required this.serviceModel,
    this.onPressed, // 允许传入点击事件处理器
  }) : super(key: key);

  String getImageUrl(UserHandleModel serviceModel) {
    String imageUrl = '';
    if (serviceModel.functionIconUrl?.isNotEmpty ?? false) {
      imageUrl = serviceModel.functionIconUrl!;
    }

    return imageUrl;
  }

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        padding: EdgeInsets.zero, // 去除默认的padding
        shadowColor: Colors.transparent, // 阴影透明
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.zero, // 去除圆角
        ),
      ),
      onPressed: onPressed, // 绑定传入的事件处理器
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: Colors.transparent, // 边框颜色
            width: 3, // 边框宽度
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: Colors.transparent, // 边框颜色
                  width: 3, // 边框宽度
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Container(
                    height: 28,
                    width: 28,
                    child: ImageView(
                      getImageUrl(serviceModel),
                      fit: BoxFit.cover,
                    ),
                  ),
                  SizedBox(height: 3),
                  Text(
                    serviceModel.functionIconName!,
                    style: TextStyle(fontSize: 12, color: Color(0xff383A40)),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}

class UseCarServiceWidget extends StatefulWidget {
  final List<UserHandleModel> toolServiceList;
  final List<String> hiddenHandelButtonNames;
  final void Function({required UserHandleModel userHandleModel})?
      onServiceButtonClicked;
  const UseCarServiceWidget(
      {Key? key,
      required this.toolServiceList,
      this.hiddenHandelButtonNames = const [],
      this.onServiceButtonClicked})
      : super(key: key);

  @override
  _UseCarServiceWidgetState createState() => _UseCarServiceWidgetState();
}

class _UseCarServiceWidgetState extends State<UseCarServiceWidget> {
  bool isExpanded = false;

  List<UserHandleModel> getFilterHiddenHandelButtons() {
    List<UserHandleModel> list = [];
    for (UserHandleModel userHandleModel in widget.toolServiceList) {
      String functionIconName = userHandleModel.functionIconName ?? '';
      if (!widget.hiddenHandelButtonNames.contains(functionIconName)) {
        list.add(userHandleModel);
      }
    }
    return list;
  }

  @override
  Widget build(BuildContext context) {
    int rowItemNum = 4;
    List<UserHandleModel> filterHiddenHandelButtons =
        getFilterHiddenHandelButtons();
    int itemCount = filterHiddenHandelButtons.length;

    double buttonHeight = 70.0; //按钮高度
    double sidePadding = 20.0; //两侧间距
    double topPadding = 20.0; //顶部间距
    double bottomPadding = 0.0; //底部间距
    int columnCount = (itemCount / rowItemNum).ceil();
    double fullHeight = buttonHeight * columnCount;
    double foldHeight =
        columnCount > 2 ? buttonHeight * 2 : buttonHeight * columnCount;
    return Container(
      color: Colors.transparent,
      child: Padding(
        padding: EdgeInsets.only(
            left: sidePadding, right: sidePadding, bottom: bottomPadding),
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: <Widget>[
              SizedBox(
                height: topPadding,
              ),
              AnimatedContainer(
                duration: Duration(milliseconds: 1000), // 动画持续时间
                height: isExpanded ? fullHeight : foldHeight,
                curve: Curves.fastLinearToSlowEaseIn,
                child: GridView.builder(
                  padding: EdgeInsets.zero,
                  physics: const NeverScrollableScrollPhysics(), // 禁止GridView滚动
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: rowItemNum,
                    childAspectRatio:
                        (MediaQuery.of(context).size.width - sidePadding * 2) /
                            (rowItemNum * buttonHeight),
                  ),
                  itemCount: itemCount,
                  itemBuilder: (context, index) {
                    return UseCarServicButton(
                      serviceModel: filterHiddenHandelButtons[index],
                      onPressed: () {
                        if (widget.onServiceButtonClicked != null) {
                          widget.onServiceButtonClicked!(
                              userHandleModel:
                                  filterHiddenHandelButtons[index]);
                        }
                      },
                    );
                  },
                ),
              ),
              if (columnCount > 2)
                Container(
                    color: Colors.transparent,
                    height: 35,
                    child: IconButton(
                      padding: const EdgeInsets.only(
                          top: 5, bottom: 5, left: 50, right: 50),
                      icon: SizedBox(
                        width: 16, // 设置IconButton的尺寸
                        height: 8,
                        child: ImageView(isExpanded
                            ? 'assets/images/use_car_page/bj_icon_tool_service_fold.png'
                            : 'assets/images/use_car_page/bj_icon_tool_service_unfold.png'),
                      ),
                      onPressed: () {
                        setState(() {
                          isExpanded = !isExpanded;
                        });
                      },
                    ))
              else
                Container(
                  color: Colors.transparent,
                  height: 10,
                  width: 70,
                )
            ],
          ),
        ),
      ),
    );
  }
}
