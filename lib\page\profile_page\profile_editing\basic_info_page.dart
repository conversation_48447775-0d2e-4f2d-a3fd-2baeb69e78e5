import 'dart:convert';
import 'dart:ui';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/constant/storage.dart';
import 'package:wuling_flutter_app/constant/constant.dart';
import 'package:wuling_flutter_app/api/index.dart';
import 'package:wuling_flutter_app/models/index.dart';
import 'package:wuling_flutter_app/utils/sp_util.dart';
import 'package:wuling_flutter_app/utils/manager/time_picker_manager.dart';
import 'package:wuling_flutter_app/utils/manager/location_picker_manager.dart';
import 'package:wuling_flutter_app/utils/manager/loading_manager.dart';
import 'package:wuling_flutter_app/utils/manager/notification_manager.dart';
import 'package:wuling_flutter_app/page/base_page/base_page.dart';
import 'package:wuling_flutter_app/widgets/common/custom_time_picker.dart';
import 'package:wuling_flutter_app/widgets/common/image_view.dart';
import 'package:wuling_flutter_app/models/global_data.dart';
import 'package:wuling_flutter_app/models/user/user_model.dart';
import 'package:image_picker_ohos/image_picker_ohos.dart';
import 'package:image_picker_platform_interface/image_picker_platform_interface.dart';
import 'package:wuling_flutter_app/utils/http/http_request.dart';

import '../../../utils/manager/log_manager.dart';

class BasicInfoPage extends BasePage {
  BasicInfoPage({
    Key? key,
    bool hideAppBar = true,
    bool isWithinSafeArea = false,
    String appBarTitle = 'New Page',
    Color appBarColor = Colors.transparent,
    Color pageBackgroundColor = Colors.white,
  }) : super(
          key: key,
          hideAppBar: hideAppBar,
          isWithinSafeArea: isWithinSafeArea,
          appBarTitle: appBarTitle,
          appBarColor: appBarColor,
          pageBackgroundColor: pageBackgroundColor,
        );

  @override
  _BasicInfoPageState createState() => _BasicInfoPageState();
}

class _BasicInfoPageState extends BasePageState<BasicInfoPage> {
  UserModel? _userModel;
  final _nickNameController = TextEditingController();
  final FocusNode _nickNameFocusNode = FocusNode();
  bool _isSelectMale = false;
  String _currentBirthDay = '';
  String _currentLocation = '';
  int _cityId = 0;
  String? _newPhotoPath;

  @override
  void pageInitState() {
    _userModel = GlobalData().isLogin ? GlobalData().userModel : null;
    _nickNameController.text = _userModel?.nickname ?? '';
    _currentBirthDay = _userModel?.birthdayStr ?? '';
    _isSelectMale = _userModel == null || _userModel!.sex > 0;
    _cityId = _userModel?.cityId ?? 0;
    if (_userModel?.provinceStr != null) {
      _currentLocation = _userModel?.cityStr ?? "";
    } else {
      String province = _userModel?.provinceStr ?? "";
      String city = _userModel?.cityStr ?? "";
      if (province.isNotEmpty && city.isNotEmpty) {
        _currentLocation = "$province-$city";
      } else if (province.isEmpty && city.isNotEmpty) {
        _currentLocation = city;
      }
    }
    // 延时执行是因为要等待页面构建完成
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  void _focusOnNickNameField() {
    FocusScope.of(context).requestFocus(_nickNameFocusNode);
  }

  String getFullCityString(
      {required String provinceName, required String cityName}) {
    if (provinceName.isEmpty) {
      return cityName;
    }
    if (cityName == '北京' ||
        cityName == '天津' ||
        cityName == '上海' ||
        cityName == '重庆') {
      return cityName;
    }
    return '$provinceName-$cityName';
  }

  void _modifyUserInfo() async {
    if (!GlobalData().isLogin) {
      LoadingManager.showToast('您还没有登录或登录过期');
      return;
    }
    try {
      LoadingManager.show(status: '修改中...');
      Map<String, dynamic> params = {};

      // 先处理头像上传
      if (_newPhotoPath != null) {
        try {
          // 1. 验证图片文件
          File imageFile = File(_newPhotoPath!);
          if (!await imageFile.exists()) {
            LoadingManager.showError('图片文件不存在');
            return;
          }

          // // 2. 检查图片大小
          // int fileSize = await imageFile.length();
          // if (fileSize > 2 * 1024 * 1024) {
          //   // 2MB限制
          //   LoadingManager.showError('头像大小不能超过2MB');
          //   return;
          // }

          // 3. 使用FileUpload上传文件 - 参考发布话题页面的逻辑
          List<String> uploadPaths = [_newPhotoPath!];
          LoadingManager.show(status: "上传头像中...");
          List<String> uploadedImages =
              await FileUpload(HttpRequest()).uploadFiles(uploadPaths);

          if (uploadedImages.isNotEmpty) {
            // 成功上传图片，使用返回的URL
            params['photo'] = uploadedImages.first;
            LoadingManager.show(status: "头像上传成功，更新信息中...");
          } else {
            LoadingManager.showError('头像上传失败');
            return;
          }
        } catch (e) {
          LogManager().debug('头像上传失败: $e');
          LoadingManager.showError('头像上传失败: ${e.toString()}');
          return; // 头像上传失败时终止后续操作
        }
      }

      // 添加其他个人信息参数
      if (_nickNameController.text.isNotEmpty) {
        params['nickname'] = _nickNameController.text;
      }
      if (_cityId > 0) {
        params['cityId'] = _cityId;
      }
      params['sex'] = _isSelectMale ? 1 : 0;
      if (_currentBirthDay.isNotEmpty) {
        params['birthdayStr'] = _currentBirthDay;
      }
      if (_currentLocation.isNotEmpty) {
        List<String> array = _currentLocation.split('-');
        if (array.length == 2) {
          params['provinceStr'] = array.first;
          params['cityStr'] = array.last;
        } else if (array.length == 1) {
          params['cityStr'] = array.last;
        }
      }

      // 调用API更新用户信息
      UserModel userModel =
          await userAPI.modifiedUserInfoWithParameters(params);
      var jsonMap = userModel.toJson();

      if (userModel.photo.isEmpty) {
        if (jsonMap.keys.contains('photo')) {
          jsonMap['photo'] = _userModel?.photo;
        }
        userModel = UserModel.fromJson(jsonMap);
      }

      // 清除临时头像路径
      _newPhotoPath = null;

      // 保存到全局和本地
      GlobalData().userModel = userModel;
      await SpUtil().setJSON(SP_USER_PROFILE_KEY, jsonMap);

      // 发送通知并返回
      NotificationManager()
          .postNotification(Constant.NOTIFICATION_USER_INFO_UPDATED);
      LoadingManager.showSuccess('修改成功')
          .then((value) => Navigator.pop(context));
    } catch (e) {
      LoadingManager.showError(e.toString());
    }
  }

  String getAvatarImageUrl() {
    if (_newPhotoPath != null) {
      return _newPhotoPath!;
    } else if ((_userModel?.photo ?? '').isEmpty) {
      return 'assets/images/profile_page/llb_default_avatar.png';
    } else {
      return _userModel!.photo;
    }
  }

  void _showAvatarOptionsBottomSheet() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 白色圆角容器包含拍照和从相册选择选项
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 10),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Column(
                children: [
                  _buildOptionButton('拍照', () {
                    Navigator.pop(context);
                    // 拍照逻辑并展示头像
                    final ImagePickerOhos _picker = ImagePickerOhos();
                    _picker
                        .pickImage(source: ImageSource.camera, imageQuality: 70)
                        .then((file) {
                      if (file != null && file.path.isNotEmpty) {
                        LogManager().debug('拍照成功: ${file.path}');
                        // 更新UI显示新照片
                        setState(() {
                          _newPhotoPath = file.path;
                        });
                        LogManager().debug('头像信息: ${_newPhotoPath}');
                        // 显示提示信息
                        LoadingManager.showSuccess('拍照成功');
                      }
                    }).catchError((error) {
                      LoadingManager.showError('拍照失败: $error');
                    });
                  }, textColor: const Color(0xFF007AFF)),
                  const Divider(
                      height: 1, thickness: 1, color: Color(0xFFE5E5E5)),
                  _buildOptionButton('从相册选择', () {
                    Navigator.pop(context);
                    // 使用鸿蒙专用的图片选择器
                    final ImagePickerOhos _picker = ImagePickerOhos();
                    _picker
                        .pickImage(
                            source: ImageSource.gallery, imageQuality: 70)
                        .then((file) {
                      if (file != null && file.path.isNotEmpty) {
                        LogManager().debug('选择图片成功: ${file.path}');
                        // 更新UI显示新照片
                        setState(() {
                          _newPhotoPath = file.path;
                        });
                        // 显示提示信息
                        LoadingManager.showSuccess('图片选择成功');
                      }
                    }).catchError((error) {
                      LoadingManager.showError('选择图片失败: $error');
                    });
                  }, textColor: const Color(0xFF007AFF)),
                ],
              ),
            ),

            // 独立的取消按钮
            Container(
              width: double.infinity,
              margin: const EdgeInsets.only(
                  left: 10, right: 10, top: 8, bottom: 34),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
              ),
              child: _buildOptionButton('取消', () {
                Navigator.pop(context);
              }, textColor: const Color(0xFF007AFF)),
            ),
          ],
        );
      },
      isScrollControlled: true,
    );
  }

  Widget _buildOptionButton(String text, VoidCallback onTap,
      {Color? textColor}) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: Container(
          alignment: Alignment.center,
          height: 57,
          child: Text(
            text,
            style: TextStyle(
              fontSize: 18,
              color: textColor ?? Colors.black,
              fontWeight: FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget buildPageContent(BuildContext context) {
    double safeAreaTop = MediaQueryData.fromWindow(window).padding.top;
    double avatarSize = 80;
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus(); // 收起键盘
      },
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 背景图片
          Container(
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage('assets/images/login/login_bg.png'), // 背景图片路径
                fit: BoxFit.cover,
              ),
            ),
          ),
          SafeArea(
            child: ListView(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(
                      height: 80,
                    ),
                    SizedBox(
                      height: avatarSize,
                      width: avatarSize,
                      child: GestureDetector(
                        onTap: _showAvatarOptionsBottomSheet,
                        child: Stack(
                          children: [
                            Positioned.fill(
                              child: ClipRRect(
                                borderRadius:
                                    BorderRadius.circular(avatarSize / 2.0),
                                child: ImageView(getAvatarImageUrl()),
                              ),
                            ),
                            Positioned(
                              right: 0,
                              bottom: 0,
                              child: SizedBox(
                                height: 21.5,
                                width: 21.5,
                                child: ImageView(
                                    'assets/images/profile_page/basic_info_camera.png'),
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 15,
                    ),
                    Container(
                      height: 27,
                      color: Colors.transparent,
                      child: Center(
                        child: Container(
                          color: Colors.transparent,
                          child: TextFormField(
                            style: const TextStyle(fontSize: 18), // 调整字体大小
                            focusNode: _nickNameFocusNode,
                            controller: _nickNameController,
                            textAlign: TextAlign.center,
                            textAlignVertical: TextAlignVertical.top,
                            cursorColor: Colors.green, // 设置光标颜色
                            cursorWidth: 2.0, // 设置光标宽度
                            cursorHeight: 20.0, // 设置光标高度
                            decoration: const InputDecoration(
                              hintText: '设置昵称', // 设置 placeholderText
                              isDense: true,
                              hintStyle: TextStyle(
                                color: Colors.grey, // 设置 placeholderText 的颜色
                                fontSize: 18.0, // 设置 placeholderText 的字体大小
                              ),
                              border: InputBorder.none, // 移除输入框的边框
                              filled: false, // 不填充背景颜色
                              contentPadding: EdgeInsets.zero, // 将内边距设为零
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return '请输入有效昵称';
                              }
                              return null;
                            },
                          ),
                        ),
                      ),
                    ),
                    Container(
                      color: Colors.transparent,
                      height: 25,
                      width: 50,
                      child: TextButton(
                        onPressed: () {
                          _focusOnNickNameField();
                        },
                        child: const Text(
                          '修改昵称',
                          style: TextStyle(
                            fontSize: 12,
                            color: Color(0xff2B7DFF),
                          ),
                        ),
                        style: ButtonStyle(
                          padding: MaterialStateProperty.all<EdgeInsets>(
                              EdgeInsets.zero), // 调整内边距以改变高度
                        ),
                      ),
                    ),
                    const SizedBox(
                      height: 35,
                    ),
                    const Text(
                      '选择您的性别',
                      style: TextStyle(color: Color(0xffAAAAAA), fontSize: 14),
                    ),
                    const SizedBox(
                      height: 8,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        GestureDetector(
                          onTap: () {
                            setState(() {
                              _isSelectMale = true;
                            });
                          },
                          child: Container(
                            color: Colors.transparent,
                            width: 124,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SizedBox(
                                  height: 22,
                                  width: 22,
                                  child: ImageView(_isSelectMale
                                      ? 'assets/images/profile_page/basic_info_male_select.png'
                                      : 'assets/images/profile_page/basic_info_male_normal.png'),
                                ),
                                const SizedBox(
                                  width: 5,
                                ),
                                const Text(
                                  '男',
                                  style: TextStyle(
                                    fontSize: 20,
                                  ),
                                )
                              ],
                            ),
                          ),
                        ),
                        Container(
                          color: const Color(0xffD3D3D3),
                          width: 1,
                          height: 24,
                        ),
                        GestureDetector(
                          onTap: () {
                            setState(() {
                              _isSelectMale = false;
                            });
                          },
                          child: Container(
                            color: Colors.transparent,
                            width: 124,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                SizedBox(
                                  height: 22,
                                  width: 22,
                                  child: ImageView(_isSelectMale
                                      ? 'assets/images/profile_page/basic_info_female_normal.png'
                                      : 'assets/images/profile_page/basic_info_female_select.png'),
                                ),
                                const SizedBox(
                                  width: 5,
                                ),
                                const Text(
                                  '女',
                                  style: TextStyle(
                                    fontSize: 20,
                                  ),
                                )
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 35,
                    ),
                    const Text(
                      '选择您的生日',
                      style: TextStyle(color: Color(0xffAAAAAA), fontSize: 14),
                    ),
                    const SizedBox(
                      height: 8,
                    ),
                    GestureDetector(
                      onTap: () {
                        TimePickerManager.showCustomTimePicker(
                            context: context,
                            mode: TimePickerMode.YMD,
                            initialTimeStr: _currentBirthDay,
                            leftTitle: '选择生日',
                            onTimeSelected:
                                (Duration selectedTime, String formattedTime) {
                              setState(() {
                                _currentBirthDay = formattedTime;
                              });
                            });
                      },
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            _currentBirthDay.isNotEmpty
                                ? _currentBirthDay
                                : '请选择',
                            style: const TextStyle(
                                fontSize: 20, fontWeight: FontWeight.w500),
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          const Icon(
                            Icons.arrow_forward_ios,
                            color: Color(0xffAAAAAA),
                            size: 18,
                          )
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 35,
                    ),
                    const Text(
                      '您常住的城市',
                      style: TextStyle(color: Color(0xffAAAAAA), fontSize: 14),
                    ),
                    const SizedBox(
                      height: 8,
                    ),
                    GestureDetector(
                      onTap: () {
                        LocationPickerManager.showLocationPicker(
                            context: context,
                            onLocationSelected:
                                (ProvinceModel? selectedProvince,
                                    CityModel? selectedCity) {
                              if (selectedProvince != null &&
                                  selectedCity != null) {
                                String location = getFullCityString(
                                    provinceName: selectedProvince.provinceName,
                                    cityName: selectedCity.cityName);
                                setState(() {
                                  _currentLocation = location;
                                  _cityId = selectedCity.cityId;
                                });
                              }
                            });
                      },
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            _currentLocation.isNotEmpty
                                ? _currentLocation
                                : '请选择',
                            style: const TextStyle(
                                fontSize: 20, fontWeight: FontWeight.w500),
                          ),
                          const SizedBox(
                            width: 5,
                          ),
                          const Icon(
                            Icons.arrow_forward_ios,
                            color: Color(0xffAAAAAA),
                            size: 18,
                          )
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 35,
                    ),
                    ElevatedButton(
                      onPressed: () {
                        _modifyUserInfo();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red, // 背景色设置为红色
                        minimumSize:
                            const Size(200, 44), // 设置按钮的最小尺寸，长度为200，高度为44
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(4), // 设置圆角为4
                        ),
                      ),
                      child: const Text(
                        '保存',
                        style: TextStyle(
                          fontSize: 16, // 文字字号设置为16
                          color: Colors.white, // 文字颜色设置为白色
                        ),
                      ),
                    )
                  ],
                ),
              ],
            ),
          ),
          Positioned(
            top: safeAreaTop,
            left: 0,
            child: Row(
              children: [
                GestureDetector(
                  onTap: () {
                    // 处理点击事件
                    Navigator.pop(context);
                  },
                  child: const SizedBox(
                      height: 44,
                      width: 54,
                      child: Icon(Icons.arrow_back_ios, color: Colors.black)),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    // 在此处添加页面销毁逻辑
    _nickNameFocusNode.dispose();
    _nickNameController.dispose();
    super.dispose();
  }
}
