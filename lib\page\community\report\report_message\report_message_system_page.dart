import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_uikit/ui_image.dart';
import 'package:wuling_flutter_app/page/store/store_navbar.dart';

import '../../../../api/report_api/report_api.dart';
import '../../../../common/action.dart';
import '../../../../models/community/report/report_system_item_model.dart';
import '../../../../widgets/webview/webview.dart';
import '../report_navbar.dart';

import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wuling_flutter_app/widgets/common/custom_refresher.dart';

class ReportMessageSystemPage extends StatefulWidget {
  const ReportMessageSystemPage({super.key});

  @override
  State<ReportMessageSystemPage> createState() => _ReportMessageSystemPageState();
}

class _ReportMessageSystemPageState extends State<ReportMessageSystemPage> {
  List<dynamic> dataSource = [];
  final RefreshController _refreshController =
  RefreshController(initialRefresh: false);
  int _pageNo = 1;

  get clear => (){
    reportApi.clear("notice");
    _onRefresh();
  };

  @override
  void initState() {
    _onRefresh();
    super.initState();
  }

  void _onRefresh() async {
    // monitor network fetch
    _pageNo = 1;
    reportApi.getReportNotice(secondMessageType: "notice",pageNo: _pageNo).then((value) {
      setState(() {
        dataSource = value;
      });
    });
    await Future.delayed(Duration(milliseconds: 1000));
    _refreshController.refreshCompleted();
  }

  void _onLoading() async {
    _pageNo += 1;
    reportApi.getMsgLogByUser(secondMessageType: "notice",pageNo: _pageNo).then((value) {
      setState(() {
        dataSource.addAll(value);
      });
    });
    await Future.delayed(Duration(milliseconds: 1000));
    _refreshController.loadComplete();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ReportNavbar(title:"通知",rightAction: clear,),
      backgroundColor: Color(0xFFF8F8F8),
      body: SmartRefresher(
        enablePullDown: true,
        enablePullUp: true,
        controller: _refreshController,
        onRefresh: _onRefresh,
        onLoading: _onLoading,
        header: AnimatedRefreshHeader(),
        footer: AnimatedRefreshFooter(),
        child: dataSource.isEmpty ? const Center(
          child: UIImage(width: 200,imgStr: "assets/images/community/normal.jpg",),
        ) : ListView.builder(itemBuilder: (ctx,i)=>ReportMessageSystemItem(model: dataSource[i],),itemCount: dataSource.length,)
      ),
    );
  }

  @override
  void dispose() {
    // TODO: implement dispose
    _refreshController?.dispose();
    super.dispose();
  }
}


class ReportMessageSystemItem extends StatelessWidget{

  final ReportSystemItemModel model;

  const ReportMessageSystemItem({super.key, required this.model});

  String getContent(){
    String template = model.messageContentVo["contentTemplate"];
    Map param = model.messageContentVo["paramMap"] ?? {};
    if(param.length>1){
      String text = template;
      if(param.containsKey("channel")){
        text = text.replaceAll("{channel}", param['channel']);
      }
      if(param.containsKey("name")){
        text = text.replaceAll("{name}", param['name']);
      }
      if(param.containsKey("time")){
        text = text.replaceAll("{time}", param['time']);
      }
      if(param.containsKey("assistant")){
        text = text.replaceAll("{assistant}", param['assistant']);
      }
      if(param.containsKey("technician")){
        text = text.replaceAll("{technician}", param['technician']);
      }
      if(param.containsKey("dealerFullname")){
        text = text.replaceAll("{dealerFullname}", param['dealerFullname']);
      }
      if(param.containsKey("nextMaintenanceTime")){
        text = text.replaceAll("{nextMaintenanceTime}", param['nextMaintenanceTime']);
      }
      if(param.containsKey("customer")){
        text = text.replaceAll("{customer}", param['customer']);
      }
      if(param.containsKey("plateNumber")){
        text = text.replaceAll("{plateNumber}", param['plateNumber']);
      }
      if(param.containsKey("serialNumber")){
        text = text.replaceAll("{serialNumber}", param['serialNumber']);
      }
      if(param.containsKey("storeName")){
        text = text.replaceAll("{storeName}", param['storeName']);
      }
      if(param.containsKey("mobile")){
        text = text.replaceAll("{mobile}", param['mobile']);
      }
      if(param.containsKey("carTypeName")){
        text = text.replaceAll("{carTypeName}", param['carTypeName']);
      }
      return text;
    }else{
      if(param.containsKey("comment")){
        return template.replaceAll("{comment}", param['comment']);
      }else if(param.containsKey("postContent")){
        return template.replaceAll("{postContent}", param['postContent']);
      }else if(param.containsKey("nickName")){
        return template.replaceAll("{nickName}", param['nickName']);
      }else if(param.containsKey("firstGoodsName")){
        return template.replaceAll("{firstGoodsName}", param['firstGoodsName']);
      }else if(param.containsKey("count")){
        return template.replaceAll("{count}", param['count'].toString());
      }else if(param.containsKey("callBackTime")){
        return template.replaceAll("{callBackTime}", param['callBackTime']);
      }else{
        return template;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: (){
        if(model.skipType == 3){
          // 跳转到网页
          NavigatorAction.init(context,view: WebViewPage(url: model.skipTarget,));
        }else if(model.skipType == 0){
          // NavigatorAction.init(context,view: );
        }else if(model.skipType == 1){
          // TODO: 跳转到喜欢的内容 skipTarget  skipType：1
          // NavigatorAction.init(context,view: );
        }else if(model.skipType == 25){
          // NavigatorAction.init(context,view: );
        }
      },
      child: Container(
        decoration: const BoxDecoration(
            color: Colors.white,
            border: Border(bottom: BorderSide(width: 1,color: Color(0xfff3f3f3)))
        ),
        padding: const EdgeInsets.symmetric(vertical: 10,horizontal: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 10),
              child: Row(
                children: [
                  UIImage(imgStr:model.image,radius:10,width: 20,height: 20,margin: EdgeInsets.only(right: 10),),
                  Expanded(flex: 1,child: Text(model.name+model.skipType.toString()),),
                  Text('${DateTime.fromMillisecondsSinceEpoch(model.time).toLocal()}'),
                ],
              ),
            ),
            Text(getContent(),maxLines: 5,),
          ],
        ),
      ),
    );
  }
}