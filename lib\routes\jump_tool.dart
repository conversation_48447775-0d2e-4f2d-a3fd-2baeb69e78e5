import 'dart:convert';

import 'package:wuling_flutter_app/constant/constant.dart';
import 'package:wuling_flutter_app/models/global_data.dart';
import 'package:wuling_flutter_app/page/community/topic_post_details_page.dart';
import 'package:wuling_flutter_app/page/post/post_normal_detail_page.dart';
import 'package:wuling_flutter_app/page/profile_page/collect_list_page.dart';
import 'package:wuling_flutter_app/page/profile_page/follow_list_page.dart';
import 'package:wuling_flutter_app/page/use_car_page/car_one_key_charge_page.dart';
import 'package:wuling_flutter_app/page/use_car_page/more_service_page.dart';
import 'package:wuling_flutter_app/utils/manager/loading_manager.dart';
import 'package:wuling_flutter_app/utils/manager/log_manager.dart';
import 'package:wuling_flutter_app/utils/str_util.dart';
import 'package:wuling_flutter_app/constant/service_constant.dart';
import 'package:wuling_flutter_app/models/user/user_handle_model.dart';
import 'package:flutter/material.dart';

import 'package:wuling_flutter_app/page/use_car_page/remote_start_engine_page.dart';
import 'package:wuling_flutter_app/page/use_car_page/ac_reservation_page.dart';
import 'package:wuling_flutter_app/page/use_car_page/test_page.dart';
import 'package:wuling_flutter_app/page/use_car_page/car_authorization_page.dart';
import 'package:wuling_flutter_app/page/use_car_page/car_check_page.dart';
import 'package:wuling_flutter_app/page/use_car_page/older_ev_page.dart';
import 'package:wuling_flutter_app/page/use_car_page/older_car_check_page.dart';
import 'package:wuling_flutter_app/page/use_car_page/charge_reservation_page.dart';
import 'package:wuling_flutter_app/page/profile_page/my_qr_code_page.dart';
import 'package:wuling_flutter_app/page/profile_page/edit_signature_page.dart';
import 'package:wuling_flutter_app/page/community/my_friends_page.dart';
import 'package:wuling_flutter_app/page/test/ble_scaner_page.dart';
import 'package:wuling_flutter_app/widgets/detail_page/car_order_detail_page.dart';
import '../api/community_api.dart';
import '../constant/cartype.dart';
import '../models/community/recommend_hot.dart';
import '../models/user/user_handle_list_model.dart';
import '../models/user/user_model.dart';
import '../page/community/report/report_page.dart';
import '../page/community/topic_detail_page.dart';
import '../page/profile_page/system_setting_page.dart';
import '../page/use_car_page/banma_temperature_setting_page.dart';
import '../page/use_car_page/car_details_page.dart';
import '../page/use_car_page/travel_car_setting_page.dart';
import '../page/store/store_detail/store_detail_page.dart';
import '../utils/sp_util.dart';
import '../widgets/webview/webview.dart';
import '../models/car/car_info_model.dart';
import '../models/car/car_service_model.dart';
import '../models/car/car_status_model.dart';
import '../models/car/service_model.dart';
import '../page/use_car_page/cyclic_reservation_charging_page.dart';
import 'app_routes.dart';

class JumpTool {
  static final JumpTool _instance = JumpTool._();

  factory JumpTool() => _instance;

  JumpTool._();

  void jump(BuildContext context, int linkType, String linkParam) {
    LogManager().debug('JumpTool 执行跳转 linkType:$linkType,linkParam:$linkParam');
    switch (linkType) {
      case 1:
        break;
      case 2:
        break;
      case 3: //自有 h5
        if (!StrUtil.isEmpty(linkParam)) {
          openWeb(context, linkParam, false);
        }
        break;
      case 4:
        break;
      case 5:
        break;
      case 180: //原生-原生跳转路径
        openNativePage(context, linkParam);
        break;
    }
  }

  void openWeb(BuildContext context, String linkUrl, bool needTitleBar) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WebViewPage(
          url: linkUrl,
          needTitleBar: needTitleBar,
        ),
      ),
    );
  }

  ///跳转原生页面方法
  ///文档：https://sgmw.feishu.cn/docx/BrUYdtqRToO6PCxP7dAcUOe1n4d
  void openNativePage(BuildContext context, String nativeId) {
    switch (nativeId) {
      case "1001": //系统设置
        Navigator.of(context).push(CustomCupertinoPageRoute(
          builder: (context) => SystemSettingsPage(),
          canSwipeBack: false, // 禁用手势返回
        ));
        break;
      case "1002": //其他功能
        break;
      case "1003": //离线模式
        LoadingManager.showToast("正在施工中。。。");
        break;
      case "1004": //一键上传日志
        LoadingManager.showToast("正在施工中。。。");
        break;
      case "1005": //我的订单

        break;
      case "1006": //服务
        LoadingManager.showToast("正在施工中。。。");
        break;
      case "1007": //分享宝骏汽车
        LoadingManager.showToast("正在施工中。。。");
        break;
      case "1008": //发现（社区功能）
        LoadingManager.showToast("正在施工中。。。");
        break;
      case "1009": //地址管理
        LoadingManager.showToast("正在施工中。。。");
        break;
      case "1010": //客服咨询
        String mServiceUrl = SpUtil().getString("serviceurl");
        if (!StrUtil.isEmpty(mServiceUrl)) {
          JumpTool().openWeb(context, mServiceUrl, true);
        }
        break;
      case "1011": //账号与安全

        break;
      case "1012":
        Navigator.of(context).push(CustomCupertinoPageRoute(
          builder: (context) => TravelCarSettingPage(),
          canSwipeBack: false, // 禁用手势返回
        ));
        break;
    }
  }

  void jumpToOlderEVNativePageWithServiceCode(
      BuildContext context, String serviceCode,
      {Map<String, dynamic>? extraData}) {
    if (serviceCode == ServiceConstant.jumpToLatestSmartKeepWarm ||
        serviceCode == ServiceConstant.jumpToLatestSmartFillElectricity) {
      // LoadingManager.showToast('功能开发中 敬请期待');
      // return;
      Navigator.of(context).push(CustomCupertinoPageRoute(
        builder: (context) => CarDetailsPage(),
        canSwipeBack: false, // 禁用手势返回
      ));
      return;
    } else if (serviceCode == ServiceConstant.jumpToNewEnergy ||
        serviceCode == ServiceConstant.jumpToLatestNewEnergy) {
      Navigator.of(context).push(CustomCupertinoPageRoute(
        builder: (context) => CarOneKeyChargePage(),
        canSwipeBack: false, // 禁用手势返回
      ));
    } else if (serviceCode == ServiceConstant.jumpTolendKey ||
        serviceCode == ServiceConstant.jumpToLatestLendKey) {
      Navigator.of(context).push(CustomCupertinoPageRoute(
        builder: (context) => CarAuthorizationPage(
          carType: CAR_TYPE_INTERNET,
        ),
        canSwipeBack: false, // 禁用手势返回
      ));
      return;
    } else if (serviceCode == ServiceConstant.jumpToLatestCarHealth) {
      Navigator.of(context).push(CustomCupertinoPageRoute(
        builder: (context) => OlderCarCheckPage(),
        canSwipeBack: false, // 禁用手势返回
      ));
      return;
    } else if (serviceCode == ServiceConstant.jumpToLatestOrderCharging) {
      Navigator.of(context).push(CustomCupertinoPageRoute(
        builder: (context) => ChargeReservationPage(
          isOldTypeCar: true,
        ),
        canSwipeBack: false, // 禁用手势返回
      ));
      return;
    }
    LoadingManager.showToast('功能开发中 敬请期待');
  }

  void jumpToNativePageWithServiceCode(BuildContext context, String serviceCode,
      {Map<String, dynamic>? extraData}) {
    if (serviceCode == ServiceConstant.jumpToOrderCharging) {
      Navigator.of(context).push(CustomCupertinoPageRoute(
        builder: (context) => ChargeReservationPage(
          isOldTypeCar: false,
        ),
        canSwipeBack: false, // 禁用手势返回
      ));
    } else if (serviceCode == ServiceConstant.jumpToMap) {
      LoadingManager.showToast('功能开发中 敬请期待');
    } else if (serviceCode == ServiceConstant.jumpToCarHealth) {
      // LoadingManager.showToast('功能开发中 敬请期待');
      bool needCheckTire = false;
      if (extraData != null && extraData.isNotEmpty) {
        if (extraData.keys.contains('needCheckTire')) {
          needCheckTire = extraData['needCheckTire'];
        }
      }
      Navigator.of(context).push(CustomCupertinoPageRoute(
        builder: (context) => CarCheckPage(
          needCheckTire: needCheckTire,
        ),
        canSwipeBack: false, // 禁用手势返回
      ));
    } else if (serviceCode == ServiceConstant.jumpToNewEnergy ||
        serviceCode == ServiceConstant.jumpToLatestNewEnergy) {
      Navigator.of(context).push(CustomCupertinoPageRoute(
        builder: (context) => CarOneKeyChargePage(),
        canSwipeBack: false, // 禁用手势返回
      ));
    } else if (serviceCode == ServiceConstant.jumpTolendKey ||
        serviceCode == ServiceConstant.jumpToLatestLendKey) {
      Navigator.of(context).push(CustomCupertinoPageRoute(
        builder: (context) => CarAuthorizationPage(carType: CAR_TYPE_INTERNET),
        canSwipeBack: false, // 禁用手势返回
      ));
    } else if (serviceCode == ServiceConstant.jumpToAcAppointment ||
        serviceCode == ServiceConstant.jumpToCarAcAppointment ||
        serviceCode == ServiceConstant.jumpToOrderAc) {
      Navigator.of(context).push(CustomCupertinoPageRoute(
        builder: (context) => AcReservationPage(),
        canSwipeBack: false, // 禁用手势返回
      ));
      // LoadingManager.showToast('功能开发中 敬请期待');
    } else if (serviceCode == ServiceConstant.jumpToRemotePowerUp) {
      Navigator.of(context).push(CustomCupertinoPageRoute(
        builder: (context) => RemoteStartEnginePage(),
        canSwipeBack: false, // 禁用手势返回
      ));
    } else if (serviceCode == ServiceConstant.jumpToLatestSmartKeepWarm ||
        serviceCode == ServiceConstant.jumpToLatestSmartFillElectricity) {
      Navigator.of(context).push(CustomCupertinoPageRoute(
        builder: (context) => CarDetailsPage(),
      ));
    } else if (serviceCode == ServiceConstant.jumpToCycleOrderCharging) {
      CarServiceModel? carServiceModel;
      CarInfoModel? carInfoModel;
      CarStatusModel? statusModel;
      if (extraData != null && extraData.isNotEmpty) {
        for (var entry in extraData.entries) {
          if (entry.key == 'carServiceModel') {
            carServiceModel = entry.value;
          } else if (entry.key == 'carInfoModel') {
            carInfoModel = entry.value;
          } else if (entry.key == 'statusModel') {
            statusModel = entry.value;
          }
          LogManager().debug('Key: ${entry.key}, Value: ${entry.value}');
        }
        Navigator.of(context).push(CustomCupertinoPageRoute(
          builder: (context) => CyclicReservationChargingPage(
              serviceModel: carServiceModel,
              infoModel: carInfoModel,
              statusModel: statusModel),
          canSwipeBack: false, // 禁用手势返回
        ));
      }
    } else if (serviceCode == ServiceConstant.jumpToMore) {
      LoadingManager.showToast('功能开发中 敬请期待');
    } else {
      LoadingManager.showToast('功能开发中 敬请期待');
    }
  }

  void jumpToWulingAllCarService(
      BuildContext context, List<UserHandleModel> handleList) {
    for (UserHandleModel handleModel in handleList) {
      if (handleModel.triggerType == UserHandleType.functions.value) {
        Navigator.of(context).push(CustomCupertinoPageRoute(
          builder: (context) =>
              MoreServicePage(categoryIds: handleModel.triggerValue ?? ''),
        ));
        return;
      }
    }
  }

  void jumpToMyQRCodePage(BuildContext context) {
    Navigator.of(context).push(CustomCupertinoPageRoute(
      builder: (context) => MyQRCodePage(),
      canSwipeBack: false, // 禁用手势返回
    ));
  }

  void jumpToEditSignaturePage(BuildContext context) {
    Navigator.of(context).push(CustomCupertinoPageRoute(
      builder: (context) => EditSignaturePage(),
      canSwipeBack: false, // 禁用手势返回
    ));
  }

  void jumpToAdvertisePage(BuildContext context,
      {required int linkType,
      required String linkUrl,
      required String eventPage,
      String? urlPre,
      int? channelCode,
      Map<String, dynamic>? sourceDict}) {
    if (linkType == LinkType.post.value) {
      // 如果是帖子, 加载帖子界面
      int postId = int.parse(linkUrl);
      JumpTool().jumpToPostDetail(
          context: context,
          postId: postId,
          postTypeId: PostType.normal,
          referrerPage: eventPage);
      return;
    }

    if (linkType == LinkType.community.value) {
      // 创建社区详情页
      int communityId = int.parse(linkUrl);
      JumpTool()
          .jumpToCommunityDetail(communityId: communityId, communityName: null);
      return;
    }

    if (linkType == LinkType.selfWebView.value) {
      // 自有H5页面
      openWeb(context, linkUrl, false);
      return;
    }

    if (linkType == LinkType.otherWebView.value) {
      // 如果是链接，创建webview的控制器
      JumpTool().openWeb(context, linkUrl, true);
      return;
    }

    if (linkType == LinkType.selfNavWebView.value) {
      // 自有H5页面，带导航栏
      openWeb(context, linkUrl, true);
      return;
    }

    if (linkType == LinkType.carType.value) {
      // 车款页
      String des = linkUrl;
      List<String> ids = des.split(',');
      int carTypeId;
      int shopId;
      if (ids.isEmpty) return;
      if (ids.length == 1) {
        carTypeId = int.parse(ids[0]);
        shopId = -1;
      } else {
        carTypeId = int.parse(ids[0]);
        shopId = int.parse(ids[1]);
      }
      // 从H5详情页面进入的app的详情页面内的店铺不再进入H5详情里了
      JumpTool().jumpToCarDetailMainPage(
        carTypeId: carTypeId,
        shopId: shopId,
        toShopDetailWebView: false,
        carPropertyChooseAlertViewShow: false,
        groupActivityId: 0,
        referrerPage: eventPage,
        sourceDict: sourceDict,
      );
      return;
    }

    if (linkType == LinkType.QIYUService.value) {
      // 客服MM
      JumpTool().jumpToKefuWebPage(
        group: KefuGroup.mm.value,
        title: null,
        imgUrl: null,
        eventPage: eventPage,
      );
      return;
    }

    if (linkType == LinkType.VRChooseCar.value) {
      // VR选车
      JumpTool().jumpToChooseCarOrVRCarPage(
        chooseCarType: ChooseCarType.vrCar.value,
      );
      return;
    }

    if (linkType == LinkType.liveWebView.value) {
      // 直播web
      JumpTool().jumpToWatchLiveInH5Page(
        webinarId: linkUrl,
      );
      return;
    }

    if (linkType == LinkType.welfareGiftDetail.value) {
      // 福利社礼品webvc
      int giftId = int.parse(linkUrl);
      JumpTool().jumpToWelfareGiftWebPage(
        giftId: giftId,
      );
      return;
    }

    if (linkType == LinkType.welfareGiftList.value) {
      // 运营打包的商品列表
      int bagId = int.parse(linkUrl);
      JumpTool().jumpToWelfareGiftsListPage(
        bagId: bagId,
        title: "更多",
      );
      return;
    }

    if (linkType == LinkType.welfareSPU.value) {
      // 优品商品详情
      int spuId = int.parse(linkUrl);
      JumpTool().jumpToCommodityDetailPage(
        commodityId: spuId,
        groupActivityId: 0,
        referrerPage: eventPage,
        sourceDict: sourceDict,
      );
      return;
    }

    if (linkType == LinkType.videoPost.value) {
      // 如果是帖子, 加载帖子界面
      int postId = int.parse(linkUrl);
      JumpTool().jumpToPostDetail(
        context: context,
        postId: postId,
        postTypeId: PostType.shortVideo,
        referrerPage: eventPage,
      );
      return;
    }

    if (linkType == LinkType.superiorProduct.value) {
      // 优品详情
      int commodityId = int.parse(linkUrl);
      JumpTool().jumpToCommodityDetailPage(
        commodityId: commodityId,
        groupActivityId: 0,
        referrerPage: eventPage,
        sourceDict: sourceDict,
      );
      return;
    }

    if (linkType == LinkType.newMerchandiseDetail.value) {
      // 跳领域服务的详情
      int commodityId = int.parse(linkUrl);
      JumpTool().jumpToMerchandiseDetailPage(
        context: context,
        commodityId: commodityId,
        referrerPage: eventPage,
        sourceDict: sourceDict,
      );
      return;
    }

    if (linkType == LinkType.superiorProductList.value) {
      // 优品列表
      String des = linkUrl;
      List<String> ids = des.split(',');
      int packageId = 0;
      int morePageStyle = 0;
      if (ids.isNotEmpty) {
        packageId = int.parse(ids[0]);
      }
      if (ids.length > 1) {
        morePageStyle = int.parse(ids[1]);
      }
      JumpTool().jumpToProductVoListPage(
        packageId: packageId,
        eventPage: eventPage,
        morePageStyle: morePageStyle,
      );
      return;
    }

    if (linkType == LinkType.classRoomPost.value) {
      // 如果是帖子, 加载帖子界面
      int postId = int.parse(linkUrl);
      JumpTool().jumpToPostDetail(
        context: context,
        postId: postId,
        postTypeId: PostType.classroom,
        referrerPage: eventPage,
      );
      return;
    }

    if (linkType == LinkType.topicDetail.value) {
      // 话题详情
      int topicId = int.parse(linkUrl);
      JumpTool().jumpToTopicDetailPage(
        context: context,
        topicId: topicId,
      );
      return;
    }

    if (linkType == LinkType.FM.value) {
      // FM
      JumpTool().jumpToFmMainPage(
        fmItemId: linkUrl,
      );
      return;
    }

    if (linkType == LinkType.shopGroupBuySwitch.value) {
      // 拼团
      JumpTool().jumpToShopGroupBuySwitchPage();
      return;
    }

    if (linkType == LinkType.shopServiceSwitch.value) {
      // 服务产品列表
      JumpTool().jumpToShopServiceSwitchPage();
      return;
    }

    if (linkType == LinkType.dearArea.value) {
      // 经销商专区
      JumpTool().jumpToDearAreaPage(pageCode: linkUrl);
      return;
    }

    if (linkType == LinkType.moreProductsLayout.value) {
      // 指定pagecode的组件列表 (pageCode, pageName)构成
      String des = linkUrl;
      List<String> ids = des.split(',');
      String pageCode;
      String title;
      if (ids.isEmpty) return;
      if (ids.length == 1) {
        pageCode = ids[0];
        title = "";
      } else {
        pageCode = ids[0];
        title = ids[1];
      }
      JumpTool()
          .jumpToShopNormalLayoutListPage(pageCode: pageCode, pageName: title);
      return;
    }

    if (linkType == LinkType.WXMINI.value) {
      // 跳wx小程序
      String des = linkUrl;
      if (des.contains(',')) {
        List<String> parts = des.split(',');
        if (parts.length > 1) {
          String userName = parts[1];
          String path = "";
          if (userName.contains(',')) {
            List<String> userNameAndPath = userName.split(',');
            userName = userNameAndPath[0];
            path = userNameAndPath[1];
          }
          JumpTool().openMiniProgram(
              userName: userName, path: path, completion: (bool success) {});
        }
      }
      return;
    }

    if (linkType == LinkType.topicList.value) {
      JumpTool().jumpToTopicListPage();
    }

    if (linkType == LinkType.bigVList.value) {
      JumpTool().jumpToBigVsListPage();
    }

    if (linkType == LinkType.clubList.value) {
      String des = linkUrl;
      List<String> ids = des.split(',');
      int cityId = 0;
      String cityStr = "";
      if (ids.isNotEmpty) {
        cityId = int.parse(ids[0]);
      }
      if (ids.length > 1) {
        cityStr = ids[1];
      }

      JumpTool().jumpToClubListHomePage(cityId: cityId, cityStr: cityStr);
    }

    if (linkType == LinkType.columnPostList.value) {
      String des = linkUrl;
      List<String> ids = des.split(',');
      int columnId = 0;
      int contentType = 0;
      String columnName = "";
      if (ids.isNotEmpty) {
        columnId = int.parse(ids[0]);
      }
      if (ids.length > 1) {
        contentType = int.parse(ids[1]);
      }
      if (ids.length > 2) {
        columnName = ids[2];
      }

      JumpTool().jumpToPastPostsPage(
        columnId: columnId,
        contentType: contentType,
        columnName: columnName,
        eventPage: eventPage,
      );
    }

    if (linkType == LinkType.secondCategoryProductList.value) {
      // 二级分类列表 更多页面跳转的数据   格式：（二级类目ID，更多页面样式，更多页面是否显示排序,筛选id）
      String des = linkUrl;
      List<String> ids = des.split(',');
      int secondCategoryId = 0;
      int morePageStyle = 0;
      bool morePageRank = false;
      int filterId = 0;
      if (ids.isNotEmpty) {
        secondCategoryId = int.parse(ids[0]);
      }
      if (ids.length > 1) {
        morePageStyle = int.parse(ids[1]);
      }
      if (ids.length > 2) {
        morePageRank = ids[2].toLowerCase() == 'true';
      }
      if (ids.length > 3) {
        filterId = int.parse(ids[3]);
      }

      JumpTool().jumpToSecondCategoryProductListPage(
        secondCategoryId: secondCategoryId,
        eventPage: eventPage,
        morePageStyle: morePageStyle,
        morePageRank: morePageRank,
        filterId: filterId,
      );
      return;
    }

    if (linkType == LinkType.userAndCartoChooseCarList.value) {
      // 人车生活-选择列表
      JumpTool().jumpToChooseCarGroupPage();
      return;
    }

    if (linkType == LinkType.userAndCarDetailPostList.value) {
      // 人车生活-车型详情下的帖子列表
      String des = linkUrl;
      List<String> ids = des.split(',');
      int contentGroupId = 0;
      if (ids.isNotEmpty) {
        contentGroupId = int.parse(ids[0]);
      }

      JumpTool()
          .jumpToCarContentGroupPostListPage(contentGroupId: contentGroupId);
      return;
    }

    if (linkType == LinkType.guanFangService.value) {
      // 官方专区
      JumpTool().jumpToShopServiceProductListPage(eventPage: eventPage);
      return;
    }

    if (linkType == LinkType.service4S.value) {
      // 4S店
      JumpTool().jumpToShop4SServiceListPage(pageName: eventPage);
      return;
    }

    if (linkType == LinkType.appointedTopicList.value) {
      // 指定话题合集列表
      int appointedId = int.parse(linkUrl);
      JumpTool().jumpToAppointedTopicListPage(appointedId: appointedId);
      return;
    }

    if (linkType == LinkType.userPostLists.value) {
      // 指定用户下的帖子列表
      JumpTool().jumpToUserPostListPage(userId: linkUrl);
      return;
    }

    if (linkType == LinkType.carHome.value) {
      // 跳出行
      // EventNotifier().postNotification(name: kJump2CarBaseVCNotifi, object: null);
      return;
    }

    if (linkType == LinkType.smartLife.value) {
      // // 未登录，返回
      // if (!UserModel().isLogin()) {
      //   UserModel().showGotoLoginAlertView();
      //   return;
      // }
    }

    if (linkType == LinkType.appStoreWriteReview.value) {
      // // App Store 写评论
      // String url = 'itms-apps://itunes.apple.com/app/id$kAppStoreId?action=write-review';
      // if (await canLaunch(url)) {
      //   await launch(url);
      // }
      return;
    }

    if (linkType == LinkType.newMerchandisePackageList.value) {
      // 优品新的列表
      String des = linkUrl;
      List<String> ids = des.split(',');
      int packageId = 0;
      int morePageStyle = 0;
      bool morePageRank = false;
      bool isHaveAll = false;
      int filterId = 0;
      if (ids.isNotEmpty) {
        packageId = int.parse(ids[0]);
      }
      if (ids.length > 1) {
        morePageStyle = int.parse(ids[1]);
      }
      if (ids.length > 2) {
        morePageRank = ids[2].toLowerCase() == 'true';
      }
      if (ids.length > 3) {
        filterId = int.parse(ids[3]);
      }
      if (ids.length > 4) {
        isHaveAll = ids[4].toLowerCase() == 'true';
      }

      JumpTool().jumpToNewMerchandiseListPage(
        secondCategoryId: 0,
        packageId: packageId,
        eventPage: eventPage,
        morePageStyle: morePageStyle,
        morePageRank: morePageRank,
        filterId: filterId,
        isHaveAll: isHaveAll,
        type: SuperiorProductListPageType,
      );
      return;
    }

    if (linkType ==
        LinkType.newMerchandiseCategoryOneAndSecondProductList.value) {
      // 一级+二级分类列表
      String des = linkUrl;
      List<String> ids = des.split(',');
      int secondCategoryId = 0;
      int morePageStyle = 0;
      bool morePageRank = false;
      bool isHaveAll = false;
      int filterId = 0;
      if (ids.isNotEmpty) {
        secondCategoryId = int.parse(ids[0]);
      }
      if (ids.length > 1) {
        morePageStyle = int.parse(ids[1]);
      }
      if (ids.length > 2) {
        morePageRank = ids[2].toLowerCase() == 'true';
      }
      if (ids.length > 3) {
        filterId = int.parse(ids[3]);
      }
      if (ids.length > 4) {
        isHaveAll = ids[4].toLowerCase() == 'true';
      }

      JumpTool().jumpToNewMerchandiseListPage(
        secondCategoryId: secondCategoryId,
        packageId: 0,
        eventPage: eventPage,
        morePageStyle: morePageStyle,
        morePageRank: morePageRank,
        filterId: filterId,
        isHaveAll: isHaveAll,
        type: SuperiorProductListPageType.newOneSecondCategory.value,
      );
      return;
    }

    if (linkType == LinkType.newMerchandiseSecondCategoryProductList.value) {
      // 二级分类列表
      String des = linkUrl;
      List<String> ids = des.split(',');
      int secondCategoryId = 0;
      int morePageStyle = 0;
      bool morePageRank = false;
      bool isHaveAll = false;
      int filterId = 0;
      if (ids.isNotEmpty) {
        secondCategoryId = int.parse(ids[0]);
      }
      if (ids.length > 1) {
        morePageStyle = int.parse(ids[1]);
      }
      if (ids.length > 2) {
        morePageRank = ids[2].toLowerCase() == 'true';
      }
      if (ids.length > 3) {
        filterId = int.parse(ids[3]);
      }
      if (ids.length > 4) {
        isHaveAll = ids[4].toLowerCase() == 'true';
      }

      JumpTool().jumpToNewMerchandiseListPage(
        secondCategoryId: secondCategoryId,
        packageId: 0,
        eventPage: eventPage,
        morePageStyle: morePageStyle,
        morePageRank: morePageRank,
        filterId: filterId,
        isHaveAll: isHaveAll,
        type: SuperiorProductListPageType.newSecondCategory.value,
      );
      return;
    }

    if (linkType == LinkType.discussionCommunity.value) {
      // 改装-讨论社区
      JumpTool().jumpToRefitForumPage();
      return;
    }

    if (linkType == LinkType.servicePartSort.value) {
      // 服务-选配件
      JumpTool().jumpToServiceSortCommodityListPage();
      return;
    }

    if (linkType == LinkType.serviceVip.value) {
      // 尊享权益
      JumpTool().jumpToShopVipPage();
      return;
    }
  }

  void jumpToHandlePage(BuildContext context,
      {UserHandleModel? handleModel,
      UserHandleListModel? listModel,
      String? categoryName,
      Map<String, dynamic>? sourceDict}) {
    if (handleModel == null) {
      return;
    }
    int triggerType = handleModel.triggerType ?? 0;
    if (triggerType == UserHandleType.selfH5.value) {
      if (GlobalData().isLogin) {
        if ((handleModel.triggerValue ?? '')
                .contains('introduce/introduceList.html') ||
            (handleModel.triggerValue ?? '')
                .contains('welfare/gameList.html')) {
          // 规避审核
          String encodedString = 'MTMzMDExMjU4OTA=';
          String decodedString = utf8.decode(base64Decode(encodedString));
          UserModel? user = GlobalData().userModel;
          if (user?.mobile == decodedString) {
            return;
          }
        }
      }
      String url = handleModel.triggerValue ?? '';
      if (url.isNotEmpty) {
        openWeb(context, url, false);
      } else {
        LoadingManager.showError('地址为空 请重新刷新重试');
      }
      return;
    }
    if (triggerType == UserHandleType.functions.value) {
      Navigator.of(context).push(CustomCupertinoPageRoute(
        builder: (context) =>
            MoreServicePage(categoryIds: handleModel.triggerValue ?? ''),
      ));
      return;
    }
    if (triggerType == UserHandleType.h5HasNav.value) {
      String url = handleModel.triggerValue ?? '';
      openWeb(context, url, true);
      return;
    }
    if (triggerType == UserHandleType.followedUser.value) {
      Navigator.of(context).push(CustomCupertinoPageRoute(
        builder: (context) => const FollowListPage(),
        canSwipeBack: false, // 禁用手势返回
      ));
    }
    if (triggerType == UserHandleType.collectedPost.value) {
      Navigator.of(context).push(CustomCupertinoPageRoute(
        builder: (context) => const CollectListPage(),
        canSwipeBack: false, // 禁用手势返回
      ));
    }
    if (triggerType == UserHandleType.message.value) {
      Navigator.of(context).push(MaterialPageRoute(builder: (context) {
        return const ReportPage();
      }));
    }
    if (triggerType == UserHandleType.topicDetail.value) {
      // 话题详情
      int topicId = int.parse(handleModel.triggerValue ?? '0');
      JumpTool().jumpToTopicDetailPage(
        context: context,
        topicId: topicId,
      );
      return;
    }
//   LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToPostDetail(
      {required BuildContext context,
      required int postId,
      required PostType postTypeId,
      required String referrerPage}) {
    if (postTypeId == PostType.normal) {
      Navigator.of(context).push(CustomCupertinoPageRoute(
        builder: (context) => TopicPostDetailsPage(
          postId: postId,
          postTypeId: postTypeId.value,
        ),
        canSwipeBack: false, // 禁用手势返回
      ));
      return;
    }
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToCommunityDetail(
      {required int communityId, required communityName}) {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToNewMerchandiseListPage(
      {required int secondCategoryId,
      required int packageId,
      required String eventPage,
      required int morePageStyle,
      required bool morePageRank,
      required int filterId,
      required bool isHaveAll,
      required type}) {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToShopVipPage() {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToServiceSortCommodityListPage() {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToRefitForumPage() {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToAppointedTopicListPage({required int appointedId}) {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToUserPostListPage({required String userId}) {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToChooseCarGroupPage() {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToShop4SServiceListPage({required String pageName}) {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToShopServiceProductListPage({required String eventPage}) {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToCarContentGroupPostListPage({required int contentGroupId}) {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToSecondCategoryProductListPage(
      {required int secondCategoryId,
      required String eventPage,
      required int morePageStyle,
      required bool morePageRank,
      required int filterId}) {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToClubListHomePage({required int cityId, required String cityStr}) {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToPastPostsPage(
      {required int columnId,
      required int contentType,
      required String columnName,
      required String eventPage}) {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToTopicListPage() {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToBigVsListPage() {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void openMiniProgram(
      {required String userName,
      required String path,
      required Null Function(bool success) completion}) {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToShopNormalLayoutListPage(
      {required String pageCode, required String pageName}) {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToDearAreaPage({required String pageCode}) {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToShopGroupBuySwitchPage() {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToShopServiceSwitchPage() {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToFmMainPage({required String fmItemId}) {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  Future<void> jumpToTopicDetailPage(
      {required BuildContext context, required int topicId}) async {
    communityAPI.getTopicInfo(topicId).then((List<RecommendHot> hotList) {
      if (hotList.isEmpty) return;
      Navigator.of(context).push(CustomCupertinoPageRoute(
        builder: (context) => TopicDetailPage(hot: hotList[0]),
      ));
    });
  }

  void jumpToProductVoListPage(
      {required int packageId,
      required String eventPage,
      required int morePageStyle}) {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToMerchandiseDetailPage(
      {required BuildContext context,
      required int commodityId,
      String? referrerPage,
      Map<String, dynamic>? sourceDict}) {
    Navigator.of(context).push(CustomCupertinoPageRoute(
      builder: (context) => StoreDetailPage(id: commodityId, code: 0),
      canSwipeBack: false, // 禁用手势返回
    ));
  }

  void jumpToCommodityDetailPage(
      {required int commodityId,
      required int groupActivityId,
      required String referrerPage,
      Map<String, dynamic>? sourceDict}) {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToWelfareGiftsListPage({required int bagId, required String title}) {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToWelfareGiftWebPage({required int giftId}) {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToWatchLiveInH5Page({required String webinarId}) {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToChooseCarOrVRCarPage({required int chooseCarType}) {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToKefuWebPage(
      {required int group,
      required title,
      required imgUrl,
      required String eventPage}) {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToCarDetailMainPage(
      {required int carTypeId,
      required int shopId,
      required bool toShopDetailWebView,
      required bool carPropertyChooseAlertViewShow,
      required int groupActivityId,
      required String referrerPage,
      Map<String, dynamic>? sourceDict}) {
    LoadingManager.showToast('页面功能开发中 敬请期待');
  }

  void jumpToBleScanPage(BuildContext context) {
    Navigator.of(context).push(CustomCupertinoPageRoute(
      builder: (context) => BleTestPage(),
      canSwipeBack: false, // 禁用手势返回
    ));
  }

  void jumpToCarOrderDetailPage(
      BuildContext context, int commodityId, int channelCode, String urlPre) {
    Navigator.of(context).push(CustomCupertinoPageRoute(
      builder: (context) => CarOrderDetailPage(
        commodityId: commodityId.toString(),
        channelCode: channelCode.toString(),
        urlPre: urlPre,
      ),
      canSwipeBack: false, // 禁用手势返回
    ));
  }
}
