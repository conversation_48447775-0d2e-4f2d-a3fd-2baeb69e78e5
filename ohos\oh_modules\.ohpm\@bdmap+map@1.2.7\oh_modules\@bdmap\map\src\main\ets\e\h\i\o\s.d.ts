          import Bundle from "./i1"; import image from '@ohos.multimedia.image'; import type { Callback } from "../../g1/h1"; import type { ImageSourceType, ImageURLString, ImageOpt } from "../../g1/a2"; import BmBitmapResource from "../c2/f2/t3";             export default class ImageEntity { private _width; private _height; private _x; private _y; private _scale; private _flipX; private _flipY; private _scaleDpi; private _sourceHashCode; private _hashcode; private callback; private _src; private type; private status; private bitmap; private imagesCache; private needArrayBuffer;         imageSource: ArrayBuffer;                                         constructor(source: ImageSourceType, width?: number | ImageOpt, height?: number, options?: ImageOpt);           private _dealSource;         _callBacks(): void;               _isExit(src: ImageURLString, filePath: string, fail: Callback<void> | null): void;             _failTry(src: ImageURLString, filePath: string): void;       requestImage(src: ImageURLString, filePath: string): void;       download(src: ImageURLString): void;           getPixesMap(callback: Callback<image.PixelMap>): void;           getArrayBuffer(callback?: Callback<ArrayBuffer>): void;         _setNeedArrayBuffer(status: boolean): void;           private getArrayBufferByPixelMap;       getPixesArrayBuffer(callback: Callback<ArrayBuffer>, inner?: number): void;               _imageSourceToPixesMap(imageSource: image.ImageSource, callback: Callback<image.PixelMap | ArrayBuffer>, t33?: boolean): void;         _pixelMapClone(pixelMap: image.PixelMap): Promise<void | image.PixelMap>;             _pixesMapToArrayBuffer(pixelMap: image.PixelMap, callback: Callback<ArrayBuffer>): void;       _flip(pixelMap: image.PixelMap): Promise<unknown>; getBitmap(): BmBitmapResource;       getHashcode(): string;       getSourceHashcode(): string;         updateHashCode(): void;         updateUUID(): void;       toBundle(): Bundle;       toString(): string; get src(): ImageSourceType;       get x(): number;       get y(): number; get width(): number; get height(): number;       get scale(): number;       set src(source: ImageSourceType);       replace(source: ImageSourceType, width: number, height: number): void;         set x(val: number);         set y(val: number);         set width(val: number);         set height(val: number);         set scale(val: number); destroy(k33?: boolean): void; } 