import { CollisionBehavior, Located } from "../../util/b1/c1"; import Overlay from "./m"; import Bundle from "../o/i1"; import { LatLng, Point } from '@bdmap/base'; import type { IBaseMarkerOption, Nullable } from "../../g1/a2"; import type OverlayMgr from "./j2"; import PopView from "../n1/o1"; import type OverlayListener from "./i2";             export default class BaseMarker extends Overlay { private mPosition; private mAnchorX; private mAnchorY; private mLocated; private mIsPerspective; private mIsDraggable; private mRotate; private mYOffset; private mXOffset; private mIsFlat; private mIsTop; private mIsFixed; private mPeriod; private mScaleX; private mScaleY; private mFixedScreenPosition; private mPopView; private mPriority; private mIsJoinCollision; private bmMarker; constructor(u35: IBaseMarkerOption, type: number);           scaleX(scaleX: number): this;         getScaleX(): number;           setScaleX(scaleX: number): void;           scaleY(scaleY: number): this;         getScaleY(): number;           setScaleY(scaleY: number): void;         yOffset(yOffset: number): this;         getYOffset(): number;         setYOffset(yOffset: number): void;         xOffset(xOffset: number): this;         getXOffset(): number;         setXOffset(xOffset: number): void;           draggable(draggable: boolean): this;           getDraggable(): boolean;           setDraggable(draggable: boolean): void;         flat(flat: boolean): this;         getFlat(): boolean;         setFlat(flat: boolean): void;           period(period: number): this;         getPeriod(): number;           setPeriod(period: number): void;         position(position: LatLng): this;         getPosition(): LatLng;         setPosition(position: LatLng): void;         changePosition(position: LatLng): void;         popView(t35: PopView): this;         setPopView(s35: PopView): void;         getPopView(): PopView;         located(r35: Located): void;         setLocated(q35: Located): void;         getLocated(): Located;               anchor(o35: number, p35: number): this;           getAnchor(): { anchorX: number; anchorY: number; };               setAnchor(m35: number, n35: number): this;         set rotate(rotate: number);         getRotate(): number;         setRotate(rotate: number): void;         isJoinCollision(l35: CollisionBehavior): this;         getIsJoinCollision(): CollisionBehavior;         setIsJoinCollision(k35: CollisionBehavior): void;         isFixed(j35: boolean): void;         setIsFixed(i35: boolean): void;         getFixedScreen(): boolean;           fixedScreenPosition(point: Nullable<Point>): this;           setFixedScreenPosition(point: Nullable<Point>): void;         perspective(perspective: boolean): this;         getPerspective(): boolean;         setPerspective(perspective: boolean): void;         priority(priority: number): this;         getPriority(): number;         setPriority(priority: number): void;         get typeName(): string;         setListener(listener: OverlayListener): void;       preUpdatePoint(): void;       baseFormat(bundle: Bundle): Bundle;       toString(): string;           toBundle(f35: OverlayMgr): Promise<Bundle>; } 