// @keepTs
// @ts-nocheck
/**
 * 文件预览工具类
 * Created on 2024/10/23
 * <AUTHOR>
 */
export declare class FilePreviewUtil {
    /**
     * 通过文件后缀获取文件mimeType类型
     * @param filePostfix
     */
    static getFileMimeTypeByFilePostfix(z105: string): string;
    /**
     * 文件预览
     * @param context
     * @param title 标题
     * @param filePostfix 文件后缀
     * @param fileUri 本地文件uri
     */
    static filePreview(o105: Context, p105: string, q105: string, r105: string): void;
    /**
     * 关闭预览窗口
     * @param context
     */
    static closePreview(l105: Context): void;
}
