import BmObject from "../u2"; import BmBitmapResource from "./t3"; export default class BmTrackStyle extends BmObject { private width; private trackType; private bmpRes; private opacity; private bmTrackPaletteRes; private mPaletteOpacity; private color; constructor();           setWidth(width: number): any; setColor(p26: number): any; setTrackType(o26: number): any; setBitmapResource(n26: BmBitmapResource): any; setPaletteBitmapResource(m26: BmBitmapResource): any; setOpacity(opacity: number): any; setPaletteOpacity(l26: number): any; } 