import 'package:flutter/services.dart';
import 'package:flutter_platform_utils/flutter_platform_utils.dart';
import 'package:wuling_flutter_app/utils/sp_util.dart';

import 'loading_manager.dart';
import 'log_manager.dart';

class LocationManager {
  static const PERMISSIONS_REQUEST_LIMIT_TIME =
      "permissions_request_limit_time";
  static const LOCATION_CACHE = "location_cache";
  static int count = 0;

  // 定义MethodChannel，通道名称为 com.sgmw.flutter/location
  static const MethodChannel _channel =
      MethodChannel('com.sgmw.flutter/location');

  // 方法：获取当前经纬度
  static Future getLocation() async {
    if (PlatformUtils.isOhos) {
      try {
        var time = SpUtil().getString(PERMISSIONS_REQUEST_LIMIT_TIME);
        if (time.isEmpty) time = "0";
        if (count > 1 &&
            (DateTime.now().millisecondsSinceEpoch - int.parse(time)) <
                24 * 60 * 60 * 1000) {
          return SpUtil().getJSON(LOCATION_CACHE);
        } else {
          // 调用原生方法 'getLocation'
          final response = await _channel.invokeMethod('getLocation');
          SpUtil().setJSON(LOCATION_CACHE, response);
          return response;
        }
      } on PlatformException catch (e) {
        if (e.code == "2") {
          count++;
          if (count > 1)
            SpUtil().setString(PERMISSIONS_REQUEST_LIMIT_TIME,
                DateTime.now().millisecondsSinceEpoch.toString());
        }
        LogManager().debug("Failed to open app detail page: ${e.message}");
        return null;
      }
    }
    return null;
  }

  static Future getPerimeterLocation() async {
    if (PlatformUtils.isOhos) {
      try {
        // 调用原生方法 'getLocation'
        final response = await _channel.invokeMethod('getPerimeterLocation');
        return response;
      } on PlatformException catch (e) {
        LogManager().debug("Failed to open app detail page: ${e.message}");
        return null;
      }
    }
    return null;
  }

  static Future jumpNavigation(String mapName, dealerModel) async {
    if (PlatformUtils.isOhos) {
      try {
        // 调用原生方法 'jumpNavigation'
        await _channel.invokeMethod('jumpNavigation', {
          'mapName': mapName,
          "latitude": dealerModel.latitude,
          "longitude": dealerModel.longitude,
          "address": dealerModel.dealerShortName
        });
      } on PlatformException catch (e) {
        LogManager().debug("Failed to open app detail page: ${e.message}");
      }
    }
  }

  static Future jumpMap(
      String name, String address, String lat, String lon) async {
    if (PlatformUtils.isOhos) {
      try {
        double latitude = double.parse(lat);
        double longitude = double.parse(lon);
        // 调用原生方法 'jumpNavigation'
        await _channel.invokeMethod('jumpMap', {
          "name": name,
          'address': address,
          "latitude": latitude,
          "longitude": longitude,
        });
      } on PlatformException catch (e) {
        LogManager().debug("Failed to open app detail page: ${e.message}");
      }
    }
  }

  static Future getAddressFromLocation(String lat, String lon) async {
    if (PlatformUtils.isOhos) {
      try {
        double latitude = double.parse(lat);
        double longitude = double.parse(lon);
        // 调用原生方法 'getLocation'
        String response =
            await _channel.invokeMethod('getAddressFromLocation', {
          "latitude": latitude,
          "longitude": longitude,
        });
        //LoadingManager.showToast(response);
        return response;
      } on PlatformException catch (e) {
        LogManager().debug("Failed to open app detail page: ${e.message}");
        return "";
      }
    }
    return "";
  }
}

// 接收H5传递过来的导航参数的类
class NavigationParam {
  // 目的地
  String? dealerShortName;
  // 经纬度
  double? longitude;
  double? latitude;

  NavigationParam({
    this.dealerShortName,
    this.longitude,
    this.latitude
  });
}