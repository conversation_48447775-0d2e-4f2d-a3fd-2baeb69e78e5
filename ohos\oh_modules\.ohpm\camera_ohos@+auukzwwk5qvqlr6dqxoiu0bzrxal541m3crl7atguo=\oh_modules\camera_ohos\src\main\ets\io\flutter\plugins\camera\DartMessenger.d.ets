import HashMap from "@ohos.util.HashMap";
import { MethodChannel } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import { FocusMode } from './features/autofocus/FocusMode';
import { ExposureMode } from './features/exposurelock/ExposureMode';
import { DeviceOrientation } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/systemchannels/PlatformChannel';
declare enum DeviceEventType {
    ORIENTATION_CHANGED = "orientation_changed"
}
declare enum CameraEventType {
    ERROR = "error",
    CLOSING = "camera_closing",
    INITIALIZED = "initialized"
}
export declare class DartMessenger {
    cameraChannel: MethodChannel;
    deviceChannel: MethodChannel;
    constructor(messenger: BinaryMessenger, cameraId: Number);
    sendDeviceOrientationChangeEvent(orientation: DeviceOrientation): void;
    sendCameraInitializedEvent(previewWidth: number, previewHeight: number, exposureMode: ExposureMode, focusMode: FocusMode, exposurePointSupported: boolean, focusPointSupported: boolean): void;
    sendCameraClosingEvent(): void;
    sendCameraErrorEvent(description?: string): void;
    sendCameraEvent(eventType: CameraEventType, args?: HashMap<String, Object>): void;
    sendDeviceEvent(eventType: DeviceEventType, args?: HashMap<String, Object>): void;
    finish(result: MethodResult, payload?: Object): void;
    error(result: MethodResult, errorCode: string, errorMessage?: string, errorDetails?: Object): void;
}
export {};
