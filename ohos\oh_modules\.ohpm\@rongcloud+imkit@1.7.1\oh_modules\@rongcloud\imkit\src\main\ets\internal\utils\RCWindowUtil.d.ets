// @keepTs
// @ts-nocheck
/**
 * Created on 2024/10/30
 * 应用窗口相关工具类
 * <AUTHOR>
 */
import { window } from '@kit.ArkUI';
export declare class RCWindowUtil {
    private static windowClass?;
    /**
     * 设置应用窗口全屏
     * @param windowStage
     * @param isLayoutFullScreen 全屏模式
     * @returns boolean 是否设置成功
     */
    static setWindowLayoutFullScreen(z351: window.WindowStage, a352: boolean): Promise<boolean>;
    /**
     * 获取是否开启全屏
     * @returns
     */
    static getWindowIsLayoutFullScreen(s351: Context): Promise<boolean>;
    /**
     * 获取窗口高度
     * @param context
     * @returns
     */
    static getWindowHeight(l351: Context): Promise<number>;
    /**
     * 获取系统默认区域的高度，顶部状态栏/底部导航栏（目前未使用）/导航条区域
     * @param context
     * @returns Array<number> [顶部状态栏,底部导航栏,导航条区域]，catch 会返回空数组
     */
    static getWindowAvoidAreaHeight(x350: Context): Promise<Array<number>>;
    /**
     * 设置窗口全屏，隐藏显示状态栏和导航条
     * @param visible true:显示，false:隐藏
     */
    static setWindowFullScreen(o350: boolean): Promise<boolean>;
    /**
     * 隐藏显示状态栏和导航条
     * @param visible
     */
    private static setSystemBarVisible;
    /**
     * 获取窗口宽高
     * @returns
     */
    static getWindowSize(): Promise<window.Size>;
}
