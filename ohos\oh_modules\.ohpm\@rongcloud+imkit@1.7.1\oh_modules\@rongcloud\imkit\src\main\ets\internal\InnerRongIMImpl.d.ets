// @keepTs
// @ts-nocheck
/**
 * IMKit 核心类的具体实现类
 * @version 1.0.0
 */
import { InitOption } from '@rongcloud/imlib';
import { InnerConnectionServiceImpl } from './connection/InnerConnectionServiceImpl';
import { InnerConversationServiceImpl } from './conversation/InnerConversationServiceImpl';
import { InnerConversationListServiceImpl } from './conversationlist/InnerConversationListServiceImpl';
import { InnerUserDataServiceImpl } from './user/InnerUserDataServiceImpl';
import { InnerMessageServiceImpl } from './message/InnerMessageServiceImpl';
import { InnerNotificationServiceImpl } from './notification/InnerNotificationServiceImpl';
import { InnerPermissionServiceImpl } from './permission/InnerPermissionServiceImpl';
import { PublicServiceDataService } from '../publicservice/PublicServiceDataService';
import { InnerForWardServiceImpl } from './forward/InnerForWardServiceImpl';
export declare class InnerRongIMImpl {
    private static instance;
    private connectionImpl;
    private messageImpl;
    private conversationImpl;
    private conversationListImpl;
    private userDataImpl;
    private permissionServiceImpl;
    private notificationImpl;
    private publicServiceImpl;
    private forWardServiceImpl;
    private constructor();
    static getInstance(): InnerRongIMImpl;
    connectionService(): InnerConnectionServiceImpl;
    messageService(): InnerMessageServiceImpl;
    conversationService(): InnerConversationServiceImpl;
    conversationListService(): InnerConversationListServiceImpl;
    userDataService(): InnerUserDataServiceImpl;
    permissionService(): InnerPermissionServiceImpl;
    notificationService(): InnerNotificationServiceImpl;
    publicService(): PublicServiceDataService;
    forWardService(): InnerForWardServiceImpl;
    onInit(r308: Context, s308: string, t308: InitOption): void;
}
