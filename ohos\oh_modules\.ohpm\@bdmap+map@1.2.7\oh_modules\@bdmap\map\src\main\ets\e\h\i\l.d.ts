          import { Point, LatLng } from '@bdmap/base'; import WinRound from "./o/l1"; import LatLngBound from "./o/m1"; import type { IMapStatusOption } from "../g1/a2"; import type BaseMap from "./o2"; import type { MapStatusBundle, NativeMapStatusBundle } from "../g1/i1";             export default class MapStatus { private _PtOffsetX; private _PtOffsetY; private _CenterPtX; private _CenterPtY; private _Level; private _Rotation; private _Overlooking; private _ZoomUnits; private _WinRound; private _GeoRound; private _isAnimate; private _animationTime; private _minZoom; private _maxZoom; private _baseMap;                                               constructor(p31: IMapStatusOption);           setRotate(o31: number): this;           get rotate(): number;         set rotate(n31: number);         setRotatePlus(val: number): this;         getRotate(): number;           setLevel(val: number): this;         get level(): number;           set level(val: number);         getLevel(): number;           setOverlooking(l31: number): this;         get overlooking(): number;           set overlooking(k31: number);         setOverlookingPlus(plus: number): this;         getOverlooking(): number;         setCenterPoint(input: Point | LatLng): this;         set centerPoint(input: Point | LatLng);         get centerPoint(): LatLng;         getCenterPoint(): LatLng;         getCenterPointMC(): Point;         get zoomUnits(): number;         getPtOffset(): number[];         setPtOffset(i31: [ number, number ]): this;         get geoRound(): LatLngBound;         getGeoRound(): LatLngBound;         get winRound(): WinRound;         set winRound(h31: WinRound);         setWinRound(g31: WinRound): this;         getWinRound(): WinRound;         setIsAnimate(f31: boolean): this;         setAnimationTime(e31: number): this;         getAnimationTime(): number;         get maxZoom(): number;           set maxZoom(zoom: number);         get minZoom(): number;           set minZoom(zoom: number);       updateLevel(): void;         set baseMap(d31: BaseMap);         get baseMap(): BaseMap; refresh(): void;         reload(): this;         parseFromBundle(y30: NativeMapStatusBundle): void;         toBundle(): MapStatusBundle; destroy(): void; toString(): string; } 