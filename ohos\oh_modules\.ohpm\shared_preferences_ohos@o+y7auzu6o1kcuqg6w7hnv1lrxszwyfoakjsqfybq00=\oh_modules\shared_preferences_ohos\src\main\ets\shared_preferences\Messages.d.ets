import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import MessageCodec from '@ohos/flutter_ohos/src/main/ets/plugin/common/MessageCodec';
export declare function wrapError(exception: Error): Array<Object>;
export interface SharedPreferencesApi {
    /** Removes property from shared preferences data set. */
    remove(key: string): boolean;
    /** Adds property to shared preferences data set of type boolean. */
    setBool(key: string, value: boolean): Promise<void>;
    /** Adds property to shared preferences data set of type string. */
    setString(key: string, value: string): Promise<void>;
    /** Adds property to shared preferences data set of type int. */
    setInt(key: string, value: number): Promise<void>;
    /** Adds property to shared preferences data set of type double. */
    setDouble(key: string, value: number): Promise<void>;
    /** Adds property to shared preferences data set of type String. */
    setStringList(key: string, value: Array<string>): Promise<void>;
    /** Removes all properties from shared preferences data set with matching prefix. */
    clear(prefix: string, allowList: Array<string>): Promise<void>;
    /** Gets all properties from shared preferences data set with matching prefix. */
    getAll(prefix: string, allowList: Array<string>): Promise<Object>;
    /** The codec used by SharedPreferencesApi. */
    getCodec(): MessageCodec<Object>;
    /**
     * Sets up an instance of `SharedPreferencesApi` to handle messages through the
     * `binaryMessenger`.
     */
    setup(messenger: BinaryMessenger, api: SharedPreferencesApi): void;
}
