import 'dart:ui';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/models/car/car_info_model.dart';
import 'package:wuling_flutter_app/page/base_page/base_page.dart';
import 'package:wuling_flutter_app/utils/sp_util.dart';

import '../../api/car_api.dart';
import '../../utils/manager/loading_manager.dart';
import '../../utils/manager/log_manager.dart';

class BluetoothSettingForHEVPage extends BasePage {

  final CarInfoModel? carInfoModel;

  BluetoothSettingForHEVPage({super.key,this.carInfoModel})
      : super(
    appBarTitle: '蓝牙设置',
    initialStatusBarBrightness: Brightness.dark,
  );

  @override
  _BluetoothSettingForHEVPageState createState() => _BluetoothSettingForHEVPageState();
}

class _BluetoothSettingForHEVPageState extends BasePageState<BluetoothSettingForHEVPage> {

  late int mShakeState;

  @override
  void initState() {
    // TODO: implement initState
    mShakeState = SpUtil().getInt('shake');
    super.initState();
  }

  @override
  Widget buildPageContent(BuildContext context) {
    // TODO: implement buildPageContent
    return _itemSettingCheckbox();
  }

  Widget _itemSettingCheckbox(){
    return InkWell(
      onTap: (){
        LogManager().debug('Item tapped _itemSettingCheckbox!');
      },
      child: SizedBox(
        height: 50,
        child: Stack(
          children: [
            const Align(
              alignment: Alignment(-0.93, 0),
              child: Text('摇一摇开锁',style:TextStyle(fontSize: 17,color: Colors.black),),
            ),
            Align(
              alignment: const Alignment(0.93, 0),
              child: Switch(
                value: mShakeState == 0 ? false : true,
                inactiveTrackColor: Colors.grey,
                inactiveThumbColor:Colors.white,
                activeColor:Colors.green,
                onChanged: (bool value) {
                  setState(() {
                    if (mShakeState == 1) {
                      setSupportShake("0",0);
                    }else{
                      setSupportShake("1",1);
                    }
                  });
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void setSupportShake(String settingVal ,int type) {
    Map<String, dynamic> map = {
      "vin": widget.carInfoModel?.vin,
      "settingVal" : settingVal,
      "type" : type
    };

    carAPI.setShakeLockStatus(map).then((result){
      if (result) {
        mShakeState = type;
        SpUtil().setInt('shake', mShakeState);
      }else{
        LoadingManager.showToast('操作失败，请稍后再试');
        mShakeState = type == 1? 0 : 1;
        SpUtil().setInt('shake', mShakeState);
      }
    }).onError((error, stackTrace){
      LoadingManager.showToast('操作失败，请稍后再试');
      mShakeState = type == 1? 0 : 1;
      SpUtil().setInt('shake', mShakeState);
    });
  }

}