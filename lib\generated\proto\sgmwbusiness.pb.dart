//
//  Generated code. Do not modify.
//  source: sgmwbusiness.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

class SgmwBusinessInfo extends $pb.GeneratedMessage {
  factory SgmwBusinessInfo({
    $fixnum.Int64? expireAtTimestamp,
    $core.String? vin,
    $core.String? msgId,
    $core.String? businessType,
    $core.String? belongUserId,
    $core.String? businessValue,
  }) {
    final $result = create();
    if (expireAtTimestamp != null) {
      $result.expireAtTimestamp = expireAtTimestamp;
    }
    if (vin != null) {
      $result.vin = vin;
    }
    if (msgId != null) {
      $result.msgId = msgId;
    }
    if (businessType != null) {
      $result.businessType = businessType;
    }
    if (belongUserId != null) {
      $result.belongUserId = belongUserId;
    }
    if (businessValue != null) {
      $result.businessValue = businessValue;
    }
    return $result;
  }
  SgmwBusinessInfo._() : super();
  factory SgmwBusinessInfo.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SgmwBusinessInfo.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'SgmwBusinessInfo', package: const $pb.PackageName(_omitMessageNames ? '' : 'com.cloudyoung.sgmw.common.car.proto'), createEmptyInstance: create)
    ..aInt64(1, _omitFieldNames ? '' : 'expireAtTimestamp', protoName: 'expireAtTimestamp')
    ..aOS(2, _omitFieldNames ? '' : 'vin')
    ..aOS(3, _omitFieldNames ? '' : 'msgId', protoName: 'msgId')
    ..aOS(4, _omitFieldNames ? '' : 'businessType', protoName: 'businessType')
    ..aOS(5, _omitFieldNames ? '' : 'belongUserId', protoName: 'belongUserId')
    ..aOS(6, _omitFieldNames ? '' : 'businessValue', protoName: 'businessValue')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SgmwBusinessInfo clone() => SgmwBusinessInfo()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SgmwBusinessInfo copyWith(void Function(SgmwBusinessInfo) updates) => super.copyWith((message) => updates(message as SgmwBusinessInfo)) as SgmwBusinessInfo;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static SgmwBusinessInfo create() => SgmwBusinessInfo._();
  SgmwBusinessInfo createEmptyInstance() => create();
  static $pb.PbList<SgmwBusinessInfo> createRepeated() => $pb.PbList<SgmwBusinessInfo>();
  @$core.pragma('dart2js:noInline')
  static SgmwBusinessInfo getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SgmwBusinessInfo>(create);
  static SgmwBusinessInfo? _defaultInstance;

  /// 消息过期时间0L，表示不过期，大于0 表示超过时间戳的数据可以不用处理。单位毫秒
  @$pb.TagNumber(1)
  $fixnum.Int64 get expireAtTimestamp => $_getI64(0);
  @$pb.TagNumber(1)
  set expireAtTimestamp($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasExpireAtTimestamp() => $_has(0);
  @$pb.TagNumber(1)
  void clearExpireAtTimestamp() => clearField(1);

  /// 消息所属vin
  @$pb.TagNumber(2)
  $core.String get vin => $_getSZ(1);
  @$pb.TagNumber(2)
  set vin($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasVin() => $_has(1);
  @$pb.TagNumber(2)
  void clearVin() => clearField(2);

  /// 消息id 可用于客户端去重消息
  @$pb.TagNumber(3)
  $core.String get msgId => $_getSZ(2);
  @$pb.TagNumber(3)
  set msgId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasMsgId() => $_has(2);
  @$pb.TagNumber(3)
  void clearMsgId() => clearField(3);

  /// 业务类型
  @$pb.TagNumber(4)
  $core.String get businessType => $_getSZ(3);
  @$pb.TagNumber(4)
  set businessType($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasBusinessType() => $_has(3);
  @$pb.TagNumber(4)
  void clearBusinessType() => clearField(4);

  /// 消息所属用户id(经过base64处理) 如果是空，则表示匹配所有用户
  @$pb.TagNumber(5)
  $core.String get belongUserId => $_getSZ(4);
  @$pb.TagNumber(5)
  set belongUserId($core.String v) { $_setString(4, v); }
  @$pb.TagNumber(5)
  $core.bool hasBelongUserId() => $_has(4);
  @$pb.TagNumber(5)
  void clearBelongUserId() => clearField(5);

  /// businessValue 业务值 泊车通知：是通知文案
  @$pb.TagNumber(6)
  $core.String get businessValue => $_getSZ(5);
  @$pb.TagNumber(6)
  set businessValue($core.String v) { $_setString(5, v); }
  @$pb.TagNumber(6)
  $core.bool hasBusinessValue() => $_has(5);
  @$pb.TagNumber(6)
  void clearBusinessValue() => clearField(6);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
