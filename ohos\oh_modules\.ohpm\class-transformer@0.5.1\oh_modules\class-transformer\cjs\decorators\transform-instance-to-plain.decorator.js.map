{"version": 3, "file": "transform-instance-to-plain.decorator.js", "sourceRoot": "", "sources": ["../../../src/decorators/transform-instance-to-plain.decorator.ts"], "names": [], "mappings": ";;;AAAA,0DAAuD;AAGvD;;;;GAIG;AACH,SAAgB,wBAAwB,CAAC,MAA8B;IACrE,OAAO,UAAU,MAA2B,EAAE,WAA4B,EAAE,UAA8B;QACxG,MAAM,gBAAgB,GAAqB,IAAI,mCAAgB,EAAE,CAAC;QAClE,MAAM,cAAc,GAAG,UAAU,CAAC,KAAK,CAAC;QAExC,UAAU,CAAC,KAAK,GAAG,UAAU,GAAG,IAAW;YACzC,MAAM,MAAM,GAAQ,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YACrD,MAAM,SAAS,GACb,CAAC,CAAC,MAAM,IAAI,CAAC,OAAO,MAAM,KAAK,QAAQ,IAAI,OAAO,MAAM,KAAK,UAAU,CAAC,IAAI,OAAO,MAAM,CAAC,IAAI,KAAK,UAAU,CAAC;YAChH,OAAO,SAAS;gBACd,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,IAAS,EAAE,EAAE,CAAC,gBAAgB,CAAC,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAC5E,CAAC,CAAC,gBAAgB,CAAC,eAAe,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;QACvD,CAAC,CAAC;IACJ,CAAC,CAAC;AACJ,CAAC;AAdD,4DAcC", "sourcesContent": ["import { ClassTransformer } from '../ClassTransformer';\nimport { ClassTransformOptions } from '../interfaces';\n\n/**\n * Transform the object from class to plain object and return only with the exposed properties.\n *\n * Can be applied to functions and getters/setters only.\n */\nexport function TransformInstanceToPlain(params?: ClassTransformOptions): MethodDecorator {\n  return function (target: Record<string, any>, propertyKey: string | Symbol, descriptor: PropertyDescriptor): void {\n    const classTransformer: ClassTransformer = new ClassTransformer();\n    const originalMethod = descriptor.value;\n\n    descriptor.value = function (...args: any[]): Record<string, any> {\n      const result: any = originalMethod.apply(this, args);\n      const isPromise =\n        !!result && (typeof result === 'object' || typeof result === 'function') && typeof result.then === 'function';\n      return isPromise\n        ? result.then((data: any) => classTransformer.instanceToPlain(data, params))\n        : classTransformer.instanceToPlain(result, params);\n    };\n  };\n}\n"]}