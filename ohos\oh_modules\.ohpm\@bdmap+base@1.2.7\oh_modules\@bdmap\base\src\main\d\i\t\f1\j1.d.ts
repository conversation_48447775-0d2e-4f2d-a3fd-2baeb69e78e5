import { Context } from '@kit.AbilityKit'; import { DeviceMetadata } from "../h1/o1";           export declare abstract class PreferencesManager<T> { protected abstract preferencesNamespace: string; protected abstract defaultValue: T; private _preferences; private _context; isInit: boolean;               init(context: Context): Promise<void>;             update(value: Partial<T>): Promise<void>;             fetch(context?: Context): Promise<T>;           updateField<K extends keyof T>(key: K, value: T[K]): Promise<void>;           fetchField<K extends keyof T>(key: K, context?: Context): Promise<T[K]>; }       export declare class IdentityPreferenceManager extends PreferencesManager<DeviceMetadata.IdentityInfo> { protected preferencesNamespace: string; protected defaultValue: DeviceMetadata.IdentityInfo; } export declare class CoordTypePreferenceManager extends PreferencesManager<DeviceMetadata.GlobalCoordType> { protected preferencesNamespace: string; protected defaultValue: DeviceMetadata.GlobalCoordType; } 