import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wuling_flutter_app/api/post_api.dart';
import 'package:wuling_flutter_app/models/index.dart';
import 'package:wuling_flutter_app/routes/jump_tool.dart';
import 'package:wuling_flutter_app/utils/manager/loading_manager.dart';
import 'package:wuling_flutter_app/widgets/common/custom_refresher.dart';
import 'package:wuling_flutter_app/widgets/common/image_view.dart';

import '../../api/community_api.dart';
import '../../constant/service_constant.dart';
import '../../models/post/news_items.dart';
import '../../utils/manager/log_manager.dart';
import '../community/topic_post_details_page.dart';
import '../community/img_text_video_post_detail_page.dart';

class NewsPage extends StatefulWidget {
  const NewsPage({Key? key}) : super(key: key);

  @override
  _NewsPageState createState() => _NewsPageState();
}

class _NewsPageState extends State<NewsPage> {
  final RefreshController _refreshController =
      RefreshController(initialRefresh: true);
  List<NewsItems> _newsList = [];
  @override
  void initState() {
    super.initState();
    _fetchNewsList();
  }

  Future _fetchNewsList() async {
    try {
      final newsItems = await communityAPI.getNewsList(145, 1, 30);

      setState(() {
        _newsList = newsItems;
      });

      LogManager().debug("获取到${_newsList.length}条新闻数据");
    } catch (e) {
      LogManager().debug("获取新闻列表失败: $e");
      LoadingManager.showError('获取资讯失败，请稍后重试');
    }
  }

  void _onRefresh() async {
    try {
      _fetchNewsList();
      setState(() {});
      _refreshController.refreshCompleted();
    } catch (e) {
      _refreshController.refreshFailed();
      LoadingManager.showError('获取资讯失败，请稍后重试');
    }
  }

  void _onLoading() async {
    // 这里可以添加加载更多数据的逻辑
    _refreshController.loadComplete();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '相关资讯',
          style: TextStyle(
            color: Colors.black,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.white,
        elevation: 0.5,
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios, color: Colors.black, size: 20),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: CustomSmartRefresher(
        controller: _refreshController,
        onRefresh: _onRefresh,
        onLoading: _onLoading,
        child: ListView.builder(
          itemCount: _newsList.length,
          itemBuilder: (context, index) {
            return NewsItem(
              newsItem: _newsList[index],
              onTap: () {
                try {
                  // 检查 postId 是否存在并尝试转为 int
                  final postId = _newsList[index].postId;
                  if (postId != null) {
                    // 先尝试转换为 int
                    final intId = int.tryParse(postId.toString());
                    if (intId != null) {
                      Navigator.of(context)
                          .push(MaterialPageRoute(builder: (context) {
                        if (_newsList[index].postTypeId == 0 ||
                            _newsList[index].postTypeId == 6 || _newsList[index].postTypeId == 5) {
                          return ImgTextVideoPostDetailPage(
                              postId: intId,
                              postTypeId: _newsList[index].postTypeId!.toInt());
                        } else {
                          return TopicPostDetailsPage(
                              postId: intId,
                              postTypeId: _newsList[index].postTypeId!.toInt());
                        }
                      }));
                    } else {
                      LoadingManager.showInfo('无效的资讯ID格式');
                    }
                  } else {
                    LoadingManager.showInfo('无法打开该资讯，ID无效');
                  }
                } catch (e) {
                  LogManager().debug("跳转错误: $e");
                  LoadingManager.showInfo('打开资讯失败');
                }
              },
            );
          },
        ),
      ),
    );
  }

  @override
  void dispose() {
    // TODO: implement dispose
    _refreshController?.dispose();
    super.dispose();
  }
}

class NewsItem extends StatelessWidget {
  final NewsItems newsItem;
  final VoidCallback onTap;

  const NewsItem({Key? key, required this.newsItem, required this.onTap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 图片作为背景，标题在左上角，浏览量评论量在右下角
            Stack(
              children: [
                // 背景图片
                ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: AspectRatio(
                    aspectRatio: 16 / 9,
                    child: ImageView(
                      newsItem.coverImage ?? newsItem.firstImg?.img ?? '',
                      fit: BoxFit.cover,
                    ),
                  ),
                ),

                // 标题 (在图片左上角，带半透明背景)
                Positioned(
                  left: 0,
                  top: 0,
                  right: 0,
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.black.withOpacity(0.7),
                          Colors.transparent,
                        ],
                      ),
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(12),
                        topRight: Radius.circular(12),
                      ),
                    ),
                    child: Text(
                      newsItem.title ?? '',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ),

                // 浏览量和评论量 (在图片右下角，带半透明背景)
                Positioned(
                  right: 0,
                  bottom: 0,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Colors.transparent,
                          Colors.black.withOpacity(0.7),
                        ],
                      ),
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(12),
                        bottomRight: Radius.circular(12),
                      ),
                    ),
                    child: Row(
                      children: [
                        const Icon(
                          Icons.remove_red_eye_outlined,
                          size: 14,
                          color: Colors.white,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${newsItem.postShowCount ?? 0}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.white,
                          ),
                        ),
                        const SizedBox(width: 10),
                        const Icon(
                          Icons.chat_bubble_outline,
                          size: 14,
                          color: Colors.white,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${newsItem.postCommentCount ?? 0}',
                          style: const TextStyle(
                            fontSize: 12,
                            color: Colors.white,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // 作者信息单独一行，在图片下方
            Row(
              children: [
                // 头像
                if (newsItem.author?.photo != null)
                  ClipRRect(
                    borderRadius: BorderRadius.circular(15),
                    child: SizedBox(
                      width: 30,
                      height: 30,
                      child: ImageView(
                        newsItem.author?.photo ?? '',
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                const SizedBox(width: 8),
                // 作者名称
                Text(
                  newsItem.author?.nickname ?? '',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Color(0xFF333333),
                  ),
                ),
              ],
            ),

            // 分隔线
            Container(
              margin: const EdgeInsets.only(top: 12),
              height: 1,
              color: const Color(0xFFF1F2F3),
            ),
          ],
        ),
      ),
    );
  }
}
