import { CameraProperties } from './CameraProperties';
import camera from "@ohos.multimedia.camera";
import { Rect } from '@ohos.application.AccessibilityExtensionAbility';
export declare class CameraPropertiesImpl implements CameraProperties {
    private cameraName;
    private cameraDevice;
    constructor(cameraName: string, cameraManager: camera.CameraManager);
    getSensorInfoActiveArraySize(): Rect;
    getControlAutoExposureCompensationRange(captureSession: camera.PhotoSession | camera.VideoSession): Array<number>;
    getCameraName(): string;
    getControlAutoExposureCompensationStep(): number;
    getControlAutoFocusAvailableModes(): number[];
    getControlMaxRegionsAutoExposure(): number;
    getControlMaxRegionsAutoFocus(): number;
    getDistortionCorrectionAvailableModes(): number[];
    getFlashInfoAvailable(): boolean;
    getLensFacing(): number;
    getLensInfoMinimumFocusDistance(): number;
    getScalerAvailableMaxDigitalZoom(): number;
    getSensorOrientation(): number;
    getHardwareLevel(): number;
    getAvailableNoiseReductionModes(): number[];
}
