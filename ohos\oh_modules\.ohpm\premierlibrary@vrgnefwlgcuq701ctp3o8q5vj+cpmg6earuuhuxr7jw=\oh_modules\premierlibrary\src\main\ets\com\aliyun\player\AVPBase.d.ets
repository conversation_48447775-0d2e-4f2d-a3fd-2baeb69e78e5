import { NativePlayerBase } from './nativeclass/NativePlayerBase';
import { IPlayer, OnCompletionListener, OnErrorListener, OnInfoListener, OnLoadingStatusListener, OnPreparedListener, OnRenderingStartListener, OnSeekCompleteListener, OnStateChangedListener, OnSubtitleDisplayListener, OnVideoRenderedListener, OnVideoSizeChangedListener, ScaleMode, OnAudioInterruptEventListener, AudioStatus, IPResolveType, OnSeiDataListener, OnSnapShotListener, OnTrackChangedListener, OnTrackReadyListener, PropertyKey, OnSubTrackReadyListener, OnStreamSwitchedListener, OnAVNotSyncStatusListener, AlphaRenderMode } from './IPlayer';
import { Context } from '@ohos.abilityAccessCtrl';
import { InfoBean } from './bean/InfoBean';
import { ErrorInfo } from './bean/ErrorInfo';
import { PlayerConfig } from './nativeclass/PlayerConfig';
import { CacheConfig } from './nativeclass/CacheConfig';
import { TrackInfo, TrackType } from './nativeclass/TrackInfo';
import { MediaInfo } from './nativeclass/MediaInfo';
export declare abstract class AVPBase implements IPlayer {
    private mCorePlayer?;
    private mContext?;
    private mTraceId?;
    private mOutMediaInfo;
    private mOutSubMediaInfo;
    private mOutOnPreparedListener;
    private mInnerOnPreparedListener;
    private mOutOnInfoListener;
    private mInnerOnInfoListener;
    private mOutOnRenderingStartListener;
    private mInnerOnRenderingStartListener;
    private mOutOnStateChangedListener;
    private mInnerOnStateChangedListener;
    private mOutOnVideoSizeChangedListener;
    private mInnerOnVideoSizeChangedListener;
    private mOutOnLoadingStatusListener;
    private mInnerOnLoadingStatusListener;
    private mOutOnSeekCompleteListener;
    private mInnerOnSeekCompleteListener;
    private mOutOnCompletionListener;
    private mInnerOnCompletionListener;
    private mOutOnVideoRenderedListener;
    private mInnerOnVideoRenderedListener;
    private mOutOnSubtitleDisplayListener;
    private mInnerOnSubtitleDisplayListener;
    private mOutOnErrorListener;
    private mInnerOnErrorListener;
    private mOutOnAudioInterruptEventListener;
    private mInnerOnAudioInterruptEventListener;
    private mOutOnTrackReadyListener;
    private mInnerOnTrackReadyListener;
    private mOutOnSubTrackReadyListener;
    private mInnerOnSubTrackReadyListener;
    private mOutOnStreamSwitchedListener;
    private mInnerOnStreamSwitchedListener;
    private mOutOnAVNotSyncStatusListener;
    private mInnerOnAVNotSyncStatusListener;
    private mOutOnTrackChangedListener;
    private mInnerOnTrackChangedListener;
    private mOutOnSnapShotListener;
    private mInnerOnSnapShotListener;
    private mOutOnSeiDataListener;
    private mInnerOnSeiDataListener;
    constructor(c17: Context, d17: string);
    getNativeContextAddr(): number;
    prepare(): void;
    prepareInner(): void;
    protected abstract createAlivcMediaPlayer(y45: Context): NativePlayerBase;
    protected getCorePlayer(): NativePlayerBase | undefined;
    start(): void;
    startInner(): void;
    pause(): void;
    pauseInner(): void;
    stop(): void;
    stopInner(): void;
    setAutoPlay(a17: boolean): void;
    setSurfaceId(z16: string): void;
    setSpeed(y16: number): void;
    setVolume(x16: number): void;
    getVolume(): number;
    seekTo(v16: number, w16: number): void;
    setStartTime(t16: number, u16: number): void;
    getDuration(): number;
    getPlayedDuration(): number;
    getCurrentPosition(): number;
    getBufferedPosition(): number;
    getPlayerStatus(): number;
    enableHardwareDecoder(s16: boolean): void;
    release(): void;
    setGlobalTime(q16: string): void;
    releaseInner(): void;
    releaseAsync(): void;
    setMute(n16: boolean): void;
    isMuted(): boolean;
    setScaleMode(m16: ScaleMode): void;
    getScaleMode(): number;
    setLoop(l16: boolean): void;
    isLoop(): boolean;
    getVideoWidth(): number;
    getVideoHeight(): number;
    getVideoRotation(): number;
    reload(): void;
    setRotateMode(k16: number): void;
    getRotateMode(): number;
    setMirrorMode(j16: number): void;
    getMirrorMode(): number;
    setAlphaRenderMode(i16: AlphaRenderMode): void;
    getAlphaRenderMode(): number;
    setVideoBackgroundColor(h16: number): void;
    getSpeed(): number;
    isAutoPlay(): boolean;
    setConfig(g16: PlayerConfig): void;
    getConfig(): PlayerConfig | undefined;
    setOption(d16: string, e16: string): void;
    setOptionNum(a16: number, b16: number): void;
    getOption(z15: string): string | number | undefined;
    selectTrack(y15: number): void;
    switchStream(x15: string): void;
    getMediaInfo(): MediaInfo | null;
    getSubMediaInfo(): MediaInfo | null;
    currentTrack(w15: TrackType): TrackInfo | null;
    addExtSubtitle(v15: string): void;
    selectExtSubtitle(t15: number, u15: boolean): void;
    setStreamDelay(r15: number, s15: number): void;
    setMaxAccurateSeekDelta(q15: number): void;
    setCacheConfig(p15: CacheConfig): void;
    setIPResolveType(o15: IPResolveType): void;
    setFastStart(n15: boolean): void;
    snapShot(): void;
    clearScreen(): void;
    getCacheFilePathByUrl(m15: string): string;
    getCacheFilePathByVid(i15: string, j15: string, k15: string, l15: number): string;
    getPropertyString(h15: PropertyKey): string;
    setDefaultBandWidth(g15: number): void;
    sendCustomEvent(f15: string): void;
    setVideoTag(e15: number[]): void;
    setUserData(d15: string): void;
    setTraceId(c15: string): void;
    getUserData(): string;
    private bindListener;
    setOnPreparedListener(b15: OnPreparedListener): void;
    setOnInfoListener(a15: OnInfoListener): void;
    setOnRenderingStartListener(z14: OnRenderingStartListener): void;
    setOnStateChangedListener(y14: OnStateChangedListener): void;
    setOnCompletionListener(x14: OnCompletionListener): void;
    setOnAVNotSyncStatusListener(w14: OnAVNotSyncStatusListener): void;
    setOnStreamSwitchedListener(v14: OnStreamSwitchedListener): void;
    setOnLoadingStatusListener(u14: OnLoadingStatusListener): void;
    setOnErrorListener(t14: OnErrorListener): void;
    setOnVideoSizeChangedListener(s14: OnVideoSizeChangedListener): void;
    setOnSeekCompleteListener(r14: OnSeekCompleteListener): void;
    setOnSubtitleDisplayListener(q14: OnSubtitleDisplayListener): void;
    setOnVideoRenderedListener(p14: OnVideoRenderedListener): void;
    setOnAudioInterruptEventListener(o14: OnAudioInterruptEventListener): void;
    setOnTrackReadyListener(n14: OnTrackReadyListener): void;
    setOnSubTrackReadyListener(m14: OnSubTrackReadyListener): void;
    setOnTrackChangedListener(l14: OnTrackChangedListener): void;
    setOnSnapShotListener(k14: OnSnapShotListener): void;
    setOnSeiDataListener(j14: OnSeiDataListener): void;
    onPrepared(): void;
    onInfo(i14: InfoBean): void;
    onRenderingStart(): void;
    onStateChanged(h14: number): void;
    onVideoSizeChanged(f14: number, g14: number): void;
    onLoadingBegin(): void;
    onLoadingProgress(d14: number, e14: number): void;
    onLoadingEnd(): void;
    onSeekEnd(): void;
    onCompletion(): void;
    onVideoRendered(b14: number, c14: number): void;
    onSubtitleShow(y13: number, z13: number, a14: string): void;
    onSubtitleExtAdded(w13: number, x13: string): void;
    onSubtitleHide(u13: number, v13: number): void;
    onSubtitleHeader(s13: number, t13: string): void;
    onError(r13: ErrorInfo): void;
    onAudioInterruptEvent(q13: AudioStatus): void;
    onTrackReady(p13: MediaInfo): void;
    onSubTrackReady(o13: MediaInfo): void;
    onSwitchedSuccess(n13: string): void;
    onSwitchedFail(l13: string, m13: ErrorInfo): void;
    onAVNotSyncStart(k13: number): void;
    onAVNotSyncEnd(): void;
    onChangedSuccess(j13: TrackInfo): void;
    onSnapShot(g13: ArrayBuffer, h13: number, i13: number): void;
    onSeiData(e13: number, f13: Uint8Array): void;
}
