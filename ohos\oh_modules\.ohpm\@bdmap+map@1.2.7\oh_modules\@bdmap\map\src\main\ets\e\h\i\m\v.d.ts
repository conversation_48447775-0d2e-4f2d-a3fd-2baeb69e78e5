          import { FontType, FontAlign } from "../../util/b1/c1"; import Bundle from "../o/i1"; import type { ColorString, ILabelOption, Nullable } from "../../g1/a2"; import BaseMarker from "./r4";             export default class Label extends BaseMarker { private mText; private mBgColor; private mFontColor; private mBorderColor; private mFontSize; private mBorderSize; private mFontType; private bmTextMarker; private bmTextStyle;   static NORMAL: FontType;   static BOLD: FontType;   static ITALIC: FontType;   static BOLD_ITALIC: FontType;                                                                   constructor(o38?: ILabelOption);       init(): void;         getText(): string;         text(text: string): this;         setText(text: string): void;         getFontColor(): any;         fontcolor(color: ColorString): this;         setFontColor(color: ColorString): void;         getBorderColor(): any;         borderColor(color: ColorString): this;         setBorderColor(color: ColorString): void;         borderSize(width: number): this;         setBorderSize(width: number): void;         getBorderSize(): number;           getBgColor(): any;         bgcolor(color: ColorString): this;           setBgColor(color: ColorString): void;         getFontSize(): number;         fontSize(size: number): this;         setFontSize(size: number): void;         getFontType(): FontType;         fontType(type: FontType): this;         setFontType(type: FontType): void;           getAlign(): void;           align(m38: FontAlign, n38: FontAlign): this;           setAlign(k38: FontAlign, l38: FontAlign): void; get typeName(): string;       dataFormat(): Nullable<Bundle>;       toBundle(): Promise<Bundle>;       toString(): string; } 