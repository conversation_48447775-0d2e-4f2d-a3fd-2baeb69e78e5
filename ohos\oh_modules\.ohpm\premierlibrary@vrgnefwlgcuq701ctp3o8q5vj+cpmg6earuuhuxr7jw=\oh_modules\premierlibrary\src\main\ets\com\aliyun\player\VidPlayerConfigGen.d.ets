export declare class VidPlayerConfigGen {
    private configMap;
    constructor();
    /**
     * 添加其他config配置
     *
     * @param key   键
     * @param value 值
     */
    /****
     * Add other config parameters.
     *
     * @param key   Key.
     * @param value Value.
     */
    addPlayerConfigNumber(f42: string, g42: number): void;
    /**
     * 添加其他config配置
     *
     * @param key   键
     * @param value 值
     */
    /****
     * Add other config parameters.
     *
     * @param key   Key.
     * @param value Value.
     */
    addPlayerConfigString(d42: string, e42: string): void;
    /**
     * 设置试看时间
     *
     * @param previewTime 秒
     */
    /****
     * Set the preview duration.
     *
     * @param previewTime Unit: seconds.
     */
    setPreviewTime(c42: number): void;
    /**
     * 设置服务器返回的视频加密类型.如果设置了对应的类型，那么将会只返回对应类型的视频。比如：设置了Unencrypted。那么只会返回不加密的视频。
     * 如果没有设置，默认将会返回加密的，每个清晰度返回一路流
     *
     * @param type 视频加密类型。见{@linkplain EncryptType}
     */
    /****
     * Set the encryption type for streams returned by the server. If an encryption type is specified, then only streams use the specified encryption type are returned. For example, if the encryption type is set to Unencrypted, then only unencrypted streams are returned.
     * If no encryption type is specified, an encrypted stream is returned for each definition.
     *
     * @param type The specified encryption type. See {@linkplain EncryptType}.
     */
    setEncryptType(b42: EncryptType): void;
    /**
     * 设置Mts的hls UriToken
     *
     * @param token
     */
    /****
     * Set the HLS UriToken of MTS.
     *
     * @param token
     */
    setMtsHlsUriToken(a42: string): void;
    /**
     * 获取config字符串
     *
     * @return
     */
    /****
     * Query config parameters.
     *
     * @return
     */
    genConfig(): string;
}
export declare enum EncryptType {
    /**
     * 不加密
     */
    /****
     * Do not encrypt.
     */
    Unencrypted = 0,
    /**
     * 私有加密
     */
    /****
     * Alibaba Cloud ApsaraVideo for VOD encryption.
     */
    AliyunVodEncryption = 1,
    /**
     * 标准加密
     */
    /****
     * Standard encryption.
     */
    HLSEncryption = 2
}
