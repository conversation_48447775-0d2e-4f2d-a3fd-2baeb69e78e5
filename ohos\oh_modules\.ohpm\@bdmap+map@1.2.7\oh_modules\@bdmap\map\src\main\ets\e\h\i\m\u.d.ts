import type { LatLng } from '@bdmap/base'; import Overlay from "./m"; import type Marker from "./w"; import ImageEntity from "../o/s"; import type { IInfoWindow, Nullable } from "../../g1/a2"; import type OverlayMgr from "./j2"; import Bundle from "../o/i1"; import type { ImageOverlayData } from "../../g1/i1";             export default class InfoWindow extends Overlay { private mPosition; private mContent; private mYOffset; private mAnchorX; private mAnchorY; private mIsFlat; private mRotate; private mIsPerspective; marker: Marker;                                                                                   constructor(e38: IInfoWindow);       init(): void;         position(position: LatLng): this;         getPosition(): LatLng;         setPosition(position: LatLng): void;         content(content: ImageEntity): this;         getContent(): ImageEntity;         setContent(content: ImageEntity): void;             anchor(c38: number, d38: number): this;         getAnchor(): { anchorX: number; anchorY: number; };             setAnchor(a38: number, b38: number): void;         yOffset(yOffset: number): this;         getYOffset(): number;         setYOffset(yOffset: number): void;         flat(flat: boolean): this;         getFlat(): boolean;         setFlat(flat: boolean): void;         rotate(rotate: number): this;         getRotate(): number;         setRotate(rotate: number): void;         perspective(perspective: boolean): this;         getPerspective(): boolean;         setPerspective(perspective: boolean): void;         setMarker(marker: Marker): void;       toString(): string;       dataFormat(z37: Array<ImageOverlayData>): Nullable<Bundle>;           toBundle(u37: OverlayMgr): Promise<Bundle>; } 