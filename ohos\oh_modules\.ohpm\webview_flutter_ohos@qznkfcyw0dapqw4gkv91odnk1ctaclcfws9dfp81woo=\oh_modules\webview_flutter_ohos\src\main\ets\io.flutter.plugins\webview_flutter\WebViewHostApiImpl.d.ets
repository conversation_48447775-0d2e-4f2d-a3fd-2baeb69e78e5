import PlatformView, { Params } from '@ohos/flutter_ohos/src/main/ets/plugin/platform/PlatformView';
import { Result, WebViewHostApi, WebViewPoint } from './GeneratedOhosWebView';
import web_webview from '@ohos.web.webview';
import { InstanceManager } from './InstanceManager';
import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import common from '@ohos.app.ability.common';
import { WebChromeClient } from './WebChromeClientHostApiImpl';
import { WebViewClient } from './WebViewClientHostApiImpl';
import { WebSettings } from './WebSettingsHostApiImpl';
import { DownloadListener } from './DownloadListenerFlutterApiImpl';
import { WebViewFlutterApiImpl } from './WebViewFlutterApiImpl.ets';
export declare class WebViewHostApiImpl extends WebViewHostApi {
    private instanceManager;
    private webViewProxy;
    private binaryMessenger;
    private context;
    private api?;
    constructor(instanceManager: InstanceManager, webViewProxy: WebViewProxy, binaryMessenger: BinaryMessenger, context: common.Context);
    setContext(context: common.Context): void;
    create(instanceId: number): void;
    loadData(instanceId: number, data: string, mimeType: string, encoding: string): void;
    loadDataWithBaseUrl(instanceId: number, baseUrl: string, data: string, mimeType: string, encoding: string, historyUrl: string): Promise<void>;
    loadUrl(instanceId: number, url: string, headers: Map<string, string>): Promise<void>;
    toWebHeaders(headers: Map<string, string>): Array<web_webview.WebHeader>;
    postUrl(instanceId: number, url: string, data: Uint8Array): Promise<void>;
    getUrl(instanceId: number): string;
    canGoBack(instanceId: number): boolean;
    canGoForward(instanceId: number): boolean;
    goBack(instanceId: number): void;
    goForward(instanceId: number): void;
    reload(instanceId: number): void;
    clearCache(instanceId: number, includeDiskFiles: boolean): void;
    evaluateJavascript(instanceId: number, javascriptString: string, result: Result<string>): void;
    getTitle(instanceId: number): string;
    scrollTo(instanceId: number, x: number, y: number): void;
    scrollBy(instanceId: number, x: number, y: number): void;
    getScrollX(instanceId: number): number;
    getScrollY(instanceId: number): number;
    getScrollPosition(instanceId: number): WebViewPoint;
    setWebContentsDebuggingEnabled(enabled: boolean): void;
    setWebViewClient(instanceId: number, webViewClientInstanceId: number): void;
    addJavaScriptChannel(instanceId: number, javaScriptChannelInstanceId: number): Promise<void>;
    removeJavaScriptChannel(instanceId: number, javaScriptChannelInstanceId: number): void;
    setDownloadListener(instanceId: number, listenerInstanceId: number): void;
    setWebChromeClient(instanceId: number, clientInstanceId: number): void;
    getInstanceManager(): InstanceManager;
    onScrollChangedImpl(left: number, top: number, oldLeft: number, oldTop: number): void;
    setApi(api: WebViewFlutterApiImpl): void;
}
export declare class WebViewPlatformView extends PlatformView {
    private WebCookieManager;
    private controller;
    private tempUrl;
    private downloadListener;
    private webChromeClient;
    private webViewClint;
    private webSettings;
    private dialogController;
    private scrollX;
    private scrollY;
    private returnValueForShouldOverrideUrlLoading;
    private controllerAttached;
    private onDisposeCallback?;
    private rotation;
    onDownloadStart: (event: ESObject) => void;
    onPageBegin: (event: ESObject) => void;
    onPageEnd: (event: ESObject) => void;
    onErrorReceive: (event: ESObject) => void;
    onOverrideUrlLoading: (request: WebResourceRequest) => boolean;
    onRefreshAccessedHistory: (event: ESObject) => void;
    onProgressChange: (event: ESObject) => void;
    onGeolocationShow: (event: ESObject) => void;
    onGeolocationHide: () => void;
    onShowFileSelector: (event: ESObject) => boolean;
    onPermissionRequest: (event: ESObject) => void;
    onConsoleMessage: (event: ESObject) => boolean;
    onWindowNew: (event: ESObject) => void;
    onWindowExit: () => void;
    onScroll: (event: ESObject) => void;
    onControllerAttached: () => void;
    setOnDisposeCallback(callback: () => void): void;
    onJsAlert: (event: ESObject) => boolean;
    onJsConfirm: (event: ESObject) => boolean;
    onJsPrompt: (event: ESObject) => boolean;
    onFullScreenEnter: (event: ESObject) => void;
    onFullScreenExit: () => void;
    getType(): string;
    getView(): WrappedBuilder<[Params]>;
    dispose(): void;
    getController(): web_webview.WebviewController;
    setUrl(url: string): void;
    getUrl(): string;
    getWebCookieManager(): web_webview.WebCookieManager;
    setDownloadListener(listener: DownloadListener): void;
    setWebViewClient(webViewClient: WebViewClient): void;
    setWebChromeClient(webChromeClient: WebChromeClient): void;
    setDialogController(dialogController: CustomDialogController): void;
    getDialogController(): CustomDialogController | null;
    getWebSettings(): WebSettings;
    getScrollX(): number;
    getScrollY(): number;
    setReturnValueForShouldOverrideUrlLoading(value: boolean): void;
    waitControllerAttached(): Promise<void>;
    checkControllerAttached(): Promise<void>;
}
export declare class WebViewProxy {
    createWebView(): WebViewPlatformView;
    setWebContentsDebuggingEnabled(enabled: boolean): void;
}
