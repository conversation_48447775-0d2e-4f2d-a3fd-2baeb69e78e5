// @keepTs
// @ts-nocheck
import { ConversationType } from '@rongcloud/imlib';
import { BaseMessageProcessor } from './BaseProcessor';
/**
 * 会话内消息处理器工厂类
 * Created on 2024/07/15
 * <AUTHOR>
 */
export declare class MessageProcessorFactory {
    private processorMap;
    private defaultProcessor;
    constructor();
    updateProcessor(r263: ConversationType, s263: BaseMessageProcessor): void;
    getProcessor(q263: ConversationType): BaseMessageProcessor;
    getDefaultProcessor(): BaseMessageProcessor;
    static getInstance(): MessageProcessorFactory;
}
