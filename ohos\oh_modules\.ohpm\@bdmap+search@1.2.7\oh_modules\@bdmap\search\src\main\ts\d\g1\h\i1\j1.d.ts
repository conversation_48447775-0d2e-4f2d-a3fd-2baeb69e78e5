import { PoiFilter } from "../p1";
import { LanguageType } from "../../../base/x2";
import { LatLngBounds } from '@bdmap/base';
/**
 * poi城市内检索参数
 */
export declare class PoiBoundSearchOption {
    /**
     * 检索矩形区域
     * 必选参数
     */
    private _bound;
    set bound(value: LatLngBounds);
    get bound(): LatLngBounds;
    /**
     * 是否召回行政区域编码,默认是
     */
    private _isExtendAdcode;
    set isExtendAdcode(value: boolean);
    get isExtendAdcode(): boolean;
    /**
     * 检索关键字。必须参数
     * 支持多个关键字并集检索，不同关键字间以$符号分隔，最多支持10个关键字检索。如:”银行$酒店”
     */
    private _keyword;
    set keyword(value: string);
    get keyword(): string;
    /**
     * 分页页码
     * 默认为0,0代表第一页，1代表第二页，以此类推
     */
    private _pageNum;
    set pageNum(value: number);
    get pageNum(): number;
    /**
     * 单次返回POI数量，默认为10条记录，最大返回20条。
     * 多关键字检索时，返回的记录数为关键字个数*mPageNum
     */
    private _pageCapacity;
    set pageCapacity(value: number);
    get pageCapacity(): number;
    /**
     * 检索分类，
     * 多个分类以","分割
     * 默认为空
     */
    private _tag;
    set tag(value: string | null);
    get tag(): string | null;
    /**
     * 检索结果详细程度。
     * 取值为1 或空，则返回基本信息；取值为2，返回检索POI详细信息
     * 默认为1
     */
    private _scope;
    set scope(value: number);
    get scope(): number;
    /**
     * 检索过滤条件。
     * 当scope取值为2时，可以设置filter进行排序
     */
    private _mPoiFilter;
    set poiFilter(value: PoiFilter | null);
    get mPoiFilter(): PoiFilter | null;
    /**
     * 检索语言类型
     * 默认中文
     */
    private _mLanguageType;
    set languageType(value: LanguageType);
    get languageType(): LanguageType;
    constructor(params: PoiBoundSearchOptionParams);
}
export interface PoiBoundSearchOptionParams {
    /**
     * 检索矩形区域
     * 必选参数
     */
    bound: LatLngBounds;
    /**
     * 检索关键字。必须参数
     * 支持多个关键字并集检索，不同关键字间以$符号分隔，最多支持10个关键字检索。如:”银行$酒店”
     */
    keyword: string;
    /**
     * 分页页码
     * 默认为0,0代表第一页，1代表第二页，以此类推
     */
    pageNum?: number;
    /**
     * 单次返回POI数量，默认为10条记录，最大返回20条。
     * 多关键字检索时，返回的记录数为关键字个数*mPageNum
     */
    pageCapacity?: number;
    /**
     * 检索分类，
     * 多个分类以","分割
     * 默认为空
     */
    tag?: string;
    /**
     * 检索结果详细程度。
     * 取值为1 或空，则返回基本信息；取值为2，返回检索POI详细信息
     * 默认为1
     */
    scope?: number;
    /**
     * 检索过滤条件。
     * 当scope取值为2时，可以设置filter进行排序
     */
    poiFilter?: PoiFilter | null;
    /**
     * 检索语言类型
     * 默认中文
     */
    languageType?: LanguageType;
    /**
     * 是否召回行政区域编码,默认是
     */
    isExtendAdcode?: boolean;
}
