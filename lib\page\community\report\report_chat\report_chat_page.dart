import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wuling_flutter_app/widgets/common/custom_refresher.dart';

import '../../../../api/report_api/report_api.dart';
import '../../../../constant/storage.dart';
import '../../../../platform/im_view/IMView.dart';
import '../../../../utils/manager/log_manager.dart';
import '../../../../utils/sp_util.dart';

class ReportChatPage extends StatefulWidget {
  final Map<String,dynamic> args;
  const ReportChatPage({super.key, required this.args});

  @override
  State<ReportChatPage> createState() => _ReportChatPageState();
}

class _ReportChatPageState extends State<ReportChatPage> {
  List<dynamic> dataSource = [];
  final RefreshController _refreshController =
  RefreshController(initialRefresh: false);
  int _pageNo = 1;
  Map<String,Future Function(dynamic message)> methodCallbacks = {};
  @override
  void initState() {
    LogManager().debug('${widget.args}');
    _onRefresh();
    super.initState();
  }

  void _onRefresh() async {
    // monitor network fetch
    _pageNo = 1;
    await Future.delayed(Duration(milliseconds: 1000));
    _refreshController.refreshCompleted();
  }

  void _onLoading() async {
    _pageNo += 1;
    await Future.delayed(Duration(milliseconds: 1000));
    _refreshController.loadComplete();
  }

  Future<String> getImToken({bool isRefresh = false})async{
    String token = "";
    if(isRefresh != true){
      token = SpUtil().getString(RR_IM_TOKEN);
    }
    if(token == ""){
      token = await reportApi.imToken();
      await SpUtil().setString(RR_IM_TOKEN, token);
    }
    return token;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text("页面"),),
      body: SmartRefresher(
        enablePullDown: true,
        enablePullUp: true,
        controller: _refreshController,
        onRefresh: _onRefresh,
        onLoading: _onLoading,
        header: AnimatedRefreshHeader(),
        footer: AnimatedRefreshFooter(),
        child:Container(
          child: Center(
            child: FutureBuilder<String>(
              future: getImToken(), // 异步操作的Future对象
              builder: (BuildContext context, AsyncSnapshot<String> snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  // 当Future还未完成时，显示加载中的UI
                  return CircularProgressIndicator();
                } else if (snapshot.hasError) {
                  // 当Future发生错误时，显示错误提示的UI
                  return Text('Error: ${snapshot.error}');
                } else {
                  // 当Future成功完成时，显示数据
                  return IMView(token: snapshot.data ?? "", getToken: (isRefresh)async {
                    String token = await getImToken(isRefresh:isRefresh);
                    LogManager().debug(token);
                    return token;
                  }, callBacks: methodCallbacks, type: 'chat',);
                }
              },
            ),
          ),
        )
      ),
    );
  }
}
