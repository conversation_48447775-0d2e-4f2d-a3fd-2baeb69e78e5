import UIAbility from '@ohos.app.ability.UIAbility';
import camera from '@ohos.multimedia.camera';
import image from '@ohos.multimedia.image';
import { EventChannel } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import { SurfaceTextureEntry } from '@ohos/flutter_ohos/src/main/ets/view/TextureRegistry';
import { CameraFeatureFactory } from './features/CameraFeatureFactory';
import { DartMessenger } from './DartMessenger';
import { CameraProperties } from './CameraProperties';
import { ResolutionPreset } from './types/ResolutionPreset';
import { CameraCaptureStateListener } from './CameraCaptureCallback';
import { CameraFeatures } from './features/CameraFeatures';
import { ImageStreamReader } from './media/ImageStreamReader';
import { DeviceOrientationManager } from './features/sensororientation/DeviceOrientationManager';
import { Point } from './features/Point';
import { DeviceOrientation } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/systemchannels/PlatformChannel';
export declare class Camera implements CameraCaptureStateListener {
    private static TAG;
    cameraFeatures: CameraFeatures;
    private imageFormatGroup;
    private videoRenderer;
    private flutterTexture;
    private resolutionPreset;
    private enableAudio;
    private applicationContext;
    dartMessenger: DartMessenger;
    private cameraProperties;
    private cameraFeatureFactory;
    private ability;
    private cameraCaptureCallback;
    private cameraDevice;
    private cameraInput;
    private imageReceiver;
    imageStreamReader: ImageStreamReader | null;
    private previewOutput;
    private pausedPreview;
    private captureTimeouts;
    private captureProps;
    private cameraManager;
    private avRecorder;
    private videoOutput;
    private recordingVideo;
    private captureFile;
    private cameraOutputCapability;
    private initialCameraFacing;
    private photoSession;
    private videoSession;
    private cameras;
    private photoOutPut;
    private fd;
    private isVideoMode;
    private aVRecorderProfile;
    private phAccessHelper;
    private videoPath;
    private videoId;
    constructor(ability: UIAbility, flutterTexture: SurfaceTextureEntry, cameraFeatureFactory: CameraFeatureFactory, dartMessenger: DartMessenger, cameraProperties: CameraProperties, resolutionPreset: ResolutionPreset, enableAudio: boolean);
    onConverged(): void;
    onPrecapture(): void;
    getCurSession(): camera.PhotoSession | camera.VideoSession;
    open(imageFormatGroup: string | null): Promise<void>;
    getDeviceOrientationManager(): DeviceOrientationManager;
    setFocusPoint(result: MethodResult, point: Point): void;
    getFocusMode(result: MethodResult): void;
    setFocusMode(result: MethodResult, newMode: camera.FocusMode): void;
    setExposureOffset(result: MethodResult, offset: number): void;
    getMaxZoomLevel(): number;
    getMinZoomLevel(): number;
    setZoomLevel(result: MethodResult, zoom: number): void;
    lockCaptureOrientation(orientation: DeviceOrientation): void;
    unlockCaptureOrientation(): void;
    pausePreview(result: MethodResult): Promise<void>;
    resumePreview(result: MethodResult): Promise<void>;
    setExposurePoint(result: MethodResult, point: Point): void;
    setExposureMode(result: MethodResult, newMode: camera.ExposureMode): void;
    setFlashMode(result: MethodResult, newMode: camera.FlashMode): void;
    getMinExposureOffset(): number;
    getMaxExposureOffset(): number;
    getExposureOffsetStepSize(): number;
    takePicture(result: MethodResult): Promise<void>;
    savePicture(buffer: ArrayBuffer, img: image.Image): Promise<string>;
    /** Start capturing a picture, doing autofocus first. */
    private runPictureAutoFocus;
    lockAutoFocus(): Promise<void>;
    unlockAutoFocus(): void;
    startVideoRecording(result: MethodResult, imageStreamChannel: EventChannel): void;
    stopVideoRecording(result: MethodResult): Promise<void>;
    startPreview(): void;
    startPreviewWithVideo(): Promise<void>;
    startPreviewWithPhoto(): Promise<void>;
    pauseVideoRecording(result: MethodResult): Promise<void>;
    resumeVideoRecording(result: MethodResult): Promise<void>;
    startPreviewWithImageStream(imageStreamChannel: EventChannel): void;
    startImageStream(): Promise<void>;
    prepareMediaRecorder(cameraManager: camera.CameraManager, cameraOutputCapability: camera.CameraOutputCapability, url: string): Promise<void>;
    startCapture(record: boolean, stream: boolean): Promise<void>;
    prepareRecording(result: MethodResult): Promise<void>;
    private setStreamHandler;
    private setImageStreamImageAvailableListener;
    close(): void;
    private stopAndReleaseCamera;
    stopSession(): Promise<void>;
    setDescriptionWhileRecording(result: MethodResult, properties: CameraProperties): void;
    dispose(): void;
    private releaseSession;
    private releaseCameraInput;
    private releasePreviewOutput;
    private releasePhotoOutput;
    private releaseVideoOutput;
    releaseCamera(): Promise<void>;
}
