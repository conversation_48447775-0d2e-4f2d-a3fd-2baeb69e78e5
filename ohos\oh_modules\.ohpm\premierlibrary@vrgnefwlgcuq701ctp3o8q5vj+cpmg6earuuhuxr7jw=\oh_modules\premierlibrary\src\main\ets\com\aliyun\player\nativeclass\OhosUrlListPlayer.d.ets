import { ListPlayerBase } from './ListPlayerBase';
import { Context } from '@ohos.abilityAccessCtrl';
export declare class OhosUrlListPlayer extends ListPlayerBase {
    constructor(b31: Context, c31: number, d31: number, e31: boolean);
    addUrl(z30: string, a31: string): void;
    getPreRenderPlayerIndex(): number;
    getCurrentPlayerIndex(): number;
    moveToNext(x30: boolean): boolean;
    moveToPrev(): boolean;
    moveTo(u30: string): boolean;
}
