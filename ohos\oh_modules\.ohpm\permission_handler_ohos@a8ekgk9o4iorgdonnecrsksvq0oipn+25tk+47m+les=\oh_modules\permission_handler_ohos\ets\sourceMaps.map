{"permission_handler_ohos|permission_handler_ohos|1.0.0|build/default/generated/profile/default/ModuleInfo.js": {"version": 3, "file": "ModuleInfo.ts", "sources": ["ohos/build/default/generated/profile/default/ModuleInfo.ts"], "names": [], "mappings": "AACA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EAAE,CAAA,CAAA;AACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EAAE,CAAA,CAAA;", "entry-package-info": "permission_handler_ohos|1.0.0"}, "permission_handler_ohos|permission_handler_ohos|1.0.0|index.ts": {"version": 3, "file": "index.ets", "sourceRoot": "", "sources": ["ohos/index.ets"], "names": [], "mappings": "OAeO,uBAAuB;AAC9B,eAAe,uBAAuB,CAAC", "entry-package-info": "permission_handler_ohos|1.0.0"}, "permission_handler_ohos|permission_handler_ohos|1.0.0|src/main/ets/com/baseflow/permissionhandler/AppSettingManager.ts": {"version": 3, "file": "AppSettingManager.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/com/baseflow/permissionhandler/AppSettingManager.ets"], "names": [], "mappings": "YAeO,SAAS;OACT,aAAa;OACb,GAAG;cACD,aAAa,QAAQ,iBAAiB;OACxC,mBAAmB;YACnB,IAAI;AAEX,MAAM,OAAO,iBAAiB;IAC5B,eAAe,CAAC,OAAO,EAAE,SAAS,GAAG,IAAI,EAAE,eAAe,EAAE,8BAA8B,EAAE,aAAa,EAAE,aAAa,GAAG,IAAI;QAC7H,IAAG,OAAO,IAAI,IAAI,EAAE;YAClB,GAAG,CAAC,CAAC,CAAC,mBAAmB,CAAC,OAAO,EAAE,yBAAyB,CAAC,CAAC;YAC9D,aAAa,EAAE,OAAO,CAAC,sCAAsC,EAAE,iCAAiC,CAAC,CAAC;YAClG,OAAO;SACR;QACD,aAAa,CAAC,oBAAoB,CAAC,aAAa,CAAC,UAAU,CAAC,gCAAgC,CAAC,CAAC,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE;YAChH,IAAI,UAAU,IAAI,IAAI,EAAE;gBACtB,eAAe,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;gBACjC,OAAO;aACR;YACD,IAAI,IAAI,EAAE,IAAI,GAAG;gBACf,WAAW,EAAE,sCAAsC;gBACnD,UAAU,EAAE,0BAA0B;gBACtC,GAAG,EAAE,wBAAwB;gBAC7B,UAAU,EAAE;oBACV,UAAU,EAAG,UAAU,CAAC,IAAI;iBAC7B;aACF,CAAC;YACF,IAAI;gBACF,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;oBAC3C,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;gBAClC,CAAC,CAAC,CAAC;aACJ;YAAC,OAAO,KAAK,EAAE;gBACd,GAAG,CAAC,CAAC,CAAC,mBAAmB,CAAC,OAAO,EAAE,yBAAyB,CAAC,CAAC;gBAC9D,eAAe,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;aACnC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAED,MAAM,WAAW,8BAA8B;IAC7C,SAAS,CAAC,6BAA6B,EAAG,OAAO,GAAG,IAAI,CAAC;CAC1D", "entry-package-info": "permission_handler_ohos|1.0.0"}, "permission_handler_ohos|permission_handler_ohos|1.0.0|src/main/ets/com/baseflow/permissionhandler/ErrorCallback.ts": {"version": 3, "file": "ErrorCallback.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/com/baseflow/permissionhandler/ErrorCallback.ets"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;EAaE;AAEF,MAAM,WAAW,aAAa;IAC5B,OAAO,CAAC,SAAS,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,GAAE,IAAI,CAAC;CAC3D", "entry-package-info": "permission_handler_ohos|1.0.0"}, "permission_handler_ohos|permission_handler_ohos|1.0.0|src/main/ets/com/baseflow/permissionhandler/MethodCallHandlerImpl.ts": {"version": 3, "file": "MethodCallHandlerImpl.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/com/baseflow/permissionhandler/MethodCallHandlerImpl.ets"], "names": [], "mappings": "cAeS,iBAAiB,EAAE,YAAY;YACjC,MAAM;YACN,UAAU;cACR,iBAAiB,QAAQ,qBAAqB;cAC9C,iBAAiB,QAAQ,qBAAqB;cAC9C,cAAc,QAAQ,kBAAkB;YAC1C,SAAS;YACT,SAAS;AAEhB,MAAM,OAAO,qBAAsB,YAAW,iBAAiB;IAC7D,OAAO,CAAC,kBAAkB,EAAE,MAAM,CAAC,OAAO,CAAC;IAC3C,OAAO,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;IAC7C,OAAO,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;IAC7C,OAAO,CAAC,cAAc,EAAE,cAAc,CAAC;IACvC,OAAO,CAAC,OAAO,EAAE,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC;IAEzC,MAAM,aAAa,kBAAkB,EAAE,MAAM,CAAC,OAAO,EAAE,iBAAiB,EAAE,iBAAiB,EACzF,iBAAiB,EAAE,iBAAiB,EAAE,cAAc,EAAE,cAAc;QACpE,IAAI,CAAC,kBAAkB,GAAG,kBAAkB,CAAC;QAC7C,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;QAC3C,IAAI,CAAC,iBAAiB,GAAG,iBAAiB,CAAC;QAC3C,IAAI,CAAC,cAAc,GAAG,cAAc,CAAC;IACvC,CAAC;IAED,MAAM,CAAC,UAAU,CAAC,OAAO,EAAE,SAAS,GAAG,IAAI,GAAG,IAAI;QAChD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED,YAAY,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,IAAI;QACxD,QAAQ,IAAI,CAAC,MAAM,EAAE;YACnB,KAAK,oBAAoB;gBACvB,IAAI,UAAU,EAAE,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;gBACnC,IAAI,CAAC,cAAc,CAAC,kBAAkB,CAAC,UAAU,EAAE,IAAI,CAAC,kBAAkB,EAAE;oBAC1E,SAAS,EAAE,CAAC,aAAa,EAAE,MAAM,EAAE,EAAE;wBACnC,MAAM,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;oBAChC,CAAC;iBACF,OAAY,EAAE;oBACb,OAAO,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,EAAE;wBACvD,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;oBAClD,CAAC;iBACF,OAAY,CAAC,CAAC;gBACjB,MAAK;YACL,KAAK,uBAAuB;gBAC1B,IAAI,iBAAiB,EAAE,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;gBAC1C,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,CAAC,iBAAiB,EAAE,IAAI,CAAC,kBAAkB,EAAE;oBACvF,SAAS,EAAG,CAAC,gBAAgB,EAAE,MAAM,EAAE,EAAE;wBACvC,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;oBACnC,CAAC;iBACF,OAAY,CAAC,CAAC;gBACjB,MAAM;YACN,KAAK,oBAAoB;gBACvB,IAAI,WAAW,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;gBAC/C,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,WAAW,EAAE,IAAI,CAAC,OAAO,IAAI,SAAS,EAAE;oBAChF,SAAS,EAAG,CAAC,OAAO,EAAG,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,EAAE;wBAC5C,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;oBAC1B,CAAC;iBACF,OAAY,EAAE;oBACb,OAAO,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,EAAE;wBACvD,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;oBAClD,CAAC;iBACF,OAAY,CAAC,CAAC;gBACjB,MAAM;YACN,KAAK,sCAAsC;gBACzC,IAAI,kBAAkB,EAAE,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC;gBAC3C,IAAI,CAAC,iBAAiB,CAAC,oCAAoC,CAAC,kBAAkB,EAAE;oBAC9E,SAAS,EAAG,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;wBAC9B,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;oBACzB,CAAC;iBACF,OAAY,EAAE;oBACb,OAAO,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,EAAE;wBACvD,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;oBAClD,CAAC;iBACF,OAAY,CAAC,CAAC;gBACjB,MAAM;YACN,KAAK,iBAAiB;gBACpB,IAAI,CAAC,iBAAiB,CAAC,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE;oBACnD,SAAS,EAAE,CAAC,SAAS,EAAE,OAAO,EAAE,EAAE;wBAChC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAA;oBAC3B,CAAC;iBACF,OAAY,EAAE;oBACb,OAAO,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,EAAE;wBACvD,MAAM,CAAC,KAAK,CAAC,SAAS,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC;oBAClD,CAAC;iBACF,OAAY,CAAC,CAAC;gBACjB,MAAM;YACN;gBACA,MAAM,CAAC,cAAc,EAAE,CAAC;gBACxB,MAAM;SACP;IACH,CAAC;CACF", "entry-package-info": "permission_handler_ohos|1.0.0"}, "permission_handler_ohos|permission_handler_ohos|1.0.0|src/main/ets/com/baseflow/permissionhandler/PermissionConstants.ts": {"version": 3, "file": "PermissionConstants.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/com/baseflow/permissionhandler/PermissionConstants.ets"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;EAaE;AAEF,MAAM,CAAC,OAAO,OAAO,mBAAmB;IACtC,MAAM,CAAC,OAAO,EAAE,MAAM,GAAG,oBAAoB,CAAC;IAC9C,MAAM,CAAC,eAAe,EAAE,MAAM,GAAG,EAAE,CAAC;IACpC,MAAM,CAAC,4CAA4C,EAAE,MAAM,GAAG,GAAG,CAAC;IAClE,MAAM,CAAC,uCAAuC,EAAE,MAAM,GAAG,GAAG,CAAC;IAC7D,MAAM,CAAC,mCAAmC,EAAE,MAAM,GAAG,GAAG,CAAC;IACzD,MAAM,CAAC,wCAAwC,EAAE,MAAM,GAAG,GAAG,CAAC;IAC9D,MAAM,CAAC,0CAA0C,EAAE,MAAM,GAAG,GAAG,CAAC;IAEhE,kBAAkB;IAClB,MAAM,CAAC,yBAAyB,EAAE,MAAM,GAAG,CAAC,CAAC;IAC7C,MAAM,CAAC,uBAAuB,EAAE,MAAM,GAAG,CAAC,CAAC;IAC3C,MAAM,CAAC,yBAAyB,EAAE,MAAM,GAAG,CAAC,CAAC;IAC7C,MAAM,CAAC,yBAAyB,EAAE,MAAM,GAAG,CAAC,CAAC;IAC7C,MAAM,CAAC,gCAAgC,EAAE,MAAM,GAAG,CAAC,CAAC;IACpD,MAAM,CAAC,qCAAqC,EAAE,MAAM,GAAG,CAAC,CAAC;IACzD,MAAM,CAAC,8BAA8B,EAAE,MAAM,GAAG,CAAC,CAAC;IAClD,MAAM,CAAC,2BAA2B,EAAE,MAAM,GAAG,CAAC,CAAC;IAC/C,MAAM,CAAC,sBAAsB,EAAE,MAAM,GAAG,CAAC,CAAC;IAC1C,MAAM,CAAC,uBAAuB,EAAE,MAAM,GAAG,CAAC,CAAC;IAC3C,MAAM,CAAC,gCAAgC,EAAE,MAAM,GAAG,EAAE,CAAC;IACrD,MAAM,CAAC,0BAA0B,EAAE,MAAM,GAAG,EAAE,CAAC;IAC/C,MAAM,CAAC,wBAAwB,EAAE,MAAM,GAAG,EAAE,CAAC;IAC7C,MAAM,CAAC,oBAAoB,EAAE,MAAM,GAAG,EAAE,CAAC;IACzC,MAAM,CAAC,uBAAuB,EAAE,MAAM,GAAG,EAAE,CAAC;IAC5C,MAAM,CAAC,wBAAwB,EAAE,MAAM,GAAG,EAAE,CAAC;IAC7C,MAAM,CAAC,6CAA6C,EAAE,MAAM,GAAG,EAAE,CAAC;IAClE,MAAM,CAAC,6BAA6B,EAAE,MAAM,GAAG,EAAE,CAAC;IAClD,MAAM,CAAC,sCAAsC,EAAE,MAAM,GAAG,EAAE,CAAC;IAC3D,MAAM,CAAC,qCAAqC,EAAE,MAAM,GAAG,EAAE,CAAC;IAC1D,MAAM,CAAC,wBAAwB,EAAE,MAAM,GAAG,EAAE,CAAC;IAC7C,MAAM,CAAC,0BAA0B,EAAE,MAAM,GAAG,EAAE,CAAC;IAC/C,MAAM,CAAC,wCAAwC,EAAE,MAAM,GAAG,EAAE,CAAC;IAC7D,MAAM,CAAC,oCAAoC,EAAE,MAAM,GAAG,EAAE,CAAC;IACzD,MAAM,CAAC,yCAAyC,EAAE,MAAM,GAAG,EAAE,CAAC;IAC9D,MAAM,CAAC,uCAAuC,EAAE,MAAM,GAAG,EAAE,CAAC;IAC5D,MAAM,CAAC,gCAAgC,EAAE,MAAM,GAAG,EAAE,CAAC;IACrD,MAAM,CAAC,2CAA2C,EAAE,MAAM,GAAG,EAAE,CAAC;IAChE,MAAM,CAAC,+BAA+B,EAAE,MAAM,GAAG,EAAE,CAAC;IACpD,MAAM,CAAC,oCAAoC,EAAE,MAAM,GAAG,EAAE,CAAC;IACzD,MAAM,CAAC,kCAAkC,EAAE,MAAM,GAAG,EAAE,CAAC;IACvD,MAAM,CAAC,oCAAoC,EAAE,MAAM,GAAG,EAAE,CAAC;IACzD,MAAM,CAAC,uBAAuB,EAAE,MAAM,GAAG,EAAE,CAAC;IAC5C,MAAM,CAAC,sBAAsB,EAAE,MAAM,GAAG,EAAE,CAAC;IAC3C,MAAM,CAAC,qCAAqC,EAAE,MAAM,GAAG,EAAE,CAAC;IAC1D,MAAM,CAAC,+BAA+B,EAAE,MAAM,GAAG,EAAE,CAAC;IAEpD,mBAAmB;IACnB,MAAM,CAAC,wBAAwB,EAAE,MAAM,GAAG,CAAC,CAAC;IAC5C,MAAM,CAAC,yBAAyB,EAAE,MAAM,GAAG,CAAC,CAAC;IAC7C,MAAM,CAAC,4BAA4B,EAAE,MAAM,GAAG,CAAC,CAAC;IAChD,MAAM,CAAC,yBAAyB,EAAE,MAAM,GAAG,CAAC,CAAC;IAC7C,MAAM,CAAC,iCAAiC,EAAE,MAAM,GAAG,CAAC,CAAC;IAErD,gBAAgB;IAChB,MAAM,CAAC,uBAAuB,EAAE,MAAM,GAAG,CAAC,CAAC;IAC3C,MAAM,CAAC,sBAAsB,EAAE,MAAM,GAAG,CAAC,CAAC;IAC1C,MAAM,CAAC,6BAA6B,EAAE,MAAM,GAAG,CAAC,CAAC;CAClD", "entry-package-info": "permission_handler_ohos|1.0.0"}, "permission_handler_ohos|permission_handler_ohos|1.0.0|src/main/ets/com/baseflow/permissionhandler/PermissionHandlerPlugin.ts": {"version": 3, "file": "PermissionHandlerPlugin.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/com/baseflow/permissionhandler/PermissionHandlerPlugin.ets"], "names": [], "mappings": "YAeO,MAAM;YACN,SAAS;YACT,YAAY;cACV,oBAAoB;cACpB,aAAa,EAAE,oBAAoB;OACrC,GAAG;cACD,eAAe;OACjB,aAAa;OACb,mBAAmB;OACnB,EAAE,iBAAiB,EAAE;OACrB,EAAE,qBAAqB,EAAE;OACzB,EAAE,iBAAiB,EAAE;OACrB,EAAE,cAAc,EAAE;AAGzB,MAAM,GAAG,EAAE,MAAM,GAAG,yBAAyB,CAAC;AAE9C;;;;;;GAMG;AACH,MAAM,CAAC,OAAO,OAAO,uBAAwB,YAAW,aAAa,EAAE,YAAY;IACjF,OAAO,CAAC,iBAAiB,EAAE,iBAAiB,CAAC;IAC7C,OAAO,CAAC,aAAa,EAAE,aAAa,GAAG,IAAI,GAAG,IAAI,CAAC;IACnD,OAAO,CAAC,iBAAiB,EAAE,qBAAqB,GAAG,IAAI,GAAG,IAAI,CAAC;IAC/D,OAAO,CAAC,aAAa,EAAE,oBAAoB,GAAG,IAAI,GAAG,IAAI,CAAC;IAE1D;QACE,IAAI,CAAC,iBAAiB,GAAG,IAAI,iBAAiB,EAAE,CAAC;IACnD,CAAC;IAED,mBAAmB,CAAC,OAAO,EAAE,oBAAoB,GAAG,IAAI;QACtD,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC;QAC7B,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC;IAChE,CAAC;IAED,qBAAqB,IAAI,IAAI;QAC3B,IAAI,CAAC,sBAAsB,EAAE,CAAC;IAChC,CAAC;IAED,kBAAkB,IAAI,MAAM;QAC1B,OAAO,GAAG,CAAC;IACb,CAAC;IAED,kBAAkB,CAAC,OAAO,EAAE,oBAAoB,GAAG,IAAI;QACrD,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC;QACjC,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,qBAAqB,EAAE,EAAE,OAAO,EAAE,kBAAkB,EAAE,CAAC,CAAC;IACvF,CAAC;IAED,oBAAoB,CAAC,OAAO,EAAE,oBAAoB,GAAG,IAAI;QACvD,IAAI,CAAC,aAAa,EAAE,CAAC;IACvB,CAAC;IAED,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,EAAE,OAAO,EAAE,eAAe,GAAG,IAAI;QAC7E,IAAI,CAAC,aAAa,GAAG,IAAI,aAAa,CAAC,OAAO,EAAE,0CAA0C,EAAE,mBAAmB,CAAC,QAAQ,CAAC,CAAC;QAC1H,IAAI,CAAC,iBAAiB,GAAG,IAAI,qBAAqB,CAAC,OAAO,EAAE,IAAI,iBAAiB,EAAE,EACjF,IAAI,CAAC,iBAAiB,EAAE,IAAI,cAAc,EAAE,CAAC,CAAC;QAChD,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;IAClE,CAAC;IAED,OAAO,CAAC,aAAa,IAAI,IAAI;QAC3B,IAAI,CAAC,aAAa,EAAE,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;QAC1B,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC;IAChC,CAAC;IAED,OAAO,CAAC,uBAAuB,CAAC,OAAO,EAAE,SAAS,GAAG,IAAI;QACvD,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,EAAE;YAClC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;SAC5C;IACH,CAAC;IAED,OAAO,CAAC,sBAAsB,IAAI,IAAI;QACpC,IAAI,IAAI,CAAC,iBAAiB,IAAI,IAAI,EAAE;YAClC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;SACzC;IACH,CAAC;CACF", "entry-package-info": "permission_handler_ohos|1.0.0"}, "permission_handler_ohos|permission_handler_ohos|1.0.0|src/main/ets/com/baseflow/permissionhandler/PermissionManager.ts": {"version": 3, "file": "PermissionManager.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/com/baseflow/permissionhandler/PermissionManager.ets"], "names": [], "mappings": "YAeO,MAAM;OACN,mBAAmB;OACnB,iBAAkC;cAAb,WAAW;OAChC,aAAa;OACb,eAAe;OACf,GAAG;YACH,SAAS;OACT,SAAS;cACP,aAAa,QAAQ,iBAAiB;cACtC,aAAa,EAAE,QAAQ;OACvB,mBAAmB;AAE5B,MAAM,OAAO,iBAAiB;IAC5B,OAAO,CAAC,OAAO,EAAE,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC;IAEtC,OAAO,CAAC,SAAS,EAAE,iBAAiB,CAAC,SAAS,CAAC;IAE/C,OAAO,CAAC,OAAO,EAAE,OAAO,GAAG,KAAK,CAAC;IAEjC,OAAO,CAAC,OAAO,EAAE,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC;IAEzC,OAAO,CAAC,eAAe,EAAE,iCAAiC,GAAG,IAAI,GAAG,IAAI,CAAC;IAEzE,OAAO,CAAC,cAAc,EAAC,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC;IAEzD;QACE,IAAI,CAAC,SAAS,GAAG,iBAAiB,CAAC,eAAe,EAAE,CAAC;IACvD,CAAC;IAED;;;;;;OAMG;IACH,MAAM,CAAC,qBAAqB,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,OAAO,EACtE,eAAe,EAAE,8BAA8B,GAAG,IAAI;QACpD,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,eAAe,CAAC,CAAC;IAC/D,CAAC;IAEF,OAAO,CAAC,yBAAyB,CAAC,UAAU,EAAE,MAAM,EAAE,eAAe,EAAE,8BAA8B,GAAG,IAAI;QAC1G,IAAI,UAAU,IAAI,mBAAmB,CAAC,6BAA6B,EAAE;YACnE,IAAI,OAAO,GAAG,mBAAmB,CAAC,yBAAyB,EAAE,CAAC;YAC9D,IAAI,OAAO,EAAE;gBACX,eAAe,CAAC,SAAS,CAAC,mBAAmB,CAAC,yBAAyB,CAAC,CAAC;aAC1E;iBAAM;gBACL,eAAe,CAAC,SAAS,CAAC,mBAAmB,CAAC,wBAAwB,CAAC,CAAC;aACzE;YACD,OAAO;SACR;QACD,IAAI,UAAU,IAAI,mBAAmB,CAAC,0BAA0B,EAAE;YAChE,IAAI,CAAC,8BAA8B,CAAC,eAAe,CAAC,CAAC;YACrD,OAAO;SACR;QACD,eAAe,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC,MAAM,CAAC,EAAE,EAAE;YACxE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,IAAI,SAAS,EAAE;gBACvC,GAAG,CAAC,CAAC,CAAC,mBAAmB,CAAC,OAAO,EAAE,8CAA8C,GAAG,UAAU,CAAC,CAAC;gBAChG,eAAe,CAAC,SAAS,CAAC,mBAAmB,CAAC,yBAAyB,CAAC,CAAC;gBACzE,OAAO;aACR;YACD,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;gBACrB,GAAG,CAAC,CAAC,CAAC,mBAAmB,CAAC,OAAO,EAAE,wCAAwC,GAAG,KAAK,GAAG,UAAU,CAAC,CAAC;gBAClG,IAAI,UAAU,IAAI,mBAAmB,CAAC,6CAA6C,EAAE;oBACnF,eAAe,CAAC,SAAS,CAAC,mBAAmB,CAAC,wBAAwB,CAAC,CAAC;oBACxE,OAAO;iBACR;gBACD,IAAI,UAAU,IAAI,mBAAmB,CAAC,wCAAwC,EAAE;oBAC9E,eAAe,CAAC,SAAS,CAAC,mBAAmB,CAAC,yBAAyB,CAAC,CAAC;oBACzE,OAAO;iBACR;gBACD,eAAe,CAAC,SAAS,CAAC,mBAAmB,CAAC,wBAAwB,CAAC,CAAC;gBACxE,OAAO;aACR;YACD,KAAK,IAAI,IAAI,IAAI,KAAK,EAAE;gBACtB,IAAI,UAAU,IAAI,mBAAmB,CAAC,6CAA6C,EAAE;oBACnF,eAAe,CAAC,SAAS,CAAC,mBAAmB,CAAC,wBAAwB,CAAC,CAAC;oBACxE,OAAO;iBACR;gBACD,IAAI,UAAU,IAAI,mBAAmB,CAAC,wCAAwC,EAAE;oBAC9E,eAAe,CAAC,SAAS,CAAC,mBAAmB,CAAC,yBAAyB,CAAC,CAAC;oBACzE,OAAO;iBACR;gBACD,IAAI,UAAU,IAAI,mBAAmB,CAAC,oCAAoC,EAAE;oBAC1E,eAAe,CAAC,SAAS,CAAC,mBAAmB,CAAC,yBAAyB,CAAC,CAAC;oBACzE,OAAO;iBACR;gBACD,IAAI,UAAU,IAAI,mBAAmB,CAAC,yCAAyC,EAAE;oBAC/E,eAAe,CAAC,SAAS,CAAC,mBAAmB,CAAC,yBAAyB,CAAC,CAAC;oBACzE,OAAO;iBACR;gBACD,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,IAAI,SAAS,EAAE;oBACrD,IAAI,UAAU,GAAG,aAAa,CAAC,wBAAwB,CAAC,aAAa,CAAC,UAAU,CAAC,gCAAgC,CAAC,CAAA;oBAClH,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC,aAAa,CAAC;iBACjD;gBACD,IAAI,MAAM,EAAE,iBAAiB,CAAC,WAAW,GAAI,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,IAAI,WAAW,CAAC,CAAC;gBACrH,IAAI,MAAM,IAAI,iBAAiB,CAAC,WAAW,CAAC,kBAAkB,EAAE;oBAC9D,eAAe,CAAC,SAAS,CAAC,mBAAmB,CAAC,wBAAwB,CAAC,CAAC;oBACxE,OAAO;iBACR;aACF;YACD,eAAe,CAAC,SAAS,CAAC,mBAAmB,CAAC,yBAAyB,CAAC,CAAC;QAC3E,CAAC,CAAC,CAAC;IACL,CAAC;IAED,kBAAkB,CAAC,WAAW,EAAE,SAAS,CAAC,MAAM,CAAC,EAAE,OAAO,EAAE,SAAS,EACnE,eAAe,EAAE,iCAAiC,EAAE,aAAa,EAAE,aAAa,GAAI,IAAI;QACxF,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,aAAa,CAAC,OAAO,CAAE,qCAAqC,EAC1D,4KAA4K,CAAC,CAAC;YAChL,OAAO;SACR;QACD,IAAI,OAAO,IAAI,IAAI,EAAE;YACnB,GAAG,CAAC,CAAC,CAAC,mBAAmB,CAAC,OAAO,EAAE,oCAAoC,CAAC,CAAC;YACzE,aAAa,CAAC,OAAO,CACnB,qCAAqC,EACrC,4CAA4C,CAAC,CAAC;YAChD,OAAO;SACR;QACD,IAAI,CAAC,eAAe,GAAG,eAAe,CAAC;QACvC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,cAAc,GAAG,IAAI,GAAG,EAAE,CAAC;QAChC,IAAI,CAAC,sBAAsB,CAAC,WAAW,EAAE,CAAC,oBAAoB,KAAU,EAAE,EAAE;YAC1E,IAAI,oBAAoB,EAAE,MAAM,GAAG,CAAC,EAAE;gBACpC,IAAI;oBACF,IAAI,CAAC,SAAS,CAAC,0BAA0B,CAAC,OAAO,CAAC,OAAO,EAAE,oBAAoB,CAAC,cAAc,EAAE,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;wBAClH,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;wBACrB,IAAI,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC;wBAC9C,IAAI,eAAe,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,WAAW,CAAC;wBACxD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;4BAC/C,IAAI,cAAc,EAAE,MAAM,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;4BAChD,IAAI,UAAU,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;4BAClC,IAAI,UAAU,EAAE,MAAM,GAAG,eAAe,CAAC,aAAa,CAAC,cAAc,CAAC,CAAC;4BACvE,IAAI,UAAU,IAAI,mBAAmB,CAAC,wBAAwB;gCAC5D,SAAS;4BACX,IAAI,UAAU,IAAI,mBAAmB,CAAC,6BAA6B,EAAE;gCACnE,mBAAmB,CAAC,yBAAyB,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;oCAC7E,IAAI,OAAO,GAAG,mBAAmB,CAAC,yBAAyB,EAAE,CAAC;oCAC9D,IAAI,OAAO,EAAE;wCACX,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,mBAAmB,CAAC,6BAA6B,EAAE,mBAAmB,CAAC,yBAAyB,CAAC,CAAC;qCAC5H;yCAAM;wCACL,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,mBAAmB,CAAC,6BAA6B,EAAE,mBAAmB,CAAC,wBAAwB,CAAC,CAAC;qCAC3H;oCACD,IAAI,CAAC,eAAe,EAAE,SAAS,CAAC,IAAI,CAAC,cAAc,IAAI,GAAG,CAAC,MAAM,EAAC,MAAM,CAAC,CAAC,CAAC;gCAC7E,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,aAAa,EAAE,EAAE;oCAC9B,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,mBAAmB,CAAC,6BAA6B,EAAE,mBAAmB,CAAC,wBAAwB,CAAC,CAAC;oCAC1H,IAAI,CAAC,eAAe,EAAE,SAAS,CAAC,IAAI,CAAC,cAAc,IAAI,GAAG,CAAC,MAAM,EAAC,MAAM,CAAC,CAAC,CAAC;gCAC7E,CAAC,CAAC,CAAC;gCACH,OAAO;6BACR;iCAAM,IAAI,UAAU,IAAI,mBAAmB,CAAC,2BAA2B,EAAE;gCACxE,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,mBAAmB,CAAC,2BAA2B,EAAE,eAAe,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC;gCAC1H,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,mBAAmB,CAAC,uBAAuB,EAAE,eAAe,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC;6BACvH;iCAAM,IAAI,UAAU,IAAI,mBAAmB,CAAC,gCAAgC,EAAE;gCAC7E,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,mBAAmB,CAAC,gCAAgC,EAAE,eAAe,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC;6BAChI;iCAAM,IAAI,UAAU,IAAI,mBAAmB,CAAC,yBAAyB,EAAE;gCAEtE,IAAI,cAAc,IAAI,wCAAwC,EAAE;oCAC9D,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,mBAAmB,CAAC,qCAAqC,EAAE,eAAe,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC;iCACrI;gCACD,IAAI,cAAc,IAAI,0BAA0B,EAAE;oCAChD,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,mBAAmB,CAAC,yBAAyB,EAAE,eAAe,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC;iCACzH;6BAEF;iCAAM;gCACL,wFAAwF;gCACxF,IAAI,SAAS,EAAE,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC;gCACrC,IAAI,MAAM,CAAC,kBAAkB,IAAI,SAAS,EAAE;oCAC1C,SAAS,GAAG,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAC;iCAC1C;gCACD,IAAI,UAAU,IAAI,CAAC,CAAC,IAAI,SAAS,IAAI,KAAK,EAAE;oCAC1C,qBAAqB;oCACrB,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,UAAU,EAAE,mBAAmB,CAAC,iCAAiC,CAAC,CAAC;iCAC7F;qCAAM;oCACL,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,UAAU,EAAE,eAAe,CAAC,kBAAkB,CAAC,UAAU,CAAC,CAAC,CAAC;iCACtF;6BACF;4BACD,eAAe,CAAC,gCAAgC,CAAC,UAAU,CAAC,CAAC;yBAC9D;wBACD,IAAI,CAAC,eAAe,EAAE,SAAS,CAAC,IAAI,CAAC,cAAc,IAAI,GAAG,CAAC,MAAM,EAAC,MAAM,CAAC,CAAC,CAAC;oBAC7E,CAAC,CAAC,CAAC;iBACJ;gBAAC,OAAO,KAAK,EAAE;oBACd,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;oBACrB,IAAI,CAAC,eAAe,EAAE,SAAS,CAAC,IAAI,CAAC,cAAc,IAAI,GAAG,CAAC,MAAM,EAAC,MAAM,CAAC,CAAC,CAAC;iBAC5E;aACF;iBAAM;gBACL,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;gBACrB,IAAI,CAAC,eAAe,EAAE,SAAS,CAAC,IAAI,CAAC,cAAc,IAAI,GAAG,CAAC,MAAM,EAAC,MAAM,CAAC,CAAC,CAAC;aAC5E;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,OAAO,CAAC,sBAAsB,CAAC,WAAW,EAAE,SAAS,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,SAAS,KAAU,CAAC,GAAG,IAAI;QAC3G,IAAI,oBAAoB,EAAE,SAAS,KAAU,GAAI,IAAI,SAAS,EAAE,CAAC;QACjE,IAAI,IAAI,EAAE,MAAM,GAAG,WAAW,CAAC,MAAM,CAAC;QACtC,KAAK,IAAI,UAAU,IAAI,WAAW,EAAE;YAClC,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE;gBACzC,SAAS,EAAG,CAAC,gBAAgB,KAAS,EAAE,EAAE;oBACxC,IAAI,gBAAgB,IAAI,mBAAmB,CAAC,yBAAyB,EAAE;wBACrE,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,UAAU,CAAC,EAAE;4BACzC,IAAI,CAAC,cAAc,EAAE,GAAG,CAAC,UAAU,EAAE,mBAAmB,CAAC,yBAAyB,CAAC,CAAC;yBACrF;wBACD,IAAI,IAAI,CAAC,CAAC;wBACV,IAAI,IAAI,IAAI,CAAC,EAAE;4BACb,QAAQ,CAAC,oBAAoB,CAAC,CAAC;yBAChC;wBACD,OAAO;qBACR;oBACD,eAAe,CAAC,gBAAgB,CAAC,UAAU,EAAE,CAAC,KAAK,EAAC,SAAS,CAAC,MAAM,CAAC,EAAE,EAAE;wBACvE,IAAI,KAAK,IAAI,IAAI,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,EAAE;4BACtC,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE;gCAC/D,IAAI,UAAU,IAAI,mBAAmB,CAAC,6CAA6C,EAAE;oCACnF,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,EAAE,mBAAmB,CAAC,wBAAwB,CAAC,CAAC;iCACnF;gCACD,IAAI,UAAU,IAAI,mBAAmB,CAAC,wCAAwC,EAAE;oCAC9E,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,EAAE,mBAAmB,CAAC,yBAAyB,CAAC,CAAC;iCACpF;gCACD,IAAI,UAAU,IAAI,mBAAmB,CAAC,oCAAoC,EAAE;oCAC1E,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,UAAU,EAAE,mBAAmB,CAAC,yBAAyB,CAAC,CAAC;iCACpF;6BACF;4BACD,IAAI,IAAI,CAAC,CAAC;4BACV,IAAI,IAAI,IAAI,CAAC,EAAE;gCACb,QAAQ,CAAC,oBAAoB,CAAC,CAAC;6BAChC;4BACD,OAAO;yBACR;wBACD,IAAI,UAAU,IAAI,mBAAmB,CAAC,6CAA6C,EAAE;4BACnF,gBAAgB;yBACjB;6BAAM,IAAI,UAAU,IAAI,mBAAmB,CAAC,wCAAwC,EAAE;4BACrF,gBAAgB;yBACjB;6BAAM,IAAI,UAAU,IAAI,mBAAmB,CAAC,oCAAoC,EAAE;4BACjF,gBAAgB;yBACjB;6BAAM;4BACL,KAAK,IAAI,IAAI,IAAI,KAAK,EAAE;gCACtB,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;6BAChC;yBACF;wBACD,IAAI,IAAI,CAAC,CAAC;wBACV,IAAI,IAAI,IAAI,CAAC,EAAE;4BACb,QAAQ,CAAC,oBAAoB,CAAC,CAAC;yBAChC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;aACF,OAAY,CAAC,CAAC;SAChB;IACH,CAAC;IAED,OAAO,CAAC,8BAA8B,CAAC,eAAe,EAAE,8BAA8B,GAAG,IAAI;QAC3F,eAAe,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,0BAA0B,EAAE,CAAC,WAAW,KAAU,EAAE,EAAE;YACzG,IAAI,iBAAiB,EAAE,OAAO,GAAG,WAAW,IAAI,IAAI,IAAI,WAAW,CAAC,OAAO,EAAE,CAAC;YAC9E,IAAI,iBAAiB,EAAE;gBACrB,GAAG,CAAC,CAAC,CAAC,mBAAmB,CAAC,OAAO,EAAE,0CAA0C,CAAC,CAAC;gBAC/E,eAAe,CAAC,SAAS,CAAC,mBAAmB,CAAC,wBAAwB,CAAC,CAAC;aACzE;iBAAM;gBACL,4EAA4E;gBAC5E,IAAI,CAAC,eAAe,CAAE;oBACpB,SAAS,EAAE,CAAC,gBAAgB,EAAE,MAAM,EAAE,EAAE;wBACtC,eAAe,CAAC,SAAS,CAAC,gBAAgB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC5D,CAAC;iBACF,OAAY,EAAE,kCAAkC,CAAC,CAAC;aACpD;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,OAAO,CAAC,eAAe,CAAC,eAAe,EAAE,8BAA8B,EAAE,cAAc,EAAE,WAAW,GAAG,IAAI;QACzG,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,IAAI,IAAI,CAAC,OAAO,IAAI,SAAS,EAAE;YACrD,aAAa,CAAC,oBAAoB,CAAC,aAAa,CAAC,UAAU,CAAC,gCAAgC,CAAC;iBAC1F,IAAI,CAAC,CAAC,UAAU,EAAC,EAAE;gBACpB,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC,aAAa,CAAC;gBAC9C,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,CAAC;YAClG,CAAC,CAAC,CAAC;SACJ;aAAM;YACL,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,CAAC;SAC/F;IACH,CAAC;IAED,oCAAoC,CAAC,UAAU,EAAE,MAAM,EACrD,QAAQ,EAAE,mDAAmD,EAAE,aAAa,EAAE,aAAa,GAAG,IAAI;QAClG,QAAQ,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC;IAC7B,CAAC;CACF;AAED,MAAM,WAAW,8BAA8B;IAC7C,SAAS,CAAC,gBAAgB,EAAE,MAAM,GAAG,IAAI,CAAC;CAC3C;AAED,MAAM,WAAW,iCAAiC;IAChD,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,GAAK,IAAI,CAAC;CACjD;AAED,MAAM,WAAW,mDAAmD;IAClE,SAAS,CAAC,oCAAoC,EAAE,OAAO,GAAG,IAAI,CAAC;CAChE", "entry-package-info": "permission_handler_ohos|1.0.0"}, "permission_handler_ohos|permission_handler_ohos|1.0.0|src/main/ets/com/baseflow/permissionhandler/PermissionUtils.ts": {"version": 3, "file": "PermissionUtils.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/com/baseflow/permissionhandler/PermissionUtils.ets"], "names": [], "mappings": "OAeO,aAAa;OACb,SAAS;cACP,QAAQ;OACV,GAAG;OACH,mBAAmB;AAG1B,MAAM,GAAG,EAAE,MAAM,GAAG,iBAAiB,CAAC;AAEtC,MAAM,CAAC,OAAO,OAAO,eAAe;IAClC,MAAM,CAAC,aAAa,CAAC,UAAU,EAAE,MAAM,GAAG,MAAM;QAC9C,QAAQ,UAAU,EAAE;YAClB,KAAK,+BAA+B,CAAC;YACrC,KAAK,gCAAgC;gBACnC,OAAO,mBAAmB,CAAC,yBAAyB,CAAC;YACvD,KAAK,wBAAwB;gBAC3B,OAAO,mBAAmB,CAAC,uBAAuB,CAAC;YACrD,KAAK,+BAA+B,CAAC;YACrC,KAAK,gCAAgC,CAAC;YACtC,KAAK,oCAAoC;gBACvC,OAAO,mBAAmB,CAAC,yBAAyB,CAAC;YACvD,KAAK,wCAAwC;gBAC3C,OAAO,mBAAmB,CAAC,gCAAgC,CAAC;YAC9D,KAAK,0BAA0B,CAAC;YAChC,KAAK,wCAAwC;gBAC3C,OAAO,mBAAmB,CAAC,yBAAyB,CAAC;YACvD,KAAK,4BAA4B;gBAC/B,OAAO,mBAAmB,CAAC,2BAA2B,CAAC;YACzD,KAAK,qCAAqC,CAAC;YAC3C,KAAK,qCAAqC,CAAC;YAC3C,KAAK,4BAA4B,CAAC;YAClC,KAAK,6BAA6B,CAAC;YACnC,KAAK,+BAA+B,CAAC;YACrC,KAAK,gCAAgC,CAAC;YACtC,KAAK,+CAA+C,CAAC;YACrD,KAAK,kCAAkC;gBACrC,OAAO,mBAAmB,CAAC,sBAAsB,CAAC;YACpD,KAAK,+BAA+B;gBAClC,OAAO,mBAAmB,CAAC,wBAAwB,CAAC;YACtD,KAAK,6BAA6B,CAAC;YACnC,KAAK,sCAAsC,CAAC;YAC5C,KAAK,6BAA6B;gBAChC,OAAO,mBAAmB,CAAC,oBAAoB,CAAC;YAClD,KAAK,uCAAuC,CAAC;YAC7C,KAAK,wCAAwC;gBAC3C,OAAO,mBAAmB,CAAC,wBAAwB,CAAC;YACtD,KAAK,gCAAgC;gBACnC,OAAO,mBAAmB,CAAC,sCAAsC,CAAC;YACpE,KAAK,sCAAsC;gBACzC,OAAO,mBAAmB,CAAC,qCAAqC,CAAC;YACnE,KAAK,yCAAyC;gBAC5C,OAAO,mBAAmB,CAAC,wCAAwC,CAAC;YACtE,KAAK,qCAAqC;gBACxC,OAAO,mBAAmB,CAAC,oCAAoC,CAAC;YAClE,KAAK,gCAAgC;gBACnC,OAAO,mBAAmB,CAAC,yCAAyC,CAAC;YACvE,KAAK,4CAA4C;gBAC/C,OAAO,mBAAmB,CAAC,2CAA2C,CAAC;YACzE,KAAK,+BAA+B;gBAClC,OAAO,mBAAmB,CAAC,+BAA+B,CAAC;YAC7D,KAAK,oCAAoC;gBACvC,OAAO,mBAAmB,CAAC,oCAAoC,CAAC;YAClE,KAAK,kCAAkC;gBACrC,OAAO,mBAAmB,CAAC,kCAAkC,CAAC;YAChE,KAAK,kCAAkC;gBACrC,OAAO,mBAAmB,CAAC,0BAA0B,CAAC;YACxD,KAAK,yCAAyC;gBAC5C,OAAO,mBAAmB,CAAC,6BAA6B,CAAC;YAC3D,KAAK,qCAAqC;gBACxC,OAAO,mBAAmB,CAAC,oCAAoC,CAAC;YAClE,KAAK,4BAA4B;gBAC/B,OAAO,mBAAmB,CAAC,sBAAsB,CAAC;YACpD,KAAK,iCAAiC,CAAC;YACvC,KAAK,kCAAkC;gBACrC,OAAO,mBAAmB,CAAC,uBAAuB,CAAC;YACrD,KAAK,sCAAsC;gBACzC,OAAO,mBAAmB,CAAC,qCAAqC,CAAC;YACnE;gBACE,OAAO,mBAAmB,CAAC,wBAAwB,CAAC;SACvD;IACH,CAAC;IAED,MAAM,CAAC,uBAAuB,CAAC,mBAAmB,EAAE,SAAS,CAAC,MAAM,CAAC,EAAE,UAAU,EAAE,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,IAAI;QAC3H,IAAI;YACF,IAAI,mBAAmB,IAAI,IAAI,EAAE;gBAC/B,KAAK,IAAI,SAAS,IAAI,mBAAmB,EAAE;oBACzC,IAAI,SAAS,IAAI,UAAU,EAAE;wBAC3B,QAAQ,CAAC,IAAI,CAAC,CAAC;wBACf,OAAO;qBACR;iBACF;aACF;YACD,aAAa,CAAC,oBAAoB,CAAC,aAAa,CAAC,UAAU,CAAC,yCAAyC,CAAC;iBACnG,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE;gBACnB,KAAK,IAAI,mBAAmB,IAAI,UAAU,CAAC,oBAAoB,EAAE;oBAC/D,IAAI,mBAAmB,EAAE,IAAI,IAAI,UAAU,EAAE;wBAC3C,QAAQ,CAAC,IAAI,CAAC,CAAC;wBACf,OAAO;qBACR;iBACF;gBACD,QAAQ,CAAC,KAAK,CAAC,CAAC;YAClB,CAAC,CAAC,CAAA;SACL;QAAC,OAAO,CAAC,EAAE;YACV,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,sCAAsC,GAAG,CAAC,CAAC,CAAC;YACvD,QAAQ,CAAC,KAAK,CAAC,CAAC;SACjB;IACH,CAAC;IAED,MAAM,CAAC,oBAAoB,CAAC,WAAW,EAAE,SAAS,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,IAAI;QACtG,IAAI,eAAe,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC;QACzD,IAAI;YACF,aAAa,CAAC,oBAAoB,CAAC,aAAa,CAAC,UAAU,CAAC,yCAAyC,CAAC;iBACnG,IAAI,CAAC,CAAC,UAAU,EAAE,EAAE;gBACnB,KAAK,IAAI,mBAAmB,IAAI,UAAU,CAAC,oBAAoB,EAAE;oBAC/D,IAAI,WAAW,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,CAAC,EAAE;wBAC7C,eAAe,CAAC,GAAG,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;qBAC/C;iBACF;gBACD,+CAA+C;gBAC/C,QAAQ,CAAC,eAAe,CAAC,CAAC;YAC5B,CAAC,CAAC,CAAA;SACL;QAAC,OAAO,CAAC,EAAE;YACV,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,mCAAmC,GAAG,CAAC,CAAC,CAAC;SACrD;IACH,CAAC;IAED,MAAM,CAAC,gBAAgB,CAAC,UAAU,EAAE,MAAM,EAAE,QAAQ,KAAU,GAAG,IAAI;QACnE,IAAI,eAAe,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC;QACzD,QAAQ,UAAU,EAAE;YAClB,KAAK,mBAAmB,CAAC,yBAAyB;gBAChD,IAAI,aAAa,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC;gBACvD,aAAa,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;gBACnD,aAAa,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;gBACpD,eAAe,CAAC,oBAAoB,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;gBAC9D,MAAM;YACR,KAAK,mBAAmB,CAAC,uBAAuB;gBAC9C,eAAe,CAAC,uBAAuB,CAAC,eAAe,EAAE,wBAAwB,EAAE,CAAC,MAAM,EAAE,EAAE;oBAC5F,IAAI,MAAM,EAAE;wBACV,eAAe,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;qBAC/C;oBACD,QAAQ,CAAC,eAAe,CAAC,CAAC;gBAC5B,CAAC,CAAC,CAAC;gBACH,MAAM;YACR,KAAK,mBAAmB,CAAC,yBAAyB;gBAChD,IAAI,SAAS,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC;gBACnD,SAAS,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;gBAC/C,SAAS,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;gBAChD,SAAS,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;gBACpD,QAAQ,CAAC,SAAS,CAAC,CAAC;gBACpB,MAAM;YACR,KAAK,mBAAmB,CAAC,gCAAgC;gBACvD,IAAI,QAAQ,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC;gBAClD,QAAQ,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;gBACvD,eAAe,CAAC,oBAAoB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBACzD,MAAM;YACR,KAAK,mBAAmB,CAAC,qCAAqC,CAAC;YAC/D,KAAK,mBAAmB,CAAC,yBAAyB;gBAChD,IAAI,aAAa,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC;gBACvD,aAAa,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;gBAC9C,aAAa,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;gBAC5D,eAAe,CAAC,oBAAoB,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;gBAC9D,MAAM;YACR,KAAK,mBAAmB,CAAC,uBAAuB,CAAC;YACjD,KAAK,mBAAmB,CAAC,2BAA2B;gBAClD,IAAI,UAAU,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC;gBACpD,UAAU,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;gBAC7C,eAAe,CAAC,oBAAoB,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;gBAC3D,MAAM;YACR,KAAK,mBAAmB,CAAC,sBAAsB;gBAC7C,IAAI,KAAK,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC;gBAC/C,KAAK,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;gBACjD,KAAK,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;gBACjD,KAAK,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;gBACxC,KAAK,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;gBACzC,KAAK,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;gBAC3C,KAAK,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;gBAC5C,KAAK,CAAC,GAAG,CAAC,+CAA+C,CAAC,CAAC;gBAC3D,KAAK,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;gBAC9C,QAAQ,CAAC,KAAK,CAAC,CAAC;gBAChB,MAAM;YACR,KAAK,mBAAmB,CAAC,wBAAwB,CAAC;YAClD,KAAK,mBAAmB,CAAC,+BAA+B;gBACtD,IAAI,MAAM,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC;gBAChD,MAAM,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;gBAC5C,QAAQ,CAAC,MAAM,CAAC,CAAC;gBACjB,MAAM;YACR,KAAK,mBAAmB,CAAC,oBAAoB;gBAC3C,IAAI,GAAG,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC;gBAC7C,GAAG,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;gBACvC,GAAG,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;gBAChD,GAAG,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;gBACvC,QAAQ,CAAC,GAAG,CAAC,CAAC;gBACd,MAAM;YACR,KAAK,mBAAmB,CAAC,wBAAwB;gBAC/C,QAAQ,CAAC,eAAe,CAAC,CAAC;gBAC1B,MAAM;YACR,KAAK,mBAAmB,CAAC,6CAA6C;gBACpE,QAAQ,CAAC,eAAe,CAAC,CAAC;gBAC1B,MAAM;YACR,KAAK,mBAAmB,CAAC,sCAAsC;gBAC7D,IAAI,aAAa,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC;gBACvD,aAAa,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;gBACpD,eAAe,CAAC,oBAAoB,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC;gBAC9D,MAAM;YACR,KAAK,mBAAmB,CAAC,qCAAqC;gBAC5D,QAAQ,CAAC,eAAe,CAAC,CAAC;gBAC1B,MAAM;YACR,KAAK,mBAAmB,CAAC,0BAA0B;gBACjD,IAAI,SAAS,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC;gBACnD,SAAS,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;gBAClD,eAAe,CAAC,oBAAoB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;gBAC1D,MAAM;YACR,KAAK,mBAAmB,CAAC,wCAAwC;gBAC/D,QAAQ,CAAC,eAAe,CAAC,CAAC;gBAC1B,MAAM;YACR,KAAK,mBAAmB,CAAC,oCAAoC;gBAC3D,QAAQ,CAAC,eAAe,CAAC,CAAC;gBAC1B,MAAM;YACR,KAAK,mBAAmB,CAAC,yCAAyC;gBAChE,IAAI,aAAa,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC;gBACvD,aAAa,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;gBACpD,QAAQ,CAAC,aAAa,CAAC,CAAC;gBACxB,MAAM;YACR,KAAK,mBAAmB,CAAC,2CAA2C;gBAClE,IAAI,MAAM,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC;gBAChD,MAAM,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;gBACzD,QAAQ,CAAC,MAAM,CAAC,CAAC;gBACjB,MAAM;YACR,KAAK,mBAAmB,CAAC,+BAA+B;gBACtD,IAAI,aAAa,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC;gBACvD,aAAa,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;gBACnD,QAAQ,CAAC,aAAa,CAAC,CAAC;gBACxB,MAAM;YACR,KAAK,mBAAmB,CAAC,oCAAoC;gBAC3D,IAAI,iBAAiB,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC;gBAC3D,iBAAiB,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAC;gBAC5D,QAAQ,CAAC,iBAAiB,CAAC,CAAC;gBAC5B,MAAM;YACR,KAAK,mBAAmB,CAAC,kCAAkC;gBACzD,IAAI,gBAAgB,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC;gBAC1D,gBAAgB,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;gBACzD,QAAQ,CAAC,gBAAgB,CAAC,CAAC;gBAC3B,MAAM;YACR,KAAK,mBAAmB,CAAC,6BAA6B;gBACpD,IAAI,gBAAgB,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC;gBAC1D,gBAAgB,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;gBAChE,QAAQ,CAAC,gBAAgB,CAAC,CAAC;gBAC3B,MAAM;YACR,KAAK,mBAAmB,CAAC,oCAAoC;gBAC3D,QAAQ,CAAC,eAAe,CAAC,CAAC;gBAC1B,MAAM;YACR,KAAK,mBAAmB,CAAC,uBAAuB,CAAC;YACjD,KAAK,mBAAmB,CAAC,uBAAuB;gBAC9C,IAAI,YAAY,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC;gBACtD,YAAY,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;gBACpD,YAAY,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;gBACrD,eAAe,CAAC,oBAAoB,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;gBAC7D,MAAM;YACR,KAAK,mBAAmB,CAAC,sBAAsB;gBAC7C,IAAI,KAAK,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC;gBAC/C,KAAK,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;gBACxC,eAAe,CAAC,oBAAoB,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;gBACtD,MAAM;YACR,KAAK,mBAAmB,CAAC,qCAAqC;gBAC5D,IAAI,UAAU,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,IAAI,SAAS,EAAE,CAAC;gBACpD,UAAU,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;gBACvD,QAAQ,CAAC,eAAe,CAAC,CAAC;gBAC1B,MAAM;YACR,KAAK,mBAAmB,CAAC,8BAA8B,CAAC;YACxD,KAAK,mBAAmB,CAAC,0BAA0B,CAAC;YACpD,KAAK,mBAAmB,CAAC,wBAAwB;gBAC/C,QAAQ,CAAC,IAAI,CAAC,CAAC;gBACf,MAAM;YACR;gBACE,QAAQ,CAAC,eAAe,CAAC,CAAC;gBAC1B,MAAM;SACT;IACH,CAAC;IAED,MAAM,CAAC,kBAAkB,CAAC,UAAU,EAAE,MAAM,GAAG,MAAM;QACnD,IAAI,UAAU,IAAI,CAAC,EAAE;YACnB,OAAO,mBAAmB,CAAC,yBAAyB,CAAC;SACtD;QACD,OAAO,mBAAmB,CAAC,wBAAwB,CAAC;IACtD,CAAC;IAED,MAAM,CAAC,gCAAgC,CAAC,UAAU,EAAE,MAAM,GAAG,IAAI;IACjE,CAAC;CACF", "entry-package-info": "permission_handler_ohos|1.0.0"}, "permission_handler_ohos|permission_handler_ohos|1.0.0|src/main/ets/com/baseflow/permissionhandler/ServiceManager.ts": {"version": 3, "file": "ServiceManager.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/com/baseflow/permissionhandler/ServiceManager.ets"], "names": [], "mappings": "YAeO,MAAM;cACJ,aAAa,QAAQ,iBAAiB;OACxC,mBAAmB;OACnB,eAAe;OACf,gBAAgB;OAChB,GAAG;AAEV,MAAM,OAAO,cAAc;IACzB,kBAAkB,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,OAAO,EAC5D,eAAe,EAAE,eAAe,EAAE,aAAa,EAAE,aAAa,GAAG,IAAI;QACrE,IAAI,UAAU,IAAI,mBAAmB,CAAC,yBAAyB;YAC7D,UAAU,IAAI,mBAAmB,CAAC,gCAAgC;YAClE,UAAU,IAAI,mBAAmB,CAAC,qCAAqC,EAAE;YACzE,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBACtD,CAAC,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,CAAC,CAAC,mBAAmB,CAAC,uBAAuB,CAAC,CAAC;YAC9F,OAAO;SACR;QACD,IAAI,UAAU,IAAI,mBAAmB,CAAC,0BAA0B,EAAE;YAChE,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,wBAAwB,EAAE;gBACvD,CAAC,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,CAAC,CAAC,mBAAmB,CAAC,uBAAuB,CAAC,CAAC;YAC9F,OAAO;SACR;QACD,IAAI,UAAU,IAAI,mBAAmB,CAAC,sBAAsB,EAAE;YAC5D,IAAI;gBACF,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;oBAChC,IAAI,KAAK,IAAI,GAAG,CAAC,QAAQ,CAAC,eAAe,EAAE;wBACzC,eAAe,CAAC,SAAS,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,CAAC;wBACtE,OAAO;qBACR;oBACD,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE;wBACtC,IAAI,WAAW,IAAI,GAAG,CAAC,QAAQ,CAAC,eAAe,EAAE;4BAC/C,eAAe,CAAC,SAAS,CAAC,mBAAmB,CAAC,sBAAsB,CAAC,CAAC;4BACtE,OAAO;yBACR;wBACD,eAAe,CAAC,SAAS,CAAC,mBAAmB,CAAC,uBAAuB,CAAC,CAAC;oBACzE,CAAC,CAAC,CAAC;gBACL,CAAC,CAAC,CAAC;aACJ;YAAC,OAAO,KAAK,EAAE;gBACd,eAAe,CAAC,SAAS,CAAC,mBAAmB,CAAC,uBAAuB,CAAC,CAAC;aACxE;YACD,OAAO;SACR;QACD,IAAI,UAAU,IAAI,mBAAmB,CAAC,6CAA6C,EAAE;YACnF,eAAe,CAAC,SAAS,CAAC,mBAAmB,CAAC,uBAAuB,CAAC,CAAC;YACvE,OAAO;SACR;QACD,eAAe,CAAC,SAAS,CAAC,mBAAmB,CAAC,6BAA6B,CAAC,CAAC;IAC/E,CAAC;IAED,OAAO,CAAC,uBAAuB,IAAI,OAAO;QACxC,IAAI;YACF,OAAO,eAAe,CAAC,iBAAiB,EAAE,CAAC;SAC5C;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAED,OAAO,CAAC,wBAAwB,IAAI,OAAO;QACzC,IAAI;YACF,OAAO,gBAAgB,CAAC,QAAQ,EAAE,IAAI,gBAAgB,CAAC,cAAc,CAAC,QAAQ,CAAC;SAChF;QAAC,OAAO,KAAK,EAAE;YACd,OAAO,KAAK,CAAC;SACd;IACH,CAAC;CACF;AAED,MAAM,WAAW,eAAe;IAC9B,SAAS,CAAC,aAAa,EAAE,MAAM,GAAG,IAAI,CAAC;CACxC", "entry-package-info": "permission_handler_ohos|1.0.0"}}