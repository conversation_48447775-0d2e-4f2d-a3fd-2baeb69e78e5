syntax = "proto3";

// 车辆状态变更，车联网后端给APP的消息
//2024-09-05 新增座椅通风和加热 共8个字段
message SgmwAppCarStatus {
  int64 collectTime = 1; //状态更新时间，unix时间戳。
  string acStatus = 2; //空调状态0关，1制冷，2制热
  string doorLockStatus = 3; //全部车门状态，0关，1开
  string windowStatus = 4; //全部车窗状态,0关1开
  string engineStatus = 5; //发动机状态，0关，1开
  string tailDoorLockStatus = 6; //尾门锁状态，0关，1开
  string lowBeamLight = 7; //近光灯开车灯
  string dipHeadLight = 8; //远光灯开车灯
  string sentinelModeStatus = 9; //哨兵模式开关
  string tailDoorOpenStatus = 10; //尾门门碰开关状态，0关1开
  string door1LockStatus = 11; //左前门锁定状态，0关1开
  string door2LockStatus = 12; //右前门锁定状态，0关1开
  string door3LockStatus = 13; //左后门锁定状态，0关1开
  string door4LockStatus = 14; //右后门锁定状态，0关1开
  string doorOpenStatus = 15; //全部车门门碰状态，0关，1开
  string door1OpenStatus = 16; //左前门门碰状态，0关1开
  string door2OpenStatus = 17; //右前门门碰状态，0关1开
  string door3OpenStatus = 18; //左后门门碰状态，0关1开
  string door4OpenStatus = 19; //右后门门碰状态，0关1开
  string window1Status = 20; //左前窗状态，0关，1开
  string window2Status = 21; //右前窗状态，0关，1开
  string window3Status = 22; //左后窗状态，0关，1开
  string window4Status = 23; //右后窗状态，0关，1开
  string topWindowStatus = 24;//天窗状态，0关，1开
  string autoGearStatus = 25;//自动档档位状态，0: N 空挡 1: D 前进 2: R 倒退 3: P 驻车
  string manualGearStatus = 26;//自动档档位状态，0: 空档，其他数字对应就是挡位
  string keyStatus = 27;//钥匙状态，0:OFF 1:ACC 2:ON
  string acTemperatureGear = 28;//空调温度档位，0:不显示 1: 1 档 2: 2 档 3: 3 档 4: 4 档 5: 5 档 6: 6 档
  string acWindGear = 29;//空调风量档位
  string leftBatteryPower = 30;//电池包剩余电量0-100%
  string leftFuel = 31;//剩余油量0-1
  string mileage = 32;//车辆行驶里程，公里
  string leftMileage = 33;//车辆行续航，公里
  string batterySoc = 34;//电池包 SOC 值0-100%
  string current = 35;//电池包电流-安
  string voltage = 36;//电池包电压，伏
  string batAvgTemp = 37;//电池包平均温度，摄氏度
  string batMaxTemp = 38;//电池包最高温度，摄氏度
  string batMinTemp = 39;//电池包最低温度，摄氏度
  string tmActTemp = 40;//电机当前温度，摄氏度
  string invActTemp = 41;//电机控制器当前温度，摄氏度
  string accActPos = 42;//加速踏板位置，0-100%
  string brakPedalPos = 43;//制动踏板位置，0: 未踩下 1: 踩下
  string strWhAng = 44;//方向盘角度，单位：deg
  string vehSpdAvgDrvn = 45;//车速,单位km/h
  string obcOtpCur = 46;//充电机输出电流
  string vecChrgingSts = 47;//充电线连接状态，0: 非连接 1: 连接
  string vecChrgStsIndOn = 48;//充电状态指示0: 非充电 1:充电
  string obcTemp = 49;//电机温度
  string batSOH = 50;//电池健康度0-100%
  string lowBatVol = 51;//低压蓄电池电压，伏
  string leftTurnLight = 52;//左转向灯状态，0关1开
  string rightTurnLight = 53;//右转向灯状态，0关1开
  string positionLight = 54;//位置灯状态，0关1开
  string frontFogLight = 55;//前雾灯状态，0关1开
  string rearFogLight = 56;//后雾灯状态，0关1开
  string latitude = 57;//车辆位置纬度
  string longitude = 58;//车辆位置经度
  string position = 59;//车辆位置（中文地址），不一定存在。若不存在，需要前端自行通过经纬度获取
  string charging = 60;//充电状态，0未充电，1充电中
  string wireConnect = 61;//充电机输出电流
  string rechargeStatus = 62;//补电状态 0 补电未开启 1 补电中
  string window1OpenDegree = 63; //左前窗开度值
  string window2OpenDegree = 64; //右前窗开度值
  string window3OpenDegree = 65; //左后窗开度值
  string window4OpenDegree = 66; //右后窗开度值
  string seat1WindStatus = 67; //主驾. 1:通风1;2通风2;3通风3;4-6预留;7 通风关闭
  string seat2WindStatus = 68; //副驾. 1:通风1;2通风2;3通风3;4-6预留;7 通风关闭
  string seat3WindStatus = 69; //主驾后排. 1:通风1;2通风2;3通风3;4-6预留;7 通风关闭
  string seat4WindStatus = 70; //副驾后排. 1:通风1;2通风2;3通风3;4-6预留;7 通风关闭
  string seat1HotStatus = 71; //主驾.1:加热1;2:加热2;3:加热3；4-6预留；7 加热关闭
  string seat2HotStatus = 72; //副驾.1:加热1;2:加热2;3:加热3；4-6预留；7 加热关闭
  string seat3HotStatus = 73; //主驾后排.1:加热1;2:加热2;3:加热3；4-6预留；7 加热关闭
  string seat4HotStatus = 74; //副驾后排.1:加热1;2:加热2;3:加热3；4-6预留；7 加热关闭
}