// @keepTs
// @ts-nocheck
/**
 * Created on 2024/08/06
 * <AUTHOR>
 */
import { Conversation, PublicServiceInfo } from '@rongcloud/imlib';
import { BaseUiConversation } from '../../../conversationlist/model/BaseUiConversation';
import { image } from '@kit.ImageKit';
import { UserInfoModel } from '../../../user/model/UserInfoModel';
import { GroupInfoModel } from '../../../user/model/GroupInfoModel';
import { GroupMemberInfoModel } from '../../../user/model/GroupMemberInfoModel';
@Observed
export declare class PublicServiceUiConversation extends BaseUiConversation {
    imagePixelMap: image.PixelMap | undefined;
    onUserInfoUpdate(e281: UserInfoModel): Promise<void>;
    onGroupInfoUpdate(d281: GroupInfoModel): void;
    onGroupMemberUpdate(c281: GroupMemberInfoModel): void;
    onPublicServiceInfoUpdate(a281: PublicServiceInfo): void;
    onConversationUpdate(z279: Conversation, a280: boolean): Promise<void>;
    /**
     * 获取图片
     * @param resource
     * @returns
     */
    private getPixmapFromMedia;
}
