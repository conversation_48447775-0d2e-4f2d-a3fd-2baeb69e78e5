type char = string;
type byte = number;
export default class HexUtil {
    private static readonly DIGITS_LOWER;
    private static readonly DIGITS_UPPER;
    static encodeHex(data: byte[], toLowerCase?: boolean): char[];
    protected static encodeHexInner(data: byte[], toDigits: char[]): char[];
    private static byteToString;
    static encodeHexStr(data: byte[], toLowerCase?: boolean): string;
    protected static encodeHexStrInner(data: byte[], toDigits: char[]): string;
    static formatHexString(data: Uint8Array, addSpace?: boolean): string;
    static decodeHex(data: char[]): byte[];
    protected static toDigit(ch: char, index: number): number;
    static hexStringToBytes(hexString: string): Uint8Array | null;
    static charToByte(c: char): byte;
    static extractData(data: Uint8Array, position: number): String;
}
export {};
