import { AoiSearchOption } from "../../../d/a2/d2";
import { SearchRequest } from '../../base/base';
import { UrlProvider } from "../../base/d3";
export declare class AoiSearchRequest extends SearchRequest {
    /**
     * @description 构造函数，用于初始化AoiSearchOption对象
     * @param aoiSearchOption AoiSearchOption类型，包含检索参数的对象
     * @returns 无返回值
     */
    constructor(y5: AoiSearchOption);
    /**
     * @description 构建Aoi搜索请求参数
     * @param option Aoi搜索选项，包含latLngList（LatLng类型的数组）
     * @returns 无返回值，通过修改this.optionBuilder来添加参数
     */
    private AoiSearchRequestBuildParam;
    /**
     * @description 获取指定URL提供者的域名
     * @param {UrlProvider} provider URL提供者对象，包括getAoiDomain方法用于获取域名
     * @returns {string} 返回URL提供者的域名字符串
     */
    getUrlDomain(r5: UrlProvider): string;
}
