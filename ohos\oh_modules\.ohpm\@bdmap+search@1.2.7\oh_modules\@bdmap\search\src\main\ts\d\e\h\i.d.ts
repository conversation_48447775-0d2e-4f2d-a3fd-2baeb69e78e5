import type { LatLng } from '@bdmap/base';
import type { PlanNode, RouteLine, RouteNode, RouteStep, SearchResult, SuggestAddrInfo, TaxiInfo } from "./f1";
/**
 * 换乘路线规划参数
 */
export interface MassTransitRoutePlanOption {
    /**
     * 起点
     */
    from: PlanNode;
    /**
     * 终点
     */
    to: PlanNode;
    /**
     *  设置市内公交换乘策略
     */
    tacticsInCity?: TacticsInCity;
    /**
     * 设置跨城公交换乘策略
     */
    tacticsIntercity?: TacticsIntercity;
    /**
     * 设置跨城交通方式策略
     */
    transTypeIntercity?: TransTypeIntercity;
    /**
     * 设置每页返回几条路线,[1,10]
     */
    pageSize?: number;
    /**
     * 设置返回第几页 从0开始
     */
    pageIndex?: number;
}
/**
 * 市内公交换乘策略
 */
export declare enum TacticsInCity {
    /**
     * 推荐
     */
    ETRANS_SUGGEST = 0,
    /**
     * 少换乘
     */
    ETRANS_LEAST_TRANSFER = 1,
    /**
     * 少步行
     */
    ETRANS_LEAST_WALK = 2,
    /**
     * 不坐地铁
     */
    ETRANS_NO_SUBWAY = 3,
    /**
     * 时间短
     */
    ETRANS_LEAST_TIME = 4,
    /**
     * 地铁优先
     */
    ETRANS_SUBWAY_FIRST = 5
}
/**
 * 跨城公交换乘策略
 */
export declare enum TacticsIntercity {
    /**
     * 时间短
     */
    ETRANS_LEAST_TIME = 0,
    /**
     * 出发早
     */
    ETRANS_START_EARLY = 1,
    /**
     * 价格低
     */
    ETRANS_LEAST_PRICE = 2
}
/**
 * 跨城交通方式策略
 */
export declare enum TransTypeIntercity {
    /**
     * 火车优先
     */
    ETRANS_TRAIN_FIRST = 0,
    /**
     * 飞机优先
     */
    ETRANS_PLANE_FIRST = 1,
    /**
     * 大巴优先
     */
    ETRANS_COACH_FIRST = 2
}
/**
 * 跨城公交线路规划结果
 */
export interface MassTransitRouteResult extends SearchResult {
    /**
     * 起点
     */
    origin?: TransitResultNode;
    /**
     * 终点
     */
    destination?: TransitResultNode;
    /**
     * 打车信息
     */
    massTaxiInfo?: TaxiInfo;
    /**
     * 所有路线总数
     */
    total?: number;
    /**
     * 获取所有换乘路线方案
     */
    routeLines?: MassTransitRouteLine[];
    /**
     * 当{@link #error} 为 {@link ERRORNO#AMBIGUOUS_ROURE_ADDR} 时
     * 可通过此接口获取建议信息
     */
    suggestAddrInfo?: SuggestAddrInfo;
}
/**
 * 公交路线规划结果点信息
 */
export interface TransitResultNode {
    /**
     * 城市编号
     */
    cityId?: number;
    /**
     * 城市名
     *
     */
    cityName?: string | null;
    /**
     * 坐标
     */
    location?: LatLng | null;
    /**
     * 检索时关键字（在检索词模糊，返回建议列表时才有。有路线结果时，该字段为空）
     */
    searchWord?: string | null;
}
/**
 * 表示一个跨城交通换乘路线，换乘路线将根据既定策略调配多种交通工具。
 * <p>
 * 换乘路线可能包含：城市公交路段，地铁路段，步行路段，飞机，大巴
 * </p>
 */
export interface MassTransitRouteLine extends RouteLine<TransitStep> {
    /**
     * 本线路预计到达时间，格式：2024-04-05T17：06：10
     */
    arriveTime?: string;
    /**
     * 本线路的总票价（元）
     */
    price?: number;
    /**
     * 车票详细信息
     */
    priceInfo?: PriceInfo[];
    /**
     * 该线路的step信息
     */
    newSteps?: TransitStep[][];
}
/**
 * 车票详细信息
 */
export interface PriceInfo {
    /**
     * 票类型
     */
    ticketType: number;
    /**
     * 票价格（元）
     */
    ticketPrice: number;
}
/**
 * 表示一个换乘路段
 */
export interface TransitStep extends RouteStep {
    /**
     * 交通情况
     */
    trafficConditions?: TrafficCondition[];
    /**
     * 本路段起点
     */
    startLocation?: LatLng;
    /**
     * 本路段终点
     */
    endLocation?: LatLng;
    /**
     * 火车具体信息
     */
    trainInfo?: TrainInfo | null;
    /**
     * 飞机具体信息
     */
    planeInfo?: PlaneInfo | null;
    /**
     * 大巴具体信息
     */
    coachInfo?: CoachInfo | null;
    /**
     * 公交具体信息
     */
    busInfo?: BusInfo | null;
    /**
     * 本路段中交通方式的类型：1火车，2飞机，3公交，4驾车，5步行，6大巴
     */
    vehicleType?: StepVehicleInfoType;
    /**
     * 该路段换乘说明
     */
    instructions?: string;
    /**
     * path点坐标,未解析,出于性能考虑，缓存path坐标点，当需要用到时再解析
     */
    pathString?: string;
}
/**
 * 交通情况
 */
export interface TrafficCondition {
    /**
     * 路况状态，0无路况，1畅通，2缓行，3拥堵，4非常拥堵
     */
    trafficStatus: number;
    /**
     * 此路况的点数
     */
    trafficGeoCnt: number;
}
export interface TransitBaseInfo {
    name: string;
    departureStation: string;
    arriveStation: string;
    departureTime: string;
    arriveTime: string;
}
/**
 * 火车具体信息
 */
export interface TrainInfo extends TransitBaseInfo {
    /**
     * 总票价
     */
    price?: number;
    /**
     * 订票电话
     */
    booking?: string;
}
/**
 * 飞机具体信息
 */
export interface PlaneInfo extends TransitBaseInfo {
    /**
     * 折扣
     */
    discount: number;
    /**
     * 航空公司
     */
    airlines: string;
    /**
     * 总票价
     */
    price: number;
    /**
     * 订票网址
     */
    booking: string;
}
/**
 * 大巴具体信息
 */
export interface CoachInfo extends TransitBaseInfo {
    /**
     * 总票价
     */
    price: number;
    /**
     * 订票网址
     */
    booking: string;
    /**
     * 合作方名称
     */
    providerName: string;
    /**
     * 合作方官网地址
     */
    providerUrl: string;
}
export interface BusInfo extends TransitBaseInfo {
    /**
     * 市内公交的具体类型
     */
    type?: number;
    /**
     * 途经站点数
     */
    stopNum?: number;
    /**
     * 方向信息
     */
    directText?: string;
    /**
     * 公交路线id
     */
    lineUid?: string;
    /**
     * 下车点站uid
     */
    endUid?: string;
    /**
     * 上车点站uid
     */
    startUid?: string;
    /**
     * 途径站点
     */
    passStopInfoList?: RouteNode[];
}
export declare enum StepVehicleInfoType {
    ESTEP_TRAIN = 1,
    ESTEP_PLANE = 2,
    ESTEP_BUS = 3,
    ESTEP_DRIVING = 4,
    ESTEP_WALK = 5,
    ESTEP_COACH = 6
}
