import type { LatLng } from '@bdmap/base';
import type { PlanNode, RouteLine, RouteNode, RouteStep, SearchResult, SuggestAddrInfo, TaxiInfo } from "./f1";
/**
 * 驾车路线规划参数
 */
export interface DrivingRoutePlanOption {
    /**
     * 起点
     */
    mFrom: PlanNode;
    /**
     * 终点
     */
    mTo: PlanNode;
    /**
     * 当前城市名称
     */
    mCityName?: string;
    /**
     * 驾车路线规划策略
     */
    mPolicy?: DrivingPolicy;
    /**
     * 途径点
     */
    mWayPoints?: PlanNode[];
    /**
     * 是否支持路况数据
     */
    mtrafficPolicy?: DrivingTrafficPolicy;
}
/**
 * 驾车路线规划策略枚举
 */
export declare enum DrivingPolicy {
    /**
     * 驾车策略：躲避拥堵
     */
    ECAR_AVOID_JAM = 3,
    /**
     * 驾乘检索策略常量：时间优先
     */
    ECAR_TIME_FIRST = 0,
    /**
     * 驾乘检索策略常量：最短距离
     */
    ECAR_DIS_FIRST = 1,
    /**
     * 驾乘检索策略常量：较少费用
     */
    ECAR_FEE_FIRST = 2
}
/**
 * 驾车路线路况策略
 */
export declare enum DrivingTrafficPolicy {
    /**
     * 驾车路线不含路况
     */
    ROUTE_PATH = 0,
    /**
     * 驾车路线含路况
     */
    ROUTE_PATH_AND_TRAFFIC = 1
}
/**
 * 驾车路线结果
 */
export interface DrivingRouteResult extends SearchResult {
    /**
     * 所有规划路线方案
     */
    routeLines?: DrivingRouteLine[];
    /**
     * 打车信息
     */
    taxiInfos?: TaxiInfo[];
    /**
     * 路线建议搜索信息,当{@link #error}值为{@link ERRORNO#AMBIGUOUS_ROURE_ADDR}时，建议信息不为空
     */
    suggestAddrInfo?: SuggestAddrInfo;
}
/**
 * 表示驾车路线
 */
export interface DrivingRouteLine extends RouteLine<DrivingStep> {
    /**
     * 该路线所在区域是否含有交通流量信息
     */
    isSupportTraffic: boolean;
    /**
     * 路线途经点
     */
    wayPoints: RouteNode[];
    /**
     * 拥堵米数
     */
    congestionDistance: number;
    /**
     * 红绿灯个数
     */
    lightNum: number;
    /**
     * 路线收费数
     */
    toll: number;
}
export interface DrivingStep extends RouteStep {
    /**
     * 该路段起点方向值 单位： 度。 正北方向为0度，顺时针
     */
    direction: number;
    /**
     * 路段入口信息
     */
    entrance: RouteNode;
    /**
     * 路段出口信息
     */
    exit: RouteNode;
    /**
     * 缓存的点
     */
    pathString: string;
    /**
     * 路段入口的指示信息
     */
    entranceInstructions: string;
    /**
     * 路段出口指示信息
     */
    exitInstructions: string;
    /**
     * 路段总体指示信息
     */
    instructions: string;
    /**
     * 路段转弯类型
     * 0：无效
     * 1：直行
     * 2：右前方转弯
     * 3：右转
     * 4：右后方转弯
     * 5：掉头
     * 6：左后方转弯
     * 7：左转
     * 8：左前方转弯
     * 9：左侧
     * 10：右侧
     * 11：分歧-左
     * 12：分歧中央
     * 13：分歧右
     * 14：环岛
     * 15：进渡口
     * 16：出渡口
     *
     * 路段转弯类型
     */
    numTurns: number;
    /**
     * 途径点信息
     */
    pathList: LatLng[];
    /**
     * 路况数据数组，个数为wayPoints个数-1,该step的路况数据，0：没路况，1：畅通，2：缓慢，3：拥堵，4：非常拥堵
     */
    trafficList: number[];
    /**
     * 道路类型
     *
     * 枚举值：返回0-9之间的值
     * 0：高速路
     * 1：城市高速路
     * 2:国道
     * 3：省道
     * 4：县道
     * 5：乡镇村道
     * 6：其他道路
     * 7：九级路
     * 8：航线(轮渡)
     * 9：行人道路
     */
    roadLevel: number;
    /**
     * 道路名称
     */
    roadName: string;
}
