import { DownloadListenerFlutterApiImpl } from './DownloadListenerFlutterApiImpl';
import { DownloadListenerHostApi } from './GeneratedOhosWebView';
import { InstanceManager } from './InstanceManager';
export declare class DownloadListenerHostApiImpl extends DownloadListenerHostApi {
    private instanceManager;
    private downloadListenerCreator;
    private flutterApi;
    constructor(instanceManager: InstanceManager, downloadListenerCreator: DownloadListenerCreator, flutterApi: DownloadListenerFlutterApiImpl);
    create(instanceId: number): void;
    static createDownloadListener(flutterApi: DownloadListenerFlutterApiImpl): DownloadListenerImpl;
}
export declare class DownloadListenerCreator {
    createDownloadListener(flutterApi: DownloadListenerFlutterApiImpl): DownloadListenerImpl;
}
declare class DownloadListenerImpl implements DownloadListener {
    private flutterApi;
    constructor(flutterApi: DownloadListenerFlutterApiImpl);
    onDownloadStart(url: string, userAgent: string, contentDisposition: string, mimetype: string, contentLength: number): void;
}
export interface DownloadListener {
    onDownloadStart(url: string, userAgent: string, contentDisposition: string, mimeType: string, contentLength: number): void;
}
export {};
