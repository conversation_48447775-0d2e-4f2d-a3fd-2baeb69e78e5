// @keepTs
// @ts-nocheck
import { List } from '@kit.ArkTS';
import { Conversation, ConversationIdentifier } from "@rongcloud/imlib";
/**
 * 辅助ConversationList处理一些繁琐逻辑
 */
export declare class ConversationListHelper {
    private waitingMap;
    private isLoadingConversation;
    private callback?;
    setConversationUpdateCallback(y283: (convList: List<ConversationWrapper>) => void): void;
    clear(): void;
    /**
     * 添加查询会话标识
     * @param conId 查询的会话标识
     * @param addConversation 是否需要新增会话，按需添加（收消息），其他场景应默认不添加，
     */
    addTask(u283: ConversationIdentifier, v283?: boolean): void;
    /**
     * 收消息更新会话列表，兼容在线消息多、会话多的情况，批量刷新。
     * 备注：未来如果支持按会话ID数组查询会话，这里会更快更准确。
     */
    private updateConversations;
    private updateConversationByPage;
    private updateConversationById;
    private fillDown;
}
/**
 * 更新会话的包装类。
 * 包含会话标识、会话对象、会话列表找不到会话时是否新增会话、是否查询失败，需要单会话查询
 */
export interface ConversationWrapper {
    conId: ConversationIdentifier;
    conversation: Conversation;
    addConversation: boolean;
    fillDownCount: number;
}
