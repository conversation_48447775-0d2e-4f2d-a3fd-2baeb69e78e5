import { Message } from '../Message';
/**
 * 阅后即焚监听
 *```
 // sequenceDiagram start
 title 阅后即焚
 发送方->>接收方: 1.发送阅后即焚消息
 activate 接收方
 接收方-->> 接收方: 2.用户进入聊天页面点击阅后即焚消息
 接收方-->> 接收方: 3.该消息开始阅后即焚
 接收方-->> 发送方: 4.发送阅后即焚通知
 deactivate 接收方
 activate 发送方
 发送方-->> 发送方: 5.删除本地消息，删除远端消息
 发送方-->> 发送方: 6.更新对应的聊天页面
 deactivate 发送方
 activate 接收方
 接收方-->> 接收方: 7.倒计时不停触发
 接收方-->> 接收方: 8.倒计时结束，删除本地消息，删除远端消息
 接收方-->> 接收方: 9.更新对应的聊天页面
 deactivate 接收方
 // sequenceDiagram end

 1. 发送方发送阅后即焚消息：将 MessageContent destructDuration 设置为 > 0 的整数，调用 sendMessage()  sendMediaMessage() 发送
 2. 接收方用户进入聊天页面点击阅后即焚消息：接收方进入聊天页面，点击阅后即焚消息
 3. 接收方该消息开始阅后即焚：接收方调用 messageBeginDestruct() 方法开始消息阅后即焚
 4. 接收方发送阅后即焚通知：接收方调用 messageBeginDestruct() 方法后，SDK 会通知发送方
 5. 发送方删除本地消息，删除远端消息: 发送方触发 MessageDestructionListener.onMessageDestructing 且 leftDuration 为 0。SDK 会把对应的本地消息和远端消息删除，此时 App/IMKit 需要将聊天页面中的该消息删除
 6. 发送方更新对应的聊天页面： 发送方聊天页面将对应消息删除
 7. 接收方倒计时不停触发： 接收方触发 MessageDestructionListener.onMessageDestructing 且 leftDuration 不停递减
 8. 接收方倒计时结束，删除本地消息，删除远端消息：当 leftDuration 为 0 时 SDK 会把对应的本地消息和远端消息删除，此时 App/IMKit 需要将聊天页面中的该消息删除
 9. 接收方更新对应的聊天页面：接收方聊天页面将对应消息删除
 *```
 * @version 1.3.0
 * @discussion 使用 https://sequencediagram.org/ 打开时序图
 */
interface MessageDestructionListener {
    /**
     * 消息开始阅后即焚
     * @param message 消息体
     * @param leftDuration 剩余的时间，单位秒
     * @discussion 调用 messageBeginDestruct() 之后，消息每经过 1 秒，就触发一次
     * @discussion 当 leftDuration 为 0 时，SDK 会将本地该消息和远端该消息删除，聊天页面需要将该消息从消息列表移除
     */
    onMessageDestructing(message: Message, leftDuration: number): any;
    /**
     * 阅后即焚停止
     * @param message 消息体
     * @discussion 调用 messageStopDestruct() 触发
     */
    onMessageDestructionStop(message: Message): any;
}
export { MessageDestructionListener };
