// @keepTs
// @ts-nocheck
import { Conversation, ConversationIdentifier, ConversationType, IAsyncResult, ICountOption, ISetConversationTopOption, Message, MessageFlag, PushNotificationLevel, SyncConversationReadStatusListener, ReceivedStatus, IQuietHoursOption } from '@rongcloud/imlib';
import { ConversationService } from '../../conversation/ConversationService';
import { IMessageItemProvider } from '../../conversation/item/provider/IMessageItemProvider';
import { UiMessage } from '../../conversation/model/UiMessage';
import ArrayList from '@ohos.util.ArrayList';
import List from '@ohos.util.List';
import { ConversationConfig } from '../../conversation/config/ConversationConfig';
import { MessageClickListener } from '../../conversation/listener/MessageClickListener';
import { ConversationEventListener } from '../../conversation/listener/ConversationEventListener';
import { IBoardPlugin } from '../../conversation/inputbar/component/plugin/IBoardPlugin';
import { ItemLongClickAction } from '../../base/click/ItemLongClickAction';
import { MessageMoreAction } from '../../conversation/more/MessageMoreAction';
import { HashMap } from '@kit.ArkTS';
import { IExtensionConfig } from '../../conversation/extension/IExtensionConfig';
import { IExtensionModule } from '../../conversation/extension/IExtensionModule';
import { ComponentIdentifier } from '../../base/enum/ComponentIdentifier';
import { ConversationContentComponentConfig, InputAreaComponentConfig, WatermarkComponentConfig } from '../../conversation/config/ComponentConfig';
import { IEmoticonTab, WatermarkPageIdentifier } from '../../../../../Index';
export declare class InnerConversationServiceImpl implements ConversationService {
    private providerCache;
    private messageActionArray;
    private config;
    private clickListeners;
    private eventListeners;
    private moreActions;
    private messageGroupReceiptCountViewBuilder;
    private messageGroupReceiptStatusViewBuilder;
    private messagePrivateReceiptStatusViewBuilder;
    private messagePrivateSentStatusViewBuilder;
    private inputAreaComponentMap;
    private conversationContentComponentMap;
    private messageBubbleComponentMap;
    private watermarkComponentMap;
    constructor();
    onInit(b122: Context, c122: string): void;
    private addMessageItemProviders;
    private addBoardPlugins;
    private loadDefaultLongAction;
    private loadLongAction;
    /**
     * 判断是否是阅后即焚消息
     * @param message
     * @returns
     */
    private isDestruct;
    private loadDefaultMoreAction;
    private loadMoreAction;
    setConversationConfig(l120: ConversationConfig): void;
    getConversationConfig(): ConversationConfig;
    /**
     * 获取会话置顶状态
     * @param conId
     * @returns
     */
    getConversationTopStatus(d120: ConversationIdentifier): Promise<IAsyncResult<boolean>>;
    /**
     * 获取消息免打扰状态
     * @param conId
     * @returns
     */
    getConversationNotificationLevel(v119: ConversationIdentifier): Promise<IAsyncResult<PushNotificationLevel>>;
    /**
     * 获取单个会话
     * @param conId 会话标识
     * @returns 会话数据
     * @see ConversationIdentifier
     */
    getConversation(u119: ConversationIdentifier): Promise<IAsyncResult<Conversation>>;
    /**
     * 获取本地会话中 @ 自己的未读消息列表
     * @param conId 会话标识
     * @param option 配置
     * @returns 返回本地消息结果
     * @see ConversationIdentifier
     * @see ICountOption
     */
    getUnreadMentionedMessages(s119: ConversationIdentifier, t119: ICountOption): Promise<IAsyncResult<List<Message>>>;
    /**
     * 获取会话里第一条未读消息
     * @param conId 会话标识
     * @returns 消息，如果该会话没有未读，返回 null
     * @see ConversationIdentifier
     */
    getFirstUnreadMessage(r119: ConversationIdentifier): Promise<IAsyncResult<Message>>;
    /**
     * 会话未读数，是否包含免打扰会话的未读数
     * @param typeList 会话类型数组
     * @param isContainBlocked 是否包含免打扰；true 代表获取所有会话未读数之和； false 代表获取不包含免打扰会话的正常会话未读数之和
     * @returns 未读数
     * @discussion 正常单聊会话 A 的未读数为1，免打扰单聊会话 B 的未读数为 2。true 代表获取两个单聊会话的未读数之和，其结果为 3。false 代表获取正常会话 A 的未读数，结果为 1
     */
    getUnreadCountByTypes(p119: List<ConversationType>, q119: boolean): Promise<IAsyncResult<number>>;
    /**
     * 获取单个会话的未读数
     * @param conId 会话标识
     * @returns 该会话的未读数
     * @version 1.0.0
     */
    getUnreadCount(o119: ConversationIdentifier): Promise<IAsyncResult<number>>;
    addConversationEventListener(n119: ConversationEventListener): void;
    removeConversationEventListener(m119: ConversationEventListener): void;
    getConversationEventListeners(): ArrayList<ConversationEventListener>;
    addMessageClickListener(l119: MessageClickListener): void;
    /**
     * 移除点击事件
     */
    removeMessageClickListener(k119: MessageClickListener): void;
    getMessageClickListeners(): ArrayList<MessageClickListener>;
    addMessageItemProvider(i119: string, j119: IMessageItemProvider<object>): void;
    removeMessageItemProvider(h119: string): void;
    getMessageItemProvider(g119: string): IMessageItemProvider<UiMessage>;
    addMessageItemLongClickAction(f119: ItemLongClickAction<Message>): void;
    addMessageItemLongClickActionByIndex(d119: number, e119: ItemLongClickAction<Message>): void;
    removeMessageItemLongClickAction(a119: string): void;
    clearMessageItemLongClickAction(): void;
    findMessageItemLongClickActionIndex(x118: string): number;
    getMessageItemLongClickActionArray(): Array<ItemLongClickAction<Message>>;
    /**
     * 同步会话已读状态
     */
    syncConversationReadStatus(v118: ConversationIdentifier, w118: number): Promise<IAsyncResult<void>>;
    addSyncConversationReadStatusListener(u118: SyncConversationReadStatusListener): void;
    removeSyncConversationReadStatusListener(t118: SyncConversationReadStatusListener): void;
    addBoardPlugin(s118: IBoardPlugin): void;
    insertBoardPlugin(q118: number, r118: IBoardPlugin): void;
    replaceBoardPlugin(o118: number, p118: IBoardPlugin): void;
    removeBoardPlugin(n118: IBoardPlugin): void;
    removeBoardPluginByName(m118: string): void;
    clearBoardPlugin(): void;
    setBoardPluginView(k118: string, l118: WrappedBuilder<[
        Context,
        ConversationIdentifier
    ]> | null): void;
    addEmoticonTab(j118: IEmoticonTab): void;
    removeEmoticonTabByName(i118: string): void;
    clearAllEmoticonTab(): void;
    getEmoticonTabList(): List<IEmoticonTab>;
    addExtensionModule(h118: IExtensionModule): void;
    registerExtensionModule(g118: IExtensionModule): void;
    unregisterExtensionModule(f118: IExtensionModule): void;
    getExtensionModules(): List<IExtensionModule>;
    setExtensionConfig(e118: IExtensionConfig): void;
    getExtensionConfig(): IExtensionConfig;
    setMessageGroupReceiptCountView(d118: WrappedBuilder<[
        Context,
        UiMessage
    ]>): void;
    getMessageGroupReceiptCountView(): WrappedBuilder<[
        Context,
        UiMessage
    ]> | null;
    setMessageGroupReceiptStatusView(c118: WrappedBuilder<[
        Context,
        UiMessage
    ]>): void;
    getMessageGroupReceiptStatusView(): WrappedBuilder<[
        Context,
        UiMessage
    ]> | null;
    setMessagePrivateReceiptStatusView(b118: WrappedBuilder<[
        Context,
        UiMessage
    ]>): void;
    getMessagePrivateReceiptStatusView(): WrappedBuilder<[
        Context,
        UiMessage
    ]> | null;
    setMessagePrivateSentStatusView(a118: WrappedBuilder<[
        Context,
        UiMessage
    ]>): void;
    getMessagePrivateSentStatusView(): WrappedBuilder<[
        Context,
        UiMessage
    ]> | null;
    setInputAreaComponentConfig(y117: InputAreaComponentConfig): void;
    getInputAreaComponentConfig(): HashMap<ComponentIdentifier, InputAreaComponentConfig>;
    setConversationContentComponentConfig(w117: ConversationContentComponentConfig): void;
    getConversationContentComponent(u117: ComponentIdentifier): WrappedBuilder<[
        import("../../conversation/config/ComponentConfig").ConversationContentComponentData
    ]>;
    private setMessageBubbleComponentConfig;
    getMessageBubbleComponentConfig(q117: ComponentIdentifier): WrappedBuilder<[
        import("../../conversation/config/ComponentConfig").MessageBubbleComponentData
    ]>;
    /**
     * 设置水印组件
     * 注意：目前水印组件只在合并转发页面生效。
     */
    setWatermarkComponentConfig(o117: WatermarkComponentConfig): void;
    getWatermarkComponentConfig(k117: WatermarkPageIdentifier): WrappedBuilder<[
        import("../../conversation/config/ComponentConfig").WatermarkComponentData
    ]>;
    /**
     * 清空某个会话内的所有未读数
     * @param conversationId 会话信息
     * @returns 清空结果
     */
    clearMessagesUnreadStatus(d117: ConversationIdentifier): Promise<IAsyncResult<void>>;
    /**
     * 清除单个会话的未读数：按照时间戳清除
     * @param conId 会话标识
     * @param time 时间，清理小于该时间戳的消息未读
     * @returns 结果
     */
    clearMessagesUnreadStatusByTime(v116: ConversationIdentifier, w116: number): Promise<IAsyncResult<void>>;
    /**
     * 批量删除会话
     */
    removeConversations(o116: List<ConversationIdentifier>): Promise<IAsyncResult<void>>;
    setConversationsToTop(f116: List<ConversationIdentifier>, g116: ISetConversationTopOption): Promise<IAsyncResult<void>>;
    setConversationsNotificationLevel(s115: List<ConversationIdentifier>, t115: PushNotificationLevel): Promise<IAsyncResult<void>>;
    /**
     * 保存文本消息草稿
     */
    saveTextMessageDraft(k115: ConversationIdentifier, l115: string): Promise<IAsyncResult<void>>;
    /**
     * 获取某个会话内保存的草稿
     */
    getTextMessageDraft(f115: ConversationIdentifier): Promise<IAsyncResult<string>>;
    addMessageMoreAction(b115: MessageMoreAction): void;
    removeMessageMoreAction(y114: string): void;
    clearMessageMoreAction(): void;
    getMessageMoreActions(): Array<MessageMoreAction>;
    getMessageTypeMap(): HashMap<string, MessageFlag>;
    sendReadReceiptMessage(t114: ConversationIdentifier, u114: number): Promise<IAsyncResult<void>>;
    sendReadReceiptRequest(s114: Message): Promise<IAsyncResult<void>>;
    sendReadReceiptResponse(n114: ConversationIdentifier, o114: Array<Message>): Promise<IAsyncResult<List<Message>>>;
    downloadMediaMessageWithProgress(g114: number, e114: (g114: number, progress: number) => void): Promise<IAsyncResult<string>>;
    /**
     * 使用自定义方法下载媒体消息
     * # 示例代码
     *```
     * let messageId = 234;
     * RongIM.getInstance().conversationService().downloadMediaMessageWithDownloader(
     *   messageId, (messageId: number, progress: number) => {
     *   // 下载进度，取值范围 [0 , 100]
     * }, (transfer: MediaMessageTransfer) => {
     *   // 业务侧下载后，调用 transfer 回传给SDK进度、成功、失败、取消
     *   // 回传进度
     *   // transfer.updateProgress(progress)
     *   // 回传成功
     *   // transfer.success("业务侧下载后的本地地址")
     *   // 回传失败
     *   // transfer.error()
     *   // 回传取消
     *   // transfer.cancel()
     * }).then(result => {
     *   if (EngineError.Success === result.code) {
     *     // 本地路径
     *     let localPath = result.data as string;
     *   }
     * });
     *```
     * @param messageId 消息 id，对应的消息必须是 MediaMessageContent 子类
     * @param progressListener 下载进度监听，取值范围 [0 , 100]
     * @param downloadCallback [可选参数]自定义媒体文件下载回调，如果此参数不传则执行SDK默认流程
     * @returns 媒体消息下载成功的本地路径，存储路径见 InitOption.mediaSavePath
     * @note 调用该接口下载成功后，消息的本地路径会保存数据库中；相同的消息重复下载，会直接返回本地路径
     * @note SDK 下载媒体过程中，调用了 cancelDownloadMediaMessage() ，该方法会返回 EngineError.RequestCanceled
     * @version 1.4.3
     */
    private downloadMediaMessageWithDownloader;
    /**
     * 下载媒体文件
     * ## 示例代码
     * ```
     * // uniqueId 是下载媒体文件任务的标识，可以用来发起下载、暂停下载和取消下载。
     * // 与 pauseDownloadMediaFile 和 cancelDownloadFile 中的 uniqueId 保持一致。
     * let uniqueId = "123"
     * let remoteUrl = "https://expamle.com/1.jpg";
     * let fileName = "1.jpg";
     * RongIM.getInstance().conversationService().downloadMediaFile(uniqueId, remoteUrl, fileName,
     *     (uniqueId: string) => {
     *         // 开始下载，下载唯一标识 uniqueId
     *       },
     *     (uniqueId: string, progress: number) => {
     *         // 下载进度
     *     }).then(result => {
     *      if (EngineError.Success !== result.code) {
     *          // 下载失败
     *          return;
     *        }
     *      if (!result.data) {
     *          // 下载的本地路径为空
     *          return;
     *        }
     *      // 本地路径
     *      let localPath = result.data as string;
     *    });
     * ```
     * @param uniqueId  文件唯一标示
     * @param remoteUrl 文件的远端地址
     * @param fileName  本地文件名
     * @param progressListener 下载进度监听，取值范围 [0 , 100]
     * @returns 媒体文件下载成功的本地路径，存储路径见 InitOption.mediaSavePath
     * @note 调用该接口下载成功后，消息的本地路径会保存数据库中；相同的消息重复下载，会直接返回本地路径
     * @note SDK 下载媒体过程中，调用了 cancelDownloadFile() ，该方法会返回 EngineError.RequestCanceled
     * @version 1.4.0
     */
    downloadMediaFile(u113: string, n113: string, o113: string, p113: (u113: string) => void, q113: (u113: string, progress: number) => void): Promise<IAsyncResult<string>>;
    cancelDownloadMediaMessage(l113: number): Promise<IAsyncResult<void>>;
    setMessageReceivedStatus(j113: number, k113: ReceivedStatus): Promise<IAsyncResult<void>>;
    /**
     * 停止播放语音
     */
    stopPlayVoice(): void;
    setNotificationQuietHoursLevel(i113: IQuietHoursOption): Promise<IAsyncResult<void>>;
    getNotificationQuietHoursLevel(): Promise<IAsyncResult<IQuietHoursOption>>;
    removeNotificationQuietHours(): Promise<IAsyncResult<void>>;
}
