import { Context } from '@kit.AbilityKit';
import { InitOption } from '../InitOption';
import { ConnectionStatus, HardwareResourceType } from '../MacroDefine';
import { Message } from '../message/Message';
import { ReceivedInfo } from '../message/model/ReceivedInfo';
/**
 * module 注解，使用该注解的类必须被创建过，该注解才能够生效
 * @version 1.2.0
 */
declare function ExtensionModuleTag(): (clazz: any) => void;
/**
 * 扩展模块协议，用于 SDK 内置模块的数据同步，例如将 IM 数据传给 RTC SDK
 *```
 * 要实现扩展需要满足如下
 * 1. 实现该抽象类
 * 2. 实现 ExtensionModuleTag 注解
 * 3. 对扩展类的构造要求如下，才能被正常创建
 *  3.1. 如果是单例类，必须实现 getInstance() 方法：建议
 *  3.2. 如果是普通类，必须实现无参的构造方法：不建议
 *```
 * @note TS 判断一个类是否实现了 interface 比较难，但是判断是否实现了抽象类比较简单
 * @warning SDK 仅会加载内置的模块，如果三方需要想实现该功能，请联系融云
 * @version 1.2.0
 */
declare abstract class IMLibExtensionModule {
    /**
     * IM SDK 已初始化完成
     * @param context 上下文
     * @param appKey  AppKey
     * @param initOption  初始化配置
     */
    abstract onInit(context: Context, appKey: string, initOption: InitOption): void;
    /**
     * 连接开始。调用 connect 方法连接开始时触发
     * @param token 使用的 IM token
     * @param deviceId 设备 id，使用 aaid 当做 deviceID
     */
    abstract onConnect(token: string, deviceId: string): void;
    /**
     * 连接成功。调用 connect 方法连接成功时触发
     * @param userId 当前用户 id
     */
    abstract onConnected(userId: string): void;
    /**
     * module 是否拦截该消息
     * @param msg 消息体
     * @param info 接收消息
     * @returns true: 由 module 处理该消息，IMLib 将不再处理该消息； false：module 不处理该消息，IMLib 将继续处理该消息
     */
    abstract didHoldReceivedMessage(msg: Message, info: ReceivedInfo): boolean;
    /**
     * rtc 配置更新
     *```
     * voipCallInfo 是导航下发的原始数据
     * {
     *   "logServer" : "https://logServer",
     *   "dataCenter" : "BJ001",
     *   "jwtToken" : "jwtToken",
     *   "openGzip" : true|false,
     *   "voipCallInfo" : "{"strategy":1,"callEngine":[{"engineType":4,"mediaServer":"https://mediaServer","maxStreamCount":30,"wwise":1,"detectorManager":"https://detectorManager"}]}",
     * }
     *```
     * @param rtcConf rtc 配置
     */
    abstract onRtcConfigUpdate(rtcConf: Map<string, Object>): void;
    /**
     * 连接状态发生变更，重连时的各种状态会触发该方法
     * @param status
     */
    abstract onConnectionStatusChanged(status: ConnectionStatus): void;
    /**
     * 断开连接。用户主动调用 disconnect 方法时触发
     * @param isReceivePush 是否接收推送
     */
    abstract onDisconnect(isReceivePush: boolean): void;
    /**
     * 将该模块的 SDK 信息返回 IMLib，可以同时返回多个 SDK 的版本号
     * @returns 版本信息， key : SDK 名称; value : SDK 版本号
     * @warning SDK 名称必须和现有 iOS Android 的命名一致，不能随便写
     * @version 1.3.0
     */
    abstract getSdkVersion(): Map</* sdkName */ string, /* sdkVersion */ string>;
    /**
     * 应用切换到后台后被系统挂起
     * @version 1.4.3
     */
    abstract onAppHangUp(): void;
    /**
     * 应用切换到前台
     * @version 1.4.3
     */
    abstract onAppForeground(): void;
    /**
     * 应用切换到后台
     * @version 1.4.3
     */
    abstract onAppBackground(): void;
    /**
     * 请求硬件资源是否被其他的 sdk 占用
     *```
     * 其他 sdk 内部实现：
     * 所有的实现该抽象类的 sdk，需要根据自己的实际情况返回对应的值
     * 例如 sdk 没有音视频录制的功能，直接返回 false
     * 例如 kit 在录制视频的时候，返回 true ；录制完成返回 false。rtc 同样的逻辑，音视频通话中返回 true，否则返回 false
     *
     * 其他 sdk 调用 InnerEngineImpl.getInstance().isOnRequestHardwareResource() ：
     * 返回 true 代表有其他 sdk 正在使用该硬件，调用方不能使用该硬件。false 代表没有 sdk 使用该硬件
     * 举例：
     * kit 录制视频前调用该方法返回了 true，说明可能 rtc 正在音视频通话，那么 kit 需要给用户提示不能录制视频。
     * rtc 发起音视频通话前调用该方法返回了 true，说明可能 kit 正在录制视频，那么 rtc 需要给用户提示不能开启音视频通话
     *```
     * @param type 硬件资源类型
     * @returns 是否正常获取
     * @note 为了保证接口兼容，该方法默认实现，其他模块按需实现。
     * @version 1.7.1
     */
    onRequestHardwareResource(type: HardwareResourceType): boolean;
}
export { IMLibExtensionModule, ExtensionModuleTag };
