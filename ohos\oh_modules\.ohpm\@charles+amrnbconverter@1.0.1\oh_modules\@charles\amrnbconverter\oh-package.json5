{"name": "@charles/amrnbconverter", "version": "1.0.1", "description": "This library enables the bidirectional conversion between AMR_NB and PCM audio formats, facilitating easier audio processing and manipulation.", "main": "Index.ets", "author": "charles", "email": "<EMAIL>", "license": "Apache-2.0", "homepage": "https://github.com/619216696/amrConverterDemo", "dependencies": {"libamrconverter.so": "file:./src/main/cpp/types/libamrconverter"}, "metadata": {"sourceRoots": ["./src/main"]}, "compatibleSdkVersion": 12, "compatibleSdkType": "HarmonyOS", "obfuscated": false, "nativeComponents": [{"name": "libamrconverter.so", "compatibleSdkVersion": 12, "compatibleSdkType": "HarmonyOS"}]}