import { Context } from '@ohos.abilityAccessCtrl';
import { MultiBitratesMode } from '../IListPlayer';
export declare class ListPlayerBase {
    private mNativeContext;
    private getNativeContext;
    private setNativeContext;
    constructor(j22: Context, k22: number, l22: number, m22: boolean);
    stop(): void;
    removeSource(i22: string): void;
    clear(): void;
    getCurrentUid(): string;
    setPreloadCount(h22: number): void;
    setPreloadCountWithPrevAndNext(f22: number, g22: number): void;
    setPreloadScene(e22: number): void;
    enablePreloadStrategy(c22: number, d22: boolean): void;
    setPreloadStrategyParam(a22: number, b22: string): void;
    setMaxPreloadMemorySizeMB(z21: number): void;
    getMaxPreloadMemorySizeMB(): number;
    SetMultiBitratesMode(y21: MultiBitratesMode): void;
    GetMultiBitratesMode(): MultiBitratesMode;
    release(): void;
}
