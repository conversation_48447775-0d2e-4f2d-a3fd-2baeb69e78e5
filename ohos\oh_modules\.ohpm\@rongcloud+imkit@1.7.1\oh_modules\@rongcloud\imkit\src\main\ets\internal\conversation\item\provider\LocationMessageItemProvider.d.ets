// @keepTs
// @ts-nocheck
/**
 * Created on 2024/09/13
 * <AUTHOR>
 * 位置消息 UI界面
 */
import { LocationMessage } from '@rongcloud/imlib';
import { UiMessage } from '../../../../conversation/model/UiMessage';
import { BaseMessageItemProvider } from '../../../../conversation/item/provider/BaseMessageItemProvider';
export declare class LocationMessageItemProvider extends BaseMessageItemProvider<LocationMessage> {
    getMessageWrapBuilder(): WrappedBuilder<[
        Context,
        UiMessage,
        number
    ]>;
    isShowSummaryName(o186: Context, p186: LocationMessage): boolean;
    getSummaryTextByMessageContent(k186: Context, l186: LocationMessage): Promise<MutableStyledString>;
}
@Builder
export declare function bindLocationMessageData(z185: Context, a186: UiMessage, b186: number): void;
@Component
export declare struct LocationMessageView {
    @Require
    @Prop
    context: Context;
    @Require
    @Prop
    index: number;
    @ObjectLink
    uiMessage: UiMessage;
    private messageContent;
    aboutToAppear(): void;
    build(): void;
    private bubbleBorderRadius;
    private poiBorderRadius;
}
