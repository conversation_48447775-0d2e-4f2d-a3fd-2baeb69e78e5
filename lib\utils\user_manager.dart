import 'package:wuling_flutter_app/models/global_data.dart';
import 'package:wuling_flutter_app/api/car_api.dart';
import 'package:wuling_flutter_app/utils/sp_util.dart';
import 'package:wuling_flutter_app/constant/storage.dart';

/// 用户管理类
/// 提供用户相关的业务逻辑，包括车主状态检查等
class User {
  static final User _instance = User._internal();
  
  factory User.shareInstance() => _instance;
  
  User._internal();

  /// 检查当前用户是否为车主
  /// 返回true表示是车主用户，false表示非车主用户
  Future<bool> isCarOwnerUser() async {
    try {
      // 首先检查是否已登录
      if (!GlobalData().isLogin) {
        print('用户未登录，返回非车主状态');
        return false;
      }

      // 检查本地是否有缓存的车辆信息
      final userIdStr = GlobalData().userModel?.userIdStr;
      if (userIdStr == null || userIdStr.isEmpty) {
        print('用户ID为空，无法检查本地缓存，返回非车主状态');
        return false;
      }

      String key = '${SP_USER_DEFAULT_CAR_KEY}_$userIdStr';
      var carInfoMap = SpUtil().getJSON(key);
      
      if (carInfoMap != null) {
        // 本地有车辆信息，说明是车主
        print('本地存在车辆信息，判定为车主用户');
        return true;
      }

      // 本地没有车辆信息，通过网络接口检查
      try {
        final carStatusResponse = await carAPI.getDefaultCarInfoAndStatus();
        final carInfo = carStatusResponse?.carInfo;

        if (carInfo != null && (carInfo.vin?.isNotEmpty ?? false)) {
          // 有车辆信息，是车主
          print('网络接口返回车辆信息，判定为车主用户');

          // 安全地缓存车辆信息到本地
          try {
            final carInfoJson = carInfo.toJson();
            if (carInfoJson != null) {
              SpUtil().setJSON(key, carInfoJson);
              GlobalData().carInfoModel = carInfo;
            }
          } catch (jsonError) {
            print('缓存车辆信息失败: $jsonError');
          }

          return true;
        } else {
          // 没有车辆信息，不是车主
          print('网络接口未返回车辆信息，判定为非车主用户');
          return false;
        }
      } catch (e) {
        // 网络请求失败，默认返回false
        print('检查车主状态网络请求失败: $e，默认返回非车主状态');
        return false;
      }
      
    } catch (e) {
      print('检查车主状态异常: $e，默认返回非车主状态');
      return false;
    }
  }

  /// 清除用户相关的缓存数据
  Future<void> clearUserCache() async {
    try {
      // 清除车辆信息缓存
      final userIdStr = GlobalData().userModel?.userIdStr;
      if (userIdStr != null && userIdStr.isNotEmpty) {
        String key = '${SP_USER_DEFAULT_CAR_KEY}_$userIdStr';
        await SpUtil().remove(key);
      }
      
      // 清除全局车辆信息
      GlobalData().carInfoModel = null;
      
      print('用户缓存数据已清除');
    } catch (e) {
      print('清除用户缓存数据失败: $e');
    }
  }

  /// 刷新车主状态
  /// 强制从网络重新获取车主状态，不使用缓存
  Future<bool> refreshCarOwnerStatus() async {
    try {
      // 清除本地缓存
      await clearUserCache();
      
      // 重新检查车主状态
      return await isCarOwnerUser();
    } catch (e) {
      print('刷新车主状态失败: $e');
      return false;
    }
  }

  /// 获取当前用户的车辆信息
  /// 优先从缓存获取，缓存不存在时从网络获取
  Future<Map<String, dynamic>?> getUserCarInfo() async {
    try {
      // 首先检查本地缓存
      final userIdStr = GlobalData().userModel?.userIdStr;
      if (userIdStr != null && userIdStr.isNotEmpty) {
        String key = '${SP_USER_DEFAULT_CAR_KEY}_$userIdStr';
        var carInfoMap = SpUtil().getJSON(key);

        if (carInfoMap != null) {
          print('从本地缓存获取车辆信息');
          return carInfoMap;
        }
      }

      // 本地没有缓存，从网络获取
      try {
        final carStatusResponse = await carAPI.getDefaultCarInfoAndStatus();
        final carInfo = carStatusResponse?.carInfo;

        if (carInfo != null) {
          print('从网络获取车辆信息');
          try {
            return carInfo.toJson();
          } catch (jsonError) {
            print('车辆信息序列化失败: $jsonError');
            return null;
          }
        }
      } catch (e) {
        print('从网络获取车辆信息失败: $e');
      }

      return null;
    } catch (e) {
      print('获取用户车辆信息异常: $e');
      return null;
    }
  }
}
