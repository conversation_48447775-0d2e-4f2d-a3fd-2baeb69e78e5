// @keepTs
// @ts-nocheck
import { ConversationIdentifier } from "../../../../../Index";
import { KeyboardType } from "../conversation/inputbar/enum/KeyboardType";
/**
 * IMKit 运行时缓存类
 */
export declare class IMKitCache {
    private static _instance;
    static getInstance(): IMKitCache;
    /**
     * 会话键盘状态缓存
     */
    private conKeyboardTypeMap;
    private getConversionKey;
    /**
     * 获取输入栏的状态
     * @param con
     * @returns
     */
    getInputBoxMode(s294: ConversationIdentifier): KeyboardType;
    /**
     * 设置输入栏状态
     * @param con
     * @param type
     */
    setInputBoxMode(q294: ConversationIdentifier, r294: KeyboardType): void;
}
