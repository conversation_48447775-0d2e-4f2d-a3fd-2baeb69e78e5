// @keepTs
// @ts-nocheck
/**
 * Created on 2024/8/9
 * <AUTHOR>
 */
import { Message } from '@rongcloud/imlib';
import { UserInfoModel } from '../../user/model/UserInfoModel';
/**
 * 聊天页面消息各种点击事件监听
 * @version 1.0.0
 */
export interface MessageClickListener {
    /**
     * 点击头像
     * @param message 被点击的消息体
     * @param userId 用户 Id
     * @param context Context (1.4.3版本新增参数)
     * @param event 点击事件 (1.4.3版本新增参数)
     * @warning 如果存在多个 Listener，只要有1个 Listener 实现了该方法且返回 true，SDK 则不再处理该事件。
     * @returns 是否处理该事件。实现该方法并且返回 true 代表 app 处理该点击事件，SDK 不再处理该点击事件。不实现该方法或者返回 false，代表由 SDK 处理点击事件。
     */
    onMessagePortraitClick?: (message: Message, userId: string, context?: Context, event?: ClickEvent) => boolean;
    /**
     * 长按头像，一般用于 @ 消息
     * @param message 被长按的消息体
     * @param user 用户信息
     * @param context Context (1.4.3版本新增参数)
     * @param event 长按事件 (1.4.3版本新增参数)
     * @warning 如果存在多个 Listener，只要有1个 Listener 实现了该方法且返回 true，SDK 则不再处理该事件。
     * @returns 是否处理该事件。实现该方法并且返回 true 代表 app 处理该点击事件，SDK 不再处理该点击事件。不实现该方法或者返回 false，代表由 SDK 处理点击事件。
     */
    onMessagePortraitLongClick?: (message: Message, user: UserInfoModel, context?: Context, event?: GestureEvent) => boolean;
    /**
     * 点击消息
     * @param message 被点击的消息体
     * @param context Context (1.4.3版本新增参数)
     * @param event 点击事件 (1.4.3版本新增参数)
     * @warning 如果存在多个 Listener，只要有1个 Listener 实现了该方法且返回 true，SDK 则不再处理该事件。
     * @returns 是否处理该事件。实现该方法并且返回 true 代表 app 处理该点击事件，SDK 不再处理该点击事件。不实现该方法或者返回 false，代表由 SDK 处理点击事件。
     */
    onMessageClick?: (message: Message, context?: Context, event?: ClickEvent) => boolean;
    /**
     * 长按消息
     * @param message 被长按的消息体
     * @param context Context (1.4.3版本新增参数)
     * @param event 长按事件 (1.4.3版本新增参数)
     * @warning 如果存在多个 Listener，只要有1个 Listener 实现了该方法且返回 true，SDK 则不再处理该事件。
     * @returns 是否处理该事件。实现该方法并且返回 true 代表 app 处理该点击事件，SDK 不再处理该点击事件。不实现该方法或者返回 false，代表由 SDK 处理点击事件。
     */
    onMessageLongClick?: (message: Message, context?: Context, event?: GestureEvent) => boolean;
    /**
     * 重新编译（撤回消息）
     * @param message 消息体
     * @param context Context (1.4.3版本新增参数)
     * @param event 点击事件 (1.4.3版本新增参数)
     * @warning 如果存在多个 Listener，只要有1个 Listener 实现了该方法且返回 true，SDK 则不再处理该事件。
     * @returns 是否处理该事件。实现该方法并且返回 true 代表 app 处理该点击事件，SDK 不再处理该点击事件。不实现该方法或者返回 false，代表由 SDK 处理点击事件。
     */
    onMessageRecallEditClick?: (message: Message, context?: Context, event?: ClickEvent) => boolean;
    /**
     * 点击消息内的链接
     * @param message 被点击的消息体
     * @param url 链接
     * @param context Context (1.4.3版本新增参数)
     * @param event 点击事件 (1.4.3版本新增参数)
     * @discussion 由鸿蒙系统识别的链接，可能会与 iOS Android 平台不完全一致。详细参考 textProcessing.getEntity
     * @warning 如果存在多个 Listener，只要有1个 Listener 实现了该方法且返回 true，SDK 则不再处理该事件。
     * @returns 是否处理该事件。实现该方法并且返回 true 代表 app 处理该点击事件，SDK 不再处理该点击事件。不实现该方法或者返回 false，代表由 SDK 处理点击事件。
     */
    onMessageLinkClick?: (message: Message, url: string, context?: Context, event?: ClickEvent) => boolean;
    /**
     * 点击消息内的手机号
     * @param message 被点击的消息体
     * @param phone 手机号
     * @param context Context (1.4.3版本新增参数)
     * @param event 点击事件 (1.4.3版本新增参数)
     * @discussion 由鸿蒙系统识别的手机号，可能会与 iOS Android 平台不完全一致。详细参考 textProcessing.getEntity
     * @warning 如果存在多个 Listener，只要有1个 Listener 实现了该方法且返回 true，SDK 则不再处理该事件。
     * @returns 是否处理该事件。实现该方法并且返回 true 代表 app 处理该点击事件，SDK 不再处理该点击事件。不实现该方法或者返回 false，代表由 SDK 处理点击事件。
     */
    onMessagePhoneClick?: (message: Message, phone: string, context?: Context, event?: ClickEvent) => boolean;
    /**
     * 点击消息内的邮箱地址
     * @param message 被点击的消息体
     * @param email 邮箱地址
     * @param context Context (1.4.3版本新增参数)
     * @param event 点击事件 (1.4.3版本新增参数)
     * @discussion 由鸿蒙系统识别的邮箱地址，可能会与 iOS Android 平台不完全一致。详细参考 textProcessing.getEntity
     * @warning 如果存在多个 Listener，只要有1个 Listener 实现了该方法且返回 true，SDK 则不再处理该事件。
     * @returns 是否处理该事件。实现该方法并且返回 true 代表 app 处理该点击事件，SDK 不再处理该点击事件。不实现该方法或者返回 false，代表由 SDK 处理点击事件。
     */
    onMessageEmailClick?: (message: Message, email: string, context?: Context, event?: ClickEvent) => boolean;
}
