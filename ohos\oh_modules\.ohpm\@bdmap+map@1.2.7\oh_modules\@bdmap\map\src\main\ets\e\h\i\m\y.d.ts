import { LatLng } from "@bdmap/base"; import { ColorType, IPrismOption } from "../../g1/a2"; import BmBitmapResource from "../c2/f2/t3"; import ImageEntity from "../o/s"; import Overlay from "./m"; export default class Prism extends Overlay { private mPoints; private mHeight; private mTopFaceColor; private mSideFaceColor; private mCustomSideImage; private mBmPrism; private mGeoElement; private mTopSurfaceStyle; private mSideSurfaceStyle; constructor(w45: IPrismOption);           height(height: number): this;           setHeight(height: number): void;           getHeight(): number;           points(points: Array<LatLng>): this;           setPoints(points: Array<LatLng>): void;           getPoints(): LatLng[];           topFaceColor(topFaceColor: ColorType): this;           setTopFaceColor(topFaceColor: ColorType): void;           getTopFaceColor(): ColorType;           sideFaceColor(sideFaceColor: ColorType): this;           setSideFaceColor(sideFaceColor: ColorType): void;           getSideFaceColor(): ColorType;           customSideImage(o45: ImageEntity): this;       updateBitMap(bitmap: BmBitmapResource): void;           setCustomSideImage(n45: ImageEntity): void;           getCustomSideImage(): ImageEntity; } 