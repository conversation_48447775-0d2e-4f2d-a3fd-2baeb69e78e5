//
//  Generated code. Do not modify.
//  source: sgmwE300VehicleCarStatus.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use sgmwE300VehicleCarStatusDescriptor instead')
const SgmwE300VehicleCarStatus$json = {
  '1': 'SgmwE300VehicleCarStatus',
  '2': [
    {'1': 'collectTime', '3': 1, '4': 1, '5': 3, '10': 'collectTime'},
    {'1': 'dipHeadLight', '3': 2, '4': 1, '5': 9, '10': 'dipHeadLight'},
    {'1': 'lowBeamLight', '3': 3, '4': 1, '5': 9, '10': 'lowBeamLight'},
    {'1': 'psWdronDgr', '3': 4, '4': 1, '5': 9, '10': 'psWdronDgr'},
    {'1': 'drWdronDgr', '3': 5, '4': 1, '5': 9, '10': 'drWdronDgr'},
    {'1': 'sntrModSetSts', '3': 6, '4': 1, '5': 9, '10': 'sntrModSetSts'},
    {'1': 'VecChrgStsIndOn1', '3': 7, '4': 1, '5': 9, '10': 'VecChrgStsIndOn1'},
    {'1': 'VecChrgStsIndOn2', '3': 8, '4': 1, '5': 9, '10': 'VecChrgStsIndOn2'},
    {'1': 'DDAjrSwAtv', '3': 9, '4': 1, '5': 9, '10': 'DDAjrSwAtv'},
    {'1': 'TDAjrSwAtv', '3': 10, '4': 1, '5': 9, '10': 'TDAjrSwAtv'},
    {'1': 'KyPstn', '3': 11, '4': 1, '5': 9, '10': 'KyPstn'},
    {'1': 'PsDoorOpenSwAct', '3': 12, '4': 1, '5': 9, '10': 'PsDoorOpenSwAct'},
    {'1': 'PDAjrSwAtv', '3': 13, '4': 1, '5': 9, '10': 'PDAjrSwAtv'},
    {'1': 'DrDoorOpenSwAct', '3': 14, '4': 1, '5': 9, '10': 'DrDoorOpenSwAct'},
    {'1': 'ACACButCntSt', '3': 15, '4': 1, '5': 9, '10': 'ACACButCntSt'},
    {'1': 'ACCntTmpOLfCntSt', '3': 16, '4': 1, '5': 9, '10': 'ACCntTmpOLfCntSt'},
    {'1': 'ACBLwLvlCntSt', '3': 17, '4': 1, '5': 9, '10': 'ACBLwLvlCntSt'},
    {'1': 'chargeLoVolBatSts', '3': 18, '4': 1, '5': 9, '10': 'chargeLoVolBatSts'},
    {'1': 'RRDoorOpenSwAct', '3': 19, '4': 1, '5': 9, '10': 'RRDoorOpenSwAct'},
    {'1': 'RSDAjrSwAtv', '3': 20, '4': 1, '5': 9, '10': 'RSDAjrSwAtv'},
    {'1': 'RLDoorOpenSwAct', '3': 21, '4': 1, '5': 9, '10': 'RLDoorOpenSwAct'},
    {'1': 'LSDAjrSwAtv', '3': 22, '4': 1, '5': 9, '10': 'LSDAjrSwAtv'},
    {'1': 'ACPwrModCntSt', '3': 23, '4': 1, '5': 9, '10': 'ACPwrModCntSt'},
    {'1': 'SecRwLtWdwOpenDgr', '3': 24, '4': 1, '5': 9, '10': 'SecRwLtWdwOpenDgr'},
    {'1': 'SecRwRtWdwOpenDgr', '3': 25, '4': 1, '5': 9, '10': 'SecRwRtWdwOpenDgr'},
    {'1': 'PLGSysMSt', '3': 26, '4': 1, '5': 9, '10': 'PLGSysMSt'},
    {'1': 'drSeatVentRCC', '3': 27, '4': 1, '5': 9, '10': 'drSeatVentRCC'},
    {'1': 'prSeatVentRCC', '3': 28, '4': 1, '5': 9, '10': 'prSeatVentRCC'},
    {'1': 'drSeatHeatRCC', '3': 29, '4': 1, '5': 9, '10': 'drSeatHeatRCC'},
    {'1': 'prSeatHeatRcc', '3': 30, '4': 1, '5': 9, '10': 'prSeatHeatRcc'},
    {'1': 'rlSeatVentRCC', '3': 31, '4': 1, '5': 9, '10': 'rlSeatVentRCC'},
    {'1': 'rrSeatVentRCC', '3': 32, '4': 1, '5': 9, '10': 'rrSeatVentRCC'},
    {'1': 'rlSeatHeatRCC', '3': 33, '4': 1, '5': 9, '10': 'rlSeatHeatRCC'},
    {'1': 'rrSeatHeatRcc', '3': 34, '4': 1, '5': 9, '10': 'rrSeatHeatRcc'},
  ],
};

/// Descriptor for `SgmwE300VehicleCarStatus`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List sgmwE300VehicleCarStatusDescriptor = $convert.base64Decode(
    'ChhTZ213RTMwMFZlaGljbGVDYXJTdGF0dXMSIAoLY29sbGVjdFRpbWUYASABKANSC2NvbGxlY3'
    'RUaW1lEiIKDGRpcEhlYWRMaWdodBgCIAEoCVIMZGlwSGVhZExpZ2h0EiIKDGxvd0JlYW1MaWdo'
    'dBgDIAEoCVIMbG93QmVhbUxpZ2h0Eh4KCnBzV2Ryb25EZ3IYBCABKAlSCnBzV2Ryb25EZ3ISHg'
    'oKZHJXZHJvbkRnchgFIAEoCVIKZHJXZHJvbkRnchIkCg1zbnRyTW9kU2V0U3RzGAYgASgJUg1z'
    'bnRyTW9kU2V0U3RzEioKEFZlY0NocmdTdHNJbmRPbjEYByABKAlSEFZlY0NocmdTdHNJbmRPbj'
    'ESKgoQVmVjQ2hyZ1N0c0luZE9uMhgIIAEoCVIQVmVjQ2hyZ1N0c0luZE9uMhIeCgpEREFqclN3'
    'QXR2GAkgASgJUgpEREFqclN3QXR2Eh4KClREQWpyU3dBdHYYCiABKAlSClREQWpyU3dBdHYSFg'
    'oGS3lQc3RuGAsgASgJUgZLeVBzdG4SKAoPUHNEb29yT3BlblN3QWN0GAwgASgJUg9Qc0Rvb3JP'
    'cGVuU3dBY3QSHgoKUERBanJTd0F0dhgNIAEoCVIKUERBanJTd0F0dhIoCg9EckRvb3JPcGVuU3'
    'dBY3QYDiABKAlSD0RyRG9vck9wZW5Td0FjdBIiCgxBQ0FDQnV0Q250U3QYDyABKAlSDEFDQUNC'
    'dXRDbnRTdBIqChBBQ0NudFRtcE9MZkNudFN0GBAgASgJUhBBQ0NudFRtcE9MZkNudFN0EiQKDU'
    'FDQkx3THZsQ250U3QYESABKAlSDUFDQkx3THZsQ250U3QSLAoRY2hhcmdlTG9Wb2xCYXRTdHMY'
    'EiABKAlSEWNoYXJnZUxvVm9sQmF0U3RzEigKD1JSRG9vck9wZW5Td0FjdBgTIAEoCVIPUlJEb2'
    '9yT3BlblN3QWN0EiAKC1JTREFqclN3QXR2GBQgASgJUgtSU0RBanJTd0F0dhIoCg9STERvb3JP'
    'cGVuU3dBY3QYFSABKAlSD1JMRG9vck9wZW5Td0FjdBIgCgtMU0RBanJTd0F0dhgWIAEoCVILTF'
    'NEQWpyU3dBdHYSJAoNQUNQd3JNb2RDbnRTdBgXIAEoCVINQUNQd3JNb2RDbnRTdBIsChFTZWNS'
    'd0x0V2R3T3BlbkRnchgYIAEoCVIRU2VjUndMdFdkd09wZW5EZ3ISLAoRU2VjUndSdFdkd09wZW'
    '5EZ3IYGSABKAlSEVNlY1J3UnRXZHdPcGVuRGdyEhwKCVBMR1N5c01TdBgaIAEoCVIJUExHU3lz'
    'TVN0EiQKDWRyU2VhdFZlbnRSQ0MYGyABKAlSDWRyU2VhdFZlbnRSQ0MSJAoNcHJTZWF0VmVudF'
    'JDQxgcIAEoCVINcHJTZWF0VmVudFJDQxIkCg1kclNlYXRIZWF0UkNDGB0gASgJUg1kclNlYXRI'
    'ZWF0UkNDEiQKDXByU2VhdEhlYXRSY2MYHiABKAlSDXByU2VhdEhlYXRSY2MSJAoNcmxTZWF0Vm'
    'VudFJDQxgfIAEoCVINcmxTZWF0VmVudFJDQxIkCg1yclNlYXRWZW50UkNDGCAgASgJUg1yclNl'
    'YXRWZW50UkNDEiQKDXJsU2VhdEhlYXRSQ0MYISABKAlSDXJsU2VhdEhlYXRSQ0MSJAoNcnJTZW'
    'F0SGVhdFJjYxgiIAEoCVINcnJTZWF0SGVhdFJjYw==');

