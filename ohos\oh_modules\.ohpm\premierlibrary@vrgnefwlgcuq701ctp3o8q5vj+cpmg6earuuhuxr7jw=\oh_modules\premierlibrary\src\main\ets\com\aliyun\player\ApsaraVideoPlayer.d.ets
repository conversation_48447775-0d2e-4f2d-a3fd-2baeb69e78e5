import { AliPlayer } from './AliPlayer';
import { NativePlayerBase } from './nativeclass/NativePlayerBase';
import { UrlVideoPlayer } from './UrlVideoPlayer';
import { Context } from '@ohos.abilityAccessCtrl';
export declare class ApsaraVideoPlayer extends UrlVideoPlayer implements AliPlayer {
    constructor(e11: Context, f11: string);
    /**
     * 设置auth源
     *
     * @param auth auth源
     */
    setVidAuthDataSource(c11: object): void;
    /**
     * 设置sts源
     *
     * @param sts sts源
     */
    setVidStsDataSource(a11: object): void;
    /**
     * 设置mps源
     *
     * @param mps mps源
     */
    setVidMpsDataSource(y10: object): void;
    /**
     * 设置liveSts源
     *
     * @param liveSts liveSts源
     */
    setLiveStsDataSource(w10: object): void;
    updateVidAuth(u10: object): void;
    updateStsInfo(s10: object): void;
    protected createAlivcMediaPlayer(q10: Context): NativePlayerBase;
}
