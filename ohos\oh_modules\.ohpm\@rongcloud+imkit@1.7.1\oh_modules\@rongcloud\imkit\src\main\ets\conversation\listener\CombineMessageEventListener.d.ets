// @keepTs
// @ts-nocheck
/**
 * 合并转发消息相关的事件监听
 * @version 1.4.0
 */
export interface CombineMessageEventListener {
    /**
     * 合并转发消息预览页面的位置消息点击事件回调
     * @param latitude 纬度, double 类型
     * @param longitude 经度, double 类型
     * @param locationName 地理位置的名称
     */
    onLocationMessageClick?: (latitude: number, longitude: number, locationName: string) => void;
    /**
     * 合并转发WebView的JS回调原生的拦截器。此拦截接口优先级高于 onLocationMessageClick。
     *
     * SDK 返回的 jsJson 数据说明
     *```
     *文件
     *  type = "RC:FileMsg"
     *  fileName = "文件.pdf"
     *  fileSize = "123456"
     *  fileType = "pdf"
     *  fileUrl = "文件地址"
     *地图
     *  type = "RC:LBSMsg"
     *  latitude = "纬度"
     *  locationName = "地理位置"
     *  longitude = "经度"
     *合并转发
     *  type = "RC:CombineMsg"
     *  fileUrl = "文件下载地址"
     *  title = "标题"
     *手机号
     *  type = "phone"
     *  phoneNum = "13888888888"
     *超链接
     *  type = "link"
     *  link = "超链接"
     *图片
     *  type = "RC:ImgMsg"
     *  fileUrl = "图片地址"
     *  imgUrl = "缩略图base64"
     *视频
     *  type = "RC:SightMsg"
     *  duration = "5"
     *  fileUrl = "视频地址"
     *  imageBase64 = "缩略图base64"
     *Gif
     *  type = "RC:GIFMsg"
     *  fileUrl = "Gif地址"
     *```
     *
     * 使用示例
     *```
     * let combineMessageEventListener: CombineMessageEventListener = {
     *  onJSCallNativeInterceptor: (jsData: string): boolean => {
     *    let jsonObject: object = JSON.parse(jsData);
     *    // 开发者根据 jsonObject 里的值来判断开发者是否需要处理，以及是否需要SDK处理。
     *    if (jsonObject["CUSTOM:MSG"]) {
     *      // 开发者处理跳转逻辑，则返回true，由SDK处理跳转逻辑则返回false。
     *      return true;
     *    }
     *    return false;
     *  }
     * }
     * RongIM.getInstance().messageService().addCombineMessageEventListener(combineMessageEventListener)
     *```
     *
     * @params jsJson 合并转发Html通过JS透传过来的Json数据
     * @return false 代表SDK继续执行JS事件，true 代表SDK不需再执行JS事件。
     * @since 1.7.0
     */
    onJSCallNativeInterceptor?: (jsJson: string) => Promise<boolean>;
}
