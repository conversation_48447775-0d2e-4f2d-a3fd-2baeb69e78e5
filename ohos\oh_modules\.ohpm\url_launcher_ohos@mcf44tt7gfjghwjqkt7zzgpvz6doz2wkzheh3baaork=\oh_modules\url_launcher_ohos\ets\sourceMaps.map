{"url_launcher_ohos|url_launcher_ohos|1.0.0|build/default/generated/profile/default/ModuleInfo.js": {"version": 3, "file": "ModuleInfo.ts", "sources": ["ohos/build/default/generated/profile/default/ModuleInfo.ts"], "names": [], "mappings": "AACA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EAAE,CAAA,CAAA;AACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EAAE,CAAA,CAAA;", "entry-package-info": "url_launcher_ohos|1.0.0"}, "url_launcher_ohos|url_launcher_ohos|1.0.0|index.ts": {"version": 3, "file": "index.ets", "sourceRoot": "", "sources": ["ohos/index.ets"], "names": [], "mappings": "OAAO,iBAAiB;AACxB,eAAe,iBAAiB,CAAC", "entry-package-info": "url_launcher_ohos|1.0.0"}, "url_launcher_ohos|url_launcher_ohos|1.0.0|src/main/ets/components/plugin/InAppBrowser.ts": {"version": 3, "file": "InAppBrowser.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/plugin/InAppBrowser.ets"], "names": [], "mappings": ";;;;;IAWE,iBAAiB,GAAE,WAAW,CAAC,iBAAiB;IAExC,GAAG,GAAG,MAAM;IACZ,gBAAgB,GAAE,OAAO;IACzB,gBAAgB,GAAE,OAAO;;OAX5B,WAAW;OACX,MAAM;cACJ,SAAS,IAAT,SAAS;AAIlB,MAAM,OAAQ,YAAY;IAF1B;;;;;iCAGqD,IAAI,WAAW,CAAC,iBAAiB,EAAE;;gCAGlD,IAAI;gCACJ,IAAI;;;KATD;;;;;;;;;;;;;;;;;;;;;;;IAKvC,2BAAmB,WAAW,CAAC,iBAAiB,CAAsC;IAEtF,OAAO,IAAI,CAAC,EAAE,MAAM,CAAC;IACrB,OAAO,mBAAmB,OAAO,CAAQ;IACzC,OAAO,mBAAmB,OAAO,CAAQ;IAEzC,aAAa;QACX,IAAI,MAAM,GAAG,MAAM,CAAC,SAAS,EAAE,IAAI,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,CAAA;QACzD,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,MAAM,CAAC;QACnC,IAAI,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;QAC1D,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,kBAAkB,CAAC,IAAI,OAAO,CAAC;QAC/D,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,kBAAkB,CAAC,IAAI,OAAO,CAAC;IACjE,CAAC;IAED;;YACE,GAAG;YAAH,GAAG,CAID,KAAK,CAAC,MAAM;YAJd,GAAG,CAIa,MAAM,CAAC,MAAM;;;YAH3B,GAAG,QAAC,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,UAAU,EAAE,IAAI,CAAC,iBAAiB,EAAE;YAAzD,GAAG,CACA,gBAAgB,CAAC,IAAI,CAAC,gBAAgB;YADzC,GAAG,CAEA,gBAAgB,CAAC,IAAI,CAAC,gBAAgB;;QAH3C,GAAG;KAKJ", "entry-package-info": "url_launcher_ohos|1.0.0"}, "url_launcher_ohos|url_launcher_ohos|1.0.0|src/main/ets/components/plugin/Messages.ts": {"version": 3, "file": "Messages.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/plugin/Messages.ets"], "names": [], "mappings": "YAMO,YAAY;OACZ,oBAAoB;cAClB,eAAe;cACf,UAAU;AAGnB,MAAM,GAAG,GAAG,aAAa,CAAA;AAEzB,MAAM,OAAO,YAAa,SAAQ,KAAK;IACrC,sBAAsB;IACtB,MAAM,CAAC,IAAI,EAAE,MAAM,GAAG,EAAE,CAAC;IAEzB,wEAAwE;IACxE,MAAM,CAAC,OAAO,EAAE,MAAW,IAAI,CAAC;IAEhC,YAAY,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,KAAU;QAC1D,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;CACF;AAED,MAAM,UAAU,SAAS,CAAC,SAAS,EAAE,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC;IACxD,IAAI,SAAS,GAAG,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC;IACpC,IAAI,SAAS,YAAY,YAAY,EAAE;QACrC,IAAI,KAAK,GAAG,SAAS,IAAI,YAAY,CAAC;QACtC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC3B,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC9B,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;KAC/B;SAAM;QACL,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC/B,SAAS,CAAC,IAAI,CACZ,SAAS,GAAG,SAAS,CAAC,OAAO,GAAG,gBAAgB,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;KACvE;IACD,OAAO,SAAS,CAAC;AACnB,CAAC;AAED;;;GAGG;AACH,MAAM,OAAO,cAAc;IACzB,OAAO,CAAC,gBAAgB,EAAE,OAAO,GAAG,KAAK,CAAC;IAE1C,MAAM,CAAC,mBAAmB,IAAI,OAAO;QACnC,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B,CAAC;IAED,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,OAAO,GAAG,IAAI;QAClD,IAAI,SAAS,IAAI,IAAI,EAAE;YACrB,MAAM,IAAI,cAAc,CAAC,6CAA6C,CAAC,CAAC;SACzE;QACD,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;IACpC,CAAC;IAED,OAAO,CAAC,gBAAgB,EAAE,OAAO,GAAG,KAAK,CAAC;IAE1C,MAAM,CAAC,mBAAmB,IAAI,OAAO;QACnC,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B,CAAC;IAED,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,OAAO;QAC3C,IAAI,SAAS,IAAI,IAAI,EAAE;YACrB,MAAM,IAAI,cAAc,CAAC,6CAA6C,CAAC,CAAC;SACzE;QACD,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC;IACpC,CAAC;IAED,OAAO,CAAC,OAAO,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC;IAEjE,MAAM,CAAC,UAAU,IAAI,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;QACtC,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED,MAAM,CAAC,UAAU,CAAC,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC;QAC9C,IAAI,SAAS,IAAI,IAAI,EAAE;YACrB,MAAM,IAAI,cAAc,CAAC,oCAAoC,CAAC,CAAC;SAChE;QACD,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;IAC3B,CAAC;IAED,MAAM,IAAI,KAAK,KAAU;QACvB,IAAI,YAAY,GAAG,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC;QACvC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACzC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QACzC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAChC,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,cAAc;QAClD,IAAI,MAAM,GAAG,IAAI,cAAc,EAAE,CAAC;QAClC,IAAI,gBAAgB,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC;QAC1C,MAAM,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;QAC7C,IAAI,gBAAgB,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC;QAC1C,MAAM,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,CAAC;QAC7C,IAAI,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACtB,MAAM,CAAC,UAAU,CAAC,OAAO,IAAI,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC;QAClD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,qEAAqE;IACrE,OAAO,CAAC,cAAc,KAAI,CAAC;CAC5B;AAED,MAAM,OAAO,mBAAoB,SAAQ,oBAAoB;IAC3D,MAAM,CAAC,MAAM,CAAC,QAAQ,EAAE,mBAAmB,GAAG,IAAI,mBAAmB,EAAE,CAAC;IAExE,OAAO;QACL,KAAK,EAAE,CAAC;IACV,CAAC;IAED,eAAe,CAAC,IAAI,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU,GAAG,MAAM;QACvD,QAAQ,IAAI,EAAE;YACZ,KAAK,GAAG;gBACN,OAAO,cAAc,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;YAC3E;gBACE,OAAO,KAAK,CAAC,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;SAC9C;IACH,CAAC;IAED,UAAU,CAAC,MAAM,EAAE,UAAU,EAAE,KAAK,KAAU,GAAG,IAAI;QACnD,IAAI,KAAK,YAAY,cAAc,EAAE;YACnC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;YACtB,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,KAAK,IAAI,cAAc,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC;SAC7D;aAAM;YACL,KAAK,CAAC,UAAU,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;SACjC;IACH,CAAC;CACF;AAED,MAAM,WAAW,cAAc;IAC7B,0DAA0D;IAC1D,YAAY,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO,CAAC;IAEnC,8DAA8D;IAC9D,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,OAAO,CAAC;IAE9D,mFAAmF;IACnF,gBAAgB,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,GAAG,OAAO,CAAC;IAEhE,iEAAiE;IACjE,YAAY,IAAI,OAAO,CAAC;IAExB,wCAAwC;IACxC,QAAQ,IAAI,YAAY,KAAU,CAAC;IAEnC,gGAAgG;IAChG,KAAK,CAAC,eAAe,EAAE,eAAe,GAAG,IAAI,EAAE,GAAG,EAAE,cAAc,GAAG,IAAI,OAAY;CACtF", "entry-package-info": "url_launcher_ohos|1.0.0"}, "url_launcher_ohos|url_launcher_ohos|1.0.0|src/main/ets/components/plugin/UrlLauncher.ts": {"version": 3, "file": "UrlLauncher.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/plugin/UrlLauncher.ets"], "names": [], "mappings": "cAMS,eAAe;YACjB,YAAY;OACZ,mBAA8B;cAAP,KAAK;YAC5B,IAAI;OACJ,YAAY;OACZ,OAAO;OACP,EAAE,YAAY,EAAkB,mBAAmB,EAAkB,SAAS,EAAE;cAAhE,cAAc,EAAuB,cAAc;YACnE,MAAM;OACN,GAAG;OACH,IAAI;OACJ,IAAI;OACJ,iBAAkC;cAAb,WAAW;OAChC,aAAa;OACb,MAAM;OACN,YAAY;cACV,aAAa,IAAb,aAAa;AAEtB,MAAM,GAAG,GAAG,aAAa,CAAA;AAEzB,MAAM,iBAAiB;IACrB,SAAS,EAAE,MAAM,GAAG,EAAE,CAAC;IACvB,eAAe,EAAE,MAAM,GAAG,EAAE,CAAC;IAC7B,WAAW,EAAE,MAAM,GAAG,EAAE,CAAC;CAC1B;AAED,MAAM,OAAO,WAAY,YAAW,cAAc;IAChD,MAAM,CAAC,eAAe,EAAE,MAAM,GAAG,MAAM,CAAC;IACxC,MAAM,CAAC,oBAAoB,EAAE,MAAM,GAAG,OAAO,CAAC;IAC9C,MAAM,CAAC,qBAAqB,EAAE,MAAM,GAAG,QAAQ,CAAC;IAChD,MAAM,CAAC,kBAAkB,EAAE,MAAM,GAAG,SAAS,CAAC;IAC9C,MAAM,CAAC,eAAe,EAAE,MAAM,GAAG,MAAM,CAAC;IACxC,MAAM,CAAC,gBAAgB,EAAE,MAAM,GAAG,OAAO,CAAC;IAC1C,MAAM,CAAC,uBAAuB,EAAE,MAAM,GAAG,QAAQ,CAAC;IAElD,MAAM,CAAC,eAAe,GAAG,cAAc,CAAC;IACxC,MAAM,CAAC,gBAAgB,GAAG,0BAA0B,CAAC;IACrD,MAAM,CAAC,YAAY,GAAG,oBAAoB,CAAC;IAE3C,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,gBAAgB,CAAC;IAEzC,YAAY,GAAG,EAAE,MAAM,CAAC,gBAAgB;QACtC,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC;IACrB,CAAC;IAED,aAAa;QACX,IAAI,KAAK,EAAE,KAAK,CAAC,WAAW,CAAC,GAAG;YAC9B,4BAA4B;YAC5B,6BAA6B;YAC7B,gCAAgC;YAChC,iCAAiC;YACjC,sCAAsC;SACvC,CAAC;QAEF,IAAI,SAAS,GAAG,iBAAiB,CAAC,eAAe,EAAE,CAAC;QACpD,+CAA+C;QAC/C,SAAS,CAAC,0BAA0B,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE;YACtE,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC;YACzC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,OAAO,GAAG,IAAI,CAAC,CAAC;YAC3B,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,mBAAmB,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;YACnD,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;QAChD,CAAC,EAAE,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;YAChB,GAAG,CAAC,CAAC,CAAC,GAAG,EAAC,yBAAyB,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;IACL,CAAC;IAED,YAAY,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO;QAChC,IAAI,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;QAChC,+FAA+F;QAC/F,IAAI,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,eAAe,CAAC;YAClD,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,oBAAoB,CAAC;YACvD,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,qBAAqB,CAAC;YACxD,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,kBAAkB,CAAC;YACrD,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,eAAe,CAAC;YAClD,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,gBAAgB,CAAC;YACnD,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,uBAAuB,CAAC,EAAE;YAC9D,OAAO,IAAI,CAAC;SACb;QAED,IAAI;YACF,mEAAmE;YACnE,4JAA4J;YAC5J,IAAI,aAAa,CAAC,WAAW,CAAC,UAAU,CAAC,EAAE;gBACzC,OAAO,IAAI,CAAC;aACb;YACD,OAAO,KAAK,CAAC;SACd;QAAC,OAAO,KAAK,EAAE;YACd,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,qBAAqB,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;YACzD,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAED,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,OAAO;QAC3D,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,EAAE;YAC3B,OAAO,KAAK,CAAC;SACd;QAED,IAAI,CAAC,aAAa,EAAE,CAAC;QACrB,IAAI,UAAU,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;QAChC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,iBAAiB,GAAG,GAAG,GAAG,SAAS,GAAG,UAAU,CAAC,CAAC;QAC7D,IAAI,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,eAAe,CAAC,EAAE;YACtD,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;SAC5B;aAAM,IAAI,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,oBAAoB,CAAC;YACvD,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,qBAAqB,CAAC,EAAE;YACnE,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;SACrC;aAAM,IAAI,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,kBAAkB,CAAC,EAAE;YAChE,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;SAC7B;aAAM,IAAI,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,eAAe,CAAC,EAAE;YAC7D,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;SAC5B;aAAM,IAAI,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,gBAAgB,CAAC,EAAE;YAC9D,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;SAC7B;aAAM,IAAI,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,uBAAuB,CAAC,EAAE;YACrE,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;SACnC;aAAM;YACL,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;SAC9B;IACH,CAAC;IAED,MAAM,CAAC,MAAM,EAAE,MAAM;QACnB,IAAI,KAAK,EAAE,MAAM,GAAG,IAAI,MAAM,CAAC,SAAS,CAAC,CAAC;QAC1C,IAAI,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;QAC5C,IAAI,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACvC,IAAI,iBAAiB,GAAE,IAAI,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;QAC7D,IAAI,aAAa,EAAC,OAAO,GAAG,iBAAiB,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;QACzE,IAAI,YAAY,GAAG,aAAa,CAAC,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;QACvF,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,SAAS,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO;QAC7B,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,WAAW,CAAC,eAAe,CAAC,CAAC;QAC/D,IAAI,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAEtC,IAAI,QAAQ,GAAG,IAAI,iBAAiB,EAAE,CAAC;QACvC,QAAQ,CAAC,SAAS,GAAG,QAAQ,CAAC;QAC9B,QAAQ,CAAC,eAAe,GAAG,SAAS,CAAC;QAErC,IAAI,MAAM,GAAG,IAAI,KAAK,CAAC,iBAAiB,GAAG,CAAC;QAC5C,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEtB,IAAI,UAAU,QAAa,EAAE,CAAC;QAC9B,UAAU,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACnD,UAAU,CAAC,QAAQ,GAAG,cAAc,CAAC;QACrC,IAAI,GAAG,EAAG,IAAI,GAAG;YACf,YAAY,EAAE,WAAW,CAAC,eAAe;YACzC,aAAa,EAAE,WAAW,CAAC,gBAAgB;YAC3C,YAAY,EAAE,UAAU;YACxB,UAAU,EAAE;gBACV,WAAW,CAAC,YAAY;aACzB;SACF,CAAC;QACF,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,oBAAoB,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAC;QACzD,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YACvC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC;QACnC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,KAAU,EAAE,EAAE;YAC3B,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,sBAAsB,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAA;QACF,OAAO,IAAI,CAAA;IACb,CAAC;IAED,SAAS,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO;QAC7B,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,YAAY,GAAG,GAAG,CAAC,CAAC;QAC/B,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,WAAW,CAAC,eAAe,GAAG,GAAG,CAAC,CAAC;QACrE,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YAChC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,KAAU,EAAE,EAAE;YACzB,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,6BAA6B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;QACjE,CAAC,CAAC,CAAA;QACF,OAAO,IAAI,CAAC;IACd,CAAC;IAED,UAAU;IACV,UAAU,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO;QAC9B,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,WAAW,CAAC,gBAAgB,GAAG,KAAK,CAAC,CAAC;QACvE,IAAI,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG,OAAO,CAAC;QAC/C,cAAc;QACd,IAAI,WAAW,GAAG,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QACnD,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,aAAa,GAAG,OAAO,GAAG,gBAAgB,GAAG,WAAW,CAAC,CAAC;QACrE,IAAI,IAAI,EAAE,IAAI,GAAG;YACf,KAAK,EAAE,YAAY,CAAC,KAAK,CAAC,8BAA8B,GAAG,YAAY,CAAC,KAAK,CAAC,6BAA6B;YAC3G,gBAAgB;YAChB,MAAM,EAAE,2BAA2B;YACnC,GAAG,EAAE,WAAW;SACjB,CAAA;QACD,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC;aAC5B,IAAI,CAAC,GAAG,EAAE;YACT,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,yCAAyC,CAAC,CAAC;QACxD,CAAC,CAAC;aACD,KAAK,CAAC,CAAC,GAAG,KAAU,EAAE,EAAE;YACvB,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,uCAAuC,GAAG,CAAC,IAAI,gBAAgB,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;QAC3F,CAAC,CAAC,CAAC;QACL,OAAO,IAAI,CAAC;IACd,CAAC;IAED,SAAS,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,OAAO;QAC3D,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,gBAAgB,GAAG,GAAG,CAAC,CAAC;QACnC,IAAI,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,GAAI,EAAE,CAAC;QAEzC,KAAK,IAAI,GAAG,IAAI,OAAO,EAAE;YACvB,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;SACxB;QAED,IAAI,IAAI,EAAE,IAAI,GAAG;YACf,GAAG,EAAE,GAAG;YACR,UAAU,EAAE,MAAM;YAClB,QAAQ,EAAE,CAAC,yBAAyB,CAAC;YACrC,MAAM,EAAE,2BAA2B;SACpC,CAAA;QACD,IAAI;YACF,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;SAChC;QAAC,OAAO,CAAC,EAAE;YACV,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,wBAAwB,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAA;YACxD,OAAO,KAAK,CAAA;SACb;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,UAAU,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO;QAC9B,IAAI,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE,WAAW,CAAC,kBAAkB,GAAG,GAAG,CAAC,CAAC;QAC3E,IAAI,IAAI,EAAE,IAAI,GAAG;YACf,MAAM,EAAE,6BAA6B;YACrC,GAAG,EAAE,WAAW;SACjB,CAAA;QACD,IAAI;YACF,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;SAChC;QAAC,OAAO,CAAC,EAAE;YACV,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,yBAAyB,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAA;YACzD,OAAO,KAAK,CAAA;SACb;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;OAKG;IACH,gBAAgB,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO;QACpC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,mBAAmB,GAAG,GAAG,CAAC,CAAC;QACtC,2EAA2E;QAC3E,IAAI,IAAI,EAAE,IAAI,GAAG;YACf,QAAQ,EAAE,4BAA4B;YACtC,KAAK,EAAE,GAAG;SACX,CAAC;QACF,IAAI;YACF,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;SAChC;QAAC,OAAO,CAAC,EAAE;YACV,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,+BAA+B,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAA;YAC/D,OAAO,KAAK,CAAA;SACb;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED,WAAW,CAAC,GAAG,EAAE,MAAM,GAAG,OAAO;QAC/B,IAAI,IAAI,EAAE,IAAI,GAAG;YACf,GAAG,EAAE,GAAG;SACT,CAAA;QACD,IAAI;YACF,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,CAAA;SAChC;QAAC,OAAO,CAAC,EAAE;YACV,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,0BAA0B,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAA;YAC1D,OAAO,KAAK,CAAA;SACb;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,QAAQ,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,GAAG,MAAM;QAC3C,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,SAAS,EAAE;YACnC,OAAO,EAAE,CAAA;SACV;QACD,OAAO,GAAG,CAAC,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;IACjC,CAAC;IAED,OAAO,CAAC,aAAa,IAAI,IAAI;QAC3B,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE;YACxB,MAAM,IAAI,YAAY,CACpB,aAAa,EAAE,iDAAiD,EAAE,IAAI,CAAC,CAAC;SAC3E;IACH,CAAC;IAED,gBAAgB,CAAC,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,GAAG,OAAO;QAC7D,IAAI,OAAO,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC;QACnC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,EAAE;YACxC,YAAY,CAAC,SAAS,CAAC;gBACrB,OAAO,EAAE,4EAA4E;aACtF,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;SACd;QAED,IAAI,kBAAkB,GAAG,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;QAC7D,MAAM,CAAC,OAAO,CAAC,EAAE,GAAG,EAAE,kBAAkB,EAAE,MAAM,EAAE,EAAC,KAAK,EAAE,GAAG,EAAE,SAAS,EAAE,OAAO,EAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,aAAa,EAAE,EAAE;YACnH,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,sBAAsB,EAAE,kBAAkB,EAAE,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC;YAC3F,IAAI,KAAK,CAAC,IAAI,IAAI,MAAM,EAAE;gBACxB,YAAY,CAAC,SAAS,CAAC;oBACrB,OAAO,EAAE,kBAAkB,GAAG,kCAAkC;iBACjE,CAAC,CAAC;aACJ;QACH,CAAC,CAAC,CAAC;QACH,OAAO,IAAI,CAAC;IACd,CAAC;IAED,YAAY,IAAI,OAAO;QACrB,MAAM,IAAI,KAAK,CAAC,yBAAyB,CAAC,CAAC;IAC7C,CAAC;IAED,QAAQ,IAAI,YAAY,KAAU;QAChC,OAAO,mBAAmB,CAAC,QAAQ,CAAC;IACtC,CAAC;IAED,KAAK,CAAC,eAAe,EAAE,eAAe,EAAE,GAAG,EAAE,cAAc;QACzD;YACE,IAAI,eAAe,IAAI,IAAI,EAAE;gBAC3B,OAAO;aACR;YACD,IAAI,OAAO,EAAE,mBAAmB,KAAU,GAAG,IAAI,mBAAmB,MAClE,eAAe,EAAE,gDAAgD,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YACtF,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAA;YAC7B,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,SAAS,EAAE;gBACnC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAA;gBAC/B,OAAO,CAAC,iBAAiB,CAAC;oBACxB,SAAS,CAAC,GAAG,KAAU,EAAE,KAAK,EAAE,KAAK,KAAU,GAAG,IAAI;wBACpD,IAAI,OAAO,GAAG,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC;wBAClC,IAAI,IAAI,GAAG,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;wBAChC,IAAI,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC;wBAC/B,IAAI;4BACF,IAAI,MAAM,GAAG,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,CAAC;4BACtC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;yBACtB;wBAAC,OAAO,GAAG,EAAE;4BACZ,IAAI,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;4BAC1B,OAAO,GAAG,IAAI,CAAC;yBAChB;wBACD,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;oBACtB,CAAC;iBACF,CAAC,CAAC;aACJ;iBAAM;gBACL,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA;aAChC;SACF;QACD;YACE,IAAI,OAAO,GAAG,IAAI,mBAAmB,CAAC,MAAM,EAC1C,eAAe,EAAE,6CAA6C,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YACnF,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAA;YAC7B,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,SAAS,EAAE;gBACnC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAA;gBAC/B,OAAO,CAAC,iBAAiB,CAAC;oBACxB,SAAS,CAAC,GAAG,KAAU,EAAE,KAAK,EAAE,KAAK,KAAU,GAAG,IAAI;wBACpD,IAAI,OAAO,GAAG,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC;wBAClC,IAAI,IAAI,GAAG,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;wBAChC,IAAI,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC;wBAC/B,IAAI,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;wBAChD,IAAI;4BACF,IAAI,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;4BAC/C,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;yBACtB;wBAAC,OAAO,GAAG,EAAE;4BACZ,IAAI,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;4BAC1B,OAAO,GAAG,IAAI,CAAC;yBAChB;wBACD,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;oBACtB,CAAC;iBACF,CAAC,CAAC;aACJ;iBAAM;gBACL,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA;aAChC;SACF;QACD;YACE,IAAI,OAAO,GAAG,IAAI,mBAAmB,CAAC,MAAM,EAC1C,eAAe,EAAE,oDAAoD,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YAC1F,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAA;YAC7B,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,SAAS,EAAE;gBACnC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAA;gBAC/B,OAAO,CAAC,iBAAiB,CAAC;oBACxB,SAAS,CAAC,GAAG,KAAU,EAAE,KAAK,EAAE,KAAK,KAAU,GAAG,IAAI;wBACpD,IAAI,OAAO,GAAG,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC;wBAClC,IAAI,IAAI,GAAG,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;wBAChC,IAAI,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC;wBAC/B,IAAI,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,cAAc,CAAC;wBAC3C,IAAI;4BACF,IAAI,MAAM,GAAG,GAAG,CAAC,gBAAgB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;4BACtD,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;yBACtB;wBAAC,OAAO,GAAG,EAAE;4BACZ,IAAI,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;4BAC1B,OAAO,GAAG,IAAI,CAAC;yBAChB;wBACD,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;oBACtB,CAAC;iBACF,CAAC,CAAC;aACJ;iBAAM;gBACL,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA;aAChC;SACF;QACD;YACE,IAAI,OAAO,GAAG,IAAI,mBAAmB,CAAC,MAAM,EAC1C,eAAe,EAAE,gDAAgD,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;YACtF,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAA;YAC7B,IAAI,GAAG,IAAI,IAAI,IAAI,GAAG,IAAI,SAAS,EAAE;gBACnC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAA;gBAC/B,OAAO,CAAC,iBAAiB,CAAC;oBACxB,SAAS,CAAC,GAAG,KAAU,EAAE,KAAK,EAAE,KAAK,KAAU,GAAG,IAAI;wBACpD,IAAI,OAAO,GAAG,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC;wBAClC,IAAI;4BACF,IAAI,MAAM,GAAG,GAAG,CAAC,YAAY,EAAE,CAAC;4BAChC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;yBACtB;wBAAC,OAAO,GAAG,EAAE;4BACZ,IAAI,IAAI,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;4BAC1B,OAAO,GAAG,IAAI,CAAC;yBAChB;wBACD,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;oBACtB,CAAC;iBACF,CAAC,CAAC;aACJ;iBAAM;gBACL,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAA;aAChC;SACF;IACH,CAAC;CACF", "entry-package-info": "url_launcher_ohos|1.0.0"}, "url_launcher_ohos|url_launcher_ohos|1.0.0|src/main/ets/components/plugin/UrlLauncherPlugin.ts": {"version": 3, "file": "UrlLauncherPlugin.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/plugin/UrlLauncherPlugin.ets"], "names": [], "mappings": "YAMO,YAAY;cAEjB,oBAAoB;cAGpB,aAAa,EACb,oBAAoB;cAEb,cAAc,QAAQ,YAAY;OACpC,EAAE,WAAW,EAAE;AAEtB,MAAM,GAAG,GAAG,mBAAmB,CAAA;AAE/B,MAAM,CAAC,OAAO,OAAO,iBAAkB,YAAW,aAAa,EAAE,YAAY;IAE3E,OAAO,CAAC,aAAa,EAAE,oBAAoB,GAAG,IAAI,GAAG,IAAI,CAAC;IAC1D,OAAO,CAAC,cAAc,EAAE,cAAc,GAAG,IAAI,GAAG,IAAI,CAAC;IAErD,kBAAkB,IAAI,MAAM;QAC1B,OAAO,mBAAmB,CAAA;IAC5B,CAAC;IAED,mBAAmB,CAAC,OAAO,EAAE,oBAAoB,GAAG,IAAI;QACtD,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,sBAAsB,CAAC,CAAA;QAC1C,IAAI,CAAC,cAAc,GAAG,IAAI,WAAW,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,OAAO,CAAC,CAAC;QACpE,IAAI,CAAC,cAAc,CAAC,KAAK,CACvB,IAAI,CAAC,aAAa,EAAE,kBAAkB,EAAE,IAAI,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,CAAC;IAC3E,CAAC;IACD,qBAAqB,IAAI,IAAI;QAC3B,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IACzC,CAAC;IAED,kBAAkB,CAAC,OAAO,EAAE,oBAAoB,GAAG,IAAI;QACrD,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAA;QACxC,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC;IAC/B,CAAC;IAGD,oBAAoB,CAAC,OAAO,EAAE,oBAAoB,GAAG,IAAI;QACvD,IAAI,CAAC,cAAc,EAAE,KAAK,CAAC,IAAI,CAAC,aAAa,EAAE,kBAAkB,EAAE,IAAI,IAAI,EAAE,IAAI,CAAC,CAAC;QACnF,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IAC5B,CAAC;CACF", "entry-package-info": "url_launcher_ohos|1.0.0"}}