syntax = "proto3";

// 燃油车辆状态，从车上传过来的状态
message SgmwVehicleCarStatus {
  int64 time = 1; //状态更新时间，ms时间戳
  int32 carControlType = 2; //整车控制方式 0=正常模式 1=远程模式 2=蓝牙钥匙控制
  string vin = 3; //车辆vin
  map<string, double> carLocation = 4; //车辆位置数据 WGS84坐标系 ("latitude":11.22,"longitude":11.22)
  /**
   * 补电状态
   * 0：默认
   * 1：补电中
   * 2：补电完成
   * 3：补电失败
   */
  int32 supplementPowerStatus = 5;
  int32 airConditioningStatus = 6; //空调电源模式状态
  int32 driverDoorLockStatus = 7; //驾驶员侧门锁开关状态
  int32 tailgateTouchStatus = 8; //尾门门碰开关状态
  int32 engineStart = 9; //发动机运转状态
  int32 skylightControlStatus = 10; //电动天窗控制状态
  int32 driverWindowControlStatus = 11; //驾驶员侧电动窗控制状态
  int32 copilotWindowControlStatus = 12; //副驾侧电动窗控制状态
  int32 rearLeftWindowControlStatus = 13; //左后侧电动窗控制状态
  int32 rearRightWindowControlStatus = 14; //右后侧电动窗控制状态
  int32 driverSeatHeatControlStatus = 15; //驾驶员侧座椅加热控制状态
  int32 engineCoverStatus = 16; //发动机罩开启指示
  int32 acTemperature = 17; //空调温度
}