// @keepTs
// @ts-nocheck
export declare class CommonConstants {
    static readonly DEFAULT_MESSAGE_COUNT: number;
    static readonly MAX_MESSAGE_LENGTH_TO_SEND: number;
    static readonly TEXT_DESTRUCT_TIME: number;
    static readonly VOICE_DESTRUCT_TIME: number;
    static readonly IMAGE_DESTRUCT_TIME: number;
    static readonly SIGHT_DESTRUCT_TIME: number;
    static readonly DESTRUCT_MODE: string;
    static readonly LOCAL_DESTRUCT_MODE: string;
    static readonly CLICK_NEW_RECEIVED_UNREAD_MESSAGE: string;
    static readonly CLICK_UNREAD_MESSAGE: string;
    static readonly CLICK_UNREAD_MENTIONED_MESSAGE: string;
    static readonly CLICK_REFERENCED_MESSAGE_CONTENT: string;
    static readonly CHANGE_INPUT_TEXT_AREA_CONTENT: string;
}
/**
 * EventHub用来发送事件的Key
 * @version 1.0.0
 */
export declare enum EventHubKey {
    /**
     * 群聊中长按头像触发输入框自动填充 '@' + 用户名 的逻辑
     */
    AitChangeEvent = "AitChangeEvent",
    /**
     * 消息撤回后，重编辑按钮被点击的事件
     */
    RecallEditClickEvent = "RecallEditClickEvent",
    /**
     * 阅后即焚消息被撤回的事件
     */
    DestructRecallMessageEvent = "DestructRecallMessageEvent",
    /**
     * 阅后即焚消息准备销毁的事件
     */
    DestructMessageClickEvent = "DestructMessageClickEvent",
    /**
     * 语音录制相关事件
     */
    AVRecorderEvent = "AVRecorderEvent",
    /**
     * 语音播放相关事件
     */
    AVPlayerEvent = "AVPlayerEvent",
    /**
     * EmoticonBoard相关事件
     */
    EmoticonBoardEvent = "EmoticonBoardEvent",
    /**
     * EmoticonBoardTab相关事件
     */
    EmoticonBoardTabEvent = "EmoticonBoardTabEvent",
    /**
     * 会话页面相关事件
     */
    IConversationViewModelEvent = "IConversationViewModelEvent"
}
