import List from '@ohos.util.List';
import { MediaMessageContent, ConversationType } from '@rongcloud/imlib';
/**
 * 消息标识
 * @version 1.4.0
 */
declare const CombineMessageObjectName: string;
/**
 * 合并转发消息
 * @version 1.4.0
 */
declare class CombineMessage extends MediaMessageContent {
    /**
     * 标题
     */
    title: string;
    /**
     * 会话类型
     */
    conversationType: ConversationType;
    /**
     * 单聊会话的聊天消息成员名字列表。单聊里最多有两个,群聊不记录
     */
    nameList: Array<string>;
    /**
     * 默认消息的内容
     */
    summaryList: Array<string>;
    /**
     * 无参构造方法
     */
    constructor();
    /**
     * 编码方法，将消息转为 json 字符串
     * @returns json 字符串
     */
    encode(): string;
    /**
     * 解码方法，将 json 字符串转为消息
     * @param contentString json 字符串
     */
    decode(e357: string): void;
    /**
     * 获取类名
     * @returns 类名
     */
    getClassName(): string;
    /**
     * 实现搜索方法
     * @returns 需要搜索的内容
     */
    getSearchableWord(): List<string> | null;
}
export { CombineMessage, CombineMessageObjectName };
