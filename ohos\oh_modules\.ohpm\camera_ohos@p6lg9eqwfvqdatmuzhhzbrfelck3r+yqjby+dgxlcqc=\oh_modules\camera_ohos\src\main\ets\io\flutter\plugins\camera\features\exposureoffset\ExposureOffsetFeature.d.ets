import { CameraProperties } from '../../CameraProperties';
import { CameraFeature } from '../CameraFeature';
import camera from '@ohos.multimedia.camera';
export declare class ExposureOffsetFeature extends CameraFeature<Number> {
    private currentSetting;
    constructor(cameraProperties: CameraProperties);
    getDebugName(): string;
    getValue(): Number;
    checkIsSupported(): boolean;
    setValue(value: number, captureSession: camera.PhotoSession | camera.VideoSession): void;
    getMinExposureOffset(captureSession: camera.PhotoSession | camera.VideoSession): number;
    getMaxExposureOffset(captureSession: camera.PhotoSession | camera.VideoSession): number;
    getExposureOffsetStepSize(captureSession: camera.PhotoSession | camera.VideoSession): number;
}
