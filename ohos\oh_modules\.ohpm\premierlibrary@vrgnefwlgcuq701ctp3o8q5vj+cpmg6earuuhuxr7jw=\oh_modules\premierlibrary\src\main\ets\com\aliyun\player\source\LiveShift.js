export class LiveShift {
    constructor() {
        this.mUrl = "";
        this.mTimeLineUrl = "";
        this.mFormat = "m3u8";
        this.mCoverPath = "";
        this.mTitle = "";
    }
    setFormat(o33) {
        this.mFormat = o33;
    }
    getFormat() {
        return this.mFormat;
    }
    getUrl() {
        return this.mUrl;
    }
    setUrl(n33) {
        this.mUrl = n33;
    }
    getCoverPath() {
        return this.mCoverPath;
    }
    setCoverPath(m33) {
        this.mCoverPath = m33;
    }
    getTitle() {
        return this.mTitle;
    }
    setTitle(l33) {
        this.mTitle = l33;
    }
    getTimeLineUrl() {
        return this.mTimeLineUrl;
    }
    setTimeLineUrl(k33) {
        this.mTimeLineUrl = k33;
    }
}
