export default class BleDevice {
    mDeviceName: string;
    mDeviceId: string;
    mScanRecord: Uint8Array;
    mRssi: number;
    mTimestampNanos: number;
    connectable: boolean;
    mTxPowerLevel: number;
    mServiceUuids: string[];
    mManufactureData: Map<number, string>;
    constructor(deviceId: string, name: string, rssi?: number, scanRecord?: Uint8Array, timestampNanos?: number, connectable?: boolean);
    static copy(device: BleDevice): BleDevice;
    getName(): string;
    setName(name: string): void;
    getMac(): string;
    getKey(): string;
    getConnectable(): boolean;
    setConnectable(connectable: boolean): void;
    getScanRecord(): Uint8Array;
    setScanRecord(scanRecord: Uint8Array): void;
    getRssi(): number;
    setRssi(rssi: number): void;
    getTimestampNanos(): number;
    setTimestampNanos(timestampNanos: number): void;
    getTxPowerLevel(): number;
    getServiceUuids(): string[];
    getManufactureData(): Map<number, string>;
    private parseScanResult;
    private parseServiceUuid;
    private parseServiceSolicitationUuid;
    private getUuidFromUint8Array;
    private parseServiceData;
    private parseManufactureData;
}
