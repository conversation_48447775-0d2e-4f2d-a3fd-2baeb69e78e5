// @keepTs
// @ts-nocheck
/**
 * Created on 2024/07/15
 * <AUTHOR>
 */
import { ConnectionStatus, Message, ReceivedInfo } from '@rongcloud/imlib';
import { UiMessage } from '../../../conversation/model/UiMessage';
import { ConversationComponentData } from '../../../conversation/model/ConversationComponentData';
import { IBusinessState } from '../state/IBusinessState';
import { IProcessor } from './IProcessor';
type DataResult = (localData: UiMessage[]) => void;
export declare class BaseMessageProcessor implements IProcessor {
    /**
     * 默认为 Normal
     */
    protected businessState: IBusinessState;
    onInit(o263: ConversationComponentData, p263?: DataResult): void;
    onReceiveMessage(k263: Message, l263: ReceivedInfo): void;
    onLoadMore(i263: ConversationComponentData, j263?: DataResult): void;
    onConnectStatusChange(h263: ConnectionStatus): void;
}
export {};
