import bundleManager from '@ohos.bundle.bundleManager';
import batteryInfo from '@ohos.batteryInfo';
import connection from '@ohos.net.connection';
import radio from "@ohos.telephony.radio";
import hilog from '@ohos.hilog';
import util from '@ohos.util';
import audio from "@ohos.multimedia.audio";
const TAG = '[aio_util]';
const DOMAIN_NUMBER = 0x0000;
export class ApplicationUtil {
    static getContext() {
        if (ApplicationUtil.sContext == undefined) {
            throw Error("Context is null, please call aioUtilIns.setContext() with -- import aioUtilIns from '@acpm/aio_util'-- first");
        }
        return ApplicationUtil.sContext;
    }
    static getBundleInfo() {
        if (ApplicationUtil.sBundleInfo == undefined) {
            ApplicationUtil.sBundleInfo = bundleManager.getBundleInfoForSelfSync(bundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_APPLICATION
                | bundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_SIGNATURE_INFO);
        }
        return ApplicationUtil.sBundleInfo;
    }
    static getApplicationBundleName() {
        let d3 = ApplicationUtil.getBundleInfo();
        if (d3 != null && d3.appInfo != null) {
            return d3.appInfo.name;
        }
        return "";
    }
    static getApplicationSignature() {
        let c3 = ApplicationUtil.getBundleInfo();
        if (c3 != null && c3.signatureInfo != null) {
            return c3.signatureInfo.fingerprint;
        }
        return "";
    }
    static getApplicationVersion() {
        let b3 = ApplicationUtil.getBundleInfo();
        if (b3 != null) {
            return b3.versionName;
        }
        return "";
    }
    static getApplicationName() {
        let z2 = ApplicationUtil.getBundleInfo();
        if (z2 != null) {
            let a3 = ApplicationUtil.getBundleInfo().appInfo.labelId;
            return ApplicationUtil.getContext().resourceManager.getStringSync(a3);
        }
        return "";
    }
    static getCacheDir() {
        return ApplicationUtil.getContext().cacheDir;
    }
    static getFileDir() {
        return ApplicationUtil.getContext().filesDir;
    }
    static getTempDir() {
        return ApplicationUtil.getContext().tempDir;
    }
    static registerLifecycleListener(h2, i2) {
        let j2 = {
            onAbilityCreate(y2) {
            },
            onWindowStageCreate(w2, x2) {
            },
            onWindowStageActive(u2, v2) {
            },
            onWindowStageInactive(s2, t2) {
            },
            onWindowStageDestroy(q2, r2) {
            },
            onAbilityDestroy(p2) {
            },
            onAbilityForeground(o2) {
                console.log(`AbilityLifecycleCallback onAbilityForeground ability: ${o2}`);
                if (i2) {
                    i2(true);
                }
            },
            onAbilityBackground(n2) {
                console.log(`AbilityLifecycleCallback onAbilityBackground ability: ${n2}`);
                if (i2) {
                    i2(false);
                }
            },
            onAbilityContinue(m2) {
            }
        };
        try {
            let l2 = h2.on('abilityLifecycle', j2);
            return l2;
        }
        catch (k2) {
            hilog.error(DOMAIN_NUMBER, TAG, `Failed to register applicationContext. Code is ${k2.code}, message is ${k2.message}`);
        }
        return -1;
    }
}
class DeviceUtils {
    static getElectricUsage() {
        return batteryInfo.batterySOC;
    }
    static setCommunicationDeviceSpeaker(e2) {
        audio.getAudioManager().getRoutingManager().setCommunicationDevice(audio.CommunicationDeviceType.SPEAKER, e2, (g2) => {
            if (g2) {
                console.error(`Failed to set the active status of the device. ${g2}`);
                return;
            }
            console.info('Callback invoked to indicate that the device is set to the active status.');
        });
    }
}
class UUID {
    static generateRandomUUID() {
        return util.generateRandomUUID(false);
    }
}
class NetworkUtils {
    static isNetworkAvailable() {
        return connection.hasDefaultNetSync();
    }
    static registerNetworkChange(m1, n1) {
        if (!canIUse("SystemCapability.Communication.NetManager.Core") || !canIUse("SystemCapability.Telephony.CoreService")) {
            return undefined;
        }
        let o1 = connection.createNetConnection();
        o1.register((d2) => {
            hilog.info(DOMAIN_NUMBER, TAG, "network register %{public}s", JSON.stringify(d2));
        });
        o1.on('netAvailable', (c2) => {
            if (m1) {
                m1(true);
            }
            hilog.info(DOMAIN_NUMBER, TAG, "network netAvailable %{public}s", JSON.stringify(c2));
        });
        o1.on('netLost', (b2) => {
            if (m1) {
                m1(false);
            }
            hilog.info(DOMAIN_NUMBER, TAG, "network netLost %{public}s", JSON.stringify(b2));
        });
        o1.on('netCapabilitiesChange', (t1) => {
            if (n1 && t1.netCap.bearerTypes.length > 0) {
                let u1 = t1.netCap.bearerTypes[0];
                hilog.info(DOMAIN_NUMBER, TAG, "network netCapabilitiesChange %{public}s", JSON.stringify(t1));
                if (u1 == connection.NetBearType.BEARER_CELLULAR) {
                    radio.getPrimarySlotId()
                        .then((w1) => {
                        radio.getSignalInformation(w1, (y1, z1) => {
                            if (y1) {
                                n1(0);
                                hilog.info(DOMAIN_NUMBER, TAG, "network getSignalInformation failed, callback: err-> %{public}s", JSON.stringify(y1));
                                return;
                            }
                            let a2 = z1[0].signalType;
                            if (a2 == radio.NetworkType.NETWORK_TYPE_GSM || a2 == radio.NetworkType.NETWORK_TYPE_CDMA) {
                                n1(3);
                            }
                            else if (a2 == radio.NetworkType.NETWORK_TYPE_WCDMA || a2 == radio.NetworkType.NETWORK_TYPE_TDSCDMA) {
                                n1(5);
                            }
                            else if (a2 == radio.NetworkType.NETWORK_TYPE_LTE) {
                                n1(4);
                            }
                            else if (a2 == radio.NetworkType.NETWORK_TYPE_NR) {
                                n1(6);
                            }
                            hilog.info(DOMAIN_NUMBER, TAG, "network getSignalInformation success, callback: data->%{public}s", JSON.stringify(z1));
                        });
                    });
                }
                else if (u1 == connection.NetBearType.BEARER_WIFI) {
                    n1(2);
                }
                else if (u1 == connection.NetBearType.BEARER_ETHERNET) {
                    n1(1);
                }
            }
        });
        return o1;
    }
    static unregisterNetworkChange(j1) {
        if (j1 == undefined) {
            return;
        }
        if (!canIUse("SystemCapability.Communication.NetManager.Core") || !canIUse("SystemCapability.Telephony.CoreService")) {
            return;
        }
        j1.unregister((l1) => {
            hilog.info(DOMAIN_NUMBER, TAG, "network unregisterNetworkChange %{public}s", JSON.stringify(l1));
        });
    }
}
class ResourceUtils {
    static getResourceManager() {
        return ApplicationUtil.getContext().resourceManager;
    }
    static readRawTextFile(c1) {
        try {
            let g1 = ResourceUtils.getResourceManager().getRawFileContentSync(c1);
            let h1 = '';
            for (let i1 = 0; i1 < g1.length; i1++) {
                h1 += String.fromCharCode(g1[i1]);
            }
            return h1;
        }
        catch (d1) {
            let e1 = d1.code;
            let f1 = d1.message;
            hilog.error(0x0000, 'aio_util', `getRawFileContentSync failed, error code: ${e1}, message: ${f1}.`);
            return "";
        }
    }
}
class MetaDataUtils {
    static getMetadataValue(x, y) {
        let z = bundleManager.getBundleInfoForSelfSync(bundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_METADATA |
            bundleManager.BundleFlag.GET_BUNDLE_INFO_WITH_HAP_MODULE);
        for (let a1 = 0; a1 < z.hapModulesInfo.length; a1++) {
            if (z.hapModulesInfo[a1].type != y) {
                continue;
            }
            for (let b1 = 0; b1 < z.hapModulesInfo[a1].metadata.length; b1++) {
                if (z.hapModulesInfo[a1].metadata[b1].name == x) {
                    return z.hapModulesInfo[a1].metadata[b1].value;
                }
            }
        }
        return "";
    }
}
export class AioUtil {
    getFunctionsBinder() {
        const w = new Map();
        w.set("ApplicationUtil.getApplicationVersion", ApplicationUtil.getApplicationVersion);
        w.set("ApplicationUtil.getApplicationBundleName", ApplicationUtil.getApplicationBundleName);
        w.set("ApplicationUtil.getApplicationSignature", ApplicationUtil.getApplicationSignature);
        w.set("ApplicationUtil.getCacheDir", ApplicationUtil.getCacheDir);
        w.set("ApplicationUtil.getFileDir", ApplicationUtil.getFileDir);
        w.set("ApplicationUtil.getTempDir", ApplicationUtil.getTempDir);
        w.set("ApplicationUtil.getApplicationName", ApplicationUtil.getApplicationName);
        w.set("ApplicationUtil.registerLifecycleListener", ApplicationUtil.registerLifecycleListener);
        w.set("DeviceUtils.getElectricUsage", DeviceUtils.getElectricUsage);
        w.set("NetworkUtils.isNetworkAvailable", NetworkUtils.isNetworkAvailable);
        w.set("NetworkUtils.registerNetworkChange", NetworkUtils.registerNetworkChange);
        w.set("NetworkUtils.unregisterNetworkChange", NetworkUtils.unregisterNetworkChange);
        w.set("UUID.generateRandomUUID", UUID.generateRandomUUID);
        w.set("ResourceUtils.getResourceManager", ResourceUtils.getResourceManager);
        w.set("ResourceUtils.readRawTextFile", ResourceUtils.readRawTextFile);
        w.set("MetaDataUtils.getMetadataValue", MetaDataUtils.getMetadataValue);
        w.set("DeviceUtils.setCommunicationDeviceSpeaker", DeviceUtils.setCommunicationDeviceSpeaker);
        return w;
    }
    setup(v) {
        ApplicationUtil.sContext = v.getApplicationContext();
        globalThis.aliyun_video_context = v;
    }
}
