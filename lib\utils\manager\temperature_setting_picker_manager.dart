import 'dart:ui';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../widgets/common/custom_list_picker.dart';
import 'log_manager.dart';

class TemperatureSettingPickerManager {

  static void showTemperatureSettingPicker({
    required BuildContext context,
    required List<String> temperatureList,
    required int currentTemperature,
    required int type,
    void Function(int? type , int? temperature)? onTemperatureSettingSelected,
  }) {
    int initialListIndex = 0;
    for(int i = 0; i<temperatureList.length;i++){
      String tpt = temperatureList[i].substring(0,temperatureList[i].length-1);
      int tptV = int.parse(tpt);
      if(tptV == currentTemperature){
        initialListIndex = i;
        break;
      }
    }
    // 弹出地点选择器
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return _TemperatureSettingPickerSheet(
          temperatureList: temperatureList,
          initialListIndex:initialListIndex,
          type: type,
          onTemperatureSettingSelected: onTemperatureSettingSelected,
        );
      },
    );
  }
}

class _TemperatureSettingPickerSheet extends StatefulWidget {
  final List<String> temperatureList;
  final int initialListIndex;
  final  void Function(int? type , int? temperature)? onTemperatureSettingSelected;
  final int? type;
  const _TemperatureSettingPickerSheet({
    Key? key,
    required this.temperatureList,
    required this.initialListIndex,
    this.onTemperatureSettingSelected,
    this.type
  }) : super(key: key);

  @override
  __TemperatureSettingPickerSheetState createState() => __TemperatureSettingPickerSheetState();
}

class __TemperatureSettingPickerSheetState extends State<_TemperatureSettingPickerSheet> {
  late int _selectedIndexd = 0;
  @override
  Widget build(BuildContext context) {
    double buttonHeight = 30;
    double safeAreaBottom = MediaQueryData.fromWindow(window).padding.bottom;

    _selectedIndexd = widget.initialListIndex;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: const EdgeInsets.only(top:10, left: 20, right: 20),
          child: SizedBox(
            height: 30,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  width: 80,
                  height: 55,
                  child: ElevatedButton(
                    onPressed: (){
                      Navigator.of(context).pop(); // 确认选择并关闭弹窗
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      padding: EdgeInsets.zero,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(buttonHeight / 2.0),
                      ),
                      elevation: 0,
                      side: const BorderSide(
                        color: Color(0xff777777), // 边框颜色
                        width: 1.0, // 边框宽度
                      ),
                    ),
                    child: const Text('取消',style: TextStyle(color: Color(0xff777777)),),
                  ),
                ),

                SizedBox(
                  width: 80,
                  height: 55,
                  child: ElevatedButton(
                    onPressed: (){
                      Navigator.of(context).pop(); // 确认选择并关闭弹窗
                      if (widget.onTemperatureSettingSelected != null) {
                        LogManager().debug('_selectedIndexd : $_selectedIndexd');
                        String? _temperature = widget.temperatureList.elementAt(_selectedIndexd);
                        widget.onTemperatureSettingSelected!(widget.type,int.parse(_temperature));
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xff000000),
                      padding: EdgeInsets.zero,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(buttonHeight / 2.0),
                      ),
                      elevation: 0,
                    ),
                    child: const Text('确认',style: TextStyle(color: Colors.white),),
                  ),
                ),
              ],
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 20),
          child: CustomListPicker(
            list: widget.temperatureList,
            initialListIndex : _selectedIndexd,
            onListSelected: (int _selectedListIndex){
              if(widget.onTemperatureSettingSelected != null){
                setState(() {
                  _selectedIndexd = _selectedListIndex;
                });
              }
            },
          ),
        ),
        SizedBox(height: safeAreaBottom,)
      ],
    );
  }
}