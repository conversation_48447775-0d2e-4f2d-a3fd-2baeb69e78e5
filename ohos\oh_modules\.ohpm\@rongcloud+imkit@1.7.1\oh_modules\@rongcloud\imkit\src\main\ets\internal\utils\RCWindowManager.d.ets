// @keepTs
// @ts-nocheck
/**
 * Created on 2025/04/14
 * <AUTHOR>
 * @description 窗口相关数据管理器
 */
import { window } from "@kit.ArkUI";
export declare class RCWindowManager {
    private readonly TAG;
    private isLayoutFullScreen;
    private windowIsLayoutFullScreen;
    private heightDefault;
    private statusBarHeight;
    private statusBarHeightValue;
    private indicatorHeight;
    private indicatorHeightValue;
    private pageContentHeight;
    getCurrentWindow(r349: Context): Promise<window.Window>;
    /**
     * 设置全屏模式
     * @param windowStage
     * @param isLayoutFullScreen 全屏模式
     * @returns boolean 是否设置成功
     */
    setWindowLayoutFullScreen(l349: window.WindowStage, m349: boolean): Promise<boolean>;
    /**
     * 设置全屏模式，直接设置，无需校验全屏模式是否设置成功
     * @param isLayoutFullScreen
     */
    setIsLayoutFullScreen(k349: boolean): void;
    /**
     * 获取页面内容展示高度
     * @returns
     */
    getPageContentHeight(): Promise<number>;
    /**
     * 获取顶部状态栏高度，不是全屏模式返回 0
     * @returns
     */
    getStatusBarHeight(): Promise<number>;
    /**
     * 获取顶部状态栏高度，实际的高度值
     * @returns
     */
    getStatusBarHeightValue(): Promise<number>;
    /**
     * 获取导航条区域的高度，不是全屏模式返回 0
     * @returns
     */
    getIndicatorHeight(): Promise<number>;
    /**
     * 获取导航条区域的高度，实际的高度值
     * @returns
     */
    getIndicatorHeightValue(): Promise<number>;
    /**
     * 获取是否开启全屏
     * @returns
     */
    getWindowIsLayoutFullScreen(): Promise<boolean>;
    /**
     * 获取系统默认区域的高度，顶部状态栏/底部导航栏（目前未使用）/导航条区域
     * @returns
     */
    getWindowAvoidAreaHeight(): Promise<Array<number>>;
    /**
     * 重置全屏模式数据
     */
    resetLayoutFullScreenData(): void;
    static getInstance(): RCWindowManager;
}
