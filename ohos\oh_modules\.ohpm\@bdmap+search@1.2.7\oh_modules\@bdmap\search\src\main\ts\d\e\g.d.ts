import type { BikingRoutePlanOption, BikingRouteResult } from "./h/k";
import type { MassTransitRoutePlanOption, MassTransitRouteResult } from "./h/i";
import type { TransitRoutePlanOption, TransitRouteResult } from "./h/n";
import type { WalkingRoutePlanOption, WalkingRouteResult } from "./h/m";
import type { DrivingRoutePlanOption, DrivingRouteResult } from "./h/l";
/**
 * @version 2.0
 * @description 路线规划 使用完后需要手动释放
 * @date 2025-3-13
 */
export declare class RoutePlanSearch {
    private isDestroy;
    private iRoutePlanSearch;
    private constructor();
    /**
     * 获取RoutePlan检索实例
     *
     * @return RoutePlan检索实例
     */
    static newInstance(): RoutePlanSearch;
    /**
     * @description
     * 使用自行车路线计划来进行搜索，返回一个Promise对象，该对象包含了一个BikingRouteResult类型的值。
     *
     * @param option - BikingRoutePlanOption类型，表示搜索选项，包括起始点、目标点和可选参数等。
     * @param useMultiThread - 接口内部是否使用多线程请求，默认为false，启用后需要手动调用destroy
     * @returns Promise<BikingRouteResult> - 返回一个Promise对象，当请求完成时会解析为一个BikingRouteResult类型的值。
     *
     * @throws {Error} 如果发生错误，则抛出一个Error对象。
     */
    bikingSearch(option: BikingRoutePlanOption, useMultiThread?: boolean): Promise<BikingRouteResult>;
    /**
     * @description
     * 根据轨道交通路线计划选项进行搜索，返回一个Promise对象，该对象包含了轨道交通路线的结果。
     *
     * @param option MassTransitRoutePlanOption - 轨道交通路线计划选项，包含起点、终点和其他可选参数。
     * @param useMultiThread - 接口内部是否使用多线程请求，默认为false，启用后需要手动调用destroy
     * @returns Promise<MassTransitRouteResult> - 返回一个Promise对象，该对象包含了轨道交通路线的结果，包括轨道交通路线列表和详情信息等。
     *
     * @throws 无异常抛出。
     */
    masstransitSearch(option: MassTransitRoutePlanOption, useMultiThread?: boolean): Promise<MassTransitRouteResult>;
    /**
     * @description 公交车路线搜索方法，返回一个Promise对象，包含了解析结果。
     * @param {TransitRoutePlanOption} option - 公交车路线计划选项，包含起点、终点和途经点等信息。
     * @param useMultiThread - 接口内部是否使用多线程请求，默认为false，启用后需要手动调用destroy
     * @returns {Promise<TransitRouteResult>} - 返回一个Promise对象，resolve状态下包含了解析结果，reject状态下包含错误信息。
     */
    transitSearch(option: TransitRoutePlanOption, useMultiThread?: boolean): Promise<TransitRouteResult>;
    /**
     * @description
     * 步行路线查询，返回一个Promise对象，包含了步行路线的结果。
     *
     * @param option {WalkingRoutePlanOption} - 步行路线计算参数，包含起点、终点和途经点等信息。
     * @param useMultiThread - 接口内部是否使用多线程请求，默认为false，启用后需要手动调用destroy
     * @returns {Promise<WalkingRouteResult>} - 返回一个Promise对象，resolve状态下包含了步行路线的结果，reject状态下包含错误信息。
     */
    walkingSearch(option: WalkingRoutePlanOption, useMultiThread?: boolean): Promise<WalkingRouteResult>;
    /**
     * @description
     * 驾车路线搜索，返回一个Promise对象，包含驾车路线的结果。
     *
     * @param option {DrivingRoutePlanOption} - 驾车路线计算参数，包含起点、终点和可选参数等信息。
     * @param useMultiThread - 接口内部是否使用多线程请求，默认为false，启用后需要手动调用destroy
     * @returns {Promise<DrivingRouteResult>} - 返回一个Promise对象，resolve状态下包含驾车路线的结果，reject状态下包含错误信息。
     */
    drivingSearch(option: DrivingRoutePlanOption, useMultiThread?: boolean): Promise<DrivingRouteResult>;
    destroy(): void;
}
