import ArrayList from '@ohos.util.ArrayList';
import MessageCodec from '@ohos/flutter_ohos/src/main/ets/plugin/common/MessageCodec';
import { BinaryMessenger } from "@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger";
export default class GeneratedOhosWebView {
    static wrapError(exception: Error): ArrayList<ESObject>;
}
export declare class FlutterError extends Error {
    code: string;
    details: ESObject;
    constructor(code: string, message: string, details: ESObject);
}
export interface Result<T> {
    success: (result: T) => void;
    error: (error: Error) => void;
}
export declare enum FileChooserMode {
    OPEN = 0,
    OPEN_MULTIPLE = 1,
    SAVE = 2
}
interface FileChooserModeEnumDataBuilder {
    value: FileChooserMode;
    setValue: (setterArg: FileChooserMode) => FileChooserModeEnumDataBuilder;
    build: () => FileChooserModeEnumData;
}
export declare class FileChooserModeEnumData {
    private value;
    private constructor();
    getValue(): FileChooserMode;
    setValue(setterArg: FileChooserMode): void;
    static Builder(): FileChooserModeEnumDataBuilder;
    toArray(): Array<ESObject>;
    static fromArray(list: Array<ESObject>): FileChooserModeEnumData;
}
interface WebViewPointBuilder {
    x: number;
    y: number;
    setX: (setterArg: number) => WebViewPointBuilder;
    setY: (setterArg: number) => WebViewPointBuilder;
    build: () => WebViewPoint;
}
export declare class WebViewPoint {
    private x;
    private y;
    getX(): number;
    setX(setterArg: number): void;
    getY(): number;
    setY(setterArg: number): void;
    private constructor();
    static Builder(): WebViewPointBuilder;
    toArray(): Array<ESObject>;
    static fromArray(list: Array<ESObject>): WebViewPoint;
}
export declare abstract class WebViewHostApi {
    abstract create(instanceId: number): void;
    abstract loadData(instanceId: number, data: string, mimeType: string, encoding: string): void;
    abstract loadDataWithBaseUrl(instanceId: number, baseUrl: string, data: string, mimeType: string, encoding: string, historyUrl: string): void;
    abstract loadUrl(instanceId: number, url: string, headers: Map<string, string>): void;
    abstract postUrl(instanceId: number, url: string, data: Uint8Array): void;
    abstract getUrl(instanceId: number): string;
    abstract canGoBack(instanceId: number): boolean;
    abstract canGoForward(instanceId: number): boolean;
    abstract goBack(instanceId: number): void;
    abstract goForward(instanceId: number): void;
    abstract reload(instanceId: number): void;
    abstract clearCache(instanceId: number, includeDiskFiles: boolean): void;
    abstract evaluateJavascript(instanceId: number, javascriptString: string, result: Result<string>): void;
    abstract getTitle(instanceId: number): string;
    abstract scrollTo(instanceId: number, x: number, y: number): void;
    abstract scrollBy(instanceId: number, x: number, y: number): void;
    abstract getScrollX(instanceId: number): number;
    abstract getScrollY(instanceId: number): number;
    abstract getScrollPosition(instanceId: number): WebViewPoint;
    abstract setWebContentsDebuggingEnabled(enabled: boolean): void;
    abstract setWebViewClient(instanceId: number, webViewClientInstanceId: number): void;
    abstract addJavaScriptChannel(instanceId: number, javaScriptChannelInstanceId: number): void;
    abstract removeJavaScriptChannel(instanceId: number, javaScriptChannelInstanceId: number): void;
    abstract setDownloadListener(instanceId: number, listenerInstanceId: number): any;
    abstract setWebChromeClient(instanceId: number, clientInstanceId: number): any;
    static getCodec(): MessageCodec<ESObject>;
    static setup(binaryMessenger: BinaryMessenger, api: WebViewHostApi): void;
}
export interface Reply<T> {
    /**
     * Handles the specified message reply.
     *
     * @param reply the reply, possibly null.
     */
    reply: (reply: T | null) => void;
}
export declare class WebViewFlutterApi {
    private binaryMessenger;
    constructor(argBinaryMessenger: BinaryMessenger);
    static getCodec(): MessageCodec<ESObject>;
    create(identifierArg: number, callback: Reply<void>): void;
    onScrollChanged(webViewInstanceIdArg: number, leftArg: number, topArg: number, oldLeftArg: number, oldTopArg: number, callback: Reply<void>): void;
}
export declare enum ConsoleMessageLevel {
    DEBUG = 0,
    ERROR = 1,
    LOG = 2,
    TIP = 3,
    WARNING = 4,
    UNKNOWN = 5
}
interface ConsoleMessageBuilder {
    lineNumber: number;
    message: string;
    level: ConsoleMessageLevel;
    sourceId: string;
    setLineNumber: (setterArg: number) => ConsoleMessageBuilder;
    setMessage: (setterArg: string) => ConsoleMessageBuilder;
    setLevel: (setterArg: ConsoleMessageLevel) => ConsoleMessageBuilder;
    setSourceId: (setterArg: string) => ConsoleMessageBuilder;
    build: () => ConsoleMessage;
}
export declare class ConsoleMessage {
    private lineNumber;
    getLineNumber(): number;
    setLineNumber(setterArg: number): void;
    private message;
    getMessage(): string;
    setMessage(setterArg: string): void;
    private level;
    getLevel(): ConsoleMessageLevel;
    setLevel(setterArg: ConsoleMessageLevel): void;
    private sourceId;
    getSourceId(): string;
    setSourceId(setterArg: string): void;
    private constructor();
    static Builder(): ConsoleMessageBuilder;
    toArray(): Array<ESObject>;
    static fromArray(array: Array<ESObject>): ConsoleMessage;
}
export declare abstract class WebChromeClientFlutterApi {
    protected binaryMessenger: BinaryMessenger;
    constructor(binaryMessenger: BinaryMessenger);
    static getCodec(): MessageCodec<ESObject>;
    onProgressChanged(instanceIdArg: number, webViewInstanceIdArg: number, progressArg: number, callback: Reply<void>): void;
    onShowFileChooser(instanceIdArg: number, webViewInstanceIdArg: number, paramsInstanceIdArg: number, callback: Reply<Array<string>>): void;
    onPermissionRequest(instanceIdArg: number, requestInstanceIdArg: number, callback: Reply<void>): void;
    onShowCustomView(instanceIdArg: number, viewIdentifierArg: number, callbackIdentifierArg: number, callback: Reply<void>): void;
    onHideCustomView(instanceIdArg: number, callback: Reply<void>): void;
    onGeolocationPermissionsShowPrompt(instanceIdArg: number, paramsInstanceIdArg: number, originArg: string, callback: Reply<void>): void;
    onGeolocationPermissionsHidePrompt(instanceIdArg: number, callback: Reply<void>): void;
    onConsoleMessage(instanceIdArg: number, messageArg: ConsoleMessage, callback: Reply<void>): void;
    onJsAlert(instanceIdArg: number, urlArg: string, messageArg: string, callback: Reply<void>): void;
    onJsConfirm(instanceIdArg: number, urlArg: string, messageArg: string, callback: Reply<boolean>): void;
    onJsPrompt(instanceIdArg: number, urlArg: string, messageArg: string, defaultValueArg: string, callback: Reply<string>): void;
}
export declare abstract class WebChromeClientHostApi {
    abstract create(instanceId: number): void;
    abstract setSynchronousReturnValueForOnShowFileChooser(instanceId: number, value: boolean): void;
    abstract setSynchronousReturnValueForOnConsoleMessage(instanceId: number, value: boolean): void;
    abstract setSynchronousReturnValueForOnJsAlert(instanceId: number, value: boolean): void;
    abstract setSynchronousReturnValueForOnJsConfirm(instanceId: number, value: boolean): void;
    abstract setSynchronousReturnValueForOnJsPrompt(instanceId: number, value: boolean): void;
    static getCodec(): MessageCodec<ESObject>;
    static setup(binaryMessenger: BinaryMessenger, api: WebChromeClientHostApi): void;
}
export declare abstract class WebViewClientHostApi {
    abstract create(instanceId: number): void;
    abstract setSynchronousReturnValueForShouldOverrideUrlLoading(instanceId: number, value: boolean): void;
    static getCodec(): MessageCodec<ESObject>;
    static setup(binaryMessenger: BinaryMessenger, api: WebViewClientHostApi): void;
}
interface WebResourceErrorDataBuilder {
    errorCode: number;
    setErrorCode: (setterArg: number) => WebResourceErrorDataBuilder;
    description: string;
    setDescription: (setterArg: string) => WebResourceErrorDataBuilder;
    build: () => WebResourceErrorData;
}
export declare class WebResourceErrorData {
    private errorCode;
    private constructor();
    getErrorCode(): number;
    setErrorCode(setterArg: number): void;
    private description;
    getDescription(): string;
    setDescription(setterArg: string): void;
    static Builder(): WebResourceErrorDataBuilder;
    toArray(): Array<ESObject>;
    static fromArray(list: Array<ESObject>): WebResourceErrorData;
}
interface WebResourceRequestDataBuilder {
    url: string;
    setUrl: (setterArg: string) => WebResourceRequestDataBuilder;
    isForMainFrame: boolean;
    setIsForMainFrame: (setterArg: boolean) => WebResourceRequestDataBuilder;
    isRedirect: boolean;
    setIsRedirect: (setterArg: boolean) => WebResourceRequestDataBuilder;
    hasGesture: boolean;
    setHasGesture: (setterArg: boolean) => WebResourceRequestDataBuilder;
    method: string;
    setMethod: (setterArg: string) => WebResourceRequestDataBuilder;
    requestHeaders: Map<string, string>;
    setRequestHeaders: (setterArg: Map<string, string>) => WebResourceRequestDataBuilder;
    build: () => WebResourceRequestData;
}
export declare class WebResourceRequestData {
    private url;
    private constructor();
    getUrl(): string;
    setUrl(setterArg: string): void;
    private isForMainFrame;
    getIsForMainFrame(): boolean;
    setIsForMainFrame(setterArg: boolean): void;
    private isRedirect;
    getIsRedirect(): boolean;
    setIsRedirect(setterArg: boolean): void;
    private hasGesture;
    getHasGesture(): boolean;
    setHasGesture(setterArg: boolean): void;
    private method;
    getMethod(): string;
    setMethod(setterArg: string): void;
    private requestHeaders;
    getRequestHeaders(): Map<string, string>;
    setRequestHeaders(setterArg: Map<string, string>): void;
    static Builder(): WebResourceRequestDataBuilder;
    toArray(): Array<ESObject>;
    static fromArray(list: Array<ESObject>): WebResourceRequestData;
}
export declare abstract class WebViewClientFlutterApi {
    protected binaryMessenger: BinaryMessenger;
    constructor(argBinaryMessenger: BinaryMessenger);
    static getCodec(): MessageCodec<ESObject>;
    onPageStarted(instanceIdArg: number, webViewInstanceIdArg: number, urlArg: string, callback: Reply<void>): void;
    onPageFinished(instanceIdArg: number, webViewInstanceIdArg: number, urlArg: string, callback: Reply<void>): void;
    onReceivedRequestError(instanceIdArg: number, webViewInstanceIdArg: number, requestArg: WebResourceRequestData, errorArg: WebResourceErrorData, callback: Reply<void>): void;
    requestLoading(instanceIdArg: number, webViewInstanceIdArg: number, requestArg: WebResourceRequestData, callback: Reply<void>): void;
    doUpdateVisitedHistory(instanceIdArg: number, webViewInstanceIdArg: number, urlArg: string, isReloadArg: boolean, callback: Reply<void>): void;
    onReceivedHttpAuthRequest(instanceIdArg: number, webViewInstanceIdArg: number, httpAuthHandlerInstanceIdArg: number, hostArg: string, realmArg: string, callback: Reply<void>): void;
}
export declare abstract class WebSettingsHostApi {
    abstract create(instanceId: number, webViewInstanceId: number): void;
    abstract setDomStorageEnabled(instanceId: number, flag: boolean): void;
    abstract setJavaScriptCanOpenWindowsAutomatically(instanceId: number, flag: boolean): void;
    abstract setSupportMultipleWindows(instanceId: number, support: boolean): void;
    abstract setBackgroundColor(instanceId: number, color: number): any;
    abstract setJavaScriptEnabled(instanceId: number, flag: boolean): void;
    abstract setUserAgentString(instanceId: number, userAgentString: string): void;
    abstract setMediaPlaybackRequiresUserGesture(instanceId: number, require: boolean): void;
    abstract setSupportZoom(instanceId: number, support: boolean): void;
    abstract setLoadWithOverviewMode(instanceId: number, overview: boolean): void;
    abstract setUseWideViewPort(instanceId: number, use: boolean): void;
    abstract setDisplayZoomControls(instanceId: number, enabled: boolean): void;
    abstract setBuiltInZoomControls(instanceId: number, enabled: boolean): void;
    abstract setAllowFileAccess(instanceId: number, enabled: boolean): void;
    abstract setTextZoom(instanceId: number, textZoom: number): void;
    abstract getUserAgentString(instanceId: number): Promise<string>;
    abstract setAllowFullScreenRotate(instanceId: number, enabled: boolean): void;
    static getCodec(): MessageCodec<ESObject>;
    static setup(binaryMessenger: BinaryMessenger, api: WebSettingsHostApi): void;
}
export declare abstract class WebStorageHostApi {
    abstract create(instanceId: number): void;
    abstract deleteAllData(instanceId: number): void;
    static getCodec(): MessageCodec<ESObject>;
    static setup(binaryMessenger: BinaryMessenger, api: WebStorageHostApi): void;
}
export declare abstract class GeolocationPermissionsCallbackHostApi {
    abstract invoke(instanceId: number, origin: string, allow: Boolean, retain: Boolean): any;
    static getCodec(): MessageCodec<ESObject>;
    static setup(binaryMessenger: BinaryMessenger, api: GeolocationPermissionsCallbackHostApi): void;
}
export declare class GeolocationPermissionsCallbackFlutterApi {
    private binaryMessenger;
    constructor(binaryMessenger: BinaryMessenger);
    static getCodec(): MessageCodec<ESObject>;
    create(instanceIdArg: number, callback: Reply<void>): void;
}
export declare abstract class HttpAuthHandlerHostApi {
    abstract useHttpAuthUsernamePassword(instanceId: number): boolean;
    abstract cancel(instanceId: number): void;
    abstract proceed(instanceId: number, username: string, password: string): void;
    static getCodec(): MessageCodec<ESObject>;
    static setup(binaryMessenger: BinaryMessenger, api: HttpAuthHandlerHostApi): void;
}
export declare class HttpAuthHandlerFlutterApi {
    private binaryMessenger;
    constructor(binaryMessenger: BinaryMessenger);
    static getCodec(): MessageCodec<ESObject>;
    create(instanceIdArg: number, callback: Reply<void>): void;
}
export declare abstract class CookieManagerHostApi {
    static getCodec(): MessageCodec<ESObject>;
    abstract attachInstance(instanceIdentifier: number): any;
    abstract setCookie(identifier: number, url: string, value: string): any;
    abstract removeAllCookies(identifier: number, result: Result<Boolean>): any;
    abstract setAcceptThirdPartyCookies(identifier: number, accept: boolean): any;
    static setup(binaryMessenger: BinaryMessenger, api: CookieManagerHostApi): void;
}
export declare class PermissionRequestFlutterApi {
    private binaryMessenger;
    constructor(binaryMessenger: BinaryMessenger);
    static getCodec(): MessageCodec<ESObject>;
    create(instanceIdArg: number, resourcesArg: string[], callback: Reply<void>): void;
}
export declare abstract class CustomViewCallbackHostApi {
    abstract onCustomViewHidden(identifier: number): void;
    static getCodec(): MessageCodec<ESObject>;
    static setup(binaryMessenger: BinaryMessenger, api: CustomViewCallbackHostApi): void;
}
export interface HttpAuthHandler {
    cancel(): void;
    proceed(username: string, password: string): void;
    useHttpAuthUsernamePassword(): boolean;
}
export interface CustomViewCallback {
    onCustomViewHidden(): void;
}
export declare class CustomViewCallbackFlutterApi {
    binaryMessenger: BinaryMessenger;
    constructor(binaryMessenger: BinaryMessenger);
    static getCodec(): MessageCodec<ESObject>;
    create(identifierArg: number, callback: Reply<void>): void;
}
export declare class ViewFlutterApi {
    binaryMessenger: BinaryMessenger;
    constructor(binaryMessenger: BinaryMessenger);
    static getCodec(): MessageCodec<ESObject>;
    create(identifierArg: number, callback: Reply<void>): void;
}
export declare abstract class PermissionRequestHostApi {
    abstract grant(instanceId: number, resources: string[]): any;
    abstract deny(instanceId: number): any;
    static getCodec(): MessageCodec<ESObject>;
    static setup(binaryMessenger: BinaryMessenger, api: PermissionRequestHostApi): void;
}
export declare class FileChooserParamsFlutterApi {
    private binaryMessenger;
    constructor(binaryMessenger: BinaryMessenger);
    static getCodec(): MessageCodec<ESObject>;
    tCreate(instanceIdArg: number, isCaptureEnabledArg: boolean, acceptTypesArg: string[], modeArg: FileChooserMode, callback: Reply<void>): void;
}
export declare class JavaScriptChannelFlutterApi {
    private binaryMessenger;
    constructor(binaryMessenger: BinaryMessenger);
    static getCodec(): MessageCodec<ESObject>;
    postMessageT(instanceIdArg: number, messageArg: string, callback: Reply<void>): void;
}
export declare class DownloadListenerFlutterApi {
    private binaryMessenger;
    constructor(binaryMessenger: BinaryMessenger);
    static getCodec(): MessageCodec<ESObject>;
    onDownloadStartT(instanceIdArg: number, urlArg: string, userAgentArg: string, contentDispositionArg: string, mimetypeArg: string, contentLengthArg: number, callback: Reply<void>): void;
}
export declare abstract class DownloadListenerHostApi {
    abstract create(instanceId: number): any;
    static getCodec(): MessageCodec<ESObject>;
    static setup(binaryMessenger: BinaryMessenger, api: DownloadListenerHostApi): void;
}
export declare abstract class FlutterAssetManagerHostApi {
    abstract list(path: string): any;
    abstract getAssetFilePathByName(name: string): any;
    static getCodec(): MessageCodec<ESObject>;
    static setup(binaryMessenger: BinaryMessenger, api: FlutterAssetManagerHostApi): void;
}
export declare abstract class JavaScriptChannelHostApi {
    abstract create(instanceId: number, channelName: string): any;
    static getCodec(): MessageCodec<ESObject>;
    static setup(binaryMessenger: BinaryMessenger, api: JavaScriptChannelHostApi): void;
}
export declare abstract class OhosObjectHostApi {
    abstract dispose(identifier: number): any;
    static getCodec(): MessageCodec<ESObject>;
    static setup(binaryMessenger: BinaryMessenger, api: OhosObjectHostApi): void;
}
export declare class OhosObjectFlutterApi {
    private binaryMessenger;
    constructor(binaryMessenger: BinaryMessenger);
    static getCodec(): MessageCodec<ESObject>;
    dispose(identifierArg: number, callback: Reply<void>): void;
}
export interface InstanceManagerApi {
    clear: () => void;
}
export declare abstract class InstanceManagerHostApi {
    abstract clear: () => void;
    static getCodec(): MessageCodec<Object>;
    /**
     * Sets up an instance of `InstanceManagerHostApi` to handle messages through the
     * `binaryMessenger`.
     */
    static setup(binaryMessenger: BinaryMessenger, api: InstanceManagerApi): void;
}
export {};
