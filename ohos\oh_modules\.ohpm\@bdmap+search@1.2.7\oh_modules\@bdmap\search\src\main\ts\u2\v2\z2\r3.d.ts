import { SearchResult } from "../../../d/e/h/f1";
import { SearchParser } from '../../base/base';
export declare class ReverseGeoCoderParser extends SearchParser {
    parseSearchResult(u12: string): SearchResult;
    private parseRgcResult;
    private parseReverseGeoCodeResult;
    private parseRoadInfoList;
    private parseMKGeocoderAddressComponent;
    private parsePoiRegionsInfoList;
    private parseJsonXY2LatLng;
    private parseJsonLocation2LatLng;
    private parseString2PoiList;
    private parseParentPoiInfo;
}
