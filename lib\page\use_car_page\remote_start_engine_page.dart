import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/api/car.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:wuling_flutter_app/utils/manager/loading_manager.dart';
import 'package:wuling_flutter_app/utils/manager/log_manager.dart';
import 'package:wuling_flutter_app/utils/manager/dialog_manager.dart';
import 'package:wuling_flutter_app/models/global_data.dart';
import 'package:wuling_flutter_app/models/car/car_control_response_model.dart';
import 'package:wuling_flutter_app/models/car/car_info_model.dart';
import 'package:wuling_flutter_app/page/base_page/base_page.dart';
import 'package:wuling_flutter_app/widgets/common/image_view.dart';
import 'package:wuling_flutter_app/widgets/common/custom_dialog.dart';
import 'package:wuling_flutter_app/widgets/use_car_page_widgets/remote_start_engine_page/remote_start_engine_page_cell_widget.dart';

class RemoteStartEnginePage extends BasePage {
  RemoteStartEnginePage({
    Key? key,
    bool hideAppBar = false,
    bool isWithinSafeArea = true,
    String appBarTitle = '远程启动',
    Color appBarColor = Colors.transparent,
    Color pageBackgroundColor = const Color(0xffFAFAFA),
  }) : super(
          key: key,
          hideAppBar: hideAppBar,
          isWithinSafeArea: isWithinSafeArea,
          appBarTitle: appBarTitle,
          appBarColor: appBarColor,
          pageBackgroundColor: pageBackgroundColor,
        );

  @override
  _RemoteStartEnginePageState createState() => _RemoteStartEnginePageState();
}

class _RemoteStartEnginePageState extends BasePageState<RemoteStartEnginePage> {
  final log = LogManager();
  final carApi = CarAPI();
  final globalData = GlobalData();
  late CarInfoModel? carInfoModel;
  bool _isRequesting = false;
  void doRemoteStartEngineRequest() {
    if (_isRequesting) {
      return;
    }
    setState(() {
      _isRequesting = true;
    });
    LoadingManager.show(status: '请稍候...', maskType: EasyLoadingMaskType.black);
    Map<String, dynamic> params = {
      'vin': carInfoModel?.vin,
    };
    String url = 'junApi/sgmw/car/control/ignition/authorize';
    carApi.requestCarControlServiceWithURLStr(
        targetUrl: url, params: params, onSucceed: onSuccess, onError: onError);
  }

  void onSuccess({CarControlResponseModel? carControlResponseModel}) {
    LoadingManager.dismiss();
    showSucceedDialog(message: '远程启动唤起成功\n请您在30秒内上车');
    setState(() {
      _isRequesting = false;
    });
  }

  void onError(Exception exception) {
    log.info(exception.toString());
    LoadingManager.dismiss();
    showFailedDialog(message: '远程启动唤起失败');
    setState(() {
      _isRequesting = false;
    });
  }

  void showSucceedDialog({required String message}) {
    DialogManager().showCustomDialog(
      CustomDialog(
        content: message,
        buttons: [
          DialogButton(
              label: "我知道了",
              onPressed: () {},
              backgroundColor: Colors.white,
              textColor: Color(0xFF00BAFF)),
        ],
      ),
    );
  }

  void showFailedDialog({required String message}) {
    DialogManager().showCustomDialog(
      CustomDialog(
        content: message,
        buttons: [
          DialogButton(
              label: "取消",
              onPressed: () {},
              backgroundColor: Colors.white,
              textColor: Color(0xFF9B9DA9)),
          DialogButton(
              label: "确定",
              onPressed: () {
                doRemoteStartEngineRequest();
              },
              backgroundColor: Colors.white,
              textColor: Color(0xFF00BAFF)),
        ],
      ),
    );
  }

  @override
  void pageInitState() {
    carInfoModel = globalData.carInfoModel;
    super.pageInitState();
    // 在此处添加页面初始化逻辑
  }

  @override
  Widget buildPageContent(BuildContext context) {
    // 在此处添加页面ui设置，默认添加一个center text组件
    return Center(
      child: Padding(
        padding: const EdgeInsets.only(left: 20,right: 20),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  AspectRatio(
                    aspectRatio: 335 / 141,
                    child: ImageView(
                      'assets/images/use_car_page/remote_start_engine_page/sgmw_image_car_power_up_header.png',
                      fit: BoxFit.cover,
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            SizedBox(
                              height: 15,
                              width: 9,
                              child: ImageView(
                                  'assets/images/use_car_page/remote_start_engine_page/sgmw_icon_car_power_up_title_1.png',
                                fit: BoxFit.cover,
                              ),
                            ),
                            SizedBox(width: 9,),
                            Text('远程启动说明',style: TextStyle(fontSize: 15,fontWeight: FontWeight.w600),),
                          ],
                        ),
                        SizedBox(height: 10,),
                        Text(
                          '当您未携带车钥匙或遇到蓝牙钥匙唤起不成功，该功能可以帮助您正常用车：\n1.点击下方远程启动按钮，请您在30秒内解锁上车，踩制动踏板，即可发动并驾驶车辆；\n2.若30秒内未上车，重复上述操作重新授权。',
                          softWrap: true,
                          style: TextStyle(
                            fontSize: 15,
                            color: Color(0xff686B78),
                          ),
                        ),
                        SizedBox(height: 10,),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            SizedBox(
                              height: 15,
                              width: 9,
                              child: ImageView(
                                'assets/images/use_car_page/remote_start_engine_page/sgmw_icon_car_power_up_title_1.png',
                                fit: BoxFit.cover,
                              ),
                            ),
                            SizedBox(width: 9,),
                            Text('请您在注意安全防盗场景下使用此功能',style: TextStyle(fontSize: 15,fontWeight: FontWeight.w600),),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Column(
              children: [
                ElevatedButton(
                  onPressed: _isRequesting
                      ? null
                      : () {
                          doRemoteStartEngineRequest();
                        },
                  child: Text(
                    '远程启动',
                    style: TextStyle(fontSize: 20, color: Colors.white),
                  ),
                  style: ButtonStyle(
                    shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                      RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.0),
                      ),
                    ),
                    minimumSize: MaterialStateProperty.all<Size>(
                        Size(double.infinity, 50)),
                    elevation: MaterialStateProperty.resolveWith<double>(
                      (Set<MaterialState> states) {
                        // 为所有状态设置阴影为0
                        return 0;
                      },
                    ),
                    backgroundColor: MaterialStateProperty.resolveWith<Color>(
                        (Set<MaterialState> states) {
                      return const Color(0xFF01A2E1); // 默认状态下的背景色
                    }),
                  ),
                ),
                SizedBox(
                  height: 50,
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    // 在此处添加页面销毁逻辑
    super.dispose();
  }
}
