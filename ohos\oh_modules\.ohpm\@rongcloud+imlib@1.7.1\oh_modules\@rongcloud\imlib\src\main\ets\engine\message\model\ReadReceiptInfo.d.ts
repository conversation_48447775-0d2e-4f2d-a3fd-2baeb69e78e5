/**
 * 已读回执信息，只有群聊的消息需要此属性，其他类型的会话中此属性为 null
 *```
 // sequenceDiagram start
 title 消息已读 V1：单聊流程
 发送方->>接收方: 1.发送消息
 activate 接收方
 接收方-->> 接收方: 2.进入聊天页面看到最新消息
 接收方-->> 发送方: 3.发送单聊已读回执（带时间戳）
 deactivate 接收方
 activate 发送方
 发送方->> 发送方: 4.接收单聊已读回执，将时间戳前的消息都置为已读
 发送方->> 发送方: 5.将聊天页面该回执前的消息变为已读
 deactivate 发送方
 // sequenceDiagram end

 单聊具体流程描述：
 1. 发送方发送消息：发送方 App/IMKit 发送任何的记录未读的消息，比如文本、图片等
 2. 接收方进入聊天页面看到最新消息
 3. 接收方发送单聊已读回执（带时间戳）：接收方 App/IMKit 将当前会话最后一条消息的 sentTime 调用 IMEngine.sendReadReceiptMessage() 发出去
 4. 发送方接收单聊已读回执，将时间戳前的消息都置为已读：发送方收到单聊已读回执之后， SDK 将时间戳之前的本地消息都置为已读（SentStatus 设置为 Read），然后触发 MessageReadReceiptListener.onMessageReadReceiptReceived
 5. 发送方将聊天页面该回执前的消息变为已读：发送方 App/IMKit 的聊天页面收到 MessageReadReceiptListener.onMessageReadReceiptReceived 之后，把时间戳之前的消息 UI 都置为已读
 备注：单聊如何查看消息已读？看 Message SentStatus == Read 代表已读
 *```
 *
 *```
 // sequenceDiagram start
 title 消息已读 V1：群聊流程
 发送方->> 接收方: 1.发送消息
 发送方-->> 接收方: 2.发送群聊已读回执请求
 activate 接收方
 接收方-->> 接收方: 3.接收群聊已读回执请求
 接收方-->> 接收方: 4.进入聊天页面，遍历所有的消息
 接收方-->> 发送方: 5.发送群聊已读回执响应
 deactivate 接收方
 activate 发送方
 发送方->> 发送方: 6.接收群聊已读回执响应
 发送方->> 发送方: 7.将聊天页面对应消息更新已读状态
 deactivate 发送方
 // sequenceDiagram end

 群聊具体流程描述：
 1. 发送方发送消息：发送方 App/IMKit 发送任何的记未读消息，比如文本、图片等
 2. 发送方发送群聊已读回执请求：发送方 App/IMKit 进入聊天页面手动点击回执请求按钮，发送方调用 IMEngine.sendReadReceiptRequest()
 3. 接收方接收群聊已读回执请求：接收方 SDK 接收已读请求，并更新消息的 isReadReceiptMessage 为 true，触发 MessageReadReceiptListener.onMessageReceiptRequest()
 4. 接收方进入聊天页面，遍历所有的消息：接收方 App/IMKit 进入聊天页面，查到当前页面 isReadReceiptMessage 为 true 的消息
 5. 接收方发送群聊已读回执响应：接收方 App/IMKit 过滤聊天页面内所有 (isReadReceiptMessage == true && hasRespond == false) 的消息，调用 IMEngine.sendReadReceiptResponse() 发送已读回执响应，证明接收方这些消息已读过，接收方 SDK 会将这些消息的 hasRespond 设为 true，避免重复调用 sendReadReceiptResponse
 6. 发送方接收群聊已读回执响应：发送方 SDK 接收到已读响应时，将本地消息的 respondUserIdList 更新，然后触发 MessageReadReceiptListener.onMessageReceiptResponse()
 7. 发送方将聊天页面对应消息更新已读状态：发送方 App/IMKit 在对应的聊天页面，找到对应的消息，更新对应的 UI
 备注：群聊如何查看消息已读？看 Message ReadReceiptInfo 的具体数据
 *```
 * @version 1.3.0
 * @discussion 使用 https://sequencediagram.org/ 打开时序图
 */
export declare class ReadReceiptInfo {
    /**
     * 是否为需要回执的消息。true: 需要回执的消息。 false: 普通消息。默认 false
     */
    isReadReceiptMessage: boolean;
    /**
     * 是否发送过消息回执响应。 仅对消息接收方有效。默认 false
     */
    hasRespond: boolean;
    /**
     * 会话中响应过该消息回执的成员 userId 列表
     *
     * key 用户 Id， value 响应时间（毫秒时间戳）
     */
    respondUserIdList: Map<string, number>;
}
