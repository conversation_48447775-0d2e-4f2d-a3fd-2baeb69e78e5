import { FlutterEngine, Log } from '@ohos/flutter_ohos';
import CameraPlugin from 'camera_ohos';
import ConnectivityPlugin from 'connectivity_plus';
import DeviceInfoPlusOhosPlugin from 'device_info_plus';
import FlutterBluePlusPlugin from 'flutter_blue_plus';
import FluwxPlugin from 'fluwx';
import IamgeqrFlutterPlugin from 'iamgeqr_flutter_plugin';
import ImagePickerPlugin from 'image_picker_ohos';
import MobileScannerPlugin from 'mobile_scanner';
import OpenAppSettingsPlugin from 'open_app_settings';
import PackageInfoPlugin from 'package_info_plus';
import PathProviderPlugin from 'path_provider_ohos';
import PermissionHandlerPlugin from 'permission_handler_ohos';
import SharedPreferencesPlugin from 'shared_preferences_ohos';
import UrlLauncherPlugin from 'url_launcher_ohos';
import WebViewFlutterPlugin from 'webview_flutter_ohos';

/**
 * Generated file. Do not edit.
 * This file is generated by the Flutter tool based on the
 * plugins that support the Ohos platform.
 */

const TAG = "GeneratedPluginRegistrant";

export class GeneratedPluginRegistrant {

  static registerWith(flutterEngine: FlutterEngine) {
    try {
      flutterEngine.getPlugins()?.add(new CameraPlugin());
      flutterEngine.getPlugins()?.add(new ConnectivityPlugin());
      flutterEngine.getPlugins()?.add(new DeviceInfoPlusOhosPlugin());
      flutterEngine.getPlugins()?.add(new FlutterBluePlusPlugin());
      flutterEngine.getPlugins()?.add(new FluwxPlugin());
      flutterEngine.getPlugins()?.add(new IamgeqrFlutterPlugin());
      flutterEngine.getPlugins()?.add(new ImagePickerPlugin());
      flutterEngine.getPlugins()?.add(new MobileScannerPlugin());
      flutterEngine.getPlugins()?.add(new OpenAppSettingsPlugin());
      flutterEngine.getPlugins()?.add(new PackageInfoPlugin());
      flutterEngine.getPlugins()?.add(new PathProviderPlugin());
      flutterEngine.getPlugins()?.add(new PermissionHandlerPlugin());
      flutterEngine.getPlugins()?.add(new SharedPreferencesPlugin());
      flutterEngine.getPlugins()?.add(new UrlLauncherPlugin());
      flutterEngine.getPlugins()?.add(new WebViewFlutterPlugin());
    } catch (e) {
      Log.e(
        TAG,
        "Tried to register plugins with FlutterEngine ("
          + flutterEngine
          + ") failed.");
      Log.e(TAG, "Received exception while registering", e);
    }
  }
}
