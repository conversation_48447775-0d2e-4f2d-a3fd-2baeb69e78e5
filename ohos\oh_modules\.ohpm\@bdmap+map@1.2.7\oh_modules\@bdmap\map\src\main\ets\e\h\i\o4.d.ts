import type { DefaultMsg } from "../g1/e2"; import type { AnyObject } from "../g1/a2"; import type BaseMap from "./o2"; import type MapViewListener from "./l2";               export default class MapControlHandler { private baseMap; private mapListener; constructor(d30: BaseMap, e30: MapViewListener); processMessage(msg: string): number | AnyObject; mapMessage(data: DefaultMsg): 0 | 1; destroy(): void; } 