    export declare class AlgorithmUtil { private constructor(); static aesDecryptAsync(d4: string, e4: string, f4: string): Promise<unknown>; static aesDecryptCRT(v3: string, w3: string, x3: string, callback: any): Promise<void>; private static genSymKeyByData; private static genIvParamsSpec;           static hexStringConvertBytes(data: string): Uint8Array; static uint8ArrayToString(array: Uint8Array): string; } 