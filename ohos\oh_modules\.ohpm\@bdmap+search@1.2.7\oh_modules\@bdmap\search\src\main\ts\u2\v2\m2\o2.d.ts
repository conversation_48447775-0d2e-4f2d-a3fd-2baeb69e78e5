import { BusLineSearchOption } from "../../../d/m2/n2";
import { BusLineResult } from "../../../d/m2/p2";
import { BaseSearch } from '../../base/base';
export interface IBusLineSearch {
    searchBusLine(option: BusLineSearchOption): Promise<BusLineResult>;
}
export declare class BusLineSearchImp extends BaseSearch implements IBusLineSearch {
    searchBusLine(e8: BusLineSearchOption): Promise<BusLineResult>;
}
