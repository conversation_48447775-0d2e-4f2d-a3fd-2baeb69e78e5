import BmDrawItem from "./a3"; import { BmScaleMode } from "./d2"; import type { Point } from '@bdmap/base'; import type BmRichView from "./n1/g4"; import type BmBase<PERSON> from "./n1/a4"; export default class BmBaseMarker extends BmDrawItem { private x; private y; private z; private located; private offsetX; private offsetY; private rotate; private scaleX; private scaleY; private opacity; private buildingId; private floorId; private mClickAction; private mRichViews; constructor(g10: number, h10: number);       setPosition(f10: Point): void;       setX(x: number): void;       getX(): number;       setY(y: number): void;       getY(): number;       setZ(z: number): void; getZ(): number;           setLocated(e10: number): void; setPerspective(perspective: number): void; setIsFix(d10: number): void; setFixX(x: number): void; setFixY(y: number): void;             setOffsetX(offsetX: number, scaleMode?: BmScaleMode): void;             setOffsetY(offsetY: number, scaleMode: BmScaleMode): void;           setScale(scale: number): void;           setScaleX(scaleX: number): void;           setScaleY(scaleY: number): void;           setRotate(rotate: number): void;           setCollisionBehavior(c10: number): void;             setCollisionPriority(priority: number): void;           setTrackBy(b10: number): void;           setRotateFeature(feature: number): void;           setFollowMapRotateAxis(a10: number): void;           addRichView(z9: BmRichView): void;           addOnlyOneRichView(y9: BmRichView): void;           removeRichView(x9: BmRichView): void;       clearRichViews(): void;           findRichViewByName(name: string): BmRichView;           findRichViewByShell(s9: number): BmRichView;           findViewByShell(p9: number): BmBaseUI;           findViewByName(name: string): BmBaseUI;           setBuildingId(m9: string): void;           setFloorId(l9: string): void; setId(id: string): void; setWidth(width: number): void; setHeight(height: number): void; setClickAction(clickAction: string): void; getClickAction(): string; setDrawFullscreenMaskFlag(flag: boolean): void; } 