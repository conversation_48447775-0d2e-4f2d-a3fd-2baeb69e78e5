import { Point, LatLng } from '@bdmap/base'; import MapOptions from "./k";               import MapStatus from "./l"; import MapController from "./t2"; import type { AddSDKTileBundle, CallbackBundle, IndoorBundle, IndoorFloorBundles, LayerAddrBundle, LayerOptions, MapOverlay, NativeContext, RemoveOverlayOptions, UpdateSDKTileBundle } from "../g1/e2"; import type { DeviceInfo, Nullable } from "../g1/a2"; import type { Callback, NativeCallback, TwoParamsCallback } from "../g1/h1"; import type Bundle from "./o/i1"; import type MapViewListener from "./l2"; import type BaseLayer from "./d1/j1"; import type BmLayer from "./c2/q2"; import { BmLayerTag } from "./c2/d2"; export default class BaseMap { baseZoom: number; private mapstatus_throttling; private mapOptions; private mapViewId; mapController: MapController;         _touchNearly: number;         context: NativeContext; constructor(context: NativeContext, mapOptions: MapOptions, device: DeviceInfo); setMapOptions(mapOptions: MapOptions | null): void; setMapStatus(l8: Nullable<MapStatus>): void; updateMapStatus(j8?: boolean): void; switchLayer(h8: number, i8: number): void; setGestureEnable(g8: Bundle): void; setGestureConfig(f8: Bundle): void; getMapOptions(): MapOptions; getMapStatus(): MapStatus; fetchMapStatus(): void; throttlingMapStatus(): void; registMapViewListener(d8: MapViewListener, e8: BmLayer): void; addOneOverlayItem(bundle: MapOverlay): void; addOneOverlayItems(c8: Array<MapOverlay>): void; createLayers(): Nullable<LayerAddrBundle>; showLayer(layer: LayerOptions): void; updateLayer(layer: BaseLayer): void; updateLayerById(id: number): void; addTileLayer(x7: any, bundle: any): number | false; updateTileLayer(bundle: any): void; addLayer(u7: number, v7: number, tag: string): number; removeLayer(t7: string): number; addSDKTile(bundle: AddSDKTileBundle): number; updateSDKTile(bundle: UpdateSDKTileBundle): number; cleanSDKTileCache(s7: string): number; updateOneOverlayItem(bundle: MapOverlay): void; removeOneOverlayItem(bundle: Bundle): void; removeOverlayItems(arr: Array<RemoveOverlayOptions>): void; setMapStatusLimits(b: Bundle): void; getNearlyObjID(x: number, y: number, radius: number): import("../interface/native").NearOverlays[]; pixelToXY(p7: number, q7: number, callback: TwoParamsCallback<Nullable<Point>, Nullable<LatLng>>): void; pixelToLLCallback(l7: number, m7: number, callback: Callback<Nullable<LatLng>>): void; pixelToLL(j7: number, k7: number): LatLng; llToPixel(i7: LatLng): Nullable<[ number, number ]>; setPixelToCenter(left: number, top: number, h7: boolean): void; llToScreen(g7: LatLng): { left: number; top: number; }; relativePixel(f7: Nullable<LatLng>): [ number, number ]; rotatePlus(d7: number): void; overlookingPlus(b7: number): void; zoom(level: number, z6?: [ number, number ]): void; changelocStatus(y6: boolean): void; setMapOnBackOrFore(x6: boolean): void; updateShowCustomLayer(): void; switchIndoorFloor(obj: Nullable<IndoorBundle>): void; getIndoorInfo(obj: Nullable<IndoorBundle>): IndoorFloorBundles; initCustomStyle(path: string): boolean; setCustomStyleEnable(enable: number): boolean; onceDraw(): void; addBmLayer(w6: number): void; removeBmLayer(v6: number): void; registerJSMethod(key: string, callback: NativeCallback<string, CallbackBundle>): void; layersIsShow(u6: BmLayerTag): number; showPoiMarker(isShow: boolean): void; showIndoorPoiMarker(isShow: boolean): void; setMinLevel(level: number): void; setMaxLevel(level: number): void; setDBClickEnable(enable: boolean): void; destroy(): void; } 