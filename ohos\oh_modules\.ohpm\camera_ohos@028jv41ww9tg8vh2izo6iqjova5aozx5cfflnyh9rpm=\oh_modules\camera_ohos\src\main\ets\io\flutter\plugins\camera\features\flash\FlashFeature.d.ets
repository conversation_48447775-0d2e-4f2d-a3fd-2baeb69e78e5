import { CameraProperties } from '../../CameraProperties';
import { CameraFeature } from '../CameraFeature';
import { FlashMode } from './FlashMode';
export declare class FlashFeature extends CameraFeature<FlashMode> {
    private currentSetting;
    constructor(cameraProperties: CameraProperties);
    getDebugName(): string;
    getValue(): FlashMode;
    setValue(value: FlashMode): void;
    checkIsSupported(): boolean;
}
