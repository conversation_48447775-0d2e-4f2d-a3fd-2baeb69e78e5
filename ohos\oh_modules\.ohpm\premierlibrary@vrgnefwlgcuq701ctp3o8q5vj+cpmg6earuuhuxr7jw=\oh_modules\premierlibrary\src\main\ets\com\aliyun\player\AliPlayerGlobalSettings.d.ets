import { IPResolveType } from './IPlayer';
export declare class AliPlayerGlobalSettings {
    static SET_PRE_CONNECT_DOMAIN: number;
    static SET_DNS_PRIORITY_LOCAL_FIRST: number;
    static ENABLE_H2_MULTIPLEX: number;
    static SET_EXTRA_DATA: number;
    static ENABLE_ANDROID_DECODE_REUSE: number;
    static NOT_PAUSE_WHEN_PREPARING: number;
    static ALLOW_RTS_DEGRADE: number;
    static ENABLE_DECODER_FAST_FIRST_FRAME: number;
    static DISABLE_CAPTURE_SCALE: number;
    static SCENE_PLAYER: number;
    static SCENE_LOADER: number;
    static CodecType_H265: number;
    static FORBID_URLHASHCB: string;
    static sOnGetUrlHashCallback: OnGetUrlHashCallback | null;
    private static sInstance;
    private static getInstance;
    constructor();
    private nOnGetUrlHashCallback;
    static OnGetUrlHashCallback(b8: string): string;
    /**
     * 获取SDK版本号
     *
     * @return SDK版本号
     */
    /****
     * Query the version of the SDK.
     *
     * @return The version of the SDK.
     */
    static getSdkVersion(): string;
    /**
     * 设置特定功能选项
     * @param key 选项key
     * @param value 选项的值
     */
    /****
     * Set specific option
     * @param key key option
     * @param value value of key
     */
    static setOptionStr(z7: number, a8: string): void;
    /**
     * 设置特定功能选项
     * @param key 选项key
     * @param value 选项的值
     */
    /****
     * Set specific option
     * @param key key option
     * @param value value of key
     */
    static setOptionNum(x7: number, y7: number): void;
    /**
     * 设置域名对应的解析ip
     * @param host 域名，需指定端口（http默认端口80，https默认端口443）。例如player.alicdn.com:80
     * @param ip 相应的ip，设置为空字符串清空设定。
     */
    /****
     * Set a DNS ip to resolve the host.
     * @param host The host. Need to specify the port(http defualt port is 80，https default port is 443). E.g player.alicdn.com:80
     * @param ip The ip address, set as empty string to clear the setting.
     */
    static setDNSResolve(v7: string, w7: string): void;
    /**
     * 设置是否使用http2。默认不使用
     * @param use true：使用。
     */
    /****
     * Set if use http2. Default if false.
     * @param use true：use http2.
     */
    static setUseHttp2(u7: boolean): void;
    /**
     * 设置网络ip解析类型
     * @param type 见 {@link IPlayer.IPResolveType}
     */
    /****
     * set ip resolve type
     * @param type See {@link IPlayer.IPResolveType}
     */
    static setIPResolveType(t7: IPResolveType): void;
    /**
     * 设置音频播放类型，若不设置，默认为AUDIOSTREAM_USAGE_MUSIC
     * @param streamType 见 {@linkplain StreamType}
     */
    /****
     * Set audio stream type, the default value is AUDIOSTREAM_USAGE_MUSIC
     * @param streamType see {@linkplain StreamType}
     */
    static setAudioStreamType(s7: AudioStreamType): void;
    /**
     * 强制音频渲染器采用指定的格式进行渲染，如果设定的格式设备不支持，则无效，无效值将被忽略，使用默认值；pcm回调数据的格式和此设置关联,如果修改，请在同一个线程操作,默认关闭
     * @param force 打开/关闭 强制设置
     * @param format  设置pcm的格式，目前只支持s16，16位有符号整数
     * @param channels 设置pcm的声道数，有效值 1~2
     * @param sample_rate 设置pcm的采样率，有效值 1～48000
     */
    /****
     *  force audio render use the particular format,the value will no effect when the format not supported by device，the out range value will be ignored，and use the default value;
     *  and it will affect the pcm data that from audio rending callback, it only can be reset in the same thread, disabled by default.
     * @param force enable/disable
     * @param format the pcm format, only support s16 for now, signed integer with 16 bits
     * @param channels the pcm channels， available range 1～2
     * @param sample_rate set the pcm sample rate， available range 1～48000
     */
    static forceAudioRendingFormat(o7: boolean, p7: string, q7: number, r7: number): void;
    /**
     * 开启本地缓存，开启之后，就会缓存到本地文件中。
     * @param enable true：开启本地缓存。false：关闭。默认关闭。
     * @param maxBufferMemoryKB 设置单个源的最大内存占用大小。单位KB
     * @param localCacheDir 本地缓存的文件目录，绝对路径
     */
    /****
     * Enable local cache. When enabled, it will be cached in local files.
     * @param enable true: enables the local cache. false: disable. This function is disabled by default.
     * @param maxBufferMemoryKB Set the maximum memory size for a single source. Unit is KB
     * @param localCacheDir Directory of files cached locally, absolute path
     */
    static enableLocalCache(l7: boolean, m7: number, n7: string): void;
    /**
     *  本地缓存文件自动清理相关的设置
     * @param expireMin 缓存多久过期：单位分钟，默认值30天，过期的缓存不管容量如何，都会在清理时淘汰掉；
     * @param maxCapacityMB 最大缓存容量：单位兆，默认值20GB，在清理时，如果缓存总大小超过此大小，会以cacheItem为粒度，按缓存的最后时间排序，一个一个淘汰掉一些缓存，直到小于等于最大缓存容量；
     * @param freeStorageMB 磁盘最小空余容量：单位兆，默认值0，在清理时，同最大缓存容量，如果当前磁盘容量小于该值，也会按规则一个一个淘汰掉一些缓存，直到freeStorage大于等于该值或者所有缓存都被干掉；
     */
    /****
     * Settings related to automatic cleanup of local cache files
     * @param expireMin How long the cache expires: the unit is minute. The default value is 30 days.
     * @param maxCapacityMB maximum cache capacity: in megabytes. The default value is 20GB. If the total cache size exceeds this size, the cache size is sorted by cacheItem until it is less than or equal to the maximum cache capacity.
     * @param freeStorageMB Minimum free disk capacity: in megabytes (default value: 0). If the current disk capacity is less than this value, the freeStorage will be eliminated one by one until the freeStorage is greater than or equal to this value or all caches are eliminated.
     */
    static setCacheFileClearConfig(i7: number, j7: number, k7: number): void;
    /**
     * 清空所有的缓存文件
     */
    /****
     * Clear all cache files.
     */
    static clearCaches(): void;
    /**
     * 是否开启httpDNS。默认不开启。
     * 开启后需要注意以下事项
     * 1.该功能与增强型Httpdns互斥，若同时打开，后开启的功能会实际生效；
     * 2.打开后，会使用标准httpdns进行请求，若失败会降级至local dns。
     * @param enable
     * @deprecated This method is not recommended, use enableEnhancedHttpDns instead.
     */
    /****
     * enable httpDNS. Default is disabled.
     * 1.This function is mutually exclusive with enhanced Httpdns.
     * 2.After opening, standard httpdns will be used for requests. If it fails, it will be downgraded to local dns.
     * @param enable
     * @deprecated This method is not recommended, use enableEnhancedHttpDns instead.
     */
    static enableHttpDns(h7: boolean): void;
    /**
     * 是否开启增强型httpDNS。默认不开启
     * 开启后需要注意以下事项
     * 1.该功能与Httpdns互斥，若同时打开，后开启的功能会实际生效；
     * 2.需要申请license的高级httpdns功能，否则该功能不工作
     * 3.需要通过接口{@link com.aliyun.dns.DomainProcessor#addEnhancedHttpDnsDomain(String)}添加cdn域名，否则会降级至local dns。
     * 4.请确保该域名在alicdn平台添加并配置对应功能，确保可提供线上服务。配置方法请参考：https://www.alibabacloud.com/product/content-delivery-network
     * @param enable
     */
    /****
     * enable enhanced httpDNS. Default is disbled.
     * Need to pay attention to the following matters after open this function
     * 1.This function is mutually exclusive with Httpdns. If turned on at the same time, the later turned on function will actually take effect;
     * 2.this method need apply license enhanced dns function, otherwise this function will not work
     * 3.need to add the cdn domain name through the interface {@link com.aliyun.dns.DomainProcessor#addEnhancedHttpDnsDomain(String)}, otherwise it will be downgraded to local dns
     * 4.Please ensure that the domain name is added and configured with corresponding functions on the alicdn platform to ensure that online services can be provided.
     * For configuration methods, please refer to: https://www.alibabacloud.com/product/content-delivery-network
     * @param enable
     */
    static enableEnhancedHttpDns(g7: boolean): void;
    /**
     * 是否开启内建预加载网络平衡策略，播放过程中，自动控制预加载的运行时机。默认开启。
     * @param enable true：开启预加载网络平衡策略。
     */
    /****
     * enable Network Balance mechanism for control media loader's scheduling automatically. Default is enabled.
     * @param enable true: enable preload network balance mechanism.
     */
    static enableNetworkBalance(f7: boolean): void;
    /**
     * 是否开启缓冲buffer到本地缓存，开启后，如果maxBufferDuration大于50s，则大于50s到部分会缓存到本地缓存。默认关闭。
     * @param enable true：开启缓冲buffer到本地缓存。
     */
    /****
     * enable buffer to local cache when maxBufferDuration large than 50s. Default is disabled.
     * @param enable true: enable buffer to local cache.
     */
    static enableBufferToLocalCache(e7: boolean): void;
    /**
     * 是否关闭crash堆栈上传
     * @param disable true：关闭crash堆栈上传。
     */
    /****
     * whether disable crash stack upload
     * @param disable true: disable crash stack upload.
     */
    static disableCrashUpload(d7: boolean): void;
    /**
     * 设置加载url的hash值回调。如果不设置，SDK使用md5算法。
     */
    /****
     * Sets the hash callback to load the URL. If this parameter is not set, the SDK uses md5 algorithm.
     * @param cb the hash callback
     */
    static setCacheUrlHashCallback(c7: OnGetUrlHashCallback | null): void;
}
/**
 * 获取加载url的hash值回调，用来做url唯一的id，必须要保证每个url都不一样
 * 注意：要在回调中做同步操作
 */
/****
 * Get the hash value of the loaded URL, used as the unique id of the URL. Ensure that each URL is different.
 * Notice: getUrlHashCallback must be sync
 */
export interface OnGetUrlHashCallback {
    /**
     * 回调方法
     *
     * @param url 视频url
     * @return hash值，必须要保证每个url都不一样
     */
    /****
     * callback
     * @param url the loaded URL
     * @return the hash value . Ensure that each URL is different.
     */
    getUrlHashCallback: (url: string) => string;
}
/**
 * 音频流类型
 */
/****
 * audio stream type
 */
export declare enum AudioStreamType {
    /**
     * 未知.
     */
    /**
     * Unknown usage.
     */
    AUDIOSTREAM_USAGE_UNKNOWN = 0,
    /**
     * 音乐播放
     */
    /**
     * Music usage.
     */
    AUDIOSTREAM_USAGE_MUSIC = 1,
    /**
     * VoIP语音通话
     */
    /**
     * Voice communication usage.
     */
    AUDIOSTREAM_USAGE_VOICE_COMMUNICATION = 2,
    /**
     * 语音播报
     */
    /**
     * Voice assistant usage.
     */
    AUDIOSTREAM_USAGE_VOICE_ASSISTANT = 3,
    /**
     * 闹钟
     */
    /**
     * Alarm usage.
     */
    AUDIOSTREAM_USAGE_ALARM = 4,
    /**
     * 语音消息
     */
    /**
     * Voice message usage.
     */
    AUDIOSTREAM_USAGE_VOICE_MESSAGE = 5,
    /**
     * 铃声
     */
    /**
     * Ringtone usage.
     */
    AUDIOSTREAM_USAGE_RINGTONE = 6,
    /**
     * 通知
     */
    /**
     * Notification usage.
     */
    AUDIOSTREAM_USAGE_NOTIFICATION = 7,
    /**
     * 无障碍
     */
    /**
     * Accessibility usage, such as screen reader.
     */
    AUDIOSTREAM_USAGE_ACCESSIBILITY = 8,
    /**
     * 电影或视频
     */
    /**
     * Movie or video usage.
     */
    AUDIOSTREAM_USAGE_MOVIE = 10,
    /**
     * 游戏
     */
    /**
     * Game sound effect usage.
     */
    AUDIOSTREAM_USAGE_GAME = 11,
    /**
     * 有声读物（包括听书、相声、评书）、听新闻、播客等
     */
    /**
     * Audiobook usage.
     */
    AUDIOSTREAM_USAGE_AUDIOBOOK = 12,
    /**
     * 导航
     */
    /**
     * Navigation usage.
     */
    AUDIOSTREAM_USAGE_NAVIGATION = 13,
    /**
     * VoIP视频通话
     */
    /**
     * Video call usage.
     */
    AUDIOSTREAM_USAGE_VIDEO_COMMUNICATION = 17
}
