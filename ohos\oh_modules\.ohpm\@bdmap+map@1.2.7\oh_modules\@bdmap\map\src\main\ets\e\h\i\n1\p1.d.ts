import type { ColorString } from "../../g1/a2"; import type { FontOption } from "../../util/b1/c1"; import BmTextStyle from "../c2/f2/y3"; import c46 from "./v4";         export default class TextStyle extends c46 { private textColor; private textSize; private borderColor; private borderWidth; private fontOption; private mInstance; private backColor; constructor();         getInstance(): BmTextStyle;         setTextColor(q47: ColorString | number): void;         setTextSize(size: number): void;         setBorderColor(o47: ColorString | number): void;         setBorderWidth(width: number): void;         setFontOption(option: FontOption): void;         setBackColor(m47: ColorString | number): void;         clone(): TextStyle; } 