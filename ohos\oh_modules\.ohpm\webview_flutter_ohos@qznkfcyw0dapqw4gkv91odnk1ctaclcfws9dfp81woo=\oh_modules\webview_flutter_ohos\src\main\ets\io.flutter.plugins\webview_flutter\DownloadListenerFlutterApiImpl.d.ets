import { InstanceManager } from './InstanceManager';
import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import { Reply, DownloadListenerFlutterApi } from "./GeneratedOhosWebView";
export declare class DownloadListenerFlutterApiImpl extends DownloadListenerFlutterApi {
    private instanceManager;
    constructor(binaryMessenger: BinaryMessenger, instanceManager: InstanceManager);
    /** Passes arguments from {@link DownloadListener#onDownloadStart} to Dart. */
    onDownloadStart(downloadListener: WebviewController | ESObject, url: string, userAgent: string, contentDisposition: string, mimetype: string, contentLength: number, callback: Reply<void>): void;
    getIdentifierForListener(listener: DownloadListener): number;
}
export interface DownloadListener {
    onDownloadStart(url: string, userAgent: string, contentDisposition: string, mimeType: string, contentLength: number): void;
}
