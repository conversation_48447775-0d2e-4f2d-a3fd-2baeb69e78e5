/*
* Copyright (c) 2023 Hunan OpenValley Digital Industry Development Co., Ltd. All rights reserved.
* Use of this source code is governed by a BSD-style license that can be
* found in the LICENSE_KHZG file.
*
* Based on BinaryMessenger.java originally written by
* Copyright (C) 2013 The Flutter Authors.
*
*/

/**
 * An abstraction over the threading policy used to invoke message handlers.
 *
 * <p>These are generated by calling methods like {@link
 * BinaryMessenger#makeBackgroundTaskQueue(TaskQueueOptions)} and can be passed into
 * platform channels' constructors to control the threading policy for handling platform
 * channels' messages.
 */

import SendableBinaryMessageHandler from './SendableBinaryMessageHandler'

export interface TaskQueue {}

/**
 * The priority of task execution
 *
 * This priority is guaranteed to be compatible with
 * https://developer.huawei.com/consumer/cn/doc/harmonyos-references-V5/js-apis-taskpool-V5#priority.
 *
 */
export enum TaskPriority {
  HIGH = 0,
  MEDIUM = 1,
  LOW = 2,
  IDLE = 3
}

/** Options that control how a TaskQueue should operate and be created. */
export class TaskQueueOptions {
  private isSerial: boolean = true;
  private isSingleThread: boolean = false;
  private priority: TaskPriority = TaskPriority.MEDIUM;

  getIsSerial():boolean {
    return this.isSerial;
  }

  setIsSerial(isSerial: boolean): TaskQueueOptions {
    this.isSerial = isSerial;
    return this;
  }

  getPriority(): TaskPriority {
    return this.priority;
  }

  setPriority(priority: TaskPriority): TaskQueueOptions {
    this.priority = priority;
    return this;
  }

  isSingleThreadMode(): boolean {
    return this.isSingleThread;
  }

  setSingleThreadMode(isSingleThread: boolean): TaskQueueOptions {
    this.isSingleThread = isSingleThread;
    return this;
  }
}

/**
 * Binary message reply callback. Used to submit a reply to an incoming message from Flutter. Also
 * used in the dual capacity to handle a reply received from Flutter after sending a message.
 */
export interface BinaryReply {
  /**
   * Handles the specified reply.
   *
   * @param reply the reply payload, a direct-allocated {@link ByteBuffer} or null. Senders of
   *     outgoing replies must place the reply bytes between position zero and current position.
   *     Reply receivers can read from the buffer directly.
   */
  reply: (reply: ArrayBuffer | null) => void;
}

/** Handler for incoming binary messages from Flutter. */
export interface BinaryMessageHandler {
  /**
   * Handles the specified message.
   *
   * <p>Handler implementations must reply to all incoming messages, by submitting a single
   * reply message to the given {@link BinaryReply}. Failure to do so will result in lingering Flutter
   * reply handlers. The reply may be submitted asynchronously.
   *
   * <p>Any uncaught exception thrown by this method will be caught by the 
   * messenger implementation and logged, and a null reply message will be sent back to Flutter.
   *
   * @param message the message {@link ByteBuffer} payload, possibly null.
   * @param reply A {@link BinaryReply} used for submitting a reply back to Flutter.
   */
  onMessage(message: ArrayBuffer, reply: BinaryReply): void;
}

/**
 * Facility for communicating with Flutter using asynchronous message passing with binary
 * messages. The Flutter Dart code should use <a
 * href="https://api.flutter.dev/flutter/services/BinaryMessages-class.html">BinaryMessages</a> to
 * participate.
 *
 * <p>{@code BinaryMessenger} is expected to be utilized from a single thread throughout the
 * duration of its existence. If created on the main thread, then all invocations should take
 * place on the main thread. If created on a background thread, then all invocations should take place on
 * that background thread.
 *
 * @see BasicMessageChannel , which supports message passing with Strings and
 *      semi-structured messages.
 * @see MethodChannel , which supports communication using asynchronous method invocation.
 * @see EventChannel , which supports communication using event streams.
 */

export interface BinaryMessenger {
  makeBackgroundTaskQueue(options?: TaskQueueOptions): TaskQueue;

  /**
   * Sends a binary message to the Flutter application.
   *
   * @param channel the name {@link String} of the logical channel used for the message.
   * @param message the message payload, a direct-allocated {@link ByteBuffer} with the message
   *     bytes between position zero and current position, or null.
   */
  send(channel: String, message: ArrayBuffer | null): void;

  /**
   * Sends a binary message to the Flutter application, optionally expecting a reply.
   *
   * <p>Any uncaught exception thrown by the reply callback will be caught and logged.
   *
   * @param channel the name {@link String} of the logical channel used for the message.
   * @param message the message payload, a direct-allocated {@link ByteBuffer} with the
   *        message bytes between position zero and current position, or null.
   * @param callback a {@link BinaryReply} callback invoked when the Flutter application 
   *     responds to the message, possibly null.
   */
  send(channel: String, message: ArrayBuffer, callback?: BinaryReply | null): void;

  /**
   * Registers a handler to be invoked when the Flutter application sends a message to its 
   * host platform.
   *
   * <p>Registration overwrites any previous registration for the same channel name. Use 
   * a null handler to deregister.
   *
   * <p>If no handler has been registered for a particular channel, any incoming message 
   * on that channel will be handled silently by sending a null reply.
   *
   * @param channel the name {@link String} of the channel.
   * @param handler a {@link BinaryMessageHandler} to be invoked on incoming messages, 
   *        or null.
   * @param taskQueue a {@link BinaryMessenger.TaskQueue} that specifies what thread 
   *     will execute the handler. Specifying null means execute on the platform thread.
   */
  setMessageHandler(channel: String, handler: BinaryMessageHandler | SendableBinaryMessageHandler | null, taskQueue?: TaskQueue, ...args: Object[]): void;
}
