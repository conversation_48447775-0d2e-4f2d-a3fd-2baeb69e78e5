// @keepTs
// @ts-nocheck
import { ConnectionStatus, ConversationIdentifier, HardwareResourceType, IMLibExtensionModule, InitOption, Message, ReceivedInfo } from '@rongcloud/imlib';
import { ApplicationStateChangeCallback } from '@kit.AbilityKit';
export declare class IMKitForLibExtensionModule extends IMLibExtensionModule {
    private _isAppForeground;
    private stateChangeCallbackList;
    onAppHangUp(): void;
    onAppForeground(): void;
    onAppBackground(): void;
    private static instance;
    private curAppKey;
    private curUserId;
    /**
     * 当前显示的会话
     */
    private curShowConversation;
    private constructor();
    static getInstance(): IMKitForLibExtensionModule;
    onInit(h295: Context, i295: string, j295: InitOption): void;
    onConnect(g295: string): void;
    onConnected(f295: string): void;
    didHoldReceivedMessage(d295: Message, e295: ReceivedInfo): boolean;
    onRtcConfigUpdate(c295: Map<string, Object>): void;
    onConnectionStatusChanged(b295: ConnectionStatus): void;
    onDisconnect(a295: boolean): void;
    getSdkVersion(): Map<string, string>;
    onRequestHardwareResource(y294: HardwareResourceType): boolean;
    getCurAppKey(): string;
    getCurUserId(): string;
    isAppForeground(): boolean;
    setCurShowConversation(x294: ConversationIdentifier | null): void;
    getCurShowConversation(): ConversationIdentifier | null;
    addApplicationStateChangeCallback(w294: ApplicationStateChangeCallback): void;
    removeApplicationStateChangeCallback(v294: ApplicationStateChangeCallback): void;
    isOnRequestHardwareResource(): boolean;
    private registerMessageType;
}
