import { LatLng } from '@bdmap/base';
import { LanguageType } from "../../base/x2";
/**
 * 反地理编码请求参数
 */
export declare class ReverseGeoCodeOption {
    /**
     * 单页展示POI数量，默认为10条记录，最大返回100条。
     */
    private _pageSize;
    set pageSize(value: number);
    get pageSize(): number;
    /**
     * 分页页码
     * 默认为0,0代表第一页，1代表第二页，以此类推
     */
    private _pageNum;
    set pageNum(value: number);
    get pageNum(): number;
    /**
     * 反地理编码位置坐标
     * 必须参数
     */
    private _location;
    set location(value: LatLng);
    get location(): LatLng;
    /**
     * 是否获取最新版行政区划数据（仅对中国数据生效），1（访问），0（不访问）
     */
    private _latestAdmin;
    set latestAdmin(value: number);
    get latestAdmin(): number;
    /**
     * poi召回半径，允许设置区间为0-1000米，超过1000米按1000米召回
     * 默认值为1000
     */
    private _radius;
    set radius(value: number);
    get radius(): number;
    /**
     * 可以选择poi类型召回不同类型的poi，例如 酒店，如想召回多个POI类型数据，可以'|'分割
     * 例如 酒店|房地产
     * http://lbsyun.baidu.com/index.php?title=lbscloud/poitags
     */
    private _poiType;
    set poiType(value: string);
    get poiType(): string;
    /**
     * 当取值为true时，召回坐标周围最近的3条道路数据。区别于行政区划中的street参数（street参数为行政区划中的街道，和普通道路不对应）。
     * 默认 false
     */
    private _isExtensionsRoad;
    set isExtensionsRoad(value: boolean);
    get isExtensionsRoad(): boolean;
    /**
     * 检索语言类型
     * 默认中文
     */
    private _languageType;
    set languageType(value: LanguageType);
    get languageType(): LanguageType;
    constructor(params: ReverseGeoCodeOptionParams);
}
/**
 * 反地理编码选项构造函数参数接口
 */
export interface ReverseGeoCodeOptionParams {
    /**
     * 单页展示POI数量，默认为10条记录，最大返回100条。
     */
    pageSize?: number;
    /**
     * 分页页码
     * 默认为0,0代表第一页，1代表第二页，以此类推
     */
    pageNum?: number;
    /**
     * 反地理编码位置坐标
     * 必须参数
     */
    location: LatLng;
    /**
     * 是否获取最新版行政区划数据（仅对中国数据生效），1（访问），0（不访问）
     */
    latestAdmin?: number;
    /**
     * poi召回半径，允许设置区间为0-1000米，超过1000米按1000米召回
     * 默认值为1000
     */
    radius?: number;
    /**
     * 可以选择poi类型召回不同类型的poi，例如 酒店，如想召回多个POI类型数据，可以'|'分割
     * 例如 酒店|房地产
     * http://lbsyun.baidu.com/index.php?title=lbscloud/poitags
     */
    poiType?: string;
    /**
     * 当取值为true时，召回坐标周围最近的3条道路数据。区别于行政区划中的street参数（street参数为行政区划中的街道，和普通道路不对应）。
     * 默认 false
     */
    isExtensionsRoad?: boolean;
    /**
     * 检索语言类型
     * 默认中文
     */
    languageType?: LanguageType;
}
