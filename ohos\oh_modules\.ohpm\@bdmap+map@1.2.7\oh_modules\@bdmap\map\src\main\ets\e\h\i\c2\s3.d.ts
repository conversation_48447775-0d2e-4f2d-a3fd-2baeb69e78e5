import BmDrawItem from "./a3"; import { BmGeoElement } from "./i3"; import BmSurfaceStyle from "./r3"; export default class BmPrism extends BmDrawItem { constructor();           addGeoElement(t12: BmGeoElement): any;         clearGeoElements(): any;           setSurfaceTopStyle(style: BmSurfaceStyle): void; setSurfaceSideStyle(style: BmSurfaceStyle): void; setSurfaceFloorTopStyle(style: BmSurfaceStyle): void; setSurfaceFloorSideStyle(style: BmSurfaceStyle): void; setHeight(height: number): any; setLastFloorHeight(height: number): any; setFloorHeight(height: number): any; setBuildingID(id: string): any; setAnimation(s12: boolean): any; setHasFloor(r12: boolean): any; setBuilding(q12: boolean): any; setBuildingId(id: string): any; setAnimateType(p12: number): any; setFloorAnimateType(o12: number): any; setIsRoundedCorner(n12: boolean): any; setRoundedCornerRadius(radius: number): any; } 