// @keepTs
// @ts-nocheck
/**
 * Created on 2024/9/18
 * <AUTHOR>
 */
import { Conversation, ConversationIdentifier, HQVoiceMessage, IAsyncResult, MentionedInfo, Message, VoiceMessage } from '@rongcloud/imlib';
import { MessageDataSource } from '../datasource/MessageDataSource';
import { RecallMessageEvent } from '../../../conversation/event/RecallMessageEvent';
import { SendMediaMessageEvent } from '../../../conversation/event/SendMediaMessageEvent';
import { SendMessageEvent } from '../../../conversation/event/SendMessageEvent';
import { GroupInfoModel } from '../../../user/model/GroupInfoModel';
import { GroupMemberInfoModel } from '../../../user/model/GroupMemberInfoModel';
import { UiMessage } from '../../../conversation/model/UiMessage';
import { UserInfoModel } from '../../../user/model/UserInfoModel';
import { BaseViewModel } from '../../base/viewmodel/BaseViewModel';
import { ConversationComponentData } from '../../../conversation/model/ConversationComponentData';
import { GetDataDirection } from '../../enum/RefreshDirectionType';
import { DeleteMessageEvent } from '../../../conversation/event/DeleteMessageEvent';
import { ClearConversationEvent } from '../../../conversation/event/ClearConversationEvent';
import { IConversationViewModel } from '../../../conversation/model/IConversationViewModel';
import { MessageChangedEvent } from '../../../conversation/event/MessageChangedEvent';
export declare class ConversationViewModel extends BaseViewModel implements IConversationViewModel {
    private readonly preferencesKey;
    private mProcessor;
    messageDataSource: MessageDataSource;
    private _unreadNewMsgArr;
    unreadMentionedMessages: Message[];
    private needReadResponseArray;
    private _firstUnreadMessage?;
    private _shouldScrollTop;
    private lastReadReceiptTime;
    private _pageShow;
    private lastSyncConversationReadStatusTime;
    private unreadStatusThrottler;
    scroller: Scroller;
    private conversationData;
    private _lastPlayedMessageId;
    private eventListener;
    private cacheListener;
    private receiveListener;
    private recalledListener;
    private readReceiptListener;
    private avPlayListener;
    private resendEventListener;
    private _willAutoScroll;
    private conversation;
    private preferencesInstance;
    onMessageOperationChange?: (type: OperationType, msgIds: number[]) => void;
    messageDidUpdate?: (message: Message) => void;
    onInputContentChange: (objectName: string, content?: string, destructMode?: boolean) => void;
    constructor();
    onClickNewReceivedUnreadMessageButton(): void;
    onClickUnreadMessageButton(): void;
    onClickUnreadMentionedMessageButton(): void;
    getMessageList(): UiMessage[];
    refreshUiMessage(a226: Message): void;
    onBind(s225: ConversationComponentData): Promise<void>;
    hideMask(): Promise<void>;
    private sendInputState;
    /**
     * 通过被引用的消息，加载消息列表，并跳转到这条消息
     */
    loadReferencedMessage(l225: Message): void;
    /**
     * 加载未读消息
     */
    loadUnreadMessage(): void;
    /**
     * 加载未读@我消息
     */
    loadUnreadMentionedMessage(): void;
    /**
     * 加载更多消息
     * @param refreshDirection 刷新方向 1，上拉；2，下拉
     * @param localCallback
     * @param remoteCallback
     */
    onLoadMore(r224: GetDataDirection, s224: () => void): void;
    onCleared(): void;
    /**
     * 接受普通消息
     */
    private onMessageReceived;
    /**
     * 同步离线消息
     */
    private onOfflineMessageSyncCompleted;
    /**
     * 收到撤回消息
     */
    private onMessageRecalled;
    /**
     * 主动撤回消息事件
     */
    onRecallMessageEvent: (event: RecallMessageEvent) => void;
    /**
     * 主动发送消息事件
     */
    onSendMessageEvent: (event: SendMessageEvent | SendMediaMessageEvent) => void;
    /**
     * 发送消息附件入库
     * @param event
     */
    private handleMessageAttach;
    /**
     * 发送消息失败
     * @param event
     */
    private handleMessageError;
    /**
     * 发送消息取消
     * @param event
     */
    private handleMessageCancel;
    /**
     * 发送消息成功
     * @param event
     */
    private handleMessageSuccess;
    /**
     * 发送消息进度
     * @param mediaEvent
     */
    private handleMessageProgress;
    /**
     * 清除草稿
     */
    private clearDraft;
    /**
     * 删除消息
     */
    onDeleteMessage: (event: DeleteMessageEvent) => void;
    /**
     * 清空会话
     */
    onClearConversation: (event: ClearConversationEvent) => void;
    onMessageChanged: (event: MessageChangedEvent) => void;
    /**
     * 滚动到底部
     */
    scrollBottom(): void;
    getTimestampScrollIndex(t222: ConversationComponentData): number;
    /**
     * 尝试通过消息uid跳转到当前内存中消息列表的指定条目
     */
    tryScrollToReferencedMessageByUid(r222?: string): boolean;
    /**
     * 监听缓存的变化
     */
    onUserInfoChanged: (userInfo: UserInfoModel) => void;
    onGroupInfoChanged: (groupInfo: GroupInfoModel) => void;
    onGroupMemberInfoChanged: (userInfo: GroupMemberInfoModel) => void;
    /**
     * 发送文本消息
     * @param isDestructMode 是否阅后即焚
     * @param conId
     * @param text
     * @param mention
     * @returns
     */
    sendText(d222: ConversationIdentifier, e222: boolean, f222: string, g222?: MentionedInfo, h222?: Message): Promise<IAsyncResult<Message>>;
    private getMessageContent;
    set willAutoScroll(s221: boolean);
    get willAutoScroll(): boolean;
    /**
     * 发送高清语音消息
     * @param isDestructMode 是否阅后即焚
     * @param conId
     * @param text
     * @param mention
     * @returns
     */
    sendHQVoice(n221: boolean, o221: ConversationIdentifier, p221: HQVoiceMessage, q221?: MentionedInfo): Promise<IAsyncResult<Message>>;
    /**
     * 发送普通语音消息
     * @param isDestructMode 是否阅后即焚
     * @param conId
     * @param text
     * @param mention
     * @returns
     */
    sendVoice(i221: boolean, j221: ConversationIdentifier, k221: VoiceMessage, l221?: MentionedInfo): Promise<IAsyncResult<Message>>;
    destructHQVoice(e221: number): void;
    findNextHQVoice(y220: number): void;
    /**
     * 播放音频
     */
    playAudio(w220: UiMessage): void;
    /**
     * 播放普通音频
     */
    private playVoiceAudio;
    /**
     * 播放高清音频
     */
    private playHQAudio;
    getConversation(): Promise<Conversation | undefined>;
    getFirstUnreadMessage(): Promise<void>;
    getUnreadMentionedMessages(s219: number): Promise<void>;
    private getUnreadMessageInfo;
    /**
     *清除未读消息
     */
    clearUnReadNewMsg(): void;
    private syncConversationReadStatus;
    private deleteUnreadNewMsg;
    removeMentionedMessage(d219: number): void;
    private generateHistoryMessage;
    sendReadReceiptMessage(y218: number): void;
    sendLastReadReceiptMessage(): void;
    private sendReadReceiptResponseForMessages;
    private canSendReadReceipt;
    sendGroupReadReceiptResponseForCache(): void;
    private updateLastMessageReadReceiptStatus;
    private updateHistoryMessageReadReceiptStatus;
    /**
     * 打开相机
     */
    openCamera(): Promise<void>;
    /**
     * 发送图片消息
     * @param localPath
     * @param isOri
     */
    private sendImageAction;
    /**
     * 发送小视频消息
     * @param localPath
     */
    private sendSightAction;
    /**
     * 复制文件
     * @param uri
     * @returns
     */
    private copyToSandbox;
    /**
     * 相册选择
     */
    openAlbum(): Promise<void>;
    /**
     * 发送多张图片消息
     * @param paths
     * @param isOri
     */
    private sendImagesAction;
    private sendGifImage;
    private sendNormalImage;
    private isGifByMimeType;
    /**
     * 延时
     * @param ms
     * @returns
     */
    private delay;
    /**
     * 复制文件
     * @param uris
     * @returns
     */
    private copyToSandBox;
    /**
     * 显示阅后即焚提示
     */
    showDestructTipDialog(): void;
    private isMentionedMessage;
    set unreadNewMsgArr(y215: Message[]);
    get unreadNewMsgArr(): Message[];
    set firstUnreadMessage(x215: Message | undefined);
    get firstUnreadMessage(): Message | undefined;
    set shouldScrollTop(w215: boolean);
    get shouldScrollTop(): boolean;
    set pageShow(v215: boolean);
    get pageShow(): boolean;
    setPreferencesValue(t215: string, u215: boolean): void;
    getPreferencesValue(s215: string): boolean;
}
export declare enum OperationType {
    DELETE = 0,
    RECALL_MESSAGE = 1,
    ON_MESSAGE_RECALLED = 2
}
