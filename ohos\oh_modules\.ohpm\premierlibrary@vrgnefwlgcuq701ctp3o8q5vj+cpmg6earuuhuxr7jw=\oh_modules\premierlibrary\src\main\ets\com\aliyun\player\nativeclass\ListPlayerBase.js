import premierlibrary from 'libpremierlibrary.so';
export class ListPlayerBase {
    constructor(j22, k22, l22, m22) {
        this.mNativeContext = 0;
        this.getNativeContext = () => {
            return this.mNativeContext;
        };
        this.setNativeContext = (p22) => {
            this.mNativeContext = p22;
        };
        premierlibrary.nListConstruct(this, k22, l22, m22);
    }
    stop() {
        premierlibrary.nListStop(this);
    }
    removeSource(i22) {
        premierlibrary.nListRemoveSource(this, i22);
    }
    clear() {
        premierlibrary.nListClear(this);
    }
    getCurrentUid() {
        return premierlibrary.nListGetCurrentUid(this);
    }
    setPreloadCount(h22) {
        premierlibrary.nListSetPreloadCount(this, h22);
    }
    setPreloadCountWithPrevAndNext(f22, g22) {
        premierlibrary.nListSetPreloadCountWithPrevAndNext(this, f22, g22);
    }
    setPreloadScene(e22) {
        premierlibrary.nListSetPreloadScene(this, e22);
    }
    enablePreloadStrategy(c22, d22) {
        premierlibrary.nListEnablePreloadStrategy(this, c22, d22);
    }
    setPreloadStrategyParam(a22, b22) {
        premierlibrary.nListSetPreloadStrategyParam(this, a22, b22);
    }
    setMaxPreloadMemorySizeMB(z21) {
        premierlibrary.nListSetMaxPreloadMemorySizeMB(this, z21);
    }
    getMaxPreloadMemorySizeMB() {
        return premierlibrary.nListGetMaxPreloadMemorySizeMB(this);
    }
    SetMultiBitratesMode(y21) {
        premierlibrary.nListSetMultiBitratesMode(this, y21);
    }
    GetMultiBitratesMode() {
        return premierlibrary.nListGetMultiBitratesMode(this);
    }
    release() {
        premierlibrary.nListRelease(this);
    }
}
