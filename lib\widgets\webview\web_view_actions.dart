enum WebViewActions {
  carInfoChange,
  uploadLog,
  bangSafeCheckCode,
  networkState,
  goBack,
  closePage,
  goRootPage,
  getIosPosition,
  showShare,
  goShare,
  goShareToMini,
  showNoLogin,
  goSendPost,
  goClub,
  goPost,
  goToUser,
  goToUserHomePage,
  saveLocalImg,
  showPic,
  goCommonWeb,
  toast,
  goCustomerService,
  goShareBtnWeb,
  goCommonOthersWeb,
  addCalendarEvent,
  sendSMS,
  forbidPanGesture,
  appealAuthentication,
  getIdCardInfo,
  scanCode,
  goScanCode,
  goTakePicture,
  goAdvertisement,
  postCarBuyingExperience,
  goCarTypeDetail,
  openInSystemBrowser,
  decryptText,
  sureToBuyGift,
  goApprove,
  goCommodityDetail,
  goNewCommodityDetail,
  goOrderReturn,
  goInvoiceList,
  goShop,
  goNewShop,
  goCommodityIndexByPageCode,
  goNavigation,
  goChat,
  goGroupChat,
  goCarShop,
  goLifeServiceList,
  shareToPartnerTreasureHouseActivity,
  openWXMiniProgram,
  goRecommend,
  goTechArea,
  goTechQA,
  checkWeChatAPP,
  openAppWithURLSchemes,
  goBindCar,
  toggleCarShowSuccess,
  closeAndOpenNewWeb,
  goAddressChoose,
  jumpStep,
  goTopic,
  shareImage,
  postReply,
  hideTopView,
  tipOffPost,
  goLabelPostList,
  goTCLive,
  goCommodityIndex,
  goCouponGoodsList,
  goShareMiniProgramToWechatSession,
  goCart,
  goChangeMobile,
  forceLogOut,
  goWelfareGifts,
  getPateoToken,
  getMessageToken,
  goCarControl,
  getPosition,
  pateoLogin,
  goPayApp,
  goScorePayApp,
  goPay,
  goScorePay,
  getADSkipWithLinkTypeAndLinkUrl,
  removeTaskCenterDot,
  taskSensors,
  getAppCode,
  goSendLingPost,
  refreshToTravelHome,
  goSignCalendar,
  goMyInfo,
  goWXOpenBusiness,
  jumpToTaoBao,
  goTopicList,
  faceDetect,
  getCurrentEstimateInfo,
  setWeixinPayReferer,
  scanCodeCharge,
}

extension WebViewActionsExtension on WebViewActions {
  String get name {
    switch (this) {
      case WebViewActions.carInfoChange:
        return "carInfoChange";
      case WebViewActions.uploadLog:
        return "uploadLogOhos";
      case WebViewActions.bangSafeCheckCode:
        return "bangSafeCheckCodeOhos";
      case WebViewActions.networkState:
        return "networkStateOhos";
      case WebViewActions.goBack:
        return "goBackOhos";
      case WebViewActions.closePage:
        return "closePageOhos";
      case WebViewActions.goRootPage:
        return "goRootPageOhos";
      case WebViewActions.getIosPosition:
        return "getIosPositionOhos";
      case WebViewActions.showShare:
        return "showShareOhos";
      case WebViewActions.goShare:
        return "goShareOhos";
      case WebViewActions.goShareToMini:
        return "goShareToMiniOhos";
      case WebViewActions.showNoLogin:
        return "showNoLoginOhos";
      case WebViewActions.goSendPost:
        return "goSendPostOhos";
      case WebViewActions.goClub:
        return "goClubOhos";
      case WebViewActions.goPost:
        return "goPostOhos";
      case WebViewActions.goToUser:
        return "goToUserOhos";
      case WebViewActions.goToUserHomePage:
        return "goToUserHomePageOhos";
      case WebViewActions.saveLocalImg:
        return "saveLocalImgOhos";
      case WebViewActions.showPic:
        return "showPicOhos";
      case WebViewActions.goCommonWeb:
        return "goCommonWebOhos";
      case WebViewActions.toast:
        return "toastOhos";
      case WebViewActions.goCustomerService:
        return "goCustomerServiceOhos";
      case WebViewActions.goShareBtnWeb:
        return "goShareBtnWebOhos";
      case WebViewActions.goCommonOthersWeb:
        return "goCommonOthersWebOhos";
      case WebViewActions.addCalendarEvent:
        return "addCalendarEventOhos";
      case WebViewActions.sendSMS:
        return "sendSMSOhos";
      case WebViewActions.forbidPanGesture:
        return "forbidPanGestureOhos";
      case WebViewActions.appealAuthentication:
        return "appealAuthenticationOhos";
      case WebViewActions.getIdCardInfo:
        return "getIdCardInfoOhos";
      case WebViewActions.scanCode:
        return "scanCodeOhos";
      case WebViewActions.goScanCode:
        return "goScanCodeOhos";
      case WebViewActions.goTakePicture:
        return "goTakePicture";
      case WebViewActions.goAdvertisement:
        return "goAdvertisementOhos";
      case WebViewActions.postCarBuyingExperience:
        return "postCarBuyingExperienceOhos";
      case WebViewActions.goCarTypeDetail:
        return "goCarTypeDetailOhos";
      case WebViewActions.openInSystemBrowser:
        return "openInSystemBrowserOhos";
      case WebViewActions.decryptText:
        return "decryptTextOhos";
      case WebViewActions.sureToBuyGift:
        return "sureToBuyGiftOhos";
      case WebViewActions.goApprove:
        return "goApproveOhos";
      case WebViewActions.goCommodityDetail:
        return "goCommodityDetailOhos";
      case WebViewActions.goNewCommodityDetail:
        return "goNewCommodityDetailOhos";
      case WebViewActions.goOrderReturn:
        return "goOrderReturnOhos";
      case WebViewActions.goInvoiceList:
        return "goInvoiceListOhos";
      case WebViewActions.goShop:
        return "goShopOhos";
      case WebViewActions.goNewShop:
        return "goNewShopOhos";
      case WebViewActions.goCommodityIndexByPageCode:
        return "goCommodityIndexByPageCodeOhos";
      case WebViewActions.goNavigation:
        return "goNavigationOhos";
      case WebViewActions.goChat:
        return "goChatOhos";
      case WebViewActions.goGroupChat:
        return "goGroupChatOhos";
      case WebViewActions.goCarShop:
        return "goCarShopOhos";
      case WebViewActions.goLifeServiceList:
        return "goLifeServiceListOhos";
      case WebViewActions.shareToPartnerTreasureHouseActivity:
        return "shareToPartnerTreasureHouseActivityOhos";
      case WebViewActions.openWXMiniProgram:
        return "openWXMiniProgramOhos";
      case WebViewActions.goRecommend:
        return "goRecommendOhos";
      case WebViewActions.goTechArea:
        return "goTechAreaOhos";
      case WebViewActions.goTechQA:
        return "goTechQAOhos";
      case WebViewActions.checkWeChatAPP:
        return "checkWeChatAPPOhos";
      case WebViewActions.openAppWithURLSchemes:
        return "openAppWithURLSchemesOhos";
      case WebViewActions.goBindCar:
        return "goBindCarOhos";
      case WebViewActions.toggleCarShowSuccess:
        return "toggleCarShowSuccessOhos";
      case WebViewActions.closeAndOpenNewWeb:
        return "closeAndOpenNewWebOhos";
      case WebViewActions.goAddressChoose:
        return "goAddressChooseOhos";
      case WebViewActions.jumpStep:
        return "jumpStepOhos";
      case WebViewActions.goTopic:
        return "goTopicOhos";
      case WebViewActions.shareImage:
        return "shareImageOhos";
      case WebViewActions.postReply:
        return "postReplyOhos";
      case WebViewActions.hideTopView:
        return "hideTopViewOhos";
      case WebViewActions.tipOffPost:
        return "tipOffPostOhos";
      case WebViewActions.goLabelPostList:
        return "goLabelPostListOhos";
      case WebViewActions.goTCLive:
        return "goTCLiveOhos";
      case WebViewActions.goCommodityIndex:
        return "goCommodityIndexOhos";
      case WebViewActions.goCouponGoodsList:
        return "goCouponGoodsListOhos";
      case WebViewActions.goShareMiniProgramToWechatSession:
        return "goShareMiniProgramToWechatSessionOhos";
      case WebViewActions.goCart:
        return "goCartOhos";
      case WebViewActions.goChangeMobile:
        return "goChangeMobileOhos";
      case WebViewActions.forceLogOut:
        return "forceLogOutOhos";
      case WebViewActions.goWelfareGifts:
        return "goWelfareGiftsOhos";
      case WebViewActions.getPateoToken:
        return "getPateoTokenOhos";
      case WebViewActions.getMessageToken:
        return "getMessageTokenOhos";
      case WebViewActions.goCarControl:
        return "goCarControlOhos";
      case WebViewActions.getPosition:
        return "getPositionOhos";
      case WebViewActions.pateoLogin:
        return "pateoLoginOhos";
      case WebViewActions.goPayApp:
        return "goPayAppOhos";
      case WebViewActions.goScorePayApp:
        return "goScorePayAppOhos";
      case WebViewActions.goPay:
        return "goPayOhos";
      case WebViewActions.goScorePay:
        return "goScorePayOhos";
      case WebViewActions.getADSkipWithLinkTypeAndLinkUrl:
        return "getADSkipWithLinkTypeAndLinkUrlOhos";
      case WebViewActions.removeTaskCenterDot:
        return "removeTaskCenterDotOhos";
      case WebViewActions.taskSensors:
        return "taskSensorsOhos";
      case WebViewActions.getAppCode:
        return "getAppCodeOhos";
      case WebViewActions.goSendLingPost:
        return "goSendLingPostOhos";
      case WebViewActions.refreshToTravelHome:
        return "refreshToTravelHomeOhos";
      case WebViewActions.goSignCalendar:
        return "goSignCalendarOhos";
      case WebViewActions.goMyInfo:
        return "goMyInfoOhos";
      case WebViewActions.goWXOpenBusiness:
        return "goWXOpenBusinessOhos";
      case WebViewActions.jumpToTaoBao:
        return "jumpToTaoBaoOhos";
      case WebViewActions.goTopicList:
        return "goTopicListOhos";
      case WebViewActions.faceDetect:
        return "faceDetect";
      case WebViewActions.getCurrentEstimateInfo:
        return "getCurrentEstimateInfoOhos";
      case WebViewActions.setWeixinPayReferer:
        return "setWeixinPayRefererOhos";
      case WebViewActions.scanCodeCharge:
        return "scanCodeChargeOhos";
    }
  }
}