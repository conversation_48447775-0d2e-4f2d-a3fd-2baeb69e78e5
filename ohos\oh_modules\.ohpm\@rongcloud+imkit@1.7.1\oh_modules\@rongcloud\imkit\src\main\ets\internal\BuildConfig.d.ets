// @keepTs
// @ts-nocheck
/**
 * @date 2024/12/6
 * <AUTHOR>
 */
export declare class BuildConfig {
    /**
     * SDK 名称
     */
    static readonly sdkName = "IMKit";
    /**
     * SDK 版本
     */
    static readonly sdkVersion = "1.7.1";
    /**
     * SDK 完整版本号
     *```
     * 公有云 SDK ：fullSdkVersion 和 sdkVersion 完全一致
     * 私有云 SDK ：命名规则仿照 Web 端私有云
     *  "${sdkVersion}-enterprise.${IMPrivatePatch}"  IMPrivatePatch 代表私有云该版本的第几个补丁包
     *  示例："1.0.2-enterprise.3" 代表私有云的 1.0.2 第三个补丁包
     *```
     */
    static readonly fullSdkVersion = "1.7.1";
    /**
     * SDK commitID
     */
    static readonly commitId = "7ca1045a";
    /**
     * 编译节点
     */
    static readonly buildNumber = 86;
    /**
     * SDK 编译时间
     */
    static readonly buildTime = "2025-07-25 18:32:37";
    /**
     * 打包任务名称。如：Harmony-PaaS-Develop
     * @since 1.4.0
     * */
    static readonly jobName = "Harmony-PaaS-Develop";
}
