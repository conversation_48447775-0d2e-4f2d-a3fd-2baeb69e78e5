import http from '@ohos.net.http';
import util from "@ohos.util";
export class HttpClientHelper {
    constructor(z43) {
        this.mReferer = null;
        this.urlRequest = null;
        this.mNetworkTimeout = HttpClientHelper.CONNECTION_TIMEOUT;
        this.mHttpProxy = null;
        this.mUserAgent = null;
        this.mCustomHeaders = [{}];
        this.mUrl = z43;
    }
    setRefer(y43) {
        this.mReferer = y43;
    }
    setTimeout(x43) {
        this.mNetworkTimeout = x43;
    }
    setHttpProxy(w43) {
        this.mHttpProxy = w43;
    }
    setUserAgent(v43) {
        this.mUserAgent = v43;
    }
    setCustomHeaders(u43) {
        this.mCustomHeaders = u43;
    }
    stop() {
        if (this.urlRequest != null) {
            setTimeout(() => {
                this.urlRequest?.destroy();
            }, 0);
        }
    }
    async doGet() {
        if (this.mUrl.startsWith("https://") || this.mUrl.startsWith("http://")) {
            this.urlRequest = http.createHttp();
        }
        else {
            return null;
        }
        if (this.urlRequest == null) {
            return null;
        }
        let g43 = [];
        if (this.mReferer != null) {
            g43.push({ 'Referer': this.mReferer });
        }
        if (this.mUserAgent != null) {
            g43.push({ 'User-Agent': this.mUserAgent });
        }
        if (this.mCustomHeaders.length > 0) {
            for (let s43 of this.mCustomHeaders) {
                if (s43 != null) {
                    g43.push(s43);
                }
            }
        }
        let h43 = "";
        let i43 = false;
        if (this.mHttpProxy != null) {
            h43 = this.mHttpProxy;
            i43 = true;
        }
        else {
            h43 = this.mUrl;
        }
        let j43 = {
            method: http.RequestMethod.GET,
            header: g43,
            connectTimeout: this.mNetworkTimeout,
            readTimeout: this.mNetworkTimeout,
            usingProxy: i43,
        };
        return new Promise((l43, m43) => {
            if (this.urlRequest == null) {
                l43(null);
                return;
            }
            this.urlRequest.request(h43, j43, (o43, p43) => {
                if (o43) {
                    console.error('error:' + JSON.stringify(o43));
                    this.urlRequest?.destroy();
                    l43(null);
                    return;
                }
                if (p43.responseCode == http.ResponseCode.OK) {
                    if (p43.resultType == http.HttpDataType.STRING) {
                        l43(p43.result);
                    }
                    else if (p43.resultType == http.HttpDataType.ARRAY_BUFFER) {
                        let q43 = util.TextDecoder.create('utf-8');
                        let r43 = q43.decodeToString(new Uint8Array(p43.result));
                        l43(r43 ? r43 : null);
                    }
                    else {
                        console.info("response failed, resultType: " + p43.resultType);
                        this.urlRequest?.destroy();
                        l43(null);
                    }
                }
                else {
                    console.info("response failed, responseCode is " + p43.responseCode);
                    this.urlRequest?.destroy();
                    l43(null);
                }
            });
        });
    }
}
HttpClientHelper.CONNECTION_TIMEOUT = 10000;
