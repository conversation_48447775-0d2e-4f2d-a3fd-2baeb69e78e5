import { VidSourceBase } from './VidSourceBase';
export declare class VidAuth extends VidSourceBase {
    private mVid;
    private mPlayAuth;
    private mRegion;
    constructor();
    private nativeGetVid;
    private nativeSetVid;
    private nativeGetPlayAuth;
    private nativeSetPlayAuth;
    private nativeGetRegion;
    private nativeSetRegion;
    /**
     * 设置清晰度相关信息
     *
     * @param quality      期望播放的清晰度
     * @param forceQuality 是否强制使用此清晰度。如果强制，则在没有对应清晰度的情况下播放不了。
     */
    /****
     * Definition settings
     *
     * @param quality      Specify a definition for playback.
     * @param forceQuality Indicate whether to force the player to play the media with the specified definition. However, if the media does not support the specified definition, then it cannot be played.
     */
    setQuality(c37: string, d37: boolean): void;
    /**
     * 获取vid
     *
     * @return vid
     */
    /****
     * Query the VID.
     *
     * @return The VID.
     */
    getVid(): string;
    /**
     * 设置vid
     *
     * @param mVid vid。
     */
    /****
     * Set the VID.
     *
     * @param mVid The VID.
     */
    setVid(b37: string): void;
    /**
     * 获取播放凭证
     *
     * @return 播放凭证
     */
    /****
     * Query the playback credential.
     *
     * @return The playback credential.
     */
    getPlayAuth(): string;
    /**
     * 设置播放凭证
     *
     * @param mPlayAuth 播放凭证
     */
    /****
     * Set the playback credential.
     *
     * @param mPlayAuth The playback credential.
     */
    setPlayAuth(a37: string): void;
    /**
     * 获取地域
     *
     * @return 地域
     */
    /****
     * Query region information.
     *
     * @return The region information.
     */
    getRegion(): string;
    /**
     * 设置地域
     *
     * @param mRegion 地域
     */
    /****
     * Specify regions.
     *
     * @param mRegion The specified regions.
     */
    setRegion(z36: string): void;
}
