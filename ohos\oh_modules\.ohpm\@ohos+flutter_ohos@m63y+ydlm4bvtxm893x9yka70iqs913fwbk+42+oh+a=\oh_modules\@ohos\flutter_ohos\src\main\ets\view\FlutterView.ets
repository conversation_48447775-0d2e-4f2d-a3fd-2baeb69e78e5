/*
* Copyright (c) 2023 Hunan OpenValley Digital Industry Development Co., Ltd. All rights reserved.
* Use of this source code is governed by a BSD-style license that can be
* found in the LICENSE_KHZG file.
*/

import FlutterEngine from '../embedding/engine/FlutterEngine';
import Log from '../util/Log';
import { DVModel, DVModelChildren, DVModelEvents, DVModelParameters } from './DynamicView/dynamicView';
import {display} from '@kit.ArkUI'
import FlutterManager from '../embedding/ohos/FlutterManager';
import window from '@ohos.window';
import KeyboardManager from '../embedding/ohos/KeyboardManager';
import MouseCursorPlugin from '../plugin/mouse/MouseCursorPlugin';
import Settings from '../embedding/ohos/Settings';
import ArrayList from '@ohos.util.ArrayList';
import { EmbeddingNodeController } from '../embedding/ohos/EmbeddingNodeController';
import PlatformView, { Params } from '../plugin/platform/PlatformView';
import { JSON } from '@kit.ArkTS';
import { accessibility } from '@kit.AccessibilityKit';
import TextInputPlugin from '../plugin/editing/TextInputPlugin';

const TAG = "FlutterViewTag";
const EVENT_BACK_PRESS = 'EVENT_BACK_PRESS';

export class ViewportMetrics {
  devicePixelRatio: number = 1.0;
  physicalWidth: number = 0;
  physicalHeight: number = 0;
  physicalViewPaddingTop: number = 0;
  physicalViewPaddingRight: number = 0;
  physicalViewPaddingBottom: number = 0;
  physicalViewPaddingLeft: number = 0;
  physicalViewInsetTop: number = 0;
  physicalViewInsetRight: number = 0;
  physicalViewInsetBottom: number = 0;
  physicalViewInsetLeft: number = 0;
  systemGestureInsetTop: number = 0;
  systemGestureInsetRight: number = 0;
  systemGestureInsetBottom: number = 0;
  systemGestureInsetLeft: number = 0;
  physicalTouchSlop: number = -1;
  displayFeatures: ArrayList<DisplayFeature> = new ArrayList();

  clone(): ViewportMetrics {
    const copy = new ViewportMetrics();
    copy.devicePixelRatio = this.devicePixelRatio;
    copy.physicalWidth = this.physicalWidth;
    copy.physicalHeight = this.physicalHeight;
    copy.physicalViewPaddingTop = this.physicalViewPaddingTop;
    copy.physicalViewPaddingRight = this.physicalViewPaddingRight;
    copy.physicalViewPaddingBottom = this.physicalViewPaddingBottom;
    copy.physicalViewPaddingLeft = this.physicalViewPaddingLeft;
    copy.physicalViewInsetTop = this.physicalViewInsetTop;
    copy.physicalViewInsetRight = this.physicalViewInsetRight;
    copy.physicalViewInsetBottom = this.physicalViewInsetBottom;
    copy.physicalViewInsetLeft = this.physicalViewInsetLeft;
    copy.systemGestureInsetTop = this.systemGestureInsetTop;
    copy.systemGestureInsetRight = this.systemGestureInsetRight;
    copy.systemGestureInsetBottom = this.systemGestureInsetBottom;
    copy.systemGestureInsetLeft = this.systemGestureInsetLeft;
    copy.physicalTouchSlop = this.physicalTouchSlop;
    copy.displayFeatures = this.displayFeatures;
    return copy;
  }

  isEqual(other: ViewportMetrics): boolean {
    return this.devicePixelRatio === other.devicePixelRatio &&
      this.physicalWidth === other.physicalWidth &&
      this.physicalHeight === other.physicalHeight &&
      this.physicalViewPaddingTop === other.physicalViewPaddingTop &&
      this.physicalViewPaddingRight === other.physicalViewPaddingRight &&
      this.physicalViewPaddingBottom === other.physicalViewPaddingBottom &&
      this.physicalViewPaddingLeft === other.physicalViewPaddingLeft &&
      this.physicalViewInsetTop === other.physicalViewInsetTop &&
      this.physicalViewInsetRight === other.physicalViewInsetRight &&
      this.physicalViewInsetBottom === other.physicalViewInsetBottom &&
      this.physicalViewInsetLeft === other.physicalViewInsetLeft &&
      this.systemGestureInsetTop === other.systemGestureInsetTop &&
      this.systemGestureInsetRight === other.systemGestureInsetRight &&
      this.systemGestureInsetBottom === other.systemGestureInsetBottom &&
      this.systemGestureInsetLeft === other.systemGestureInsetLeft &&
      this.physicalTouchSlop === other.physicalTouchSlop &&
      this.displayFeatures === other.displayFeatures;
  }
}

export class DisplayFeature {
  bounds: display.Rect[];
  type: DisplayFeatureType;
  state: DisplayFeatureState;

  constructor(bounds: display.Rect[], type: DisplayFeatureType, state: DisplayFeatureState) {
    this.bounds = bounds;
    this.type = type;
    this.state = state;
  }

  getBounds(): display.Rect[] {
    return this.bounds;
  }

  getType(): DisplayFeatureType {
    return this.type;
  }

  getState(): DisplayFeatureState {
    return this.state
  }

  setBounds(bounds: display.Rect[]): void {
    this.bounds = bounds;
  }

  setType(type: DisplayFeatureType): void {
    this.type = type;
  }

  setState(state: DisplayFeatureState): void {
    this.state = state;
  }
}

export enum DisplayFeatureType{
  UNKNOWN = 0,
  FOLD = 1,
  HINGE = 2,
  CUTOUT = 3
};

export enum DisplayFeatureState{
  UNKNOWN = 0,
  POSTURE_FLAT = 1,
  POSTURE_HALF_OPENED = 2,
};

export enum DisplayFoldStatus{
  FOLD_STATUS_UNKNOWN = 0,
  FOLD_STATUS_EXPANDED = 1,
  FOLD_STATUS_FOLDED = 2,
  FOLD_STATUS_HALF_FOLDED = 3
};

export class PlatformViewParas {
  width: number = 0.0;
  height: number = 0.0;
  top: number = 0.0;
  left: number = 0.0;
  direction: Direction = Direction.Auto;

  setValue(width: number, height: number, top: number, left: number): void {
    this.width = width;
    this.height = height;
    this.top = top;
    this.left = left;
  }

  setOffset(top: number, left: number): void {
    this.top = top;
    this.left = left;
  }
}

export class FlutterView {

  private flutterEngine: FlutterEngine | null = null
  private id: string = ""
  private dVModel: DVModel = new DVModel("Stack", new DVModelParameters(), new DVModelEvents(), new DVModelChildren(), null);
  private wrapBuilder: WrappedBuilder<[Params]> | undefined = undefined;
  private platformView: PlatformView | undefined = undefined;
  private isSurfaceAvailableForRendering: boolean = false
  private viewportMetrics = new ViewportMetrics();
  private displayInfo?: display.Display;
  private keyboardManager: KeyboardManager | null = null;
  private mainWindow: window.Window | null = null;
  private mouseCursorPlugin?: MouseCursorPlugin;
  private textInputPlugin?: TextInputPlugin;
  private uiContext?: UIContext | undefined;
  private context: Context;
  private settings?: Settings;
  private mFirstFrameListeners: ArrayList<FirstFrameListener>;
  private isFlutterUiDisplayed: boolean = false;
  private surfaceId: string = "0";
  private nodeController: EmbeddingNodeController = new EmbeddingNodeController();
  private platformViewSize: PlatformViewParas = new PlatformViewParas();
  private checkFullScreen: boolean = true;
  private checkKeyboard: boolean = true;
  private checkGesture: boolean = true;
  private systemAvoidArea: window.AvoidArea;
  private navigationAvoidArea: window.AvoidArea;
  private gestureAvoidArea: window.AvoidArea;
  private keyboardAvoidArea: window.AvoidArea;

  constructor(viewId: string, context: Context) {
    this.id = viewId;
    this.context = context;
    this.displayInfo = display.getDefaultDisplaySync();
    this.viewportMetrics.devicePixelRatio = this.displayInfo?.densityPixels;

    this.buildDisplayFeatures(display.getFoldStatus());
    this.mainWindow = FlutterManager.getInstance()
      .getWindowStage(FlutterManager.getInstance().getUIAbility(context))
      .getMainWindowSync();
    this.mFirstFrameListeners = new ArrayList<FirstFrameListener>();

    this.mainWindow?.on('windowSizeChange', this.windowSizeChangeCallback);
    this.mainWindow?.on('avoidAreaChange', this.avoidAreaChangeCallback);
    this.mainWindow?.on('windowStatusChange', this.windowStatusChangeCallback);

    //监听系统无障碍服务状态改变
    accessibility.on('accessibilityStateChange', (data: boolean) => {
      Log.i(TAG, `subscribe accessibility state change, result: ${JSON.stringify(data)}`);
      this.flutterEngine?.getFlutterNapi()?.accessibilityStateChange(data);
    });
    this.mainWindow?.on('keyboardHeightChange', this.keyboardHeightChangeCallback);
    this.systemAvoidArea = this.mainWindow?.getWindowAvoidArea(window.AvoidAreaType.TYPE_SYSTEM);
    this.navigationAvoidArea = this.mainWindow?.getWindowAvoidArea(window.AvoidAreaType.TYPE_NAVIGATION_INDICATOR);
    this.gestureAvoidArea = this.mainWindow?.getWindowAvoidArea(window.AvoidAreaType.TYPE_SYSTEM_GESTURE);
    this.keyboardAvoidArea = this.mainWindow?.getWindowAvoidArea(window.AvoidAreaType.TYPE_KEYBOARD);

    // 监听折叠状态的改变
    display?.on('foldStatusChange', (data: display.FoldStatus) => {
      Log.d(TAG, `Fold status change to ${JSON.stringify(data)}`)
      this.buildDisplayFeatures(data);
    })

    // Subscribes to display changes. Example: event that the display size is changed.
    try {
      display.on("change", ()=>{
        this.displayInfo = display.getDefaultDisplaySync();
        if (this.viewportMetrics.devicePixelRatio != this.displayInfo?.densityPixels) {
          this.viewportMetrics.devicePixelRatio = this.displayInfo?.densityPixels;
          Log.i(TAG, "Display on: " + JSON.stringify(this.displayInfo))
          this.updateViewportMetrics()
        }
      });
    } catch (e) {
      Log.e(TAG, "displayInfo error" + JSON.stringify(e));
    }
  }

  private async buildDisplayFeatures(foldStatus: display.FoldStatus) {
    let displayFeatures: ArrayList<DisplayFeature> = new ArrayList();
    let bound: display.Rect[];
    let state: DisplayFeatureState = DisplayFeatureState.UNKNOWN;
    let type: DisplayFeatureType = DisplayFeatureType.FOLD;
    const displayInfos = display.getDefaultDisplaySync();
    if (display.isFoldable()) {
      switch (foldStatus) {
        case display.FoldStatus.FOLD_STATUS_EXPANDED:
          type = DisplayFeatureType.FOLD;
          state = DisplayFeatureState.POSTURE_FLAT;
          break;
        case display.FoldStatus.FOLD_STATUS_HALF_FOLDED:
          type = DisplayFeatureType.FOLD;
          state = DisplayFeatureState.POSTURE_HALF_OPENED;
          break;
        case display.FoldStatus.FOLD_STATUS_UNKNOWN:
          type = DisplayFeatureType.UNKNOWN;
          state = DisplayFeatureState.UNKNOWN;
          break;
        default:
          state = DisplayFeatureState.UNKNOWN;
          type = DisplayFeatureType.FOLD;
          break;
      }
      const displays = await display.getAllDisplays();
      for (let i = 0; i < displays.length; i++) {
        let cutoutInfo = await displays[i].getCutoutInfo();
        bound = cutoutInfo.boundingRects;
        displayFeatures.add(new DisplayFeature(bound, type, state));
      }
      Log.d(TAG, `FOLD device displayFeatures is : ${JSON.stringify(displayFeatures)}`)
    } else {
      type = DisplayFeatureType.CUTOUT;
      state = DisplayFeatureState.UNKNOWN;
      let infos = await displayInfos?.getCutoutInfo();
      bound = infos.boundingRects;
      displayFeatures.add(new DisplayFeature(bound, type, state));
      Log.d(TAG, `UNFold device displayFeatures is : ${JSON.stringify(displayFeatures)}`)
    }
    this.viewportMetrics.displayFeatures = displayFeatures;
    this.updateViewportMetrics();
  }

  private  windowSizeChangeCallback = (data: window.Size) => {
    Log.i(TAG, "windowSizeChangeCallback w:" + data.width + ", h:" + data.height);
    if (this.isAttachedToFlutterEngine()) {
      this.onAreaChange(null);
    }
  }

  private avoidAreaChangeCallback = (data: window.AvoidAreaOptions) => {
    Log.i(TAG, "avoidAreaChangeCallback, type=" + data.type);
    if (data.type == window.AvoidAreaType.TYPE_SYSTEM) { //0
      this.systemAvoidArea = data.area;
    } else if (data.type == window.AvoidAreaType.TYPE_SYSTEM_GESTURE) { //2
      this.gestureAvoidArea = data.area;
    } else if (data.type == window.AvoidAreaType.TYPE_KEYBOARD) { //3
      this.keyboardAvoidArea = data.area;
    } else if (data.type == window.AvoidAreaType.TYPE_NAVIGATION_INDICATOR) { //4
      this.navigationAvoidArea = data.area;
    }
    if (this.isAttachedToFlutterEngine()) {
      this.onAreaChange(null);
    }
  }

  private windowStatusChangeCallback = (data: window.WindowStatusType) => {
    Log.i(TAG, "windowStatusChangeCallback " + data);
    if (this.isAttachedToFlutterEngine()) {
      FlutterManager.getInstance().getFullScreenListener().onScreenStateChanged(data);
    }
  };

  private keyboardHeightChangeCallback = (data: number) => {
    Log.i(TAG, "keyboardHeightChangeCallback " + data);
    this.keyboardAvoidArea.bottomRect.height = data;
    if (this.isAttachedToFlutterEngine()) {
      this.onAreaChange(null);
    }
  };

  getId(): string {
    return this.id
  }

  setSurfaceId(surfaceId: string): void {
    this.surfaceId = surfaceId;
  }

  getSurfaceId(): string {
    return this.surfaceId;
  }

  getEmbeddingNodeController(): EmbeddingNodeController {
    return this.nodeController;
  }

  setWrappedBuilder(wrappedBuilder: WrappedBuilder<[Params]>) {
    this.wrapBuilder = wrappedBuilder;
  }

  getWrappedBuilder(): WrappedBuilder<[Params]> | undefined {
    return this.wrapBuilder;
  }

  setPlatformView(platformView: PlatformView) {
    this.platformView = platformView;
  }

  getPlatformView(): PlatformView | undefined {
    return this.platformView;
  }

  getPlatformViewSize(): PlatformViewParas {
    return this.platformViewSize;
  }

  getDVModel() {
    return this.dVModel
  }

  getKeyboardHeight() {
    return this.keyboardAvoidArea.bottomRect.height
  }

  onDestroy() {
    try {
      this.mainWindow?.off('windowSizeChange', this.windowSizeChangeCallback);
      this.mainWindow?.off('avoidAreaChange', this.avoidAreaChangeCallback);
      this.mainWindow?.off('windowStatusChange', this.windowStatusChangeCallback);
      accessibility.off('accessibilityStateChange', (data: boolean) => {
        Log.i(TAG, `unsubscribe accessibility state change, result: ${JSON.stringify(data)}`);
      });
      this.mainWindow?.off('keyboardHeightChange', this.keyboardHeightChangeCallback);
      display.off('foldStatusChange', (status: display.FoldStatus) => {
        Log.d(TAG, `foldStatusChange listner destory`);
      })
    } catch (e) {
      Log.e(TAG, "mainWindow off error: " + JSON.stringify(e));
    }
    this.mainWindow = null;

    try {
      display.off("change", ()=>{
        this.displayInfo = display.getDefaultDisplaySync();
        this.viewportMetrics.devicePixelRatio = this.displayInfo?.densityPixels;
        Log.i(TAG, "Display Info: " + JSON.stringify(this.displayInfo))
        this.updateViewportMetrics()
      });
    } catch (e) {
      Log.e(TAG, "displayInfo off error" + JSON.stringify(e));
    }
  }

  attachToFlutterEngine(flutterEngine: FlutterEngine): void {
    if (this.isAttachedToFlutterEngine()) {
      if (flutterEngine == this.flutterEngine) {
        Log.i(TAG, "Already attached to this engine. Doing nothing.");
        return;
      }
      // Detach from a previous FlutterEngine so we can attach to this new one.f
      Log.i(
        TAG,
        "Currently attached to a different engine. Detaching and then attaching"
          + " to new engine.");
      this.detachFromFlutterEngine();
    }
    Log.i(TAG, "attachToFlutterEngine");
    this.flutterEngine = flutterEngine;
    if (this.isSurfaceAvailableForRendering) {
      this.flutterEngine.getFlutterNapi().xComponentAttachFlutterEngine(this.id)
    }
    this.flutterEngine?.getFlutterNapi()?.updateRefreshRate(this.displayInfo!.refreshRate)
    flutterEngine.getPlatformViewsController()?.attachToView(this);
    this.updateViewportMetrics()

    let newArea: Area | null = {
      width: px2vp(this.displayInfo!.width),
      height: px2vp(this.displayInfo!.height),
      position: { x: 0, y: 0 },
      globalPosition: { x: 0, y: 0 }
    };
    if (this.viewportMetrics.physicalWidth != 0 || this.viewportMetrics.physicalHeight != 0) {
      newArea = null;
    }
    this.onAreaChange(newArea, true);

    this.context.eventHub.on(EVENT_BACK_PRESS, () => {
      if (this?.getKeyboardHeight() == 0) {
        this.flutterEngine?.getNavigationChannel()?.popRoute();
        this.flutterEngine?.getPlatformViewsController()?.setBackNodeControllers();
      } else {
        this.flutterEngine?.getTextInputChannel()?.textInputMethodHandler?.hide();
      }
    });

    let windowId = this.mainWindow?.getWindowProperties()?.id ?? 0
    this.mouseCursorPlugin = new MouseCursorPlugin(windowId, this.flutterEngine?.getMouseCursorChannel()!);
    this.textInputPlugin = new TextInputPlugin(this.flutterEngine?.getTextInputChannel()!);
    this.keyboardManager = new KeyboardManager(flutterEngine, this.textInputPlugin!);
    this.settings = new Settings(this.flutterEngine.getSettingsChannel()!);
    this.sendSettings();
    this.isFlutterUiDisplayed = this.flutterEngine.getFlutterNapi().isDisplayingFlutterUi;
    if (this.isFlutterUiDisplayed) {
      this.onFirstFrame();
    }
  }

  detachFromFlutterEngine(): void {
    Log.i(TAG, "detachFromFlutterEngine");

    if (!this.isAttachedToFlutterEngine()) {
      Log.d(TAG, "FlutterView not attached to an engine. Not detaching.");
      return;
    }
    if (this.isSurfaceAvailableForRendering) {
      this.flutterEngine!!.getFlutterNapi().xComponentDetachFlutterEngine(this.id)
    }
    this.flutterEngine?.getPlatformViewsController()?.detachFromView();
    this.flutterEngine = null;
    this.keyboardManager = null;
    this.textInputPlugin?.destroy();
    this.context?.eventHub.off(EVENT_BACK_PRESS);
  }

  onWindowCreated() {
    Log.d(TAG, "received onwindowCreated.");
    let _UIContext = this.mainWindow?.getUIContext();
    this.uiContext = _UIContext;
    this.sendSettings();
    Log.d(TAG, "uiContext init and sendSettings finished.");
  }

  sendSettings(): void {
    if (this.uiContext != undefined && this.isAttachedToFlutterEngine()) {
      this.settings?.sendSettings(this.uiContext.getMediaQuery());
    } else {
      Log.e(TAG, "UIContext is null, cannot send Settings!");
    }
  }

  onSurfaceCreated() {
    this.isSurfaceAvailableForRendering = true;
    if (this.isAttachedToFlutterEngine()) {
      this.flutterEngine!!.getFlutterNapi().xComponentAttachFlutterEngine(this.id)
    }
  }

  onSurfaceDestroyed() {
    this.isSurfaceAvailableForRendering = false;
    if (this.isAttachedToFlutterEngine()) {
      this.flutterEngine!!.getFlutterNapi().xComponentDetachFlutterEngine(this.id)
    }
  }

  onAccessibilityIsOpen() {
    let accessibilityState: boolean = accessibility.isOpenAccessibilitySync();
    if(accessibilityState) {
      this.flutterEngine?.getFlutterNapi()?.accessibilityStateChange(accessibilityState)
    }
    Log.i(TAG, `accessibility isOpen state -> ${JSON.stringify(accessibilityState)}`);
  }

  onAreaChange(newArea: Area | null, setFullScreen: boolean = false) {
    if (newArea != null) {
      this.viewportMetrics.physicalWidth = vp2px(newArea.width as number);
      this.viewportMetrics.physicalHeight = vp2px(newArea.height as number);
    }

    // 根据是否全屏显示，设置标题栏高度（若全屏，则及时规避）
    let isUseFullScreen = FlutterManager.getInstance().getFullScreenListener().useFullScreen();
    let isLayoutFullScreen = this.mainWindow?.getWindowProperties().isLayoutFullScreen;
    if (this.checkFullScreen && (setFullScreen || isLayoutFullScreen || isUseFullScreen)) { // 全屏显示
      this.viewportMetrics.physicalViewPaddingTop =
        this.systemAvoidArea?.topRect.height ?? this.viewportMetrics.physicalViewPaddingTop;
      this.viewportMetrics.physicalViewPaddingBottom =
        this.navigationAvoidArea?.bottomRect.height ?? this.viewportMetrics.physicalViewPaddingBottom;
    } else { // 非全屏显示（保持规避效果）
      // 顶部状态栏和底部导航栏规避为0，无平滑过渡效果
      this.viewportMetrics.physicalViewPaddingTop = 0;
      this.viewportMetrics.physicalViewPaddingBottom = 0;
    }

    this.viewportMetrics.physicalViewPaddingLeft =
      this.systemAvoidArea?.leftRect.width ?? this.viewportMetrics.physicalViewPaddingLeft;
    this.viewportMetrics.physicalViewPaddingRight =
      this.systemAvoidArea?.rightRect.width ?? this.viewportMetrics.physicalViewPaddingRight;

    this.onKeyboardAreaChange()
    this.onGestureAreaChange()
    this.updateViewportMetrics()
  }

  private onKeyboardAreaChange() {
    if (this.checkKeyboard) {
      this.viewportMetrics.physicalViewInsetTop =
        this.keyboardAvoidArea?.topRect.height ?? this.viewportMetrics.physicalViewInsetTop
      this.viewportMetrics.physicalViewInsetLeft =
        this.keyboardAvoidArea?.leftRect.width ?? this.viewportMetrics.physicalViewInsetLeft
      this.viewportMetrics.physicalViewInsetBottom =
        this.keyboardAvoidArea?.bottomRect.height ?? this.viewportMetrics.physicalViewInsetBottom
      this.viewportMetrics.physicalViewInsetRight =
        this.keyboardAvoidArea?.rightRect.width ?? this.viewportMetrics.physicalViewInsetRight
    } else {
      this.viewportMetrics.physicalViewInsetTop = 0
      this.viewportMetrics.physicalViewInsetLeft = 0
      this.viewportMetrics.physicalViewInsetBottom = 0
      this.viewportMetrics.physicalViewInsetRight = 0
    }
  }

  private onGestureAreaChange() {
    if (this.checkGesture) {
      this.viewportMetrics.systemGestureInsetTop =
        this.gestureAvoidArea?.topRect.height ?? this.viewportMetrics.systemGestureInsetTop
      this.viewportMetrics.systemGestureInsetLeft =
        this.gestureAvoidArea?.leftRect.width ?? this.viewportMetrics.systemGestureInsetLeft
      this.viewportMetrics.systemGestureInsetBottom =
        this.gestureAvoidArea?.bottomRect.height ?? this.viewportMetrics.systemGestureInsetBottom
      this.viewportMetrics.systemGestureInsetRight =
        this.gestureAvoidArea?.rightRect.width ?? this.viewportMetrics.systemGestureInsetRight
    } else {
      this.viewportMetrics.systemGestureInsetTop = 0
      this.viewportMetrics.systemGestureInsetLeft = 0
      this.viewportMetrics.systemGestureInsetBottom = 0
      this.viewportMetrics.systemGestureInsetRight = 0
    }
  }

  public isAttachedToFlutterEngine(): boolean {
    return this.flutterEngine != null
  }

  private updateViewportMetrics(): boolean {
    if (!this.isAttachedToFlutterEngine()) {
      return false;
    }
    Log.i(TAG, 'updateViewportMetrics devicePixelRatio:' + this.viewportMetrics.devicePixelRatio);

    const displayFeatures = this.viewportMetrics.displayFeatures;
    let boundCount = 0;
    for (let i = 0; i < displayFeatures.length; i++) {
      boundCount = boundCount + displayFeatures[i].getBounds().length;
    }
    let displayFeatureBound: number[] = new Array(boundCount * 4);
    let displayFeatureType: number[] = new Array(boundCount ? displayFeatures.length : boundCount);
    let displayFeatureStatus: number[] = new Array(boundCount ? displayFeatures.length : boundCount);
    for (let i = 0; i < displayFeatures.length; i++) {
      let singleFeatureBound = displayFeatures[i].getBounds();
      if (singleFeatureBound.length === 0) {
        continue;
      }
      for (let j = 0; j < singleFeatureBound.length; j++) {
        displayFeatureBound[4 * i + 4 * j] = singleFeatureBound[j].left;
        displayFeatureBound[4 * i + 4 * j + 1] = singleFeatureBound[j].top;
        displayFeatureBound[4 * i + 4 * j + 2] = singleFeatureBound[j].width;
        displayFeatureBound[4 * i + 4 * j + 3] = singleFeatureBound[j].height;
      }
      displayFeatureType[i] = displayFeatures[i].getType();
      displayFeatureStatus[i] = displayFeatures[i].getState();
    }
    this?.flutterEngine?.getFlutterNapi()?.setViewportMetrics(this.viewportMetrics.devicePixelRatio,
      this.viewportMetrics.physicalWidth,
      this.viewportMetrics.physicalHeight,
      this.viewportMetrics.physicalViewPaddingTop,
      this.viewportMetrics.physicalViewPaddingRight,
      this.viewportMetrics.physicalViewPaddingBottom,
      this.viewportMetrics.physicalViewPaddingLeft,
      this.viewportMetrics.physicalViewInsetTop,
      this.viewportMetrics.physicalViewInsetRight,
      this.viewportMetrics.physicalViewInsetBottom,
      this.viewportMetrics.physicalViewInsetLeft,
      this.viewportMetrics.systemGestureInsetTop,
      this.viewportMetrics.systemGestureInsetRight,
      this.viewportMetrics.systemGestureInsetBottom,
      this.viewportMetrics.systemGestureInsetLeft,
      this.viewportMetrics.physicalTouchSlop,
      displayFeatureBound,
      displayFeatureType,
      displayFeatureStatus)
    return true;
  }

  onKeyPreIme(event: KeyEvent) : boolean {
    return this.keyboardManager?.onKeyPreIme(event) ?? false;
  }

  onKeyEvent(event: KeyEvent) : boolean {
    return this.keyboardManager?.onKeyEvent(event) ?? false;
  }

  onMouseWheel(eventType: string, event: PanGestureEvent) {
    this.flutterEngine?.getFlutterNapi()?.xComponentDisPatchMouseWheel(this.id, eventType, event);
  }

  addFirstFrameListener(listener: FirstFrameListener) {
    this.mFirstFrameListeners.add(listener);
  }

  removeFirstFrameListener(listener: FirstFrameListener) {
    this.mFirstFrameListeners.remove(listener);
  }

  hasRenderedFirstFrame(): boolean {
    return this.isFlutterUiDisplayed;
  }

  onFirstFrame() {
    let listeners = this.mFirstFrameListeners.clone();
    listeners.forEach((listener) => {
      listener.onFirstFrame();
    })
  }

  setCheckFullScreen(check: boolean) {
    this.checkFullScreen = check;
  }

  setCheckKeyboard(check: boolean) {
    this.checkKeyboard = check
  }

  setCheckGesture(check: boolean) {
    this.checkGesture = check
  }

}

export interface FirstFrameListener {
  onFirstFrame(): void;
}
