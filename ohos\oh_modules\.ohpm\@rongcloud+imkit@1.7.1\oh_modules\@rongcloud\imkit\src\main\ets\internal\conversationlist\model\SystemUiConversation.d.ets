// @keepTs
// @ts-nocheck
/**
 * Created on 2024/08/06
 * <AUTHOR>
 */
import { Conversation } from '@rongcloud/imlib';
import { BaseUiConversation } from '../../../conversationlist/model/BaseUiConversation';
import { image } from '@kit.ImageKit';
import { UserInfoModel } from '../../../user/model/UserInfoModel';
import { GroupInfoModel } from '../../../user/model/GroupInfoModel';
import { GroupMemberInfoModel } from '../../../user/model/GroupMemberInfoModel';
@Observed
export declare class SystemUiConversation extends BaseUiConversation {
    imagePixelMap: image.PixelMap | undefined;
    onUserInfoUpdate(t282: UserInfoModel): void;
    onGroupInfoUpdate(s282: GroupInfoModel): void;
    onGroupMemberUpdate(r282: GroupMemberInfoModel): void;
    onConversationUpdate(q281: Conversation, r281: boolean): void;
    /**
     * 获取图片
     * @param resource
     * @returns
     */
    private getPixmapFromMedia;
    /**
     * 设置语音消息样式
     * @param lastMessage
     * @param value
     */
    private setVoiceMutableStyledString;
}
