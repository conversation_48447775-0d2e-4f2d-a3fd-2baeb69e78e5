import bundleManager from '@ohos.bundle.bundleManager';
import { common } from '@kit.AbilityKit';
import { NativeCallExportBase } from './NativeCallExportBase';
export declare class ApplicationUtil {
    static sBundleInfo: bundleManager.BundleInfo;
    static sContext: Context;
    static getContext(): Context;
    static getBundleInfo(): bundleManager.BundleInfo;
    static getApplicationBundleName(): string;
    static getApplicationSignature(): string;
    static getApplicationVersion(): string;
    static getApplicationName(): string;
    static getCacheDir(): string;
    static getFileDir(): string;
    static getTempDir(): string;
    static registerLifecycleListener(h2: common.ApplicationContext, i2: (result: boolean) => void): number;
}
export declare class AioUtil implements NativeCallExportBase {
    getFunctionsBinder(): Map<string, Function>;
    setup(v: Context): void;
}
