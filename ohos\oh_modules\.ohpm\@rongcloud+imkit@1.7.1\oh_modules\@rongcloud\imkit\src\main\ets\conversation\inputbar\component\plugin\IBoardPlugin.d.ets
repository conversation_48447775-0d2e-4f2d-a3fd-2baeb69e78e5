// @keepTs
// @ts-nocheck
/**
 * Created on 2024/09/06
 * <AUTHOR>
 */
import { Context } from '@kit.AbilityKit';
import { ConversationIdentifier } from '@rongcloud/imlib';
/**
 * 加号扩展栏的插件基类
 * @version 1.0.0
 */
export interface IBoardPlugin {
    /**
     * 插件名，用来判断插件唯一标识
     * @returns 插件名
     */
    pluginName?(): string;
    /**
     * 构建要显示的标题
     *```
     * 注意：通过该接口设置指定插件的UI组件，优先级低于 `setBoardPluginView` 设置的插件UI组件。
     *
     * 插件UI组件展示的优先级排序说明：
     * 1. 通过 `RongIM.getInstance().conversationService().setBoardPluginView` 设置的插件UI组件。
     * 2. 使用SDK的UI组件，插件标题是 `obtainTitle` ，插件图标是 `obtainImage` 。
     *```
     * @param context 上下文
     * @returns 标题
     */
    obtainTitle(context: Context): ResourceStr;
    /**
     * 构建要显示的图片
     *```
     * 注意：通过该接口设置指定插件的UI组件，优先级低于 `setBoardPluginView` 设置的插件UI组件。
     *
     * 插件UI组件展示的优先级排序说明：
     * 1. 通过 `RongIM.getInstance().conversationService().setBoardPluginView` 设置的插件UI组件。
     * 2. 使用SDK的UI组件，插件标题是 `obtainTitle` ，插件图标是 `obtainImage` 。
     *```
     * @param context 上下文
     * @returns 图片
     */
    obtainImage(context: Context): ResourceStr;
    /**
     * 插件被点击
     * @param context 上下文
     * @param conId 插件所在的会话
     */
    onClick(context: Context, conId: ConversationIdentifier): void;
    /**
     * 需要在哪些会话中显示，可以进行过滤
     * @returns true 在该会话显示；false 不显示
     */
    onFilter(conId: ConversationIdentifier): boolean;
}
