// @keepTs
// @ts-nocheck
import { RequestPermissionInterceptor } from './listener/RequestPermissionInterceptor';
/**
 * 权限管理
 * @version 1.0.0
 */
export interface PermissionService {
    /**
     * 增加权限申请监听
     * @param listener 监听
     * @discussion 配合 removeRequestPermissionListener 使用，否则会出现内存泄露
     */
    setRequestPermissionInterceptor(interceptor: RequestPermissionInterceptor): void;
    /**
     * 移除权限申请监听
     * @param listener 监听
     * @discussion 配合 addRequestPermissionListener 使用，否则会出现内存泄露
     */
    removeRequestPermissionInterceptor(interceptor: RequestPermissionInterceptor): void;
}
