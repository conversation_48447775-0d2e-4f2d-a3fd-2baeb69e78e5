{"app": {"bundleName": "cn.openharmony.ohos_mqtt", "debug": true, "versionCode": 1000000, "versionName": "2.0.10", "minAPIVersion": 12, "targetAPIVersion": 12, "apiReleaseType": "Release", "compileSdkVersion": "5.0.0.71", "compileSdkType": "OpenHarmony", "appEnvironments": [], "bundleType": "app", "buildMode": "debug"}, "module": {"name": "ohos_Mqtt", "type": "har", "deviceTypes": ["default", "tablet"], "packageName": "@ohos/mqtt", "installationFree": false, "virtualMachine": "ark12.0.2.0", "compileMode": "esmodule", "dependencies": []}}