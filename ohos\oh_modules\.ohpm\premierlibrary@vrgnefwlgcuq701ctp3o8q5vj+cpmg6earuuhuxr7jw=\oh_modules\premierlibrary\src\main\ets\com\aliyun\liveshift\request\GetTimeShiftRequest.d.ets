import { LiveShift } from "../../player/source/LiveShift";
import { BaseRequest, OnRequestListener } from "../../utils/BaseRequest";
export declare class GetTimeShiftRequest extends BaseRequest {
    private mLiveShiftSource;
    private mReferer;
    private mNetworkTimeout;
    private mHttpProxy;
    private mUserAgent;
    private mCustomHeaders;
    private httpClientHelper;
    constructor(c4: LiveShift, d4: OnRequestListener);
    setRefer(b4: string): void;
    setTimeout(a4: number): void;
    setHttpProxy(z3: string): void;
    setUserAgent(y3: string): void;
    setCustomHeaders(x3: Array<Record<string, string>>): void;
    runInBackground(): Promise<void>;
    stopInner(): void;
}
