// login_manager.dart
import 'dart:convert';

import 'package:wuling_flutter_app/models/user/user_model.dart';
import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/page/account/login_page.dart';
import 'package:wuling_flutter_app/api/index.dart';
import 'package:wuling_flutter_app/utils/manager/notification_manager.dart';
import 'package:wuling_flutter_app/utils/manager/dialog_manager.dart';
import 'package:wuling_flutter_app/constant/constant.dart';
import 'package:wuling_flutter_app/models/global_data.dart';
import 'package:wuling_flutter_app/models/user/oauth_model.dart';
import 'package:wuling_flutter_app/utils/sp_util.dart';
import 'package:wuling_flutter_app/constant/storage.dart';
import 'package:uuid/uuid.dart';

import 'log_manager.dart';

/// 登录管理器，用于处理登录相关的逻辑和界面展示。
class LoginManager {
  /// 私有构造函数，实现单例模式。
  LoginManager._privateConstructor();

  /// 用于导航的全局Key。
  static final GlobalKey<NavigatorState> navigatorKey =
      DialogManager.navigatorKey;

  /// 单例实例。
  static final LoginManager _instance = LoginManager._privateConstructor();

  /// 工厂构造函数，返回单例实例。
  factory LoginManager() {
    return _instance;
  }

  /// 用于跟踪登录界面是否正在显示的私有变量。
  bool _isShowingLoginModal = false;

  /// 显示登录界面的方法。
  /// 只有在登录界面未显示时才显示新的登录界面，并在关闭时更新状态。
  void showLoginModal() {
    if (!_isShowingLoginModal) {
      _isShowingLoginModal = true;
      showModalBottomSheet(
        context: navigatorKey.currentContext!,
        isScrollControlled: true,
        builder: (BuildContext context) {
          return LoginScreen();
        },
      ).then((value) {
        // 当登录界面关闭时，更新状态以反映它不再显示。
        _isShowingLoginModal = false;
      });
    }
  }

  /// 隐藏登录界面的方法。
  /// 如果登录界面正在显示并且可以返回上一个页面，则关闭登录界面并更新状态。
  void hideLoginModal() {
    if (_isShowingLoginModal && navigatorKey.currentState!.canPop()) {
      navigatorKey.currentState!.pop();
      _isShowingLoginModal = false; // 确保在登录界面关闭时更新状态。
    }
  }

  /// 登录获取授权
  Future<OauthModel> oauthLoginWithPassword({
    required String mobile,
    String? password,
    String? smsCode,
  }) async {
    String clientId = Constant.APP_CLIENT_ID;
    String clientSecret = Constant.APP_CLIENT_SECRET;
    String state = DateTime.now().toIso8601String();
    String responseType = "token";

    try {
      OauthModel model = await userAPI.oauthLoginWithPassword(mobile, clientId,
          clientSecret, state, responseType, smsCode, password);
      GlobalData().oauthModel = model; //设置到全局变量
      SpUtil().setJSON(SP_USER_OAUTH_KEY, model.toJson()); //保存到本地
      // 打印登录成功的accessToken
      LogManager().debug('============= 登录成功 ===============');
      LogManager().debug('用户手机号: $mobile');
      LogManager().debug('AccessToken: ${model.accessToken}');
      LogManager().debug('====================================');
      return model;
    } catch (e) {
      rethrow;
    }
  }

  /// 验证码登录获取授权
  Future<OauthModel> oauthLoginWithCode({
    required String mobile,
    required String smsCode,
    String? subChannel,
  }) async {
    String clientId = Constant.APP_CLIENT_ID;
    String clientSecret = Constant.APP_CLIENT_SECRET;
    String state = DateTime.now().toIso8601String();
    String responseType = "token";

    try {
      OauthModel model = await userAPI.oauthLoginWithCode(mobile, clientId,
          clientSecret, state, responseType, smsCode, subChannel);
      GlobalData().oauthModel = model; //设置到全局变量
      SpUtil().setJSON(SP_USER_OAUTH_KEY, model.toJson()); //保存到本地
      // 打印登录成功的accessToken
      LogManager().debug('============= 验证码登录成功 ===============');
      LogManager().debug('用户手机号: $mobile');
      LogManager().debug('AccessToken: ${model.accessToken}');
      LogManager().debug('========================================');
      return model;
    } catch (e) {
      rethrow;
    }
  }

  /// 获取当前登录账户的个人信息
  Future<UserModel> getSelfInfo() async {
    try {
      UserModel model = await userAPI.getSelfInfo();
      GlobalData().userModel = model; //设置到全局变量
      SpUtil().setJSON(SP_USER_PROFILE_KEY, model.toJson()); //保存到本地
      return model;
    } catch (e) {
      rethrow;
    }
  }

  /// 微信登录短信验证登录
  Future<OauthModel> oauthLoginWithWechat({
    required String mobile,
    required String smsCode,
    required String unionId,
    String? subChannel,
  }) async {
    String clientId = Constant.APP_CLIENT_ID;
    String clientSecret = Constant.APP_CLIENT_SECRET;
    String state = DateTime.now().toIso8601String();
    String responseType = "token";

    try {
      OauthModel model = await userAPI.oauthLoginWithWechat(mobile, clientId,
          clientSecret, state, responseType, smsCode, unionId,unionId,subChannel);
      GlobalData().oauthModel = model; //设置到全局变量
      SpUtil().setJSON(SP_USER_OAUTH_KEY, model.toJson()); //保存到本地
      // 打印登录成功的accessToken
      LogManager().debug('============= 微信登录成功 ===============');
      LogManager().debug('用户手机号: $mobile');
      LogManager().debug('AccessToken: ${model.accessToken}');
      LogManager().debug('========================================');
      return model;
    } catch (e) {
      rethrow;
    }
  }

  /// 华为一键登录
  Future<OauthModel> oauthLoginWithHuawei({
    required String authCode,
  }) async {
    String clientId = Constant.APP_CLIENT_ID;
    String clientSecret = Constant.APP_CLIENT_SECRET;
    String state = DateTime.now().toIso8601String();
    String responseType = "token";
    try {
      OauthModel model = await userAPI.oauthLoginWithHuawei(
          authCode, clientId, clientSecret, state, responseType);
      GlobalData().oauthModel = model; //设置到全局变量
      SpUtil().setJSON(SP_USER_OAUTH_KEY, model.toJson()); //保存到本地

      // 打印登录成功的accessToken
      LogManager().debug('============= 华为一键登录成功 ===============');
      LogManager().debug('AuthCode: $authCode');
      LogManager().debug('AccessToken: ${model.accessToken}');
      LogManager().debug('=========================================');

      return model;
    } catch (e) {
      rethrow;
    }
  }

  /// 退出登录
  Future<bool> oauthLogout() async {
    OauthModel? oauthModel = GlobalData().oauthModel;
    String clientId = Constant.APP_CLIENT_ID;
    String clientSecret = Constant.APP_CLIENT_SECRET;
    String state = oauthModel?.state ?? Uuid().v4();
    String responseType = "token";

    try {
      bool response = await userAPI.oauthLogout(
          clientId, clientSecret, state, responseType);
      //移除缓存
      await GlobalData().clearUser();
      return response;
    } catch (e) {
      rethrow;
    }
    // finally {
    //   //清理缓存
    //   GlobalData().clearUser();
    // }
  }
}
