          import { LatLng } from '@bdmap/base'; import Bundle from "../o/i1"; import Overlay from "./m"; import type { IAnchor, IGroundOption, Nullable } from "../../g1/a2"; import type OverlayMgr from "./j2"; import type { ImageOverlayData } from "../../g1/i1"; import ImageEntity from "../o/s"; import type BmBitmapResource from "../c2/f2/t3"; import { Callback } from '@kit.BasicServicesKit';             export default class Ground extends Overlay { private mImage; private mWidth; private mHeight; private mBounds; private mAnchorX; private mAnchorY; private mTransparency; private bmGround;                               constructor(s37: IGroundOption);       init(): void;         getImage(): ImageEntity;         image(image: ImageEntity): this;         setImage(image: ImageEntity): void;       updateBmIcon(icon: ImageEntity): void;       updateBitMap(bitmap: BmBitmapResource): void;       getImageBitMap(image: ImageEntity, callback: Callback<BmBitmapResource | undefined>): void;         getDimensions(): { width: number; height: number; };         getAnchor(): IAnchor;           anchor(p37: number, q37: number): this;           setAnchor(n37: number, o37: number): void;             bounds(southwest: LatLng, northeast: LatLng): this;         getBound(): [ LatLng, LatLng ];             setBound(southwest: LatLng, northeast: LatLng): void;       preUpdatePoint(): void;         transparency(num: number): this;         setTransparency(num: number): void;         getTransparency(): number; get typeName(): string;       dataFormat(h37: Array<ImageOverlayData>): Nullable<Bundle>;       toBundle(c37: OverlayMgr): Promise<Bundle>;       toString(): string; } 