import { VidAuth } from '../player/source/VidAuth';
import { VidSts } from '../player/source/VidSts';
import { ErrorInfo } from '../player/bean/ErrorInfo';
import { AliMediaDownloader, ConvertURLCallback, OnCompletionListener, OnErrorListener, OnPreparedListener, OnProgressListener } from "./AliMediaDownloader";
import { DownloaderConfig } from './DownloaderConfig';
import { Context } from '@kit.AbilityKit';
import { MediaInfo } from '../player/nativeclass/MediaInfo';
export declare class ApsaraDownloader implements AliMediaDownloader {
    private mCoreDownloader;
    private mContext?;
    private mOutOnCompletionListener;
    private mInnerOnCompletionListener;
    private mOutOnPreparedListener;
    private mInnerOnPreparedListener;
    private mOutOnProgressListener;
    private mInnerOnProgressListener;
    private mOutOnErrorListener;
    private mInnerOnErrorListener;
    constructor(f1: Context);
    start(): void;
    stop(): void;
    release(): void;
    setSaveDir(e1: string): void;
    getFilePath(): string;
    selectItem(d1: number): void;
    prepareVidAuth(c1: VidAuth): void;
    prepareVidSts(b1: VidSts): void;
    updateVidStsSource(a1: VidSts): void;
    updateVidAuthSource(z: VidAuth): void;
    deleteFile(): void;
    static setConvertURLCallback(y: ConvertURLCallback): void;
    setDownloaderConfig(x: DownloaderConfig): void;
    setOnPreparedListener(w: OnPreparedListener): void;
    setOnProgressListener(v: OnProgressListener): void;
    setOnCompletionListener(u: OnCompletionListener): void;
    setOnErrorListener(t: OnErrorListener): void;
    private bindListener;
    onCompletion(): void;
    onPrepared(s: MediaInfo): void;
    onProgress(q: number, r: number): void;
    onError(p: ErrorInfo): void;
}
