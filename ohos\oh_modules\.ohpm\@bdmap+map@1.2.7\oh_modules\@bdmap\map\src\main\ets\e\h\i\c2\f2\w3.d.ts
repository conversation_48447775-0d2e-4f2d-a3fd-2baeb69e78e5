import { BmTextureOption } from "../d2"; import BmObject from "../u2"; import BmDrawableResource from "./l3"; export default class BmLineStyle extends BmObject { private lineResId; private bmpResId; private color; private bmpRes; private width; private strokeWidth; private strokeColor; private textureOption; private lineType; constructor();           setColor(e26: number): any;           setBitmapResource(d26: BmDrawableResource): any;           setBmpResourceId(c26: number): any;           setLineResourceId(b26: number): any;           setWidth(width: number): any;           setStrokeWidth(width: number): any;           setStrokeColor(a26: number): any;           setTextureOption(option: BmTextureOption): any;             setLineType(lineType: number): any; destroy(delay?: boolean): void; } 