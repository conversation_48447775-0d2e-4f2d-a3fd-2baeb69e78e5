// @keepTs
// @ts-nocheck
/**
 * Created on 2024/08/15
 * <AUTHOR>
 */
import { ClearConversationEvent } from '../event/ClearConversationEvent';
import { DeleteMessageEvent } from '../event/DeleteMessageEvent';
import { MessageChangedEvent } from '../event/MessageChangedEvent';
import { RecallMessageEvent } from '../event/RecallMessageEvent';
import { SendMediaMessageEvent } from '../event/SendMediaMessageEvent';
import { SendMessageEvent } from '../event/SendMessageEvent';
export interface MessageEventListener {
    /**
     * 普通消息发送时的状态回调
     * @param event 操作事件
     */
    onSendMessage?: (event: SendMessageEvent) => void;
    /**
     * 媒体消息发送时的状态回调
     * @param event 操作事件
     */
    onSendMediaMessage?: (event: SendMediaMessageEvent) => void;
    /**
     * 当删除消息时的回调
     * @param event 操作事件
     */
    onDeleteMessage?: (event: DeleteMessageEvent) => void;
    /**
     * 当清空历史记录
     * @param event 操作事件
     */
    onClearConversation?: (event: ClearConversationEvent) => void;
    /**
     * 当撤回消息时的回调
     * @param event 操作事件
     */
    onRecallMessage?: (event: RecallMessageEvent) => void;
    /**
     * 消息变更时的回调
     * @param event 操作事件
     * @since 1.7.0
     * 使用场景：1. 点击语音消息修改消息的状态为已读时触发
     */
    onMessageChanged?: (event: MessageChangedEvent) => void;
}
