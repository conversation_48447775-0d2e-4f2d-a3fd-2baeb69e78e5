import { WebSettingsHostApi } from './GeneratedOhosWebView';
import { InstanceManager } from './InstanceManager';
import { WebViewPlatformView } from './WebViewHostApiImpl';
export interface WebSettings {
    setDomStorageEnabled(flag: boolean): void;
    getDomStorageEnabled(): boolean;
    setJavaScriptCanOpenWindowsAutomatically(flag: boolean): void;
    getJavaScriptCanOpenWindowsAutomatically(): boolean;
    setSupportMultipleWindows(support: boolean): void;
    getSupportMultipleWindows(): boolean;
    setBackgroundColor(color: number): void;
    getBackgroundColor(): string;
    setJavaScriptEnabled(flag: boolean): void;
    getJavaScriptEnabled(): boolean;
    setUserAgentString(userAgentString: string): void;
    setMediaPlaybackRequiresUserGesture(require: boolean): void;
    getMediaPlaybackRequiresUserGesture(): boolean;
    setSupportZoom(support: boolean): void;
    getSupportZoom(): boolean;
    setLoadWithOverviewMode(overview: boolean): void;
    getLoadWithOverviewMode(): boolean;
    setUseWideViewPort(use: boolean): void;
    getUseWideViewPort(): boolean;
    setDisplayZoomControls(enabled: boolean): void;
    setBuiltInZoomControls(enabled: boolean): void;
    setAllowFileAccess(enabled: boolean): void;
    getAllowFileAccess(): boolean;
    setTextZoom(textZoom: number): void;
    getTextZoom(): number;
    getUserAgentString(): Promise<string>;
    onTextZoomChanged(callback: (ratio: number) => void): void;
    setAllowFullScreenRotate(enabled: boolean): void;
    getAllowFullScreenRotate(): boolean;
}
export declare class WebSettingsCreator {
    createWebSettings(webView: WebViewPlatformView): WebSettings;
}
export declare class WebSettingsHostApiImpl extends WebSettingsHostApi {
    private instanceManager;
    private webSettingsCreator;
    constructor(instanceManager: InstanceManager, webSettingsCreator: WebSettingsCreator);
    create(instanceId: number, webViewInstanceId: number): void;
    setDomStorageEnabled(instanceId: number, flag: boolean): void;
    setJavaScriptCanOpenWindowsAutomatically(instanceId: number, flag: boolean): void;
    setSupportMultipleWindows(instanceId: number, support: boolean): void;
    setBackgroundColor(instanceId: number, color: number): void;
    setJavaScriptEnabled(instanceId: number, flag: boolean): void;
    setUserAgentString(instanceId: number, userAgentString: string): void;
    setMediaPlaybackRequiresUserGesture(instanceId: number, require: boolean): void;
    setSupportZoom(instanceId: number, support: boolean): void;
    setLoadWithOverviewMode(instanceId: number, overview: boolean): void;
    setUseWideViewPort(instanceId: number, use: boolean): void;
    setDisplayZoomControls(instanceId: number, enabled: boolean): void;
    setBuiltInZoomControls(instanceId: number, enabled: boolean): void;
    setAllowFileAccess(instanceId: number, enabled: boolean): void;
    setTextZoom(instanceId: number, textZoom: number): void;
    getUserAgentString(instanceId: number): Promise<string>;
    setAllowFullScreenRotate(instanceId: number, enabled: boolean): void;
}
