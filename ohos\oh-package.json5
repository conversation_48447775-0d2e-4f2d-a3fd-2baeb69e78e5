{"modelVersion": "5.0.0", "name": "apptemplate", "version": "1.0.0", "description": "Please describe the basic information.", "main": "", "author": "", "license": "", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har", "@tencent/wechat_open_sdk": "1.0.14", "@free/global": "1.0.3", "@rongcloud/imkit": "^1.5.0", "@rongcloud/imlib": "^1.5.0", "@ohos/mqtt": "^2.0.22"}, "devDependencies": {"@ohos/hypium": "1.0.18"}, "overrides": {"@ohos/flutter_ohos": "file:./har/flutter.har", "shared_preferences_ohos": "../../../../../pub/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/shared_preferences/shared_preferences_ohos/ohos", "url_launcher_ohos": "../../../../../pub/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/url_launcher/url_launcher_ohos/ohos", "webview_flutter_ohos": "../../../../../pub/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/webview_flutter/webview_flutter_ohos/ohos", "path_provider_ohos": "../../../../../pub/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/path_provider/path_provider_ohos/ohos", "@ohos/flutter_module": "file:./entry", "image_picker_ohos": "../../../../../pub/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/image_picker/image_picker_ohos/ohos", "permission_handler_ohos": "../../../../../pub/git/flutter_permission_handler-1e7c80f0720aff742d9f1e08017e1753bcad76ef/permission_handler_ohos/ohos", "device_info_plus": "../../../../../pub/git/flutter_plus_plugins-a1347adcca3a46346a6ddd127cebcec9970cad6c/packages/device_info_plus/device_info_plus/ohos", "package_info_plus": "../../../../../pub/git/flutter_plus_plugins-452bfb40465722552f31cf78cde60f99cc691e36/packages/package_info_plus/package_info_plus/ohos", "flutter_blue_plus_ohos": "file:./har/flutter_blue_plus_ohos.har", "iamgeqr_flutter_plugin": "../../../../../pub/git/flutter_image_scanner-b9f20a88ca1c738c700a15655b8fab4c305c4d32/ohos", "connectivity_plus": "../../../../../pub/git/flutter_plus_plugins-8720286d1b4b664dbc1d87ba169125242d4f10fb/packages/connectivity_plus/connectivity_plus/ohos", "mobile_scanner": "../../../../../pub/git/fluttertpc_mobile_scanner-a50b8f5e377524f79dd143f1297e82d257ad53b1/ohos", "fluwx": "../../../../../pub/git/fluwx-9fe4d9f1d3c05de5de18d25cc53852866d9c34d7/ohos", "open_app_settings": "../../../../../pub/git/fluttertpc_open_app_settings-038c0dd8ffd45bcf84691d62cfff957748cd493a/ohos", "flutter_blue_plus": "../../../../../pub/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos", "camera_ohos": "../plugins/camera_ohos/ohos", "video_compress": "file:./har/video_compress.har"}, "dynamicDependencies": {}}