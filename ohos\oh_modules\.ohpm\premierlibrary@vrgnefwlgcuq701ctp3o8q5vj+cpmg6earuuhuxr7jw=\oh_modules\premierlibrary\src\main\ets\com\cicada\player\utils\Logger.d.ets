import { Context } from "@kit.AbilityKit";
export declare class Logger {
    private mValue;
    private mLogCallback;
    private mEnableConsoleLog;
    private static sInstance;
    private static sAppContext;
    private mCurrentLogLevel;
    constructor();
    /**
     * 获取Logger的单例
     * @param context 上下文
     * @return Logger的单例
     */
    /****
     * gets the singleton of the Logger
     * @param context
     * @return singleton for Logger
     */
    static getInstance(r44: Context): Logger;
    /**
     * 设置日志回调
     * @param callback 回调。
     */
    /****
     * Set a log callback.
     * @param callback The log callback.
     */
    setLogCallback(q44: OnLogCallback): void;
    /**
     * 获取当前设置的日志回调
     * @return 日志回调
     */
    /****
     * get the log callback
     * @return log callback
     */
    getLogCallback(): OnLogCallback | null;
    /**
     * 设置日志等级
     * @param logLevel 日志等级。 见{@linkplain LogLevel}。
     */
    /****
     * Set the log level.
     * @param logLevel The level of the log. See {@linkplain LogLevel}.
     */
    setLogLevel(p44: LogLevel): void;
    /**
     * 设置日志选项
     * @param logOption 日志选项。 见{@linkplain LogOption}。
     * @param value 0代表关闭 1代表打开
     */
    /****
     * Set a log option.
     * @param logOption
     * @param value  0 represents off, 1 represents on
     */
    setLogOption(n44: LogOption, o44: number): void;
    /**
     * 获取日志等级
     * @return 日志等级。见{@linkplain LogLevel}。
     */
    /****
     * get the log level
     * @return log level. See {@linkplain LogLevel}.
     */
    getLogLevel(): LogLevel;
    /**
     * 是否开启控制台日志打印
     * @param bEnabled
     */
    /****
     * whether to enable console log printing
     * @param bEnabled
     */
    enableConsoleLog(l44: boolean): void;
    protected nOnLogCallback: Function;
    private callback;
    private static getLevel;
}
/**
 * 日志回调接口
 */
/****
 * Log callback.
 */
export interface OnLogCallback {
    /**
     *
     * @param level 日志等级。 见{@linkplain LogLevel}
     * @param msg 日志信息
     */
    /****
     *
     * @param level The level of the log. See {@linkplain LogLevel}.
     * @param msg The log data.
     */
    onLog: (level: LogLevel, msg: string) => void;
}
/**
 * 日志等级
 */
/****
 * Log level
 */
export declare enum LogLevel {
    /**
     * 无
     */
    /****
     * None
     */
    AF_LOG_LEVEL_NONE = 0,
    /**
     * 崩溃
     */
    /****
     * Fatal
     */
    AF_LOG_LEVEL_FATAL = 8,
    /**
     * 错误
     */
    /****
     * Error
     */
    AF_LOG_LEVEL_ERROR = 16,
    /**
     * 警告
     */
    /****
     * Warning
     */
    AF_LOG_LEVEL_WARNING = 24,
    /**
     * 信息
     */
    /****
     * Info
     */
    AF_LOG_LEVEL_INFO = 32,
    /**
     * 调试
     */
    /****
     * Debug
     */
    AF_LOG_LEVEL_DEBUG = 48,
    /**
     * 堆栈
     */
    /****
     * Trace
     */
    AF_LOG_LEVEL_TRACE = 56
}
export declare enum LogOption {
    /**
     * 打印帧级别日志
     */
    /**
     * Print frame level log
     */
    FRAME_LEVEL_LOGGING_ENABLED = 1
}
