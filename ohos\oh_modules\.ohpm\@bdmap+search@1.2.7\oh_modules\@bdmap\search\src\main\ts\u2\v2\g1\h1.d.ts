import { PoiBoundSearchOption } from "../../../d/g1/h/i1/j1";
import { PoiCitySearchOption } from "../../../d/g1/h/i1/k1";
import { PoiDetailSearchOption } from "../../../d/g1/h/i1/t1";
import { PoiIndoorOption } from "../../../d/g1/h/i1/s1";
import { PoiNearbySearchOption } from "../../../d/g1/h/i1/l1";
import { PoiDetailSearchResult } from "../../../d/g1/h/m1/q1";
import { PoiIndoorResult } from "../../../d/g1/h/m1/r1";
import { PoiResult } from "../../../d/g1/h/m1/n1";
import { BaseSearch } from '../../base/base';
/**
 * 所有接口用于暴露给API层
 */
export interface IPoiSearch {
    searchNearby(option: PoiNearbySearchOption): Promise<PoiResult>;
    searchInCity(option: PoiCitySearchOption): Promise<PoiResult>;
    searchInBound(option: PoiBoundSearchOption): Promise<PoiResult>;
    searchPoiDetail(option: PoiDetailSearchOption): Promise<PoiDetailSearchResult>;
    searchPoiIndoor(option: PoiIndoorOption): Promise<PoiIndoorResult>;
}
export declare class PoiSearchImp extends BaseSearch implements IPoiSearch {
    searchNearby(f17: PoiNearbySearchOption): Promise<PoiResult>;
    searchInCity(c17: PoiCitySearchOption): Promise<PoiResult>;
    searchInBound(z16: PoiBoundSearchOption): Promise<PoiResult>;
    searchPoiDetail(w16: PoiDetailSearchOption): Promise<PoiDetailSearchResult>;
    searchPoiIndoor(t16: PoiIndoorOption): Promise<PoiIndoorResult>;
}
