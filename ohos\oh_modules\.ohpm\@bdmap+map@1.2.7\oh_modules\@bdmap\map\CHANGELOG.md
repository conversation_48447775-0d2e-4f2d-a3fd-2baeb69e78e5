# Changelog
### 变更
## [v1.2.7] 2025-05-08
1. 移除定位控件，由用户使用[@bdmap/locsdk](https://ohpm.openharmony.cn/#/cn/detail/@bdmap%2Flocsdk)定位包自定义组件实现
2. 增加PrismBuildingInfo解析楼块数据
3. 增加`MapOptions`的`enableDBClickZoom`属性，设置双击事件是否放大地图
4. 更新地图基础样式
5. 修复设置最小最大缩放层级不生效问题
6. 修复`Polyline`多纹理索引不符合预期问题
7. 修复设置长按事件、双击事件不触发回调问题
8. 修复地图控件UI尺寸跟随屏幕尺寸动态变化问题
## [v1.2.6] 2025-03-18
1. 增加Prism覆盖物类
2. 增加Building楼块类
## [v1.2.5] 2025-02-19
1. 底图异常问题修复
2. 安全问题修复
## [v1.2.4] 2025-02-12
1. 修复1.2.3包在DevEco Studio 5.0.2 Release中使用异常问题
## [v1.2.3] 2025-02-05
1. 通过使用`OverlayLayer`的`pauseCommit`和`resumeCommit`的方法，降低批量数据上图时长
2. 恢复`TOUCHSTART`|`TOUCHMOVE`|`TOUCHEND` 事件的像素坐标、经纬度数据返回
3. 修复Marker覆盖物半透明度图标异常问题
4. 修复设置`setMapCenterWithOffset`时`offset`失效问题
5. 修复设置缩放中心点失效问题
## [v1.2.2] 2024-12-18
1. 优化手势交互
2. 此版中地图倾斜、缩放、旋转相关消息事件都是通过地图移动事件进行外发，后续版本会进行再次开放
## [v1.2.1] 2024-11-14
1. 面覆盖物支持镂空面
2. 地图状态监听事件增加触发来源标识
3. Marker增加是否清除不使用纹理的配置
4. 修复多实例地图显示异常问题
5. 修复按线类型删除所有线覆盖物失效问题
## [v1.2.0] 2024-10-25
1. 气泡「PopView」支持多种布局能力以及文本、图片内容设置
2. 线覆盖物增加描边设置
3. 面覆盖物增加抽稀设置、拐点类型设置
4. 圆覆盖物增加渐变色等能力
5. 文字覆盖物增加可设置气泡等能力
6. Marker覆盖物增加初始化显示动画
7. 修复大量Marker实例化显示数量不完整问题
8. 地图增加可设置限制显示范围
9. 支持在线瓦片图层的加载以及参数更新
10. 修复覆盖物zIndex不生效问题
11. 修复地图尺寸变化引起的crash问题
12. 修复GCJ02坐标系下点击事件获取坐标异常问题
13. 优化地图文字渲染以及手势交互
14. 构建字节码格式的HAR
## [v1.1.3] 2024-07-18
### 变更
1. 修改依赖@bdmap/base包版本为1.1.1
## [v1.1.2] 2024-07-15
### 变更
1. ImageEntity图像类支持横轴纵轴的反转操作
2. 修复线多纹理在线上效果不符合预期问题
3. 修复设置地图最佳视野不符合预期问题
4. 修复地图监听覆盖物Label事件失效问题
5. 提升手势缩放灵敏度等
## [v1.1.1] 2024-07-11
### 变更
1. 修复打包混淆引起的点击事件失效
## [v1.1.0] 2024-07-04
### 变更
1. 线覆盖物增加多颜色线、多纹理线、渐变线、发光线效果
2. 增加InfoWindow覆盖物
3. Marker增加多种碰撞能力
4. Marker附加InfoWindow覆盖物或者PopView视图能力
5. MapController增加获取或设置地图最佳视野等能力
6. 覆盖物统一增加点击事件以及MapController增加新状态事件监听
7. 增加室内图控制方法
8. 增加个性化样式地图控制方法
9. 引擎升级与优化
## [v1.0.2] 2024-06-04
### 变更
1. 修复地图多实例问题
2. 修复地图尺寸变化问题
## [v1.0.1] 2024-05-22
### 变更
1. 新增类型声明
2. 完善类文档
## [v1.0.0] 2024-03-22
### 变更
1. 地图SDK发布
