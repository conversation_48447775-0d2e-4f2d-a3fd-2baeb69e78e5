//
//  Generated code. Do not modify.
//  source: commCarParking.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use carParkingStatusDescriptor instead')
const CarParkingStatus$json = {
  '1': 'CarParkingStatus',
  '2': [
    {'1': 'collectTime', '3': 1, '4': 1, '5': 3, '10': 'collectTime'},
    {'1': 'APPInfcSw', '3': 2, '4': 1, '5': 9, '10': 'APPInfcSw'},
    {'1': 'VehCntrlSt', '3': 3, '4': 1, '5': 9, '10': 'VehCntrlSt'},
    {'1': 'IPAOperSts', '3': 4, '4': 1, '5': 9, '10': 'IPAOperSts'},
    {'1': 'VPAOperSts', '3': 5, '4': 1, '5': 9, '10': 'VPAOperSts'},
    {'1': 'IPAFailrReas', '3': 6, '4': 1, '5': 9, '10': 'IPAFailrReas'},
    {'1': 'VPAgFailrReas', '3': 7, '4': 1, '5': 9, '10': 'VPAgFailrReas'},
  ],
};

/// Descriptor for `CarParkingStatus`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List carParkingStatusDescriptor = $convert.base64Decode(
    'ChBDYXJQYXJraW5nU3RhdHVzEiAKC2NvbGxlY3RUaW1lGAEgASgDUgtjb2xsZWN0VGltZRIcCg'
    'lBUFBJbmZjU3cYAiABKAlSCUFQUEluZmNTdxIeCgpWZWhDbnRybFN0GAMgASgJUgpWZWhDbnRy'
    'bFN0Eh4KCklQQU9wZXJTdHMYBCABKAlSCklQQU9wZXJTdHMSHgoKVlBBT3BlclN0cxgFIAEoCV'
    'IKVlBBT3BlclN0cxIiCgxJUEFGYWlsclJlYXMYBiABKAlSDElQQUZhaWxyUmVhcxIkCg1WUEFn'
    'RmFpbHJSZWFzGAcgASgJUg1WUEFnRmFpbHJSZWFz');

