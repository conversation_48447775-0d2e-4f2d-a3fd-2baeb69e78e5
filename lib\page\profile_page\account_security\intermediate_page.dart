import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/common/action.dart';
import 'package:wuling_flutter_app/page/base_page/base_page.dart';
import 'package:wuling_flutter_app/routes/app_routes.dart';
import 'package:wuling_flutter_app/utils/manager/loading_manager.dart';
import 'package:wuling_flutter_app/widgets/profile_page_widgets/system_setting_page_widgets/setting_cell.dart';
import 'package:wuling_flutter_app/widgets/profile_page_widgets/system_setting_page_widgets/setting_page_item.dart';
import 'package:wuling_flutter_app/widgets/webview/webview.dart';

import '../../../api/user_api.dart';
import '../../../models/global_data.dart';
import '../../../models/user/user_model.dart';
import '../../../plugins/share_plugin/share_plugin.dart';
import '../../../utils/manager/log_manager.dart';
import '../../../utils/manager/login_manager.dart';

class IntermediatePage extends BasePage {
  final SettingItem settingItem;
  IntermediatePage({
    Key? key,
    bool hideAppBar = false,
    bool isWithinSafeArea = true,
    String appBarTitle = '',
    Color appBarColor = Colors.transparent,
    Color pageBackgroundColor = Colors.white,
    required this.settingItem
  }) : super(
          key: key,
          hideAppBar: hideAppBar,
          isWithinSafeArea: isWithinSafeArea,
          appBarTitle: settingItem.itemType.description,
          appBarColor: appBarColor,
          pageBackgroundColor: pageBackgroundColor,
        );

  @override
  _AccountSecurityPageState createState() => _AccountSecurityPageState();
}

class _AccountSecurityPageState extends BasePageState<IntermediatePage> {
  @override
  void pageInitState() {
    super.pageInitState();
    // 在此处添加页面初始化逻辑
  }

  List<SettingItem> _getDataList(){
    return widget.settingItem.sectionType?.defaultItems() ?? [];
  }

  @override
  Widget buildPageContent(BuildContext context) {
    // 在此处添加页面ui设置，默认添加一个center text组件
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: _getDataList().length,
      itemBuilder: (BuildContext context, int itemIndex) {
        SettingItem item = _getDataList()[itemIndex];
        return SettingCell(
          item: item,
          onTap: (item) async {
            if(item.itemType.description == "认证信息") {
              Navigator.of(context).push(MaterialPageRoute(builder: (context) {
                return WebViewPage(url: "https://m.baojun.net/lingClub/authentication/car-auth");
              }));
            }else if(item.itemType.description == "微信") {
              if(GlobalData().userModel?.unionId==null){
                SharePlugin.login({}).then((value) async {
                  await userAPI.oauthBindWithWX(value['code']);
                  await LoginManager().getSelfInfo();
                  setState(() {

                  });
                });
              }else{
                ShowAction.init(context).showDialog(msg: "请确认是否解除微信绑定").then((value) async {
                  if(value == true){
                    await userAPI.unBindWithWX();
                    await LoginManager().getSelfInfo();
                    setState(() {

                    });
                  }
                });
              }
            }else{
              Widget page = item.itemType.segueWidget()??Container();
              if(page is Container){
                LoadingManager.showToast('页面尚未完成 敬请期待');
              }else {
                Navigator.of(context).push(CustomCupertinoPageRoute(
                  builder: (context) => page,
                  canSwipeBack: false, // 禁用手势返回
                ));
              }
            }

          },
          switchValueChanged: (value, item) {
            // 处理开关值变化
            LogManager().debug('${item.itemType.description} 开关变为 $value');
            setState(() {
              // 更新开关值
              item.isOn = value;
            });
          },
        );
      },
    );
  }

  @override
  void dispose() {
    // 在此处添加页面销毁逻辑
    super.dispose();
  }
}