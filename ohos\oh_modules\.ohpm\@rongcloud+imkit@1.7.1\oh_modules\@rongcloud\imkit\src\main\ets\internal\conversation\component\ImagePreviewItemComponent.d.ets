// @keepTs
// @ts-nocheck
import { image } from '@kit.ImageKit';
/**
 * 给函数的执行加上动画
 * @param fn：要在动画内执行的回调函数，通常fn里面会改变状态变量
 * @param duration：动画持续时长
 * @param curve：动画区线
 */
export declare function runWithAnimation(f77: Function, g77?: number, h77?: Curve): void;
@Component
export declare struct ImagePreviewItemComponent {
    @Require
    remoteUrl: string;
    @Require
    messageId: number;
    @Require
    base64: string;
    @State
    uri: string;
    @State
    progress: number;
    @State
    private imageScaleInfo;
    @State
    private imageOffsetInfo;
    @State
    private matrix;
    @State
    destructDuration: number;
    imageDefaultSize: image.Size;
    @State
    private panDirection;
    @Consume
    windowWidth: number;
    @Consume
    windowHeight: number;
    @State
    private loadImageTimeout;
    private destructMessageIntervalId;
    private downloadIntervalId;
    private oneClick?;
    private backClick?;
    private fileInfo;
    private downloadListener;
    private connectionStatusListener;
    aboutToAppear(): void;
    aboutToDisappear(): void;
    private initFileDownloadInfo;
    private downloadImage;
    handleImageScaleInfo(): Promise<void>;
    /**
     * 根据图片宽高比及窗口大小计算图片的默认宽高，即，图片最适配屏幕的大小
     * @param imageWHRatio：图片原始宽高比
     * @param size：窗口大小{with:number,height:number}
     * @returns image.Size
     */
    private calcImageDefaultSize;
    /**
     * 设置当前图片的相关信息：uri、whRatio、pixelMap、fitWH、defaultSize、maxScaleValue
     * 提前获取图片的信息，以进行Image组件的尺寸设置及后续的相关计算
     */
    private initCurrentImageInfo;
    /**
     * 在偏移时评估是否到达边界，以便进行位移限制与图片的切换
     */
    private evaluateBound;
    build(): void;
}
