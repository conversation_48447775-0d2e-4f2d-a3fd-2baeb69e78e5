import { SearchResult } from "../../../d/e/h/f1";
import { RecommendStopSearchOption } from "../../../d/e/h/j";
import { SearchParser, SearchRequest } from '../../base/base';
import { UrlProvider } from "../../base/d3";
/**
 * 搜索结果解析器
 */
export declare class RecommendStopSearchParser extends SearchParser {
    parseSearchResult(c27: string): SearchResult;
    /**
     * 把从服务端解析得到的数据封装为提供给用户的数据
     * @param jsonParseResult
     * @param result
     * @returns
     */
    private parseRecommendStopInfo;
}
export declare class RecommendStopSearchRequest extends SearchRequest {
    constructor(r26: RecommendStopSearchOption);
    /**
     * 解析用户输入参数
     * @param option
     */
    private requestRecommendStopBuildParam;
    getUrlDomain(o26: UrlProvider): string;
}
