import 'dart:ffi';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:marquee/marquee.dart';
import 'package:wuling_flutter_app/api/car_api.dart';
import 'package:wuling_flutter_app/constant/storage.dart';
import 'package:wuling_flutter_app/global.dart';
import 'package:wuling_flutter_app/models/index.dart';
import 'package:wuling_flutter_app/routes/jump_tool.dart';
import 'package:wuling_flutter_app/utils/app_config_util.dart';
import 'package:wuling_flutter_app/utils/manager/loading_manager.dart';
import 'package:wuling_flutter_app/utils/manager/location_manager.dart';
import 'package:wuling_flutter_app/utils/str_util.dart';
import 'package:wuling_flutter_app/utils/manager/log_manager.dart';
import 'package:wuling_flutter_app/widgets/common/arrow_button.dart';
import 'package:wuling_flutter_app/widgets/use_car_page_widgets/use_car_page_control_data_widget.dart';
import 'dart:ui';

import '../../models/car/car_info_model.dart';
import '../../models/car/car_service_model.dart';
import '../../models/car/car_service_status_model.dart';
import '../../models/car/car_status_model.dart';
import '../../utils/manager/ble_manager.dart';
import '../../constant/constant.dart';
import '../../constant/service_constant.dart';
import '../../models/car/car_control_item_model.dart';
import '../../models/car/car_maintenance_model.dart';
import '../../models/global_data.dart';
import '../../utils/manager/dialog_manager.dart';
import '../../utils/manager/notification_manager.dart';
import '../../utils/sp_util.dart';
import '../common/custom_dialog.dart';
import 'use_car_page_notification_widget.dart';
import 'package:wuling_flutter_app/extensions/car/car_status_model_extensions.dart';

// 电池状态展示枚举
enum CarNotificationType {
  undefined(0), // 未定义类型
  other(1), // 其它通知，如弹窗
  repairRemind(2), //  保养提醒
  chargingTime(4); // 充电剩余时间

  final int value;
  const CarNotificationType(this.value);
}

class ControlTitleWidget extends StatefulWidget {
  final String carName;
  final String carPosition;
  final bool hideSettingButton;
  final bool isCarOwner;
  final bool hasMoreCar;
  final VoidCallback onMyCarButtonClicked;
  final VoidCallback? onSwitchButtonClicked;
  final VoidCallback? onSettingButtonClicked;
  final String collectTime;
  final String carLatitude;
  final String carLongitude;
  final CarStatusModel? statusModel;
  final CarInfoModel? carInfoModel;
  final List<CarServiceModel>? notifiList;
  final CarMaintenanceModel? mainModel;
  final bool isRefresh;

  const ControlTitleWidget({
    Key? key,
    required this.carName,
    required this.isRefresh,
    this.carPosition = '',
    this.carLatitude = '',
    this.carLongitude = '',
    this.hideSettingButton = false,
    this.isCarOwner = false,
    this.hasMoreCar = false,
    this.collectTime = '',
    required this.onMyCarButtonClicked,
    this.onSwitchButtonClicked,
    this.onSettingButtonClicked,
    this.statusModel,
    this.carInfoModel,
    this.notifiList,
    this.mainModel,
  }) : super(key: key);

  Future<String> updateCarPosition(
      carPosition, carLatitude, carLongitude) async {
    if (carPosition.isEmpty || carPosition == 'null') {
      if (carLatitude != "null" &&
          carLongitude != "null" &&
          carLatitude.isNotEmpty &&
          carLongitude.isNotEmpty) {
        try {
          String address = await LocationManager.getAddressFromLocation(
            carLatitude,
            carLongitude,
          );
          // 格式化地址：处理门牌号中的点号分隔问题
          return _formatAddress(address);
        } catch (e) {
          return '获取车况位置信息失败';
        }
      } else {
        return '获取车况位置信息失败';
      }
    } else {
      // 对已有的地址也进行格式化处理
      return _formatAddress(carPosition);
    }
  }

  /// 格式化地址字符串，处理门牌号中的点号分隔问题
  /// 例如：将 "河西路18号*******号" 转换为 "河西路18号1234号"
  String _formatAddress(String address) {
    // 使用正则表达式匹配门牌号中的点号分隔数字模式
    // 匹配模式：数字.数字.数字.数字号 或类似的模式
    return address.replaceAllMapped(
      RegExp(r'(\d+(?:\.\d+)+)号'),
      (Match match) {
        String numberPart = match.group(1)!;
        // 去掉所有点号，保留数字和字母
        String cleanedNumber = numberPart.replaceAll('.', '');
        return '${cleanedNumber}号';
      },
    );
  }

  @override
  State<StatefulWidget> createState() => _ControlTitleWidgetState();
}

class _ControlTitleWidgetState extends State<ControlTitleWidget> {
  bool showNotifi = false;
  CarNotificationType notificationType = CarNotificationType.undefined;
  CarServiceModel? _notifiSM;
  String? _leftIcon;
  bool _showCloseBtn = false;

  /// 格式化地址字符串，处理门牌号中的点号分隔问题
  /// 例如：将 "河西路18号*******号" 转换为 "河西路18号1234号"
  String _formatAddress(String address) {
    // 使用正则表达式匹配门牌号中的点号分隔数字模式
    // 匹配模式：数字.数字.数字.数字号 或类似的模式
    return address.replaceAllMapped(
      RegExp(r'(\d+(?:\.\d+)+)号'),
      (Match match) {
        String numberPart = match.group(1)!;
        // 去掉所有点号，保留数字和字母
        String cleanedNumber = numberPart.replaceAll('.', '');
        return '${cleanedNumber}号';
      },
    );
  }

  String _displayText = '';
  Color _textColor = Colors.white;
  bool _isRefreshService = false;
  bool _didAlert = false;

  /// 弹窗类型 1每天首次进入出行用车页弹出/2杀死APP再进入出行用车页弹出/3每次点击出行用车页弹出/4下拉刷新出行用车页弹出
  String _alertType = '0';
  // @override
  // void initState() {
  //   super.initState();
  //   WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
  //   });
  // }

  void getWhtherShowNotificationService() {
    bool hasChargeTime = false;
    bool hasMaintenance = false;
    if ((widget.statusModel?.isCharging() ?? false) &&
        widget.statusModel?.leftChargeTime != null &&
        widget.statusModel?.leftChargeTime != 0) {
      hasChargeTime = true;
    }
    if (widget.mainModel?.message != null &&
        (widget.mainModel?.message != '')) {
      hasMaintenance = true;
    }
    // LogManager().debug(widget.mainModel?.message ?? '没有数据');
    // 获取将要显示的通知数据
    _notifiSM = getShowNotificationModel(
        widget.notifiList, hasMaintenance, hasChargeTime);
    //显示通知数据
    if (_notifiSM != null) {
      showServiceNotificationUI(
          _notifiSM!,
          notificationType == CarNotificationType.chargingTime
              ? getFormattedLeftChargeTime()
              : null);
    } else {
      showNotifi = false;
    }
  }

  // 展示服务通知数据
  void showServiceNotificationUI(CarServiceModel model, String? chargeTime) {
    String? leftIcon;
    bool showCloseBtn = false;
    String displayText = '';
    Color textColor = Colors.white;
    for (CarServiceParamModel paramModel in model.serviceSkipParamList ?? []) {
      // LogManager()
      //     .debug('参数---${paramModel.paramName}: ${paramModel.paramValue}');
      if (paramModel.paramName == 'closeEnable') {
        showCloseBtn = paramModel.paramValue == '1';
      } else if (paramModel.paramName == 'displayText') {
        if (chargeTime != null &&
            notificationType == CarNotificationType.chargingTime) {
          String timeTitle = '预计充满时间';
          if (paramModel.paramValue.isNotEmpty && paramModel.paramValue != '') {
            timeTitle = paramModel.paramValue;
          }
          displayText = '$timeTitle $chargeTime';
        } else {
          displayText =
              (paramModel.paramValue.isNotEmpty && paramModel.paramValue != '')
                  ? paramModel.paramValue
                  : '';
        }
      } else if (paramModel.paramName == 'textColor') {
        textColor = hexToColor(paramModel.paramValue);
      } else if (paramModel.paramName == 'hasIcon') {
        bool hasIcon = paramModel.paramValue == '1';
        if (hasIcon &&
            model.serviceStatusList != null &&
            model.serviceStatusList!.isNotEmpty) {
          CarServiceStatusModel statusModel = model.serviceStatusList![0];
          leftIcon = statusModel.serviceStatusImage;
          // LogManager().debug('参数---${statusModel.serviceStatusImage}');
        }
      }
    }

    _leftIcon = leftIcon;
    _showCloseBtn = showCloseBtn;
    _displayText = displayText;
    _textColor = textColor;
    showNotifi = true;
  }

  Color hexToColor(String hexString) {
    hexString = hexString.toUpperCase().replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString'; // 如果不包含透明度，添加不透明度FF（完全不透明）
    }
    return Color(int.parse(hexString, radix: 16));
  }

  //获取充电时间显示数据
  String getFormattedLeftChargeTime() {
    String result = '';
    int minTime = widget.statusModel?.leftChargeTime ?? 0;
    int h = 0;
    int min = 0;
    if (minTime > 60) {
      min = minTime % 60;
      h = (minTime - min) ~/ 60;
    } else {
      min = minTime;
    }

    if (h > 0) {
      if (min > 0) {
        result = '$h小时 $min分钟';
      } else {
        result = '$h小时';
      }
    } else {
      result = '$min分钟';
    }
    // LogManager().debug('时间---$result---${widget.statusModel?.leftChargeTime}');
    return result;
  }

// 获取将要显示的通知数据
  CarServiceModel? getShowNotificationModel(List<CarServiceModel>? notifiArr,
      bool hasRepairRemind, bool hasChargeTime) {
    if (notifiArr == null || notifiArr.isEmpty) return null;
    CarServiceModel? notifiModel;
    for (CarServiceModel model in notifiArr) {
      LogManager().debug('参数---${model.serviceCode}--: $hasChargeTime');
      // //保养提醒
      if (model.serviceCode == 'repairRemind') {
        if (hasRepairRemind) {
          notifiModel = model;
          notificationType = CarNotificationType.repairRemind;
          break;
        } else {
          continue;
        }
      }
      if (model.serviceCode == 'ChargingTime') {
        //充电剩余时间
        if (hasChargeTime) {
          notifiModel = model;
          notificationType = CarNotificationType.chargingTime;
          break;
        } else {
          continue;
        }
      } else {
        if (!model.userClosed) {
          // 用户没有关闭才会使用
          notifiModel = model;
          notificationType = CarNotificationType.other;
          break;
        }
        continue;
      }
    }
    // LogManager().debug(
    //     '参数===preSelectedIndex:${Global.preSelectedIndex}-currentSelectedIndex:${Global.currentSelectedIndex}');
    updateWhetherAlertWithServiceModel(notifiModel);
    return notifiModel;
  }

//服务通知的特殊值要依据其他字段判断，所以要更新，不然有可能取的通知数据不对,无法弹窗
  void updateWhetherAlertWithServiceModel(CarServiceModel? model) {
    //如果model不存在或者弹窗数据已经有了，直接返回（已经有了，再处理会弹两次窗
    if (model == null || _didAlert || _isRefreshService) {
      _isRefreshService = false;
      return;
    }
    String type = (model.serviceSkipType == '') ? '0' : model.serviceSkipType;
    int skipTypeCode = int.parse(type);
    UnifiedSkipType? skipType = UnifiedSkipTypeExtension.fromInt(skipTypeCode);
    // 判断是弹窗，更新数据并弹窗
    if (skipType == UnifiedSkipType.PopHud && !model.userClosed) {
      for (CarServiceParamModel paramModel
          in _notifiSM?.serviceSkipParamList ?? []) {
        if (paramModel.paramName == 'noticeType') {
          _alertType = paramModel.paramValue;
          //延迟调用，等组件完全初始化
          Future.delayed(const Duration(seconds: 1), () {
            manageAlertTypeAndAlert();
          });

          break;
        }
      }
    }
  }

  //弹窗类型判断并弹窗
  void manageAlertTypeAndAlert() {
    // LogManager().debug('参数---前：${_notifiSM?.serviceCode}');
    if (_notifiSM == null) return;
    // LogManager().debug(
    //     '参数---后：${_notifiSM?.serviceCode}--${Global.isAppOpen}--$_alertType');
    // 1 每天首次进入出行用车页弹出
    if (_alertType == '1') {
      String timestampStr =
          SpUtil().getString(SP_NOTIFICATION_SERVICE_ALERT_TIME_KEY);
      bool isAlert = false;
      if (timestampStr != '') {
        isAlert = isMoreThan24Hours(timestampStr);
      }
      if (timestampStr == '' || isAlert) {
        String currentTimeTampStr = getCurrentTimestampInSeconds();
        SpUtil().setString(
            SP_NOTIFICATION_SERVICE_ALERT_TIME_KEY, currentTimeTampStr);
        alertMessage();
      }
    }
    // 2.杀死APP再进入出行用车页弹出
    else if (_alertType == '2' && Global.isAppOpen == true) {
      Global.isAppOpen = false;
      alertMessage();
    }
    // 3.每次点击出行用车页弹出
    else if (_alertType == '3' &&
        Global.preSelectedIndex != 1 &&
        Global.currentSelectedIndex == 1) {
      Global.preSelectedIndex = Global.currentSelectedIndex;
      alertMessage();
    }
    // 4.下拉刷新出行用车页弹出
    else if (_alertType == '4' &&
        widget.isRefresh &&
        Global.currentSelectedIndex == 1) {
      alertMessage();
    }
  }

  // 关闭服务通知
  void closeServiceAction() {
    if (notificationType == CarNotificationType.repairRemind) {
      LoadingManager.show(
          status: '关闭中...', maskType: EasyLoadingMaskType.black);
      closeMaintainInfo();
    } else {
      LoadingManager.show(
          status: '关闭中...', maskType: EasyLoadingMaskType.black);
      closeNotifyRequest();
    }
  }

  void closeMaintainInfo() async {
    try {
      Map<String, dynamic> params = {
        'vin': widget.carInfoModel?.vin ?? '',
      };
      bool success = await carAPI.closeCarMaintenance(params);
      if (success) {
        closeNotifyRequest();
      } else {
        LoadingManager.dismiss();
        LogManager().debug('关闭失败');
      }
    } catch (e) {
      LoadingManager.showError('关闭失败');
      LogManager().debug(e.toString());
    }
  }

  void closeNotifyRequest() async {
    try {
      Map<String, dynamic> params = {
        'vin': widget.carInfoModel?.vin ?? '',
        'notificationCode': _notifiSM?.serviceCode ?? ''
      };
      LogManager().debug(params.toString());
      bool success = await carAPI.closeCarNotificationService(params);
      if (success) {
        _isRefreshService = true;
        NotificationManager().postNotification(
            Constant.NOTIFICATION_SERVICE_DATA_REFRESH,
            userInfo: {});
        LoadingManager.dismiss();
        // LoadingManager.showSuccess('关闭成功');
      } else {
        LoadingManager.showError('关闭失败');
      }
    } catch (e) {
      LoadingManager.showError('关闭失败');
      LogManager().debug(e.toString());
    }
  }

  // 点击服务通知
  void onNotificaitonServiceTap() {
    int skipTypeCode = int.parse(_notifiSM?.serviceSkipType ?? '0');
    UnifiedSkipType? skipType = UnifiedSkipTypeExtension.fromInt(skipTypeCode);

    if (skipType != null) {
      switch (skipType) {
        case UnifiedSkipType.H5:
          jumpToWebViewPageWithServiceModel(_notifiSM!, false);
          break;
        case UnifiedSkipType.HasNavH5:
          jumpToWebViewPageWithServiceModel(_notifiSM!, true);
          break;
        case UnifiedSkipType.PopHud:
          alertMessage();
          break;
        default:
          break;
      }
    }
  }

  //弹窗文案
  void alertMessage() {
    _didAlert = true;
    String titleStr = '';
    for (CarServiceParamModel model in _notifiSM?.serviceSkipParamList ?? []) {
      if (model.paramName == 'displayText') {
        titleStr = model.paramValue;
        break;
      }
    }
    DialogManager().showCustomDialog(
      CustomDialog(
        title: titleStr,
        content: _notifiSM?.serviceSkipTarget ?? '',
        buttonHeight: 60,
        buttons: [
          DialogButton(
              label: "确定",
              onPressed: () {
                _didAlert = false;
              },
              backgroundColor: Colors.white,
              textColor: Color.fromARGB(255, 15, 91, 233)),
        ],
      ),
    );
  }

  void jumpToWebViewPageWithServiceModel(
      CarServiceModel serviceModel, bool needTitleBar) {
    String url = serviceModel.serviceSkipTarget;
    JumpTool().openWeb(context, url, needTitleBar);
  }

  bool isMoreThan24Hours(String timestampStr) {
    final dateTime = parseTimestamp(timestampStr).toLocal();
    final now = DateTime.now().toLocal();
    return now.difference(dateTime).inHours > 24;
  }

  DateTime parseTimestamp(String timestampStr) {
    if (timestampStr.length == 13) {
      return DateTime.fromMillisecondsSinceEpoch(int.parse(timestampStr));
    } else if (timestampStr.length == 10) {
      return DateTime.fromMillisecondsSinceEpoch(
          int.parse(timestampStr) * 1000);
    }
    throw FormatException('Invalid timestamp format');
  }

  String getCurrentTimestampInSeconds() {
    return (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString();
  }

  @override
  Widget build(BuildContext context) {
    bool isOffline = widget.carLongitude.isEmpty;
    double screenScale = MediaQueryData.fromWindow(window).size.width / 375.0;
    getWhtherShowNotificationService();
    return Container(
      color: Colors.transparent,
      child: Column(
        children: <Widget>[
          SafeArea(
            bottom: false,
            minimum: const EdgeInsets.only(left: 0, right: 0),
            child: Visibility(
              visible: showNotifi && !isOffline, // 离线模式下隐藏通知栏
              child: CarNotificationServiceWidget(
                showCloseBtn: _showCloseBtn,
                leftIcon: _leftIcon,
                displayText: _displayText,
                textColor: _textColor,
                onCloseTap: closeServiceAction,
                onNotificaitonServiceTap: onNotificaitonServiceTap,
              ),
            ),
          ),
          Container(
            color: Colors.transparent,
            margin: const EdgeInsets.only(left: 20, right: 20),
            height: 40,
            child: Row(
              mainAxisAlignment: widget.hideSettingButton
                  ? MainAxisAlignment.start
                  : MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                Container(
                  color: const Color.fromARGB(0, 39, 2, 2),
                  child: TextButton(
                    onPressed: widget.onSwitchButtonClicked,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Image.asset(
                          'assets/images/use_car_page/car_online_tip.png',
                          width: 16,
                          height: 16,
                        ),
                        const SizedBox(
                          width: 4,
                        ),
                        const Text(
                          '网上展厅',
                          style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                              color: Color(0xff383A40)),
                        ),
                      ],
                    ),
                  ),
                ),
                if (!widget.hideSettingButton)
                  Container(
                    color: Colors.transparent,
                    child: Row(
                      children: [
                        IconButton(
                          iconSize: 30,
                          padding: EdgeInsets.zero,
                          icon: Image.asset(
                            widget.hasMoreCar
                                ? 'assets/images/use_car_page/car_switch_temp.png'
                                : 'assets/images/use_car_page/car_add_temp.png',
                            width: 16,
                            height: 16,
                          ),
                          onPressed: widget.onMyCarButtonClicked,
                        ),
                        Container(
                          color: const Color(0xe0000000),
                          width: 1,
                          height: 16,
                        ),
                        IconButton(
                          iconSize: 30,
                          padding: EdgeInsets.zero,
                          icon: Image.asset(
                            'assets/images/use_car_page/car_setting_temp.png',
                            width: 16,
                            height: 16,
                          ),
                          onPressed: () {
                            // 跳转到设置页面
                            JumpTool().openNativePage(context, "1012");
                          },
                        ),
                      ],
                    ),
                  )
              ],
            ),
          ),
          Container(
            color: Colors.transparent,
            child: Padding(
              padding: const EdgeInsets.only(top: 15),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    color: Colors.transparent,
                    child: Text(
                      widget.carName,
                      style: TextStyle(
                        fontSize: 24 * screenScale,
                        fontWeight: FontWeight.w500,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                  if (!widget.isCarOwner) const SizedBox(width: 10),
                  if (!widget.isCarOwner)
                    Container(
                      color: Colors.transparent,
                      child: Image.asset(
                        'assets/images/use_car_page/authorization_car.png',
                        width: 34,
                        height: 17,
                      ),
                    ),
                ],
              ),
            ),
          ),
          if (widget.carLatitude.isNotEmpty && widget.carLatitude != 'null')
            Padding(
              padding: const EdgeInsets.only(top: 22),
              child: Container(
                color: Colors.transparent,
                height: 37,
                child: FutureBuilder<String>(
                    future: isOffline
                        ? Future.value('离线模式')
                        : widget.updateCarPosition(widget.carPosition,
                            widget.carLatitude, widget.carLongitude),
                    builder: (context, snapshot) {
                      return LocationTitleButton(
                        text: snapshot.data ?? '获取车况位置信息失败',
                        textColor: isOffline
                            ? const Color(0xFFFF9B39)
                            : const Color(0xFF008DFF),
                        imageUrl: isOffline
                            ? 'assets/images/use_car_page/car_address_offline.png'
                            : 'assets/images/use_car_page/car_address.png',
                        arrowImageUrl: isOffline
                            ? 'assets/images/use_car_page/car_address_arrow_offline.png'
                            : 'assets/images/use_car_page/car_address_arrow.png',
                        textFontSize: 12,
                        onPressed: () => {
                          if (snapshot.data == null ||
                              snapshot.data == '获取车况位置信息失败')
                            {LoadingManager.showToast('页面功能开发中 敬请期待')}
                          else
                            {
                              // 确保传递格式化后的地址
                              LocationManager.jumpMap(
                                  widget.carName,
                                  _formatAddress(snapshot.data!),
                                  widget.carLatitude,
                                  widget.carLongitude)
                            }
                        },
                      );
                    }),
              ),
            ),
          const SizedBox(
            height: 4,
          ),
          if (widget.collectTime.isNotEmpty)
            Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '车况更新于：${widget.collectTime}',
                  style:
                      const TextStyle(fontSize: 12, color: Color(0xff99a1b2)),
                ),
                const SizedBox(
                  width: 8,
                ),
                SizedBox(
                  height: 14,
                  child: TextButton(
                    onPressed: () {
                      LoadingManager.showToast('页面功能开发中 敬请期待');
                    },
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.all(0),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Text(
                          '遇到问题',
                          style: TextStyle(
                              fontSize: 12,
                              color: Color(0xff384967),
                              decoration: TextDecoration.underline),
                        ),
                        const SizedBox(
                          width: 4,
                        ),
                        Image.asset(
                          'assets/images/use_car_page/car_exclamation_mark.png',
                          width: 12,
                          height: 12,
                          fit: BoxFit.contain,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            )
        ],
      ),
    );
  }
}

class ControlTopCarInfoEVWidget extends StatefulWidget {
  final String carName;
  final bool hideSettingButton;
  final bool isCarOwner;
  final bool hasMoreCar;
  final VoidCallback onMyCarButtonClicked;
  final VoidCallback? onSwitchButtonClicked;
  final VoidCallback? onSettingButtonClicked;
  final String collectTime;
  final CarStatusModel? statusModel;
  final CarInfoModel? carInfoModel;
  final List<CarServiceModel>? notifiList;
  final CarMaintenanceModel? mainModel;
  final bool isRefresh;
  final bool isOffline;
  final String batSoc;
  final String leftMileage;
  final int? leftChargeTime;
  final bool isCharging;
  final bool hasBLE;
  final BleStatus currentBleStatus;
  final bool isHandleConnectBle;
  final VoidCallback? onBleButtonClicked;
  final List<CarServiceModel> ctrlServiceList;
  final VoidCallback? onOfflineButtonClicked;
  final VoidCallback? onCarImageClicked;
  final bool showNetworkBanner;
  final bool isNetworkConnected;
  final bool isOfflineMode; // 新增：是否进入离线模式
  final VoidCallback? onEnterOfflineModeClicked; // 新增：进入离线模式回调

  const ControlTopCarInfoEVWidget({
    Key? key,
    required this.carName,
    required this.isRefresh,
    this.hideSettingButton = false,
    this.isCarOwner = false,
    this.hasMoreCar = false,
    this.collectTime = '',
    required this.onMyCarButtonClicked,
    this.onSwitchButtonClicked,
    this.onSettingButtonClicked,
    this.statusModel,
    this.carInfoModel,
    this.notifiList,
    this.mainModel,
    this.isOffline = false,
    this.batSoc = '',
    this.leftMileage = '',
    this.leftChargeTime,
    this.isCharging = false,
    required this.hasBLE,
    this.currentBleStatus = BleStatus.bleDefault,
    this.isHandleConnectBle = false,
    this.onBleButtonClicked,
    required this.ctrlServiceList,
    this.onOfflineButtonClicked,
    this.onCarImageClicked,
    this.showNetworkBanner = false,
    this.isNetworkConnected = true,
    this.isOfflineMode = false,
    this.onEnterOfflineModeClicked,
  }) : super(key: key);

  Future<String> updateCarPosition(
      carPosition, carLatitude, carLongitude) async {
    if (carPosition.isEmpty || carPosition == 'null') {
      if (carLatitude != "null" &&
          carLongitude != "null" &&
          carLatitude.isNotEmpty &&
          carLongitude.isNotEmpty) {
        try {
          String address = await LocationManager.getAddressFromLocation(
            carLatitude,
            carLongitude,
          );
          // 格式化地址：处理门牌号中的点号分隔问题
          return _formatAddress2(address);
        } catch (e) {
          return '获取车况位置信息失败';
        }
      } else {
        return '获取车况位置信息失败';
      }
    } else {
      // 对已有的地址也进行格式化处理
      return _formatAddress2(carPosition);
    }
  }

  /// 格式化地址字符串，处理门牌号中的点号分隔问题
  /// 例如：将 "河西路18号*******号" 转换为 "河西路18号1234号"
  String _formatAddress2(String address) {
    // 使用正则表达式匹配门牌号中的点号分隔数字模式
    // 匹配模式：数字.数字.数字.数字号 或类似的模式
    return address.replaceAllMapped(
      RegExp(r'(\d+(?:\.\d+)+)号'),
      (Match match) {
        String numberPart = match.group(1)!;
        // 去掉所有点号，保留数字和字母
        String cleanedNumber = numberPart.replaceAll('.', '');
        return '${cleanedNumber}号';
      },
    );
  }

  @override
  State<StatefulWidget> createState() => _ControlTopCarInfoEVWidgetState();
}

class _ControlTopCarInfoEVWidgetState extends State<ControlTopCarInfoEVWidget> {
  bool showNotifi = false;
  CarNotificationType notificationType = CarNotificationType.undefined;
  CarServiceModel? _notifiSM;
  String? _leftIcon;
  bool _showCloseBtn = false;
  String _displayText = '';
  Color _textColor = Colors.white;
  bool _isRefreshService = false;
  bool _didAlert = false;

  /// 弹窗类型 1每天首次进入出行用车页弹出/2杀死APP再进入出行用车页弹出/3每次点击出行用车页弹出/4下拉刷新出行用车页弹出
  String _alertType = '0';
  // @override
  // void initState() {
  //   super.initState();
  //   WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
  //   });
  // }

  void getWhtherShowNotificationService() {
    bool hasChargeTime = false;
    bool hasMaintenance = false;
    // if ((widget.statusModel?.isCharging() ?? false) &&
    //     widget.statusModel?.leftChargeTime != null &&
    //     widget.statusModel?.leftChargeTime != 0) {
    //   hasChargeTime = true;
    // }
    if (widget.mainModel?.message != null &&
        (widget.mainModel?.message != '')) {
      hasMaintenance = true;
    }
    // LogManager().debug(widget.mainModel?.message ?? '没有数据');
    // 获取将要显示的通知数据
    _notifiSM = getShowNotificationModel(
        widget.notifiList, hasMaintenance, hasChargeTime);
    //显示通知数据
    if (_notifiSM != null) {
      showServiceNotificationUI(
          _notifiSM!,
          notificationType == CarNotificationType.chargingTime
              ? getFormattedLeftChargeTime()
              : null);
    } else {
      showNotifi = false;
    }
  }

  // 展示服务通知数据
  void showServiceNotificationUI(CarServiceModel model, String? chargeTime) {
    String? leftIcon;
    bool showCloseBtn = false;
    String displayText = '';
    Color textColor = Colors.white;
    for (CarServiceParamModel paramModel in model.serviceSkipParamList ?? []) {
      // LogManager()
      //     .debug('参数---${paramModel.paramName}: ${paramModel.paramValue}');
      if (paramModel.paramName == 'closeEnable') {
        showCloseBtn = paramModel.paramValue == '1';
      } else if (paramModel.paramName == 'displayText') {
        if (chargeTime != null &&
            notificationType == CarNotificationType.chargingTime) {
          String timeTitle = '预计充满时间';
          if (paramModel.paramValue.isNotEmpty && paramModel.paramValue != '') {
            timeTitle = paramModel.paramValue;
          }
          displayText = '$timeTitle $chargeTime';
        } else {
          displayText =
              (paramModel.paramValue.isNotEmpty && paramModel.paramValue != '')
                  ? paramModel.paramValue
                  : '';
        }
      } else if (paramModel.paramName == 'textColor') {
        textColor = hexToColor(paramModel.paramValue);
      } else if (paramModel.paramName == 'hasIcon') {
        bool hasIcon = paramModel.paramValue == '1';
        if (hasIcon &&
            model.serviceStatusList != null &&
            model.serviceStatusList!.isNotEmpty) {
          CarServiceStatusModel statusModel = model.serviceStatusList![0];
          leftIcon = statusModel.serviceStatusImage;
          // LogManager().debug('参数---${statusModel.serviceStatusImage}');
        }
      }
    }

    _leftIcon = leftIcon;
    _showCloseBtn = showCloseBtn;
    _displayText = displayText;
    _textColor = textColor;
    showNotifi = true;
  }

  Color hexToColor(String hexString) {
    hexString = hexString.toUpperCase().replaceAll('#', '');
    if (hexString.length == 6) {
      hexString = 'FF$hexString'; // 如果不包含透明度，添加不透明度FF（完全不透明）
    }
    return Color(int.parse(hexString, radix: 16));
  }

  //获取充电时间显示数据
  String getFormattedLeftChargeTime() {
    String result = '';
    int minTime = widget.statusModel?.leftChargeTime ?? 0;
    int h = 0;
    int min = 0;
    if (minTime > 60) {
      min = minTime % 60;
      h = (minTime - min) ~/ 60;
    } else {
      min = minTime;
    }

    if (h > 0) {
      if (min > 0) {
        result = '$h小时 $min分钟';
      } else {
        result = '$h小时';
      }
    } else {
      result = '$min分钟';
    }
    // LogManager().debug('时间---$result---${widget.statusModel?.leftChargeTime}');
    return result;
  }

// 获取将要显示的通知数据
  CarServiceModel? getShowNotificationModel(List<CarServiceModel>? notifiArr,
      bool hasRepairRemind, bool hasChargeTime) {
    if (notifiArr == null || notifiArr.isEmpty) return null;
    CarServiceModel? notifiModel;
    for (CarServiceModel model in notifiArr) {
      LogManager().debug('参数---${model.serviceCode}--: $hasChargeTime');
      // //保养提醒
      if (model.serviceCode == 'repairRemind') {
        if (hasRepairRemind) {
          notifiModel = model;
          notificationType = CarNotificationType.repairRemind;
          break;
        } else {
          continue;
        }
      }
      if (model.serviceCode == 'ChargingTime') {
        //充电剩余时间
        if (hasChargeTime) {
          notifiModel = model;
          notificationType = CarNotificationType.chargingTime;
          break;
        } else {
          continue;
        }
      } else {
        if (!model.userClosed) {
          // 用户没有关闭才会使用
          notifiModel = model;
          notificationType = CarNotificationType.other;
          break;
        }
        continue;
      }
    }
    // LogManager().debug(
    //     '参数===preSelectedIndex:${Global.preSelectedIndex}-currentSelectedIndex:${Global.currentSelectedIndex}');
    updateWhetherAlertWithServiceModel(notifiModel);
    return notifiModel;
  }

//服务通知的特殊值要依据其他字段判断，所以要更新，不然有可能取的通知数据不对,无法弹窗
  void updateWhetherAlertWithServiceModel(CarServiceModel? model) {
    //如果model不存在或者弹窗数据已经有了，直接返回（已经有了，再处理会弹两次窗
    if (model == null || _didAlert || _isRefreshService) {
      _isRefreshService = false;
      return;
    }
    String type = (model.serviceSkipType == '') ? '0' : model.serviceSkipType;
    int skipTypeCode = int.parse(type);
    UnifiedSkipType? skipType = UnifiedSkipTypeExtension.fromInt(skipTypeCode);
    // 判断是弹窗，更新数据并弹窗
    if (skipType == UnifiedSkipType.PopHud && !model.userClosed) {
      for (CarServiceParamModel paramModel
          in _notifiSM?.serviceSkipParamList ?? []) {
        if (paramModel.paramName == 'noticeType') {
          _alertType = paramModel.paramValue;
          //延迟调用，等组件完全初始化
          Future.delayed(const Duration(seconds: 1), () {
            manageAlertTypeAndAlert();
          });

          break;
        }
      }
    }
  }

  //弹窗类型判断并弹窗
  void manageAlertTypeAndAlert() {
    // LogManager().debug('参数---前：${_notifiSM?.serviceCode}');
    if (_notifiSM == null) return;
    // LogManager().debug(
    //     '参数---后：${_notifiSM?.serviceCode}--${Global.isAppOpen}--$_alertType');
    // 1 每天首次进入出行用车页弹出
    if (_alertType == '1') {
      String timestampStr =
          SpUtil().getString(SP_NOTIFICATION_SERVICE_ALERT_TIME_KEY);
      bool isAlert = false;
      if (timestampStr != '') {
        isAlert = isMoreThan24Hours(timestampStr);
      }
      if (timestampStr == '' || isAlert) {
        String currentTimeTampStr = getCurrentTimestampInSeconds();
        SpUtil().setString(
            SP_NOTIFICATION_SERVICE_ALERT_TIME_KEY, currentTimeTampStr);
        alertMessage();
      }
    }
    // 2.杀死APP再进入出行用车页弹出
    else if (_alertType == '2' && Global.isAppOpen == true) {
      Global.isAppOpen = false;
      alertMessage();
    }
    // 3.每次点击出行用车页弹出
    else if (_alertType == '3' &&
        Global.preSelectedIndex != 1 &&
        Global.currentSelectedIndex == 1) {
      Global.preSelectedIndex = Global.currentSelectedIndex;
      alertMessage();
    }
    // 4.下拉刷新出行用车页弹出
    else if (_alertType == '4' &&
        widget.isRefresh &&
        Global.currentSelectedIndex == 1) {
      alertMessage();
    }
  }

  // 关闭服务通知
  void closeServiceAction() {
    if (notificationType == CarNotificationType.repairRemind) {
      LoadingManager.show(
          status: '关闭中...', maskType: EasyLoadingMaskType.black);
      closeMaintainInfo();
    } else {
      LoadingManager.show(
          status: '关闭中...', maskType: EasyLoadingMaskType.black);
      closeNotifyRequest();
    }
  }

  void closeMaintainInfo() async {
    try {
      Map<String, dynamic> params = {
        'vin': widget.carInfoModel?.vin ?? '',
      };
      bool success = await carAPI.closeCarMaintenance(params);
      if (success) {
        closeNotifyRequest();
      } else {
        LoadingManager.dismiss();
        LogManager().debug('关闭失败');
      }
    } catch (e) {
      LoadingManager.showError('关闭失败');
      LogManager().debug(e.toString());
    }
  }

  void closeNotifyRequest() async {
    try {
      Map<String, dynamic> params = {
        'vin': widget.carInfoModel?.vin ?? '',
        'notificationCode': _notifiSM?.serviceCode ?? ''
      };
      LogManager().debug(params.toString());
      bool success = await carAPI.closeCarNotificationService(params);
      if (success) {
        _isRefreshService = true;
        NotificationManager().postNotification(
            Constant.NOTIFICATION_SERVICE_DATA_REFRESH,
            userInfo: {});
        LoadingManager.dismiss();
        // LoadingManager.showSuccess('关闭成功');
      } else {
        LoadingManager.showError('关闭失败');
      }
    } catch (e) {
      LoadingManager.showError('关闭失败');
      LogManager().debug(e.toString());
    }
  }

  // 点击服务通知
  void onNotificaitonServiceTap() {
    int skipTypeCode = int.parse(_notifiSM?.serviceSkipType ?? '0');
    UnifiedSkipType? skipType = UnifiedSkipTypeExtension.fromInt(skipTypeCode);

    if (skipType != null) {
      switch (skipType) {
        case UnifiedSkipType.H5:
          jumpToWebViewPageWithServiceModel(_notifiSM!, false);
          break;
        case UnifiedSkipType.HasNavH5:
          jumpToWebViewPageWithServiceModel(_notifiSM!, true);
          break;
        case UnifiedSkipType.PopHud:
          alertMessage();
          break;
        default:
          break;
      }
    }
  }

  //弹窗文案
  void alertMessage() {
    _didAlert = true;
    String titleStr = '';
    for (CarServiceParamModel model in _notifiSM?.serviceSkipParamList ?? []) {
      if (model.paramName == 'displayText') {
        titleStr = model.paramValue;
        break;
      }
    }
    DialogManager().showCustomDialog(
      CustomDialog(
        title: titleStr,
        content: _notifiSM?.serviceSkipTarget ?? '',
        buttonHeight: 60,
        buttons: [
          DialogButton(
              label: "确定",
              onPressed: () {
                _didAlert = false;
              },
              backgroundColor: Colors.white,
              textColor: Color.fromARGB(255, 15, 91, 233)),
        ],
      ),
    );
  }

  void jumpToWebViewPageWithServiceModel(
      CarServiceModel serviceModel, bool needTitleBar) {
    String url = serviceModel.serviceSkipTarget;
    JumpTool().openWeb(context, url, needTitleBar);
  }

  bool isMoreThan24Hours(String timestampStr) {
    final dateTime = parseTimestamp(timestampStr).toLocal();
    final now = DateTime.now().toLocal();
    return now.difference(dateTime).inHours > 24;
  }

  DateTime parseTimestamp(String timestampStr) {
    if (timestampStr.length == 13) {
      return DateTime.fromMillisecondsSinceEpoch(int.parse(timestampStr));
    } else if (timestampStr.length == 10) {
      return DateTime.fromMillisecondsSinceEpoch(
          int.parse(timestampStr) * 1000);
    }
    throw FormatException('Invalid timestamp format');
  }

  String getCurrentTimestampInSeconds() {
    return (DateTime.now().millisecondsSinceEpoch ~/ 1000).toString();
  }

  bool _checkBleShouldAnimation() {
    if (!widget.isHandleConnectBle) {
      return false;
    } else {
      if (widget.currentBleStatus == BleStatus.bleDefault ||
          widget.currentBleStatus == BleStatus.bleAuthorized) {
        return false;
      } else {
        return true;
      }
    }
  }

  String _getBleIcon() {
    if (widget.currentBleStatus == BleStatus.bleAuthorized) {
      return 'assets/images/use_car_page/new_UI/car_home_ble_connected.png';
    } else {
      return 'assets/images/use_car_page/new_UI/car_home_ble_disConnect.png';
    }
  }

  String _getCurrentCarImageCodeWithModel() {
    if (widget.statusModel == null || widget.carInfoModel == null) {
      return '000000';
    }
    Map<String, dynamic> statusMap = widget.statusModel!.toJson();
    List<String> list = [
      'door1OpenStatus',
      'door2OpenStatus',
      'doorLockStatus',
      'dipHeadLight',
      'tailDoorOpenStatus',
      'windowStatus'
    ];
    StringBuffer currentCarImage = StringBuffer();

    for (String str in list) {
      if (statusMap[str] == '1') {
        currentCarImage.write('1');
      } else {
        currentCarImage.write('0');
      }
    }
    return currentCarImage.toString();
  }

  /// 空调是否开启
  bool _isAcTurnOn() {
    if (widget.statusModel == null) {
      return false;
    }
    return widget.statusModel!.acStatus == '1' ||
        widget.statusModel!.acStatus == '2';
  }

  /// 判断是否是新空调
  bool _isNewAir(
      CarStatusModel? carStatus, List<CarServiceModel>? ctrlServiceList) {
    if (carStatus == null) {
      LogManager().debug('isNewAir() carStatus == null');
      return false;
    }
    if (ctrlServiceList == null) {
      LogManager().debug('isNewAir() ctrlServiceList == null');
      return false;
    }
    //是否有空调车控
    bool hasAcStatus = false;
    for (int i = 0; i < ctrlServiceList.length; i++) {
      CarServiceModel serviceModel = ctrlServiceList[i];
      if (serviceModel.serviceStatusList != null) {
        for (int j = 0; j < serviceModel.serviceStatusList!.length; j++) {
          CarServiceStatusModel carServiceStatusModel =
              serviceModel.serviceStatusList![j];
          if (serviceModel.serviceCode == 'acStatus') {
            hasAcStatus = true;
            if (carServiceStatusModel.serviceStatusValue ==
                carStatus!.acStatus) {
              //服务状态动作，0表示跳转到target（直接调指令接口），其他表示弹出对应level的状态。
              if (carServiceStatusModel.serviceStatusAction == '0') {
              } else {
                List<CarServiceStatusModel> statusList =
                    serviceModel.serviceStatusList!;
                //serviceStatusAction=2是新空调，跳有温度进度调节的空调弹框
                if ((statusList != null && statusList.length > 0) &&
                    (statusList[0].serviceStatusAction == '2' ||
                        statusList[0].serviceStatusAction == '4')) {
                  LogManager().debug('isNewAir() 当前是新空调');
                  return true;
                }
              }
            }
          }
        }
      }
    }
    if (ctrlServiceList.length == 0) {
      LogManager().debug('isNewAir() 车控按钮列表数据还没请求到，默认为新空调，不然车图上会闪现空调动画');
      return true;
    } else if (!hasAcStatus) {
      LogManager().debug('isNewAir() 车控按钮列表没有空调按钮，默认为新空调，不然会出现空调动画');
      return true;
    } else {
      LogManager().debug('isNewAir() 当前是旧空调');
      return false;
    }
  }

  /// 空调开启前提下是否为取暖模式
  bool _isWarmMode() {
    return widget.statusModel?.acStatus == '2';
  }

  Widget _buildLeftChargeText() {
    if (widget.leftChargeTime == null) {
      return const Text('充电中',
          style: TextStyle(
            color: Color(0xFF686B78),
            fontSize: 10,
            fontWeight: FontWeight.normal,
          ),
          textAlign: TextAlign.center);
    } else {
      return Text.rich(
        TextSpan(
            style: const TextStyle(
                color: Color(0xFF686B78),
                fontSize: 10,
                fontWeight: FontWeight.normal),
            children: [
              const TextSpan(text: '充电中｜剩余'),
              TextSpan(
                  text: StrUtil.getFormattedLeftChargeTime(
                      widget.leftChargeTime ?? 0, '时', '分'),
                  style: const TextStyle(color: Color(0xFF008DFF)))
            ]),
        textAlign: TextAlign.center,
      );
    }
  }

  String _getCarImageStr() {
    String imageUrl = '';
    if (widget.statusModel != null && widget.carInfoModel != null) {
      if (widget.carInfoModel!.folderUrl?.isNotEmpty ?? false) {
        String imageCode = _getCurrentCarImageCodeWithModel();
        String folderUrl = widget.carInfoModel!.folderUrl!;
        imageUrl = "$folderUrl/$imageCode.png";
      } else {
        imageUrl = widget.carInfoModel!.image ?? "";
      }
    } else if (widget.carInfoModel != null) {
      imageUrl = widget.carInfoModel!.image ?? "";
    }
    return imageUrl;
  }

  double _calculateTextOriginWidth(String text, TextStyle style) {
    final TextPainter textPainter = TextPainter(
      text: TextSpan(text: text, style: style),
      textDirection: TextDirection.ltr,
    )..layout();
    return textPainter.width;
  }

  // 构建顶部数据行（根据controlView判断显示逻辑）
  Widget _buildTopDataRow(String finalBat, String finalMil) {
    // 判断是否为controlView = 8
    bool isControlView8 = widget.carInfoModel?.controlView == 8;

    if (isControlView8) {
      // controlView = 8 的特殊显示逻辑
      return _buildControlView8DataRow(finalMil);
    } else {
      // controlView = 2/3 的原有显示逻辑
      return _buildOriginalDataRow(finalBat, finalMil);
    }
  }

  // controlView = 2/3 的原有显示逻辑
  Widget _buildOriginalDataRow(String finalBat, String finalMil) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Image.asset(
          'assets/images/use_car_page/new_UI/EngNew_batSoc_Image.png',
          height: 25,
          width: 25,
        ),
        Text(
          finalBat,
          style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.normal,
              color: Color(0xFF383A40)),
        ),
        Container(
          width: 12,
        ),
        Text(
          finalMil,
          style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.normal,
              color: Color(0xFF383A40)),
        )
      ],
    );
  }

  // controlView = 8 的特殊显示逻辑
  Widget _buildControlView8DataRow(String finalMil) {
    // 获取油量续航里程
    String oilLeftMileage = _getOilEnduranceMileage();

    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        // 闪电图标
        Image.asset(
          'assets/images/use_car_page/new_UI/EngNew_batSoc_Image.png',
          height: 25,
          width: 25,
        ),
        // 电量续航里程（原来显示百分比电量的位置）
        Text(
          finalMil,
          style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.normal,
              color: Color(0xFF383A40)),
        ),
        Container(
          width: 12,
        ),
        // 油量图标（调整为更小的尺寸）
        Image.asset(
          'assets/images/use_car_page/new_UI/png/ic_remain_mileage.png',
          height: 18,
          width: 18,
          fit: BoxFit.contain,
        ),
        // 油量续航里程
        Text(
          '${oilLeftMileage}km',
          style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.normal,
              color: Color(0xFF383A40)),
        )
      ],
    );
  }

  // 获取油量续航里程
  String _getOilEnduranceMileage() {
    String? oilLeftMileage = widget.statusModel?.oilLeftMileage;
    if (oilLeftMileage == null ||
        oilLeftMileage.isEmpty ||
        oilLeftMileage == 'null') {
      return '--';
    } else {
      return oilLeftMileage;
    }
  }

  @override
  Widget build(BuildContext context) {
    double screenScale = MediaQueryData.fromWindow(window).size.width / 375.0;
    getWhtherShowNotificationService();

    bool bleAnimation = _checkBleShouldAnimation();
    String bleIcon = _getBleIcon();
    String finalBat =
        StrUtil.isNullOrEmpty(widget.batSoc) ? '--%' : '${widget.batSoc}%';
    String finalMil = StrUtil.isNullOrEmpty(widget.leftMileage)
        ? '--km'
        : '${widget.leftMileage}km';

    String carImageUrl = _getCarImageStr();

    double nameWidgetMaxWid = widget.hasBLE
        ? AppConfigUtil.screenWidth - 105 - 50
        : AppConfigUtil.screenWidth - 105 - 50;
    double nameMaxWid =
        widget.isCarOwner ? nameWidgetMaxWid : (nameWidgetMaxWid - 38);
    double nameOriginWid = _calculateTextOriginWidth(
        widget.carName,
        TextStyle(
            fontSize: 24 * screenScale,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF383A40)));
    double nameWid = nameOriginWid > nameMaxWid ? nameMaxWid : nameOriginWid;
    bool shouldScroll = nameOriginWid > nameMaxWid ? true : false;

    return Container(
      color: Colors.transparent,
      child: Column(
        children: <Widget>[
          SafeArea(
            bottom: false,
            //minimum: const EdgeInsets.only(left: 30, right: 20),
            child:
                Column(mainAxisAlignment: MainAxisAlignment.start, children: [
              Visibility(
                visible: showNotifi && !widget.isOfflineMode, // 离线模式下隐藏通知栏
                child: CarNotificationServiceWidget(
                  showCloseBtn: _showCloseBtn,
                  leftIcon: _leftIcon,
                  displayText: _displayText,
                  textColor: _textColor,
                  onCloseTap: closeServiceAction,
                  onNotificaitonServiceTap: onNotificaitonServiceTap,
                ),
              ),
              Container(
                height: 0,
              ),
            ]),
          ),
          // 网络提示条 - 根据参数控制显示，进入离线模式时隐藏
          if (widget.showNetworkBanner && !widget.isOfflineMode)
            Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: const BoxDecoration(
                color: Color(0xFFFFE8E8), // 浅红色背景
              ),
              child: Row(
                children: [
                  // 警告图标
                  Image.asset(
                    'assets/images/use_car_page/new_UI/png/icon_car_inform.png',
                    width: 16,
                    height: 16,
                  ),
                  const SizedBox(width: 6),

                  // 提示文字
                  const Text(
                    '当前网络不可用，请检查您的网络设置。',
                    style: TextStyle(
                      fontSize: 13,
                      color: Color(0xFFE53E3E),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 2),

                  // 进入离线模式按钮 - 当bleType为0时隐藏
                  if (widget.carInfoModel?.bleType != 0)
                    GestureDetector(
                      onTap: () {
                        print('用户点击进入离线模式');
                        widget.onEnterOfflineModeClicked?.call();
                      },
                      child: const Text(
                        '进入离线模式',
                        style: TextStyle(
                          fontSize: 13,
                          color: Color(0xFFE53E3E),
                          fontWeight: FontWeight.w600,
                          decoration: TextDecoration.underline,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          Container(
            padding: const EdgeInsets.only(left: 30, right: 20),
            color: Colors.transparent,
            height: 48,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                SizedBox(
                  width: nameWidgetMaxWid,
                  child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                            height: 30,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                SizedBox(
                                  width: nameWid,
                                  child: shouldScroll
                                      ? Marquee(
                                          // text: '这是一段富有创意性的建设性的意见，采用创新、先进、有远见的思想，进行跑马灯的富文本样式显示',
                                          text: widget.carName,
                                          style: TextStyle(
                                              fontSize: 24 * screenScale,
                                              fontWeight: FontWeight.w600,
                                              color: const Color(0xFF383A40)),
                                          scrollAxis: Axis.horizontal,
                                          blankSpace: 20.0,
                                          velocity: 40,
                                          startPadding: 10,

                                          pauseAfterRound:
                                              const Duration(seconds: 1),
                                          showFadingOnlyWhenScrolling: true,
                                          fadingEdgeStartFraction: 0.1,
                                          fadingEdgeEndFraction: 0.1,
                                          onDone: () {},
                                        )
                                      : Text(
                                          widget.carName,
                                          style: TextStyle(
                                              fontSize: 24 * screenScale,
                                              fontWeight: FontWeight.w600,
                                              color: const Color(0xFF383A40)),
                                        ),
                                ),
                                Visibility(
                                  visible: !widget.isCarOwner,
                                  child: Container(
                                      margin: const EdgeInsets.only(left: 10),
                                      child: Image.asset(
                                        'assets/images/use_car_page/new_UI/car_relation_tip_new.png',
                                        width: 28,
                                        height: 15,
                                      )),
                                ),
                              ],
                            )),
                        const SizedBox(
                          height: 4,
                        ),
                        // 只有在进入离线模式时才隐藏车况更新时间，无网络时仍然显示
                        if (!widget.isOfflineMode &&
                            widget.collectTime.isNotEmpty)
                          Text(
                            '车况更新于：${widget.collectTime}',
                            style: TextStyle(
                              fontSize: 10 * screenScale,
                              fontWeight: FontWeight.normal,
                              overflow: TextOverflow.ellipsis,
                            ),
                          )
                      ]),
                ),
                Container(
                  width: widget.hasBLE ? 100 : 65,
                  height: 32,
                  color: Colors.transparent,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Visibility(
                          visible: widget.hasBLE,
                          child: SizedBox(
                            width: 35,
                            child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  SizedBox(
                                      width: 32,
                                      height: 32,
                                      child: GestureDetector(
                                          onTap: widget.onBleButtonClicked,
                                          child: Stack(
                                            alignment: Alignment.center,
                                            children: [
                                              Image.asset(bleIcon,
                                                  width: 22, height: 22),
                                              Visibility(
                                                  visible: bleAnimation,
                                                  child: Lottie.asset(
                                                      'assets/animations/newUI_bluetooth_connecting.json',
                                                      width: 22,
                                                      height: 22))
                                            ],
                                          ))),
                                  Container(
                                    width: 2,
                                  ),
                                  Container(
                                    height: 16,
                                    width: 1,
                                    color: const Color(0xFFDDDDDD),
                                  )
                                ]),
                          )),
                      Container(
                        width: 2,
                      ),
                      SizedBox(
                          width: 32,
                          child: IconButton(
                            iconSize: 30,
                            padding: EdgeInsets.zero,
                            icon: Image.asset(
                              widget.hasMoreCar
                                  ? 'assets/images/use_car_page/new_UI/EngNew_Switch_icon.png'
                                  : 'assets/images/use_car_page/car_add_temp.png',
                              width: 22,
                              height: 22,
                            ),
                            onPressed: () {
                              // 离线模式下显示弹窗，否则执行原来的逻辑
                              if (widget.isOfflineMode) {
                                _showOfflineModeDialog();
                              } else {
                                widget.onMyCarButtonClicked();
                              }
                            },
                          )),
                      Container(
                        width: 1,
                      ),
                      SizedBox(
                          width: 30,
                          child: IconButton(
                            iconSize: 30,
                            padding: EdgeInsets.zero,
                            icon: Image.asset(
                              'assets/images/use_car_page/new_UI/EngNew_Setting_Icon.png',
                              width: 22,
                              height: 22,
                            ),
                            onPressed: () {
                              // 离线模式下显示弹窗，否则跳转到设置页面
                              if (widget.isOfflineMode) {
                                _showOfflineModeDialog();
                              } else {
                                JumpTool().openNativePage(context, "1012");
                              }
                            },
                          )),
                    ],
                  ),
                )
              ],
            ),
          ),
          Container(
            height: 2,
          ),
          Container(
            padding: const EdgeInsets.only(left: 25),
            height: 25,
            child: Visibility(
                visible: !widget.isOfflineMode,
                child: _buildTopDataRow(finalBat, finalMil)),
          ),
          const SizedBox(
            height: 12,
          ),
          Stack(alignment: const FractionalOffset(0.5, 0.95), children: [
            Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: AspectRatio(
                    aspectRatio: 287 / 57.0,
                    child: Visibility(
                        visible: widget.isCharging,
                        child: Image.asset(
                            'assets/animations/newUI_home_charging.webp')))),
            GestureDetector(
                onTap: widget.isOfflineMode
                    ? null
                    : widget.onCarImageClicked, // 离线模式下禁用点击
                child: CarImageWidget(
                  imageUrl: carImageUrl,
                  isAcTurnOn: _isAcTurnOn(),
                  isWarmMode: _isWarmMode(),
                  isNewAir:
                      _isNewAir(widget.statusModel, widget.ctrlServiceList),
                  fallbackUrl: widget.carInfoModel?.image ?? '',
                  isOfflineMode: widget.isOfflineMode, // 传递离线模式状态
                ))
          ]),
          Visibility(
              visible: widget.isOffline,
              child: Container(
                margin: const EdgeInsets.only(top: 2),
                width: AppConfigUtil.screenWidth - 60,
                height: 16,
                child: GestureDetector(
                  onTap: widget.onOfflineButtonClicked,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        'assets/images/use_car_page/new_UI/use_car_page_offline_icon.png',
                        width: 12,
                        height: 12,
                      ),
                      const Text(
                        '车辆已进入离线模式',
                        style: TextStyle(
                          color: Color(0xFF384967),
                          fontSize: 10,
                          fontWeight: FontWeight.normal,
                        ),
                      )
                    ],
                  ),
                ),
              )),
          // 新增的离线提示文案
          Visibility(
              visible: widget.isOfflineMode, // 修复：只有进入离线模式时才显示
              child: Transform.translate(
                offset: const Offset(0, -20),
                child: GestureDetector(
                  onTap: () {
                    // 点击"车辆已进入离线模式"文案时显示弹窗
                    _showOfflineModeInfoDialog();
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Image.asset(
                        'assets/images/use_car_page/new_UI/png/ic_travel_car_offline_tip_new.png',
                        width: 12,
                        height: 12,
                      ),
                      const SizedBox(width: 4),
                      RichText(
                        text: const TextSpan(
                          children: [
                            TextSpan(
                              text: '车辆已进入',
                              style: TextStyle(
                                color: Color(0xFF99A1B2),
                                fontSize: 10,
                                fontWeight: FontWeight.normal,
                              ),
                            ),
                            TextSpan(
                              text: '离线模式',
                              style: TextStyle(
                                color: Color(0xFF008DFF),
                                fontSize: 10,
                                fontWeight: FontWeight.normal,
                                decoration: TextDecoration.underline, // 恢复下划线
                                decorationColor: Color(0xFF008DFF), // 蓝色下划线
                              ),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              )),
          Visibility(
              visible: widget.isCharging,
              child: Container(
                  margin: const EdgeInsets.only(top: 2),
                  height: 16,
                  child: _buildLeftChargeText())),
        ],
      ),
    );
  }

  // 显示离线模式信息弹窗
  void _showOfflineModeInfoDialog() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          child: Container(
            width: 280, // 对应Android的560dp/2
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 标题 - 对应第一个TextView
                Container(
                  width: double.infinity,
                  margin: EdgeInsets.only(top: 20, bottom: 10),
                  child: Text(
                    '离线模式',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF383A40),
                    ),
                  ),
                ),
                // 内容 - 对应第二个TextView
                Container(
                  width: double.infinity,
                  margin: EdgeInsets.symmetric(horizontal: 25),
                  child: Text(
                    '·当前手机网络不稳定，已进入车控离线模式，仅支持蓝牙钥匙功能\n·手机网络恢复后，下拉刷新可恢复全部功能',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF383A40),
                      height: 1.4,
                    ),
                  ),
                ),
                // 按钮 - 对应第三个TextView (sure_btn)
                Container(
                  margin: EdgeInsets.only(top: 20, bottom: 25),
                  child: GestureDetector(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 30, vertical: 9),
                      decoration: BoxDecoration(
                        color: Colors.transparent, // 透明背景
                        borderRadius: BorderRadius.circular(23),
                        border: Border.all(
                          color: Color(0xFF3C8CF7), // 蓝色边框
                          width: 1,
                        ),
                      ),
                      child: Text(
                        '我知道了',
                        style: TextStyle(
                          fontSize: 14,
                          color: Color(0xFF3C8CF7), // 蓝色文字
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // 显示离线模式提示弹窗（与用车服务一样的样式）
  void _showOfflineModeDialog() {
    DialogManager().showSingleButtonDialog(
      '', // 不显示标题
      '当前APP处于【离线模式】，暂不支持该功能',
      '我知道了',
      () {
        // 点击我知道了按钮的回调
      },
    );
  }
}
