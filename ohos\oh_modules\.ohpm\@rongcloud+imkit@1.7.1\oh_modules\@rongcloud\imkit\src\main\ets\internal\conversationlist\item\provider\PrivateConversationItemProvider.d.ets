// @keepTs
// @ts-nocheck
/**
 * Created on 2024/07/09
 * <AUTHOR>
 */
import { BaseConversationItemProvider } from '../../../../conversationlist/item/provider/BaseConversationItemProvider';
import { BaseUiConversation } from '../../../../conversationlist/model/BaseUiConversation';
export declare class PrivateConversationItemProvider extends BaseConversationItemProvider {
    getConversationWrapBuilder(): WrappedBuilder<[
        Context,
        BaseUiConversation,
        number
    ]>;
}
@Builder
export declare function bindPrivateConversationMessageData(m274: Context, n274: BaseUiConversation, o274: number): void;
@Component
export declare struct PrivateConversationItemView {
    @ObjectLink
    conversation: BaseUiConversation;
    build(): void;
}
