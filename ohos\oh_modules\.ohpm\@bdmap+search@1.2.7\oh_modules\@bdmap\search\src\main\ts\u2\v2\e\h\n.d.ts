import { SdkInnerError } from "../../../base/f1";
export declare namespace BusJson {
    interface BusJsonParseResult {
        result?: Result;
        bus?: Bus;
        SDK_InnerError?: SdkInnerError;
    }
    interface Bus {
        taxi: Taxi;
        current_city: CurrentCity;
        routes: Route[];
        option: Option;
        emergency_tip: string;
        redis_key: string;
        walk: Walk;
        session: Session;
        weak_real_pos_text: string;
        predict_pos_text: string;
        dig_arr_range_text: string;
    }
    interface CurrentCity {
        code: number;
        geo: string;
        level: number;
        name: string;
        sup_subway: number;
        sup_lukuang: number;
        sgeo: number[];
    }
    interface Option {
        total: number;
        exptime: Date;
        sy: number;
        start: End;
        end: End;
        inter_city: number;
        session_in: string;
        rtbus_update_interval: number;
        city_info: CityInfo;
        out_service_idx: number;
        international: number;
        guide_switch: GuideSwitch;
        future_switch: number;
        carbon_emissions_tip: string;
        f: number[];
        dispatch_mode: number;
        sub_title: number;
    }
    interface CityInfo {
        city_id: number;
        sup_subway: number;
        sup_rtbus: number;
        sup_cycle: number;
    }
    interface End {
        pt?: string;
        wd?: string;
        uid?: string;
    }
    interface GuideSwitch {
        master_switch: number;
        auto_switch: number;
        start_guide_name: string;
        stop_guide_name: string;
    }
    interface Route {
        legs: Leg[];
    }
    interface Leg {
        steps: LegStep[];
        distance: number;
        duration: number;
        end_location: string;
        start_location: string;
        arrive_time: Date;
        price: string;
        tip_label?: string;
        tip_label_background?: string;
        line_price: LinePrice[];
        emergency_tip: string;
        top_card: number;
        tip?: number;
        tip_text?: string;
        tip_background?: string;
    }
    interface LinePrice {
        line_type: number;
        line_price: number;
    }
    interface LegStep {
        step: StepStep[];
    }
    interface StepStep {
        pois?: Pois[];
        distance?: number;
        duration?: number;
        end_location: string;
        start_location: string;
        instructions?: string;
        path?: string;
        type: number;
        can_ride?: number;
        walk_type?: number;
        key: string;
        map_key: string;
        trans_type?: number;
        vehicle?: Vehicle;
        is_depot?: number;
        start_address?: string;
        end_address?: string;
        tip?: number;
        tip_text?: string;
        tip_background?: string;
    }
    interface Pois {
        name: Name;
        location: string;
        type: number;
        detail: string;
    }
    enum Name {
    }
    interface Vehicle {
        subway_crowdedness?: SubwayCrowdedness;
        name: string;
        type: number;
        uid: string;
        start_time: string;
        end_time: string;
        start_uid: string;
        end_name: string;
        stop_num: number;
        total_price: number;
        zone_price: number;
        start_name: string;
        end_uid: string;
        line_color: LineColor;
        is_rtbus: number;
        kind_type: number;
        direct_text: string;
        working_time_tag: string;
        working_time_desc: WorkingTimeDesc;
        alias_name?: string;
        entrance_port?: Port;
        side_door_type?: number;
        side_door_desc?: string;
        exit_port?: Port;
        convient_door_info?: string[];
        headway?: string;
        next_shuttle_time?: string;
        shuttle_time_table?: string[];
        time_table?: string[];
        recommend_taxi_info?: RecommendTaxiInfo;
    }
    interface Port {
        name: string;
        x: number;
        y: number;
        uid: string;
        barrierfree_facilities?: number[];
        tip?: string;
    }
    enum LineColor {
        F6C582 = "#f6c582",
        F78731 = "#f78731",
        The008E9C = "#008e9c"
    }
    interface RecommendTaxiInfo {
        text: string;
        discount: string;
    }
    interface SubwayCrowdedness {
        icon_type: number;
        content_text: string;
        icon_color: string;
        icon_url: string;
        status_text: string;
        load_rate_value: number;
        load_rate_text: string;
        extra_text: string;
    }
    enum WorkingTimeDesc {
        Empty = ""
    }
    interface Session {
        session_id: string;
        cache_id: string;
    }
    interface Taxi {
        distance: number;
        duration: number;
        remark: string;
        detail: Detail[];
    }
    interface Detail {
        desc: string;
        km_price: string;
        start_price: string;
        total_price: string;
    }
    interface Walk {
        is_better: number;
    }
    interface Result {
        type: number;
        error: number;
    }
}
