import common from '@ohos.app.ability.common';
import PlatformView from '@ohos/flutter_ohos/src/main/ets/plugin/platform/PlatformView';
import PlatformViewFactory from '@ohos/flutter_ohos/src/main/ets/plugin/platform/PlatformViewFactory';
import { InstanceManager } from './InstanceManager';
export declare class FlutterWebViewFactory extends PlatformViewFactory {
    private instanceManager;
    constructor(instanceManager: InstanceManager);
    create(context: common.Context, viewId: number, args: ESObject): PlatformView;
}
