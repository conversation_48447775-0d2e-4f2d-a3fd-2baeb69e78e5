import { <PERSON>t<PERSON><PERSON>, <PERSON><PERSON>, Point } from '@bdmap/base'; import OverlayLayer from "./d1/k1"; import { LayerTag, OverlayType, MapStatusChangeReason } from "../util/b1/c1"; import BaseLayer from "./d1/j1"; import MapStatus from "./l"; import MapOptions from "./k"; import OverlayMgr from "./m/j2"; import { FingerEvent, FingerEventHandle } from "./k2"; import BaseMap from "./o2"; import type { IGetPoint, ILocation, ISetCenter, IViewportOption, Nullable, TLayer, DeviceInfo } from "../g1/a2"; import type { Callback, EOverLayTypeName, EventOverlayBundle, MapEvent, TMapViewEvent, OverlayEvent, TMapListener, TMapOverlayListener } from "../g1/h1"; import type { IndoorFloorBundles, LayerAddrBundle, NativeContext } from "../g1/e2"; import type Overlay from "./m/m"; import type { TLayerMgr } from "../g1/i1"; import WinRound from "./o/l1"; import BmLayer from "./c2/q2"; import type BaseMarker from "./m/r4"; import type TileLayer from "./d1/b2"; import { Context } from '@kit.AbilityKit'; import OnTouchListener from "../g1/z1";               declare const e1: unique symbol; declare const overlayLayer: unique symbol;             export default class Map { private _token; private [e1]; private [overlayLayer]; private infoWindowEvent; private scaleThrottling; private rotateThrottling; private lookThrottling; private moveThrottling; private mapviewListener; private touchIdle; private touchDebounce; private touchMsg; static HAR_VERSION: string;         baseMap: BaseMap;         mapViewId: string;         filesDir: string;         cacheDir: string;         densityDPI: number;         dpiScale: number;         mapEventListener: TMapListener;         overLayEventListener: TMapOverlayListener;         overlayMgr: OverlayMgr;         fingerEventHandle: FingerEventHandle;         mBmLayer: BmLayer;             constructor(context: NativeContext, mapOptions: Nullable<MapOptions>, device: DeviceInfo);           get token(): Nullable<string>;           set token(val: Nullable<string>);             init(context: NativeContext, mapOptions: Nullable<MapOptions>, device: DeviceInfo): void;       touchChange(): void;             addEventListener(model: MapEvent, o6: Callback<TMapViewEvent>): void;             removeEventListener(model: MapEvent, m6: Callback<TMapViewEvent>): void;           addOverlay(overlay: Overlay | BaseMarker): void;           removeOverlay(overlay: Overlay | BaseMarker): void;         getOverlayLayer(): OverlayLayer;             setViewport(points: Array<LatLng>, options?: IViewportOption): void;               getViewport(points: Array<LatLng> | Bounds, options?: IViewportOption): { center: Point | LatLng; ptOffset: Point; zoom: number; };               fitVisibleMapRect(bounds: Bounds, y5: WinRound, z5: boolean): void;                 setViewPadding(left: number, right: number, top: number, bottom: number): void;           setGeoRoundLimit(bounds: Bounds): void;                 _getBestLevel(bounds: Bounds, options: IViewportOption): number;           getZoomUnits(zoom: Nullable<number>): number;           removeOverlays(type?: OverlayType): void;           getLayers(): TLayerMgr;           getLayerByTag(tag: LayerTag): Nullable<TLayer>;         setLocation(i5: ILocation): void;           createLayerFactory(h5: LayerAddrBundle): void;         addTileLayer(layer: TileLayer): void;         removeTileLayer(layer: TileLayer): void;         switchLayer(a5: BaseLayer, b5: BaseLayer): void;         get mapOptions(): MapOptions;         set mapOptions(mapOptions: MapOptions);         set mapStatus(z4: MapStatus);         get mapStatus(): MapStatus; setOnTouchMessageListener(listener: OnTouchListener): void;         setAppAbility(x4: DeviceInfo): void;         constructorEvent(): void;               addOverlayEventListener(type: EOverLayTypeName, model: OverlayEvent, v4: Callback<EventOverlayBundle>): void;               removeOverlayEventListener(type: EOverLayTypeName, model: OverlayEvent, t4: Callback<EventOverlayBundle>): void;                 createOverlayEvent(type: EOverLayTypeName, model: OverlayEvent, p4: Callback<EventOverlayBundle>): { key: number; event: { [x: symbol]: Callback<EventOverlayBundle>; }; };               createEvent(model: string, o4: Function): { event: { [x: symbol]: Function; }; };               addInfoWindowEvent(model: string, n4: Function): any;               removeInfoWindowEvent(model: string, m4: Function): any;           mapSingleClick(e: FingerEvent): void;           mapDoubleClick(e: FingerEvent): void;           mapLongPressClick(e: FingerEvent): void;             mapSingleMove(e: FingerEvent, a4?: boolean): void;           mapSingleMoveStart(e: FingerEvent): void;           mapSingleMoveEnd(e: FingerEvent): void;           mapRotation(e: FingerEvent): void;           mapRotationStart(e: FingerEvent): void;           mapRotationEnd(e: FingerEvent): void;           mapDoubleMove(e: FingerEvent): void;           mapDoubleMoveStart(e: FingerEvent): void;           mapDoubleMoveEnd(e: FingerEvent): void;             mapPinchScale(e: FingerEvent, scale: number): void;             mapPinchScaleStart(e: FingerEvent): void;           mapPinchScaleEnd(e: FingerEvent): void;             moveCenterPositon(e: FingerEvent, q2: boolean): void;             transformPointPositionToLL(e: FingerEvent): { left: number; top: number; ll: LatLng; };           addInfoWindow(): void;           removeInfoWindow(): void;         get maxZoom(): number;         set maxZoom(zoom: number);           setMaxZoom(zoom: number): void;         get minZoom(): number;           set minZoom(zoom: number);           setMinZoom(zoom: number): void;               zoomTo(level: number, center: Nullable<LatLng>, screenOffset: Nullable<[ number, number ]>): any;             zoomInOne(center?: Nullable<LatLng>, screenOffset?: Nullable<[ number, number ]>): any;             zoomOutOne(center?: Nullable<LatLng>, screenOffset?: Nullable<[ number, number ]>): any;         getZoom(): number;                 setMapCenter(center: ISetCenter, zoom: Nullable<number>, l2?: boolean, m2?: number): void;                   setMapCenterWithOffset(center: ISetCenter, offset: Point, zoom: Nullable<number>, i2?: boolean, j2?: number): void;           getCenter(options?: Nullable<IGetPoint>): Point | LatLng;         getRotate(): number;         getPerPixelMc(): number;                   changeLocStatus(g2: boolean): void;         enableGesturesRotate(): void;         disableGesturesRotate(): void;         enableGesturesZoom(): void;         disableGesturesZoom(): void;         enableGesturesDrag(): void;         disableGesturesDrag(): void;         enableGesturesPich(): void;         disableGesturesPich(): void;             switchIndoorFloor(floor: string, uid: string): void;         getIndoorInfo(uid?: Nullable<string>): Nullable<IndoorFloorBundles>;               initCustomStyle(f2: string, callback: Callback<void>, overwrite?: boolean): void;           setCustomStyleEnable(enable: boolean): boolean;             pixel2bdll(left: number, top: number): Nullable<LatLng>;           bdll2pixel(e2: LatLng): Nullable<[ number, number ]>;         onceDraw(): void;       changeCrsUpdate(): void;       wgsll2bdll(c2: LatLng): Nullable<LatLng>;       wgsll2gcjll(b2: LatLng): Nullable<LatLng>;       gcjll2bdll(a2: LatLng): Nullable<LatLng>;       bdll2gcjll(z1: LatLng): Nullable<LatLng>;               _copyR2S(w1: string, path: string, callback: Callback<void>): void;                 _fileOpera(overwrite: boolean, v1: string, path: string, callback: Callback<void>): void;       _operaTag(tag: MapStatusChangeReason): void; refresh(): void; static copyCfgToSandBox(f1: Context): void; destroy(): void; } export {}; 