import 'dart:async';

import 'package:flutter/services.dart';
import 'package:wuling_flutter_app/utils/error_util.dart';
import 'package:flutter/material.dart';

import 'constant/main_color.dart';
import 'utils/manager/log_manager.dart';
void main() {

  ErrorUtil.install();
  runZoned(
          () => runApp(const ColorFiltered(
        colorFilter: ColorFilter.mode(Color(0x00000000), BlendMode.colorBurn),
        child: MyApp(),
      )), onError: (Object error, StackTrace stack) {
    LogManager().debug('${error.runtimeType}');
    // IBGlobalChannel.throwMassage(stack.toString());
    if (error.runtimeType == RangeError) {
      LogManager().debug("数组越界======");
    } else if (error.runtimeType == MissingPluginException) {
      LogManager().debug("通道错误======");
    } else {
      LogManager().debug("其他错误======");
    }
  });
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '五菱汽车',
      theme: ThemeData(
        primarySwatch: MainColors.white,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      debugShowCheckedModeBanner: false,
      home: Scaffold(
        body: Container(
          child: Text("hello 你好"),
        ),
      ),
    );
  }
}
