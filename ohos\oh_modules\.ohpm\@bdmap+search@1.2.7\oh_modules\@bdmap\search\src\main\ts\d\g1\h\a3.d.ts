import { LatLng } from '@bdmap/base';
import { PoiChildrenInfo } from "../../e/h/f1";
/**
 * poi详细信息
 */
export interface PoiDetailInfo {
    /**
     * poi名称
     */
    name?: string;
    /**
     * poi经纬度坐标
     */
    location?: LatLng | null;
    /**
     * poi地址信息
     */
    address?: string;
    /**
     * 所属省份
     */
    province?: string;
    /**
     * 所属城市
     */
    city?: string;
    /**
     * 所属区县
     */
    area?: string;
    /**
     * 行政区划编码
     */
    adCode?: number;
    /**
     * poi电话信息
     */
    telephone?: string;
    /**
     * poi的唯一标示
     */
    uid?: string;
    /**
     * 街景图id
     */
    streetId?: string;
    /**
     * 是否有详情页：1有，0没有
     */
    detail?: number;
    /**
     * 距离中心点的距离，圆形区域检索时返回
     */
    distance?: number;
    /**
     * 所属分类，如’hotel’、’cater’
     */
    type?: string;
    /**
     * 标签
     */
    tag?: string;
    /**
     * POI对应的导航引导点坐标。大型面状POI的导航引导点，一般为各类出入口，方便结合导航、路线规划等服务使用
     */
    naviLocation?: LatLng;
    /**
     * poi的详情页Url
     */
    detailUrl?: string;
    /**
     * poi商户的价格
     */
    price?: number;
    /**
     * 营业时间
     */
    shopHours?: string;
    /**
     * 总体评分
     */
    overallRating?: number;
    /**
     * 口味评分
     */
    tasteRating?: number;
    /**
     * 服务评分
     */
    serviceRating?: number;
    /**
     * 环境评分
     */
    environmentRating?: number;
    /**
     * 星级（设备）评分
     */
    facilityRating?: number;
    /**
     * 卫生评分
     */
    hygieneRating?: number;
    /**
     * 技术评分
     */
    technologyRating?: number;
    /**
     * 图片数
     */
    imageNum?: number;
    /**
     * 团购数
     */
    grouponNum?: number;
    /**
     * 优惠数
     */
    discountNum?: number;
    /**
     * 评论数
     */
    commentNum?: number;
    /**
     * 收藏数
     */
    favoriteNum?: number;
    /**
     * 签到数
     */
    checkinNum?: number;
    /**
     * 品牌
     */
    brand?: string;
    /**
     * 等级
     */
    label?: string;
    /**
     * POI子点列表，只有城市检索，周边检索，Sug检索（需要权限）支持
     * 存储POI所有子点信息
     * V5.2.0版本新增字段，需要使用getter和setter方法操作
     */
    poiChildrenInfoList?: PoiChildrenInfo[];
}
