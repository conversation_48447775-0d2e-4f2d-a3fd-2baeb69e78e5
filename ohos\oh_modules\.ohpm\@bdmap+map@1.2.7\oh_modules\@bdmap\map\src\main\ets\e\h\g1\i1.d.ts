import type BaseLayer from "../i/d1/j1"; import type TileLayer from "../i/d1/b2"; import type Bundle from "../i/o/i1"; import type { LayerTag } from "../util/b1/c1"; import type { AnyObject, IMapRoundOption } from "./a2";       export interface MapStatusBundle { level: number; rotation: number; overlooking: number; ptOffsetX: number; ptOffsetY: number; isAnimate: number; animationTime: number; winRound: { left: number; right: number; top: number; bottom: number; }; mapRound?: { minX: number; minY: number; maxX: number; maxY: number; }; centerPtX: number; centerPtY: number; } export interface NativeMapStatusBundle { centerPtX?: number; centerPtY?: number; level: number; rotation: number; overlooking: number; zoomUnits: number; mapRound: IMapRoundOption; } export interface Overlays { [name: number]: Bundle; } export interface ImageSize { image_width: number; image_height: number; } export interface ImageHashcodeSource extends ImageSize { image_hashcode: string; image_data: ArrayBuffer; } export interface ImageData { width: number; height: number; data: ArrayBuffer; } export type TextImage = ImageData | ImageHashcodeSource | ImageSize; export interface LocalPosition { localX: number; localY: number; } export interface FingerItemData extends LocalPosition, AnyObject { globalX: number; globalY: number; pinchCenterX: number; pinchCenterY: number; speed?: number; scale?: number; angle?: number; spanX?: number; spanY?: number; } export interface FingerData extends FingerItemData { fingerList: Array<FingerItemData>; touches?: Array<LocalPosition>; } export interface ImageOverlayData { index?: number; imageHashcode: string; imageWidth: number; imageHeight: number; imageData: ArrayBuffer; } export type TLayerMgr = { [tag in LayerTag as string]?: BaseLayer | Array<TileLayer>; }; 