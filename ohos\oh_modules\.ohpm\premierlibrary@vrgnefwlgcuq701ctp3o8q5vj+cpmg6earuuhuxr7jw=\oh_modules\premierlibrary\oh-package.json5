{"name": "premierlibrary", "version": "1.0.0", "description": "Please describe the basic information.", "main": "Index.js", "acpm-dependencies": ["license", "aio_stat", "aio_network", "aio_util", "aio_crashsdk", "aki", "aio_base_service"], "author": "", "license": "Apache-2.0", "dependencies": {"libpremierlibrary.so": "file:./src/main/cpp/types/libpremierlibrary", "@acpm/acpm_ohos_pc": "file:./libs/acpm_ohos_pc.har"}, "types": "Index.d.ets", "artifactType": "obfuscation", "metadata": {"sourceRoots": ["./src/main"], "debug": false, "nativeDebugSymbol": true}, "compatibleSdkVersion": 12, "compatibleSdkType": "HarmonyOS", "obfuscated": true, "nativeComponents": [{"name": "libpremierlibrary.so", "compatibleSdkVersion": 12, "compatibleSdkType": "HarmonyOS", "linkLibraries": []}]}