interface g59 { d2: string | undefined; scaleWidth: number | undefined; }  @Component export default struct Scale {  @Prop perPixelMc: number;  @Prop mapViewId: string; private settings; private ctxScale; scaleTxt: string; numberArray: Array<number>; ratioArray: Array<number>; updateScale(zoom: number, n59: number): void; zoomToScale(zoom: number, i59: number): g59; drawScale(h59: string, scaleWidth: number): void; aboutToAppear(): Promise<void>; build(): void; } export {}; 