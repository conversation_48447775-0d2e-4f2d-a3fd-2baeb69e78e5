export declare class CacheConfig {
    /**
     * 是否开启缓存功能。true: 开启。默认关闭。
     */
    /****
     * Indicate whether content caching is enabled. Value true indicates that content caching is enabled. The default is disabled.
     */
    mEnable: boolean;
    /**
     * 缓存目录
     */
    /****
     * The directory of the cache.
     */
    mDir: string;
    /**
     * 缓存目录的最大占用空间。如果超过，则删除最旧的文件。单位：MB
     */
    /****
     * The maximum cache size. If the size of the files need to be cached exceeds the maximum cache size, the earliest files are deleted. Unit: MB.
     */
    mMaxSizeMB: number;
    /**
     * 设置能够缓存的单个文件的最大时长。如果文件的时长超过此时长，则不会缓存。单位：秒。
     */
    /****
     * The maximum size of a single file that can be cached. Files that fail to match this limit are not cached. Unit: seconds.
     */
    mMaxDurationS: number;
    /**
     * 获取缓存启用状态
     * @return {boolean}
     */
    /****
     * Get the cache enable status.
     * @return {boolean}
     */
    isCacheEnabled(): boolean;
    /**
     * 设置缓存启用状态
     * @param {boolean} enable
     */
    /****
     * Set the cache enable status.
     * @param {boolean} enable
     */
    setCacheEnabled(x21: boolean): void;
    /**
     * 获取缓存目录
     * @return {string}
     */
    /****
     * Get the cache directory.
     * @return {string}
     */
    getCacheDir(): string;
    /**
     * 设置缓存目录
     * @param {string} dir
     */
    /****
     * Set the cache directory.
     * @param {string} dir
     */
    setCacheDir(w21: string): void;
    /**
     * 获取最大缓存大小
     * @return {number}
     */
    /****
     * Get the maximum cache size.
     * @return {number}
     */
    getMaxCacheSizeMB(): number;
    /**
     * 设置最大缓存大小
     * @param {number} maxSizeMB
     */
    /****
     * Set the maximum cache size.
     * @param {number} maxSizeMB
     */
    setMaxCacheSizeMB(v21: number): void;
    /**
     * 获取单个文件的最大缓存时长
     * @return {number}
     */
    /****
     * Get the maximum duration of a single cached file.
     * @return {number}
     */
    getMaxDurationS(): number;
    /**
     * 设置单个文件的最大缓存时长
     * @param {number} maxDurationS
     */
    /****
     * Set the maximum duration of a single cached file.
     * @param {number} maxDurationS
     */
    setMaxDurationS(u21: number): void;
}
