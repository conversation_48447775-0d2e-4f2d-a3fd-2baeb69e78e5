import premierlibrary from 'libpremierlibrary.so';
export class AliPlayerGlobalSettings {
    static getInstance() {
        if (AliPlayerGlobalSettings.sInstance == undefined) {
            AliPlayerGlobalSettings.sInstance = new AliPlayerGlobalSettings();
        }
        return AliPlayerGlobalSettings.sInstance;
    }
    constructor() {
        this.nOnGetUrlHashCallback = (e8) => {
            if (AliPlayerGlobalSettings.sOnGetUrlHashCallback != null) {
                return AliPlayerGlobalSettings.sOnGetUrlHashCallback.getUrlHashCallback(e8);
            }
            else {
                return null;
            }
        };
    }
    static OnGetUrlHashCallback(b8) {
        if (AliPlayerGlobalSettings.sOnGetUrlHashCallback != null) {
            let c8 = AliPlayerGlobalSettings.sOnGetUrlHashCallback.getUrlHashCallback(b8);
            if (c8 != undefined) {
                if (c8.length == 0) {
                    return AliPlayerGlobalSettings.FORBID_URLHASHCB;
                }
                else {
                    return c8;
                }
            }
            else {
                return AliPlayerGlobalSettings.FORBID_URLHASHCB;
            }
        }
        else {
            return AliPlayerGlobalSettings.FORBID_URLHASHCB;
        }
    }
    static getSdkVersion() {
        return premierlibrary.nGetSdkVersion();
    }
    static setOptionStr(z7, a8) {
        premierlibrary.nSetOptionStr(z7, a8);
    }
    static setOptionNum(x7, y7) {
        premierlibrary.nSetOptionInt(x7, y7);
    }
    static setDNSResolve(v7, w7) {
        premierlibrary.nSetDNSResolve(v7, w7);
    }
    static setUseHttp2(u7) {
        premierlibrary.nSetUseHttp2(u7);
    }
    static setIPResolveType(t7) {
        premierlibrary.nGlobalSettingSetIPResolveType(t7);
    }
    static setAudioStreamType(s7) {
        premierlibrary.nSetAudioStreamType(s7);
    }
    static forceAudioRendingFormat(o7, p7, q7, r7) {
        premierlibrary.nForceAudioRendingFormat(o7, p7, q7, r7);
    }
    static enableLocalCache(l7, m7, n7) {
        return premierlibrary.nEnableLocalCache(l7, m7, n7);
    }
    static setCacheFileClearConfig(i7, j7, k7) {
        premierlibrary.nSetCacheFileClearConfig(i7, j7, k7);
    }
    static clearCaches() {
        premierlibrary.nClearCaches();
    }
    static enableHttpDns(h7) {
        premierlibrary.nEnableHttpDns(h7);
    }
    static enableEnhancedHttpDns(g7) {
        premierlibrary.nEnableEnhancedHttpDns(g7);
    }
    static enableNetworkBalance(f7) {
        premierlibrary.nEnableNetworkBalance(f7);
    }
    static enableBufferToLocalCache(e7) {
        premierlibrary.nEnableBufferToLocalCache(e7);
    }
    static disableCrashUpload(d7) {
        premierlibrary.nDisableCrashUpload(d7);
    }
    static setCacheUrlHashCallback(c7) {
        AliPlayerGlobalSettings.sOnGetUrlHashCallback = c7;
        premierlibrary.nSetCacheUrlHashCallback(AliPlayerGlobalSettings.getInstance(), c7 != null);
    }
}
AliPlayerGlobalSettings.SET_PRE_CONNECT_DOMAIN = 0;
AliPlayerGlobalSettings.SET_DNS_PRIORITY_LOCAL_FIRST = 1;
AliPlayerGlobalSettings.ENABLE_H2_MULTIPLEX = 2;
AliPlayerGlobalSettings.SET_EXTRA_DATA = 3;
AliPlayerGlobalSettings.ENABLE_ANDROID_DECODE_REUSE = 4;
AliPlayerGlobalSettings.NOT_PAUSE_WHEN_PREPARING = 5;
AliPlayerGlobalSettings.ALLOW_RTS_DEGRADE = 6;
AliPlayerGlobalSettings.ENABLE_DECODER_FAST_FIRST_FRAME = 7;
AliPlayerGlobalSettings.DISABLE_CAPTURE_SCALE = 8;
AliPlayerGlobalSettings.SCENE_PLAYER = 0;
AliPlayerGlobalSettings.SCENE_LOADER = 1;
AliPlayerGlobalSettings.CodecType_H265 = 0;
AliPlayerGlobalSettings.FORBID_URLHASHCB = "forbid_UseUrlHashCb";
AliPlayerGlobalSettings.sOnGetUrlHashCallback = null;
export var AudioStreamType;
(function (b7) {
    b7[b7["AUDIOSTREAM_USAGE_UNKNOWN"] = 0] = "AUDIOSTREAM_USAGE_UNKNOWN";
    b7[b7["AUDIOSTREAM_USAGE_MUSIC"] = 1] = "AUDIOSTREAM_USAGE_MUSIC";
    b7[b7["AUDIOSTREAM_USAGE_VOICE_COMMUNICATION"] = 2] = "AUDIOSTREAM_USAGE_VOICE_COMMUNICATION";
    b7[b7["AUDIOSTREAM_USAGE_VOICE_ASSISTANT"] = 3] = "AUDIOSTREAM_USAGE_VOICE_ASSISTANT";
    b7[b7["AUDIOSTREAM_USAGE_ALARM"] = 4] = "AUDIOSTREAM_USAGE_ALARM";
    b7[b7["AUDIOSTREAM_USAGE_VOICE_MESSAGE"] = 5] = "AUDIOSTREAM_USAGE_VOICE_MESSAGE";
    b7[b7["AUDIOSTREAM_USAGE_RINGTONE"] = 6] = "AUDIOSTREAM_USAGE_RINGTONE";
    b7[b7["AUDIOSTREAM_USAGE_NOTIFICATION"] = 7] = "AUDIOSTREAM_USAGE_NOTIFICATION";
    b7[b7["AUDIOSTREAM_USAGE_ACCESSIBILITY"] = 8] = "AUDIOSTREAM_USAGE_ACCESSIBILITY";
    b7[b7["AUDIOSTREAM_USAGE_MOVIE"] = 10] = "AUDIOSTREAM_USAGE_MOVIE";
    b7[b7["AUDIOSTREAM_USAGE_GAME"] = 11] = "AUDIOSTREAM_USAGE_GAME";
    b7[b7["AUDIOSTREAM_USAGE_AUDIOBOOK"] = 12] = "AUDIOSTREAM_USAGE_AUDIOBOOK";
    b7[b7["AUDIOSTREAM_USAGE_NAVIGATION"] = 13] = "AUDIOSTREAM_USAGE_NAVIGATION";
    b7[b7["AUDIOSTREAM_USAGE_VIDEO_COMMUNICATION"] = 17] = "AUDIOSTREAM_USAGE_VIDEO_COMMUNICATION";
})(AudioStreamType || (AudioStreamType = {}));
