import ImageEntity from "./s"; import { StrokeStyle } from "../../util/b1/c1"; import Bundle from "./i1"; import type { ColorString, IStrokeOption } from "../../g1/a2"; import type { ImageOverlayData } from "../../g1/i1"; import BmLineStyle from "../c2/f2/w3"; import type OverlayListener from "../m/i2"; import type BmBitmapResource from "../c2/f2/t3"; import { TextureOption } from "../../util/b1/c1"; import { Callback } from '@kit.BasicServicesKit'; import type Polygon from "../m/x"; import type Circle from "../m/n";           export default class Stroke {   private m_strokeWidth;   private m_color;   private m_strokeStyle; private m_textureOption; private m_strokeTexture; private bmLineStyle; private listener; private own;                       constructor(e35: IStrokeOption);         setOwn(d35: Polygon | Circle): void;         get strokeWidth(): number;         set strokeWidth(c35: number);         getStrokeWidth(): number;         setStrokeWidth(b35: number): this;         get color(): ColorString;         set color(color: ColorString);         getColor(): string;         setColor(color: ColorString): this;         get strokeStyle(): StrokeStyle;         set strokeStyle(z34: StrokeStyle);         getStrokeStyle(): StrokeStyle;         setStrokeStyle(y34: StrokeStyle): this;         setStrokeTexture(x34: ImageEntity): this;         get strokeTexture(): ImageEntity;         set strokeTexture(w34: ImageEntity);           get textureOption(): TextureOption;           set textureOption(v34: TextureOption);           setTextureOption(u34: TextureOption): this;           getTextureOption(): TextureOption;       setListener(listener: OverlayListener): void;       getLineStyleObj(): BmLineStyle;         strokeBundle(): Bundle;             getImageEntity(r34?: string): ImageEntity;       updateBmIcon(icon: ImageEntity): void;       updateBitMap(bitmap: BmBitmapResource): void;       getImageBitMap(image: ImageEntity, callback: Callback<BmBitmapResource | undefined>): void;               strokeStyleBundle(x: number, y: number, p34: ImageOverlayData): Bundle;       toString(): string; } 