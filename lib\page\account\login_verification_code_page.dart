import 'dart:async';
import 'package:wuling_flutter_app/utils/manager/login_manager.dart';
import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/utils/manager/loading_manager.dart';
import 'package:wuling_flutter_app/page/account/set_password_page.dart';
import 'package:flutter/gestures.dart';
import 'package:wuling_flutter_app/widgets/webview/webview.dart';
import 'package:wuling_flutter_app/api/index.dart';
import 'package:wuling_flutter_app/utils/manager/notification_manager.dart';
import 'package:wuling_flutter_app/constant/constant.dart';
import 'package:wuling_flutter_app/utils/manager/alert_manager.dart';
import 'package:wuling_flutter_app/widgets/common/image_view.dart';
import 'package:wuling_flutter_app/page/base_page/base_page.dart';

import '../../utils/manager/log_manager.dart';
enum LoginVerificationCodeType {
  SMSLogin, // 验证码登录
  Register, // 手机号注册
  Binding, // 手机号注册
  ForgotPwd, // 登录流程忘记密码
  ChangePwd, // 个人中心修改密码
  PwdLogin, // 密码登录
}

// class LoginVerificationCodeScreen extends StatefulWidget {
//   final LoginVerificationCodeType type;
//   final String? mobile;
//   const LoginVerificationCodeScreen({Key? key, required this.type, this.mobile})
//       : super(key: key);
//
//   @override
//   _LoginVerificationCodeScreenState createState() =>
//       _LoginVerificationCodeScreenState();
// }
//
// class _LoginVerificationCodeScreenState
//     extends State<LoginVerificationCodeScreen> {
//   final TextEditingController _phoneController = TextEditingController();
//   final FocusNode _codeFocusNode = FocusNode();
//   final TextEditingController _codeController = TextEditingController();
//   final List<String> _inputChars = List<String>.filled(6, '');
//   bool _isLoginButtonEnabled = false; //是否激活按钮
//   bool _isAgree = false;
//   String _welcomeText = '';
//   String _placeholderText = '请输入您的手机号码';
//   bool _showPrivacy = true;
//   final String _codeBtnTxt = '获取验证码';
//   bool inputCodeShow = false; //是否显示输入验证码界面
//   bool isGettingCode = false; //是否正在获取验证码
//   Timer? _timer;
//   int _start = 60;
//   String _retryBtnText = '重新获取';
//   bool _isRetryBtnEnabled = true;
//
//   @override
//   void initState() {
//     super.initState();
//     _phoneController.addListener(_updateButtonState);
//     _codeController.addListener(_updateCodeText);
//     setUI();
//   }
//
//   @override
//   void dispose() {
//     _phoneController.dispose();
//     _codeController.dispose();
//     _codeFocusNode.dispose();
//     _timer?.cancel();
//     super.dispose();
//   }
//
//   void showPrivacyModal() {
//     WidgetsBinding.instance.addPostFrameCallback((_) {
//       AlertManager().showUserAgreementAlertView(context);
//     });
//   }
//
//   void setUI() {
//     switch (widget.type) {
//       case LoginVerificationCodeType.SMSLogin:
//         _welcomeText = '您好，\n请使用验证码登录';
//         _showPrivacy = true;
//         break;
//       case LoginVerificationCodeType.Register:
//         _welcomeText = '您好，\n欢迎加入五菱汽车';
//         _placeholderText = '注册手机号';
//         _showPrivacy = true;
//         showPrivacyModal();
//         break;
//       case LoginVerificationCodeType.ForgotPwd:
//         _welcomeText = '您好，\n请使用验证码找回';
//         _showPrivacy = false;
//         _isAgree = true;
//         break;
//       case LoginVerificationCodeType.ChangePwd:
//         _welcomeText = '您好，\n请使用验证码修改';
//         _showPrivacy = false;
//         _isAgree = true;
//         break;
//       case LoginVerificationCodeType.PwdLogin:
//         _welcomeText = '您好，\n请使用验证码登录';
//         _showPrivacy = true;
//         break;
//     }
//     setState(() {
//       _welcomeText = _welcomeText;
//       _placeholderText = _placeholderText;
//       _showPrivacy = _showPrivacy;
//       _phoneController.text = widget.mobile ?? '';
//     });
//   }
//
//   void _updateButtonState() {
//     final isPhoneValid = _isPhoneValid(_phoneController.text);
//     setState(() {
//       _isLoginButtonEnabled = isPhoneValid;
//     });
//   }
//
//   void _updateCodeText() {
//     setState(() {
//       for (int i = 0; i < 6; i++) {
//         if (i < _codeController.text.length) {
//           _inputChars[i] = _codeController.text[i];
//         } else {
//           _inputChars[i] = '';
//         }
//       }
//     });
//     //写满后自动进行下一步操作
//     if (_codeController.text.length >= 6) {
//       // 调用这个方法来隐藏键盘
//       FocusScope.of(context).unfocus();
//       _submit();
//     }
//   }
//
//   //提交验证码进行下一步操作
//   void _submit() {
//     switch (widget.type) {
//       case LoginVerificationCodeType.SMSLogin:
//         _quickLoginRequest();
//         break;
//       case LoginVerificationCodeType.Register:
//         _quickLoginRequest();
//         break;
//       case LoginVerificationCodeType.ForgotPwd:
//         _checkForgetPasswordSMSCode();
//         break;
//       case LoginVerificationCodeType.ChangePwd:
//         _checkChangePasswordSMSCode();
//         break;
//       case LoginVerificationCodeType.PwdLogin:
//         break;
//     }
//   }
//
//   // 注册、登录时请求
//   void _quickLoginRequest() async {
//     String phone = _phoneController.text;
//     String code = _codeController.text;
//     LoadingManager.show(status: '登录中');
//     try {
//       await LoginManager().oauthLoginWithCode(mobile: phone, smsCode: code);
//       await LoginManager().getSelfInfo();
//       //通知登录成功
//       NotificationManager()
//           .postNotification(Constant.NOTIFICATION_LOGIN_SUCCEED);
//       LoadingManager.dismiss();
//       if (!mounted) return;
//       Navigator.popUntil(context, (route) => route.isFirst); //关闭整个登录页面
//     } catch (e) {
//       return;
//     }
//   }
//
//   //忘记密码时请求
//   void _checkForgetPasswordSMSCode() async {
//     String phone = _phoneController.text;
//     String code = _codeController.text;
//     LoadingManager.show(status: '验证中');
//     try {
//       bool success =
//           await userAPI.checkSMSCodeWithMobile(phone, code, 'changePassword');
//       if (success) {
//         LoadingManager.dismiss();
//         if (!mounted) return;
//         Navigator.push(
//           context,
//           MaterialPageRoute(
//             builder: (context) => SetPasswordScreen(
//               type: SetPasswordType.ForgotPwd,
//               mobile: phone,
//               smsCode: code,
//             ),
//           ),
//         );
//       } else {
//         LoadingManager.showError('验证码错误');
//       }
//     } catch (e) {
//       return;
//     }
//   }
//
//   //更改密码时请求
//   void _checkChangePasswordSMSCode() async {
//     String phone = _phoneController.text;
//     String code = _codeController.text;
//     LoadingManager.show(status: '验证中');
//     try {
//       bool success =
//       await userAPI.checkSMSCodeWithMobile(phone, code, 'changePassword');
//       if (success) {
//         LoadingManager.dismiss();
//         if (!mounted) return;
//         Navigator.push(
//           context,
//           MaterialPageRoute(
//             builder: (context) => SetPasswordScreen(
//               type: SetPasswordType.ForgotPwd,
//               mobile: phone,
//               smsCode: code,
//             ),
//           ),
//         );
//       } else {
//         LoadingManager.showError('验证码错误');
//       }
//     } catch (e) {
//       return;
//     }
//   }
//
//   // 开始倒计时
//   void _startTimer() {
//     setState(() {
//       _start = 60;
//       _retryBtnText = '获取中(${_start}s)';
//       _isRetryBtnEnabled = false;
//     });
//
//     const oneSec = Duration(seconds: 1);
//     _timer = Timer.periodic(
//       oneSec,
//       (Timer timer) {
//         if (_start <= 0) {
//           setState(() {
//             timer.cancel();
//             _retryBtnText = '重新获取';
//             _isRetryBtnEnabled = true;
//           });
//         } else {
//           setState(() {
//             _start--;
//             _retryBtnText = '获取中(${_start}s)';
//             _isRetryBtnEnabled = false;
//           });
//         }
//       },
//     );
//   }
//
//   bool _isPhoneValid(String phone) {
//     // 这里可以根据实际需要实现手机号的验证逻辑
//     return phone.length == 11;
//   }
//
//   jumpToPrivacy(int type) {
//     //跳转到协议webview，type为1表示用户协议，2表示隐私政策
//     Navigator.push(
//       context,
//       MaterialPageRoute(
//         builder: (context) => WebViewPage(
//           url:
//               'https://cdn-m.00bang.cn/llb/html/agreement/linglingProtocol.html?protocolType=$type',
//           needTitleBar: true,
//           titleName: type == 1 ? '用户协议' : '隐私政策',
//         ),
//       ),
//     );
//   }
//
//   void _getSmsCode() async {
//     final phone = _phoneController.text;
//     if (!_isAgree) {
//       LoadingManager.showInfo('请先同意用户协议及隐私政策');
//       return;
//     }
//     if (!_isPhoneValid(phone)) {
//       LoadingManager.showInfo('请输入正确的手机号');
//       return;
//     }
//     LoadingManager.show(status: '获取中');
//     setState(() {
//       isGettingCode = true;
//     });
//     // 获取验证码
//     try {
//       bool success = false;
//       switch (widget.type) {
//         case LoginVerificationCodeType.SMSLogin:
//           success = await userAPI.getQuickLoginSMSCode(phone);
//           break;
//         case LoginVerificationCodeType.Register:
//           success = await userAPI.getQuickLoginSMSCode(phone);
//           break;
//         case LoginVerificationCodeType.ForgotPwd:
//           success = await userAPI.getChangePasswordSMSCode(phone);
//           break;
//         case LoginVerificationCodeType.ChangePwd:
//           success = await userAPI.getChangePasswordSMSCode(phone);
//           break;
//         case LoginVerificationCodeType.PwdLogin:
//           break;
//       }
//       if (success) {
//         LoadingManager.dismiss();
//         _startTimer();
//       }
//       setState(() {
//         inputCodeShow = success;
//         isGettingCode = false;
//       });
//     } catch (e) {
//       setState(() {
//         isGettingCode = false;
//       });
//       LogManager().debug(e);
//     }
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return GestureDetector(
//       onTap: () {
//         // 调用这个方法来隐藏键盘
//         FocusScope.of(context).unfocus();
//       },
//       child: Stack(children: <Widget>[
//         // 白色背景层，带有一定的透明度
//         Container(
//           color: Colors.white,
//         ),
//         // 背景图片
//         Container(
//           decoration: const BoxDecoration(
//             image: DecorationImage(
//               image: AssetImage('assets/images/login/login_bg.png'), // 背景图片路径
//               fit: BoxFit.cover,
//             ),
//           ),
//         ),
//         Padding(
//           padding: const EdgeInsets.all(16.0),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: <Widget>[
//               const SizedBox(height: 40),
//               GestureDetector(
//                 onTap: () {
//                   // 处理点击事件
//                   Navigator.pop(context);
//                 },
//                 child: const Icon(Icons.arrow_back_ios, color: Colors.black),
//               ),
//               const SizedBox(height: 50),
//               Padding(
//                 padding: const EdgeInsets.symmetric(horizontal: 12),
//                 child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: <Widget>[
//                       Text(
//                         _welcomeText,
//                         style: const TextStyle(fontSize: 32),
//                       ),
//                       const SizedBox(height: 20),
//                       TextField(
//                         style: const TextStyle(
//                           fontSize: 16,
//                           color: Color(0xff383a40),
//                         ),
//                         cursorColor: Colors.black,
//                         decoration: InputDecoration(
//                           labelText: _placeholderText,
//                           labelStyle: const TextStyle(
//                             fontSize: 16,
//                             color: Color(0xff9b9da9),
//                           ),
//                           enabledBorder: const UnderlineInputBorder(
//                             borderSide: BorderSide(color: Color(0xfff4f4f4)),
//                           ),
//                           focusedBorder: const UnderlineInputBorder(
//                             borderSide: BorderSide(color: Color(0xfff4f4f4)),
//                           ),
//                         ),
//                         enabled: !inputCodeShow,
//                         controller: _phoneController,
//                         keyboardType: TextInputType.number,
//                       ),
//                       const SizedBox(height: 20),
//                       if (_showPrivacy)
//                         Row(
//                           children: <Widget>[
//                             GestureDetector(
//                                 onTap: () {
//                                   setState(() {
//                                     _isAgree = !_isAgree;
//                                   });
//                                 },
//                                 child: Container(
//                                     height: 23,
//                                     width: 23,
//                                     child: Center(
//                                       child: SizedBox(
//                                         width: 13,
//                                         height: 13,
//                                         child: ImageView(
//                                             _isAgree ? 'assets/images/login/login_page_icon_checkbox_select.png':
//                                             'assets/images/login/login_page_icon_checkbox_normal.png',
//                                             fit: BoxFit.contain
//                                         ),
//                                       ),
//                                     )
//                                 )
//                             ),
//                             // const SizedBox(width: 5),
//                             Expanded(
//                               child: GestureDetector(
//                                 onTap: () {},
//                                 child: Text.rich(
//                                   TextSpan(
//                                     text: '我已阅读并同意',
//                                     style: const TextStyle(
//                                       color: Color(0xff7b7b7b),
//                                       fontSize: 12,
//                                     ),
//                                     recognizer: TapGestureRecognizer()
//                                       ..onTap = () {
//                                         setState(() {
//                                           _isAgree = !_isAgree;
//                                         });
//                                       },
//                                     children: [
//                                       TextSpan(
//                                         text: '五菱汽车用户协议',
//                                         style: const TextStyle(
//                                             color: Colors.blue,
//                                             decoration:
//                                                 TextDecoration.underline),
//                                         recognizer: TapGestureRecognizer()
//                                           ..onTap = () {
//                                             jumpToPrivacy(1);
//                                           },
//                                       ),
//                                       const TextSpan(text: '和'),
//                                       TextSpan(
//                                         text: '隐私政策',
//                                         style: const TextStyle(
//                                             color: Colors.blue,
//                                             decoration:
//                                                 TextDecoration.underline),
//                                         recognizer: TapGestureRecognizer()
//                                           ..onTap = () {
//                                             jumpToPrivacy(2);
//                                           },
//                                       ),
//                                     ],
//                                   ),
//                                 ),
//                               ),
//                             ),
//                           ],
//                         ),
//                       const SizedBox(height: 60),
//                       if (!inputCodeShow)
//                         Center(
//                           child: ElevatedButton(
//                             style: ElevatedButton.styleFrom(
//                               backgroundColor: const Color(0xFFea0029), // 背景色
//                               minimumSize: const Size(double.infinity, 50),
//                               shape: RoundedRectangleBorder(
//                                 borderRadius: BorderRadius.circular(4), // 圆角半径
//                               ),
//                             ),
//                             onPressed: _isLoginButtonEnabled && !isGettingCode
//                                 ? _getSmsCode
//                                 : null,
//                             child: Text(_codeBtnTxt,
//                                 style: const TextStyle(
//                                     color: Colors.white, fontSize: 16)),
//                           ),
//                         ),
//                       if (inputCodeShow)
//                         Column(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           children: <Widget>[
//                             Center(
//                               child: GestureDetector(
//                                 onTap: () {
//                                   // 重新获取验证码的逻辑
//                                   if (!isGettingCode && _isRetryBtnEnabled) {
//                                     _getSmsCode();
//                                   }
//                                 },
//                                 child: Text(
//                                   _retryBtnText,
//                                   style: const TextStyle(
//                                       color: Color(0xff383a40), fontSize: 14),
//                                 ),
//                               ),
//                             ),
//                             const SizedBox(height: 20),
//                             GestureDetector(
//                               onTap: () {
//                                 FocusScope.of(context)
//                                     .requestFocus(_codeFocusNode);
//                               },
//                               child: Row(
//                                 mainAxisAlignment: MainAxisAlignment.center,
//                                 children: List.generate(6, (index) {
//                                   return Row(
//                                     children: [
//                                       Container(
//                                         width: 28,
//                                         height: 40,
//                                         decoration: const BoxDecoration(
//                                           border: Border(
//                                               bottom: BorderSide(
//                                                   width: 1,
//                                                   color: Color(0xffb4b4b4))),
//                                         ),
//                                         alignment: Alignment.center,
//                                         child: Text(
//                                           _inputChars[index],
//                                           style: const TextStyle(
//                                               fontSize: 24,
//                                               color: Color(0xff383a40)),
//                                         ),
//                                       ),
//                                       if (index < 5)
//                                         const SizedBox(
//                                             width: 12), // 每个容器之间增加一个 SizedBox
//                                     ],
//                                   );
//                                 }),
//                               ),
//                             ),
//                             //设置一个隐藏的输入框
//                             Opacity(
//                               opacity: 0,
//                               child: SizedBox(
//                                 width: 0,
//                                 height: 0,
//                                 child: TextField(
//                                   focusNode: _codeFocusNode,
//                                   controller: _codeController,
//                                   maxLength: 6,
//                                   keyboardType: TextInputType.number,
//                                   decoration: const InputDecoration(
//                                     border: InputBorder.none,
//                                     counterText: '',
//                                   ),
//                                   cursorColor: Colors.transparent,
//                                 ),
//                               ),
//                             ),
//                           ],
//                         ),
//                     ]),
//               )
//             ],
//           ),
//         )
//       ]),
//     );
//   }
// }

class LoginVerificationCodePage extends BasePage {
  final LoginVerificationCodeType type;
  final String? mobile;
  final String? code;
  final String? unionId;
  LoginVerificationCodePage({
    Key? key,
    bool hideAppBar = true,
    bool isWithinSafeArea = false,
    String appBarTitle = 'New Page',
    Color appBarColor = Colors.transparent,
    Color pageBackgroundColor = Colors.white,
    this.code,
    this.unionId,
    required this.type,
    this.mobile
  }) : super(
          key: key,
          hideAppBar: hideAppBar,
          isWithinSafeArea: isWithinSafeArea,
          appBarTitle: appBarTitle,
          appBarColor: appBarColor,
          pageBackgroundColor: pageBackgroundColor,
          enablePrivacyMode: true
        );

  @override
  _LoginVerificationCodePageState createState() => _LoginVerificationCodePageState();
}

class _LoginVerificationCodePageState extends BasePageState<LoginVerificationCodePage> {
  final TextEditingController _phoneController = TextEditingController();
  final FocusNode _codeFocusNode = FocusNode();
  final TextEditingController _codeController = TextEditingController();
  final List<String> _inputChars = List<String>.filled(6, '');
  bool _isLoginButtonEnabled = false; //是否激活按钮
  bool _isAgree = false;
  String _welcomeText = '';
  String _placeholderText = '请输入您的手机号码';
  bool _showPrivacy = true;
  final String _codeBtnTxt = '获取验证码';
  bool inputCodeShow = false; //是否显示输入验证码界面
  bool isGettingCode = false; //是否正在获取验证码
  Timer? _timer;
  int _start = 60;
  String _retryBtnText = '重新获取';
  bool _isRetryBtnEnabled = true;

  @override
  void pageInitState() {
    _phoneController.addListener(_updateButtonState);
    _codeController.addListener(_updateCodeText);
    setUI();
  }

  void showPrivacyModal() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      AlertManager().showUserAgreementAlertView(context);
    });
  }

  void setUI() {
    switch (widget.type) {
      case LoginVerificationCodeType.SMSLogin:
        _welcomeText = '您好，\n请使用验证码登录';
        _showPrivacy = true;
        break;
      case LoginVerificationCodeType.Register:
        _welcomeText = '您好，\n欢迎加入五菱汽车';
        _placeholderText = '注册手机号';
        _showPrivacy = true;
        showPrivacyModal();
        break;
      case LoginVerificationCodeType.Binding:
        _welcomeText = '您好，\n请绑定您的手机号';
        _showPrivacy = true;
        break;
      case LoginVerificationCodeType.ForgotPwd:
        _welcomeText = '您好，\n请使用验证码找回';
        _showPrivacy = false;
        _isAgree = true;
        break;
      case LoginVerificationCodeType.ChangePwd:
        _welcomeText = '您好，\n请使用验证码修改';
        _showPrivacy = false;
        _isAgree = true;
        break;
      case LoginVerificationCodeType.PwdLogin:
        _welcomeText = '您好，\n请使用验证码登录';
        _showPrivacy = true;
        break;
    }
    setState(() {
      _welcomeText = _welcomeText;
      _placeholderText = _placeholderText;
      _showPrivacy = _showPrivacy;
      _phoneController.text = widget.mobile ?? '';
    });
  }

  void _updateButtonState() {
    final isPhoneValid = _isPhoneValid(_phoneController.text);
    setState(() {
      _isLoginButtonEnabled = isPhoneValid;
    });
  }

  void _updateCodeText() {
    setState(() {
      for (int i = 0; i < 6; i++) {
        if (i < _codeController.text.length) {
          _inputChars[i] = _codeController.text[i];
        } else {
          _inputChars[i] = '';
        }
      }
    });
    //写满后自动进行下一步操作
    if (_codeController.text.length >= 6) {
      // 调用这个方法来隐藏键盘
      FocusScope.of(context).unfocus();
      _submit();
    }
  }

  //提交验证码进行下一步操作
  void _submit() {
    switch (widget.type) {
      case LoginVerificationCodeType.SMSLogin:
        _quickLoginRequest();
        break;
      case LoginVerificationCodeType.Register:
        _quickLoginRequest();
        break;
      case LoginVerificationCodeType.Binding:
        _quickLoginRequest();
        break;
      case LoginVerificationCodeType.ForgotPwd:
        _checkForgetPasswordSMSCode();
        break;
      case LoginVerificationCodeType.ChangePwd:
        _checkChangePasswordSMSCode();
        break;
      case LoginVerificationCodeType.PwdLogin:
        break;
    }
  }

  // 注册、登录时请求
  void _quickLoginRequest() async {
    String phone = _phoneController.text;
    String code = _codeController.text;
    LoadingManager.show(status: '登录中');
    try {

      if(widget.unionId!=null){
        await LoginManager().oauthLoginWithWechat(mobile: phone, smsCode: code,unionId: widget.unionId!);
      }else{
        await LoginManager().oauthLoginWithCode(mobile: phone, smsCode: code);
      }
      await LoginManager().getSelfInfo();


      //通知登录成功
      NotificationManager()
          .postNotification(Constant.NOTIFICATION_LOGIN_SUCCEED);
      LoadingManager.dismiss();
      if (!mounted) return;
      Navigator.popUntil(context, (route) => route.isFirst); //关闭整个登录页面
    } catch (e) {
      return;
    }
  }

  //忘记密码时请求
  void _checkForgetPasswordSMSCode() async {
    String phone = _phoneController.text;
    String code = _codeController.text;
    LoadingManager.show(status: '验证中');
    try {
      bool success =
      await userAPI.checkSMSCodeWithMobile(phone, code, 'changePassword');
      if (success) {
        LoadingManager.dismiss();
        if (!mounted) return;
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => SetPasswordScreen(
              type: SetPasswordType.ForgotPwd,
              mobile: phone,
              smsCode: code,
            ),
          ),
        );
      } else {
        LoadingManager.showError('验证码错误');
      }
    } catch (e) {
      return;
    }
  }

  //更改密码时请求
  void _checkChangePasswordSMSCode() async {
    String phone = _phoneController.text;
    String code = _codeController.text;
    LoadingManager.show(status: '验证中');
    try {
      bool success =
      await userAPI.checkSMSCodeWithMobile(phone, code, 'changePassword');
      if (success) {
        LoadingManager.dismiss();
        if (!mounted) return;
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => SetPasswordScreen(
              type: SetPasswordType.ForgotPwd,
              mobile: phone,
              smsCode: code,
            ),
          ),
        );
      } else {
        LoadingManager.showError('验证码错误');
      }
    } catch (e) {
      return;
    }
  }

  // 开始倒计时
  void _startTimer() {
    setState(() {
      _start = 60;
      _retryBtnText = '获取中(${_start}s)';
      _isRetryBtnEnabled = false;
    });

    const oneSec = Duration(seconds: 1);
    _timer = Timer.periodic(
      oneSec,
          (Timer timer) {
        if (_start <= 0) {
          setState(() {
            timer.cancel();
            _retryBtnText = '重新获取';
            _isRetryBtnEnabled = true;
          });
        } else {
          setState(() {
            _start--;
            _retryBtnText = '获取中(${_start}s)';
            _isRetryBtnEnabled = false;
          });
        }
      },
    );
  }

  bool _isPhoneValid(String phone) {
    // 这里可以根据实际需要实现手机号的验证逻辑
    return phone.length == 11;
  }

  jumpToPrivacy(int type) {
    //跳转到协议webview，type为1表示用户协议，2表示隐私政策
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => WebViewPage(
          url:
          'https://cdn-m.00bang.cn/llb/html/agreement/linglingProtocol.html?protocolType=$type',
          needTitleBar: true,
          titleName: type == 1 ? '用户协议' : '隐私政策',
        ),
      ),
    );
  }

  void _getSmsCode() async {
    final phone = _phoneController.text;
    if (!_isAgree) {
      LoadingManager.showInfo('请先同意用户协议及隐私政策');
      return;
    }
    if (!_isPhoneValid(phone)) {
      LoadingManager.showInfo('请输入正确的手机号');
      return;
    }
    LoadingManager.show(status: '获取中');
    setState(() {
      isGettingCode = true;
    });
    // 获取验证码
    try {
      bool success = false;
      switch (widget.type) {
        case LoginVerificationCodeType.SMSLogin:
          success = await userAPI.getQuickLoginSMSCode(phone);
          break;
        case LoginVerificationCodeType.Register:
          success = await userAPI.getQuickLoginSMSCode(phone);
          break;
        case LoginVerificationCodeType.Binding:
          success = await userAPI.getQuickLoginSMSCode(phone);
          break;
        case LoginVerificationCodeType.ForgotPwd:
          success = await userAPI.getChangePasswordSMSCode(phone);
          break;
        case LoginVerificationCodeType.ChangePwd:
          success = await userAPI.getChangePasswordSMSCode(phone);
          break;
        case LoginVerificationCodeType.PwdLogin:
          break;
      }
      if (success) {
        LoadingManager.dismiss();
        _startTimer();
      }
      setState(() {
        inputCodeShow = success;
        isGettingCode = false;
      });
    } catch (e) {
      setState(() {
        isGettingCode = false;
      });
      LogManager().debug('$e');
    }
  }

  @override
  Widget buildPageContent(BuildContext context) {
    // 在此处添加页面ui设置，默认添加一个center text组件
    return GestureDetector(
      onTap: () {
        // 调用这个方法来隐藏键盘
        FocusScope.of(context).unfocus();
      },
      child: Stack(children: <Widget>[
        // 白色背景层，带有一定的透明度
        Container(
          color: Colors.white,
        ),
        // 背景图片
        Container(
          decoration: const BoxDecoration(
            image: DecorationImage(
              image: AssetImage('assets/images/login/login_bg.png'), // 背景图片路径
              fit: BoxFit.cover,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              const SizedBox(height: 40),
              GestureDetector(
                onTap: () {
                  // 处理点击事件
                  Navigator.pop(context);
                },
                child: const Icon(Icons.arrow_back_ios, color: Colors.black),
              ),
              const SizedBox(height: 50),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        _welcomeText,
                        style: const TextStyle(fontSize: 32),
                      ),
                      const SizedBox(height: 20),
                      TextField(
                        style: const TextStyle(
                          fontSize: 16,
                          color: Color(0xff383a40),
                        ),
                        cursorColor: Colors.black,
                        decoration: InputDecoration(
                          labelText: _placeholderText,
                          labelStyle: const TextStyle(
                            fontSize: 16,
                            color: Color(0xff9b9da9),
                          ),
                          enabledBorder: const UnderlineInputBorder(
                            borderSide: BorderSide(color: Color(0xfff4f4f4)),
                          ),
                          focusedBorder: const UnderlineInputBorder(
                            borderSide: BorderSide(color: Color(0xfff4f4f4)),
                          ),
                        ),
                        enabled: !inputCodeShow,
                        controller: _phoneController,
                        keyboardType: TextInputType.number,
                      ),
                      const SizedBox(height: 20),
                      if (_showPrivacy)
                        Row(
                          children: <Widget>[
                            GestureDetector(
                                onTap: () {
                                  setState(() {
                                    _isAgree = !_isAgree;
                                  });
                                },
                                child: Container(
                                    height: 23,
                                    width: 23,
                                    child: Center(
                                      child: SizedBox(
                                        width: 13,
                                        height: 13,
                                        child: ImageView(
                                            _isAgree ? 'assets/images/login/login_page_icon_checkbox_select.png':
                                            'assets/images/login/login_page_icon_checkbox_normal.png',
                                            fit: BoxFit.contain
                                        ),
                                      ),
                                    )
                                )
                            ),
                            // const SizedBox(width: 5),
                            Expanded(
                              child: GestureDetector(
                                onTap: () {},
                                child: Text.rich(
                                  TextSpan(
                                    text: '我已阅读并同意',
                                    style: const TextStyle(
                                      color: Color(0xff7b7b7b),
                                      fontSize: 12,
                                    ),
                                    recognizer: TapGestureRecognizer()
                                      ..onTap = () {
                                        setState(() {
                                          _isAgree = !_isAgree;
                                        });
                                      },
                                    children: [
                                      TextSpan(
                                        text: '五菱汽车用户协议',
                                        style: const TextStyle(
                                            color: Colors.blue,
                                            decoration:
                                            TextDecoration.underline),
                                        recognizer: TapGestureRecognizer()
                                          ..onTap = () {
                                            jumpToPrivacy(1);
                                          },
                                      ),
                                      const TextSpan(text: '和'),
                                      TextSpan(
                                        text: '隐私政策',
                                        style: const TextStyle(
                                            color: Colors.blue,
                                            decoration:
                                            TextDecoration.underline),
                                        recognizer: TapGestureRecognizer()
                                          ..onTap = () {
                                            jumpToPrivacy(2);
                                          },
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      const SizedBox(height: 60),
                      if (!inputCodeShow)
                        Center(
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFFea0029), // 背景色
                              minimumSize: const Size(double.infinity, 50),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4), // 圆角半径
                              ),
                            ),
                            onPressed: _isLoginButtonEnabled && !isGettingCode
                                ? _getSmsCode
                                : null,
                            child: Text(_codeBtnTxt,
                                style: const TextStyle(
                                    color: Colors.white, fontSize: 16)),
                          ),
                        ),
                      if (inputCodeShow)
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            Center(
                              child: GestureDetector(
                                onTap: () {
                                  // 重新获取验证码的逻辑
                                  if (!isGettingCode && _isRetryBtnEnabled) {
                                    _getSmsCode();
                                  }
                                },
                                child: Text(
                                  _retryBtnText,
                                  style: const TextStyle(
                                      color: Color(0xff383a40), fontSize: 14),
                                ),
                              ),
                            ),
                            const SizedBox(height: 20),
                            GestureDetector(
                              onTap: () {
                                FocusScope.of(context)
                                    .requestFocus(_codeFocusNode);
                              },
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: List.generate(6, (index) {
                                  return Row(
                                    children: [
                                      Container(
                                        width: 28,
                                        height: 40,
                                        decoration: const BoxDecoration(
                                          border: Border(
                                              bottom: BorderSide(
                                                  width: 1,
                                                  color: Color(0xffb4b4b4))),
                                        ),
                                        alignment: Alignment.center,
                                        child: Text(
                                          _inputChars[index],
                                          style: const TextStyle(
                                              fontSize: 24,
                                              color: Color(0xff383a40)),
                                        ),
                                      ),
                                      if (index < 5)
                                        const SizedBox(
                                            width: 12), // 每个容器之间增加一个 SizedBox
                                    ],
                                  );
                                }),
                              ),
                            ),
                            //设置一个隐藏的输入框
                            Opacity(
                              opacity: 0,
                              child: SizedBox(
                                width: 0,
                                height: 0,
                                child: TextField(
                                  focusNode: _codeFocusNode,
                                  controller: _codeController,
                                  maxLength: 6,
                                  keyboardType: TextInputType.number,
                                  decoration: const InputDecoration(
                                    border: InputBorder.none,
                                    counterText: '',
                                  ),
                                  cursorColor: Colors.transparent,
                                ),
                              ),
                            ),
                          ],
                        ),
                    ]),
              )
            ],
          ),
        )
      ]),
    );
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _codeController.dispose();
    _codeFocusNode.dispose();
    _timer?.cancel();
    super.dispose();
  }
}