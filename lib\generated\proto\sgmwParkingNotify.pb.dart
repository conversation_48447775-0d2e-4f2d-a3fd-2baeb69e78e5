//
//  Generated code. Do not modify.
//  source: sgmwParkingNotify.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

class SgmwParkingNotify extends $pb.GeneratedMessage {
  factory SgmwParkingNotify({
    $fixnum.Int64? expireAtTimestamp,
    $core.String? vin,
    $core.String? msgId,
    $core.String? businessType,
  }) {
    final $result = create();
    if (expireAtTimestamp != null) {
      $result.expireAtTimestamp = expireAtTimestamp;
    }
    if (vin != null) {
      $result.vin = vin;
    }
    if (msgId != null) {
      $result.msgId = msgId;
    }
    if (businessType != null) {
      $result.businessType = businessType;
    }
    return $result;
  }
  SgmwParkingNotify._() : super();
  factory SgmwParkingNotify.fromBuffer($core.List<$core.int> i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(i, r);
  factory SgmwParkingNotify.fromJson($core.String i, [$pb.ExtensionRegistry r = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(i, r);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'SgmwParkingNotify', createEmptyInstance: create)
    ..aInt64(1, _omitFieldNames ? '' : 'expireAtTimestamp', protoName: 'expireAtTimestamp')
    ..aOS(2, _omitFieldNames ? '' : 'vin')
    ..aOS(3, _omitFieldNames ? '' : 'msgId', protoName: 'msgId')
    ..aOS(4, _omitFieldNames ? '' : 'businessType', protoName: 'businessType')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.deepCopy] instead. '
  'Will be removed in next major version')
  SgmwParkingNotify clone() => SgmwParkingNotify()..mergeFromMessage(this);
  @$core.Deprecated(
  'Using this can add significant overhead to your binary. '
  'Use [GeneratedMessageGenericExtensions.rebuild] instead. '
  'Will be removed in next major version')
  SgmwParkingNotify copyWith(void Function(SgmwParkingNotify) updates) => super.copyWith((message) => updates(message as SgmwParkingNotify)) as SgmwParkingNotify;

  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static SgmwParkingNotify create() => SgmwParkingNotify._();
  SgmwParkingNotify createEmptyInstance() => create();
  static $pb.PbList<SgmwParkingNotify> createRepeated() => $pb.PbList<SgmwParkingNotify>();
  @$core.pragma('dart2js:noInline')
  static SgmwParkingNotify getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<SgmwParkingNotify>(create);
  static SgmwParkingNotify? _defaultInstance;

  /// 消息过期时间0L，表示不过期，大于0 表示超过时间戳的数据可以不用处理。单位毫秒
  @$pb.TagNumber(1)
  $fixnum.Int64 get expireAtTimestamp => $_getI64(0);
  @$pb.TagNumber(1)
  set expireAtTimestamp($fixnum.Int64 v) { $_setInt64(0, v); }
  @$pb.TagNumber(1)
  $core.bool hasExpireAtTimestamp() => $_has(0);
  @$pb.TagNumber(1)
  void clearExpireAtTimestamp() => clearField(1);

  /// 消息所属vin
  @$pb.TagNumber(2)
  $core.String get vin => $_getSZ(1);
  @$pb.TagNumber(2)
  set vin($core.String v) { $_setString(1, v); }
  @$pb.TagNumber(2)
  $core.bool hasVin() => $_has(1);
  @$pb.TagNumber(2)
  void clearVin() => clearField(2);

  /// 消息id 可用于客户端去重消息
  @$pb.TagNumber(3)
  $core.String get msgId => $_getSZ(2);
  @$pb.TagNumber(3)
  set msgId($core.String v) { $_setString(2, v); }
  @$pb.TagNumber(3)
  $core.bool hasMsgId() => $_has(2);
  @$pb.TagNumber(3)
  void clearMsgId() => clearField(3);

  /// 业务类型
  @$pb.TagNumber(4)
  $core.String get businessType => $_getSZ(3);
  @$pb.TagNumber(4)
  set businessType($core.String v) { $_setString(3, v); }
  @$pb.TagNumber(4)
  $core.bool hasBusinessType() => $_has(3);
  @$pb.TagNumber(4)
  void clearBusinessType() => clearField(4);
}


const _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
