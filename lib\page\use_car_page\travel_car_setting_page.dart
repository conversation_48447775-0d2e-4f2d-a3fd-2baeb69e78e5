import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/api/car_api.dart';
import 'package:wuling_flutter_app/constant/cartype.dart';
import 'package:wuling_flutter_app/constant/web_view_url_tool.dart';
import 'package:wuling_flutter_app/models/car/car_info_model.dart';
import 'package:wuling_flutter_app/models/car/car_service_model.dart';
import 'package:wuling_flutter_app/models/car/car_service_response_model.dart';
import 'package:wuling_flutter_app/models/global_data.dart';
import 'package:wuling_flutter_app/models/user/user_model.dart';
import 'package:wuling_flutter_app/page/base_page/base_page.dart';
import 'package:wuling_flutter_app/page/use_car_page/car_authorization_page.dart';
import 'package:wuling_flutter_app/page/use_car_page/reset_safe_code_page.dart';
import 'package:wuling_flutter_app/routes/jump_tool.dart';
import 'package:wuling_flutter_app/utils/manager/dialog_manager.dart';
import 'package:wuling_flutter_app/utils/manager/loading_manager.dart';
import 'package:wuling_flutter_app/utils/manager/log_manager.dart';
import 'package:wuling_flutter_app/utils/str_util.dart';
import 'package:wuling_flutter_app/widgets/common/custom_dialog.dart';
import '../../constant/car_constant.dart';
import '../../constant/constant.dart';
import '../../models/car/car_service_param_model.dart';
import '../../routes/app_routes.dart';
import '../../widgets/use_car_page_widgets/car_control_setting_page/car_authorization_dialog.dart';
import 'banma_temperature_setting_page.dart';
import 'bluetooth_setting_for_HEV_page.dart';
import 'package:wuling_flutter_app/utils/sp_util.dart';

class TravelCarSettingPage extends BasePage {
  TravelCarSettingPage({super.key})
      : super(
          appBarTitle: '设置',
          initialStatusBarBrightness: Brightness.dark,
        );

  @override
  _TravelCarSettingPageState createState() => _TravelCarSettingPageState();
}

class _TravelCarSettingPageState extends BasePageState<TravelCarSettingPage> {
  late List<CarServiceModel> _datas = [];
  late bool mBanmaHasAuthIdentity = false;
  bool isVisibleButton = false;
  CarInfoModel? _carInfoModel;
  UserModel? _userModel; // 用户信息
  bool _isBluetoothAutoConnectEnabled = false;
  bool _bluetoothKeySettingFailure = false;

  @override
  void initState() {
    super.initState();
    _carInfoModel = GlobalData().carInfoModel;
    _userModel = GlobalData().userModel;
    if (_carInfoModel == null) {
      LoadingManager.showToast("获取车辆信息失败，请退出重试");
      return;
    }

    // 从本地存储加载蓝牙自动连接状态
    _isBluetoothAutoConnectEnabled = SpUtil().getString(BLUE_TOOTH_KEY_CONNECT_MARK) == '1';

    if (1 == _carInfoModel?.relation) {
      // 显示解绑按钮
      setState(() {
        isVisibleButton = true;
      });
    } else {
      setState(() {
        isVisibleButton = false;
      });
    }
    _queryCarMsg();
    if (_carInfoModel?.relation == 1) {
      // 该用户是斑马车车主，并且是未实名认证的时候，点击爱车授权的时候提示用户进行认证才能授权
      if (_carInfoModel?.isAuthIdentity == 0) {
        mBanmaHasAuthIdentity = false;
      } else {
        mBanmaHasAuthIdentity = true;
      }
    }
  }

  Widget _itemSettingCheckbox(CarServiceModel data) {
    return InkWell(
      onTap: () {
        LogManager().debug('Item tapped _itemSettingCheckbox!');
      },
      child: SizedBox(
        height: 50,
        child: Stack(
          children: [
            const Align(
              alignment: Alignment(-0.93, 0),
              child: Text(
                '蓝牙钥匙自动连接',
                style: TextStyle(fontSize: 17, color: Colors.black),
              ),
            ),
            Align(
              alignment: const Alignment(0.93, 0),
              child: Switch(
                value: _isBluetoothAutoConnectEnabled,
                inactiveTrackColor: Colors.grey,
                inactiveThumbColor: Colors.white,
                activeColor: Colors.green,
                onChanged: (bool value) {
                  if (!_bluetoothKeySettingFailure) {
                    LoadingManager.show(status: '设置中...');
                    _setBluetoothKeyAutoConnect(value);
                  } else {
                    _bluetoothKeySettingFailure = false; // 重置失败状态
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _itemSettingHev(CarServiceModel data) {
    return InkWell(
      onTap: () {
        // 这里添加你的点击事件逻辑
        _goToSettingItem(data, data.serviceCode, data.serviceName);
        LogManager().debug('Item tapped _itemSettingHev!');
      },
      child: SizedBox(
        height: 50,
        child: Stack(
          children: [
            Align(
              alignment: const Alignment(-0.93, 0),
              child: Text(
                data.serviceName,
                style: const TextStyle(fontSize: 17, color: Colors.black),
              ),
            ),
            const Align(
              alignment: Alignment(0.93, 0),
              child: Icon(
                Icons.arrow_forward_ios,
                size: 18,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget buildPageContent(BuildContext context) {
    return Stack(
      children: <Widget>[
        Container(
          color: const Color(0xFFF8F8F8),
          child: ListView.builder(
            itemCount: _datas.length,
            itemBuilder: (context, index) {
              CarServiceModel data = _datas[index];
              if (data.serviceCode == 'bluetoothKeyConnectMark') {
                return _itemSettingCheckbox(data);
              } else {
                return _itemSettingHev(data);
              }
            },
          ),
        ),
        Positioned(
          right: 100.0,
          bottom: 80.0,
          left: 100.0,
          child: Visibility(
            visible: isVisibleButton,
            child: ElevatedButton(
              style: ButtonStyle(
                backgroundColor: MaterialStateProperty.all<Color>(
                    Colors.redAccent), // 设置背景颜色为蓝色
              ),
              child: const Text(
                "解除绑定",
                style: TextStyle(color: Colors.white),
              ),
              onPressed: () {
                _unblockBingCar();
              },
            ),
          ),
        ),
      ],
    );
  }

  _checkedChange(bool isChecked, String serviceCode, String serviceName) {
    if (serviceCode.isEmpty) {
      LoadingManager.showToast("code为空");
      return;
    }
    switch (serviceCode.trim()) {
      //蓝牙钥匙自动连接
      case "bluetoothKeyConnectMark":
        /*if (!bluetoothKeySettingFailure) {
          if (isChecked) {
            LogUtils.e("设置蓝牙自动连接为开");
            bluetoothKeySetting(checkBox, mCarInfo.getVin(),"1");
            SensorsUtils.sensorsClickBtn("点击" + mCarInfo.getCarTypeName() + "蓝牙自动连接按钮", "设置", "自动连接开");
          } else {
            LogUtils.e("设置蓝牙自动连接为关");
            bluetoothKeySetting(checkBox, mCarInfo.getVin(),"0");
            SensorsUtils.sensorsClickBtn("点击" + mCarInfo.getCarTypeName() + "蓝牙自动连接按钮", "设置", "自动连接关");
          }
        }else {
          bluetoothKeySettingFailure = false;//若是上次用户设置失败将状态重置，则不进行请求
        }*/
        break;
    }
  }

  _goToSettingItem(
      CarServiceModel carServiceModel, String serviceCode, String serviceName) {
    if (serviceCode.isEmpty) {
      LoadingManager.showToast("code为空");
      return;
    }
    //帖子详情的跳转统一由serviceSkipType=5来判断
    if (carServiceModel != null && carServiceModel.serviceSkipType == '5') {
      //跳转帖子详情
      //JumpPageUtil.goToPost(mContext, unifyControlBtnStat.getServiceSkipTarget());
      //LoadingManager.showToast('功能开发中 敬请期待');
      JumpTool().openWeb(context, carServiceModel.serviceSkipTarget, false);
      return;
    }
    switch (serviceCode.trim()) {
      case "HelpCenter":
        //帮助中心
        _onCarHelpClick(carServiceModel, serviceName);
        break;
      case "SecurityCode":
        //安全码
        _onCarSafeCodeClick(serviceName);
        break;
      case "BluetoothSettings":
        //蓝牙设置
        _onCarBluetoothClick(serviceName);
        break;
      case "CarLicensing":
        //爱车授权
        _onCarAuthorizationClick(serviceName);
        break;
      case "CarManagement":
        //爱车管理
        _onCarManageClick(carServiceModel, serviceName);
        break;
      case "ProblemFeedback":
        //问题反馈
        if (carServiceModel != null) {
          // carServiceModel.serviceSkipTarget = 'https://m.baojun.net/lingClub/bug-feedback';
          carServiceModel = _changeModel(
              carServiceModel, WebViewURLTool.problemFeedbackURL());
        }
        //跳转到H5页面是否需要显示原生标题栏的通用方法
        _goCommonWebCheckBar(carServiceModel);
        break;
      case "AirConditionSetting":
        //空调设置
        _onTemperatureSet(serviceName);
        break;

      //其他类型都按H5跳转（产品要求的）
      default:
        //跳转到H5页面是否需要显示原生标题栏的通用方法
        _goCommonWebCheckBar(carServiceModel);
        break;
    }
  }

  _goCommonWebCheckBar(CarServiceModel carServiceModel) {
    if (carServiceModel == null) {
      LogManager().log('跳转到H5页面是否需要显示原生标题栏的通用方法carServiceModel');
      return;
    }
    StringBuffer url = StringBuffer();
    url.write(carServiceModel.serviceSkipTarget);
    List<CarServiceParamModel> params = carServiceModel.serviceSkipParamList!;
    if (params.length > 0) {
      for (int i = 0; i < params.length; i++) {
        if (i == 0) {
          url.write("?");
        } else {
          url.write("&");
        }
        url = _subStringH5Url(url, params[i]);
      }
    }
    if (carServiceModel.serviceSkipType == '8') {
      JumpTool().openWeb(context, url.toString(), true);
    } else {
      //默认是没有原生标题栏的
      JumpTool().openWeb(context, url.toString(), false);
    }
  }

  StringBuffer _subStringH5Url(StringBuffer url, CarServiceParamModel params) {
    switch (params.paramType) {
      case "0":
        String p = '${params.paramName}=${params.paramValue}';
        url.write(p);
        break;
      case "1":
        String paramV = _getStringMap()[params.paramName].toString();
        String p = '${params.paramName}=$paramV';
        url.write(p);
        break;
    }
    return url;
  }

  Map<String, String> _getStringMap() {
    Map<String, String> stringMap = {};
    stringMap.clear();
    stringMap['mobile'] = _userModel!.mobile;
    stringMap['userId'] = _userModel!.userIdStr;
    stringMap['appId'] = 'llb';
    stringMap['vin'] = _carInfoModel!.vin!;
    return stringMap;
  }

  /**
   * 帮助
   */
  _onCarHelpClick(CarServiceModel carServiceModel, String serviceName) {
    if (GlobalData().isLogin) {
      String url = StrUtil.formatInt(
          "${Constant.WEB_VIEW_BASE_URL}my/helpCenter.html?carPlatform=%d",
          [1]);
      // String url = WebViewURLTool.iovCarHelpCenterURL(1);
      if (carServiceModel != null) {
        carServiceModel = _changeModel(carServiceModel, url);
      }
      //跳转到H5页面是否需要显示原生标题栏的通用方法
      _goCommonWebCheckBar(carServiceModel);
    }
  }

  CarServiceModel _changeModel(CarServiceModel carServiceModel, String url) {
    Map<String, dynamic> map = new Map();
    map = carServiceModel.toJson();
    map['serviceSkipTarget'] = url;
    CarServiceModel newCarServiceModel = CarServiceModel.fromJson(map);
    return newCarServiceModel;
  }

  /**
   * 车管理
   */
  _onCarManageClick(CarServiceModel carServiceModel, String serviceName) {
    if (GlobalData().isLogin) {
      if (carServiceModel != null) {
        //carServiceModel.serviceSkipTarget = 'https://m.baojun.net/lingClub/love-car/list';
        carServiceModel = _changeModel(
            carServiceModel, WebViewURLTool.carListURLString());
      }
      //跳转到H5页面是否需要显示原生标题栏的通用方法
      JumpTool().openWeb(context, carServiceModel.serviceSkipTarget, false);
    }
  }

  /**
   * 爱车授权
   */
  _onCarAuthorizationClick(String serviceName) {
    if (GlobalData().isLogin) {
      if (_carInfoModel?.controlView == CAR_TYPE_BANMA) {
        if (mBanmaHasAuthIdentity) {
          Navigator.of(context).push(CustomCupertinoPageRoute(
            builder: (context) => CarAuthorizationPage(
              carType: CAR_TYPE_BANMA,
            ),
            canSwipeBack: false, // 禁用手势返回
          ));
        } else {
          _showDialog(context);
        }
      } else {
        late int mCarType = -1;
        if (_carInfoModel?.providerCode == "botai") {
          if (_carInfoModel?.telematicsPlatform == 1) {
            mCarType = CAR_TYPE_SGMW_BOTAI;
          } else {
            if (_carInfoModel?.controlView == "0") {
              mCarType = CAR_TYPE_NO_CONTROL_FUNCTION;
            } else {
              mCarType = CAR_TYPE_INTERNET;
            }
          }
        } else {
          mCarType = CAR_TYPE_INTERNET;
        }
        Navigator.of(context).push(CustomCupertinoPageRoute(
          builder: (context) => CarAuthorizationPage(carType: mCarType),
          canSwipeBack: false, // 禁用手势返回
        ));
      }
    }
  }

  /**
   * 安全码
   */
  _onCarSafeCodeClick(String serviceName) {
    LoadingManager.showToast('功能开发中 敬请期待');
    return;
    if (GlobalData().isLogin) {
      if (_carInfoModel?.controlView == CAR_TYPE_BANMA) {
        //如果是斑马，则弹出6位安全码
        Navigator.of(context).push(CustomCupertinoPageRoute(
          builder: (context) => ResetSafeCodePage(
            vin: _carInfoModel!.vin,
            maxLength: 6,
            mFrom: 1,
          ),
          canSwipeBack: false, // 禁用手势返回
        ));
      } else {
        Navigator.of(context).push(CustomCupertinoPageRoute(
          builder: (context) => ResetSafeCodePage(
            vin: _carInfoModel!.vin,
          ),
          canSwipeBack: false, // 禁用手势返回
        ));
      }
    }
  }

  /**
   * 空调设置界面
   */
  _onTemperatureSet(String serviceName) {
    if (GlobalData().isLogin) {
      Navigator.of(context).push(CustomCupertinoPageRoute(
        builder: (context) => BanmaTemperatureSettingPage(),
        canSwipeBack: false, // 禁用手势返回
      ));
    }
  }

  /**
   * 蓝牙设置
   */
  _onCarBluetoothClick(String serviceName) {
    if (GlobalData().isLogin) {
      //IntentUtils.startActivity(mContext, BluetoothSettingForHEVActivity.class, mCarInfo);
      //_carInfoModel
      Navigator.of(context).push(CustomCupertinoPageRoute(
        builder: (context) =>
            BluetoothSettingForHEVPage(carInfoModel: _carInfoModel),
        canSwipeBack: false, // 禁用手势返回
      ));
    }
  }

  void _setBluetoothKeyAutoConnect(bool value) {
    String vin = _carInfoModel?.vin ?? '';
    if (vin.isEmpty) {
      LoadingManager.showError('车辆信息不完整，无法设置');
      return;
    }

    Map<String, dynamic> map = {
      'vin': vin,
      'settingName': 'BLUETOOTH_KEY_AUTO_CONNECT_SETTING',
      'settingVal': value ? '1' : '0'
    };

    carAPI.setBluetoothKeyAutoConnect(map).then((result) {
      LoadingManager.dismiss();

      if (result) {
        // 设置成功
        LogManager().log('设置蓝牙钥匙自动连接${value ? "开启" : "关闭"}成功');

        // 更新UI状态
        setState(() {
          _isBluetoothAutoConnectEnabled = value;
        });

        // 保存到本地存储
        SpUtil().setString(BLUE_TOOTH_KEY_CONNECT_MARK, value ? '1' : '0');
        // 显示成功提示
        LoadingManager.showSuccess('设置成功');
      } else {
        // 设置失败
        _bluetoothKeySettingFailure = true;

        // 重置UI状态
        setState(() {
          _isBluetoothAutoConnectEnabled = !value;
        });

        // 显示失败提示
        LoadingManager.showError('设置失败，请稍后重试');
      }
    }).catchError((error) {
      LoadingManager.dismiss();
      _bluetoothKeySettingFailure = true;

      // 重置UI状态
      setState(() {
        _isBluetoothAutoConnectEnabled = !value;
      });

      // 显示错误提示
      LoadingManager.showError('网络异常，请检查网络连接');
      LogManager().log('蓝牙钥匙自动连接设置异常: $error');
    });
  }

  _queryCarMsg() {
    //_carInfoModel?.relation == 1  自绑车   2 授权车
    LogManager().log('请求getCarServiceList');
    final List<String> servicePositionCodeList = ['service_setting'];
    carAPI
        .getCarServiceList(servicePositionCodeList)
        .then((List<CarServiceResponseModel> list) {
      setState(() {
        for (CarServiceResponseModel responseModel in list) {
          if (responseModel.positionCode == 'service_setting') {
            for (CarServiceModel modle in responseModel.serviceList!) {
              _datas.add(modle);
            }
          }
        }
      });
    }).onError((error, stackTrace) {
      LogManager().log('getCarServiceList ${error.toString()}');
    });
  }

  _unblockBingCar() {
    LogManager().debug("ElevatedButton Click");
    if (_carInfoModel?.controlView == CAR_TYPE_BANMA) {
      //斑马车不处理
    } else {
      DialogManager().showCustomDialog(
        CustomDialog(
          content: "是否确认解绑车辆？",
          contentFontSize: 18,
          contentColor: Colors.black,
          buttonHeight: 70,
          horizontalPadding: 20,
          buttons: [
            DialogButton(
                label: "我再想想",
                onPressed: () {},
                backgroundColor: Colors.white,
                textColor: const Color(0xFF9B9DA9)),
            DialogButton(
                label: "确定解绑",
                onPressed: () {
                  _unBindCarNet();
                },
                backgroundColor: Colors.white,
                textColor: const Color(0xffea0029)),
          ],
        ),
      );
    }
  }

  _unBindCarNet() {
    Map<String, dynamic> map = {
      "vin": _carInfoModel?.vin,
    };
    carAPI.getSgmwUnbindCar(map).then((value) {
      if (value) {
        Navigator.pop(context);
      }
    }).onError((error, stackTrace) {
      LogManager().log(error.toString());
    });
  }

  void _showDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return StatefulBuilder(builder: (context, state) {
          return CarAuthorizationDialog(
            title: "当前车辆未完成车主认证，需要完成车主认证才能使用车辆控制功能！",
            negativeText: "稍后再说",
            positiveText: "马上认证",
            isShowTitleDivi: false,
            onPositivePressEvent: () {
              LogManager().debug("马上认证");
              Navigator.pop(context);
              JumpTool().openWeb(
                  context,
                  'https://m.baojun.net/lingClub/authentication/upload-cert?vin=${_carInfoModel!.vin}',
                  false);
            },
            onCloseEvent: () {
              LogManager().debug("稍后再说");
              Navigator.pop(context);
            },
          );
        });
      },
    );
  }
}
