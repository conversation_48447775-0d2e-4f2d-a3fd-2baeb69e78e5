import { CameraProperties } from '../CameraProperties';
import camera from '@ohos.multimedia.camera';
export declare abstract class CameraFeature<T> {
    protected cameraProperties: CameraProperties;
    constructor(cameraProperties: CameraProperties);
    abstract getDebugName(): string;
    abstract getValue(): T;
    abstract setValue(value: T, captureSession?: camera.PhotoSession | camera.VideoSession): any;
    abstract checkIsSupported(): boolean;
}
