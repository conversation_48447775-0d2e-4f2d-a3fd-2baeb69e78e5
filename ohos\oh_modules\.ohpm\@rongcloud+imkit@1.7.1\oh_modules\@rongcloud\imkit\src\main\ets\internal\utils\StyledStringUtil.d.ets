// @keepTs
// @ts-nocheck
/**
 * Created on 2024/10/9
 * <AUTHOR>
 */
import { EntityType } from '@kit.NaturalLanguageKit';
export declare class StyledStringUtil {
    private static readonly MAX_LENGTH;
    private static readonly SPLIT_CHARS;
    /**
     * 按照 emoji 表情分割字符串，返回带类型的片段数组
     * @param content 原始文本
     * @returns 分割后的文本片段数组
     */
    private static splitByEmoji;
    /**
     * 分割文本
     * @param content 原始文本
     * @returns 分割后的文本数组
     */
    private static splitContent;
    /**
     * 高亮url和phone
     * @param content
     * @param color
     * @param click
     * @returns
     */
    static getLinkPhoneString(b353: MutableStyledString, c353?: Color, d353?: (item: string, type: EntityType, event: ClickEvent) => void): Promise<MutableStyledString>;
    /**
     * 高亮@信息
     * @param content
     * @param mentionMap
     * @param color
     * @param click
     * @returns
     */
    static getMentionString(p352: MutableStyledString, q352: Map<string, string>, r352?: Color, s352?: (userId: string, event: ClickEvent) => void): MutableStyledString;
    /**
     * 使用转义后的字符串构建RegExp。
     *
     * 注意："()"是RegExp内部关键字，需要转义.
     * @param content 字符串
     * @returns RegExp
     */
    static escapeRegExp(n352: string): RegExp;
}
