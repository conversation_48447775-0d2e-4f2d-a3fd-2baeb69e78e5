import  { convertPcmToAmr, convertAmrToPcm } from 'libamrconverter.so'

export async function nativeConvertPcmToAmr(pcmFilePath: string, amrFilePath: string): Promise<boolean> {
  const result = await convertPcmToAmr(pcmFilePath, amrFilePath)
  return result
}

export async function nativeConvertAmrToPcm(amrFilePath: string, pcmFilePath: string): Promise<boolean> {
  const result = await convertAmrToPcm(amrFilePath, pcmFilePath)
  return result
}