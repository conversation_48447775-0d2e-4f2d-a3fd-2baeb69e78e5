{"open_app_settings|open_app_settings|1.0.0|build/default/generated/profile/default/ModuleInfo.js": {"version": 3, "file": "ModuleInfo.ts", "sources": ["ohos/build/default/generated/profile/default/ModuleInfo.ts"], "names": [], "mappings": "AACA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EAAE,CAAA,CAAA;AACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EAAE,CAAA,CAAA;", "entry-package-info": "open_app_settings|1.0.0"}, "open_app_settings|open_app_settings|1.0.0|Index.ts": {"version": 3, "file": "Index.ets", "sourceRoot": "", "sources": ["ohos/Index.ets"], "names": [], "mappings": "OAeQ,EAAE,qBAAqB,EAAE;AACjC,eAAe,qBAAqB,CAAA", "entry-package-info": "open_app_settings|1.0.0"}, "open_app_settings|open_app_settings|1.0.0|src/main/ets/components/mainpage/OpenAppSettingsPlugin.ts": {"version": 3, "file": "OpenAppSettingsPlugin.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/mainpage/OpenAppSettingsPlugin.ets"], "names": [], "mappings": "OAeO,EAGa,aAAa,GAE9B;cALM,YAAY,EAAE,oBAAoB,EAAE,aAAa,EACxD,oBAAoB,EACpB,UAAU,EACV,iBAAiB,EACjB,YAAY;YAEP,OAAO;YACL,MAAM;YAAE,IAAI;AAErB,MAAM,GAAG,GAAG,uBAAuB,CAAA;AACnC;;GAEG;AACH,MAAM,OAAO,qBAAsB,YAAW,YAAY,EAAE,aAAa,EAAE,iBAAiB;IAE1F,oFAAoF;IACpF,GAAG;IACH,gGAAgG;IAChG,yDAAyD;IACzD,OAAO,CAAC,OAAO,EAAE,aAAa,GAAG,SAAS,GAAG,SAAS,CAAA;IACtD,OAAO,CAAC,OAAO,EAAE,OAAO,GAAG,SAAS,GAAG,SAAS,CAAA;IAChD,OAAO,CAAC,OAAO,EAAE,MAAM,CAAC,gBAAgB,GAAG,SAAS,GAAG,SAAS,CAAA;IAEhE,kBAAkB,CAAC,OAAO,EAAE,oBAAoB,GAAG,IAAI;QACrD,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC;QACxC,IAAI,CAAC,OAAO,GAAG,IAAI,aAAa,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,mBAAmB,CAAC,CAAC;QACpF,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC;IAED,mBAAmB,CAAC,OAAO,EAAE,oBAAoB,GAAG,IAAI;QACtD,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,qBAAqB,CAAC,CAAC;QACzC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,EAAE,CAAA;QACnC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC,OAAO,CAAA;IAC7C,CAAC;IAED,qCAAqC,CAAC,OAAO,EAAE,oBAAoB,GAAG,IAAI;QACxE,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,uCAAuC,CAAC,CAAC;QAC3D,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,EAAE,CAAA;IACrC,CAAC;IAED,qCAAqC,IAAI,IAAI;QAC3C,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,uCAAuC,CAAC,CAAC;IAC7D,CAAC;IAED,qBAAqB,IAAI,IAAI;QAC3B,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,uBAAuB,CAAC,CAAC;IAC7C,CAAC;IAED,kBAAkB,IAAI,MAAM;QAC1B,OAAO,GAAG,CAAA;IACZ,CAAC;IAED,YAAY,CAAC,GAAG,EAAE,MAAM,GAAI,IAAI;QAC9B,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,cAAc,CAAC,CAAC;QAClC,IAAI;YACF,IAAI,IAAI,EAAE,IAAI,GAAG;gBACf,UAAU,EAAE,0BAA0B;gBACtC,WAAW,EAAE,sCAAsC;gBACnD,GAAG,EAAE,GAAG;aACT,CAAA;YACD,IAAI,CAAC,OAAO,EAAE,YAAY,CAAC,IAAI,CAAC,CAAA;SACjC;QAAC,OAAO,CAAC,EAAE;YACV,mFAAmF;YACnF,IAAI,CAAC,eAAe,EAAE,CAAC;SACxB;IACH,CAAC;IAED,eAAe,IAAI,IAAI;QACrB,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC;QACrC,IAAI,IAAI,EAAE,IAAI,GAAG;YACf,UAAU,EAAE,0BAA0B;YACtC,WAAW,EAAE,sCAAsC;SACpD,CAAA;QACD,IAAI,CAAC,OAAO,EAAE,YAAY,CAAC,IAAI,CAAC,CAAA;IAClC,CAAC;IAED,YAAY,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,IAAI;QACxD,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,eAAe,GAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC/C,QAAQ,IAAI,CAAC,MAAM,EAAE;YACnB,KAAK,MAAM;gBACT,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,CAAA;gBAC/B,MAAK;YACP,KAAK,UAAU;gBACb,IAAI,CAAC,YAAY,CAAC,2BAA2B,CAAC,CAAA;gBAC9C,MAAK;YACP,KAAK,UAAU;gBACb,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAA;gBACrC,MAAK;YACP,KAAK,WAAW;gBACd,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAA;gBACpC,MAAK;YACP,KAAK,cAAc;gBACjB,IAAI,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAA;gBACzC,MAAK;YACP,KAAK,MAAM;gBACT,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,CAAA;gBAClC,MAAK;YACP,KAAK,SAAS;gBACZ,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAA;gBACrC,MAAK;YACP,KAAK,cAAc;gBACjB,IAAI,CAAC,YAAY,CAAC,gCAAgC,CAAC,CAAA;gBACnD,MAAK;YACP,KAAK,KAAK;gBACR,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,CAAA;gBACjC,MAAK;YACP,KAAK,OAAO;gBACV,IAAI,CAAC,YAAY,CAAC,iBAAiB,CAAC,CAAA;gBACpC,MAAK;YACP,KAAK,kBAAkB;gBACrB,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAA;gBACrC,MAAK;YACP,KAAK,sBAAsB;gBACzB,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,CAAA;gBAC5B,MAAK;YACP,KAAK,cAAc;gBACjB,IAAI,CAAC,eAAe,EAAE,CAAA;gBACtB,MAAK;YACP,KAAK,SAAS;gBACZ,IAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE;oBAC9B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAA;iBAC5C;gBACD,MAAK;YACP;gBACE,MAAK;SACR;IACH,CAAC;IAED,oBAAoB,CAAC,OAAO,EAAE,oBAAoB,GAAG,IAAI;QACvD,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,sBAAsB,CAAC,CAAC;QAC1C,IAAI,CAAC,OAAO,EAAE,oBAAoB,CAAC,IAAI,CAAC,CAAC;IAC3C,CAAC;CACF", "entry-package-info": "open_app_settings|1.0.0"}}