// @keepTs
// @ts-nocheck
import { UserDataProvider } from '../../user/provider/UserDataProvider';
import { UserDataService } from '../../user/UserDataService';
import HashSet from '@ohos.util.HashSet';
import { UserDataListener } from '../../user/listener/UserDataListener';
import { UserInfoModel } from '../../user/model/UserInfoModel';
import { GroupInfoModel } from '../../user/model/GroupInfoModel';
import { GroupMemberInfoModel } from '../../user/model/GroupMemberInfoModel';
export declare class InnerUserDataServiceImpl implements UserDataService {
    private provider;
    private memCache;
    private dbHelper;
    listeners: HashSet<UserDataListener>;
    private isAttachedUserInfoInMessage;
    private messageReceivedListener;
    onInit(): void;
    setUserDataProvider(n336: UserDataProvider): void;
    removeUserDataProvider(m336: UserDataProvider): void;
    getUserDataProvider(): UserDataProvider | null;
    addUserDataListener(l336: UserDataListener): void;
    removeUserDataListener(k336: UserDataListener): void;
    getUserInfo(g336: string): Promise<UserInfoModel | undefined>;
    getUserInfoFromCache(e336: string): UserInfoModel | undefined;
    getUserInfoFromLocal(c336: string): Promise<UserInfoModel | undefined>;
    getGroupInfo(y335: string): Promise<GroupInfoModel | undefined>;
    getGroupMemberInfo(t335: string, u335: string): Promise<GroupMemberInfoModel | undefined>;
    getGroupMemberInfoFromCache(r335: string, s335: string): GroupMemberInfoModel | undefined;
    getGroupMemberInfoFromLocal(o335: string, p335: string): Promise<GroupMemberInfoModel | undefined>;
    /**
     * 批量获取群成员信息
     * @param groupId
     * @returns
     */
    getGroupMemberInfos(k335: string): Promise<Array<GroupMemberInfoModel> | undefined>;
    updateUserInfo(h335: UserInfoModel): Promise<void>;
    updateGroupInfo(e335: GroupInfoModel): Promise<void>;
    updateGroupMemberInfo(b335: GroupMemberInfoModel): Promise<void>;
    setMemCacheEnable(a335: boolean): void;
    getMemCacheEnable(): boolean;
    /**
     * 设置当前用户信息。 如果开发者没有实现用户信息提供者，而是使用消息携带用户信息，需要使用这个方法设置当前用户的信息， 然后在{@link init()}之后调用{@link
     * RongIM.getInstance().userDataService.setMessageAttachedUserInfo(true)}，
     * 这样可以在每条消息中携带当前用户的信息，IMKit会在接收到消息的时候取出用户信息并刷新到界面上。
     *
     * @param userInfo 当前用户信息。
     */
    setCurrentUserInfo(z334?: UserInfoModel): void;
    getCurrentUserInfo(): Promise<UserInfoModel | undefined>;
    /**
     * 设置消息体内是否携带用户信息。
     *
     * @param isAttached 是否携带用户信息，true 携带，false 不携带。
     */
    setMessageAttachedUserInfo(x334: boolean): void;
    /**
     * 获取当前用户关于消息体内是否携带用户信息的配置
     *
     * @return 是否携带用户信息
     */
    getUserInfoAttachedState(): boolean;
    clear(): Promise<void>;
}
