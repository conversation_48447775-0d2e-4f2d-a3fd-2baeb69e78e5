import { SdkInnerError } from "../../../base/f1";
export declare namespace DriveJson {
    interface DrivingJsonParseResult {
        result?: Result;
        cars?: Cars;
        SDK_InnerError?: SdkInnerError;
    }
    interface Cars {
        option: Option;
        content: Content;
    }
    interface Content {
        routes?: Route[];
        steps?: Step[];
        stepts?: Stept[];
        traffics?: Traffic[];
        taxis?: string;
    }
    interface Route {
        desc: string;
        mrsl: string;
        legs: Leg[];
        traffic_condition: number;
        main_roads: string;
        toll: number;
        light_num: number;
        congestion_length: number;
        tab: string;
        whole_condition: WholeCondition;
    }
    interface Leg {
        distance: number;
        duration: number;
        stepis: Stepi[];
        duration_info: Duration;
        duration_wholeday: Duration;
    }
    interface Duration {
        infos: Info[];
        interval: number;
        timestamp: string;
    }
    interface Info {
        index: number;
        duration: number;
    }
    interface Stepi {
        n: number;
        s: number;
    }
    interface WholeCondition {
        type: number;
        length: number;
    }
    interface Step {
        direction: number;
        distance: number;
        instructions: string;
        turn: number;
        start_instructions: StartInstructions;
        end_instructions: string;
        spath: number[];
        road_level?: number;
        road_name: string;
    }
    enum StartInstructions {
        Empty = ""
    }
    interface Stept {
        end: number[];
        status: number[];
    }
    interface Traffic {
        digest: string;
    }
    interface Option {
        avoid_jam?: number;
        total?: number;
        start?: Start;
        end?: Start[];
        sy?: number;
        prefer?: number;
    }
    interface Start {
        bus_stop?: number;
        spt?: number[];
        wd?: string;
        uid?: string;
    }
    interface Result {
        type: number;
        error: number;
    }
}
