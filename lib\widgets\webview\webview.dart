import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/services.dart';
import 'package:flutter_uikit/ui_overlay.dart';
import 'package:ssl_checker/ssl_checker.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:wuling_flutter_app/common/action.dart';
import 'package:wuling_flutter_app/plugins/share_plugin/share_plugin.dart';
import 'package:wuling_flutter_app/widgets/webview/web_view_js_channel.dart';
import 'package:wuling_flutter_app/widgets/webview/web_view_js_method.dart';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';
import 'package:webview_flutter_ohos/webview_flutter_ohos.dart';
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';
import 'package:wuling_flutter_app/utils/manager/log_manager.dart';
import '../../models/global_data.dart';
import '../../routes/jump_tool.dart';
import '../../utils/str_util.dart';
import 'package:wuling_flutter_app/utils/manager/phone_call_manager.dart';

class WebViewPage extends StatefulWidget {
  final String url;
  late final String html;
  late final bool needTitleBar;
  late final String titleName;
  late final bool horizontalDrag;
  WebViewPage({super.key, required this.url, html, needTitleBar, titleName,horizontalDrag}) {
    this.html = StrUtil.isNotEmpty(html) ? html! : "";
    this.needTitleBar = needTitleBar ?? false;
    this.titleName = StrUtil.isNotEmpty(titleName) ? titleName : "";
    this.horizontalDrag = horizontalDrag ?? false;
  }

  @override
  State<StatefulWidget> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> {
  late final PlatformWebViewControllerCreationParams params;
  late WebViewController controller;

  double lineProgress = 0.0;
  String title = "";

  //配置WebView支持的Js方法
  @override
  void initState() {
    super.initState();
    OverlayCreate.context = context;
    //初始化Controller-区分不同平台的特定处理。
    initController();
    //设置网页代理方法（加载进度，网络请求等）
    setNavigationDelegate();

    try {
      sslChecker(widget.url);
    } catch (e) {
      controller.goBack();
      NavigatorAction.init(context);
      /* ShowAction.init(context).showPrompt(msg: "此网页不支持再app内查看").then((value) {
        NavigatorAction.init(context);
      });*/
    }

    //设置原生方法调用监听
    WebViewJsChannel().injectData(context, controller);
    //加载网页
    if (widget.html.isNotEmpty) {
      controller.loadHtmlString(widget.html);
    } else {
      controller.loadRequest(Uri.parse(widget.url));
    }
  }

  void onData(String userAgent) {
    if (userAgent.isEmpty) {
      controller.setUserAgent(
          'Mozilla/5.0 (Phone; OpenHarmony 5.0) AppleWebKit/537.36 (KHTML, like Gecko) '
          'Chrome/114.0.0.0 Safari/537.36  ArkWeb/4.1.6.1 Mobile Android LingLingBang');
    } else {
      controller.setUserAgent(
          "$userAgent Android LingLingBang ${Platform.operatingSystem}");
    }
  }

  void initController() async {
    if (WebViewPlatform.instance is WebKitWebViewPlatform) {
      params = WebKitWebViewControllerCreationParams(
        allowsInlineMediaPlayback: true,
        mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
      );
    } else if (WebViewPlatform.instance is OhosWebViewPlatform) {
      params = OhosWebViewControllerCreationParams();
    } else {
      params = const PlatformWebViewControllerCreationParams();
    }
    controller = WebViewController.fromPlatformCreationParams(params,
        onPermissionRequest: (WebViewPermissionRequest request){
      request.grant();
    })
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000));

    if (controller.platform is AndroidWebViewController) {
      AndroidWebViewController.enableDebugging(true);
      (controller.platform as AndroidWebViewController)
          .setMediaPlaybackRequiresUserGesture(false);
    }

    if (controller.platform is OhosWebViewController) {
      OhosWebViewController.enableDebugging(true);
    }

    try {
      controller.getUserAgent().then((userAgent) =>
          {LogManager().debug("我的UserAgent:$userAgent"), onData(userAgent.toString())});
    } catch (e) {
      LogManager().info(e.toString());
    }
  }

  passUrl(String url) {
    LogManager().debug("H5地址url: $url");
    final uri = Uri.parse(url);
    final scheme = uri.scheme; // 获取URL的scheme
    LogManager().debug(scheme);
    LogManager().debug(uri.path + "?" + uri.query);
    if (url.contains("baojun.net") ||
        url.contains("baojunev.com") ||
        url.contains("00bang.cn") ||
        url.contains("intonecc.com") ||
        url.contains("sgmw") ||
        url.contains("wuling") ||
        url.contains("data:image/png") ||
        url.contains("weixin://dl") ||
        url.contains("lingclub.com") ||
        url.contains("https://jxi-fuli-m.jd.com") ||
        url.contains("http://pateokwb.mno.jrak.cn") ||
        url.contains("https://zhihuan.autobole.com/") ||
        url.contains("https://ling.eintone.com/") ||
        url.contains("jd.com/") ||
        scheme == 'tel' ||
        url.contains("huawei.com")) {
      if (scheme == 'weixin') {
        SharePlugin.openWX(url).then((value) {
          NavigatorAction.init(context);
        });
        // String? userIdStr = GlobalData().userModel?.userIdStr;
        // // 处理weixin: scheme
        // SharePlugin.openMini({
        //   "path": "${uri.path}?${uri.query}&shareUserIdStr=$userIdStr",
        //   "userName": "gh_bd7a2c3f3483"
        // }).then((value) {
        //   NavigatorAction.init(context);
        // });
      }
    } else {
      controller.goBack();
      NavigatorAction.init(context);
      /* ShowAction.init(context).showPrompt(msg: "此网页不支持在app内查看").then((value) {
        NavigatorAction.init(context);
      });*/
    }
  }

  void setNavigationDelegate() {
    controller.setNavigationDelegate(NavigationDelegate(
        onNavigationRequest: (NavigationRequest request) {
          passUrl(request.url);
          final uri = Uri.parse(request.url);
          final scheme = uri.scheme; // 获取URL的scheme

          if (scheme == 'weixin') {
            // 处理weixin: scheme
            SharePlugin.openWX(request.url).then((value) {
              NavigatorAction.init(context);
            });
          } else if (scheme == 'tel') {
            // 处理tel: scheme
            // 提取电话号码
            String phoneNumber =
                uri.path; // 直接获取电话号码，例如：tel:1234567890 中 path 是 '1234567890'
            if (phoneNumber.isNotEmpty) {
              Uri launchUri = Uri(
                scheme: 'tel',
                path: phoneNumber,
              );
              launchUrl(launchUri);
            }
            return NavigationDecision.prevent; // 阻止WebView内的导航
          }
          if (uri.path == '/h5/goods/detail') {
            String commodityId = uri.queryParameters['commodityId'] ?? '';
            JumpTool().jumpToMerchandiseDetailPage(
                context: context, commodityId: int.parse(commodityId));
            return NavigationDecision.prevent;
          }
          return NavigationDecision.navigate; // 允许导航
        },
        onPageStarted: (url) {
          passUrl(url);
          // js方法注入调整到onPageStarted开始进行，避免调用落后问题
          controller.runJavaScript(WebViewJsMethod().javaScript());
        },
        onProgress: (progress) => {
              //加载进度条
              if (mounted)
                setState(() {
                  lineProgress = progress / 100;
                })
            },
        onPageFinished: (url) {
          if (mounted) setTitle();
          // controller.runJavaScript(WebViewJsMethod().javaScript());
          //设置登录信息
          WebViewJsMethod().messageToken(controller);
        }));
  }

  //设置web标题
  void setTitle() async {
    if (StrUtil.isNotEmpty(widget.titleName)) {
      title = widget.titleName;
    } else {
      String loadTitle = await controller.getTitle() ?? "";
      setState(() {
        title = loadTitle;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    //设置状态栏
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      statusBarBrightness: Brightness.dark,
    ));

    return WillPopScope(
      onWillPop: () async {
        if (await controller.canGoBack()) {
          await controller.goBack();
          return false;
        } else {
          return true;
        }
      },
      child: Container(
        color: Colors.white,
        child: SafeArea(
          child: Scaffold(
            appBar: widget.needTitleBar
                ? AppBar(
                    title: Text(title),
                    centerTitle: true,
                    toolbarHeight: 45,
                  )
                : null,
            body: Column(children: [
              _progressBar(lineProgress),
              Expanded(
                  child: WebViewWidget(
                controller: controller,
                gestureRecognizers: {
                  Factory<VerticalDragGestureRecognizer>(
                    () => VerticalDragGestureRecognizer(),
                  ),
                  if (widget.horizontalDrag) Factory<HorizontalDragGestureRecognizer>(
                          () => HorizontalDragGestureRecognizer()
                  )
                },
              ))
            ]),
          ),
        ),
      ),
    );
  }

  _progressBar(double progress) {
    return SizedBox(
      height: 1,
      child: LinearProgressIndicator(
        backgroundColor: Colors.blueAccent.withOpacity(0),
        value: progress == 1.0 ? 0 : progress,
        valueColor: const AlwaysStoppedAnimation<Color>(Colors.lightBlue),
      ),
    );
  }
}
