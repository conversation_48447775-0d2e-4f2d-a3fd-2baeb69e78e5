import { FlutterPlugin, Flutter<PERSON>lugin<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, AbilityPluginBinding } from '@ohos/flutter_ohos';
import ble from "@ohos.bluetooth.ble";
import List from "@ohos.util.List";
import { Permissions as Permissions } from "@ohos.abilityAccessCtrl";
import access from '@ohos.bluetooth.access';
type byte = number;
/** FlutterBluePlusPlugin **/
export default class FlutterBluePlusPlugin implements FlutterPlugin, MethodCallHandler {
    readonly ENABLE_NOTIFICATION_VALUE: byte[];
    readonly ENABLE_INDICATION_VALUE: byte[];
    readonly DISABLE_NOTIFICATION_VALUE: byte[];
    private channel;
    private static TAG;
    private context;
    private pluginBinding;
    private abilityPluginBinding;
    private mConnectedDevices;
    private mCurrentlyConnectingDevices;
    private mBondingDevices;
    private mMtu;
    private mAutoConnected;
    private mWrite<PERSON>hr;
    private mWriteDesc;
    private operationsOnPermission;
    private lastEventId;
    private loglevel;
    private mIsScanning;
    private clientDevice;
    private remoteId;
    private mScanFilters;
    private mAdvSeen;
    private mScanCounts;
    FlutterBluePlusPlugin(): void;
    constructor();
    getUniqueClassName(): string;
    onAttachedToEngine(binding: FlutterPluginBinding): void;
    onDetachedFromEngine(binding: FlutterPluginBinding): void;
    onAttachedToAbility(binding: AbilityPluginBinding): void;
    onMethodCall(call: MethodCall, result: MethodResult): Promise<void>;
    bondStateChange(): void;
    CharacteristicChange(characteristicChangeReq: ble.BLECharacteristic): void;
    bmBondStateEnum(bs: number): number;
    descriptorReadServicePair(service: ble.GattService, services: ble.GattService[]): ServicePair;
    getMaxPayload(remoteId: string, writeType: number, allowLongWrite: boolean): number;
    onCharacteristicReceived(address: string, services: ble.GattService[], characteristic: ble.BLECharacteristic): void;
    readAndWriteServicePair(services: ble.GattService[], characteristic: ble.BLECharacteristic): ServicePair;
    bytesToHex(bytes: byte[]): string;
    typedArrayToBuffer(array: Uint8Array): ArrayBuffer;
    getDescriptorFromArray(uuid: string, array: Array<ble.BLEDescriptor>): ble.BLEDescriptor | null;
    getReadDescriptorFromArray(uuid: string, array: Array<ble.BLEDescriptor>): ble.BLEDescriptor | null;
    locateCharacteristic(serviceId: string, secondaryServiceId: string, characteristicId: string, gattServices: ble.GattService[]): Promise<ChrFound | undefined>;
    getServiceFromArray(uuid: string, array: Array<ble.GattService>): ble.GattService | null;
    getCharacteristicFromArray(uuid: string, array: Array<ble.BLECharacteristic>): ble.BLECharacteristic | null;
    uuid128(uuid: string): string;
    uuidStr(uuid: string): string;
    ConnectStateChanged: (state: ble.BLEConnectionChangeState) => void;
    waitIfBonding(): Promise<void>;
    delay(milliseconds: number): Promise<void>;
    bmConnectionStateEnum(cs: number): number;
    bmBluetoothDevice(deviceAddress: string, name?: string): Map<string, string>;
    ensurePermissions(permissions: Permissions[], result: MethodResult): Promise<void>;
    checkSelfPermission(permission: Permissions): Promise<boolean>;
    OnDiscoveredServices(gattServices: Array<ble.GattService>, remoteId: string): void;
    bmBluetoothService(service: ble.GattService, remoteId: string, gattServices?: Array<ble.GattService>): Map<string, object | string | number>;
    bmBluetoothCharacteristic(remoteId: string, characteristic: ble.BLECharacteristic, service: ble.GattService, gattServices?: Array<ble.GattService>): Map<string, object | string>;
    bmCharacteristicProperties(characteristic: ble.BLECharacteristic): Map<string, Object>;
    getServicePair(service: ble.GattService, gattServices?: Array<ble.GattService>): ServicePair;
    disconnectAllDevices(func: string): void;
    bmBluetoothDescriptor(remote_id: string, descriptor: ble.BLEDescriptor): Map<string, object | string>;
    scanCallback(data: Array<ble.ScanResult>): Promise<void>;
    bmAdapterStateEnum(as: number): number;
    isAdapterOn(): boolean;
    onReceiveEvent(data: access.BluetoothState): void;
    scanCountIncrement(remoteId: string): number;
    filterKeywords(keywords: List<string>, target: string): boolean;
}
declare class ChrFound {
    characteristic: ble.BLECharacteristic | null;
    error: string | null;
    constructor(characteristic: ble.BLECharacteristic | null, error: string | null);
}
declare class ServicePair {
    primary: string | null;
    secondary: string | null;
}
export {};
