import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter/src/rendering/sliver.dart';
import 'package:flutter/src/rendering/sliver_grid.dart';
import 'package:flutter_uikit/flutter_uikit.dart';
import 'package:wuling_flutter_app/common/action.dart';
import 'package:wuling_flutter_app/models/store/store_item_model.dart';
import 'package:wuling_flutter_app/page/store/store_all/store_all_page.dart';
import 'package:wuling_flutter_app/page/store/store_item.dart';

import '../../api/store/store_api.dart';
import '../../utils/manager/log_manager.dart';

class StoreProduct extends StatefulWidget {
  const StoreProduct({super.key});

  @override
  State<StoreProduct> createState() => _StoreProductState();
}

class _StoreProductState extends State<StoreProduct> {

  @override
  void initState() {
    super.initState();
    // storeApi.getStoreList([0]).then((value) {
    //   setState(() {
    //     dataSource = value;
    //   });
    //   print(dataSource);
    // });

  }

  List<StoreItemModel> dataSource = [];

  @override
  Widget build(BuildContext context) {
    return UICustomTableView(
      headerView:  UIScrollView(
        height: 335,
        child: (int p ,String str){
          return Container(
            decoration: const BoxDecoration(
                borderRadius: BorderRadius.only(bottomLeft: Radius.circular(5),bottomRight: Radius.circular(5))
            ),
            child: Stack(
              alignment: Alignment.bottomLeft,
              children: [
                UIImage(imgStr: str,radius: 5,fit: BoxFit.cover,),
                Container(
                  width: 350,
                  height: 58,
                  decoration: const BoxDecoration(
                    color: Color(0x66000000),
                    borderRadius: BorderRadius.only(bottomLeft: Radius.circular(5),bottomRight: Radius.circular(5))
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 15,bottom: 28),
                  child: UIText( data: '环保帆布手提袋',fontSize: 14,color: 0xFFFFFFFF,),
                )
              ],
            ),
          );
        },
        dataSource: [
          "https://oss.00bang.cn/image/LLB_OSSB7C93ED64C1B41FB9F4C07A3F511E89C.jpg",
          "https://oss.00bang.cn/image/LLB_OSSB7C93ED64C1B41FB9F4C07A3F511E89C.jpg",
          "https://oss.00bang.cn/image/LLB_OSSB7C93ED64C1B41FB9F4C07A3F511E89C.jpg",
          "https://oss.00bang.cn/image/LLB_OSSB7C93ED64C1B41FB9F4C07A3F511E89C.jpg",
          "https://oss.00bang.cn/image/LLB_OSSB7C93ED64C1B41FB9F4C07A3F511E89C.jpg",
          "https://oss.00bang.cn/image/LLB_OSSB7C93ED64C1B41FB9F4C07A3F511E89C.jpg",
        ],
      ),
      padding: const EdgeInsets.symmetric(horizontal: 20),
      groupHeader: (int g){
        return Padding(
          padding: const EdgeInsets.only(bottom: 10,top: 20),
          child: Row(
            children: [
              const Expanded(flex: 1,child: UIText(data: "热门商品",fontSize: 16,)),
              UIButton(onPressed: (){
                NavigatorAction.init(context,view: const StoreAllPage());
              },buttonImage: const Icon(Icons.chevron_right,color: Color(0xFF686B78),),buttonState: UIButtonState(
                buttonType: UIButtonType.right,
                title: "全部",
                color: 0xFF686B78,
                fontSize: 14,
                spacing: 0,
              ),)
            ],
          ),
        );
      },
      itemList: (int g){
        return SliverGrid(
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 15,
              mainAxisSpacing:15,
              childAspectRatio: 160/212,
            ),

            delegate: SliverChildBuilderDelegate(
                  (BuildContext context, int index) {
                return StoreItem(model: dataSource[index]);
              },childCount: dataSource.length,
            ));
      },
    );
  }
  GlobalKey listGlobalKey = GlobalKey();
}


class WaterSliverGridDelegate extends SliverGridDelegate{

  final int crossAxisCount;
  final double crossAxisSpacing;
  final double mainAxisSpacing;
  final double childAspectRatio;
  final GlobalKey? listGlobalKey;
  final double Function(int i)? itemLength;


  WaterSliverGridDelegate(
      {this.itemLength,this.listGlobalKey, this.mainAxisExtent, this.crossAxisCount = 1, this.crossAxisSpacing = 0, this.mainAxisSpacing = 0, this.childAspectRatio = 1.0});

  bool _debugAssertIsValid() {
    assert(crossAxisCount > 0);
    assert(mainAxisSpacing >= 0.0);
    assert(crossAxisSpacing >= 0.0);
    assert(childAspectRatio >= 0.0);
    return true;
  }

  @override
  SliverGridLayout getLayout(SliverConstraints constraints) {
    assert(_debugAssertIsValid());
    final double usableCrossAxisExtent = math.max(
      0.0,
      constraints.crossAxisExtent - crossAxisSpacing * (crossAxisCount - 1),
    );

    final double childCrossAxisExtent = usableCrossAxisExtent / crossAxisCount;
    final double childMainAxisExtent = childCrossAxisExtent / childAspectRatio;

    return WaterSliverGridLayout(
      crossAxisCount: crossAxisCount,
        mainAxisSpacing:mainAxisSpacing,
      itemLength: itemLength ?? (i)=>childMainAxisExtent,
      crossAxisStride: childCrossAxisExtent + crossAxisSpacing,
      childCrossAxisExtent: childCrossAxisExtent,
      lengths: [
      ],
    );
  }

  final double? mainAxisExtent;

  @override
  bool shouldRelayout(covariant WaterSliverGridDelegate oldDelegate) {
    return oldDelegate.crossAxisCount != crossAxisCount
        || oldDelegate.mainAxisSpacing != mainAxisSpacing
        || oldDelegate.crossAxisSpacing != crossAxisSpacing
        || oldDelegate.childAspectRatio != childAspectRatio
        || oldDelegate.mainAxisExtent != mainAxisExtent;
  }
  
}


class WaterSliverGridLayout extends SliverGridLayout{

  WaterSliverGridLayout({
    required this.mainAxisSpacing,
    required this.itemLength,
    required this.crossAxisCount,
    required this.crossAxisStride,
    required this.childCrossAxisExtent,
    required this.lengths,
  }) : assert(crossAxisCount != null && crossAxisCount > 0),
        assert(crossAxisStride != null && crossAxisStride >= 0),
        assert(childCrossAxisExtent != null && childCrossAxisExtent >= 0);

  final double Function(int i) itemLength;

  final double mainAxisSpacing;

  final int crossAxisCount;

  final double crossAxisStride;

  final double childCrossAxisExtent;

  final List<double> lengths;


  @override
  int getMinChildIndexForScrollOffset(double scrollOffset) {
    return 0;
  }

  @override
  int getMaxChildIndexForScrollOffset(double scrollOffset) {
    cacheLengths.clear();
    for (var element in lengths) {
      WaterLowModel model = setLowScrollOffset(element);
      cacheLengths.add(model.low);
    }
    cacheLengths.removeRange(0,2);
    cacheLengths.addAll(crossLengths);
    int c = cacheLengths.lastIndexWhere((element) => element<scrollOffset);

    crossLengths.clear();
    return math.max(0, c);
  }

  @override
  SliverGridGeometry getGeometryForChildIndex(int index) {
    WaterLowModel model = setLowScrollOffset(lengths[index]);
    double crossAxisStart = model.index * crossAxisStride;
    return SliverGridGeometry(
      scrollOffset: model.low ,
      crossAxisOffset: crossAxisStart,
      mainAxisExtent: lengths[index],
      crossAxisExtent: childCrossAxisExtent,
    );
  }

  @override
  double computeMaxScrollOffset(int childCount) {
    assert(childCount != null);
    crossLengths.clear();
    for (var element in lengths) {
      setLowScrollOffset(element);
    }
    LogManager().debug('${crossLengths.last}');
    return crossLengths.last + mainAxisSpacing*lengths.length/2;
  }

  double maxScrollOffset = 0.0;

  List<double> crossLengths = [];

  List<double> cacheLengths = [];

  double getMaxScrollOffset(){
    List<double> lens = crossLengths;
    return lens.reduce(math.max);
  }

  WaterLowModel setLowScrollOffset(double len){
    if(crossLengths.length<crossAxisCount){
      crossLengths.add(len + mainAxisSpacing);
      return WaterLowModel(crossLengths.length-1,0.0);
    }else{
      List<double> lens = crossLengths;
      double lowLen = lens.reduce(math.min);
      int count = crossLengths.indexOf(lowLen);
      crossLengths.fillRange(count,count+1, lowLen+len+mainAxisSpacing);
      return WaterLowModel(count,lowLen);
    }
  }
}

class WaterLowModel{
  final double low;
  final int index;

  WaterLowModel(this.index,this.low, );
}
