import aio_crashsdk from '@acpm/aio_crashsdk';
import aio_util from '@acpm/aio_util';
export class ACPM {
    static setup(h) {
        aio_crashsdk.setup(h);
        aio_util.setup(h);
    }
    static getFunctionsBinder() {
        const a = new Map();
        aio_crashsdk.getFunctionsBinder().forEach((f, g) => { a.set(g, f); });
        aio_util.getFunctionsBinder().forEach((d, e) => { a.set(e, d); });
        return a;
    }
}
