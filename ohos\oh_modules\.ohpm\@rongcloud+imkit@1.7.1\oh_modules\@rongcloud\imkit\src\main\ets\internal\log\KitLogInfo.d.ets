// @keepTs
// @ts-nocheck
import { FwLogInfo } from '@rongcloud/imlib/src/main/ets/engine/internal/log/FwLog';
declare class KitLogInfo {
    private logInfo;
    private constructor();
    getLogInfo(): FwLogInfo;
    toString(): string;
    static log0(): KitLogInfo;
    static log1(g313: string, h313: Object): KitLogInfo;
    static log2(b313: string, c313: Object, d313: string, e313: Object): KitLogInfo;
    static log3(u312: string, v312: Object, w312: string, x312: Object, y312: string, z312: Object): KitLogInfo;
    static log4(l312: string, m312: Object, n312: string, o312: Object, p312: string, q312: Object, r312: string, s312: Object): KitLogInfo;
    static log5(a312: string, b312: Object, c312: string, d312: Object, e312: string, f312: Object, g312: string, h312: Object, i312: string, j312: Object): KitLogInfo;
    static log6(n311: string, o311: Object, p311: string, q311: Object, r311: string, s311: Object, t311: string, u311: Object, v311: string, w311: Object, x311: string, y311: Object): KitLogInfo;
    static log7(y310: string, z310: Object, a311: string, b311: Object, c311: string, d311: Object, e311: string, f311: Object, g311: string, h311: Object, i311: string, j311: Object, k311: string, l311: Object): KitLogInfo;
    static log8(h310: string, i310: Object, j310: string, k310: Object, l310: string, m310: Object, n310: string, o310: Object, p310: string, q310: Object, r310: string, s310: Object, t310: string, u310: Object, v310: string, w310: Object): KitLogInfo;
    static log9(o309: string, p309: Object, q309: string, r309: Object, s309: string, t309: Object, u309: string, v309: Object, w309: string, x309: Object, y309: string, z309: Object, a310: string, b310: Object, c310: string, d310: Object, e310: string, f310: Object): KitLogInfo;
    static logN(m309: Map<string, Object>): KitLogInfo;
}
export { KitLogInfo };
