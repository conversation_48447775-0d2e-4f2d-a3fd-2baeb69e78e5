import { BmScaleMode } from "../d2"; import BmObject from "../u2"; import type B<PERSON><PERSON><PERSON><PERSON> from "./a4"; export default class BmRichView extends BmObject { mVisibility: number; mLocated: number; opacity: number; scaleX: number; scaleY: number; mDescription: string; mRootUI: BmBaseUI; constructor();         setView(q27: BmBaseUI): any; getView(): BmBaseUI;           findRichViewByName(name: string): this;           findRichViewByShell(p27: number): this;           findViewByName(name: string): BmBaseUI;           findViewByShell(o27: number): BmBaseUI;           setLocated(n27: number): any;             setOffsetX(offsetX: number, scaleMode?: BmScaleMode): any;             setOffsetY(offsetY: number, scaleMode?: BmScaleMode): any;           setVisibility(visibility: number): any;           setPBVisibility(m27: number): any;             setShowLevel(from: number, to: number): any;           setCollisionBehavior(l27: number): any;                 setCollisionPriority(priority: number): any;           setCollisionLineTagId(k27: number): any;       setCollisionBorder(left: number, top: number, right: number, bottom: number): any;                    setScale(scale: number): any;           setScaleX(scaleX: number): any;           setScaleY(scaleY: number): any;           setOpacity(opacity: number): any;           setDescription(description: string): void; getDescription(): string; setDrawFullscreenMaskFlag(flag: boolean): any; } 