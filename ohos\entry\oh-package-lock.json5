{"meta": {"stableOrder": true, "enableUnifiedLockfile": false}, "lockfileVersion": 3, "ATTENTION": "THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.", "specifiers": {"@acpm/acpm_ohos_pc@../oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=/oh_modules/premierlibrary/libs/acpm_ohos_pc.har": "@acpm/acpm_ohos_pc@../oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=/oh_modules/premierlibrary/libs/acpm_ohos_pc.har", "@acpm/aio_crashsdk@../oh_modules/.ohpm/@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=/oh_modules/@acpm/acpm_ohos_pc/hars/aio_crashsdk.har": "@acpm/aio_crashsdk@../oh_modules/.ohpm/@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=/oh_modules/@acpm/acpm_ohos_pc/hars/aio_crashsdk.har", "@acpm/aio_util@../oh_modules/.ohpm/@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=/oh_modules/@acpm/acpm_ohos_pc/hars/aio_util.har": "@acpm/aio_util@../oh_modules/.ohpm/@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=/oh_modules/@acpm/acpm_ohos_pc/hars/aio_util.har", "@bdmap/base@1.2.7": "@bdmap/base@1.2.7", "@bdmap/map@1.2.7": "@bdmap/map@1.2.7", "@bdmap/search@1.2.7": "@bdmap/search@1.2.7", "@bdmap/verify@1.0.2": "@bdmap/verify@1.0.2", "@ohos/flutter_ohos@../har/flutter.har": "@ohos/flutter_ohos@../har/flutter.har", "@pura/harmony-utils@1.2.4": "@pura/harmony-utils@1.2.4", "@tencent/wechat_open_sdk@1.0.6": "@tencent/wechat_open_sdk@1.0.14", "@tencent/wechat_open_sdk@^1.0.11": "@tencent/wechat_open_sdk@1.0.14", "@types/libbaidumapsdk_base_for_js_v1_0_0.so@../oh_modules/.ohpm/@bdmap+base@1.2.7/oh_modules/@bdmap/base/src/main/cpp/type": "@types/libbaidumapsdk_base_for_js_v1_0_0.so@../oh_modules/.ohpm/@bdmap+base@1.2.7/oh_modules/@bdmap/base/src/main/cpp/type", "@types/libbaidumapsdk_map_for_js_v1_0_0.so@../oh_modules/.ohpm/@bdmap+map@1.2.7/oh_modules/@bdmap/map/src/main/cpp/type": "@types/libbaidumapsdk_map_for_js_v1_0_0.so@../oh_modules/.ohpm/@bdmap+map@1.2.7/oh_modules/@bdmap/map/src/main/cpp/type", "camera_ohos@../har/camera_ohos.har": "camera_ohos@../har/camera_ohos.har", "class-transformer@^0.5.1": "class-transformer@0.5.1", "connectivity_plus@../har/connectivity_plus.har": "connectivity_plus@../har/connectivity_plus.har", "device_info_plus@../har/device_info_plus.har": "device_info_plus@../har/device_info_plus.har", "flutter_blue_plus@../har/flutter_blue_plus.har": "flutter_blue_plus@../har/flutter_blue_plus.har", "flutter_blue_plus_ohos@../har/flutter_blue_plus_ohos.har": "flutter_blue_plus_ohos@../har/flutter_blue_plus_ohos.har", "fluwx@../har/fluwx.har": "fluwx@../har/fluwx.har", "iamgeqr_flutter_plugin@../har/iamgeqr_flutter_plugin.har": "iamgeqr_flutter_plugin@../har/iamgeqr_flutter_plugin.har", "image_picker_ohos@../har/image_picker_ohos.har": "image_picker_ohos@../har/image_picker_ohos.har", "libpremierlibrary.so@../oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=/oh_modules/premierlibrary/src/main/cpp/types/libpremierlibrary": "libpremierlibrary.so@../oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=/oh_modules/premierlibrary/src/main/cpp/types/libpremierlibrary", "mobile_scanner@../har/mobile_scanner.har": "mobile_scanner@../har/mobile_scanner.har", "open_app_settings@../har/open_app_settings.har": "open_app_settings@../har/open_app_settings.har", "package_info_plus@../har/package_info_plus.har": "package_info_plus@../har/package_info_plus.har", "path_provider_ohos@../har/path_provider_ohos.har": "path_provider_ohos@../har/path_provider_ohos.har", "permission_handler_ohos@../har/permission_handler_ohos.har": "permission_handler_ohos@../har/permission_handler_ohos.har", "premierlibrary@libs/premierlibrary.har": "premierlibrary@libs/premierlibrary.har", "shared_preferences_ohos@../har/shared_preferences_ohos.har": "shared_preferences_ohos@../har/shared_preferences_ohos.har", "url_launcher_ohos@../har/url_launcher_ohos.har": "url_launcher_ohos@../har/url_launcher_ohos.har", "webview_flutter_ohos@../har/webview_flutter_ohos.har": "webview_flutter_ohos@../har/webview_flutter_ohos.har"}, "packages": {"@acpm/acpm_ohos_pc@../oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=/oh_modules/premierlibrary/libs/acpm_ohos_pc.har": {"name": "@acpm/acpm_ohos_pc", "version": "1.0.0", "resolved": "../oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=/oh_modules/premierlibrary/libs/acpm_ohos_pc.har", "registryType": "local", "dependencies": {"@acpm/aio_crashsdk": "file:./hars/aio_crashsdk.har", "@acpm/aio_util": "file:./hars/aio_util.har"}}, "@acpm/aio_crashsdk@../oh_modules/.ohpm/@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=/oh_modules/@acpm/acpm_ohos_pc/hars/aio_crashsdk.har": {"name": "@acpm/aio_crashsdk", "version": "1.20.0", "resolved": "../oh_modules/.ohpm/@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=/oh_modules/@acpm/acpm_ohos_pc/hars/aio_crashsdk.har", "registryType": "local"}, "@acpm/aio_util@../oh_modules/.ohpm/@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=/oh_modules/@acpm/acpm_ohos_pc/hars/aio_util.har": {"name": "@acpm/aio_util", "version": "2.1.0", "resolved": "../oh_modules/.ohpm/@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=/oh_modules/@acpm/acpm_ohos_pc/hars/aio_util.har", "registryType": "local"}, "@bdmap/base@1.2.7": {"name": "@bdmap/base", "version": "1.2.7", "integrity": "sha512-9NtZHtlzKGweVcYpHJ5RT5dMAjta+z3akqDQr2BHvxM5HI14/G7P6/Q5oNKAjXkM8KLg+p9jYisnCKLM9cdwWg==", "resolved": "https://ohpm.openharmony.cn/ohpm/@bdmap/base/-/base-1.2.7.har", "registryType": "ohpm", "dependencies": {"@types/libbaidumapsdk_base_for_js_v1_0_0.so": "file:./src/main/cpp/type", "@bdmap/verify": "1.0.2"}}, "@bdmap/map@1.2.7": {"name": "@bdmap/map", "version": "1.2.7", "integrity": "sha512-dxP3vn91ssbKb8shy0JoFSiYHFWyEHFFZXH1fM2z7ZNLu9GT5DeoHGw8bAehtwoEoencK4mmF5eOQ4v8D3b9ew==", "resolved": "https://ohpm.openharmony.cn/ohpm/@bdmap/map/-/map-1.2.7.har", "registryType": "ohpm", "dependencies": {"@types/libbaidumapsdk_map_for_js_v1_0_0.so": "file:./src/main/cpp/type", "@bdmap/base": "1.2.7"}}, "@bdmap/search@1.2.7": {"name": "@bdmap/search", "version": "1.2.7", "integrity": "sha512-aO5sQq1Sv93CQlZt3l0Xq37SZoJLaKJhr6pLQd/kH/zA7VTvZeVILl1N5loyXMvXR9tOFQWqxtY6Lm+B/7gK8Q==", "resolved": "https://ohpm.openharmony.cn/ohpm/@bdmap/search/-/search-1.2.7.har", "registryType": "ohpm", "dependencies": {"@bdmap/base": "1.2.7"}}, "@bdmap/verify@1.0.2": {"name": "@bdmap/verify", "version": "1.0.2", "integrity": "sha512-QyOqj/MmAoHuQZQCLAbFl7jYYVRSdqd2mLx4ajM1xI1ZFfZ0tkugf/K7LwdFwDDsCtv+U7bjGOYVUx38zBJrvQ==", "resolved": "https://ohpm.openharmony.cn/ohpm/@bdmap/verify/-/verify-1.0.2.har", "registryType": "ohpm"}, "@ohos/flutter_ohos@../har/flutter.har": {"name": "@ohos/flutter_ohos", "version": "1.0.0-a9e521ff88", "resolved": "../har/flutter.har", "registryType": "local"}, "@pura/harmony-utils@1.2.4": {"name": "@pura/harmony-utils", "version": "1.2.4", "integrity": "sha512-P17M+h5eonalgtEd3Xe+a85PPZilguRc8uoggEt9+AjrBdAasQddf6L/W7NRubSBCiDf4k2vnVRtgeQqKsWrmA==", "resolved": "https://ohpm.openharmony.cn/ohpm/@pura/harmony-utils/-/harmony-utils-1.2.4.har", "registryType": "ohpm", "dependencies": {"class-transformer": "^0.5.1"}}, "@tencent/wechat_open_sdk@1.0.14": {"name": "@tencent/wechat_open_sdk", "version": "1.0.14", "integrity": "sha512-qTA/XkKqBp5Bji6D4ePCnxdGpaDvzcqeI/vK6FRUwdBIlO9T/o0xuO1akCMCEB9c6qshIUAYqyRlHYmkRIkm4g==", "resolved": "https://ohpm.openharmony.cn/ohpm/@tencent/wechat_open_sdk/-/wechat_open_sdk-1.0.14.har", "registryType": "ohpm"}, "@types/libbaidumapsdk_base_for_js_v1_0_0.so@../oh_modules/.ohpm/@bdmap+base@1.2.7/oh_modules/@bdmap/base/src/main/cpp/type": {"name": "@types/libbaidumapsdk_base_for_js_v1_0_0.so", "version": "1.0.0", "resolved": "../oh_modules/.ohpm/@bdmap+base@1.2.7/oh_modules/@bdmap/base/src/main/cpp/type", "registryType": "local"}, "@types/libbaidumapsdk_map_for_js_v1_0_0.so@../oh_modules/.ohpm/@bdmap+map@1.2.7/oh_modules/@bdmap/map/src/main/cpp/type": {"name": "@types/libbaidumapsdk_map_for_js_v1_0_0.so", "version": "1.0.0", "resolved": "../oh_modules/.ohpm/@bdmap+map@1.2.7/oh_modules/@bdmap/map/src/main/cpp/type", "registryType": "local"}, "camera_ohos@../har/camera_ohos.har": {"name": "camera_ohos", "version": "1.0.0", "resolved": "../har/camera_ohos.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:libs/flutter.har"}}, "class-transformer@0.5.1": {"name": "class-transformer", "version": "0.5.1", "integrity": "sha512-SQa1Ws6hUbfC98vKGxZH3KFY0Y1lm5Zm0SY8XX9zbK7FJCyVEac3ATW0RIpwzW+oOfmHE5PMPufDG9hCfoEOMw==", "resolved": "https://ohpm.openharmony.cn/ohpm/class-transformer/-/class-transformer-0.5.1.tgz", "shasum": "24147d5dffd2a6cea930a3250a677addf96ab336", "registryType": "ohpm"}, "connectivity_plus@../har/connectivity_plus.har": {"name": "connectivity_plus", "version": "1.0.0", "resolved": "../har/connectivity_plus.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./libs/flutter_embedding.har"}}, "device_info_plus@../har/device_info_plus.har": {"name": "device_info_plus", "version": "1.0.0", "resolved": "../har/device_info_plus.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har"}}, "flutter_blue_plus@../har/flutter_blue_plus.har": {"name": "flutter_blue_plus", "version": "1.0.0", "resolved": "../har/flutter_blue_plus.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har"}}, "flutter_blue_plus_ohos@../har/flutter_blue_plus_ohos.har": {"name": "flutter_blue_plus_ohos", "version": "1.0.0", "resolved": "../har/flutter_blue_plus_ohos.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har"}}, "fluwx@../har/fluwx.har": {"name": "fluwx", "version": "1.0.0", "resolved": "../har/fluwx.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har", "@tencent/wechat_open_sdk": "1.0.6"}}, "iamgeqr_flutter_plugin@../har/iamgeqr_flutter_plugin.har": {"name": "iamgeqr_flutter_plugin", "version": "1.0.0", "resolved": "../har/iamgeqr_flutter_plugin.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har"}}, "image_picker_ohos@../har/image_picker_ohos.har": {"name": "image_picker_ohos", "version": "1.0.0", "resolved": "../har/image_picker_ohos.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:libs/flutter.har"}}, "libpremierlibrary.so@../oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=/oh_modules/premierlibrary/src/main/cpp/types/libpremierlibrary": {"name": "libpremierlibrary.so", "version": "0.0.0", "resolved": "../oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=/oh_modules/premierlibrary/src/main/cpp/types/libpremierlibrary", "registryType": "local"}, "mobile_scanner@../har/mobile_scanner.har": {"name": "mobile_scanner", "version": "1.0.0", "resolved": "../har/mobile_scanner.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:libs/flutter.har"}}, "open_app_settings@../har/open_app_settings.har": {"name": "open_app_settings", "version": "1.0.0", "resolved": "../har/open_app_settings.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har"}}, "package_info_plus@../har/package_info_plus.har": {"name": "package_info_plus", "version": "1.0.0", "resolved": "../har/package_info_plus.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./libs/flutter.har"}}, "path_provider_ohos@../har/path_provider_ohos.har": {"name": "path_provider_ohos", "version": "1.0.0", "resolved": "../har/path_provider_ohos.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:libs/flutter.har"}}, "permission_handler_ohos@../har/permission_handler_ohos.har": {"name": "permission_handler_ohos", "version": "1.0.0", "resolved": "../har/permission_handler_ohos.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har"}}, "premierlibrary@libs/premierlibrary.har": {"name": "premierlibrary", "version": "1.0.0", "resolved": "libs/premierlibrary.har", "registryType": "local", "dependencies": {"libpremierlibrary.so": "file:./src/main/cpp/types/libpremierlibrary", "@acpm/acpm_ohos_pc": "file:./libs/acpm_ohos_pc.har"}}, "shared_preferences_ohos@../har/shared_preferences_ohos.har": {"name": "shared_preferences_ohos", "version": "1.0.0", "resolved": "../har/shared_preferences_ohos.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:libs/flutter.har"}}, "url_launcher_ohos@../har/url_launcher_ohos.har": {"name": "url_launcher_ohos", "version": "1.0.0", "resolved": "../har/url_launcher_ohos.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har"}}, "webview_flutter_ohos@../har/webview_flutter_ohos.har": {"name": "webview_flutter_ohos", "version": "1.0.0", "resolved": "../har/webview_flutter_ohos.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:libs/flutter.har"}}}}