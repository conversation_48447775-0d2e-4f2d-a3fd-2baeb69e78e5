{"meta": {"stableOrder": true, "enableUnifiedLockfile": false}, "lockfileVersion": 3, "ATTENTION": "THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.", "specifiers": {"@acpm/acpm_ohos_pc@../oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=/oh_modules/premierlibrary/libs/acpm_ohos_pc.har": "@acpm/acpm_ohos_pc@../oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=/oh_modules/premierlibrary/libs/acpm_ohos_pc.har", "@acpm/aio_crashsdk@../oh_modules/.ohpm/@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=/oh_modules/@acpm/acpm_ohos_pc/hars/aio_crashsdk.har": "@acpm/aio_crashsdk@../oh_modules/.ohpm/@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=/oh_modules/@acpm/acpm_ohos_pc/hars/aio_crashsdk.har", "@acpm/aio_util@../oh_modules/.ohpm/@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=/oh_modules/@acpm/acpm_ohos_pc/hars/aio_util.har": "@acpm/aio_util@../oh_modules/.ohpm/@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=/oh_modules/@acpm/acpm_ohos_pc/hars/aio_util.har", "@bdmap/base@1.2.7": "@bdmap/base@1.2.7", "@bdmap/map@1.2.7": "@bdmap/map@1.2.7", "@bdmap/search@1.2.7": "@bdmap/search@1.2.7", "@bdmap/verify@1.0.2": "@bdmap/verify@1.0.2", "@ohos/flutter_ohos@../har/flutter.har": "@ohos/flutter_ohos@../har/flutter.har", "@pura/harmony-utils@1.2.4": "@pura/harmony-utils@1.2.4", "@tencent/wechat_open_sdk@1.0.6": "@tencent/wechat_open_sdk@1.0.14", "@tencent/wechat_open_sdk@^1.0.11": "@tencent/wechat_open_sdk@1.0.14", "@types/libbaidumapsdk_base_for_js_v1_0_0.so@../oh_modules/.ohpm/@bdmap+base@1.2.7/oh_modules/@bdmap/base/src/main/cpp/type": "@types/libbaidumapsdk_base_for_js_v1_0_0.so@../oh_modules/.ohpm/@bdmap+base@1.2.7/oh_modules/@bdmap/base/src/main/cpp/type", "@types/libbaidumapsdk_map_for_js_v1_0_0.so@../oh_modules/.ohpm/@bdmap+map@1.2.7/oh_modules/@bdmap/map/src/main/cpp/type": "@types/libbaidumapsdk_map_for_js_v1_0_0.so@../oh_modules/.ohpm/@bdmap+map@1.2.7/oh_modules/@bdmap/map/src/main/cpp/type", "camera_ohos@../../plugins/camera_ohos/ohos": "camera_ohos@../../plugins/camera_ohos/ohos", "class-transformer@^0.5.1": "class-transformer@0.5.1", "connectivity_plus@../../../../PUB/git/flutter_plus_plugins-8720286d1b4b664dbc1d87ba169125242d4f10fb/packages/connectivity_plus/connectivity_plus/ohos": "connectivity_plus@../../../../PUB/git/flutter_plus_plugins-8720286d1b4b664dbc1d87ba169125242d4f10fb/packages/connectivity_plus/connectivity_plus/ohos", "device_info_plus@../../../../PUB/git/flutter_plus_plugins-a1347adcca3a46346a6ddd127cebcec9970cad6c/packages/device_info_plus/device_info_plus/ohos": "device_info_plus@../../../../PUB/git/flutter_plus_plugins-a1347adcca3a46346a6ddd127cebcec9970cad6c/packages/device_info_plus/device_info_plus/ohos", "flutter_blue_plus@../../../../PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos": "flutter_blue_plus@../../../../PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos", "flutter_blue_plus_ohos@../har/flutter_blue_plus_ohos.har": "flutter_blue_plus_ohos@../har/flutter_blue_plus_ohos.har", "fluwx@../../../../PUB/git/fluwx-9fe4d9f1d3c05de5de18d25cc53852866d9c34d7/ohos": "fluwx@../../../../PUB/git/fluwx-9fe4d9f1d3c05de5de18d25cc53852866d9c34d7/ohos", "iamgeqr_flutter_plugin@../../../../PUB/git/flutter_image_scanner-b9f20a88ca1c738c700a15655b8fab4c305c4d32/ohos": "iamgeqr_flutter_plugin@../../../../PUB/git/flutter_image_scanner-b9f20a88ca1c738c700a15655b8fab4c305c4d32/ohos", "image_picker_ohos@../../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/image_picker/image_picker_ohos/ohos": "image_picker_ohos@../../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/image_picker/image_picker_ohos/ohos", "libpremierlibrary.so@../oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=/oh_modules/premierlibrary/src/main/cpp/types/libpremierlibrary": "libpremierlibrary.so@../oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=/oh_modules/premierlibrary/src/main/cpp/types/libpremierlibrary", "mobile_scanner@../../../../PUB/git/fluttertpc_mobile_scanner-a50b8f5e377524f79dd143f1297e82d257ad53b1/ohos": "mobile_scanner@../../../../PUB/git/fluttertpc_mobile_scanner-a50b8f5e377524f79dd143f1297e82d257ad53b1/ohos", "open_app_settings@../../../../PUB/git/fluttertpc_open_app_settings-038c0dd8ffd45bcf84691d62cfff957748cd493a/ohos": "open_app_settings@../../../../PUB/git/fluttertpc_open_app_settings-038c0dd8ffd45bcf84691d62cfff957748cd493a/ohos", "package_info_plus@../../../../PUB/git/flutter_plus_plugins-452bfb40465722552f31cf78cde60f99cc691e36/packages/package_info_plus/package_info_plus/ohos": "package_info_plus@../../../../PUB/git/flutter_plus_plugins-452bfb40465722552f31cf78cde60f99cc691e36/packages/package_info_plus/package_info_plus/ohos", "path_provider_ohos@../../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/path_provider/path_provider_ohos/ohos": "path_provider_ohos@../../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/path_provider/path_provider_ohos/ohos", "permission_handler_ohos@../../../../PUB/git/flutter_permission_handler-1e7c80f0720aff742d9f1e08017e1753bcad76ef/permission_handler_ohos/ohos": "permission_handler_ohos@../../../../PUB/git/flutter_permission_handler-1e7c80f0720aff742d9f1e08017e1753bcad76ef/permission_handler_ohos/ohos", "premierlibrary@libs/premierlibrary.har": "premierlibrary@libs/premierlibrary.har", "shared_preferences_ohos@../../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/shared_preferences/shared_preferences_ohos/ohos": "shared_preferences_ohos@../../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/shared_preferences/shared_preferences_ohos/ohos", "url_launcher_ohos@../../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/url_launcher/url_launcher_ohos/ohos": "url_launcher_ohos@../../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/url_launcher/url_launcher_ohos/ohos", "webview_flutter_ohos@../../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/webview_flutter/webview_flutter_ohos/ohos": "webview_flutter_ohos@../../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/webview_flutter/webview_flutter_ohos/ohos"}, "packages": {"@acpm/acpm_ohos_pc@../oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=/oh_modules/premierlibrary/libs/acpm_ohos_pc.har": {"name": "@acpm/acpm_ohos_pc", "version": "1.0.0", "resolved": "../oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=/oh_modules/premierlibrary/libs/acpm_ohos_pc.har", "registryType": "local", "dependencies": {"@acpm/aio_crashsdk": "file:./hars/aio_crashsdk.har", "@acpm/aio_util": "file:./hars/aio_util.har"}}, "@acpm/aio_crashsdk@../oh_modules/.ohpm/@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=/oh_modules/@acpm/acpm_ohos_pc/hars/aio_crashsdk.har": {"name": "@acpm/aio_crashsdk", "version": "1.20.0", "resolved": "../oh_modules/.ohpm/@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=/oh_modules/@acpm/acpm_ohos_pc/hars/aio_crashsdk.har", "registryType": "local"}, "@acpm/aio_util@../oh_modules/.ohpm/@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=/oh_modules/@acpm/acpm_ohos_pc/hars/aio_util.har": {"name": "@acpm/aio_util", "version": "2.1.0", "resolved": "../oh_modules/.ohpm/@acpm+acpm_ohos_pc@+pktmjv+7f4ylc6d+ktncclvop0azyjc6wjowggczh8=/oh_modules/@acpm/acpm_ohos_pc/hars/aio_util.har", "registryType": "local"}, "@bdmap/base@1.2.7": {"name": "@bdmap/base", "version": "1.2.7", "integrity": "sha512-9NtZHtlzKGweVcYpHJ5RT5dMAjta+z3akqDQr2BHvxM5HI14/G7P6/Q5oNKAjXkM8KLg+p9jYisnCKLM9cdwWg==", "resolved": "https://ohpm.openharmony.cn/ohpm/@bdmap/base/-/base-1.2.7.har", "registryType": "ohpm", "dependencies": {"@types/libbaidumapsdk_base_for_js_v1_0_0.so": "file:./src/main/cpp/type", "@bdmap/verify": "1.0.2"}}, "@bdmap/map@1.2.7": {"name": "@bdmap/map", "version": "1.2.7", "integrity": "sha512-dxP3vn91ssbKb8shy0JoFSiYHFWyEHFFZXH1fM2z7ZNLu9GT5DeoHGw8bAehtwoEoencK4mmF5eOQ4v8D3b9ew==", "resolved": "https://ohpm.openharmony.cn/ohpm/@bdmap/map/-/map-1.2.7.har", "registryType": "ohpm", "dependencies": {"@types/libbaidumapsdk_map_for_js_v1_0_0.so": "file:./src/main/cpp/type", "@bdmap/base": "1.2.7"}}, "@bdmap/search@1.2.7": {"name": "@bdmap/search", "version": "1.2.7", "integrity": "sha512-aO5sQq1Sv93CQlZt3l0Xq37SZoJLaKJhr6pLQd/kH/zA7VTvZeVILl1N5loyXMvXR9tOFQWqxtY6Lm+B/7gK8Q==", "resolved": "https://ohpm.openharmony.cn/ohpm/@bdmap/search/-/search-1.2.7.har", "registryType": "ohpm", "dependencies": {"@bdmap/base": "1.2.7"}}, "@bdmap/verify@1.0.2": {"name": "@bdmap/verify", "version": "1.0.2", "integrity": "sha512-QyOqj/MmAoHuQZQCLAbFl7jYYVRSdqd2mLx4ajM1xI1ZFfZ0tkugf/K7LwdFwDDsCtv+U7bjGOYVUx38zBJrvQ==", "resolved": "https://ohpm.openharmony.cn/ohpm/@bdmap/verify/-/verify-1.0.2.har", "registryType": "ohpm"}, "@ohos/flutter_ohos@../har/flutter.har": {"name": "@ohos/flutter_ohos", "version": "1.0.0-a9e521ff88", "resolved": "../har/flutter.har", "registryType": "local"}, "@pura/harmony-utils@1.2.4": {"name": "@pura/harmony-utils", "version": "1.2.4", "integrity": "sha512-P17M+h5eonalgtEd3Xe+a85PPZilguRc8uoggEt9+AjrBdAasQddf6L/W7NRubSBCiDf4k2vnVRtgeQqKsWrmA==", "resolved": "https://ohpm.openharmony.cn/ohpm/@pura/harmony-utils/-/harmony-utils-1.2.4.har", "registryType": "ohpm", "dependencies": {"class-transformer": "^0.5.1"}}, "@tencent/wechat_open_sdk@1.0.14": {"name": "@tencent/wechat_open_sdk", "version": "1.0.14", "integrity": "sha512-qTA/XkKqBp5Bji6D4ePCnxdGpaDvzcqeI/vK6FRUwdBIlO9T/o0xuO1akCMCEB9c6qshIUAYqyRlHYmkRIkm4g==", "resolved": "https://ohpm.openharmony.cn/ohpm/@tencent/wechat_open_sdk/-/wechat_open_sdk-1.0.14.har", "registryType": "ohpm"}, "@types/libbaidumapsdk_base_for_js_v1_0_0.so@../oh_modules/.ohpm/@bdmap+base@1.2.7/oh_modules/@bdmap/base/src/main/cpp/type": {"name": "@types/libbaidumapsdk_base_for_js_v1_0_0.so", "version": "1.0.0", "resolved": "../oh_modules/.ohpm/@bdmap+base@1.2.7/oh_modules/@bdmap/base/src/main/cpp/type", "registryType": "local"}, "@types/libbaidumapsdk_map_for_js_v1_0_0.so@../oh_modules/.ohpm/@bdmap+map@1.2.7/oh_modules/@bdmap/map/src/main/cpp/type": {"name": "@types/libbaidumapsdk_map_for_js_v1_0_0.so", "version": "1.0.0", "resolved": "../oh_modules/.ohpm/@bdmap+map@1.2.7/oh_modules/@bdmap/map/src/main/cpp/type", "registryType": "local"}, "camera_ohos@../../plugins/camera_ohos/ohos": {"name": "camera_ohos", "version": "1.0.0", "resolved": "../../plugins/camera_ohos/ohos", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:libs/flutter.har"}}, "class-transformer@0.5.1": {"name": "class-transformer", "version": "0.5.1", "integrity": "sha512-SQa1Ws6hUbfC98vKGxZH3KFY0Y1lm5Zm0SY8XX9zbK7FJCyVEac3ATW0RIpwzW+oOfmHE5PMPufDG9hCfoEOMw==", "resolved": "https://ohpm.openharmony.cn/ohpm/class-transformer/-/class-transformer-0.5.1.tgz", "shasum": "24147d5dffd2a6cea930a3250a677addf96ab336", "registryType": "ohpm"}, "connectivity_plus@../../../../PUB/git/flutter_plus_plugins-8720286d1b4b664dbc1d87ba169125242d4f10fb/packages/connectivity_plus/connectivity_plus/ohos": {"name": "connectivity_plus", "version": "1.0.0", "resolved": "../../../../PUB/git/flutter_plus_plugins-8720286d1b4b664dbc1d87ba169125242d4f10fb/packages/connectivity_plus/connectivity_plus/ohos", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./libs/flutter_embedding.har"}}, "device_info_plus@../../../../PUB/git/flutter_plus_plugins-a1347adcca3a46346a6ddd127cebcec9970cad6c/packages/device_info_plus/device_info_plus/ohos": {"name": "device_info_plus", "version": "1.0.0", "resolved": "../../../../PUB/git/flutter_plus_plugins-a1347adcca3a46346a6ddd127cebcec9970cad6c/packages/device_info_plus/device_info_plus/ohos", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har"}}, "flutter_blue_plus@../../../../PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos": {"name": "flutter_blue_plus", "version": "1.0.0", "resolved": "../../../../PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har"}}, "flutter_blue_plus_ohos@../har/flutter_blue_plus_ohos.har": {"name": "flutter_blue_plus_ohos", "version": "1.0.0", "resolved": "../har/flutter_blue_plus_ohos.har", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har"}}, "fluwx@../../../../PUB/git/fluwx-9fe4d9f1d3c05de5de18d25cc53852866d9c34d7/ohos": {"name": "fluwx", "version": "1.0.0", "resolved": "../../../../PUB/git/fluwx-9fe4d9f1d3c05de5de18d25cc53852866d9c34d7/ohos", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har", "@tencent/wechat_open_sdk": "1.0.6"}}, "iamgeqr_flutter_plugin@../../../../PUB/git/flutter_image_scanner-b9f20a88ca1c738c700a15655b8fab4c305c4d32/ohos": {"name": "iamgeqr_flutter_plugin", "version": "1.0.0", "resolved": "../../../../PUB/git/flutter_image_scanner-b9f20a88ca1c738c700a15655b8fab4c305c4d32/ohos", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har"}}, "image_picker_ohos@../../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/image_picker/image_picker_ohos/ohos": {"name": "image_picker_ohos", "version": "1.0.0", "resolved": "../../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/image_picker/image_picker_ohos/ohos", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:libs/flutter.har"}}, "libpremierlibrary.so@../oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=/oh_modules/premierlibrary/src/main/cpp/types/libpremierlibrary": {"name": "libpremierlibrary.so", "version": "0.0.0", "resolved": "../oh_modules/.ohpm/premierlibrary@vrgnefwlgcuq701ctp3o8q5vj+cpmg6earuuhuxr7jw=/oh_modules/premierlibrary/src/main/cpp/types/libpremierlibrary", "registryType": "local"}, "mobile_scanner@../../../../PUB/git/fluttertpc_mobile_scanner-a50b8f5e377524f79dd143f1297e82d257ad53b1/ohos": {"name": "mobile_scanner", "version": "1.0.0", "resolved": "../../../../PUB/git/fluttertpc_mobile_scanner-a50b8f5e377524f79dd143f1297e82d257ad53b1/ohos", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:libs/flutter.har"}}, "open_app_settings@../../../../PUB/git/fluttertpc_open_app_settings-038c0dd8ffd45bcf84691d62cfff957748cd493a/ohos": {"name": "open_app_settings", "version": "1.0.0", "resolved": "../../../../PUB/git/fluttertpc_open_app_settings-038c0dd8ffd45bcf84691d62cfff957748cd493a/ohos", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har"}}, "package_info_plus@../../../../PUB/git/flutter_plus_plugins-452bfb40465722552f31cf78cde60f99cc691e36/packages/package_info_plus/package_info_plus/ohos": {"name": "package_info_plus", "version": "1.0.0", "resolved": "../../../../PUB/git/flutter_plus_plugins-452bfb40465722552f31cf78cde60f99cc691e36/packages/package_info_plus/package_info_plus/ohos", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./libs/flutter.har"}}, "path_provider_ohos@../../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/path_provider/path_provider_ohos/ohos": {"name": "path_provider_ohos", "version": "1.0.0", "resolved": "../../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/path_provider/path_provider_ohos/ohos", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:libs/flutter.har"}}, "permission_handler_ohos@../../../../PUB/git/flutter_permission_handler-1e7c80f0720aff742d9f1e08017e1753bcad76ef/permission_handler_ohos/ohos": {"name": "permission_handler_ohos", "version": "1.0.0", "resolved": "../../../../PUB/git/flutter_permission_handler-1e7c80f0720aff742d9f1e08017e1753bcad76ef/permission_handler_ohos/ohos", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har"}}, "premierlibrary@libs/premierlibrary.har": {"name": "premierlibrary", "version": "1.0.0", "resolved": "libs/premierlibrary.har", "registryType": "local", "dependencies": {"libpremierlibrary.so": "file:./src/main/cpp/types/libpremierlibrary", "@acpm/acpm_ohos_pc": "file:./libs/acpm_ohos_pc.har"}}, "shared_preferences_ohos@../../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/shared_preferences/shared_preferences_ohos/ohos": {"name": "shared_preferences_ohos", "version": "1.0.0", "resolved": "../../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/shared_preferences/shared_preferences_ohos/ohos", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:libs/flutter.har"}}, "url_launcher_ohos@../../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/url_launcher/url_launcher_ohos/ohos": {"name": "url_launcher_ohos", "version": "1.0.0", "resolved": "../../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/url_launcher/url_launcher_ohos/ohos", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har"}}, "webview_flutter_ohos@../../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/webview_flutter/webview_flutter_ohos/ohos": {"name": "webview_flutter_ohos", "version": "1.0.0", "resolved": "../../../../PUB/git/flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c/packages/webview_flutter/webview_flutter_ohos/ohos", "registryType": "local", "dependencies": {"@ohos/flutter_ohos": "file:libs/flutter.har"}}}}