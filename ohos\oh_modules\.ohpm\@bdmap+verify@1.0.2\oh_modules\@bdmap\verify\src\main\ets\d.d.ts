export interface LBSAuthManagerListener { onAuthResult(state: number, result: String): void; }       export declare enum CodeDefine { SUCCESS = 0, NO_AK = 1, FAIL_AK = 2, INIT_MAP_ERROR = 3, NET_ERROR = 4 }       export declare class AuthResult { status: number; token: string; message: string; user_permission: string; ak_permission: string; }       export declare class LBSAuthManager { private static instance; private static tmpAppid; private static _key; private constructor(); static getInstance(): LBSAuthManager;         setkey(key: string): void;           authenticate(i: string, callback: any): Promise<void>;         getBundleAppId(): Promise<void>;         static jsonToQuery(json: any): string; } 