// @keepTs
// @ts-nocheck
/**
 * Created on 2024/07/15
 * <AUTHOR>
 */
import { UiMessage } from '../../../conversation/model/UiMessage';
import { ConversationComponentData } from '../../../conversation/model/ConversationComponentData';
type DataResult = (localData: UiMessage[], remoteData?: UiMessage[]) => void;
export interface IBusinessState {
    /**
     * 初始化，进入页面创建时
     */
    onInit(data: ConversationComponentData, localCallback?: DataResult): void;
    /**
     * 当接收到消息时
     * @param message 接受的消息
     * @param receivedInfo 接受的消息信息
     */
    /**
     * 当加载消息时，第一次加载消息，刷新时都会调用
     * @param messageArray array
     */
    /**
     * 加载更多消息
     */
    onLoadMore(data: ConversationComponentData, localCallback?: DataResult): void;
}
export {};
