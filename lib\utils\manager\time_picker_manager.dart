import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'dart:ui';
import 'package:wuling_flutter_app/widgets/common/custom_time_picker.dart';

import 'log_manager.dart';
class TimePickerManager {
  static void showCustomTimePicker({
    required BuildContext context,
    required TimePickerMode mode,
    Duration? initialDuration,  // 可选的初始 Duration
    String? initialTimeStr,  // 可选的初始时间字符串
    Duration? minDuration,
    Duration? maxDuration,
    String? leftTitle,
    required void Function(Duration selectedTime, String formattedTime) onTimeSelected,
  }) {
    // 如果同时传入了 initialDuration 和 initialTimeStr，以 initialDuration 为优先
    if (initialDuration == null && initialTimeStr != null) {
      initialDuration = _parseInitialTimeStr(initialTimeStr, mode);
    }

    // 如果 initialDuration 仍为 null，则使用当前时间
    initialDuration ??= DateTime.now().difference(DateTime(1970, 1, 1));

    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return _TimePickerSheet(
          mode: mode,
          initialDuration: initialDuration!,
          minDuration: minDuration,
          maxDuration: maxDuration,
          onTimeSelected: onTimeSelected,
          leftTitle: leftTitle ?? '选择时间',
        );
      },
    );
  }

  static Duration _parseInitialTimeStr(String? initialTimeStr, TimePickerMode mode) {
    if (initialTimeStr == null || initialTimeStr.isEmpty) {
      // 如果initialTimeStr为空，返回当前时间的Duration
      return DateTime.now().difference(DateTime(1970, 1, 1));
    }

    DateTime baseDateTime = DateTime(1970, 1, 1);
    DateFormat format;

    switch (mode) {
      case TimePickerMode.YMDHMS:
        format = DateFormat('yyyy-MM-dd HH:mm:ss');
        break;
      case TimePickerMode.YMDHM:
        format = DateFormat('yyyy-MM-dd HH:mm');
        break;
      case TimePickerMode.YMDH:
        format = DateFormat('yyyy-MM-dd HH');
        break;
      case TimePickerMode.MDHM:
        format = DateFormat('MM-dd HH:mm');
        break;
      case TimePickerMode.YMD:
        format = DateFormat('yyyy-MM-dd');
        break;
      case TimePickerMode.YM:
        format = DateFormat('yyyy-MM');
        break;
      case TimePickerMode.Y:
        format = DateFormat('yyyy');
        break;
      case TimePickerMode.MD:
        format = DateFormat('MM-dd');
        break;
      case TimePickerMode.HMS:
        format = DateFormat('HH:mm:ss');
        break;
      case TimePickerMode.HM:
        format = DateFormat('HH:mm');
        break;
      case TimePickerMode.MS:
        format = DateFormat('mm:ss');
        break;
      default:
        throw FormatException('Unsupported TimePickerMode');
    }

    DateTime parsedDateTime;
    try {
      parsedDateTime = format.parse(initialTimeStr);
    } catch (e) {
      // 如果解析失败，返回当前时间的Duration
      return DateTime.now().difference(baseDateTime);
    }

    return parsedDateTime.difference(baseDateTime);
  }
}

class _TimePickerSheet extends StatefulWidget {
  final TimePickerMode mode;
  final Duration initialDuration;
  final Duration? minDuration;
  final Duration? maxDuration;
  final String leftTitle;
  final void Function(Duration selectedTime, String formattedTime) onTimeSelected;

  const _TimePickerSheet({
    Key? key,
    required this.mode,
    required this.initialDuration,
    this.minDuration,
    this.maxDuration,
    required this.onTimeSelected,
    this.leftTitle = '选择时间'
  }) : super(key: key);

  @override
  __TimePickerSheetState createState() => __TimePickerSheetState();
}

class __TimePickerSheetState extends State<_TimePickerSheet> {
  late Duration _selectedDuration;
  late String _formattedTimeStr;

  @override
  void initState() {
    super.initState();
    DateTime baseDateTime = DateTime(1970, 1, 1, 0, 0, 0);
    // 计算初始时间、最小时间和最大时间
    DateTime initialDateTime = baseDateTime.add(widget.initialDuration);
    DateTime _minDateTime = widget.minDuration != null ? baseDateTime.add(widget.minDuration!) : DateTime(DateTime.now().year - 100);
    DateTime _maxDateTime = widget.maxDuration != null ? baseDateTime.add(widget.maxDuration!) : DateTime(DateTime.now().year + 100);
    // 调整initialDateTime，如果超出边界
    if (initialDateTime.isBefore(_minDateTime)) {
      initialDateTime = _minDateTime;
    }
    if (initialDateTime.isAfter(_maxDateTime)) {
      initialDateTime = _maxDateTime;
    }
    _selectedDuration = initialDateTime.difference(baseDateTime);
    _formattedTimeStr = _formatDuration(_selectedDuration, widget.mode);
  }

  @override
  Widget build(BuildContext context) {
    double buttonHeight = 30;
    double safeAreaBottom = MediaQueryData.fromWindow(window).padding.bottom;
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Padding(
          padding: EdgeInsets.only(top:10, left: 20, right: 20),
          child: Container(
            height: 30,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(widget.leftTitle, style: TextStyle(fontSize: 16),),
                ElevatedButton(
                  onPressed: (){
                    Navigator.of(context).pop(); // 确认选择并关闭弹窗
                    widget.onTimeSelected(_selectedDuration, _formattedTimeStr);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Color(0xffea0029),
                    padding: EdgeInsets.zero,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(buttonHeight / 2.0),
                    ),
                    elevation: 0,
                  ),
                  child: Text('确认',style: TextStyle(color: Colors.white),),
                ),
              ],
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 20.0,bottom: 20),
          child: CustomTimePicker(
            initialDuration: widget.initialDuration,
            minDuration: widget.minDuration,
            maxDuration: widget.maxDuration,
            mode: widget.mode,
            onDurationChanged: ({required Duration selectTime, required String selectTimeStr}) {
              setState(() {
                _selectedDuration = selectTime;
                _formattedTimeStr = selectTimeStr;
              });
            },
          ),
        ),
        SizedBox(height: safeAreaBottom,)
      ],
    );
  }

  String _formatDuration(Duration duration, TimePickerMode mode) {
    DateTime baseDateTime = DateTime(1970, 1, 1).add(duration);
    switch (mode) {
      case TimePickerMode.YMDHMS:
        return DateFormat('yyyy-MM-dd HH:mm:ss').format(baseDateTime);
      case TimePickerMode.YMDHM:
        return DateFormat('yyyy-MM-dd HH:mm').format(baseDateTime);
      case TimePickerMode.YMDH:
        return DateFormat('yyyy-MM-dd HH').format(baseDateTime);
      case TimePickerMode.MDHM:
        return DateFormat('MM-dd HH:mm').format(baseDateTime);
      case TimePickerMode.YMD:
        return DateFormat('yyyy-MM-dd').format(baseDateTime);
      case TimePickerMode.YM:
        return DateFormat('yyyy-MM').format(baseDateTime);
      case TimePickerMode.Y:
        return DateFormat('yyyy').format(baseDateTime);
      case TimePickerMode.MD:
        return DateFormat('MM-dd').format(baseDateTime);
      case TimePickerMode.HMS:
        return DateFormat('HH:mm:ss').format(baseDateTime);
      case TimePickerMode.HM:
        return DateFormat('HH:mm').format(baseDateTime);
      case TimePickerMode.MS:
        return DateFormat('mm:ss').format(baseDateTime);
      default:
        return '';
    }
  }
}