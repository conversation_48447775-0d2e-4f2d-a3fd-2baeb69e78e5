// @keepTs
// @ts-nocheck
import { ConversationIdentifier } from '@rongcloud/imlib';
import { IBoardPlugin } from '../../../../../conversation/inputbar/component/plugin/IBoardPlugin';
@Component
export declare struct PluginSwiperPage {
    @Require
    @Prop
    pageData: IBoardPlugin[];
    @Require
    @Prop
    conId: ConversationIdentifier;
    context: import("@rongcloud/imkit/../../../../../../../../../../root/.harmony/command-line-tools/sdk/default/openharmony/ets/api/application/ApplicationContext").default;
    build(): void;
    @Builder
    PluginItemView(t148: IBoardPlugin): void;
}
