import { AoiResult } from "./b2";
import { AoiSearchOption } from "./d2";
/**
 * Aoi检索
 */
export declare class AoiSearch {
    private aoiSearch;
    /**
     * 构造函数，初始化AoiSearchImp对象
     * @private
     * @constructor
     */
    constructor();
    /**
     * 获取检索Aoi对象
     *
     * @return Aoi检索对象
     */
    static newInstance(): AoiSearch;
    /**
     * 发起Aoi检索请求
     *
     * @param option 建筑检索参数，经纬度不能为null
     *
     * @return 异步aoi检索结果
     */
    requestAoi(option: AoiSearchOption): Promise<AoiResult>;
}
