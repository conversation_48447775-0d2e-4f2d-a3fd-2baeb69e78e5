import { BuildingInfo } from "@bdmap/base"; import { ColorType, IBuildingOption } from "../../g1/a2"; import { FloorAnimateType } from "../../util/b1/c1"; import BmBitmapResource from "../c2/f2/t3"; import ImageEntity from "../o/s"; import Prism from "./y"; export default class Building extends Prism {   private mFloorHeight; private mLastFloorHeight;   private mFloorColor; private mHasFloor;   private mFloorSideTextureImage;   private mBuildingFloorAnimateType;   private mIsAnimation;   private mBuildingInfo;   private mIsRoundedCorner;   private mRoundedCornerRadius; private bmPrism; private mSurfaceFloorSideStyle; private mSurfaceFloorTopStyle; constructor(j36: IBuildingOption); getFloorHeight(): number; floorHeight(i36: number): this; setFloorHeight(h36: number): void; getHasFloor(): boolean; hasFloor(g36: boolean): this; setHasFloor(f36: boolean): void; getFloorColor(): ColorType; floorColor(d36: ColorType): this; setFloorColor(c36: ColorType): void; getLastFloorHeight(): number; lastFloorHeight(b36: number): this; getFloorSideTextureImage(): ImageEntity; floorSideTextureImage(image: ImageEntity): this;       updateFloorBitMap(bitmap: BmBitmapResource): void; setFloorSideTextureImage(image: ImageEntity): void; getBuildingFloorAnimateType(): FloorAnimateType; buildingFloorAnimateType(type: FloorAnimateType): this; setBuildingFloorAnimateType(type: FloorAnimateType): void; getIsAnimation(): boolean; isAnimation(a36: boolean): this; setIsAnimation(z35: boolean): void; getBuildingInfo(): BuildingInfo; buildingInfo(info: BuildingInfo): this; setBuildingInfo(info: BuildingInfo): void; getIsRoundedCorner(): boolean; isRoundedCorner(x35: boolean): this; setIsRoundedCorner(w35: boolean): void; getRoundedCornerRadius(): number; roundedCornerRadius(radius: number): this; setRoundedCornerRadius(radius: number): void; } 