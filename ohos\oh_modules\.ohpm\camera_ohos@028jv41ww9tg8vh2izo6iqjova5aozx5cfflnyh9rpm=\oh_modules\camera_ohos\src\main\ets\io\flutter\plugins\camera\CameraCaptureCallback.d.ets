import { CameraState } from './CameraState';
import { CameraCaptureProperties } from './types/CameraCaptureProperties';
import { CaptureTimeoutsWrapper } from './types/CaptureTimeoutsWrapper';
export declare class CameraCaptureCallback {
    private static TAG;
    private cameraStateListener;
    private cameraState;
    private captureTimeouts;
    private captureProps;
    constructor(cameraStateListener: CameraCaptureStateListener, captureTimeouts: CaptureTimeoutsWrapper, captureProps: CameraCaptureProperties);
    static create(cameraStateListener: CameraCaptureStateListener, captureTimeouts: CaptureTimeoutsWrapper, captureProps: CameraCaptureProperties): CameraCaptureCallback;
    getCameraState(): CameraState;
    setCameraState(state: CameraState): void;
}
export interface CameraCaptureStateListener {
    onConverged(): void;
    onPrecapture(): void;
}
