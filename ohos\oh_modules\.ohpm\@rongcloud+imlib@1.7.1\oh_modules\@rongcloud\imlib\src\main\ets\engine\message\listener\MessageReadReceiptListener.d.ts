import { ConversationIdentifier } from '../../conversation/ConversationIdentifier';
import { Message } from '../Message';
/**
 * 消息已读 V1 监听，支持单群聊
 * @version 1.3.0
 */
export interface MessageReadReceiptListener {
    /**
     * 单聊中收到消息回执的回调。
     * @param message 消息体
     * @discussion 对方调用 sendReadReceiptMessage() 时触发该方法。触发该方法时本地数据的对应消息已变为已读，需要上层把聊天页面对应消息变为已读
     */
    onMessageReadReceiptReceived(message: Message): any;
    /**
     * 群聊中某人发起了回执请求，会话中其余人会收到该请求，并回调此方法。
     *```
     * 对方调用 sendReadReceiptRequest() 时触发该方法
     * 触发该方法后，当前用户在合适的时机调用 sendReadReceiptResponse() 响应
     *```
     * @param conId 会话标识
     * @param messageUid 请求已读回执的消息 UId
     */
    onMessageReceiptRequest(conId: ConversationIdentifier, messageUid: string): any;
    /**
     * 群组中收到了其他人发送的响应
     * @param conId 会话标识
     * @param messageUid 请求已读响应的消息 UId
     * @param respondUserIdList 响应了此消息的用户列表，key 用户 Id， value 响应时间（毫秒时间戳）
     */
    onMessageReceiptResponse(conId: ConversationIdentifier, messageUid: string, respondUserIdList: Map</* userId */ string, /* timestamp */ number>): any;
}
