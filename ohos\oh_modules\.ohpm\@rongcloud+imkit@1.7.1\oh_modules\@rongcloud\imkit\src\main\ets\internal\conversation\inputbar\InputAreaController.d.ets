// @keepTs
// @ts-nocheck
/**
 * Created on 2024/9/24
 * <AUTHOR>
 */
import { HQVoiceMessage, MentionedInfo, VoiceMessage } from '@rongcloud/imlib';
import { DestructClickEvent } from '../destruct/enum/DestructClickEvent';
import { DestructMode } from '../destruct/enum/DestructMode';
import { DestructType } from '../destruct/enum/DestructType';
import { KeyboardEvent } from './enum/InputEvent';
import { KeyboardType } from './enum/KeyboardType';
import { PublicServiceMode } from './enum/PublicServiceMode';
import { MentionInfo } from './MentionInfo';
import { ConversationComponentData } from '../../../conversation/model/ConversationComponentData';
import { IConversationViewModel } from '../../../conversation/model/IConversationViewModel';
/**
 * 输入区域的控制器
 * @version 1.0.0
 */
export declare class InputAreaController implements IConversationViewModel {
    private _conversationData;
    private _text;
    private _keyboardType;
    private _caretIndex;
    private _maxNumberOfHeight;
    private _minNumberOfHeight;
    private _currentTextAreaHeight;
    private _currentTextAreaLineCount;
    private _isEdit;
    private _textController;
    private _focusable;
    private _onFocus;
    finalCaretIndex: number;
    private _mentionInfoArray;
    private _isShowPublicService;
    private _publicServiceMode;
    private _destructMode;
    private _destructType;
    private _showInputArea;
    onDestructModeChange?: (destructMode: DestructMode) => void;
    onClickMediaAction?: () => void;
    onSendTextAction?: (content: string, mention?: MentionedInfo) => void;
    onSendHQVoiceAction?: (voiceMessage: HQVoiceMessage, mention?: MentionedInfo) => void;
    onSendVoiceAction?: (voiceMessage: VoiceMessage, mention?: MentionedInfo) => void;
    onKeyboardChange?: (keyboardType: KeyboardType) => void;
    onPublicServiceModeChange?: (publicServiceMode: PublicServiceMode) => void;
    onChange?: (objectName: string, content?: string, destructMode?: boolean) => void;
    onClickNewReceivedUnreadMessageButton(): void;
    onClickUnreadMessageButton(): void;
    onClickUnreadMentionedMessageButton(): void;
    onChangeInputTextAreaContent(s151: string): void;
    getInputTextAreaContent(): string;
    /**
     * 结束编辑（收回键盘）
     */
    stopEditing(): void;
    /**
     * 文字键盘弹起
     */
    startEditing(): void;
    /**
     * 回到默认模式
     */
    reset(): void;
    /**
     * 切换键盘
     * @param event
     */
    switchKeyboard(p151: KeyboardEvent): void;
    /**
     * 点击那个按钮
     * @param event
     */
    clickThatButton(n151: DestructClickEvent): void;
    set caretIndex(m151: number);
    get caretIndex(): number;
    set keyboardType(l151: KeyboardType);
    get keyboardType(): KeyboardType;
    set text(k151: string);
    get text(): string;
    get maxNumberOfHeight(): number;
    get minNumberOfHeight(): number;
    set currentTextAreaHeight(j151: number);
    get currentTextAreaHeight(): number;
    set currentTextAreaLineCount(i151: number);
    get currentTextAreaLineCount(): number;
    set conversationData(h151: ConversationComponentData);
    get conversationData(): ConversationComponentData;
    set textController(g151: TextAreaController);
    get textController(): TextAreaController;
    set focusable(f151: boolean);
    get focusable(): boolean;
    set onFocus(e151: boolean);
    get onFocus(): boolean;
    set isShowPublicService(d151: boolean);
    get isShowPublicService(): boolean;
    set publicServiceMode(c151: PublicServiceMode);
    get publicServiceMode(): PublicServiceMode;
    set destructMode(b151: DestructMode);
    get destructMode(): DestructMode;
    set destructType(a151: DestructType);
    get destructType(): DestructType;
    set mentionInfoArray(z150: MentionInfo[]);
    get mentionInfoArray(): MentionInfo[];
    /**
     * 配置输入框组件是否展示
     * @param value 是否展示。true：展示；false：隐藏
     */
    set showInputArea(y150: boolean);
    get showInputArea(): boolean;
    set isEdit(x150: boolean);
    get isEdit(): boolean;
}
