import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_uikit/flutter_uikit.dart';
import 'package:flutter_uikit/ui_text.dart';
import 'package:open_app_settings/open_app_settings.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:wuling_flutter_app/api/report_api/report_api.dart';
import 'package:wuling_flutter_app/page/community/img_text_video_post_detail_page.dart';
import 'package:wuling_flutter_app/page/community/report/report_message/report_message_focus_page.dart';
import 'package:wuling_flutter_app/page/community/report/report_message/report_message_like_page.dart';
import 'package:wuling_flutter_app/page/community/report/report_message/report_message_major_page.dart';
import 'package:wuling_flutter_app/page/community/report/report_message/report_message_order_page.dart';
import 'package:wuling_flutter_app/page/community/report/report_message/report_message_owner_page.dart';
import 'package:wuling_flutter_app/page/community/report/report_message/report_message_reply_page.dart';
import 'package:wuling_flutter_app/page/community/report/report_message/report_message_system_page.dart';

import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wuling_flutter_app/page/store/store_baojun/store_baojun_page.dart';
import 'package:wuling_flutter_app/page/store/store_detail/store_detail_page.dart';
import 'package:wuling_flutter_app/page/store/store_navbar.dart';
import 'package:wuling_flutter_app/page/store/store_wuling/store_wuling_page.dart';
import 'package:wuling_flutter_app/widgets/common/custom_refresher.dart';
import 'package:badges/badges.dart' as badges;

import '../../../common/action.dart';
import '../../../constant/constant.dart';
import '../../../constant/service_constant.dart';
import '../../../constant/web_view_url_tool.dart';
import '../../../models/community/report/report_model.dart';
import '../../../models/community/report/report_owner_item_model.dart';
import '../../../routes/jump_tool.dart';
import '../../../utils/manager/log_manager.dart';
import '../../../utils/manager/notification_manager.dart';
import '../../../widgets/webview/webview.dart';
import '../../post/post_normal_detail_page.dart';
import '../topic_post_details_page.dart';
import '../user_info_page.dart';

class ReportMessage extends StatefulWidget {
  const ReportMessage({super.key});

  @override
  State<ReportMessage> createState() => _ReportMessageState();
}

class _ReportMessageState extends State<ReportMessage> {
  List<ReportMessageItemModel> dataSource = [];
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  int _pageNo = 1;
  bool _isNotification = false;

  ReportOwnerItemModel? model;

  int news = 0;

  @override
  void initState() {
    Permission.notification.isDenied.then((value) {
      LogManager().debug('$value');
      setState(() {
        _isNotification = value;
      });
    });
    _onRefresh();
    super.initState();
  }

  void _onRefresh() async {
    // monitor network fetch
    _pageNo = 1;
    dataSource.clear();
    for (int i = 0; i < data.length; i++) {
      Map<String, dynamic> element = data[i];
      ReportModel value = await reportApi.getNoReadMsgByUser(
          secondMessageType: element["type"]);
      element["message"] = value.toJson()[element["type"]];
      dataSource.add(ReportMessageItemModel.fromMap(element));
    }
    ReportModel value =
        await reportApi.getNoReadMsgByUser(secondMessageType: 'news');
    setState(() {
      news = value.news;
    });
    model = null;
    reportApi
        .getReportNews(secondMessageType: "news", pageNo: _pageNo, pageSize: 1)
        .then((value) {
      if (value.length > 0) {
        setState(() {
          model = value[0];
        });
      }
    });

    await Future.delayed(Duration(milliseconds: 1000));
    _refreshController.refreshCompleted();
  }

  void _onLoading() async {
    _pageNo += 1;
    await Future.delayed(Duration(milliseconds: 1000));
    _refreshController.loadComplete();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SmartRefresher(
          enablePullDown: true,
          enablePullUp: true,
          controller: _refreshController,
          onRefresh: _onRefresh,
          // onLoading: _onLoading,
          header: AnimatedRefreshHeader(),
          // footer: AnimatedRefreshFooter(),
          child: CustomScrollView(
            slivers: [
              _isNotification == false
                  ? SliverToBoxAdapter(
                      child: setNotificationView(),
                    )
                  : SliverToBoxAdapter(),
              SliverToBoxAdapter(
                child: ownerView(),
              ),
              SliverList(
                delegate: SliverChildBuilderDelegate(
                    (context, index) =>
                        GestureDetector(
                            onTap: () => {
                              Navigator.of(context).push(MaterialPageRoute(builder: (context) {
                                if (dataSource[index].type == "praise_collection") {
                                  return const ReportMessageLikePage();
                                } else if (dataSource[index].type == "comments_responses") {
                                  return const ReportMessageReplyPage();
                                } else if (dataSource[index].type == "attention") {
                                  return const ReportMessageFocusPage();
                                } else if (dataSource[index].type == "order_reminding") {
                                  return const ReportMessageOrderPage();
                                } else if (dataSource[index].type == "notice") {
                                  return const ReportMessageSystemPage();
                                } else if (dataSource[index].type == "engineer") {
                                  return const ReportMessageMajorPage();
                                } else {
                                  return const ReportMessageMajorPage();
                                }
                              })).then((value) {
                                _onRefresh();
                            })
                            },
                            child: ReportMessageItem(model: dataSource[index])),
                    childCount: dataSource.length),
              )
            ],
          )),
    );
  }

  Widget setNotificationView() {
    return Container(
        decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(5)),
            boxShadow: [BoxShadow(color: Color(0xFFEEEEEE), blurRadius: 10)]),
        margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
        height: 80,
        child: Row(
          children: [
            Expanded(
              flex: 1,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: const [
                  Text(
                    '打开推送通知',
                    style: TextStyle(color: Colors.black, fontSize: 16),
                  ),
                  Text(
                    '以免错误中奖通知、奖励福利、活动邀请',
                    style: TextStyle(color: Colors.black45, fontSize: 13),
                  ),
                ],
              ),
            ),
            TextButton(
                onPressed: () {
                  OpenAppSettings.openNotificationSettings();
                },
                style: const ButtonStyle(
                  minimumSize: MaterialStatePropertyAll(Size(0, 0)),
                  padding: MaterialStatePropertyAll(
                      EdgeInsets.symmetric(vertical: 5, horizontal: 15)),
                  backgroundColor: MaterialStatePropertyAll(Colors.blueAccent),
                  shape: MaterialStatePropertyAll(RoundedRectangleBorder(
                      borderRadius: BorderRadius.all(Radius.circular(20)))),
                ),
                child: const Text(
                  "开启",
                  style: TextStyle(color: Colors.white),
                ))
          ],
        ));
  }

  Widget ownerView() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 15),
      child: Column(
        children: [
          GestureDetector(
            onTap: () => {
              Navigator.of(context).push(MaterialPageRoute(builder: (context) {
                return const ReportMessageOwnerPage();
              }))
            },
            child: Container(
              decoration: const BoxDecoration(
                  border: Border(
                      bottom: BorderSide(width: 1, color: Color(0xfff3f3f3)))),
              height: 50,
              child: Row(
                children: [
                  const Icon(
                    Icons.star,
                    size: 20,
                  ),
                  const Padding(
                    padding: EdgeInsets.only(left: 10),
                    child: Text(
                      "小菱播报",
                      style: TextStyle(fontSize: 17),
                    ),
                  ),
                  Expanded(
                    flex: 1,
                    child: Container(),
                  ),
                  badges.Badge(
                    position: badges.BadgePosition.custom(start: -10, top: 7.5),
                    showBadge: news == 0 ? false : true,
                    badgeStyle: badges.BadgeStyle(padding: EdgeInsets.all(3)),
                    // badgeContent: UIText(data: model.message.toString(),color: 0xFFFFFFFF,fontSize: 10,),
                    child: Padding(
                      padding: EdgeInsets.only(left: 10),
                      child: Text(
                        "查看全部",
                        style: TextStyle(fontSize: 17),
                      ),
                    ),
                  ),
                  const Icon(
                    Icons.chevron_right,
                    size: 20,
                  )
                ],
              ),
            ),
          ),
          GestureDetector(
            onTap: (){
              switch (model?.skipType) {
                case 1:
                  // 普通帖(非短视频帖、非课堂帖)
                  NavigatorAction.init(context,view: TopicPostDetailsPage(postId: int.parse(model!.skipTarget), postTypeId: 8,));
                  break;
                case 3:
                  // 自有链接
                  NavigatorAction.init(context,view: WebViewPage(url: model!.skipTarget,));
                  break;
                case 4:
                  // "第三方链接";
                  NavigatorAction.init(context,view: WebViewPage(url: model!.skipTarget,));
                  break;
                case 5:
                  // "自有链接，带导航条"; // 客户端会加一个顶部导航栏
                  NavigatorAction.init(context,view: WebViewPage(url: model!.skipTarget,));
                  break;
                case 7:
                  // "客服MM";
                  String url = WebViewURLTool.kefuURLStrWithGroup(
                      KefuGroup.mm.value, '', '');
                  JumpTool().openWeb(context, url, true);
                  break;
                case 13:
                  //  "文本类型";
                  break;
                case 17:
                  //  "用户个人主页";
                  NavigatorAction.init(context,view: UserInfoPage(nickname: '', userIdStr: model!.skipTarget,));
                  break;
                case 25:
                  // "短视频帖";
                  NavigatorAction.init(context,view: ImgTextVideoPostDetailPage(postId: int.parse(model!.skipTarget),postTypeId: 5,));
                  break;
                case 50:
                  // "商品中心-商品详情页";
                  NavigatorAction.init(context,view: StoreDetailPage(id: int.parse(model!.skipTarget),code: 0,));
                  break;
                case 55:
                  // "商品列表(商品中心)";
                  NavigatorAction.init(
                    context,
                    isBack: true,
                  );
                  NotificationManager().postNotification(
                      Constant.NOTIFICATION_MAIN_PAGE_SELECT_PAGE,
                      userInfo: {'pageIndex': 3});
                  break;
                case 56:
                  // "指定一二级分类(商品中心)";
                  NavigatorAction.init(context,view: StoreBaoJunPage(id: int.parse(model!.skipTarget),));
                  break;
                case 57:
                  //  "仅指定二级分类(商品中心)";
                  NavigatorAction.init(context,view: StoreWuLingPage(id: int.parse(model!.skipTarget),));
                  break;
                case 58:
                  // "话题详情页";
                  int topicId = int.parse(model!.skipTarget);
                  JumpTool().jumpToTopicDetailPage(
                    context: context,
                    topicId: topicId,
                  );
                  break;
                case 59:
                 // "小程序";
                  break;
              }
            },
            child: model != null
                ? Container(
                    decoration: const BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.all(Radius.circular(5)),
                        boxShadow: [
                          BoxShadow(color: Color(0xFFEEEEEE), blurRadius: 10)
                        ]),
                    padding: const EdgeInsets.all(10),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        UIImage(
                          imgStr: model!.image,
                          width: 500,
                          height: 200,
                          fit: BoxFit.cover,
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(vertical: 10),
                          child: Text(model!.text,
                              style: TextStyle(
                                  color: Color(0xFF999999), fontSize: 12)),
                        ),
                        Align(
                          alignment: Alignment.centerRight,
                          child: Text(
                            '${DateTime.fromMillisecondsSinceEpoch(model!.time).toLocal()}',
                            style: TextStyle(
                                color: Color(0xFFCCCCCC), fontSize: 12),
                          ),
                        ),
                      ],
                    ),
                  )
                : Container(),
          )
        ],
      ),
    );
  }

  @override
  void dispose() {
    // TODO: implement dispose
    _refreshController?.dispose();
    super.dispose();
  }
}

// 是否开启消息通知
const bool isNotificationPermission = false;

List<Map<String, dynamic>> data = [
  {
    "title": "赞和收藏",
    "type": "praise_collection",
    "icon": "",
    "newMessage": false,
    "message": 4,
    "showNew": false
  },
  {
    "title": "评论、回复和@",
    "type": "comments_responses",
    "icon": "",
    "newMessage": false,
    "message": 4,
    "showNew": false
  },
  {
    "title": "新增关注",
    "type": "attention",
    "icon": "",
    "newMessage": false,
    "message": 4,
    "showNew": false
  },
  {
    "title": "订单提醒",
    "type": "order_reminding",
    "icon": "",
    "newMessage": false,
    "message": 4,
    "showNew": false
  },
  {
    "title": "通知",
    "type": "notice",
    "icon": "",
    "newMessage": false,
    "message": 4,
    "showNew": false
  },
  // {
  //   "title": "金牌技师",
  //   "type": "engineer",
  //   "icon": "",
  //   "newMessage": false,
  //   "message": 4,
  //   "showNew": false
  // }
];

class ReportMessageItemModel {
  final String? title;
  final String? type;
  final bool? newMessage;
  final String? icon;
  final int? message;
  final bool? showNew;

  ReportMessageItemModel(
      {this.type,
      this.title,
      this.newMessage,
      this.icon,
      this.message,
      this.showNew});

  factory ReportMessageItemModel.fromMap(Map data) {
    return ReportMessageItemModel(
        title: data["title"],
        type: data["type"],
        newMessage: data["newMessage"],
        icon: data["icon"],
        message: data["message"],
        showNew: data["showNew"]);
  }
}

class ReportMessageItem extends StatelessWidget {
  final ReportMessageItemModel model;
  const ReportMessageItem({super.key, required this.model});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
          border:
              Border(bottom: BorderSide(width: 1, color: Color(0xfff3f3f3)))),
      padding: const EdgeInsets.symmetric(horizontal: 15),
      height: 50,
      child: Row(
        children: [
          const Icon(
            Icons.star,
            size: 20,
          ),
          Padding(
            padding: const EdgeInsets.only(left: 10),
            child: Text(
              model.title!,
              style: const TextStyle(fontSize: 17),
            ),
          ),
          Expanded(
            flex: 1,
            child: Container(),
          ),
          badges.Badge(
              position: badges.BadgePosition.custom(start: -10, top: 7.5),
              showBadge: model.message == 0 ? false : true,
              badgeStyle: badges.BadgeStyle(padding: EdgeInsets.all(3)),
              // badgeContent: UIText(data: model.message.toString(),color: 0xFFFFFFFF,fontSize: 10,),
              child: const Icon(
                Icons.chevron_right,
                size: 20,
              ))
        ],
      ),
    );
  }
}
