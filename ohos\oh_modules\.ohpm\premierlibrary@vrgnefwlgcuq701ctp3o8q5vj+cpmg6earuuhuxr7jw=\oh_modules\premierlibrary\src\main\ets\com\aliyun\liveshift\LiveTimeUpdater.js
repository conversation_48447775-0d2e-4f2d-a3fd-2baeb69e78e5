import { GetTimeShiftRequest } from "./request/GetTimeShiftRequest";
import J<PERSON><PERSON> from "@ohos.util.json";
export class LiveTimeUpdater {
    constructor(u3) {
        this.mConfig = null;
        this.mTimeShift = null;
        this.timeShiftUpdaterListener = null;
        this.playTime = 0;
        this.liveTime = 0;
        this.needPause = false;
        this.lastLiveMsgID = -1;
        this.lastPlayMsgID = -1;
        this.mTimeShift = u3;
    }
    HandleMessage(t3) {
        if (t3.id == LiveTimeUpdater.WHAT_UPDATE_LIVE_TIME) {
            this.updateLiveTimer();
            this.startUpdateLiveTimerDelay(60);
        }
        else if (t3.id == LiveTimeUpdater.WHAT_UPDATE_PLAY_TIME) {
            if (this.needPause) {
            }
            else {
                this.playTime++;
            }
            this.liveTime++;
            this.startUpdatePlayTimerDelay(1);
        }
        console.info("playTime: " + this.playTime + " liveTime: " + this.liveTime);
    }
    getStartTime(s3) {
        if (s3.length <= 0) {
            return 0;
        }
        return s3[0].start;
    }
    getEndTime(r3) {
        if (r3.length <= 0) {
            return 0;
        }
        return r3[r3.length - 1].end;
    }
    updateLiveTimer() {
        let c3 = {
            onSuccess: (h3, i3) => {
                if (this.timeShiftUpdaterListener != null) {
                    let j3 = JSON.parse(h3);
                    let k3 = Reflect.get(j3, "content");
                    let l3 = JSON.stringify(k3);
                    let m3 = JSON.parse(l3);
                    let n3 = m3.current;
                    let o3 = m3.timeline;
                    let p3 = this.getStartTime(o3);
                    let q3 = this.getEndTime(o3);
                    this.liveTime = n3;
                    if (this.playTime < 0) {
                        this.playTime = n3;
                    }
                    this.startUpdatePlayTimerDelay(0);
                    this.timeShiftUpdaterListener?.onUpdater(n3, p3, q3);
                }
            },
            onFail: (e3, f3, g3) => {
                console.info(`request failed, code is ${e3}, msg is ${f3}, extra is ${g3}`);
            }
        };
        if (!this.mTimeShift) {
            console.info("got a empty LiveShift");
            return;
        }
        let d3 = new GetTimeShiftRequest(this.mTimeShift, c3);
        if (this.mConfig) {
            d3.setRefer(this.mConfig.mReferrer);
            d3.setTimeout(this.mConfig.mNetworkTimeout);
            d3.setHttpProxy(this.mConfig.mHttpProxy);
            d3.setUserAgent(this.mConfig.mUserAgent);
            d3.setCustomHeaders(this.mConfig.getCustomHeadersArray());
        }
        d3.getAsync();
    }
    stopUpdatePlayTimer() {
        clearTimeout(this.lastPlayMsgID);
    }
    stopUpdateLiveTimer() {
        clearTimeout(this.lastLiveMsgID);
    }
    startUpdatePlayTimerDelay(b3) {
        this.sendEmptyMessageDelayed(1, b3);
    }
    startUpdateLiveTimerDelay(a3) {
        this.sendEmptyMessageDelayed(0, a3);
    }
    sendEmptyMessageDelayed(u2, v2) {
        if (u2 == 0) {
            clearTimeout(this.lastLiveMsgID);
            this.lastLiveMsgID = setTimeout(() => {
                let z2 = {
                    id: 0,
                    failCode: 0,
                    extra: "",
                    data: undefined,
                    type: "",
                    timeStamp: 0
                };
                this.HandleMessage(z2);
            }, v2 * 1000);
        }
        else if (u2 == 1) {
            clearTimeout(this.lastPlayMsgID);
            this.lastPlayMsgID = setTimeout(() => {
                let x2 = {
                    id: 1,
                    failCode: 0,
                    extra: "",
                    data: undefined,
                    type: "",
                    timeStamp: 0
                };
                this.HandleMessage(x2);
            }, v2 * 1000);
        }
    }
    setStartPlayTime(t2) {
        this.playTime = t2;
    }
    startUpdater() {
        this.startUpdateLiveTimerDelay(0);
    }
    setUpdaterListener(s2) {
        this.timeShiftUpdaterListener = s2;
    }
    pauseUpdater() {
        this.needPause = true;
    }
    resumeUpdater() {
        this.needPause = false;
    }
    stopUpdater() {
        this.stopUpdateLiveTimer();
        this.stopUpdatePlayTimer();
    }
    getPlayTime() {
        return this.playTime;
    }
    getLiveTime() {
        return this.liveTime;
    }
    setConfig(r2) {
        this.mConfig = r2;
    }
}
LiveTimeUpdater.WHAT_UPDATE_LIVE_TIME = 0;
LiveTimeUpdater.WHAT_UPDATE_PLAY_TIME = 1;
