// @keepTs
// @ts-nocheck
/**
 * 消息时间格式化
 * @version 1.0.0
 */
export declare class MessageTimeFormatter {
    private static readonly oneDayHours;
    private static readonly oneHourMinutes;
    private static readonly oneMinuteSeconds;
    private static readonly oneSecondMilliseconds;
    private static readonly oneDayMilliseconds;
    private static readonly oneHourMilliseconds;
    private static readonly oneMinuteMilliseconds;
    /**
     * 会话列表时间格式化
     * @param timestamp 时间戳
     * @returns
     */
    static formatConversationListTime(w346: Context, x346: number): string;
    /**
     * 聊天页面的消息时间格式化
     * @param timestamp 时间戳
     * @returns
     */
    static formatMessageTime(r345: Context, s345: number, t345?: boolean): string;
    /**
     * 获取周几字符串描述
     * @param context
     * @param week
     * @returns
     */
    private static getWeekStr;
    /**
     * 获取描述
     * 凌晨
     * 上午
     * 中午
     * 下午
     * 晚上
     */
    private static getDescribe;
    private static converHours;
}
