export class PlayerConfig {
    constructor() {
        this.mHttpProxy = "";
        this.mReferrer = "";
        this.mNetworkTimeout = 15000;
        this.mMaxDelayTime = 5000;
        this.mMaxBufferDuration = 50000;
        this.mHighBufferDuration = 3000;
        this.mStartBufferDuration = 500;
        this.mMaxProbeSize = -1;
        this.mClearFrameWhenStop = false;
        this.mEnableVideoTunnelRender = false;
        this.mEnableVideoBufferRender = false;
        this.mEnableSEI = false;
        this.mUserAgent = "";
        this.mNetworkRetryCount = 2;
        this.mLiveStartIndex = -3;
        this.mDisableAudio = false;
        this.mDisableVideo = false;
        this.mPositionTimerIntervalMs = 500;
        this.mMaxBackwardBufferDurationMs = 0;
        this.mPreferAudio = false;
        this.mEnableLocalCache = true;
        this.mEnableHttpDns = -1;
        this.mEnableEnhancedHttpDns = -1;
        this.mEnableHttp3 = false;
        this.mEnableStrictFlvHeader = false;
        this.mEnableLowLatencyMode = true;
        this.mEnableProjection = false;
        this.mEnableStrictAuthMode = true;
        this.mStartBufferLimit = 15000;
        this.mStopBufferLimit = 3000;
        this.mSelectTrackBufferMode = 0;
        this.mMaxAllowedAbrVideoPixelNumber = 2147483647;
        this.mAudioInterruptMode = 0;
        this.nativeGetHttpProxy = () => {
            return this.mHttpProxy;
        };
        this.nativeGetReferrer = () => {
            return this.mReferrer;
        };
        this.nativeGetNetworkTimeout = () => {
            return this.mNetworkTimeout;
        };
        this.nativeGetMaxDelayTime = () => {
            return this.mMaxDelayTime;
        };
        this.nativeGetMaxBufferDuration = () => {
            return this.mMaxBufferDuration;
        };
        this.nativeGetHighBufferDuration = () => {
            return this.mHighBufferDuration;
        };
        this.nativeGetStartBufferDuration = () => {
            return this.mStartBufferDuration;
        };
        this.nativeGetMaxProbeSize = () => {
            return this.mMaxProbeSize;
        };
        this.nativeGetClearFrameWhenStop = () => {
            return this.mClearFrameWhenStop;
        };
        this.nativeGetEnableVideoTunnelRender = () => {
            return this.mEnableVideoTunnelRender;
        };
        this.nativeGetEnableVideoBufferRender = () => {
            return this.mEnableVideoBufferRender;
        };
        this.nativeGetEnableSEI = () => {
            return this.mEnableSEI;
        };
        this.nativeGetUserAgent = () => {
            return this.mUserAgent;
        };
        this.nativeGetNetworkRetryCount = () => {
            return this.mNetworkRetryCount;
        };
        this.nativeGetLiveStartIndex = () => {
            return this.mLiveStartIndex;
        };
        this.nativeGetDisableAudio = () => {
            return this.mDisableAudio;
        };
        this.nativeGetDisableVideo = () => {
            return this.mDisableVideo;
        };
        this.nativeGetPositionTimerIntervalMs = () => {
            return this.mPositionTimerIntervalMs;
        };
        this.nativeGetMaxBackwardBufferDurationMs = () => {
            return this.mMaxBackwardBufferDurationMs;
        };
        this.nativeGetPreferAudio = () => {
            return this.mPreferAudio;
        };
        this.nativeGetEnableLocalCache = () => {
            return this.mEnableLocalCache;
        };
        this.nativeGetEnableHttpDns = () => {
            return this.mEnableHttpDns;
        };
        this.nativeGetEnableHttp3 = () => {
            return this.mEnableHttp3;
        };
        this.nativeGetEnableStrictFlvHeader = () => {
            return this.mEnableStrictFlvHeader;
        };
        this.nativeGetEnableLowLatencyMode = () => {
            return this.mEnableLowLatencyMode;
        };
        this.nativeGetEnableProjection = () => {
            return this.mEnableProjection;
        };
        this.nativeGetEnableStrictAuthMode = () => {
            return this.mEnableStrictAuthMode;
        };
        this.nativeGetStartBufferLimit = () => {
            return this.mStartBufferLimit;
        };
        this.nativeGetStopBufferLimit = () => {
            return this.mStopBufferLimit;
        };
        this.nativeGetAudioInterruptMode = () => {
            return this.mAudioInterruptMode;
        };
        this.nativeGetCustomHeaders = () => {
            return this.mCustomHeaders;
        };
        this.nativeGetSelectTrackBufferMode = () => {
            return this.mSelectTrackBufferMode;
        };
        this.nativeGetMaxAllowedAbrVideoPixelNumber = () => {
            return this.mMaxAllowedAbrVideoPixelNumber;
        };
        this.nativeGetEnableEnhancedHttpDns = () => {
            return this.mEnableHttpDns;
        };
        this.nativeGetCustomHeadersNumber = () => {
            return this.mCustomHeaders?.length;
        };
        this.nativeGetCustomHeader = (a33) => {
            if (this.mCustomHeaders && a33 < this.mCustomHeaders.length) {
                return this.mCustomHeaders[a33];
            }
            return undefined;
        };
    }
    getCustomHeaders() {
        return this.mCustomHeaders;
    }
    getCustomHeadersArray() {
        let j31 = [{}];
        if (this.mCustomHeaders != undefined && this.mCustomHeaders?.length > 0) {
            for (let k31 = 0; k31 < this.mCustomHeaders.length; k31++) {
                let l31 = this.mCustomHeaders[k31];
                if (l31.length > 0) {
                    let m31 = l31.split(":");
                    if (m31.length === 2) {
                        let n31 = m31[0].trim();
                        let o31 = m31[1].trim();
                        let p31 = {};
                        p31[n31] = o31;
                        j31.push(p31);
                    }
                }
            }
        }
        return j31;
    }
    setCustomHeaders(i31) {
        this.mCustomHeaders = i31;
    }
}
export var PixelNumber;
(function (h31) {
    h31[h31["Resolution_360P"] = 172800] = "Resolution_360P";
    h31[h31["Resolution_480P"] = 345600] = "Resolution_480P";
    h31[h31["Resolution_540P"] = 518400] = "Resolution_540P";
    h31[h31["Resolution_720P"] = 921600] = "Resolution_720P";
    h31[h31["Resolution_1080P"] = 2073600] = "Resolution_1080P";
    h31[h31["Resolution_2K"] = 3686400] = "Resolution_2K";
    h31[h31["Resolution_4K"] = 8847360] = "Resolution_4K";
    h31[h31["Resolution_NoLimit"] = 2147483647] = "Resolution_NoLimit";
})(PixelNumber || (PixelNumber = {}));
