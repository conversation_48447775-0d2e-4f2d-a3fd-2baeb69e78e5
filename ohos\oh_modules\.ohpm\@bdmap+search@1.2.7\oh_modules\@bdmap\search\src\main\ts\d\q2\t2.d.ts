import { SuggestionSearchOption } from "./r2";
import { SuggestionResult } from "./s2";
/**
 * sug检索
 */
export declare class SuggestionSearch {
    private suggestionSearch;
    private constructor();
    static newInstance(): SuggestionSearch;
    /**
     * sug检索
     *
     * @param option 请求参数
     *
     * @return 异步返回检索结果 SuggestionResult
     */
    requestSuggestion(option: SuggestionSearchOption): Promise<SuggestionResult>;
}
