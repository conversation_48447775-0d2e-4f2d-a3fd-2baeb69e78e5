import { InstanceManager } from './InstanceManager';
import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import { Reply, JavaScriptChannelFlutterApi } from "./GeneratedOhosWebView";
import { JavaScriptChannel } from './JavaScriptChannel';
export declare class JavaScriptChannelFlutterApiImpl extends JavaScriptChannelFlutterApi {
    private instanceManager;
    constructor(binaryMessenger: BinaryMessenger, instanceManager: InstanceManager);
    postMessage(javaScriptChannel: JavaScriptChannel, messageArg: string, callback: Reply<void>): void;
    private getIdentifierForJavaScriptChannel;
}
