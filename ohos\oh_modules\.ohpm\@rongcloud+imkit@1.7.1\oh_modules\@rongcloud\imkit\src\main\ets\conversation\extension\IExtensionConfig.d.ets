// @keepTs
// @ts-nocheck
import { ConversationIdentifier, IBoardPlugin } from '../../../../../Index';
import { ArrayList } from '@kit.ArkTS';
import { IEmoticonTab } from '../inputbar/component/emoticon/IEmoticonTab';
/**
 * 模块组件配置
 * @version 1.4.3
 */
export interface IExtensionConfig {
    /**
     * 返回输入框 “+” 号区域配置的插件列表。
     *
     * @param convId 会话标识。
     * @return 插件列表。
     */
    getPluginModules: (convId: ConversationIdentifier) => ArrayList<IBoardPlugin>;
    /**
     * 返回输入框表情区域配置字的表情 tab 列表。
     *
     * @param convId 会话标识。
     * @return 表情 tab 列表。
     * @version 1.5.1
     */
    getEmoticonTabs?: (convId: ConversationIdentifier) => ArrayList<IEmoticonTab>;
}
