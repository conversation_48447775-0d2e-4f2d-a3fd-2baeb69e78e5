// @keepTs
// @ts-nocheck
import { Conversation, ConversationStatusInfo, ConversationStatusListener, ConversationType, IAsyncResult } from '@rongcloud/imlib';
import { ItemLongClickAction } from '../../base/click/ItemLongClickAction';
import { ItemProvider } from '../../base/item/provider/ItemProvider';
import { ConversationListService } from '../../conversationlist/ConversationListService';
import { BaseUiConversation } from '../../conversationlist/model/BaseUiConversation';
import List from '@ohos.util.List';
import ArrayList from '@ohos.util.ArrayList';
import { ConversationListEventListener } from '../../conversationlist/listener/ConversationListEventListener';
import { ConversationListConfig } from '../../conversationlist/config/ConversationListConfig';
export declare class InnerConversationListServiceImpl implements ConversationListService, ConversationStatusListener {
    private config;
    private providerCache;
    private conversationActionArray;
    private conversationListEventListeners;
    constructor();
    onInit(): void;
    private init;
    private loadDefaultLongAction;
    onConversationStatusChange: (items: List<ConversationStatusInfo>) => void;
    setConversationListConfig(k268: ConversationListConfig): void;
    getConversationListConfig(): ConversationListConfig;
    private getConversationIdentifiers;
    addConversationItemLongClickAction(d268: ItemLongClickAction<Conversation>): void;
    getConversationActionArray(): Array<ItemLongClickAction<Conversation>>;
    addConversationItemProvider(b268: ConversationType, c268: ItemProvider<BaseUiConversation>): void;
    removeConversationItemProvider(a268: ConversationType): void;
    /**
     * 会话列表 item ，对应的会话类型没有，会返回单聊的 provider
     * @param conversationType
     * @returns
     */
    getConversationItemProvider(z267: ConversationType): ItemProvider<BaseUiConversation>;
    /**
     * 添加会话操作事件监听
     */
    addConversationListEventListener(y267: ConversationListEventListener): void;
    /**
     * 移除会话操作事件监听
     */
    removeConversationListEventListener(x267: ConversationListEventListener): void;
    getConversationListEventListener(): ArrayList<ConversationListEventListener>;
    /**
     * 获取会话列表
     */
    getConversationList(q267: ConversationType[], r267?: number, s267?: number): Promise<IAsyncResult<List<Conversation>>>;
}
