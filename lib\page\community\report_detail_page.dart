import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker_ohos/image_picker_ohos.dart';
import 'package:image_picker_platform_interface/image_picker_platform_interface.dart';
import 'package:wuling_flutter_app/api/community_api.dart';
import 'package:wuling_flutter_app/utils/http/http_request.dart';
import 'package:wuling_flutter_app/utils/manager/loading_manager.dart';

import '../../models/community/report_type.dart';
import '../../utils/manager/log_manager.dart';

class ReportDetailPage extends StatefulWidget {
  const ReportDetailPage({super.key,required this.postId});
  final int postId;

  @override
  State<ReportDetailPage> createState() => _ReportDetailPageState();
}

class _ReportDetailPageState extends State<ReportDetailPage> {
  List<String> imgList = ["assets/images/community/add_img.png"];
  final ImagePickerOhos _picker = ImagePickerOhos();
  List<ReportType> typeList = [];
  int prosecutionTypeId = -1;
  String reportText = "选择举报理由(必填)";
  String contentText = ""; //举报的内容文本
  List<String> imageList = [];

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    getReportTypeList();

  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        title: const Text("举报详情"),
        centerTitle: true,
        actions: [
          GestureDetector(
            onTap: () {
              _submit();
            },
            child: const Center(
              child: Padding(
          padding: EdgeInsets.all(10),
      child: Text("提交",style: TextStyle(
          fontWeight: FontWeight.w500,
          fontSize: 18
      ),
      ),),
            ),
          )
        ],
      ),
      body: SingleChildScrollView(
        child: Container(
          padding: const EdgeInsets.only(left: 20,right: 20),
          margin: const EdgeInsets.only(top: 10),
          color: Colors.white,
          child: Column(
            children: [
              _reason(),
              const Divider(thickness: 1,color: Color(0xFFF1F3F5,),),
              _applyReason(),
              Container(
                height: 50,
                alignment: Alignment.centerLeft,
                child: Text("举报配图${imgList.length - 1}/5 (选填)"),
              ),
              Container(
                alignment: Alignment.centerLeft,
                child: const Text("请截取帖子中重点违规部分，方便运营人员快速审核",style: TextStyle(
                    fontSize: 12
                ),),
              ),
              _addImg(),
              Container(
                height: 50,
                alignment: Alignment.topLeft,
                child: const Text("感谢您对社区的严格督促，收到举报后，我们会及时处理，处理后，会第一时间告诉您处理结果",style: TextStyle(
                  fontSize: 12
                ),),
              )
            ],
          ),
        ),
      ),
    );
  }
  //举报理由
  Widget _reason() {
    return GestureDetector(
      onTap: () {
        _showDialog();
      },
      child: Container(
        height: 50,
        color: Colors.white,
        margin: const EdgeInsets.only(top: 10),
        padding: const EdgeInsets.only(left: 10),
        alignment: Alignment.centerLeft,
        child: Text(reportText),
      ),
    );
  }
  //申请理由
  Widget _applyReason() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 60,
          alignment: Alignment.centerLeft,
          child: const Text("申请理由 (必填)"),
        ),
        Container(
          height: 160,
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xFFF1F2F3),width: 1.0)
          ),
          child: TextField(
            onChanged: (String text) {
              contentText = text;
            },
            decoration: const InputDecoration(
              contentPadding: EdgeInsets.symmetric(vertical: 10,horizontal: 10),
              hintText: "请详细描述您举报的理由，这对我们很重要，谢谢!",
              hintStyle: TextStyle(
                fontSize: 12
              ),
              labelStyle: TextStyle(
                fontSize: 12
              ),
              border: InputBorder.none,
            ),
            maxLines: 11,
            maxLength: 100,
          ),
        )
      ],
    );
  }
  
  Widget _addImg() {
    return Container(
      margin: const EdgeInsets.only(top: 15),
      child: GridView.builder(
      physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,   // 每行3列
          mainAxisSpacing: 10, // 行间距
          crossAxisSpacing: 10 // 列间距
      ), itemBuilder: (context,index) {
        return GestureDetector(
          onTap: () {
            _getImage();
            countDown();
          },
          child: SizedBox(
            width: 80,
            height: 80,
            child: _imageWidget(index),
          ),
        );
      },itemCount: imgList.length),
    );
  }
  Timer? _timer;
  int seconds = 1;
  bool selectCancel = true; //是否取消图片选择
  _getImage() async {
    LoadingManager.show(status: "选择图片中....");

    try {
      List<PickedFile>? images = await _picker.pickMultiImage(imageQuality: 70);
      selectCancel = false;
      imgList.clear();
      imgList.add("assets/images/community/add_img.png");
      imageList.clear();
      if(images != null && images.isNotEmpty) {
        if(images.length > 4) {
          images.sublist(0,5).forEach((element) {
            imgList.insert(0, element.path);
            imageList.add(element.path);
          });
        }else {
          for(PickedFile filePath in images) {
            imgList.insert(0, filePath.path);
            imageList.add(filePath.path);
          }
        }
      }
      setState(() {
      });
    }catch (e) {
      LogManager().debug("选择图片失败: $e");
    }
    LoadingManager.showSuccess("图片选择完成");
  }

  void countDown() {
    if(selectCancel) {
      _timer?.cancel();
      seconds = 1;
      _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if(seconds > 0) {
          setState(() {
            seconds--;
          });
        }else {
          _timer?.cancel();
          LoadingManager.dismiss();
        }
      });
    }else {
      _timer?.cancel();
      LoadingManager.dismiss();
    }

  }

  Widget _imageWidget(int index) {
    if(index == imgList.length - 1) {
      return Image.asset(imgList[index],fit: BoxFit.cover,);
    }else {
      return Image.file(File(imgList[index]),fit: BoxFit.cover,);
    }
  }

  void _showDialog() {
    showModalBottomSheet(context: context, builder: (context) {
      return Container(
        height: 390,
        color: Colors.white,
        child: Column(
          children: [
            Container(
              height: 50,
              alignment: Alignment.center,
              padding: const EdgeInsets.only(left: 15,right: 15),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: const [
                  Text("举报理由",style: TextStyle(
                      color: Colors.grey
                  ),),
                  Text("完成",style: TextStyle(
                      color: Colors.grey
                  ),),
                ],
              ),
            ),
            Column(
              children: typeList.map((item) {
                return _item(item,context);
              }).toList(),
            )
          ],
        ),
      );
    });
  }

  Widget _item(ReportType reportType,BuildContext context) {
    return GestureDetector(
      onTap: () {
        prosecutionTypeId = reportType.prosecutionTypeId;
        reportText = reportType.prosecutionTypeDesc;
        Navigator.of(context).pop();

        setState(() {

        });
      },
      child: Container(
        height: 44,
        alignment: Alignment.center,
        decoration: const BoxDecoration(
          border: Border(top: BorderSide(width: 1.0,color: Color(0xFFF1F3F5)))
        ),
        child: Text(reportType.prosecutionTypeDesc),
      ),
    );
  }

  //获取举报理由
  Future<List<ReportType>> getReportTypeList() async {
    typeList = await communityAPI.getReportTypeList();
    return typeList;
  }

  void _submit() async {
    List<String> list = [];
    if(prosecutionTypeId == -1) {
      return LoadingManager.showToast("请选择举报理由");
    }
    if(contentText.isEmpty) {
      return LoadingManager.showToast("请输入申请理由");
    }
   /* if(contentText.length < 10) {
      return LoadingManager.showToast("请详细描写申请理由，不能少于10个字");
    }*/
    if(imageList.isNotEmpty) {
      LoadingManager.show(status: "图片上传中.....");
      Future.delayed(const Duration(seconds: 1),() async {
        list = await FileUpload(HttpRequest()).uploadFiles(imageList);
        _sendReport(list);
      });
    }else {
      _sendReport([]);
    }
  }
  void _sendReport(List<String> list) async{
    await communityAPI.submitReport(widget.postId, prosecutionTypeId, contentText,list);
    LoadingManager.showSuccess("举报成功");

    Navigator.of(context).pop();
  }

}
