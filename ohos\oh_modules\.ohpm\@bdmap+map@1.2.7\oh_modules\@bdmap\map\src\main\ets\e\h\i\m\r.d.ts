          import { LineJoinType, LineCapType, LineDirectionCross, PolylineDottedLineType, ELineBloomType, TextureOption, SmoothType } from "../../util/b1/c1"; import ImageEntity from "../o/s"; import Bundle from "../o/i1"; import Overlay from "./m"; import type { LatLng } from '@bdmap/base'; import type { ColorString, IPolylineOption, Nullable } from "../../g1/a2"; import type { ImageOverlayData } from "../../g1/i1"; import type OverlayMgr from "./j2"; import type BmBitmapResource from "../c2/f2/t3";             export default class Polyline extends Overlay { private mPoints; private mWidth; private mTextures; private mTextureOption; private mJoin; private mCap; private mStartCap; private mEndCap; private mIsGeodesic; private mDirectionCross180; private mColor; private mStrokeWidth; private mStrokeColor; private mIsThined; private mThinFactor; private mSmooth; private mSmoothFactor; private mDottedLine; private mDottedLineType;   private mColorList;   private mIndexList;   private mIsGradient;   private mLineBloomType;   private mLineBloomWidth;   private mLineBloomAlpha;   private mLineBloomGradientASPeed;   private mLineBloomBlurTimes; private _solidLine; private bmPolyline; private bmGeoElements; private bitmaps;                                   constructor(l45?: Nullable<IPolylineOption>);       init(): void; private setGeoElement; private addGeoElement; private setGeoEleLineStyle; private sePolylineStyle; private getLineStyle; private setGeoElementsWidthStyle; private setGeoElementsColorStyle; private setGeoElementsStrokeWidthStyle; private setGeoElementsStrokeColorStyle; private setGeoElementsLineTypeStyle; private setGeoElementsTextureStyle; private setGeoElementsTextureOptionStyle; private setGeoElementsGradientColors;         textureOption(val: TextureOption): this;         setTextureOption(val: TextureOption): void;         getTextureOption(): TextureOption;         getColor(): any;         color(color?: ColorString): this;         setColor(color: ColorString): void;         getPoints(): LatLng[];         points(points: Array<LatLng>): this;       preUpdatePoint(): void;         setPoints(points: Array<LatLng>): void;         getWidth(): number;         width(width: number): this;         setWidth(width: number): void;         getStrokeWidth(): number;         strokeWidth(width: number): this;         setStrokeWidth(width: number): void;         getStrokeColor(): any;         strokeColor(color: ColorString): this;         setStrokeColor(color: ColorString): void;         getJoinType(): LineJoinType;         joinType(type: LineJoinType): this;         setJoinType(type: LineJoinType): void;         getCapType(): LineCapType;         capType(type: LineCapType): this;         setCapType(type: LineCapType): void; startCap(val: LineCapType): void; setStartCap(val: LineCapType): void; getStartCap(): LineCapType; endCap(val: LineCapType): void; setEndCap(val: LineCapType): void; getEndCap(): LineCapType;         getIsThined(): (isThined: boolean) => this;         isThined(w43: boolean): this;         setIsThined(v43: boolean): void;         thinFactor(val: number): void;         setThinFactor(val: number): void;         getThinFactor(): number;         getDirectionCross180(): LineDirectionCross;         directionCross180(cross: LineDirectionCross): this;         setDirectionCross180(cross: LineDirectionCross): void;         getGeodesic(): boolean;         isGeodesic(geodesic: boolean): this;         setGeodesic(geodesic: boolean): void;         getIsDotted(): boolean;         isDottedline(u43: boolean): this;         setIsDottedline(t43: boolean): void;         getDottedLineType(): PolylineDottedLineType;         dottedLineType(val: PolylineDottedLineType): this;         setDottedLineType(val: PolylineDottedLineType): void;         textures(r43: Array<ImageEntity>): this;       updateBmIcon(icon: ImageEntity, index: number): void;       updateBitMap(bitmap: BmBitmapResource, index: number): void;         getTextures(): ImageEntity[];         setTexture(p43: ImageEntity): void;         setTextures(o43: Array<ImageEntity>): void;         colorList(n43: Array<ColorString>): this;         setColorList(m43: Array<ColorString>): void;         getColorList(): any[];         indexList(l43: Array<number>): this;         setIndexList(k43: Array<number>): void;         getIndexList(): number[];         isGradient(enable: boolean): this; private toDrawItem;         setIsGradient(enable: boolean): void;         getIsGradient(): boolean;         lineBloomType(type: ELineBloomType): this;         setLineBloomType(type: ELineBloomType): void;         getLineBloomType(): ELineBloomType;         lineBloomWidth(g43: number): this;         setLineBloomWidth(f43: number): void;         getLineBloomWidth(): number;           lineBloomAlpha(d43: number): void;           setLineBloomAlpha(c43: number): void;         getLineBloomAlpha(): number;           lineBloomGradientASPeed(speed: number): this;           setLineBloomGradientASPeed(speed: number): void;         getLineBloomGradientASPeed(): number;           lineBloomBlurTimes(z42: number): this;           setLineBloomBlurTimes(y42: number): void;         getLineBloomBlurTimes(): number;         smooth(val: SmoothType): this;         setSmooth(val: SmoothType): void;         getSmooth(): (val: SmoothType) => this;       smoothFactor(val: number): this;       setSmoothFactor(val: number): void;       getSmoothFactor(): number; get typeName(): string;       dataFormat(k42: Array<ImageOverlayData>): Nullable<Bundle>;       getImageEntity(): ImageEntity;       toBundle(e42: OverlayMgr): Promise<Bundle>;       toString(): string; } 