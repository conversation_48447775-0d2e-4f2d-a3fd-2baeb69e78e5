export declare enum AoiType {
    AOI_TYPE_UNKNOWN = 0,
    AOI_TYPE_AIRPORT = 1,
    AOI_TYPE_RAILWAT_STATION = 2,
    AOI_TYPE_SHOPPINGMALL = 3,
    AOI_TYPE_GAS_STATION = 4,
    AOI_TYPE_SCHOOL = 5,
    AOI_TYPE_HOSPITAL = 6,
    AOI_TYPE_RESIDENTIAL_DISTRICT = 7,
    AOI_TYPE_SCENIC_AREA = 8,
    AOI_TYPE_PARK = 9,
    AOI_TYPE_FREEWAY_SERVICE = 10,
    AOI_TYPE_WATER = 11
}
export interface AoiInfo {
    uid?: string;
    aoiName?: string;
    polygon?: string;
    aoiType?: AoiType;
    nearestDistance?: number;
    order?: number;
    relation?: number;
}
