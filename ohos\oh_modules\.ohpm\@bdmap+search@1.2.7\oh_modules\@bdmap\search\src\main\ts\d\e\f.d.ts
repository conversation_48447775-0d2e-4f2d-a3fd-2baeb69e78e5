import { BaseSearch } from "../../u2/base/base";
import type { RecommendStopResult, RecommendStopSearchOption } from "./h/j";
interface IRecommendStop {
    /**
     * 推荐上车点请求接口
     * @param option 推荐上车点请求参数配置
     * @return 是否成功发起请求
     */
    requestRecommendStop(option: RecommendStopSearchOption): Promise<RecommendStopResult>;
}
export declare class RecommendStopSearch {
    private iRecommendStop;
    private constructor();
    /**
     * 新建推荐上车点检索对象
     *
     * @return 推荐上车点检索对象
     */
    static newInstance(): RecommendStopSearch;
    /**
     * 发起推荐上车点检索请求
     *
     * @param option 请求参数。 请求参数的位置经纬度信息不能为空
     * @return 成功发起检索返回true , 失败返回false
     */
    requestRecommendStop(option: RecommendStopSearchOption): Promise<RecommendStopResult>;
}
/**
 * 推荐上车点查询实现类.
 */
export declare class RecommendStopSearchImp extends BaseSearch implements IRecommendStop {
    requestRecommendStop(option: RecommendStopSearchOption): Promise<RecommendStopResult>;
}
export {};
