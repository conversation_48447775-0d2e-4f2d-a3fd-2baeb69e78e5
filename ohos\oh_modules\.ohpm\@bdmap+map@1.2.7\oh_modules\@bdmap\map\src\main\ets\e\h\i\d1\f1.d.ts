import { LatLng } from '@bdmap/base'; import BaseLayer from "./j1"; import type { ColorString, Nullable } from "../../g1/a2"; import type BaseMap from "../o2";             export default class LocationLayer extends BaseLayer { private _location; private _direction; private _showDirection; private _radius; private _circleFillColor;               constructor(name: string, id: string, m29: BaseMap);         get location(): Nullable<LatLng>;         set location(val: Nullable<LatLng>);         get direction(): number;           set direction(val: number);         get radius(): number;         set radius(val: number);         get circleFillColor(): ColorString;         set circleFillColor(val: ColorString);           showDirection(): void;           hideDirection(): void;         reDraw(): { type: number; result?: undefined; } | { type: number; result: { type: number; data: { ptx: number; pty: number; radius: number; direction: number; iconarrownor: string; iconarrownorid: number; iconarrowfocid: number; iconarrowfoc: string; areacolor: any; }; }; }; } 