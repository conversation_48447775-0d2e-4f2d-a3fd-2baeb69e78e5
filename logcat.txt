Flutter assets will be downloaded from https://storage.flutter-io.cn. Make sure you trust this source!
Launching lib\main.dart on 29Q0223813005436 in debug mode...
start hap build...
Running Hvigor task assembleHap...
Compiling debug_ohos_application for the Ohos...
07-29 09:54:14.123  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:14.122000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 09:54:14.621  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:14.621000] [UseCarPageState.getNotificationData (use_car_page.dart:1918:20)] 通知服务-- 2
07-29 09:54:14.624  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:14.623000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:54:14.635  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:14.632000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:54:14.637  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:14.635000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:54:14.640  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:14.638000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:54:14.645  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:14.644000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:54:15.126  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:15.125000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:54:15.139  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:15.136000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:54:15.141  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:15.139000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:54:15.144  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:15.142000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:54:15.151  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:15.149000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:54:16.900  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:16.900000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:54:16.962  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:16.962000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:54:16.968  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:16.967000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:54:16.969  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:16.968000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:54:16.970  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:16.969000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:54:16.973  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:16.972000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:54:17.117  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.117000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 218, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.33377, longitude: 109.366806, position: null, collectTime: 2025-07-29 09:51:16, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 1.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.07, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 38, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:54:17.118  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.118000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:51:16，输入时间为2025-07-29 09:51:16，相差0，状态已过期，更新输入时间为状态时间
07-29 09:54:17.118  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.118000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:51:16
07-29 09:54:17.123  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.123000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:54:17.123  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.123000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:54:17.124  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.124000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:17.124  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.124000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:17.124  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.124000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:17.125  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.124000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:17.125  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.125000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:54:17.129  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.129000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:54:17.134  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.133000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:54:17.135  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.134000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:54:17.137  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.136000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:54:17.140  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.139000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:54:17.461  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.461000] [UseCarPageState._onRefresh (use_car_page.dart:1573:13)] 手动下拉刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 218, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.33377, longitude: 109.366806, position: null, collectTime: 2025-07-29 09:51:16, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 1.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.07, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 38, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:54:17.462  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.461000] [UseCarPageState._onRefresh (use_car_page.dart:1587:15)] 手动下拉刷新接口请求成功，_userHandleDisConnectBle：false ，_bleManager.currentStatus：BleStatus.bleDefault，_currentConnectBleVin:LK6AEAE44SB192188 , currentVin:LK6AEAE44SB192188,_carInfoModel?.bleType:2
07-29 09:54:17.462  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.462000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:54:17.463  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.463000] [UseCarPageState._fetchBluetoothKey (use_car_page.dart:1626:9)] [BleKey]===获取蓝牙钥匙===
07-29 09:54:17.463  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.463000] [UseCarPageState._fetchBluetoothKey (use_car_page.dart:1631:13)] [BleKey]===获取蓝牙钥匙===_currentConnectBleVin：LK6AEAE44SB192188 , vin:LK6AEAE44SB192188 , _currentBleStatus:BleStatus.bleDefault
07-29 09:54:17.464  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.463000] [UseCarPageState.loginMQTT (use_car_page.dart:1932:18)] MQTT登录，loginMQTT: _currentCarVin - LK6AEAE44SB192188 ， vin：LK6AEAE44SB192188 ， supportMqtt：1 , isLogin:true
07-29 09:54:17.465  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.465000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:54:17.472  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.471000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:54:17.473  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.472000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:54:17.475  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.474000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:54:17.480  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.478000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:54:17.506  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.505000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:54:17.811  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.810000] [use_car_page.dart:1649:19] [BleKey]===获取蓝牙钥匙成功，userHandleDisConnectBle：false ，_bleManager.currentStatus：BleStatus.bleDefault，_currentConnectBleVin:LK6AEAE44SB192188 , currentVin:LK6AEAE44SB192188,_carInfoModel?.bleType:2 ，bleKeyModel.vin:LK6AEAE44SB192188
07-29 09:54:17.811  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.811000] [use_car_page.dart:1651:19] [BleKey]===缓存蓝牙钥匙，bleMac: 40:F3:B0:12:2B:FE
07-29 09:54:17.811  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.811000] [MiniLifeCacheManager.saveBleKeyModel (mini_life_cache_manager.dart:86:22)] MiniLifeCacheManager: 开始保存蓝牙钥匙到缓存
07-29 09:54:17.812  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.812000] [MiniLifeCacheManager.saveBleKeyModel (mini_life_cache_manager.dart:88:14)] MiniLifeCacheManager: 蓝牙钥匙 bleMac: 40:F3:B0:12:2B:FE
07-29 09:54:17.816  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:17.816000] [MiniLifeCacheManager.saveBleKeyModel (mini_life_cache_manager.dart:93:22)] MiniLifeCacheManager: 蓝牙钥匙缓存保存成功
07-29 09:54:18.045  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.045000] [UseCarPageState.getHasCarListData (use_car_page.dart:1820:20)] UseCarPage: 开始保存MiniLife数据到缓存
07-29 09:54:18.045  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.045000] [MiniLifeCacheManager.saveMiniLifeData (mini_life_cache_manager.dart:107:18)] MiniLifeCacheManager: 开始批量保存MiniLife数据
07-29 09:54:18.045  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.045000] [MiniLifeCacheManager.saveMiniLifeData (mini_life_cache_manager.dart:108:18)] MiniLifeCacheManager: 活动列表数量: 0, MiniLife模型: null
07-29 09:54:18.045  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.045000] [MiniLifeCacheManager.saveActivityList (mini_life_cache_manager.dart:22:20)] MiniLifeCacheManager: 开始保存活动列表到缓存，数量: 0
07-29 09:54:18.046  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.046000] [MiniLifeCacheManager.saveMiniLifeModel (mini_life_cache_manager.dart:75:22)] MiniLifeCacheManager: MiniLife模型为null，跳过缓存保存
07-29 09:54:18.048  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.047000] [MiniLifeCacheManager.saveActivityList (mini_life_cache_manager.dart:56:12)] MiniLifeCacheManager: 活动列表缓存保存成功，数量: 0
07-29 09:54:18.048  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.048000] [MiniLifeCacheManager.saveMiniLifeData (mini_life_cache_manager.dart:116:18)] MiniLifeCacheManager: MiniLife数据批量保存完成
07-29 09:54:18.048  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.048000] [UseCarPageState.getHasCarListData (use_car_page.dart:1825:20)] UseCarPage: MiniLife数据缓存保存完成
07-29 09:54:18.048  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.048000] [use_car_page.dart:1834:22] DEBUG: setState中设置变量完成 - _toolServiceList长度: 7
07-29 09:54:18.048  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.048000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:18.048  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.048000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:18.049  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.049000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:18.049  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.049000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:18.055  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.055000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:54:18.061  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.060000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:54:18.061  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.061000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:54:18.063  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.062000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:54:18.066  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.065000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:54:18.610  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.610000] [UseCarPageState.getHasCarListData (use_car_page.dart:1846:20)] 获取用车服务列表成功，数量: 8
07-29 09:54:18.610  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.610000] [UseCarPageState.getHasCarListData (use_car_page.dart:1849:22)]   [0] 流量商城: https://oss-rewrite.00bang.cn/image/LLB_OSSA8AC35ACF9DA4BE59C41FA3170EF0BB4.png
07-29 09:54:18.610  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.610000] [UseCarPageState.getHasCarListData (use_car_page.dart:1849:22)]   [1] 车主认证: https://oss-rewrite.00bang.cn/image/LLB_OSS7D1E5A35F57A43ABA88ACEC1FF63ABBB.png
07-29 09:54:18.610  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.610000] [UseCarPageState.getHasCarListData (use_car_page.dart:1849:22)]   [2] 用车指南: https://oss-rewrite.00bang.cn/image/LLB_OSSE96C6CBE4CA0439E80D50B32F64797C1.png
07-29 09:54:18.611  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.610000] [UseCarPageState.getHasCarListData (use_car_page.dart:1849:22)]   [3] 服务进度: https://cdn-df.00bang.cn/images/T1kJKTBjWT1RCvBVdK.png
07-29 09:54:18.611  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.611000] [UseCarPageState.getHasCarListData (use_car_page.dart:1849:22)]   [4] 预约服务: https://cdn-df.00bang.cn/images/T1LAKTBgxT1RCvBVdK.png
07-29 09:54:18.611  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.611000] [UseCarPageState.getHasCarListData (use_car_page.dart:1849:22)]   [5] 道路救援: https://cdn-df.00bang.cn/images/T1LKKTBgbT1RCvBVdK.png
07-29 09:54:18.611  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.611000] [UseCarPageState.getHasCarListData (use_car_page.dart:1849:22)]   [6] 优惠劵: https://cdn-df.00bang.cn/images/T1dPCTByYT1RCvBVdK.png
07-29 09:54:18.611  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.611000] [UseCarPageState.getHasCarListData (use_car_page.dart:1849:22)]   [7] 全部功能: https://cdn-df.00bang.cn/images/T1nJKTBTCT1RCvBVdK.png
07-29 09:54:18.611  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.611000] [use_car_page.dart:1855:22] 设置_useCarServiceList完成，准备缓存
07-29 09:54:18.619  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.618000] [UseCarPageState._cacheServiceLists (use_car_page.dart:4465:22)] 服务列表缓存成功，key: travel_service_lists_MTcwMDc0ODg=_LK6AEAE44SB192188
07-29 09:54:18.623  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.623000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:54:18.631  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.629000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:54:18.632  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.631000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:54:18.634  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.632000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:54:18.638  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:18.636000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:54:19.121  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:19.120000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 09:54:19.354  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:19.354000] [UseCarPageState.getNotificationData (use_car_page.dart:1918:20)] 通知服务-- 2
07-29 09:54:19.367  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:19.366000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:54:19.372  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:19.371000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:54:19.373  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:19.372000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:54:19.375  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:19.374000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:54:19.379  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:19.377000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:54:19.860  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:19.859000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:54:19.867  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:19.866000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:54:19.869  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:19.867000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:54:19.871  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:19.870000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:54:19.876  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:19.874000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:54:21.616  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:21.615000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:54:21.785  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:21.785000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 218, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.33377, longitude: 109.366806, position: null, collectTime: 2025-07-29 09:51:16, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 1.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.07, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 38, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:54:21.786  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:21.786000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:51:16，输入时间为2025-07-29 09:51:16，相差0，状态已过期，更新输入时间为状态时间
07-29 09:54:21.786  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:21.786000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:51:16
07-29 09:54:21.789  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:21.789000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:54:21.790  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:21.789000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:54:21.791  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:21.791000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:21.792  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:21.791000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:21.793  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:21.793000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:21.793  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:21.793000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:21.794  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:21.794000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:54:21.802  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:21.800000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:54:21.803  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:21.802000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:54:21.805  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:21.804000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:54:21.808  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:21.807000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:54:21.830  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:21.829000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:54:24.123  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:24.122000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 09:54:24.618  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:24.617000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:54:25.029  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:25.027000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 218, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.33377, longitude: 109.366806, position: null, collectTime: 2025-07-29 09:51:16, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 1.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.07, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 38, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:54:25.031  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:25.030000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:51:16，输入时间为2025-07-29 09:51:16，相差0，状态已过期，更新输入时间为状态时间
07-29 09:54:25.031  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:25.031000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:51:16
07-29 09:54:25.038  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:25.037000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:54:25.038  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:25.038000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:54:25.039  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:25.039000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:25.039  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:25.039000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:25.040  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:25.040000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:25.040  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:25.040000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:25.043  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:25.042000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:54:25.049  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:25.048000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:54:25.063  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:25.060000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:54:25.066  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:25.064000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:54:25.071  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:25.068000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:54:25.082  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:25.079000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:54:27.617  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:27.615000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:54:28.130  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:28.129000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 218, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.33377, longitude: 109.366806, position: null, collectTime: 2025-07-29 09:51:16, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 1.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.07, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 38, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:54:28.131  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:28.131000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:51:16，输入时间为2025-07-29 09:51:16，相差0，状态已过期，更新输入时间为状态时间
07-29 09:54:28.132  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:28.131000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:51:16
07-29 09:54:28.135  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:28.134000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:54:28.135  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:28.135000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:54:28.137  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:28.136000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:28.137  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:28.137000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:28.138  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:28.138000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:28.139  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:28.138000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:28.139  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:28.139000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:54:28.141  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:28.140000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:54:28.150  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:28.149000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:54:28.152  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:28.150000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:54:28.154  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:28.153000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:54:28.160  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:28.158000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:54:29.123  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:29.122000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 09:54:30.618  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:30.616000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:54:31.003  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:31.002000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 218, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.33377, longitude: 109.366806, position: null, collectTime: 2025-07-29 09:51:16, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 1.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.07, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 38, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:54:31.005  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:31.004000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:51:16，输入时间为2025-07-29 09:51:16，相差0，状态已过期，更新输入时间为状态时间
07-29 09:54:31.006  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:31.005000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:51:16
07-29 09:54:31.010  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:31.009000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:54:31.010  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:31.010000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:54:31.012  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:31.011000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:31.012  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:31.012000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:31.013  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:31.013000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:31.014  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:31.014000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:31.016  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:31.016000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:54:31.030  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:31.028000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:54:31.032  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:31.030000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:54:31.035  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:31.033000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:54:31.041  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:31.039000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:54:31.065  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:31.064000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:54:33.622  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:33.620000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:54:33.887  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:33.886000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 218, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.33377, longitude: 109.366806, position: null, collectTime: 2025-07-29 09:51:16, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 1.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.07, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 38, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:54:33.888  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:33.888000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:51:16，输入时间为2025-07-29 09:51:16，相差0，状态已过期，更新输入时间为状态时间
07-29 09:54:33.889  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:33.889000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:51:16
07-29 09:54:33.896  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:33.895000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:54:33.897  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:33.896000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:54:33.900  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:33.899000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:33.901  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:33.900000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:33.902  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:33.902000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:33.903  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:33.902000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:33.905  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:33.904000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:54:33.923  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:33.919000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:54:33.926  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:33.923000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:54:33.931  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:33.928000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:54:33.941  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:33.938000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:54:33.967  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:33.967000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:54:34.127  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:34.125000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 09:54:36.617  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:36.616000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:54:36.928  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:36.927000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 218, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.33377, longitude: 109.366806, position: null, collectTime: 2025-07-29 09:51:16, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 1.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.07, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 38, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:54:36.930  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:36.930000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:51:16，输入时间为2025-07-29 09:51:16，相差0，状态已过期，更新输入时间为状态时间
07-29 09:54:36.931  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:36.930000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:51:16
07-29 09:54:36.936  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:36.935000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:54:36.936  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:36.936000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:54:36.939  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:36.939000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:36.941  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:36.940000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:36.942  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:36.941000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:36.942  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:36.942000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:36.945  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:36.944000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:54:36.965  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:36.961000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:54:36.967  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:36.965000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:54:36.969  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:36.968000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:54:36.973  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:36.972000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:54:36.996  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:36.996000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:54:39.123  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:39.121000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 09:54:39.617  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:39.615000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:54:40.525  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:40.524000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 218, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.33377, longitude: 109.366806, position: null, collectTime: 2025-07-29 09:51:16, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 1.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.07, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 38, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:54:40.526  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:40.526000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:51:16，输入时间为2025-07-29 09:51:16，相差0，状态已过期，更新输入时间为状态时间
07-29 09:54:40.527  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:40.527000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:51:16
07-29 09:54:40.532  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:40.532000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:54:40.533  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:40.533000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:54:40.536  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:40.535000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:40.537  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:40.536000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:40.539  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:40.537000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:40.539  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:40.539000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:40.540  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:40.540000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:54:40.547  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:40.546000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:54:40.565  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:40.561000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:54:40.568  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:40.565000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:54:40.574  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:40.571000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:54:40.583  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:40.581000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:54:42.618  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:42.616000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:54:42.950  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:42.949000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 218, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.33377, longitude: 109.366806, position: null, collectTime: 2025-07-29 09:51:16, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 1.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.07, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 38, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:54:42.952  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:42.951000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:51:16，输入时间为2025-07-29 09:51:16，相差0，状态已过期，更新输入时间为状态时间
07-29 09:54:42.953  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:42.952000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:51:16
07-29 09:54:42.958  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:42.958000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:54:42.959  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:42.959000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:54:42.961  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:42.960000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:42.961  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:42.961000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:42.962  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:42.962000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:42.963  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:42.962000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:42.963  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:42.963000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:54:42.968  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:42.967000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:54:42.984  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:42.981000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:54:42.987  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:42.984000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:54:42.992  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:42.989000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:54:43.004  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:43.001000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:54:44.126  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:44.125000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 09:54:45.617  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:45.616000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:54:46.783  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:46.782000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 218, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.33377, longitude: 109.366806, position: null, collectTime: 2025-07-29 09:51:16, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 1.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.07, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 38, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:54:46.785  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:46.784000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:51:16，输入时间为2025-07-29 09:51:16，相差0，状态已过期，更新输入时间为状态时间
07-29 09:54:46.785  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:46.785000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:51:16
07-29 09:54:46.790  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:46.789000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:54:46.790  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:46.790000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:54:46.793  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:46.792000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:46.793  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:46.793000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:46.794  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:46.794000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:46.795  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:46.794000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:46.796  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:46.796000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:54:46.812  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:46.809000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:54:46.816  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:46.813000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:54:46.821  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:46.818000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:54:46.831  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:46.828000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:54:46.866  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:46.866000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:54:48.621  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:48.619000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:54:48.926  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:48.924000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 218, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.33377, longitude: 109.366806, position: null, collectTime: 2025-07-29 09:51:16, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 1.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.07, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 38, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:54:48.928  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:48.927000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:51:16，输入时间为2025-07-29 09:51:16，相差0，状态已过期，更新输入时间为状态时间
07-29 09:54:48.929  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:48.928000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:51:16
07-29 09:54:48.935  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:48.934000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:54:48.936  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:48.935000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:54:48.941  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:48.940000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:48.941  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:48.941000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:48.942  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:48.942000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:48.943  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:48.942000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:48.944  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:48.943000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:54:48.947  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:48.947000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:54:48.965  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:48.961000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:54:48.967  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:48.965000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:54:48.972  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:48.969000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:54:48.979  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:48.976000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:54:49.125  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:49.123000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 09:54:51.617  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:51.616000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:54:51.986  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:51.985000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 218, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.33377, longitude: 109.366806, position: null, collectTime: 2025-07-29 09:51:16, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 1.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.07, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 38, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:54:51.987  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:51.987000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:51:16，输入时间为2025-07-29 09:51:16，相差0，状态已过期，更新输入时间为状态时间
07-29 09:54:51.988  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:51.987000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:51:16
07-29 09:54:51.994  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:51.993000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:54:51.994  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:51.994000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:54:51.998  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:51.997000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:51.998  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:51.998000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:51.999  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:51.999000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:52.000  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:51.999000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:52.002  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:52.001000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:54:52.004  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:52.003000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:54:52.011  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:52.010000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:54:52.012  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:52.011000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:54:52.014  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:52.013000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:54:52.019  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:52.018000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:54:54.126  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:54.125000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 09:54:54.616  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:54.615000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:54:54.934  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:54.932000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 218, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.33377, longitude: 109.366806, position: null, collectTime: 2025-07-29 09:51:16, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 1.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.07, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 38, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:54:54.938  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:54.937000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:51:16，输入时间为2025-07-29 09:51:16，相差0，状态已过期，更新输入时间为状态时间
07-29 09:54:54.939  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:54.938000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:51:16
07-29 09:54:54.945  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:54.944000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:54:54.945  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:54.945000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:54:54.947  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:54.946000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:54.948  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:54.947000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:54.948  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:54.948000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:54.949  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:54.948000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:54.950  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:54.950000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:54:54.959  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:54.959000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:54:54.977  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:54.974000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:54:54.980  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:54.977000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:54:54.983  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:54.982000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:54:54.991  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:54.989000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:54:57.619  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:57.618000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:54:57.840  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:57.839000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 218, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.33377, longitude: 109.366806, position: null, collectTime: 2025-07-29 09:51:16, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 1.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.07, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 38, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:54:57.842  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:57.841000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:51:16，输入时间为2025-07-29 09:51:16，相差0，状态已过期，更新输入时间为状态时间
07-29 09:54:57.843  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:57.842000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:51:16
07-29 09:54:57.846  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:57.846000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:54:57.847  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:57.846000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:54:57.849  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:57.848000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:57.850  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:57.849000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:57.851  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:57.850000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:54:57.851  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:57.851000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:54:57.853  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:57.853000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:54:57.867  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:57.864000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:54:57.868  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:57.867000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:54:57.871  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:57.869000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:54:57.877  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:57.875000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:54:57.904  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:57.903000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:54:58.094  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:58.093000] [UseCarPageState.doCarControlNetwork (UseCarPageState.doCarControlNetwork (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2808)] 开始车控，serviceStatusResultType: 1 ，backStatusValue：2 , serviceCode: acStatus , mAccType:4 , 车控accParameter: ,车控params:{buttonLayout: 1, accOnOff: 1, temperature: 17, blowerLvl: 7, duration: 10}
07-29 09:54:59.122  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:54:59.121000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 09:55:00.619  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:00.618000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:55:01.072  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:01.071000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 218, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.33377, longitude: 109.366806, position: null, collectTime: 2025-07-29 09:51:16, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 1.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.07, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 38, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:55:01.073  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:01.073000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:51:16，输入时间为2025-07-29 09:51:16，相差0，状态已过期，更新输入时间为状态时间
07-29 09:55:01.074  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:01.073000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:51:16
07-29 09:55:01.078  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:01.077000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:55:01.078  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:01.078000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:55:01.080  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:01.080000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:01.081  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:01.080000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:01.082  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:01.082000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:01.083  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:01.082000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:01.085  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:01.084000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:55:01.099  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:01.096000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:55:01.102  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:01.100000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:55:01.106  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:01.104000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:55:01.111  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:01.109000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:55:01.139  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:01.139000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:55:03.617  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:03.616000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:55:03.950  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:03.949000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 218, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.33377, longitude: 109.366806, position: null, collectTime: 2025-07-29 09:51:16, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 1.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.07, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 38, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:55:03.951  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:03.951000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:51:16，输入时间为2025-07-29 09:51:16，相差0，状态已过期，更新输入时间为状态时间
07-29 09:55:03.952  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:03.952000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:51:16
07-29 09:55:03.957  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:03.956000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:55:03.958  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:03.957000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:55:03.961  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:03.960000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:03.961  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:03.961000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:03.962  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:03.962000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:03.963  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:03.962000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:03.966  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:03.965000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:55:03.980  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:03.977000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:55:03.983  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:03.980000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:55:03.987  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:03.985000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:55:03.990  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:03.989000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:55:04.019  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:04.019000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:55:04.122  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:04.122000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 09:55:06.464  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:06.463000] [MQTTManager.startMqttTimeOutTimer (mqtt_manager.dart:620:18)] MQTTManager: 启动接收MQTT异步结果推送超时定时器
07-29 09:55:06.464  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:06.464000] [MQTTManager.stopMqttTimeOutTimer (mqtt_manager.dart:627:18)] MQTTManager: 停止接收MQTT异步结果推送超时定时器
07-29 09:55:06.623  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:06.621000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:55:06.861  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:06.860000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 218, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.33377, longitude: 109.366806, position: null, collectTime: 2025-07-29 09:51:16, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 1.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.07, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 38, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:55:06.863  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:06.862000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:51:16，输入时间为2025-07-29 09:51:16，相差0，状态已过期，更新输入时间为状态时间
07-29 09:55:06.863  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:06.863000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:51:16
07-29 09:55:06.868  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:06.868000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:1 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:true , IS_AC_HOT_WIND_SETTING:false
07-29 09:55:06.870  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:06.869000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:06.871  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:06.870000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:06.871  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:06.871000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:06.872  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:06.871000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:06.883  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:06.882000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:55:06.902  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:06.897000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:55:06.905  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:06.902000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:55:06.910  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:06.907000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:55:06.920  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:06.917000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:55:09.130  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:09.129000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 09:55:09.617  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:09.615000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:55:09.888  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:09.886000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 218, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.33377, longitude: 109.366806, position: null, collectTime: 2025-07-29 09:51:16, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 1.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.07, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 38, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:55:09.889  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:09.889000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:51:16，输入时间为2025-07-29 09:51:16，相差0，状态已过期，更新输入时间为状态时间
07-29 09:55:09.890  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:09.890000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:51:16
07-29 09:55:09.900  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:09.899000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:1 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:true , IS_AC_HOT_WIND_SETTING:false
07-29 09:55:09.902  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:09.901000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:09.903  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:09.902000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:09.904  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:09.903000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:09.904  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:09.904000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:09.906  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:09.905000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:55:09.923  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:09.920000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:55:09.926  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:09.924000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:55:09.931  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:09.928000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:55:09.941  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:09.938000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:55:12.618  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:12.616000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:55:12.954  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:12.952000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 218, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.33377, longitude: 109.366806, position: null, collectTime: 2025-07-29 09:51:16, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 1.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.07, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 38, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:55:12.956  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:12.955000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:51:16，输入时间为2025-07-29 09:51:16，相差0，状态已过期，更新输入时间为状态时间
07-29 09:55:12.956  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:12.956000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:51:16
07-29 09:55:12.960  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:12.960000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:1 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:true , IS_AC_HOT_WIND_SETTING:false
07-29 09:55:12.962  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:12.961000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:12.962  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:12.962000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:12.963  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:12.962000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:12.963  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:12.963000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:12.965  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:12.964000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:55:12.981  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:12.976000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:55:12.984  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:12.981000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:55:12.988  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:12.985000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:55:12.997  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:12.994000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:55:14.126  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:14.125000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 09:55:15.617  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:15.615000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:55:15.915  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:15.915000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 218, acStatus: 1, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.3338, longitude: 109.366758, position: null, collectTime: 2025-07-29 09:55:10, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.8, voltage: 420.5, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 31, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.46, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 39, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:55:15.918  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:15.917000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:51:16，输入时间为2025-07-29 09:55:10，相差234000，状态已过期，更新输入时间为状态时间
07-29 09:55:15.918  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:15.918000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:55:10
07-29 09:55:15.933  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:15.932000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:1 ,_MQTTBackStatus:1 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:true , IS_AC_HOT_WIND_SETTING:false
07-29 09:55:15.933  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:15.933000] [MQTTManager.stopMqttTimeOutTimer (mqtt_manager.dart:627:18)] MQTTManager: 停止接收MQTT异步结果推送超时定时器
07-29 09:55:15.934  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:15.934000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:55:15.936  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:15.936000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:15.937  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:15.937000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:15.938  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:15.937000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:15.938  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:15.938000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:15.944  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:15.944000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:55:15.961  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:15.957000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:55:15.963  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:15.961000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:55:15.964  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:15.963000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:55:15.968  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:15.967000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  1
07-29 09:55:15.996  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:15.996000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:55:18.616  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:18.615000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:55:18.923  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:18.922000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 218, acStatus: 1, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.3338, longitude: 109.366758, position: null, collectTime: 2025-07-29 09:55:10, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.8, voltage: 420.5, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 31, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.46, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 39, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:55:18.925  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:18.924000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:55:10，输入时间为2025-07-29 09:55:10，相差0，状态已过期，更新输入时间为状态时间
07-29 09:55:18.925  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:18.925000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:55:10
07-29 09:55:18.930  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:18.929000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:1 ,_MQTTBackStatus:1 , _currentACStatus:1,_statusModel?.acStatus:1 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:55:18.931  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:18.930000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:55:18.933  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:18.933000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:18.934  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:18.933000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:18.935  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:18.935000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:18.936  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:18.935000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:18.937  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:18.936000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:55:18.941  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:18.940000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:55:18.960  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:18.956000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:55:18.962  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:18.960000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:55:18.964  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:18.963000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:55:18.971  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:18.969000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  1
07-29 09:55:19.123  6435  6578 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:19.122000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 09:55:23.099 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:23.066000] [error_util.dart:28:20] instance
07-29 09:55:23.577 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: 本地存在车辆信息，判定为车主用户
07-29 09:55:23.956 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: ✅ isCarOwner = 1，车主用户，将进入出行页 (INDEX_TRAVEL = 1)
07-29 09:55:29.841 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:29.822000] [UseCarPageState._loadAllCacheDataOnInit (use_car_page.dart:4655:20)] 缓存优先：页面初始化时开始加载所有缓存数据
07-29 09:55:29.845 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:29.845000] [UseCarPageState._loadUseCarServiceFromCache (use_car_page.dart:4756:24)] 从缓存加载用车服务列表成功，共8项
07-29 09:55:29.866 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:29.865000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:55:30.023 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:30.022000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1289:20)] isNewAir() carStatus == null
07-29 09:55:30.209 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:30.208000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:30.209 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:30.209000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:30.210 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:30.210000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:30.210 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:30.210000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:30.211 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:30.211000] [UseCarPageState._loadAllCacheDataOnInit (use_car_page.dart:4709:20)] 缓存优先：页面初始化时缓存数据加载完成
07-29 09:55:30.218 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:30.218000] [mqtt_manager.dart:664:20] MQTTManager 网络状态变化: 已连接
07-29 09:55:30.219 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:30.219000] [mqtt_manager.dart:664:20] MQTTManager 网络状态变化: 已连接
07-29 09:55:30.219 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:30.219000] [mqtt_manager.dart:664:20] MQTTManager 网络状态变化: 已连接
07-29 09:55:30.220 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:30.220000] [mqtt_manager.dart:664:20] MQTTManager 网络状态变化: 已连接
07-29 09:55:30.378 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:30.378000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:55:30.409 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:30.408000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:55:30.424 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:30.424000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  1
07-29 09:55:30.616 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:30.616000] [BleManager._updateSytemBleStatus (ble_manager.dart:146:22)] [BleKey]===系统蓝牙打开---
07-29 09:55:31.185 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:31.184000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:55:31.328 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:31.327000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:55:31.334 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:31.334000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  1
07-29 09:55:31.424 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:31.424000] [UseCarPageState._onRefresh (use_car_page.dart:1573:13)] 手动下拉刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 1, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.33379, longitude: 109.366797, position: null, collectTime: 2025-07-29 09:55:20, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 2, voltage: 420.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 32, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.23, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 40, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:55:31.425 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:31.425000] [UseCarPageState._onRefresh (use_car_page.dart:1587:15)] 手动下拉刷新接口请求成功，_userHandleDisConnectBle：false ，_bleManager.currentStatus：BleStatus.bleDefault，_currentConnectBleVin: , currentVin:LK6AEAE44SB192188,_carInfoModel?.bleType:2
07-29 09:55:31.425 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:31.425000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:55:31.428 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:31.427000] [UseCarPageState._fetchBluetoothKey (use_car_page.dart:1626:9)] [BleKey]===获取蓝牙钥匙===
07-29 09:55:31.428 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:31.428000] [UseCarPageState._fetchBluetoothKey (use_car_page.dart:1631:13)] [BleKey]===获取蓝牙钥匙===_currentConnectBleVin： , vin:LK6AEAE44SB192188 , _currentBleStatus:BleStatus.bleDefault
07-29 09:55:31.428 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:31.428000] [BleManager.resetHasBluetoothKey (BleManager.resetHasBluetoothKey (package:wuling_flutter_app/utils/manager/ble_manager.dart:329)] [BleKey]===重设蓝牙钥匙获取标记===hasBluetoothKey:false
07-29 09:55:31.430 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:31.430000] [UseCarPageState.loginMQTT (use_car_page.dart:1932:18)] MQTT登录，loginMQTT: _currentCarVin -  ， vin：LK6AEAE44SB192188 ， supportMqtt：1 , isLogin:true
07-29 09:55:31.434 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:31.434000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:55:31.446 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:31.445000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:55:31.450 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:31.450000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  1
07-29 09:55:31.490 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:31.490000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:55:31.748 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:31.747000] [use_car_page.dart:1649:19] [BleKey]===获取蓝牙钥匙成功，userHandleDisConnectBle：false ，_bleManager.currentStatus：BleStatus.bleDefault，_currentConnectBleVin: , currentVin:LK6AEAE44SB192188,_carInfoModel?.bleType:2 ，bleKeyModel.vin:LK6AEAE44SB192188
07-29 09:55:31.748 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:31.748000] [use_car_page.dart:1651:19] [BleKey]===缓存蓝牙钥匙，bleMac: 40:F3:B0:12:2B:FE
07-29 09:55:31.749 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:31.748000] [MiniLifeCacheManager.saveBleKeyModel (mini_life_cache_manager.dart:86:22)] MiniLifeCacheManager: 开始保存蓝牙钥匙到缓存
07-29 09:55:31.749 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:31.749000] [MiniLifeCacheManager.saveBleKeyModel (mini_life_cache_manager.dart:88:14)] MiniLifeCacheManager: 蓝牙钥匙 bleMac: 40:F3:B0:12:2B:FE
07-29 09:55:31.750 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:31.750000] [use_car_page.dart:1666:27] [BleKey]===获取蓝牙钥匙===首次连接蓝牙===
07-29 09:55:31.754 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:31.753000] [MiniLifeCacheManager.saveBleKeyModel (mini_life_cache_manager.dart:93:22)] MiniLifeCacheManager: 蓝牙钥匙缓存保存成功
07-29 09:55:31.835 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:31.835000] [MQTTManager._getTokenWithVin (mqtt_manager.dart:255:20)] MQTT: Token获取成功 - ClientId: LK6AEAE44SB192188_4796
07-29 09:55:31.836 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:31.836000] [MQTTManager.getTokenAndLoginWithVin (mqtt_manager.dart:197:22)] MQTT: 获取账号密码成功
07-29 09:55:31.837 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:31.837000] [MQTTManager._connectWithCredentials (mqtt_manager.dart:326:18)] MQTTManager: 连接参数
07-29 09:55:31.838 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:31.837000] [MQTTManager._connectWithCredentials (mqtt_manager.dart:327:18)] MQTTManager:   username: e681b3b345bbe5712df6cdb0fd733957
07-29 09:55:31.838 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:31.838000] [MQTTManager._connectWithCredentials (mqtt_manager.dart:328:18)] MQTTManager:   password: c7dde1a7594a65a2de7845f1e8a89036
07-29 09:55:31.838 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:31.838000] [MQTTManager._connectWithCredentials (mqtt_manager.dart:329:18)] MQTTManager:   clientId: LK6AEAE44SB192188_4796
07-29 09:55:32.072 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.071000] [MQTTManager._handleConnected (mqtt_manager.dart:133:18)] MQTTManager: 连接成功
07-29 09:55:32.220 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.219000] [UseCarPageState.getHasCarListData (use_car_page.dart:1820:20)] UseCarPage: 开始保存MiniLife数据到缓存
07-29 09:55:32.222 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.221000] [MiniLifeCacheManager.saveMiniLifeData (mini_life_cache_manager.dart:107:18)] MiniLifeCacheManager: 开始批量保存MiniLife数据
07-29 09:55:32.222 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.222000] [MiniLifeCacheManager.saveMiniLifeData (mini_life_cache_manager.dart:108:18)] MiniLifeCacheManager: 活动列表数量: 0, MiniLife模型: null
07-29 09:55:32.225 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.224000] [MiniLifeCacheManager.saveActivityList (mini_life_cache_manager.dart:22:20)] MiniLifeCacheManager: 开始保存活动列表到缓存，数量: 0
07-29 09:55:32.226 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.226000] [MiniLifeCacheManager.saveMiniLifeModel (mini_life_cache_manager.dart:75:22)] MiniLifeCacheManager: MiniLife模型为null，跳过缓存保存
07-29 09:55:32.230 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.229000] [MiniLifeCacheManager.saveActivityList (mini_life_cache_manager.dart:56:12)] MiniLifeCacheManager: 活动列表缓存保存成功，数量: 0
07-29 09:55:32.231 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.230000] [MiniLifeCacheManager.saveMiniLifeData (mini_life_cache_manager.dart:116:18)] MiniLifeCacheManager: MiniLife数据批量保存完成
07-29 09:55:32.231 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.231000] [UseCarPageState.getHasCarListData (use_car_page.dart:1825:20)] UseCarPage: MiniLife数据缓存保存完成
07-29 09:55:32.232 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.232000] [use_car_page.dart:1834:22] DEBUG: setState中设置变量完成 - _toolServiceList长度: 7
07-29 09:55:32.233 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.233000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:32.233 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.233000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:32.234 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.234000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:32.234 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.234000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:32.246 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.245000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:55:32.258 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.257000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:55:32.264 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.263000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  1
07-29 09:55:32.407 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.406000] [UseCarPageState.getHasCarListData (use_car_page.dart:1846:20)] 获取用车服务列表成功，数量: 8
07-29 09:55:32.408 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.407000] [UseCarPageState.getHasCarListData (use_car_page.dart:1849:22)]   [0] 流量商城: https://oss-rewrite.00bang.cn/image/LLB_OSSA8AC35ACF9DA4BE59C41FA3170EF0BB4.png
07-29 09:55:32.408 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.408000] [UseCarPageState.getHasCarListData (use_car_page.dart:1849:22)]   [1] 车主认证: https://oss-rewrite.00bang.cn/image/LLB_OSS7D1E5A35F57A43ABA88ACEC1FF63ABBB.png
07-29 09:55:32.409 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.408000] [UseCarPageState.getHasCarListData (use_car_page.dart:1849:22)]   [2] 用车指南: https://oss-rewrite.00bang.cn/image/LLB_OSSE96C6CBE4CA0439E80D50B32F64797C1.png
07-29 09:55:32.409 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.409000] [UseCarPageState.getHasCarListData (use_car_page.dart:1849:22)]   [3] 服务进度: https://cdn-df.00bang.cn/images/T1kJKTBjWT1RCvBVdK.png
07-29 09:55:32.410 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.409000] [UseCarPageState.getHasCarListData (use_car_page.dart:1849:22)]   [4] 预约服务: https://cdn-df.00bang.cn/images/T1LAKTBgxT1RCvBVdK.png
07-29 09:55:32.410 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.410000] [UseCarPageState.getHasCarListData (use_car_page.dart:1849:22)]   [5] 道路救援: https://cdn-df.00bang.cn/images/T1LKKTBgbT1RCvBVdK.png
07-29 09:55:32.410 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.410000] [UseCarPageState.getHasCarListData (use_car_page.dart:1849:22)]   [6] 优惠劵: https://cdn-df.00bang.cn/images/T1dPCTByYT1RCvBVdK.png
07-29 09:55:32.411 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.410000] [UseCarPageState.getHasCarListData (use_car_page.dart:1849:22)]   [7] 全部功能: https://cdn-df.00bang.cn/images/T1nJKTBTCT1RCvBVdK.png
07-29 09:55:32.412 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.411000] [use_car_page.dart:1855:22] 设置_useCarServiceList完成，准备缓存
07-29 09:55:32.435 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.435000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:55:32.455 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.450000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:55:32.461 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.460000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  1
07-29 09:55:32.504 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.503000] [MQTTManager._connectWithCredentials (mqtt_manager.dart:345:20)] MQTTManager: 连接请求已发送 - 连接已启动
07-29 09:55:32.504 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.504000] [MQTTManager._handleSubscribeSuccess (mqtt_manager.dart:173:18)] MQTTManager: 订阅成功
07-29 09:55:32.505 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.505000] [UseCarPageState._cacheServiceLists (use_car_page.dart:4465:22)] 服务列表缓存成功，key: travel_service_lists_MTcwMDc0ODg=_LK6AEAE44SB192188
07-29 09:55:32.818 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.816000] [UseCarPageState.getNotificationData (use_car_page.dart:1918:20)] 通知服务-- 2
07-29 09:55:32.822 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.821000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:55:32.849 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.847000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:55:32.851 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.849000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:55:32.855 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.854000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:55:32.862 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:32.861000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  1
07-29 09:55:33.334 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:33.332000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:55:33.366 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:33.362000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:55:33.369 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:33.366000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:55:33.374 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:33.371000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:55:33.385 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:33.384000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  1
07-29 09:55:35.433 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:35.431000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:55:35.686 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:35.685000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 1, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.33379, longitude: 109.366797, position: null, collectTime: 2025-07-29 09:55:20, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 2, voltage: 420.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 32, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.23, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 40, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:55:35.715 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:35.715000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:55:10，输入时间为2025-07-29 09:55:20，相差10000，状态已过期，更新输入时间为状态时间
07-29 09:55:35.716 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:35.716000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:55:20
07-29 09:55:35.736 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:35.735000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:,statusMap[_currentServiceCode]:null ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:1 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:55:35.737 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:35.736000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:55:35.738 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:35.738000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:35.738 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:35.738000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:35.739 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:35.738000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:35.739 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:35.739000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:35.741 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:35.740000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:55:35.760 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:35.758000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:55:35.760 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:35.760000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:55:35.762 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:35.761000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:55:35.769 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:35.767000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  1
07-29 09:55:35.806 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:35.805000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:55:37.139 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:37.139000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 09:55:37.607 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:37.606000] [UseCarPageState.doCarControlNetwork (UseCarPageState.doCarControlNetwork (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2808)] 开始车控，serviceStatusResultType: 0 ，backStatusValue：0 , serviceCode: acStatus , mAccType:4 , 车控accParameter:1 ,车控params:{buttonLayout: 1, accOnOff: 0, status: 0}
07-29 09:55:38.433 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.431000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:55:38.855 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.855000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:152:18)] MQTTManager: 接收消息参数
07-29 09:55:38.856 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.856000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:153:18)] MQTTManager:   主题: LK6AEAE44SB192188/prod/sgmw/vehicle/app/status
07-29 09:55:38.856 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.856000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:154:18)] MQTTManager:   数据长度: 156
07-29 09:55:38.857 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.856000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:155:18)] MQTTManager:   QoS级别: 1
07-29 09:55:38.857 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.857000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:156:18)] MQTTManager:   是否Base64编码: true
07-29 09:55:38.860 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.858000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:160:18)] MQTTManager:   匹配的主题枚举: MQTTTopic.carControlAllStatus
07-29 09:55:38.904 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.904000] [MQTTManager._convertCarStatusToMap (mqtt_manager.dart:698:18)] MQTTManager:将SgmwAppCarStatus protobuf对象转换为Map ,carStatus.collectTime:1753753844120 , carStatus.collectTime.toInt():1753753844120
07-29 09:55:38.911 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.910000] [MQTTManager._notifyListeners (mqtt_manager.dart:492:18)] MQTTManager: 通知监听器 - 主题: MQTTTopic.carControlAllStatus, 监听器数量: 1
07-29 09:55:38.912 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.911000] [UseCarPageState._onCarControlAllStatusReceived (use_car_page.dart:735:18)] [MQTT]===收到车辆控制全状态MQTT消息,data: {collectTime: 1753753844120, acStatus: 0, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 0, acTemperatureGear: , acWindGear: 0, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat31WindStatus: 7, seat4WindStatus: 7, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753754138910}
07-29 09:55:38.913 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.912000] [UseCarPageState._handleCarControlAllStatusFromMap (use_car_page.dart:822:18)] [MQTT]===处理从protobuf转换而来的Map格式数据，_handleCarControlAllStatusFromMap: {collectTime: 1753753844120, acStatus: 0, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 0, acTemperatureGear: , acWindGear: 0, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat31WindStatus: 7, seat4WindStatus: 7, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753754138910}
07-29 09:55:38.914 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.914000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:866:24)] [MQTT]===时间戳1753753844120转换为2025-07-29 09:50:44
07-29 09:55:38.933 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.932000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:55:20，输入时间为2025-07-29 09:50:44，相差-276000，状态未过期
07-29 09:55:38.934 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.933000] [UseCarPageState._refreshCarControlUI (use_car_page.dart:1020:18)] 车辆控制状态已更新，刷新UI
07-29 09:55:38.934 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.934000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:875:22)] [MQTT]===是否需要更新车辆数据: isNeedUpdate:false
07-29 09:55:38.949 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.948000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 1, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333797, longitude: 109.366795, position: null, collectTime: 2025-07-29 09:55:30, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 2.9, voltage: 420.2, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 32, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.04, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 40, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:55:38.950 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.950000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:55:20，输入时间为2025-07-29 09:55:30，相差10000，状态已过期，更新输入时间为状态时间
07-29 09:55:38.950 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.950000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:55:30
07-29 09:55:38.968 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.967000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:,statusMap[_currentServiceCode]:null ,_MQTTBackStatus:0 , _currentACStatus:3,_statusModel?.acStatus:1 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:55:38.968 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.968000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:55:38.969 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.969000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:38.970 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.969000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:38.970 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.970000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:38.970 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.970000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:38.977 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.976000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:55:38.995 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.994000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:55:38.997 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.996000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:55:38.999 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:38.998000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:55:39.007 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:39.006000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  1
07-29 09:55:39.097 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:39.096000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:55:39.099 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:39.098000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:152:18)] MQTTManager: 接收消息参数
07-29 09:55:39.099 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:39.099000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:153:18)] MQTTManager:   主题: LK6AEAE44SB192188/prod/sgmw/vehicle/app/status
07-29 09:55:39.099 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:39.099000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:154:18)] MQTTManager:   数据长度: 128
07-29 09:55:39.100 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:39.100000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:155:18)] MQTTManager:   QoS级别: 1
07-29 09:55:39.100 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:39.100000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:156:18)] MQTTManager:   是否Base64编码: true
07-29 09:55:39.101 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:39.100000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:160:18)] MQTTManager:   匹配的主题枚举: MQTTTopic.carControlAllStatus
07-29 09:55:39.102 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:39.102000] [MQTTManager._convertCarStatusToMap (mqtt_manager.dart:698:18)] MQTTManager:将SgmwAppCarStatus protobuf对象转换为Map ,carStatus.collectTime:1753754107279 , carStatus.collectTime.toInt():1753754107279
07-29 09:55:39.103 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:39.102000] [MQTTManager._notifyListeners (mqtt_manager.dart:492:18)] MQTTManager: 通知监听器 - 主题: MQTTTopic.carControlAllStatus, 监听器数量: 1
07-29 09:55:39.103 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:39.103000] [UseCarPageState._onCarControlAllStatusReceived (use_car_page.dart:735:18)] [MQTT]===收到车辆控制全状态MQTT消息,data: {collectTime: 1753754107279, acStatus: 0, doorLockStatus: 0, windowStatus: , engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: , window2Status: , window3Status: , window4Status: , topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 0, acTemperatureGear: , acWindGear: 0, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat31WindStatus: 7, seat4WindStatus: 7, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753754139102}
07-29 09:55:39.103 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:39.103000] [UseCarPageState._handleCarControlAllStatusFromMap (use_car_page.dart:822:18)] [MQTT]===处理从protobuf转换而来的Map格式数据，_handleCarControlAllStatusFromMap: {collectTime: 1753754107279, acStatus: 0, doorLockStatus: 0, windowStatus: , engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: , window2Status: , window3Status: , window4Status: , topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 0, acTemperatureGear: , acWindGear: 0, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat31WindStatus: 7, seat4WindStatus: 7, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753754139102}
07-29 09:55:39.104 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:39.104000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:866:24)] [MQTT]===时间戳1753754107279转换为2025-07-29 09:55:07
07-29 09:55:39.105 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:39.104000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:55:30，输入时间为2025-07-29 09:55:07，相差-23000，状态未过期
07-29 09:55:39.105 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:39.105000] [UseCarPageState._refreshCarControlUI (use_car_page.dart:1020:18)] 车辆控制状态已更新，刷新UI
07-29 09:55:39.106 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:39.105000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:875:22)] [MQTT]===是否需要更新车辆数据: isNeedUpdate:false
07-29 09:55:39.126 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:39.125000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:39.126 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:39.126000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:39.126 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:39.126000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:39.127 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:39.126000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:39.127 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:39.127000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:55:38
07-29 09:55:39.135 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:39.135000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:55:39.150 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:39.149000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:55:39.152 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:39.151000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:55:39.154 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:39.153000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:55:39.162 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:39.160000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:55:41.437 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:41.435000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:55:41.702 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:41.702000] [UseCarPageState.handleControlActionWithItem (use_car_page.dart:2340:11)] 点击了第0个车控按钮:空调 座椅通风加热-F511C LV2高配主副通风加热都有  后排只有加热 当前状态:空调
07-29 09:55:41.702 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:41.702000] [UseCarPageState.handleControlActionWithItem (use_car_page.dart:2401:17)] 处理 车控 类型 4 , serviceCode =acStatus , acStatus = 0
07-29 09:55:41.747 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:41.746000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat1HotStatus 获取的状态是: 7
07-29 09:55:41.749 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:41.748000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat2HotStatus 获取的状态是: 7
07-29 09:55:41.751 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:41.750000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat3HotStatus 获取的状态是: 7
07-29 09:55:41.752 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:41.751000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat4HotStatus 获取的状态是: 7
07-29 09:55:41.754 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:41.753000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat1WindStatus 获取的状态是: 7
07-29 09:55:41.756 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:41.755000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat2WindStatus 获取的状态是: 7
07-29 09:55:41.758 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:41.757000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat3WindStatus 获取的状态是:
07-29 09:55:41.760 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:41.759000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:849:20)] 格式错误: seat3WindStatus 状态异常了，取本地状态，当前本地状态是: 0
07-29 09:55:41.761 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:41.760000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat4WindStatus 获取的状态是:
07-29 09:55:41.762 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:41.761000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:849:20)] 格式错误: seat4WindStatus 状态异常了，取本地状态，当前本地状态是: 0
07-29 09:55:41.978 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:41.978000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 1, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333797, longitude: 109.366795, position: null, collectTime: 2025-07-29 09:55:30, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 2.9, voltage: 420.2, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 32, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.04, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 40, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:55:41.979 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:41.978000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:55:38，输入时间为2025-07-29 09:55:30，相差-8000，状态未过期
07-29 09:55:42.077 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:42.076000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 09:55:44.433 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:44.432000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:55:44.731 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:44.731000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 1, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333797, longitude: 109.366795, position: null, collectTime: 2025-07-29 09:55:30, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 2.9, voltage: 420.2, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 32, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.04, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 40, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:55:44.732 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:44.732000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:55:38，输入时间为2025-07-29 09:55:30，相差-8000，状态未过期
07-29 09:55:47.090 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:47.088000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 09:55:47.430 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:47.429000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:55:47.815 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:47.814000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 1, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333797, longitude: 109.366795, position: null, collectTime: 2025-07-29 09:55:30, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 2.9, voltage: 420.2, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 32, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.04, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 40, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:55:47.818 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:47.816000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:55:38，输入时间为2025-07-29 09:55:30，相差-8000，状态未过期
07-29 09:55:50.233 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:50.232000] [UseCarPageState.doCarControlNetwork (UseCarPageState.doCarControlNetwork (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2808)] 开始车控，serviceStatusResultType: 1 ，backStatusValue：2 , serviceCode: acStatus , mAccType:4 , 车控accParameter:4 ,车控params:{buttonLayout: 1, accOnOff: 1, temperature: 17, blowerLvl: 7, duration: 10, status: 1, acType: 4, quickDownTemperature: 1}
07-29 09:55:50.255 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:50.254000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat1HotStatus 获取的状态是: 7
07-29 09:55:50.255 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:50.255000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat2HotStatus 获取的状态是: 7
07-29 09:55:50.256 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:50.256000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat3HotStatus 获取的状态是: 7
07-29 09:55:50.257 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:50.256000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat4HotStatus 获取的状态是: 7
07-29 09:55:50.258 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:50.257000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat1WindStatus 获取的状态是: 7
07-29 09:55:50.258 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:50.258000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat2WindStatus 获取的状态是: 7
07-29 09:55:50.259 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:50.259000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat3WindStatus 获取的状态是:
07-29 09:55:50.259 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:50.259000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:849:20)] 格式错误: seat3WindStatus 状态异常了，取本地状态，当前本地状态是: 0
07-29 09:55:50.260 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:50.260000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat4WindStatus 获取的状态是:
07-29 09:55:50.260 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:50.260000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:849:20)] 格式错误: seat4WindStatus 状态异常了，取本地状态，当前本地状态是: 0
07-29 09:55:50.437 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:50.435000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:55:51.083 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.082000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333792, longitude: 109.366795, position: null, collectTime: 2025-07-29 09:55:40, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 217.2, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 32, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.05, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 40, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:55:51.102 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.084000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:55:38，输入时间为2025-07-29 09:55:40，相差2000，状态已过期，更新输入时间为状态时间
07-29 09:55:51.103 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.102000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:55:40
07-29 09:55:51.112 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.111000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:,statusMap[_currentServiceCode]:null ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:55:51.112 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.112000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3592:20)] 旧空调状态：0, 新空调状态：0
07-29 09:55:51.112 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.112000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3608:22)] 空调状态没有发生改变
07-29 09:55:51.115 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.115000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:55:51.117 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.116000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:51.118 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.117000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:51.119 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.118000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:51.119 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.119000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:51.121 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.120000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat1HotStatus 获取的状态是: 7
07-29 09:55:51.121 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.121000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat2HotStatus 获取的状态是: 7
07-29 09:55:51.122 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.122000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat3HotStatus 获取的状态是: 7
07-29 09:55:51.122 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.122000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat4HotStatus 获取的状态是: 7
07-29 09:55:51.123 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.122000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat1WindStatus 获取的状态是: 7
07-29 09:55:51.123 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.123000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat2WindStatus 获取的状态是: 7
07-29 09:55:51.123 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.123000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat3WindStatus 获取的状态是: 7
07-29 09:55:51.124 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.123000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat4WindStatus 获取的状态是: 7
07-29 09:55:51.142 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.142000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:55:51.153 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.152000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:55:51.154 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.153000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:55:51.155 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.154000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:55:51.159 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.158000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:55:51.202 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.201000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:55:51.807 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.805000] [MQTTManager.startMqttTimeOutTimer (mqtt_manager.dart:620:18)] MQTTManager: 启动接收MQTT异步结果推送超时定时器
07-29 09:55:51.808 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.807000] [MQTTManager.stopMqttTimeOutTimer (mqtt_manager.dart:627:18)] MQTTManager: 停止接收MQTT异步结果推送超时定时器
07-29 09:55:51.869 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.868000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:152:18)] MQTTManager: 接收消息参数
07-29 09:55:51.870 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.869000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:153:18)] MQTTManager:   主题: LK6AEAE44SB192188/prod/sgmw/vehicle/app/status
07-29 09:55:51.871 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.870000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:154:18)] MQTTManager:   数据长度: 128
07-29 09:55:51.872 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.871000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:155:18)] MQTTManager:   QoS级别: 1
07-29 09:55:51.872 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.872000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:156:18)] MQTTManager:   是否Base64编码: true
07-29 09:55:51.873 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.872000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:160:18)] MQTTManager:   匹配的主题枚举: MQTTTopic.carControlAllStatus
07-29 09:55:51.875 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.874000] [MQTTManager._convertCarStatusToMap (mqtt_manager.dart:698:18)] MQTTManager:将SgmwAppCarStatus protobuf对象转换为Map ,carStatus.collectTime:1753754107400 , carStatus.collectTime.toInt():1753754107400
07-29 09:55:51.876 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.875000] [MQTTManager._notifyListeners (mqtt_manager.dart:492:18)] MQTTManager: 通知监听器 - 主题: MQTTTopic.carControlAllStatus, 监听器数量: 1
07-29 09:55:51.877 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.876000] [UseCarPageState._onCarControlAllStatusReceived (use_car_page.dart:735:18)] [MQTT]===收到车辆控制全状态MQTT消息,data: {collectTime: 1753754107400, acStatus: 0, doorLockStatus: 0, windowStatus: , engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: , window2Status: , window3Status: , window4Status: , topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 0, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat31WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753754151875}
07-29 09:55:51.879 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.878000] [UseCarPageState._handleCarControlAllStatusFromMap (use_car_page.dart:822:18)] [MQTT]===处理从protobuf转换而来的Map格式数据，_handleCarControlAllStatusFromMap: {collectTime: 1753754107400, acStatus: 0, doorLockStatus: 0, windowStatus: , engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: , window2Status: , window3Status: , window4Status: , topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 0, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat31WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753754151875}
07-29 09:55:51.880 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.879000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:866:24)] [MQTT]===时间戳1753754107400转换为2025-07-29 09:55:07
07-29 09:55:51.881 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.880000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:55:40，输入时间为2025-07-29 09:55:07，相差-33000，状态未过期
07-29 09:55:51.882 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.882000] [UseCarPageState._refreshCarControlUI (use_car_page.dart:1020:18)] 车辆控制状态已更新，刷新UI
07-29 09:55:51.883 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:51.882000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:875:22)] [MQTT]===是否需要更新车辆数据: isNeedUpdate:false
07-29 09:55:52.077 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:52.076000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 09:55:53.431 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.429000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:55:53.938 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.937000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:152:18)] MQTTManager: 接收消息参数
07-29 09:55:53.939 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.938000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:153:18)] MQTTManager:   主题: LK6AEAE44SB192188/prod/sgmw/vehicle/app/status
07-29 09:55:53.940 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.939000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:154:18)] MQTTManager:   数据长度: 156
07-29 09:55:53.940 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.940000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:155:18)] MQTTManager:   QoS级别: 1
07-29 09:55:53.941 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.940000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:156:18)] MQTTManager:   是否Base64编码: true
07-29 09:55:53.942 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.941000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:160:18)] MQTTManager:   匹配的主题枚举: MQTTTopic.carControlAllStatus
07-29 09:55:53.943 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.943000] [MQTTManager._convertCarStatusToMap (mqtt_manager.dart:698:18)] MQTTManager:将SgmwAppCarStatus protobuf对象转换为Map ,carStatus.collectTime:1753754109057 , carStatus.collectTime.toInt():1753754109057
07-29 09:55:53.944 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.944000] [MQTTManager._notifyListeners (mqtt_manager.dart:492:18)] MQTTManager: 通知监听器 - 主题: MQTTTopic.carControlAllStatus, 监听器数量: 1
07-29 09:55:53.945 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.944000] [UseCarPageState._onCarControlAllStatusReceived (use_car_page.dart:735:18)] [MQTT]===收到车辆控制全状态MQTT消息,data: {collectTime: 1753754109057, acStatus: 0, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 0, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat31WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753754153943}
07-29 09:55:53.946 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.945000] [UseCarPageState._handleCarControlAllStatusFromMap (use_car_page.dart:822:18)] [MQTT]===处理从protobuf转换而来的Map格式数据，_handleCarControlAllStatusFromMap: {collectTime: 1753754109057, acStatus: 0, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 0, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat31WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753754153943}
07-29 09:55:53.947 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.946000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:866:24)] [MQTT]===时间戳1753754109057转换为2025-07-29 09:55:09
07-29 09:55:53.949 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.948000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:55:40，输入时间为2025-07-29 09:55:09，相差-31000，状态未过期
07-29 09:55:53.950 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.950000] [UseCarPageState._refreshCarControlUI (use_car_page.dart:1020:18)] 车辆控制状态已更新，刷新UI
07-29 09:55:53.951 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.951000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:875:22)] [MQTT]===是否需要更新车辆数据: isNeedUpdate:false
07-29 09:55:53.953 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.952000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:152:18)] MQTTManager: 接收消息参数
07-29 09:55:53.954 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.953000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:153:18)] MQTTManager:   主题: LK6AEAE44SB192188/prod/sgmw/vehicle/app/status
07-29 09:55:53.955 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.954000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:154:18)] MQTTManager:   数据长度: 156
07-29 09:55:53.955 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.955000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:155:18)] MQTTManager:   QoS级别: 1
07-29 09:55:53.956 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.955000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:156:18)] MQTTManager:   是否Base64编码: true
07-29 09:55:53.957 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.956000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:160:18)] MQTTManager:   匹配的主题枚举: MQTTTopic.carControlAllStatus
07-29 09:55:53.959 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.958000] [MQTTManager._convertCarStatusToMap (mqtt_manager.dart:698:18)] MQTTManager:将SgmwAppCarStatus protobuf对象转换为Map ,carStatus.collectTime:1753754109360 , carStatus.collectTime.toInt():1753754109360
07-29 09:55:53.960 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.959000] [MQTTManager._notifyListeners (mqtt_manager.dart:492:18)] MQTTManager: 通知监听器 - 主题: MQTTTopic.carControlAllStatus, 监听器数量: 1
07-29 09:55:53.961 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.960000] [UseCarPageState._onCarControlAllStatusReceived (use_car_page.dart:735:18)] [MQTT]===收到车辆控制全状态MQTT消息,data: {collectTime: 1753754109360, acStatus: 1, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 1, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat31WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753754153959}
07-29 09:55:53.962 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.961000] [UseCarPageState._handleCarControlAllStatusFromMap (use_car_page.dart:822:18)] [MQTT]===处理从protobuf转换而来的Map格式数据，_handleCarControlAllStatusFromMap: {collectTime: 1753754109360, acStatus: 1, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 1, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat31WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753754153959}
07-29 09:55:53.963 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.962000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:866:24)] [MQTT]===时间戳1753754109360转换为2025-07-29 09:55:09
07-29 09:55:53.964 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.964000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:55:40，输入时间为2025-07-29 09:55:09，相差-31000，状态未过期
07-29 09:55:53.965 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.965000] [UseCarPageState._refreshCarControlUI (use_car_page.dart:1020:18)] 车辆控制状态已更新，刷新UI
07-29 09:55:53.966 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.966000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:875:22)] [MQTT]===是否需要更新车辆数据: isNeedUpdate:false
07-29 09:55:53.989 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.988000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333792, longitude: 109.366795, position: null, collectTime: 2025-07-29 09:55:40, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 217.2, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 32, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.05, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 40, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:55:53.990 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.989000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:55:40，输入时间为2025-07-29 09:55:40，相差0，状态已过期，更新输入时间为状态时间
07-29 09:55:53.990 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.990000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:55:40
07-29 09:55:53.994 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.994000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:1 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:true , IS_AC_HOT_WIND_SETTING:false
07-29 09:55:53.995 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.995000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:53.996 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.995000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:53.996 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.996000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:53.997 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:53.996000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:54.016 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:54.015000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:55:54.025 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:54.024000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:55:54.026 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:54.025000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:55:54.028 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:54.027000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:55:54.032 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:54.031000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:55:55.476 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:55.475000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:152:18)] MQTTManager: 接收消息参数
07-29 09:55:55.477 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:55.476000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:153:18)] MQTTManager:   主题: LK6AEAE44SB192188/prod/sgmw/vehicle/app/status
07-29 09:55:55.478 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:55.477000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:154:18)] MQTTManager:   数据长度: 156
07-29 09:55:55.479 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:55.478000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:155:18)] MQTTManager:   QoS级别: 1
07-29 09:55:55.479 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:55.479000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:156:18)] MQTTManager:   是否Base64编码: true
07-29 09:55:55.480 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:55.480000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:160:18)] MQTTManager:   匹配的主题枚举: MQTTTopic.carControlAllStatus
07-29 09:55:55.482 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:55.481000] [MQTTManager._convertCarStatusToMap (mqtt_manager.dart:698:18)] MQTTManager:将SgmwAppCarStatus protobuf对象转换为Map ,carStatus.collectTime:1753754108057 , carStatus.collectTime.toInt():1753754108057
07-29 09:55:55.483 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:55.482000] [MQTTManager._notifyListeners (mqtt_manager.dart:492:18)] MQTTManager: 通知监听器 - 主题: MQTTTopic.carControlAllStatus, 监听器数量: 1
07-29 09:55:55.484 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:55.483000] [UseCarPageState._onCarControlAllStatusReceived (use_car_page.dart:735:18)] [MQTT]===收到车辆控制全状态MQTT消息,data: {collectTime: 1753754108057, acStatus: 0, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 0, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat31WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753754155482}
07-29 09:55:55.485 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:55.484000] [UseCarPageState._handleCarControlAllStatusFromMap (use_car_page.dart:822:18)] [MQTT]===处理从protobuf转换而来的Map格式数据，_handleCarControlAllStatusFromMap: {collectTime: 1753754108057, acStatus: 0, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 0, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat31WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753754155482}
07-29 09:55:55.486 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:55.485000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:866:24)] [MQTT]===时间戳1753754108057转换为2025-07-29 09:55:08
07-29 09:55:55.488 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:55.487000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:55:40，输入时间为2025-07-29 09:55:08，相差-32000，状态未过期
07-29 09:55:55.489 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:55.488000] [UseCarPageState._refreshCarControlUI (use_car_page.dart:1020:18)] 车辆控制状态已更新，刷新UI
07-29 09:55:55.490 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:55.489000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:875:22)] [MQTT]===是否需要更新车辆数据: isNeedUpdate:false
07-29 09:55:56.431 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:56.429000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:55:57.033 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:57.031000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333799, longitude: 109.366794, position: null, collectTime: 2025-07-29 09:55:50, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 0, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 33, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.05, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 40, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:55:57.035 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:57.034000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:55:40，输入时间为2025-07-29 09:55:50，相差10000，状态已过期，更新输入时间为状态时间
07-29 09:55:57.036 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:57.035000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:55:50
07-29 09:55:57.048 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:57.047000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:1 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:true , IS_AC_HOT_WIND_SETTING:false
07-29 09:55:57.050 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:57.049000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:57.050 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:57.050000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:57.051 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:57.050000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:57.052 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:57.051000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:57.056 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:57.055000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:55:57.078 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:57.074000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:55:57.082 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:57.078000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:55:57.087 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:57.083000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:55:57.100 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:57.096000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:55:57.177 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:57.177000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 09:55:58.040 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.038000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:152:18)] MQTTManager: 接收消息参数
07-29 09:55:58.042 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.040000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:153:18)] MQTTManager:   主题: LK6AEAE44SB192188/prod/sgmw/vehicle/control
07-29 09:55:58.043 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.042000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:154:18)] MQTTManager:   数据长度: 48
07-29 09:55:58.044 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.043000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:155:18)] MQTTManager:   QoS级别: 1
07-29 09:55:58.045 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.044000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:156:18)] MQTTManager:   是否Base64编码: true
07-29 09:55:58.046 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.045000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:160:18)] MQTTManager:   匹配的主题枚举: MQTTTopic.carRemoteAsyncResult
07-29 09:55:58.054 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.053000] [MQTTManager._notifyListeners (mqtt_manager.dart:492:18)] MQTTManager: 通知监听器 - 主题: MQTTTopic.carRemoteAsyncResult, 监听器数量: 1
07-29 09:55:58.057 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.056000] [UseCarPageState._onCarRemoteAsyncResultReceived (use_car_page.dart:198:18)] [MQTT]===处理接收到的车辆远程异步结果消息,data: {code: 0, message: 操作成功, serviceCode: acStatus, collectTime: 1753754110000, timestamp: 1753754158052}
07-29 09:55:58.063 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.062000] [UseCarPageState._handleCarRemoteAsyncResultFromMap (use_car_page.dart:383:18)] [MQTT]===收到车辆远程异步结果MQTT消息,处理Map格式的车辆远程异步结果，_handleCarRemoteAsyncResultFromMap,data:：{code: 0, message: 操作成功, serviceCode: acStatus, collectTime: 1753754110000, timestamp: 1753754158052}
07-29 09:55:58.063 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.063000] [UseCarPageState._handleCarRemoteAsyncResultFromMap (use_car_page.dart:385:18)]   [MQTT]===代码: 0
07-29 09:55:58.065 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.063000] [UseCarPageState._handleCarRemoteAsyncResultFromMap (use_car_page.dart:386:18)]   [MQTT]===消息: 操作成功
07-29 09:55:58.066 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.065000] [UseCarPageState._handleCarRemoteAsyncResultFromMap (use_car_page.dart:387:18)]   [MQTT]===服务代码: acStatus
07-29 09:55:58.066 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.066000] [UseCarPageState._handleCarRemoteAsyncResultFromMap (use_car_page.dart:388:18)]   [MQTT]===采集时间: 1753754110000
07-29 09:55:58.067 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.067000] [UseCarPageState._handleCarRemoteAsyncResultFromMap (use_car_page.dart:389:18)]   [MQTT]===时间戳: 1753754158052
07-29 09:55:58.069 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.069000] [UseCarPageState.hasCurrentRemoteControlInPerformed (use_car_page.dart:364:18)] [MQTT]===是否有本地远控异步指令正在执行,result：true
07-29 09:55:58.071 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.070000] [UseCarPageState._handleCarRemoteAsyncResultFromMap (use_car_page.dart:409:30)] [MQTT]===时间戳1753754110000转换为2025-07-29 09:55:10
07-29 09:55:58.071 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.071000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:55:10
07-29 09:55:58.079 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.078000] [use_car_page.dart:469:34] [MQTT]===[CAR_STATUS]===acStatus的值从2更新为2
07-29 09:55:58.080 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.079000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3592:20)] 旧空调状态：0, 新空调状态：2
07-29 09:55:58.081 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.080000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3596:22)] 空调状态发生了改变
07-29 09:55:58.084 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.083000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:58.084 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.084000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:58.084 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.084000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:58.084 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.084000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:58.084 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.084000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:58.085 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.084000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:58.085 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.085000] [MQTTManager.stopMqttTimeOutTimer (mqtt_manager.dart:627:18)] MQTTManager: 停止接收MQTT异步结果推送超时定时器
07-29 09:55:58.087 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.086000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:152:18)] MQTTManager: 接收消息参数
07-29 09:55:58.087 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.087000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:153:18)] MQTTManager:   主题: LK6AEAE44SB192188/prod/sgmw/vehicle/app/status
07-29 09:55:58.087 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.087000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:154:18)] MQTTManager:   数据长度: 156
07-29 09:55:58.088 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.087000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:155:18)] MQTTManager:   QoS级别: 1
07-29 09:55:58.088 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.088000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:156:18)] MQTTManager:   是否Base64编码: true
07-29 09:55:58.089 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.088000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:160:18)] MQTTManager:   匹配的主题枚举: MQTTTopic.carControlAllStatus
07-29 09:55:58.090 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.089000] [MQTTManager._convertCarStatusToMap (mqtt_manager.dart:698:18)] MQTTManager:将SgmwAppCarStatus protobuf对象转换为Map ,carStatus.collectTime:1753754113257 , carStatus.collectTime.toInt():1753754113257
07-29 09:55:58.090 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.090000] [MQTTManager._notifyListeners (mqtt_manager.dart:492:18)] MQTTManager: 通知监听器 - 主题: MQTTTopic.carControlAllStatus, 监听器数量: 1
07-29 09:55:58.091 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.091000] [UseCarPageState._onCarControlAllStatusReceived (use_car_page.dart:735:18)] [MQTT]===收到车辆控制全状态MQTT消息,data: {collectTime: 1753754113257, acStatus: 1, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 3, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat31WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753754158090}
07-29 09:55:58.092 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.091000] [UseCarPageState._handleCarControlAllStatusFromMap (use_car_page.dart:822:18)] [MQTT]===处理从protobuf转换而来的Map格式数据，_handleCarControlAllStatusFromMap: {collectTime: 1753754113257, acStatus: 1, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 3, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat31WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753754158090}
07-29 09:55:58.092 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.092000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:866:24)] [MQTT]===时间戳1753754113257转换为2025-07-29 09:55:13
07-29 09:55:58.093 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.093000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:55:10，输入时间为2025-07-29 09:55:13，相差3000，状态已过期，更新输入时间为状态时间
07-29 09:55:58.094 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.093000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:55:13
07-29 09:55:58.094 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.094000] [UseCarPageState._refreshCarControlUI (use_car_page.dart:1020:18)] 车辆控制状态已更新，刷新UI
07-29 09:55:58.099 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.099000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat1HotStatus 获取的状态是: 7
07-29 09:55:58.100 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.099000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat2HotStatus 获取的状态是: 7
07-29 09:55:58.100 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.100000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat3HotStatus 获取的状态是: 7
07-29 09:55:58.103 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.100000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat4HotStatus 获取的状态是: 7
07-29 09:55:58.103 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.103000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat1WindStatus 获取的状态是: 7
07-29 09:55:58.104 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.104000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat2WindStatus 获取的状态是: 7
07-29 09:55:58.104 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.104000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat3WindStatus 获取的状态是: 7
07-29 09:55:58.105 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.104000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat4WindStatus 获取的状态是: 7
07-29 09:55:58.122 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.121000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:55:58.130 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.129000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:55:58.131 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.130000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:55:58.132 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.131000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:55:58.136 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.135000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  2
07-29 09:55:58.176 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.176000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:875:22)] [MQTT]===是否需要更新车辆数据: isNeedUpdate:true
07-29 09:55:58.178 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.178000] [use_car_page.dart:907:26] [MQTT]===[更新车况]===异步车况变更推送，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:1 ,_MQTTBackStatus:1 , _currentACStatus:0,_statusModel?.acStatus:2 ,_statusModel?.accCntTemp:17.0 , IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:55:58.179 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.178000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3592:20)] 旧空调状态：2, 新空调状态：1
07-29 09:55:58.179 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.179000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3596:22)] 空调状态发生了改变
07-29 09:55:58.179 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.179000] [use_car_page.dart:984:20] [MQTT]===更新后的车辆数据: newStatus:{vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 1, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: 3, mileage: 110, latitude: 24.333792, longitude: 109.366795, position: null, collectTime: 2025-07-29 09:55:13, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 217.2, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 32, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.05, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 40, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 0}
07-29 09:55:58.180 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.179000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:55:58.180 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.180000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 0
07-29 09:55:58.181 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.180000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:58.181 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.181000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 0
07-29 09:55:58.181 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.181000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:58.191 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.190000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat1HotStatus 获取的状态是: 7
07-29 09:55:58.191 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.191000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat2HotStatus 获取的状态是: 7
07-29 09:55:58.192 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.191000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat3HotStatus 获取的状态是: 7
07-29 09:55:58.192 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.192000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat4HotStatus 获取的状态是: 7
07-29 09:55:58.192 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.192000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat1WindStatus 获取的状态是: 7
07-29 09:55:58.193 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.192000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat2WindStatus 获取的状态是: 7
07-29 09:55:58.193 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.193000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat3WindStatus 获取的状态是: 7
07-29 09:55:58.193 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.193000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat4WindStatus 获取的状态是: 0
07-29 09:55:58.194 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.193000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:796:22)] seat4WindStatus 状态异常了，取本地状态，当前本地状态是: 0
07-29 09:55:58.204 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.204000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:55:58.210 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.210000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:55:58.211 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.210000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:55:58.212 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.212000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:55:58.216 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.215000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  1
07-29 09:55:58.244 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:58.244000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:55:59.133 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.131000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:152:18)] MQTTManager: 接收消息参数
07-29 09:55:59.134 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.133000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:153:18)] MQTTManager:   主题: LK6AEAE44SB192188/prod/sgmw/vehicle/app/status
07-29 09:55:59.135 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.134000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:154:18)] MQTTManager:   数据长度: 156
07-29 09:55:59.136 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.135000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:155:18)] MQTTManager:   QoS级别: 1
07-29 09:55:59.138 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.136000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:156:18)] MQTTManager:   是否Base64编码: true
07-29 09:55:59.139 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.138000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:160:18)] MQTTManager:   匹配的主题枚举: MQTTTopic.carControlAllStatus
07-29 09:55:59.141 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.140000] [MQTTManager._convertCarStatusToMap (mqtt_manager.dart:698:18)] MQTTManager:将SgmwAppCarStatus protobuf对象转换为Map ,carStatus.collectTime:1753754113359 , carStatus.collectTime.toInt():1753754113359
07-29 09:55:59.142 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.142000] [MQTTManager._notifyListeners (mqtt_manager.dart:492:18)] MQTTManager: 通知监听器 - 主题: MQTTTopic.carControlAllStatus, 监听器数量: 1
07-29 09:55:59.143 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.143000] [UseCarPageState._onCarControlAllStatusReceived (use_car_page.dart:735:18)] [MQTT]===收到车辆控制全状态MQTT消息,data: {collectTime: 1753754113359, acStatus: 1, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 5, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat31WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753754159141}
07-29 09:55:59.144 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.144000] [UseCarPageState._handleCarControlAllStatusFromMap (use_car_page.dart:822:18)] [MQTT]===处理从protobuf转换而来的Map格式数据，_handleCarControlAllStatusFromMap: {collectTime: 1753754113359, acStatus: 1, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 5, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat31WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753754159141}
07-29 09:55:59.145 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.145000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:866:24)] [MQTT]===时间戳1753754113359转换为2025-07-29 09:55:13
07-29 09:55:59.147 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.146000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:55:13，输入时间为2025-07-29 09:55:13，相差0，状态已过期，更新输入时间为状态时间
07-29 09:55:59.148 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.147000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:55:13
07-29 09:55:59.150 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.149000] [UseCarPageState._refreshCarControlUI (use_car_page.dart:1020:18)] 车辆控制状态已更新，刷新UI
07-29 09:55:59.158 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.157000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:875:22)] [MQTT]===是否需要更新车辆数据: isNeedUpdate:true
07-29 09:55:59.160 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.159000] [use_car_page.dart:907:26] [MQTT]===[更新车况]===异步车况变更推送，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:1 ,_MQTTBackStatus:1 , _currentACStatus:0,_statusModel?.acStatus:1 ,_statusModel?.accCntTemp:17.0 , IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:55:59.160 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.160000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3592:20)] 旧空调状态：1, 新空调状态：1
07-29 09:55:59.161 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.160000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3608:22)] 空调状态没有发生改变
07-29 09:55:59.163 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.163000] [use_car_page.dart:984:20] [MQTT]===更新后的车辆数据: newStatus:{vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 1, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: 5, mileage: 110, latitude: 24.333792, longitude: 109.366795, position: null, collectTime: 2025-07-29 09:55:13, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 217.2, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 32, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.05, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 40, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 0}
07-29 09:55:59.163 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.163000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:55:59.165 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.164000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 0
07-29 09:55:59.166 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.166000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:59.167 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.166000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 0
07-29 09:55:59.168 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.167000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:59.170 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.169000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat1HotStatus 获取的状态是: 7
07-29 09:55:59.170 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.170000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat2HotStatus 获取的状态是: 7
07-29 09:55:59.171 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.170000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat3HotStatus 获取的状态是: 7
07-29 09:55:59.171 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.171000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat4HotStatus 获取的状态是: 7
07-29 09:55:59.172 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.171000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat1WindStatus 获取的状态是: 7
07-29 09:55:59.172 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.172000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat2WindStatus 获取的状态是: 7
07-29 09:55:59.174 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.173000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat3WindStatus 获取的状态是: 7
07-29 09:55:59.175 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.174000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat4WindStatus 获取的状态是: 0
07-29 09:55:59.176 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.175000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:796:22)] seat4WindStatus 状态异常了，取本地状态，当前本地状态是: 0
07-29 09:55:59.194 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.193000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:55:59.201 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.200000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:55:59.202 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.201000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:55:59.203 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.203000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:55:59.207 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.206000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  1
07-29 09:55:59.236 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.235000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:55:59.438 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.436000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:55:59.699 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.698000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333799, longitude: 109.366794, position: null, collectTime: 2025-07-29 09:55:50, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 0, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 33, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.05, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 40, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:55:59.701 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.700000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:55:13，输入时间为2025-07-29 09:55:50，相差37000，状态已过期，更新输入时间为状态时间
07-29 09:55:59.701 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.701000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:55:50
07-29 09:55:59.712 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.712000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:1 , _currentACStatus:0,_statusModel?.acStatus:1 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:55:59.713 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.712000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3592:20)] 旧空调状态：1, 新空调状态：0
07-29 09:55:59.713 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.713000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3596:22)] 空调状态发生了改变
07-29 09:55:59.713 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.713000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:55:59.714 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.714000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:59.715 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.714000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:59.715 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.715000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:55:59.716 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.715000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:55:59.727 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.727000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat1HotStatus 获取的状态是: 7
07-29 09:55:59.728 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.728000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat2HotStatus 获取的状态是: 7
07-29 09:55:59.729 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.729000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat3HotStatus 获取的状态是: 7
07-29 09:55:59.730 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.729000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat4HotStatus 获取的状态是: 7
07-29 09:55:59.731 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.730000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat1WindStatus 获取的状态是: 7
07-29 09:55:59.732 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.731000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat2WindStatus 获取的状态是: 7
07-29 09:55:59.732 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.732000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat3WindStatus 获取的状态是: 7
07-29 09:55:59.733 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.733000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat4WindStatus 获取的状态是: 7
07-29 09:55:59.755 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.755000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:55:59.764 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.762000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:55:59.765 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.764000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:55:59.767 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.766000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:55:59.772 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.771000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:55:59.810 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:55:59.810000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:56:00.085 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:00.083000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:152:18)] MQTTManager: 接收消息参数
07-29 09:56:00.086 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:00.085000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:153:18)] MQTTManager:   主题: LK6AEAE44SB192188/prod/sgmw/vehicle/app/status
07-29 09:56:00.087 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:00.086000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:154:18)] MQTTManager:   数据长度: 156
07-29 09:56:00.087 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:00.087000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:155:18)] MQTTManager:   QoS级别: 1
07-29 09:56:00.088 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:00.087000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:156:18)] MQTTManager:   是否Base64编码: true
07-29 09:56:00.089 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:00.088000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:160:18)] MQTTManager:   匹配的主题枚举: MQTTTopic.carControlAllStatus
07-29 09:56:00.091 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:00.090000] [MQTTManager._convertCarStatusToMap (mqtt_manager.dart:698:18)] MQTTManager:将SgmwAppCarStatus protobuf对象转换为Map ,carStatus.collectTime:1753754114708 , carStatus.collectTime.toInt():1753754114708
07-29 09:56:00.092 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:00.091000] [MQTTManager._notifyListeners (mqtt_manager.dart:492:18)] MQTTManager: 通知监听器 - 主题: MQTTTopic.carControlAllStatus, 监听器数量: 1
07-29 09:56:00.093 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:00.092000] [UseCarPageState._onCarControlAllStatusReceived (use_car_page.dart:735:18)] [MQTT]===收到车辆控制全状态MQTT消息,data: {collectTime: 1753754114708, acStatus: 1, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 6, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat31WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753754160091}
07-29 09:56:00.095 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:00.094000] [UseCarPageState._handleCarControlAllStatusFromMap (use_car_page.dart:822:18)] [MQTT]===处理从protobuf转换而来的Map格式数据，_handleCarControlAllStatusFromMap: {collectTime: 1753754114708, acStatus: 1, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 6, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat31WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753754160091}
07-29 09:56:00.096 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:00.095000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:866:24)] [MQTT]===时间戳1753754114708转换为2025-07-29 09:55:14
07-29 09:56:00.098 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:00.097000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:55:50，输入时间为2025-07-29 09:55:14，相差-36000，状态未过期
07-29 09:56:00.099 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:00.098000] [UseCarPageState._refreshCarControlUI (use_car_page.dart:1020:18)] 车辆控制状态已更新，刷新UI
07-29 09:56:00.099 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:00.099000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:875:22)] [MQTT]===是否需要更新车辆数据: isNeedUpdate:false
07-29 09:56:02.082 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:02.081000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 09:56:02.431 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:02.429000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:56:03.265 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:03.264000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333799, longitude: 109.366794, position: null, collectTime: 2025-07-29 09:55:50, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 0, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 33, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.05, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 40, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:56:03.266 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:03.265000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:55:50，输入时间为2025-07-29 09:55:50，相差0，状态已过期，更新输入时间为状态时间
07-29 09:56:03.267 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:03.266000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:55:50
07-29 09:56:03.272 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:03.271000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:1 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:56:03.272 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:03.272000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3592:20)] 旧空调状态：0, 新空调状态：0
07-29 09:56:03.273 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:03.272000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3608:22)] 空调状态没有发生改变
07-29 09:56:03.274 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:03.273000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:56:03.276 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:03.275000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:56:03.276 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:03.276000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:56:03.276 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:03.276000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:56:03.277 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:03.276000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:56:03.278 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:03.277000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:56:03.284 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:03.284000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat1HotStatus 获取的状态是: 7
07-29 09:56:03.285 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:03.285000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat2HotStatus 获取的状态是: 7
07-29 09:56:03.286 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:03.286000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat3HotStatus 获取的状态是: 7
07-29 09:56:03.287 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:03.286000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat4HotStatus 获取的状态是: 7
07-29 09:56:03.288 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:03.287000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat1WindStatus 获取的状态是: 7
07-29 09:56:03.289 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:03.288000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat2WindStatus 获取的状态是: 7
07-29 09:56:03.289 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:03.289000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat3WindStatus 获取的状态是: 7
07-29 09:56:03.290 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:03.289000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat4WindStatus 获取的状态是: 7
07-29 09:56:03.309 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:03.308000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:56:03.316 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:03.315000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:56:03.318 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:03.316000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:56:03.320 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:03.318000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:56:03.335 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:03.323000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:56:05.432 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:05.429000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:56:05.652 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:05.651000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333799, longitude: 109.366794, position: null, collectTime: 2025-07-29 09:55:50, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 0, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 33, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.05, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 40, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:56:05.654 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:05.653000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:55:50，输入时间为2025-07-29 09:55:50，相差0，状态已过期，更新输入时间为状态时间
07-29 09:56:05.655 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:05.654000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:55:50
07-29 09:56:05.661 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:05.661000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:1 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:56:05.662 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:05.662000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3592:20)] 旧空调状态：0, 新空调状态：0
07-29 09:56:05.662 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:05.662000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3608:22)] 空调状态没有发生改变
07-29 09:56:05.663 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:05.662000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:56:05.663 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:05.663000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:56:05.664 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:05.664000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:56:05.664 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:05.664000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:56:05.665 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:05.664000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:56:05.666 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:05.665000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:56:05.673 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:05.672000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat1HotStatus 获取的状态是: 7
07-29 09:56:05.674 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:05.673000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat2HotStatus 获取的状态是: 7
07-29 09:56:05.674 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:05.674000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat3HotStatus 获取的状态是: 7
07-29 09:56:05.675 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:05.675000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat4HotStatus 获取的状态是: 7
07-29 09:56:05.676 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:05.675000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat1WindStatus 获取的状态是: 7
07-29 09:56:05.677 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:05.676000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat2WindStatus 获取的状态是: 7
07-29 09:56:05.678 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:05.677000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat3WindStatus 获取的状态是: 7
07-29 09:56:05.678 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:05.678000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat4WindStatus 获取的状态是: 7
07-29 09:56:05.707 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:05.705000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:56:05.716 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:05.714000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:56:05.717 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:05.716000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:56:05.719 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:05.718000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:56:05.724 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:05.723000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 09:56:07.082 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:07.081000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 09:56:08.433 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.432000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:56:08.762 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.760000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 1, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333794, longitude: 109.366798, position: null, collectTime: 2025-07-29 09:56:00, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 1.1, voltage: 420.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 33, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.06, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 40, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:56:08.763 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.763000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:55:50，输入时间为2025-07-29 09:56:00，相差10000，状态已过期，更新输入时间为状态时间
07-29 09:56:08.764 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.763000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:56:00
07-29 09:56:08.784 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.783000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:1 ,_MQTTBackStatus:1 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:56:08.784 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.784000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3592:20)] 旧空调状态：0, 新空调状态：1
07-29 09:56:08.785 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.784000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3596:22)] 空调状态发生了改变
07-29 09:56:08.786 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.785000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:56:08.788 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.788000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:56:08.789 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.788000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:56:08.790 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.789000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:56:08.790 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.790000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:56:08.791 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.790000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:56:08.791 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.791000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:56:08.796 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.795000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat1HotStatus 获取的状态是: 7
07-29 09:56:08.797 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.796000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat2HotStatus 获取的状态是: 7
07-29 09:56:08.798 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.798000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat3HotStatus 获取的状态是: 7
07-29 09:56:08.799 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.799000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat4HotStatus 获取的状态是: 7
07-29 09:56:08.800 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.800000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat1WindStatus 获取的状态是: 3
07-29 09:56:08.801 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.800000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat2WindStatus 获取的状态是: 7
07-29 09:56:08.802 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.802000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat3WindStatus 获取的状态是:
07-29 09:56:08.803 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.802000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:849:20)] 格式错误: seat3WindStatus 状态异常了，取本地状态，当前本地状态是: 0
07-29 09:56:08.803 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.803000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat4WindStatus 获取的状态是:
07-29 09:56:08.804 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.803000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:849:20)] 格式错误: seat4WindStatus 状态异常了，取本地状态，当前本地状态是: 0
07-29 09:56:08.828 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.828000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:56:08.836 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.834000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:56:08.837 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.836000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:56:08.839 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.838000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:56:08.844 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.843000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  1
07-29 09:56:08.884 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:08.884000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:56:11.431 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.430000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:56:11.810 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.808000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 1, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333794, longitude: 109.366798, position: null, collectTime: 2025-07-29 09:56:00, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 1.1, voltage: 420.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 33, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.06, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 40, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:56:11.812 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.811000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:56:00，输入时间为2025-07-29 09:56:00，相差0，状态已过期，更新输入时间为状态时间
07-29 09:56:11.813 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.812000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:56:00
07-29 09:56:11.819 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.818000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:1 ,_MQTTBackStatus:1 , _currentACStatus:0,_statusModel?.acStatus:1 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:56:11.820 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.819000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3592:20)] 旧空调状态：1, 新空调状态：1
07-29 09:56:11.820 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.820000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3608:22)] 空调状态没有发生改变
07-29 09:56:11.821 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.820000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:56:11.823 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.822000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:56:11.824 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.823000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:56:11.824 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.824000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:56:11.826 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.825000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:56:11.826 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.826000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:56:11.827 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.826000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:56:11.829 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.828000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat1HotStatus 获取的状态是: 7
07-29 09:56:11.830 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.829000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat2HotStatus 获取的状态是: 7
07-29 09:56:11.831 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.830000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat3HotStatus 获取的状态是: 7
07-29 09:56:11.832 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.831000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat4HotStatus 获取的状态是: 7
07-29 09:56:11.833 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.832000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat1WindStatus 获取的状态是: 3
07-29 09:56:11.834 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.833000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat2WindStatus 获取的状态是: 7
07-29 09:56:11.835 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.834000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat3WindStatus 获取的状态是:
07-29 09:56:11.836 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.835000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:849:20)] 格式错误: seat3WindStatus 状态异常了，取本地状态，当前本地状态是: 0
07-29 09:56:11.837 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.836000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat4WindStatus 获取的状态是:
07-29 09:56:11.838 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.837000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:849:20)] 格式错误: seat4WindStatus 状态异常了，取本地状态，当前本地状态是: 0
07-29 09:56:11.856 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.856000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:56:11.864 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.863000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:56:11.865 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.864000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:56:11.867 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.866000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:56:11.872 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.871000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  1
07-29 09:56:11.900 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:11.900000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:56:12.079 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:12.077000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 09:56:12.925 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:12.925000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:56:12.930 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:12.929000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:56:12.930 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:12.930000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:56:12.932 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:12.931000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:56:12.935 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:12.934000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  1
07-29 09:56:14.431 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:14.430000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:56:14.725 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:14.723000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 1, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333791, longitude: 109.36679, position: null, collectTime: 2025-07-29 09:56:10, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 2.7, voltage: 420.2, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 33, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.04, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 40, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:56:14.726 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:14.725000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:56:00，输入时间为2025-07-29 09:56:10，相差10000，状态已过期，更新输入时间为状态时间
07-29 09:56:14.726 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:14.726000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:56:10
07-29 09:56:14.745 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:14.744000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:1 ,_MQTTBackStatus:1 , _currentACStatus:0,_statusModel?.acStatus:1 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 09:56:14.745 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:14.745000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 09:56:14.748 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:14.747000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:56:14.749 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:14.748000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:56:14.750 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:14.749000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:56:14.750 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:14.750000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:56:14.751 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:14.750000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:56:14.752 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:14.751000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:56:14.754 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:14.753000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:56:14.770 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:14.768000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:56:14.772 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:14.770000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:56:14.775 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:14.773000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 09:56:14.781 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:14.780000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  1
07-29 09:56:14.815 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:14.814000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 09:56:15.129 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:15.129000] [UseCarPageState.doCarControlNetwork (UseCarPageState.doCarControlNetwork (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2808)] 开始车控，serviceStatusResultType: 0 ，backStatusValue： , serviceCode: acStatus , mAccType:4 , 车控accParameter:19 ,车控params:{buttonLayout: 1}
07-29 09:56:16.472 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:16.470000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:152:18)] MQTTManager: 接收消息参数
07-29 09:56:16.472 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:16.472000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:153:18)] MQTTManager:   主题: LK6AEAE44SB192188/prod/sgmw/vehicle/app/status
07-29 09:56:16.473 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:16.473000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:154:18)] MQTTManager:   数据长度: 156
07-29 09:56:16.474 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:16.473000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:155:18)] MQTTManager:   QoS级别: 1
07-29 09:56:16.475 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:16.474000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:156:18)] MQTTManager:   是否Base64编码: true
07-29 09:56:16.476 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:16.475000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:160:18)] MQTTManager:   匹配的主题枚举: MQTTTopic.carControlAllStatus
07-29 09:56:16.478 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:16.477000] [MQTTManager._convertCarStatusToMap (mqtt_manager.dart:698:18)] MQTTManager:将SgmwAppCarStatus protobuf对象转换为Map ,carStatus.collectTime:1753754115660 , carStatus.collectTime.toInt():1753754115660
07-29 09:56:16.479 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:16.478000] [MQTTManager._notifyListeners (mqtt_manager.dart:492:18)] MQTTManager: 通知监听器 - 主题: MQTTTopic.carControlAllStatus, 监听器数量: 1
07-29 09:56:16.480 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:16.479000] [UseCarPageState._onCarControlAllStatusReceived (use_car_page.dart:735:18)] [MQTT]===收到车辆控制全状态MQTT消息,data: {collectTime: 1753754115660, acStatus: 1, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 7, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat31WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753754176478}
07-29 09:56:16.481 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:16.480000] [UseCarPageState._handleCarControlAllStatusFromMap (use_car_page.dart:822:18)] [MQTT]===处理从protobuf转换而来的Map格式数据，_handleCarControlAllStatusFromMap: {collectTime: 1753754115660, acStatus: 1, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 7, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat31WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753754176478}
07-29 09:56:16.482 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:16.481000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:866:24)] [MQTT]===时间戳1753754115660转换为2025-07-29 09:55:15
07-29 09:56:16.483 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:16.482000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:56:10，输入时间为2025-07-29 09:55:15，相差-55000，状态未过期
07-29 09:56:16.484 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:16.483000] [UseCarPageState._refreshCarControlUI (use_car_page.dart:1020:18)] 车辆控制状态已更新，刷新UI
07-29 09:56:16.485 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:16.484000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:875:22)] [MQTT]===是否需要更新车辆数据: isNeedUpdate:false
07-29 09:56:16.503 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:16.503000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:56:16.503 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:16.503000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:56:16.504 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:16.503000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:56:16.504 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:16.504000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:56:16.504 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:16.504000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:56:16.504 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:16.504000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:56:16.504 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:16.504000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:56:15
07-29 09:56:16.512 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:16.512000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:56:16.523 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:16.522000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:56:16.525 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:16.523000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:56:16.527 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:16.526000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1333:20)] isNewAir() 当前是旧空调
07-29 09:56:16.531 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:16.530000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：
07-29 09:56:17.077 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:17.076000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 09:56:17.432 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:17.431000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:56:17.720 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:17.719000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 1, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333791, longitude: 109.36679, position: null, collectTime: 2025-07-29 09:56:10, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 2.7, voltage: 420.2, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 33, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.04, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 40, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:56:17.721 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:17.721000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:56:15，输入时间为2025-07-29 09:56:10，相差-5000，状态未过期
07-29 09:56:20.431 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:20.429000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:56:20.725 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:20.724000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 1, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333791, longitude: 109.36679, position: null, collectTime: 2025-07-29 09:56:10, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 2.7, voltage: 420.2, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 33, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.04, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 40, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:56:20.727 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:20.726000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:56:15，输入时间为2025-07-29 09:56:10，相差-5000，状态未过期
07-29 09:56:22.080 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:22.079000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 09:56:22.863 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:22.862000] [UseCarPageState.doCarControlNetwork (UseCarPageState.doCarControlNetwork (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2808)] 开始车控，serviceStatusResultType: 0 ，backStatusValue： , serviceCode: acStatus , mAccType:4 , 车控accParameter:19 ,车控params:{buttonLayout: 1}
07-29 09:56:23.430 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:23.429000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:56:23.904 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:23.904000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 1, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333791, longitude: 109.36679, position: null, collectTime: 2025-07-29 09:56:10, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 2.7, voltage: 420.2, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 33, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.04, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 40, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:56:23.905 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:23.905000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:56:15，输入时间为2025-07-29 09:56:10，相差-5000，状态未过期
07-29 09:56:24.552 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:24.551000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:56:24.553 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:24.552000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:56:24.554 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:24.553000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:56:24.554 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:24.554000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:56:24.555 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:24.554000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 09:56:24.555 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:24.555000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 09:56:24.556 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:24.555000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 09:56:23
07-29 09:56:24.562 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:24.562000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 09:56:24.569 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:24.568000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 09:56:24.570 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:24.569000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 09:56:24.572 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:24.571000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1333:20)] isNewAir() 当前是旧空调
07-29 09:56:24.575 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:24.574000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：
07-29 09:56:26.431 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:26.429000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 09:56:26.692 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:26.691000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333798, longitude: 109.366793, position: null, collectTime: 2025-07-29 09:56:20, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.5, voltage: 420.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 33, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.06, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 40, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 09:56:26.694 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:26.693000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 09:56:23，输入时间为2025-07-29 09:56:20，相差-3000，状态未过期
07-29 09:56:27.080 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:27.078000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 09:56:28.674 11785 12670 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 09:56:28.673000] [UseCarPageState.doCarControlNetwork (UseCarPageState.doCarControlNetwork (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2808)] 开始车控，serviceStatusResultType: 0 ，backStatusValue： , serviceCode: acStatus , mAccType:4 , 车控accParameter:19 ,车控params:{buttonLayout: 1}
