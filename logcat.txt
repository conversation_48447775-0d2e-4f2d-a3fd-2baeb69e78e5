Flutter assets will be downloaded from https://storage.flutter-io.cn. Make sure you trust this source!
Launching lib\main.dart on 29Q0223813005436 in debug mode...
start hap build...
Running Hvigor task assembleHap...
Compiling debug_ohos_application for the Ohos...
07-28 07:54:21.853 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:21.820000] [error_util.dart:28:20] instance
07-28 07:54:22.325 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: 本地存在车辆信息，判定为车主用户
07-28 07:54:22.686 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: ✅ isCarOwner = 1，车主用户，将进入出行页 (INDEX_TRAVEL = 1)
07-28 07:54:22.962 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:22.961000] [_SplashScreenState.fetchAdData (splash_screen_page.dart:74:20)] DioException [unknown]: The connection errored: Failed host lookup: 'hapi.00bang.cn' This indicates an error which most likely cannot be solved by the library.
07-28 07:54:22.964 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:22.963000] [main.dart:31:20] APIException
07-28 07:54:22.964 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:22.964000] [main.dart:38:22] 其他错误======
07-28 07:54:28.534 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:28.533000] [UseCarPageState._loadAllCacheDataOnInit (use_car_page.dart:4641:20)] 缓存优先：页面初始化时开始加载所有缓存数据
07-28 07:54:28.538 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:28.537000] [UseCarPageState._loadUseCarServiceFromCache (use_car_page.dart:4742:24)] 从缓存加载用车服务列表成功，共8项
07-28 07:54:28.552 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:28.552000] [UseCarPageState.getWidgetList (use_car_page.dart:3681:12)] DEBUG:---- controlView: 3
07-28 07:54:28.693 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:28.692000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1289:20)] isNewAir() carStatus == null
07-28 07:54:28.863 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:28.862000] [UseCarPageState._loadAllCacheDataOnInit (use_car_page.dart:4695:20)] 缓存优先：页面初始化时缓存数据加载完成
07-28 07:54:28.867 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:28.867000] [UseCarPageState.setConnectionStatus (use_car_page.dart:1077:22)] UseCarPage: Disconnected
07-28 07:54:28.868 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:28.868000] [UseCarPageState._showPageNetworkDialog (use_car_page.dart:1110:20)] UseCarPage: 支持蓝牙功能，跳过网络检测弹窗，等待自动进入离线模式
07-28 07:54:28.873 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:28.873000] [mqtt_manager.dart:664:20] MQTTManager 网络状态变化: 无网络
07-28 07:54:28.874 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:28.874000] [UseCarPageState.setConnectionStatus (use_car_page.dart:1077:22)] UseCarPage: Disconnected
07-28 07:54:28.874 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:28.874000] [UseCarPageState._showPageNetworkDialog (use_car_page.dart:1110:20)] UseCarPage: 支持蓝牙功能，跳过网络检测弹窗，等待自动进入离线模式
07-28 07:54:28.875 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:28.874000] [mqtt_manager.dart:664:20] MQTTManager 网络状态变化: 无网络
07-28 07:54:28.875 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:28.875000] [UseCarPageState.setConnectionStatus (use_car_page.dart:1077:22)] UseCarPage: Disconnected
07-28 07:54:28.875 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:28.875000] [UseCarPageState._showPageNetworkDialog (use_car_page.dart:1110:20)] UseCarPage: 支持蓝牙功能，跳过网络检测弹窗，等待自动进入离线模式
07-28 07:54:29.055 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:29.055000] [UseCarPageState.getWidgetList (use_car_page.dart:3681:12)] DEBUG:---- controlView: 3
07-28 07:54:29.086 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:29.086000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1333:20)] isNewAir() 当前是旧空调
07-28 07:54:29.102 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:29.101000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-28 07:54:29.327 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:29.327000] [BleManager._updateSytemBleStatus (ble_manager.dart:146:22)] [BleKey]===系统蓝牙打开---
07-28 07:54:29.378 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:29.376000] [_MainPageState.fetchAdData (main_page.dart:151:20)] DioException [unknown]: The connection errored: Failed host lookup: 'openapi.baojun.net' This indicates an error which most likely cannot be solved by the library.
07-28 07:54:29.399 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:29.399000] [error_util.dart:37:20] onError
07-28 07:54:29.399 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:29.399000] [main.dart:31:20] LateError
07-28 07:54:29.399 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:29.399000] [main.dart:38:22] 其他错误======
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: ══╡ EXCEPTION CAUGHT BY WIDGETS LIBRARY ╞═══════════════════════════════════════════════════════════
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: The following LateError was thrown building FutureBuilder<List<CommunityTabs>>(dirty, state:
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: _FutureBuilderState<List<CommunityTabs>>#9cb43):
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: LateInitializationError: Field '_tabController@301365091' has not been initialized.
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message:
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: The relevant error-causing widget was:
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message:   FutureBuilder<List<CommunityTabs>>
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message:   FutureBuilder:file:///D:/wuling-flutter-app/lib/page/community/community_page.dart:103:12
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message:
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: When the exception was thrown, this was the stack:
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: #0      _CommunityPageState._tabController (package:wuling_flutter_app/page/community/community_page.dart)
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: #1      _CommunityPageState._contentWidget (package:wuling_flutter_app/page/community/community_page.dart:156:35)
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: #2      _CommunityPageState.buildPageContent.<anonymous closure> (package:wuling_flutter_app/page/community/community_page.dart:107:20)
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: #3      _FutureBuilderState.build (package:flutter/src/widgets/async.dart:616:55)
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: #4      StatefulElement.build (package:flutter/src/widgets/framework.dart:5080:27)
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: #5      ComponentElement.performRebuild (package:flutter/src/widgets/framework.dart:4968:15)
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: #6      StatefulElement.performRebuild (package:flutter/src/widgets/framework.dart:5133:11)
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: #7      Element.rebuild (package:flutter/src/widgets/framework.dart:4690:5)
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: #8      BuildOwner.buildScope (package:flutter/src/widgets/framework.dart:2743:19)
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: #9      WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:863:21)
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: #10     RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:381:5)
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: #11     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1289:15)
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: #12     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1218:9)
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: #13     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1076:5)
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: #17     _invoke (dart:ui/hooks.dart:147:10)
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: #18     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:339:5)
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: #19     _drawFrame (dart:ui/hooks.dart:112:31)
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: (elided 3 frames from dart:async)
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message:
07-28 07:54:29.434 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: ════════════════════════════════════════════════════════════════════════════════════════════════════
07-28 07:54:29.435 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:29.434000] [main.dart:31:20] LateError
07-28 07:54:29.435 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:29.435000] [main.dart:38:22] 其他错误======
07-28 07:54:29.874 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:29.874000] [UseCarPageState.setConnectionStatus (use_car_page.dart:1077:22)] UseCarPage: Disconnected
07-28 07:54:29.875 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:29.874000] [UseCarPageState._showPageNetworkDialog (use_car_page.dart:1110:20)] UseCarPage: 支持蓝牙功能，跳过网络检测弹窗，等待自动进入离线模式
07-28 07:54:29.879 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:29.878000] [UseCarPageState._loadDataFromCache (use_car_page.dart:4586:20)] UseCarPage: 开始从缓存加载MiniLife数据
07-28 07:54:29.880 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:29.879000] [MiniLifeCacheManager.getCachedMiniLifeData (mini_life_cache_manager.dart:220:18)] MiniLifeCacheManager: 开始获取完整的MiniLife缓存数据
07-28 07:54:29.881 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:29.880000] [MiniLifeCacheManager.getCachedActivityList (mini_life_cache_manager.dart:122:20)] MiniLifeCacheManager: 开始从缓存获取活动列表
07-28 07:54:29.881 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:29.881000] [MiniLifeCacheManager.getCachedActivityList (mini_life_cache_manager.dart:132:22)] MiniLifeCacheManager: 从缓存获取活动列表成功，数量: 0
07-28 07:54:29.881 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:29.881000] [MiniLifeCacheManager.getCachedActivityList (mini_life_cache_manager.dart:135:22)] MiniLifeCacheManager: 准备打印活动详细信息...
07-28 07:54:29.884 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:29.883000] [MiniLifeCacheManager.getCachedMiniLifeModel (mini_life_cache_manager.dart:175:20)] MiniLifeCacheManager: 开始从缓存获取MiniLife模型
07-28 07:54:29.884 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:29.884000] [MiniLifeCacheManager.getCachedMiniLifeModel (mini_life_cache_manager.dart:187:22)] MiniLifeCacheManager: 缓存中没有MiniLife模型数据
07-28 07:54:29.884 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:29.884000] [MiniLifeCacheManager.getCachedMiniLifeData (mini_life_cache_manager.dart:227:18)] MiniLifeCacheManager: MiniLife缓存数据获取完成
07-28 07:54:29.884 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:29.884000] [MiniLifeCacheManager.getCachedMiniLifeData (mini_life_cache_manager.dart:228:18)] MiniLifeCacheManager: 活动列表数量: 0, MiniLife模型: null, 是否来自缓存: false
07-28 07:54:29.885 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:29.885000] [UseCarPageState._loadDataFromCache (use_car_page.dart:4602:22)] UseCarPage: 缓存中没有MiniLife数据
07-28 07:54:29.895 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:29.894000] [UseCarPageState.getWidgetList (use_car_page.dart:3681:12)] DEBUG:---- controlView: 3
07-28 07:54:29.906 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:29.905000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1333:20)] isNewAir() 当前是旧空调
07-28 07:54:29.926 16688 17094 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-28 07:54:29.923000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
