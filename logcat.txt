Flutter assets will be downloaded from https://storage.flutter-io.cn. Make sure you trust this source!
Launching lib\main.dart on 29Q0223813005436 in debug mode...
start hap build...
Running Hvigor task assembleHap...
Compiling debug_ohos_application for the Ohos...
√ Built ohos\entry\build\default\outputs\default\entry-default-signed.hap.
installing hap. bundleName: com.sgmw.wuling
waiting for a debug connection: http://127.0.0.1:63817/MOu8FXdvCO0=/
07-29 10:22:15.126 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:15.090000] [error_util.dart:28:20] instance
07-29 10:22:15.605 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: 本地存在车辆信息，判定为车主用户
Debug service listening on ws://127.0.0.1:55140/PYkdO92NTeI=/ws
Syncing files to device 29Q0223813005436...
07-29 10:22:15.967 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: ✅ isCarOwner = 1，车主用户，将进入出行页 (INDEX_TRAVEL = 1)
07-29 10:22:21.855 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:21.832000] [UseCarPageState._loadAllCacheDataOnInit (use_car_page.dart:4655:20)] 缓存优先：页面初始化时开始加载所有缓存数据
07-29 10:22:21.860 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:21.859000] [UseCarPageState._loadUseCarServiceFromCache (use_car_page.dart:4756:24)] 从缓存加载用车服务列表成功，共8项
07-29 10:22:21.882 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:21.881000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:22:22.035 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:22.034000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1289:20)] isNewAir() carStatus == null
07-29 10:22:22.236 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:22.235000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:22.237 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:22.237000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:22.238 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:22.237000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:22.238 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:22.238000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:22.239 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:22.238000] [UseCarPageState._loadAllCacheDataOnInit (use_car_page.dart:4709:20)] 缓存优先：页面初始化时缓存数据加载完成
07-29 10:22:22.248 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:22.248000] [mqtt_manager.dart:664:20] MQTTManager 网络状态变化: 已连接
07-29 10:22:22.250 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:22.249000] [mqtt_manager.dart:664:20] MQTTManager 网络状态变化: 已连接
07-29 10:22:22.250 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:22.250000] [mqtt_manager.dart:664:20] MQTTManager 网络状态变化: 已连接
07-29 10:22:22.251 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:22.250000] [mqtt_manager.dart:664:20] MQTTManager 网络状态变化: 已连接
07-29 10:22:22.411 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:22.410000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:22:22.439 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:22.439000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:22:22.452 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:22.451000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:22:22.653 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:22.653000] [BleManager._updateSytemBleStatus (ble_manager.dart:146:22)] [BleKey]===系统蓝牙打开---
07-29 10:22:23.193 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:23.192000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:22:23.203 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:23.202000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:22:23.212 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:23.211000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:22:23.615 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:23.614000] [UseCarPageState._onRefresh (use_car_page.dart:1573:13)] 手动下拉刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333808, longitude: 109.366771, position: null, collectTime: 2025-07-29 10:15:52, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 0, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 35, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.06, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 41, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:22:23.617 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:23.617000] [UseCarPageState._onRefresh (use_car_page.dart:1587:15)] 手动下拉刷新接口请求成功，_userHandleDisConnectBle：false ，_bleManager.currentStatus：BleStatus.bleDefault，_currentConnectBleVin: , currentVin:LK6AEAE44SB192188,_carInfoModel?.bleType:2
07-29 10:22:23.618 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:23.618000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:22:23.621 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:23.621000] [UseCarPageState._fetchBluetoothKey (use_car_page.dart:1626:9)] [BleKey]===获取蓝牙钥匙===
07-29 10:22:23.622 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:23.622000] [UseCarPageState._fetchBluetoothKey (use_car_page.dart:1631:13)] [BleKey]===获取蓝牙钥匙===_currentConnectBleVin： , vin:LK6AEAE44SB192188 , _currentBleStatus:BleStatus.bleDefault
07-29 10:22:23.623 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:23.622000] [BleManager.resetHasBluetoothKey (BleManager.resetHasBluetoothKey (package:wuling_flutter_app/utils/manager/ble_manager.dart:329)] [BleKey]===重设蓝牙钥匙获取标记===hasBluetoothKey:false
07-29 10:22:23.625 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:23.625000] [UseCarPageState.loginMQTT (use_car_page.dart:1932:18)] MQTT登录，loginMQTT: _currentCarVin -  ， vin：LK6AEAE44SB192188 ， supportMqtt：1 , isLogin:true
07-29 10:22:23.631 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:23.630000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:22:23.640 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:23.639000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:22:23.646 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:23.645000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:22:23.679 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:23.678000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:22:23.874 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:23.873000] [MQTTManager._getTokenWithVin (mqtt_manager.dart:255:20)] MQTT: Token获取成功 - ClientId: LK6AEAE44SB192188_4796
07-29 10:22:23.875 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:23.875000] [MQTTManager.getTokenAndLoginWithVin (mqtt_manager.dart:197:22)] MQTT: 获取账号密码成功
07-29 10:22:23.876 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:23.876000] [MQTTManager._connectWithCredentials (mqtt_manager.dart:326:18)] MQTTManager: 连接参数
07-29 10:22:23.877 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:23.876000] [MQTTManager._connectWithCredentials (mqtt_manager.dart:327:18)] MQTTManager:   username: e681b3b345bbe5712df6cdb0fd733957
07-29 10:22:23.877 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:23.877000] [MQTTManager._connectWithCredentials (mqtt_manager.dart:328:18)] MQTTManager:   password: c7dde1a7594a65a2de7845f1e8a89036
07-29 10:22:23.877 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:23.877000] [MQTTManager._connectWithCredentials (mqtt_manager.dart:329:18)] MQTTManager:   clientId: LK6AEAE44SB192188_4796
07-29 10:22:24.483 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:24.483000] [MQTTManager._handleConnected (mqtt_manager.dart:133:18)] MQTTManager: 连接成功
07-29 10:22:24.547 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:24.547000] [use_car_page.dart:1649:19] [BleKey]===获取蓝牙钥匙成功，userHandleDisConnectBle：false ，_bleManager.currentStatus：BleStatus.bleDefault，_currentConnectBleVin: , currentVin:LK6AEAE44SB192188,_carInfoModel?.bleType:2 ，bleKeyModel.vin:LK6AEAE44SB192188
07-29 10:22:24.548 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:24.548000] [use_car_page.dart:1651:19] [BleKey]===缓存蓝牙钥匙，bleMac: 40:F3:B0:12:2B:FE
07-29 10:22:24.548 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:24.548000] [MiniLifeCacheManager.saveBleKeyModel (mini_life_cache_manager.dart:86:22)] MiniLifeCacheManager: 开始保存蓝牙钥匙到缓存
07-29 10:22:24.548 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:24.548000] [MiniLifeCacheManager.saveBleKeyModel (mini_life_cache_manager.dart:88:14)] MiniLifeCacheManager: 蓝牙钥匙 bleMac: 40:F3:B0:12:2B:FE
07-29 10:22:24.549 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:24.549000] [use_car_page.dart:1666:27] [BleKey]===获取蓝牙钥匙===首次连接蓝牙===
07-29 10:22:24.571 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:24.571000] [MiniLifeCacheManager.saveBleKeyModel (mini_life_cache_manager.dart:93:22)] MiniLifeCacheManager: 蓝牙钥匙缓存保存成功
07-29 10:22:24.690 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:24.690000] [MQTTManager._connectWithCredentials (mqtt_manager.dart:345:20)] MQTTManager: 连接请求已发送 - 连接已启动
07-29 10:22:24.691 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:24.691000] [MQTTManager._handleSubscribeSuccess (mqtt_manager.dart:173:18)] MQTTManager: 订阅成功
07-29 10:22:24.969 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:24.968000] [UseCarPageState.getHasCarListData (use_car_page.dart:1820:20)] UseCarPage: 开始保存MiniLife数据到缓存
07-29 10:22:24.970 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:24.969000] [MiniLifeCacheManager.saveMiniLifeData (mini_life_cache_manager.dart:107:18)] MiniLifeCacheManager: 开始批量保存MiniLife数据
07-29 10:22:24.970 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:24.970000] [MiniLifeCacheManager.saveMiniLifeData (mini_life_cache_manager.dart:108:18)] MiniLifeCacheManager: 活动列表数量: 0, MiniLife模型: null
07-29 10:22:24.971 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:24.971000] [MiniLifeCacheManager.saveActivityList (mini_life_cache_manager.dart:22:20)] MiniLifeCacheManager: 开始保存活动列表到缓存，数量: 0
07-29 10:22:24.973 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:24.972000] [MiniLifeCacheManager.saveMiniLifeModel (mini_life_cache_manager.dart:75:22)] MiniLifeCacheManager: MiniLife模型为null，跳过缓存保存
07-29 10:22:24.994 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:24.993000] [MiniLifeCacheManager.saveActivityList (mini_life_cache_manager.dart:56:12)] MiniLifeCacheManager: 活动列表缓存保存成功，数量: 0
07-29 10:22:24.994 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:24.994000] [MiniLifeCacheManager.saveMiniLifeData (mini_life_cache_manager.dart:116:18)] MiniLifeCacheManager: MiniLife数据批量保存完成
07-29 10:22:24.995 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:24.994000] [UseCarPageState.getHasCarListData (use_car_page.dart:1825:20)] UseCarPage: MiniLife数据缓存保存完成
07-29 10:22:24.995 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:24.995000] [use_car_page.dart:1834:22] DEBUG: setState中设置变量完成 - _toolServiceList长度: 7
07-29 10:22:24.996 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:24.996000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:24.996 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:24.996000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:24.997 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:24.997000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:24.997 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:24.997000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:25.000 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:24.999000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:22:25.017 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:25.016000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:22:25.023 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:25.022000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:22:25.806 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:25.806000] [UseCarPageState.getHasCarListData (use_car_page.dart:1846:20)] 获取用车服务列表成功，数量: 8
07-29 10:22:25.806 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:25.806000] [UseCarPageState.getHasCarListData (use_car_page.dart:1849:22)]   [0] 流量商城: https://oss-rewrite.00bang.cn/image/LLB_OSSA8AC35ACF9DA4BE59C41FA3170EF0BB4.png
07-29 10:22:25.807 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:25.806000] [UseCarPageState.getHasCarListData (use_car_page.dart:1849:22)]   [1] 车主认证: https://oss-rewrite.00bang.cn/image/LLB_OSS7D1E5A35F57A43ABA88ACEC1FF63ABBB.png
07-29 10:22:25.807 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:25.807000] [UseCarPageState.getHasCarListData (use_car_page.dart:1849:22)]   [2] 用车指南: https://oss-rewrite.00bang.cn/image/LLB_OSSE96C6CBE4CA0439E80D50B32F64797C1.png
07-29 10:22:25.807 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:25.807000] [UseCarPageState.getHasCarListData (use_car_page.dart:1849:22)]   [3] 服务进度: https://cdn-df.00bang.cn/images/T1kJKTBjWT1RCvBVdK.png
07-29 10:22:25.808 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:25.807000] [UseCarPageState.getHasCarListData (use_car_page.dart:1849:22)]   [4] 预约服务: https://cdn-df.00bang.cn/images/T1LAKTBgxT1RCvBVdK.png
07-29 10:22:25.808 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:25.808000] [UseCarPageState.getHasCarListData (use_car_page.dart:1849:22)]   [5] 道路救援: https://cdn-df.00bang.cn/images/T1LKKTBgbT1RCvBVdK.png
07-29 10:22:25.808 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:25.808000] [UseCarPageState.getHasCarListData (use_car_page.dart:1849:22)]   [6] 优惠劵: https://cdn-df.00bang.cn/images/T1dPCTByYT1RCvBVdK.png
07-29 10:22:25.808 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:25.808000] [UseCarPageState.getHasCarListData (use_car_page.dart:1849:22)]   [7] 全部功能: https://cdn-df.00bang.cn/images/T1nJKTBTCT1RCvBVdK.png
07-29 10:22:25.808 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:25.808000] [use_car_page.dart:1855:22] 设置_useCarServiceList完成，准备缓存
07-29 10:22:25.820 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:25.820000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:22:25.836 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:25.835000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:22:25.843 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:25.842000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:22:25.896 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:25.896000] [UseCarPageState._cacheServiceLists (use_car_page.dart:4465:22)] 服务列表缓存成功，key: travel_service_lists_MTcwMDc0ODg=_LK6AEAE44SB192188
07-29 10:22:26.482 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:26.482000] [UseCarPageState.getNotificationData (use_car_page.dart:1918:20)] 通知服务-- 2
07-29 10:22:26.495 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:26.494000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:22:26.507 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:26.505000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:22:26.509 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:26.507000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:22:26.512 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:26.511000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:22:26.517 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:26.516000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:22:26.996 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:26.995000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:22:27.011 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:27.009000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:22:27.012 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:27.011000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:22:27.015 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:27.014000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:22:27.022 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:27.020000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:22:28.830 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:28.829000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:22:29.053 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:29.052000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333808, longitude: 109.366771, position: null, collectTime: 2025-07-29 10:15:52, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 0, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 35, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.06, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 41, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:22:29.088 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:29.087000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:15:24，输入时间为2025-07-29 10:15:52，相差28000，状态已过期，更新输入时间为状态时间
07-29 10:22:29.088 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:29.088000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:15:52
07-29 10:22:29.105 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:29.104000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:,statusMap[_currentServiceCode]:null ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:22:29.106 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:29.106000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:22:29.108 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:29.108000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:29.109 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:29.108000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:29.109 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:29.109000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:29.110 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:29.109000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:29.111 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:29.111000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:22:29.115 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:29.115000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:22:29.126 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:29.124000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:22:29.128 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:29.126000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:22:29.130 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:29.128000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:22:29.137 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:29.135000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:22:29.496 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:29.496000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 10:22:31.869 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:31.869000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:22:31.882 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:31.881000] [UseCarPageState.handleControlActionWithItem (use_car_page.dart:2340:11)] 点击了第0个车控按钮:空调 座椅通风加热-F511C LV2高配主副通风加热都有  后排只有加热 当前状态:空调
07-29 10:22:31.882 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:31.882000] [UseCarPageState.handleControlActionWithItem (use_car_page.dart:2401:17)] 处理 车控 类型 4 , serviceCode =acStatus , acStatus = 0
07-29 10:22:31.923 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:31.921000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat1HotStatus 获取的状态是: 7
07-29 10:22:31.925 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:31.924000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat2HotStatus 获取的状态是: 7
07-29 10:22:31.926 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:31.925000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat3HotStatus 获取的状态是: 7
07-29 10:22:31.928 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:31.926000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat4HotStatus 获取的状态是: 7
07-29 10:22:31.931 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:31.929000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat1WindStatus 获取的状态是: 7
07-29 10:22:31.934 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:31.932000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat2WindStatus 获取的状态是: 7
07-29 10:22:31.936 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:31.934000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat3WindStatus 获取的状态是: 7
07-29 10:22:31.938 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:31.936000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat4WindStatus 获取的状态是: 7
07-29 10:22:32.110 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:32.110000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333808, longitude: 109.366771, position: null, collectTime: 2025-07-29 10:15:52, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 0, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 35, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.06, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 41, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:22:32.111 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:32.111000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:15:52，输入时间为2025-07-29 10:15:52，相差0，状态已过期，更新输入时间为状态时间
07-29 10:22:32.111 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:32.111000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:15:52
07-29 10:22:32.139 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:32.138000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:,statusMap[_currentServiceCode]:null ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:22:32.139 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:32.139000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3592:20)] 旧空调状态：0, 新空调状态：0
07-29 10:22:32.140 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:32.139000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3608:22)] 空调状态没有发生改变
07-29 10:22:32.142 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:32.141000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:22:32.143 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:32.143000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:32.144 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:32.144000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:32.145 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:32.144000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:32.145 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:32.145000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:32.147 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:32.147000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat1HotStatus 获取的状态是: 7
07-29 10:22:32.148 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:32.148000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat2HotStatus 获取的状态是: 7
07-29 10:22:32.149 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:32.148000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat3HotStatus 获取的状态是: 7
07-29 10:22:32.150 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:32.149000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat4HotStatus 获取的状态是: 7
07-29 10:22:32.150 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:32.150000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat1WindStatus 获取的状态是: 7
07-29 10:22:32.151 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:32.150000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat2WindStatus 获取的状态是: 7
07-29 10:22:32.151 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:32.151000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat3WindStatus 获取的状态是: 7
07-29 10:22:32.152 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:32.152000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat4WindStatus 获取的状态是: 7
07-29 10:22:32.172 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:32.171000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:22:32.182 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:32.181000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:22:32.183 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:32.182000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:22:32.185 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:32.184000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:22:32.192 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:32.191000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:22:32.281 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:32.280000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:22:34.498 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:34.497000] [UseCarPageState.doCarControlNetwork (UseCarPageState.doCarControlNetwork (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2808)] 开始车控，serviceStatusResultType: 1 ，backStatusValue：2 , serviceCode: acStatus , mAccType:4 , 车控accParameter:4 ,车控params:{buttonLayout: 1, accOnOff: 1, temperature: 17, blowerLvl: 7, duration: 10, status: 1, acType: 4, quickDownTemperature: 1}
07-29 10:22:34.501 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:34.500000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 10:22:34.524 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:34.524000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat1HotStatus 获取的状态是: 7
07-29 10:22:34.525 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:34.525000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat2HotStatus 获取的状态是: 7
07-29 10:22:34.525 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:34.525000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat3HotStatus 获取的状态是: 7
07-29 10:22:34.525 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:34.525000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat4HotStatus 获取的状态是: 7
07-29 10:22:34.526 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:34.526000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat1WindStatus 获取的状态是: 7
07-29 10:22:34.526 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:34.526000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat2WindStatus 获取的状态是: 7
07-29 10:22:34.526 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:34.526000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat3WindStatus 获取的状态是: 7
07-29 10:22:34.527 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:34.526000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat4WindStatus 获取的状态是: 7
07-29 10:22:34.818 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:34.817000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:22:35.224 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:35.223000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333808, longitude: 109.366771, position: null, collectTime: 2025-07-29 10:15:52, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 0, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 35, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.06, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 41, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:22:35.226 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:35.225000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:15:52，输入时间为2025-07-29 10:15:52，相差0，状态已过期，更新输入时间为状态时间
07-29 10:22:35.226 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:35.226000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:15:52
07-29 10:22:35.229 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:35.229000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:,statusMap[_currentServiceCode]:null ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:22:35.230 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:35.229000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3592:20)] 旧空调状态：0, 新空调状态：0
07-29 10:22:35.230 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:35.230000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3608:22)] 空调状态没有发生改变
07-29 10:22:35.230 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:35.230000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:22:35.232 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:35.232000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:35.234 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:35.233000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:35.235 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:35.234000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:35.236 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:35.235000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:35.238 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:35.237000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:22:35.242 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:35.240000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat1HotStatus 获取的状态是: 7
07-29 10:22:35.244 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:35.242000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat2HotStatus 获取的状态是: 7
07-29 10:22:35.246 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:35.245000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat3HotStatus 获取的状态是: 7
07-29 10:22:35.248 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:35.247000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat4HotStatus 获取的状态是: 7
07-29 10:22:35.249 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:35.248000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat1WindStatus 获取的状态是: 7
07-29 10:22:35.249 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:35.249000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat2WindStatus 获取的状态是: 7
07-29 10:22:35.250 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:35.250000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat3WindStatus 获取的状态是: 7
07-29 10:22:35.251 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:35.251000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat4WindStatus 获取的状态是: 7
07-29 10:22:35.270 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:35.270000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:22:35.287 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:35.286000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:22:35.288 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:35.287000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:22:35.290 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:35.289000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:22:35.296 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:35.295000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:22:37.823 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:37.821000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:22:37.904 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:37.903000] [MQTTManager.startMqttTimeOutTimer (mqtt_manager.dart:620:18)] MQTTManager: 启动接收MQTT异步结果推送超时定时器
07-29 10:22:37.905 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:37.904000] [MQTTManager.stopMqttTimeOutTimer (mqtt_manager.dart:627:18)] MQTTManager: 停止接收MQTT异步结果推送超时定时器
07-29 10:22:38.091 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:38.090000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333808, longitude: 109.366771, position: null, collectTime: 2025-07-29 10:15:52, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 0, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 35, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.06, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 41, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:22:38.094 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:38.093000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:15:52，输入时间为2025-07-29 10:15:52，相差0，状态已过期，更新输入时间为状态时间
07-29 10:22:38.094 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:38.094000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:15:52
07-29 10:22:38.108 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:38.107000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:1 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:true , IS_AC_HOT_WIND_SETTING:false
07-29 10:22:38.110 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:38.109000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:38.111 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:38.110000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:38.112 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:38.111000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:38.112 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:38.112000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:38.125 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:38.124000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:22:38.151 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:38.147000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:22:38.154 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:38.152000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:22:38.157 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:38.155000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:22:38.167 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:38.164000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:22:39.490 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:39.489000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 10:22:39.586 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:39.585000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:152:18)] MQTTManager: 接收消息参数
07-29 10:22:39.587 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:39.587000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:153:18)] MQTTManager:   主题: LK6AEAE44SB192188/prod/sgmw/vehicle/app/status
07-29 10:22:39.588 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:39.587000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:154:18)] MQTTManager:   数据长度: 156
07-29 10:22:39.589 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:39.588000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:155:18)] MQTTManager:   QoS级别: 1
07-29 10:22:39.590 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:39.589000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:156:18)] MQTTManager:   是否Base64编码: true
07-29 10:22:39.594 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:39.593000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:160:18)] MQTTManager:   匹配的主题枚举: MQTTTopic.carControlAllStatus
07-29 10:22:39.640 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:39.640000] [MQTTManager._convertCarStatusToMap (mqtt_manager.dart:698:18)] MQTTManager:将SgmwAppCarStatus protobuf对象转换为Map ,carStatus.collectTime:1753755133512 , carStatus.collectTime.toInt():1753755133512
07-29 10:22:39.644 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:39.644000] [MQTTManager._notifyListeners (mqtt_manager.dart:492:18)] MQTTManager: 通知监听器 - 主题: MQTTTopic.carControlAllStatus, 监听器数量: 1
07-29 10:22:39.645 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:39.645000] [UseCarPageState._onCarControlAllStatusReceived (use_car_page.dart:735:18)] [MQTT]===收到车辆控制全状态MQTT消息,data: {collectTime: 1753755133512, acStatus: 0, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 0, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753755759644}
07-29 10:22:39.645 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:39.645000] [UseCarPageState._handleCarControlAllStatusFromMap (use_car_page.dart:822:18)] [MQTT]===处理从protobuf转换而来的Map格式数据，_handleCarControlAllStatusFromMap: {collectTime: 1753755133512, acStatus: 0, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 0, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753755759644}
07-29 10:22:39.646 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:39.646000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:866:24)] [MQTT]===时间戳1753755133512转换为2025-07-29 10:12:13
07-29 10:22:39.647 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:39.646000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:15:52，输入时间为2025-07-29 10:12:13，相差-219000，状态未过期
07-29 10:22:39.647 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:39.647000] [UseCarPageState._refreshCarControlUI (use_car_page.dart:1020:18)] 车辆控制状态已更新，刷新UI
07-29 10:22:39.647 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:39.647000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:875:22)] [MQTT]===是否需要更新车辆数据: isNeedUpdate:false
07-29 10:22:40.529 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.528000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:152:18)] MQTTManager: 接收消息参数
07-29 10:22:40.531 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.530000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:153:18)] MQTTManager:   主题: LK6AEAE44SB192188/prod/sgmw/vehicle/app/status
07-29 10:22:40.532 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.531000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:154:18)] MQTTManager:   数据长度: 128
07-29 10:22:40.534 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.533000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:155:18)] MQTTManager:   QoS级别: 1
07-29 10:22:40.535 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.534000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:156:18)] MQTTManager:   是否Base64编码: true
07-29 10:22:40.537 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.536000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:160:18)] MQTTManager:   匹配的主题枚举: MQTTTopic.carControlAllStatus
07-29 10:22:40.540 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.539000] [MQTTManager._convertCarStatusToMap (mqtt_manager.dart:698:18)] MQTTManager:将SgmwAppCarStatus protobuf对象转换为Map ,carStatus.collectTime:1753755131893 , carStatus.collectTime.toInt():1753755131893
07-29 10:22:40.542 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.541000] [MQTTManager._notifyListeners (mqtt_manager.dart:492:18)] MQTTManager: 通知监听器 - 主题: MQTTTopic.carControlAllStatus, 监听器数量: 1
07-29 10:22:40.543 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.542000] [UseCarPageState._onCarControlAllStatusReceived (use_car_page.dart:735:18)] [MQTT]===收到车辆控制全状态MQTT消息,data: {collectTime: 1753755131893, acStatus: 0, doorLockStatus: 0, windowStatus: , engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: , window2Status: , window3Status: , window4Status: , topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 0, acTemperatureGear: , acWindGear: 0, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753755760541}
07-29 10:22:40.544 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.543000] [UseCarPageState._handleCarControlAllStatusFromMap (use_car_page.dart:822:18)] [MQTT]===处理从protobuf转换而来的Map格式数据，_handleCarControlAllStatusFromMap: {collectTime: 1753755131893, acStatus: 0, doorLockStatus: 0, windowStatus: , engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: , window2Status: , window3Status: , window4Status: , topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 0, acTemperatureGear: , acWindGear: 0, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753755760541}
07-29 10:22:40.546 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.545000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:866:24)] [MQTT]===时间戳1753755131893转换为2025-07-29 10:12:11
07-29 10:22:40.548 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.547000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:15:52，输入时间为2025-07-29 10:12:11，相差-221000，状态未过期
07-29 10:22:40.549 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.548000] [UseCarPageState._refreshCarControlUI (use_car_page.dart:1020:18)] 车辆控制状态已更新，刷新UI
07-29 10:22:40.550 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.549000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:875:22)] [MQTT]===是否需要更新车辆数据: isNeedUpdate:false
07-29 10:22:40.643 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.642000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:152:18)] MQTTManager: 接收消息参数
07-29 10:22:40.644 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.643000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:153:18)] MQTTManager:   主题: LK6AEAE44SB192188/prod/sgmw/vehicle/app/status
07-29 10:22:40.644 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.644000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:154:18)] MQTTManager:   数据长度: 128
07-29 10:22:40.645 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.644000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:155:18)] MQTTManager:   QoS级别: 1
07-29 10:22:40.645 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.645000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:156:18)] MQTTManager:   是否Base64编码: true
07-29 10:22:40.646 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.646000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:160:18)] MQTTManager:   匹配的主题枚举: MQTTTopic.carControlAllStatus
07-29 10:22:40.647 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.647000] [MQTTManager._convertCarStatusToMap (mqtt_manager.dart:698:18)] MQTTManager:将SgmwAppCarStatus protobuf对象转换为Map ,carStatus.collectTime:1753755131944 , carStatus.collectTime.toInt():1753755131944
07-29 10:22:40.648 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.648000] [MQTTManager._notifyListeners (mqtt_manager.dart:492:18)] MQTTManager: 通知监听器 - 主题: MQTTTopic.carControlAllStatus, 监听器数量: 1
07-29 10:22:40.649 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.648000] [UseCarPageState._onCarControlAllStatusReceived (use_car_page.dart:735:18)] [MQTT]===收到车辆控制全状态MQTT消息,data: {collectTime: 1753755131944, acStatus: 0, doorLockStatus: 0, windowStatus: , engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: , window2Status: , window3Status: , window4Status: , topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 0, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753755760647}
07-29 10:22:40.649 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.649000] [UseCarPageState._handleCarControlAllStatusFromMap (use_car_page.dart:822:18)] [MQTT]===处理从protobuf转换而来的Map格式数据，_handleCarControlAllStatusFromMap: {collectTime: 1753755131944, acStatus: 0, doorLockStatus: 0, windowStatus: , engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: , window2Status: , window3Status: , window4Status: , topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 0, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753755760647}
07-29 10:22:40.650 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.650000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:866:24)] [MQTT]===时间戳1753755131944转换为2025-07-29 10:12:11
07-29 10:22:40.651 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.651000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:15:52，输入时间为2025-07-29 10:12:11，相差-221000，状态未过期
07-29 10:22:40.652 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.652000] [UseCarPageState._refreshCarControlUI (use_car_page.dart:1020:18)] 车辆控制状态已更新，刷新UI
07-29 10:22:40.652 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.652000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:875:22)] [MQTT]===是否需要更新车辆数据: isNeedUpdate:false
07-29 10:22:40.653 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.653000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:152:18)] MQTTManager: 接收消息参数
07-29 10:22:40.654 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.654000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:153:18)] MQTTManager:   主题: LK6AEAE44SB192188/prod/sgmw/vehicle/app/status
07-29 10:22:40.654 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.654000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:154:18)] MQTTManager:   数据长度: 156
07-29 10:22:40.654 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.654000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:155:18)] MQTTManager:   QoS级别: 1
07-29 10:22:40.655 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.655000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:156:18)] MQTTManager:   是否Base64编码: true
07-29 10:22:40.655 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.655000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:160:18)] MQTTManager:   匹配的主题枚举: MQTTTopic.carControlAllStatus
07-29 10:22:40.656 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.655000] [MQTTManager._convertCarStatusToMap (mqtt_manager.dart:698:18)] MQTTManager:将SgmwAppCarStatus protobuf对象转换为Map ,carStatus.collectTime:1753755133812 , carStatus.collectTime.toInt():1753755133812
07-29 10:22:40.656 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.656000] [MQTTManager._notifyListeners (mqtt_manager.dart:492:18)] MQTTManager: 通知监听器 - 主题: MQTTTopic.carControlAllStatus, 监听器数量: 1
07-29 10:22:40.657 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.657000] [UseCarPageState._onCarControlAllStatusReceived (use_car_page.dart:735:18)] [MQTT]===收到车辆控制全状态MQTT消息,data: {collectTime: 1753755133812, acStatus: 1, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 1, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753755760656}
07-29 10:22:40.658 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.657000] [UseCarPageState._handleCarControlAllStatusFromMap (use_car_page.dart:822:18)] [MQTT]===处理从protobuf转换而来的Map格式数据，_handleCarControlAllStatusFromMap: {collectTime: 1753755133812, acStatus: 1, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 1, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753755760656}
07-29 10:22:40.658 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.658000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:866:24)] [MQTT]===时间戳1753755133812转换为2025-07-29 10:12:13
07-29 10:22:40.659 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.659000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:15:52，输入时间为2025-07-29 10:12:13，相差-219000，状态未过期
07-29 10:22:40.659 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.659000] [UseCarPageState._refreshCarControlUI (use_car_page.dart:1020:18)] 车辆控制状态已更新，刷新UI
07-29 10:22:40.660 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.660000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:875:22)] [MQTT]===是否需要更新车辆数据: isNeedUpdate:false
07-29 10:22:40.822 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.820000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:22:40.871 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.869000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:152:18)] MQTTManager: 接收消息参数
07-29 10:22:40.872 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.871000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:153:18)] MQTTManager:   主题: LK6AEAE44SB192188/prod/sgmw/vehicle/app/status
07-29 10:22:40.873 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.872000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:154:18)] MQTTManager:   数据长度: 156
07-29 10:22:40.874 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.873000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:155:18)] MQTTManager:   QoS级别: 1
07-29 10:22:40.875 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.875000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:156:18)] MQTTManager:   是否Base64编码: true
07-29 10:22:40.877 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.876000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:160:18)] MQTTManager:   匹配的主题枚举: MQTTTopic.carControlAllStatus
07-29 10:22:40.879 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.878000] [MQTTManager._convertCarStatusToMap (mqtt_manager.dart:698:18)] MQTTManager:将SgmwAppCarStatus protobuf对象转换为Map ,carStatus.collectTime:1753755133913 , carStatus.collectTime.toInt():1753755133913
07-29 10:22:40.880 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.879000] [MQTTManager._notifyListeners (mqtt_manager.dart:492:18)] MQTTManager: 通知监听器 - 主题: MQTTTopic.carControlAllStatus, 监听器数量: 1
07-29 10:22:40.881 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.880000] [UseCarPageState._onCarControlAllStatusReceived (use_car_page.dart:735:18)] [MQTT]===收到车辆控制全状态MQTT消息,data: {collectTime: 1753755133913, acStatus: 1, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 1, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753755760879}
07-29 10:22:40.882 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.881000] [UseCarPageState._handleCarControlAllStatusFromMap (use_car_page.dart:822:18)] [MQTT]===处理从protobuf转换而来的Map格式数据，_handleCarControlAllStatusFromMap: {collectTime: 1753755133913, acStatus: 1, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 1, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753755760879}
07-29 10:22:40.883 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.882000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:866:24)] [MQTT]===时间戳1753755133913转换为2025-07-29 10:12:13
07-29 10:22:40.884 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.883000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:15:52，输入时间为2025-07-29 10:12:13，相差-219000，状态未过期
07-29 10:22:40.884 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.884000] [UseCarPageState._refreshCarControlUI (use_car_page.dart:1020:18)] 车辆控制状态已更新，刷新UI
07-29 10:22:40.885 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:40.885000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:875:22)] [MQTT]===是否需要更新车辆数据: isNeedUpdate:false
07-29 10:22:41.064 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:41.063000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333808, longitude: 109.366771, position: null, collectTime: 2025-07-29 10:15:52, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 0, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 35, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.06, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 41, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:22:41.067 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:41.066000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:15:52，输入时间为2025-07-29 10:15:52，相差0，状态已过期，更新输入时间为状态时间
07-29 10:22:41.068 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:41.067000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:15:52
07-29 10:22:41.078 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:41.077000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:1 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:true , IS_AC_HOT_WIND_SETTING:false
07-29 10:22:41.079 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:41.079000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:41.080 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:41.080000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:41.081 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:41.081000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:41.082 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:41.081000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:41.084 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:41.083000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:22:41.099 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:41.098000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:22:41.100 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:41.099000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:22:41.102 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:41.101000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:22:41.106 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:41.105000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:22:41.171 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:41.170000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:152:18)] MQTTManager: 接收消息参数
07-29 10:22:41.171 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:41.171000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:153:18)] MQTTManager:   主题: LK6AEAE44SB192188/prod/sgmw/vehicle/app/status
07-29 10:22:41.172 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:41.171000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:154:18)] MQTTManager:   数据长度: 156
07-29 10:22:41.172 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:41.172000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:155:18)] MQTTManager:   QoS级别: 1
07-29 10:22:41.172 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:41.172000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:156:18)] MQTTManager:   是否Base64编码: true
07-29 10:22:41.172 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:41.172000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:160:18)] MQTTManager:   匹配的主题枚举: MQTTTopic.carControlAllStatus
07-29 10:22:41.173 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:41.173000] [MQTTManager._convertCarStatusToMap (mqtt_manager.dart:698:18)] MQTTManager:将SgmwAppCarStatus protobuf对象转换为Map ,carStatus.collectTime:1753755132532 , carStatus.collectTime.toInt():1753755132532
07-29 10:22:41.174 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:41.174000] [MQTTManager._notifyListeners (mqtt_manager.dart:492:18)] MQTTManager: 通知监听器 - 主题: MQTTTopic.carControlAllStatus, 监听器数量: 1
07-29 10:22:41.174 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:41.174000] [UseCarPageState._onCarControlAllStatusReceived (use_car_page.dart:735:18)] [MQTT]===收到车辆控制全状态MQTT消息,data: {collectTime: 1753755132532, acStatus: 0, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 0, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753755761174}
07-29 10:22:41.175 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:41.174000] [UseCarPageState._handleCarControlAllStatusFromMap (use_car_page.dart:822:18)] [MQTT]===处理从protobuf转换而来的Map格式数据，_handleCarControlAllStatusFromMap: {collectTime: 1753755132532, acStatus: 0, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 0, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753755761174}
07-29 10:22:41.175 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:41.175000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:866:24)] [MQTT]===时间戳1753755132532转换为2025-07-29 10:12:12
07-29 10:22:41.176 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:41.175000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:15:52，输入时间为2025-07-29 10:12:12，相差-220000，状态未过期
07-29 10:22:41.176 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:41.176000] [UseCarPageState._refreshCarControlUI (use_car_page.dart:1020:18)] 车辆控制状态已更新，刷新UI
07-29 10:22:41.176 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:41.176000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:875:22)] [MQTT]===是否需要更新车辆数据: isNeedUpdate:false
07-29 10:22:42.663 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.662000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:152:18)] MQTTManager: 接收消息参数
07-29 10:22:42.665 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.664000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:153:18)] MQTTManager:   主题: LK6AEAE44SB192188/prod/sgmw/vehicle/control
07-29 10:22:42.666 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.665000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:154:18)] MQTTManager:   数据长度: 48
07-29 10:22:42.668 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.667000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:155:18)] MQTTManager:   QoS级别: 1
07-29 10:22:42.669 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.668000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:156:18)] MQTTManager:   是否Base64编码: true
07-29 10:22:42.671 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.670000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:160:18)] MQTTManager:   匹配的主题枚举: MQTTTopic.carRemoteAsyncResult
07-29 10:22:42.676 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.675000] [MQTTManager._notifyListeners (mqtt_manager.dart:492:18)] MQTTManager: 通知监听器 - 主题: MQTTTopic.carRemoteAsyncResult, 监听器数量: 1
07-29 10:22:42.678 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.678000] [UseCarPageState._onCarRemoteAsyncResultReceived (use_car_page.dart:198:18)] [MQTT]===处理接收到的车辆远程异步结果消息,data: {code: 0, message: 操作成功, serviceCode: acStatus, collectTime: 1753755135000, timestamp: 1753755762675}
07-29 10:22:42.682 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.682000] [UseCarPageState._handleCarRemoteAsyncResultFromMap (use_car_page.dart:383:18)] [MQTT]===收到车辆远程异步结果MQTT消息,处理Map格式的车辆远程异步结果，_handleCarRemoteAsyncResultFromMap,data:：{code: 0, message: 操作成功, serviceCode: acStatus, collectTime: 1753755135000, timestamp: 1753755762675}
07-29 10:22:42.683 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.683000] [UseCarPageState._handleCarRemoteAsyncResultFromMap (use_car_page.dart:385:18)]   [MQTT]===代码: 0
07-29 10:22:42.684 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.684000] [UseCarPageState._handleCarRemoteAsyncResultFromMap (use_car_page.dart:386:18)]   [MQTT]===消息: 操作成功
07-29 10:22:42.685 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.685000] [UseCarPageState._handleCarRemoteAsyncResultFromMap (use_car_page.dart:387:18)]   [MQTT]===服务代码: acStatus
07-29 10:22:42.687 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.686000] [UseCarPageState._handleCarRemoteAsyncResultFromMap (use_car_page.dart:388:18)]   [MQTT]===采集时间: 1753755135000
07-29 10:22:42.687 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.687000] [UseCarPageState._handleCarRemoteAsyncResultFromMap (use_car_page.dart:389:18)]   [MQTT]===时间戳: 1753755762675
07-29 10:22:42.690 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.689000] [UseCarPageState.hasCurrentRemoteControlInPerformed (use_car_page.dart:364:18)] [MQTT]===是否有本地远控异步指令正在执行,result：true
07-29 10:22:42.691 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.690000] [UseCarPageState._handleCarRemoteAsyncResultFromMap (use_car_page.dart:409:30)] [MQTT]===时间戳1753755135000转换为2025-07-29 10:12:15
07-29 10:22:42.692 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.691000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:12:15
07-29 10:22:42.704 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.702000] [use_car_page.dart:469:34] [MQTT]===[CAR_STATUS]===acStatus的值从2更新为2
07-29 10:22:42.705 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.704000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3592:20)] 旧空调状态：0, 新空调状态：2
07-29 10:22:42.708 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.706000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3596:22)] 空调状态发生了改变
07-29 10:22:42.712 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.711000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:42.712 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.712000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:42.713 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.712000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:42.713 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.713000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:42.714 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.713000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:42.714 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.714000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:42.715 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.715000] [MQTTManager.stopMqttTimeOutTimer (mqtt_manager.dart:627:18)] MQTTManager: 停止接收MQTT异步结果推送超时定时器
07-29 10:22:42.722 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.721000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat1HotStatus 获取的状态是: 7
07-29 10:22:42.723 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.722000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat2HotStatus 获取的状态是: 7
07-29 10:22:42.723 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.723000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat3HotStatus 获取的状态是: 7
07-29 10:22:42.724 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.723000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat4HotStatus 获取的状态是: 7
07-29 10:22:42.724 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.724000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat1WindStatus 获取的状态是: 7
07-29 10:22:42.725 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.725000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat2WindStatus 获取的状态是: 7
07-29 10:22:42.728 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.726000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat3WindStatus 获取的状态是: 7
07-29 10:22:42.729 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.728000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat4WindStatus 获取的状态是: 7
07-29 10:22:42.748 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.748000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:22:42.762 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.760000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:22:42.763 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.762000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:22:42.765 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.764000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:22:42.771 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:42.769000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  2
07-29 10:22:43.819 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:43.818000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:22:44.110 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:44.109000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333808, longitude: 109.366771, position: null, collectTime: 2025-07-29 10:15:52, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 0, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 35, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.06, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 41, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:22:44.113 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:44.112000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:12:15，输入时间为2025-07-29 10:15:52，相差217000，状态已过期，更新输入时间为状态时间
07-29 10:22:44.114 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:44.113000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:15:52
07-29 10:22:44.130 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:44.129000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:1 , _currentACStatus:0,_statusModel?.acStatus:2 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:22:44.131 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:44.130000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3592:20)] 旧空调状态：2, 新空调状态：0
07-29 10:22:44.132 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:44.131000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3596:22)] 空调状态发生了改变
07-29 10:22:44.133 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:44.132000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:22:44.138 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:44.135000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:44.139 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:44.138000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:44.140 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:44.139000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:44.140 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:44.140000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:44.143 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:44.142000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat1HotStatus 获取的状态是: 7
07-29 10:22:44.144 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:44.143000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat2HotStatus 获取的状态是: 7
07-29 10:22:44.146 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:44.145000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat3HotStatus 获取的状态是: 7
07-29 10:22:44.147 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:44.146000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat4HotStatus 获取的状态是: 7
07-29 10:22:44.148 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:44.147000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat1WindStatus 获取的状态是: 7
07-29 10:22:44.149 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:44.148000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat2WindStatus 获取的状态是: 7
07-29 10:22:44.150 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:44.150000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat3WindStatus 获取的状态是: 7
07-29 10:22:44.151 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:44.151000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat4WindStatus 获取的状态是: 7
07-29 10:22:44.174 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:44.173000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:22:44.182 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:44.180000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:22:44.183 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:44.182000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:22:44.185 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:44.184000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:22:44.190 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:44.189000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:22:44.228 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:44.228000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:22:44.497 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:44.496000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 10:22:45.118 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.116000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:152:18)] MQTTManager: 接收消息参数
07-29 10:22:45.120 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.118000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:153:18)] MQTTManager:   主题: LK6AEAE44SB192188/prod/sgmw/vehicle/app/status
07-29 10:22:45.122 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.120000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:154:18)] MQTTManager:   数据长度: 156
07-29 10:22:45.123 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.122000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:155:18)] MQTTManager:   QoS级别: 1
07-29 10:22:45.126 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.124000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:156:18)] MQTTManager:   是否Base64编码: true
07-29 10:22:45.128 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.127000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:160:18)] MQTTManager:   匹配的主题枚举: MQTTTopic.carControlAllStatus
07-29 10:22:45.132 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.131000] [MQTTManager._convertCarStatusToMap (mqtt_manager.dart:698:18)] MQTTManager:将SgmwAppCarStatus protobuf对象转换为Map ,carStatus.collectTime:1753755137712 , carStatus.collectTime.toInt():1753755137712
07-29 10:22:45.134 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.133000] [MQTTManager._notifyListeners (mqtt_manager.dart:492:18)] MQTTManager: 通知监听器 - 主题: MQTTTopic.carControlAllStatus, 监听器数量: 1
07-29 10:22:45.136 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.135000] [UseCarPageState._onCarControlAllStatusReceived (use_car_page.dart:735:18)] [MQTT]===收到车辆控制全状态MQTT消息,data: {collectTime: 1753755137712, acStatus: 1, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 4, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753755765133}
07-29 10:22:45.138 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.137000] [UseCarPageState._handleCarControlAllStatusFromMap (use_car_page.dart:822:18)] [MQTT]===处理从protobuf转换而来的Map格式数据，_handleCarControlAllStatusFromMap: {collectTime: 1753755137712, acStatus: 1, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 4, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753755765133}
07-29 10:22:45.140 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.139000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:866:24)] [MQTT]===时间戳1753755137712转换为2025-07-29 10:12:17
07-29 10:22:45.142 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.141000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:15:52，输入时间为2025-07-29 10:12:17，相差-215000，状态未过期
07-29 10:22:45.143 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.142000] [UseCarPageState._refreshCarControlUI (use_car_page.dart:1020:18)] 车辆控制状态已更新，刷新UI
07-29 10:22:45.144 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.144000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:875:22)] [MQTT]===是否需要更新车辆数据: isNeedUpdate:false
07-29 10:22:45.148 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.146000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:152:18)] MQTTManager: 接收消息参数
07-29 10:22:45.150 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.149000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:153:18)] MQTTManager:   主题: LK6AEAE44SB192188/prod/sgmw/vehicle/app/status
07-29 10:22:45.151 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.150000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:154:18)] MQTTManager:   数据长度: 156
07-29 10:22:45.152 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.151000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:155:18)] MQTTManager:   QoS级别: 1
07-29 10:22:45.153 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.152000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:156:18)] MQTTManager:   是否Base64编码: true
07-29 10:22:45.154 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.153000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:160:18)] MQTTManager:   匹配的主题枚举: MQTTTopic.carControlAllStatus
07-29 10:22:45.156 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.155000] [MQTTManager._convertCarStatusToMap (mqtt_manager.dart:698:18)] MQTTManager:将SgmwAppCarStatus protobuf对象转换为Map ,carStatus.collectTime:1753755137812 , carStatus.collectTime.toInt():1753755137812
07-29 10:22:45.157 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.156000] [MQTTManager._notifyListeners (mqtt_manager.dart:492:18)] MQTTManager: 通知监听器 - 主题: MQTTTopic.carControlAllStatus, 监听器数量: 1
07-29 10:22:45.158 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.157000] [UseCarPageState._onCarControlAllStatusReceived (use_car_page.dart:735:18)] [MQTT]===收到车辆控制全状态MQTT消息,data: {collectTime: 1753755137812, acStatus: 1, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 5, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753755765156}
07-29 10:22:45.159 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.159000] [UseCarPageState._handleCarControlAllStatusFromMap (use_car_page.dart:822:18)] [MQTT]===处理从protobuf转换而来的Map格式数据，_handleCarControlAllStatusFromMap: {collectTime: 1753755137812, acStatus: 1, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 5, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753755765156}
07-29 10:22:45.161 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.160000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:866:24)] [MQTT]===时间戳1753755137812转换为2025-07-29 10:12:17
07-29 10:22:45.163 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.162000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:15:52，输入时间为2025-07-29 10:12:17，相差-215000，状态未过期
07-29 10:22:45.164 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.163000] [UseCarPageState._refreshCarControlUI (use_car_page.dart:1020:18)] 车辆控制状态已更新，刷新UI
07-29 10:22:45.165 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:45.164000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:875:22)] [MQTT]===是否需要更新车辆数据: isNeedUpdate:false
07-29 10:22:46.238 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:46.235000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:152:18)] MQTTManager: 接收消息参数
07-29 10:22:46.240 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:46.239000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:153:18)] MQTTManager:   主题: LK6AEAE44SB192188/prod/sgmw/vehicle/app/status
07-29 10:22:46.241 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:46.240000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:154:18)] MQTTManager:   数据长度: 156
07-29 10:22:46.242 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:46.241000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:155:18)] MQTTManager:   QoS级别: 1
07-29 10:22:46.243 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:46.242000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:156:18)] MQTTManager:   是否Base64编码: true
07-29 10:22:46.244 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:46.243000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:160:18)] MQTTManager:   匹配的主题枚举: MQTTTopic.carControlAllStatus
07-29 10:22:46.245 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:46.245000] [MQTTManager._convertCarStatusToMap (mqtt_manager.dart:698:18)] MQTTManager:将SgmwAppCarStatus protobuf对象转换为Map ,carStatus.collectTime:1753755139262 , carStatus.collectTime.toInt():1753755139262
07-29 10:22:46.248 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:46.247000] [MQTTManager._notifyListeners (mqtt_manager.dart:492:18)] MQTTManager: 通知监听器 - 主题: MQTTTopic.carControlAllStatus, 监听器数量: 1
07-29 10:22:46.249 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:46.248000] [UseCarPageState._onCarControlAllStatusReceived (use_car_page.dart:735:18)] [MQTT]===收到车辆控制全状态MQTT消息,data: {collectTime: 1753755139262, acStatus: 1, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 6, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753755766246}
07-29 10:22:46.251 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:46.250000] [UseCarPageState._handleCarControlAllStatusFromMap (use_car_page.dart:822:18)] [MQTT]===处理从protobuf转换而来的Map格式数据，_handleCarControlAllStatusFromMap: {collectTime: 1753755139262, acStatus: 1, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 6, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753755766246}
07-29 10:22:46.252 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:46.251000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:866:24)] [MQTT]===时间戳1753755139262转换为2025-07-29 10:12:19
07-29 10:22:46.254 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:46.253000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:15:52，输入时间为2025-07-29 10:12:19，相差-213000，状态未过期
07-29 10:22:46.255 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:46.254000] [UseCarPageState._refreshCarControlUI (use_car_page.dart:1020:18)] 车辆控制状态已更新，刷新UI
07-29 10:22:46.256 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:46.256000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:875:22)] [MQTT]===是否需要更新车辆数据: isNeedUpdate:false
07-29 10:22:46.819 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:46.818000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:22:47.105 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.103000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:152:18)] MQTTManager: 接收消息参数
07-29 10:22:47.106 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.105000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:153:18)] MQTTManager:   主题: LK6AEAE44SB192188/prod/sgmw/vehicle/app/status
07-29 10:22:47.107 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.106000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:154:18)] MQTTManager:   数据长度: 156
07-29 10:22:47.108 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.107000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:155:18)] MQTTManager:   QoS级别: 1
07-29 10:22:47.109 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.108000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:156:18)] MQTTManager:   是否Base64编码: true
07-29 10:22:47.110 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.109000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:160:18)] MQTTManager:   匹配的主题枚举: MQTTTopic.carControlAllStatus
07-29 10:22:47.111 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.111000] [MQTTManager._convertCarStatusToMap (mqtt_manager.dart:698:18)] MQTTManager:将SgmwAppCarStatus protobuf对象转换为Map ,carStatus.collectTime:1753755140214 , carStatus.collectTime.toInt():1753755140214
07-29 10:22:47.112 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.112000] [MQTTManager._notifyListeners (mqtt_manager.dart:492:18)] MQTTManager: 通知监听器 - 主题: MQTTTopic.carControlAllStatus, 监听器数量: 1
07-29 10:22:47.114 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.113000] [UseCarPageState._onCarControlAllStatusReceived (use_car_page.dart:735:18)] [MQTT]===收到车辆控制全状态MQTT消息,data: {collectTime: 1753755140214, acStatus: 1, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 7, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753755767112}
07-29 10:22:47.115 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.114000] [UseCarPageState._handleCarControlAllStatusFromMap (use_car_page.dart:822:18)] [MQTT]===处理从protobuf转换而来的Map格式数据，_handleCarControlAllStatusFromMap: {collectTime: 1753755140214, acStatus: 1, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 7, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753755767112}
07-29 10:22:47.116 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.115000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:866:24)] [MQTT]===时间戳1753755140214转换为2025-07-29 10:12:20
07-29 10:22:47.118 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.117000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:15:52，输入时间为2025-07-29 10:12:20，相差-212000，状态未过期
07-29 10:22:47.119 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.118000] [UseCarPageState._refreshCarControlUI (use_car_page.dart:1020:18)] 车辆控制状态已更新，刷新UI
07-29 10:22:47.120 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.119000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:875:22)] [MQTT]===是否需要更新车辆数据: isNeedUpdate:false
07-29 10:22:47.135 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.134000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333808, longitude: 109.366771, position: null, collectTime: 2025-07-29 10:15:52, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 0, leftBatteryPower: 24.2, batterySoc: 36, current: 0, voltage: 0, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 35, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.06, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 41, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 7, seat4WindStatus: 7}, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:22:47.135 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.135000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:15:52，输入时间为2025-07-29 10:15:52，相差0，状态已过期，更新输入时间为状态时间
07-29 10:22:47.136 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.136000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:15:52
07-29 10:22:47.140 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.139000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:1 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:22:47.140 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.140000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3592:20)] 旧空调状态：0, 新空调状态：0
07-29 10:22:47.140 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.140000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3608:22)] 空调状态没有发生改变
07-29 10:22:47.141 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.141000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:22:47.143 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.142000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:47.143 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.143000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:47.144 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.144000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:47.144 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.144000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:47.145 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.145000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat1HotStatus 获取的状态是: 7
07-29 10:22:47.147 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.147000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat2HotStatus 获取的状态是: 7
07-29 10:22:47.148 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.148000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat3HotStatus 获取的状态是: 7
07-29 10:22:47.149 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.148000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat4HotStatus 获取的状态是: 7
07-29 10:22:47.149 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.149000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat1WindStatus 获取的状态是: 7
07-29 10:22:47.150 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.150000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat2WindStatus 获取的状态是: 7
07-29 10:22:47.151 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.150000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat3WindStatus 获取的状态是: 7
07-29 10:22:47.152 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.152000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat4WindStatus 获取的状态是: 7
07-29 10:22:47.167 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.167000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:22:47.174 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.172000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:22:47.175 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.174000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:22:47.177 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.176000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:22:47.181 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.180000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:22:47.211 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:47.210000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:22:49.490 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:49.489000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 10:22:49.824 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:49.823000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:22:50.245 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:50.244000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333808, longitude: 109.366771, position: null, collectTime: 2025-07-29 10:22:41, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.7, voltage: 420.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 32, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.46, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 42, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:22:50.247 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:50.247000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:15:52，输入时间为2025-07-29 10:22:41，相差409000，状态已过期，更新输入时间为状态时间
07-29 10:22:50.248 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:50.248000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:22:41
07-29 10:22:50.264 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:50.263000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:1 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:22:50.265 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:50.264000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3592:20)] 旧空调状态：0, 新空调状态：0
07-29 10:22:50.266 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:50.265000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3608:22)] 空调状态没有发生改变
07-29 10:22:50.267 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:50.266000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:22:50.271 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:50.270000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:50.272 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:50.271000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:50.274 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:50.273000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:50.275 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:50.274000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:50.277 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:50.276000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat1HotStatus 获取的状态是: 7
07-29 10:22:50.278 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:50.278000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat2HotStatus 获取的状态是: 7
07-29 10:22:50.280 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:50.279000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat3HotStatus 获取的状态是: 7
07-29 10:22:50.281 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:50.280000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat4HotStatus 获取的状态是: 7
07-29 10:22:50.282 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:50.281000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat1WindStatus 获取的状态是: 7
07-29 10:22:50.283 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:50.282000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat2WindStatus 获取的状态是: 7
07-29 10:22:50.284 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:50.283000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat3WindStatus 获取的状态是:
07-29 10:22:50.287 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:50.286000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:849:20)] 格式错误: seat3WindStatus 状态异常了，取本地状态，当前本地状态是: 0
07-29 10:22:50.287 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:50.287000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat4WindStatus 获取的状态是:
07-29 10:22:50.288 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:50.288000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:849:20)] 格式错误: seat4WindStatus 状态异常了，取本地状态，当前本地状态是: 0
07-29 10:22:50.304 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:50.304000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:22:50.312 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:50.311000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:22:50.313 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:50.312000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:22:50.315 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:50.314000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:22:50.320 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:50.319000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:22:50.352 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:50.352000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:22:52.820 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:52.818000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:22:53.081 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:53.079000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 101, rechargeStatus: , avgFuel: 25.5, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333808, longitude: 109.366771, position: null, collectTime: 2025-07-29 10:22:41, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.7, voltage: 420.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 32, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.46, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 42, avgElectronFuel: null, oilLeftMileage: 2047, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:22:53.083 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:53.082000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:22:41，输入时间为2025-07-29 10:22:41，相差0，状态已过期，更新输入时间为状态时间
07-29 10:22:53.084 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:53.083000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:22:41
07-29 10:22:53.088 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:53.087000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:1 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:22:53.089 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:53.088000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3592:20)] 旧空调状态：0, 新空调状态：0
07-29 10:22:53.090 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:53.089000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3608:22)] 空调状态没有发生改变
07-29 10:22:53.091 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:53.090000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:22:53.094 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:53.093000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:53.094 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:53.094000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:53.096 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:53.095000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 7 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:53.097 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:53.096000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:53.099 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:53.098000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat1HotStatus 获取的状态是: 7
07-29 10:22:53.101 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:53.100000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat2HotStatus 获取的状态是: 7
07-29 10:22:53.102 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:53.101000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat3HotStatus 获取的状态是: 7
07-29 10:22:53.103 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:53.102000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat4HotStatus 获取的状态是: 7
07-29 10:22:53.104 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:53.103000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat1WindStatus 获取的状态是: 7
07-29 10:22:53.105 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:53.104000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat2WindStatus 获取的状态是: 7
07-29 10:22:53.107 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:53.106000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat3WindStatus 获取的状态是:
07-29 10:22:53.108 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:53.107000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:849:20)] 格式错误: seat3WindStatus 状态异常了，取本地状态，当前本地状态是: 0
07-29 10:22:53.109 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:53.108000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat4WindStatus 获取的状态是:
07-29 10:22:53.110 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:53.109000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:849:20)] 格式错误: seat4WindStatus 状态异常了，取本地状态，当前本地状态是: 0
07-29 10:22:53.125 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:53.124000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:22:53.132 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:53.131000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:22:53.134 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:53.132000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:22:53.135 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:53.134000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:22:53.139 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:53.138000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:22:53.166 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:53.166000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:22:54.490 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:54.489000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 10:22:55.820 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:55.818000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:22:56.143 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.141000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 1, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333809, longitude: 109.366767, position: null, collectTime: 2025-07-29 10:22:51, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 1.9, voltage: 420.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 33, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.42, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:22:56.145 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.144000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:22:41，输入时间为2025-07-29 10:22:51，相差10000，状态已过期，更新输入时间为状态时间
07-29 10:22:56.145 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.145000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:22:51
07-29 10:22:56.159 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.158000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:1 ,_MQTTBackStatus:1 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:22:56.160 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.160000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3592:20)] 旧空调状态：0, 新空调状态：1
07-29 10:22:56.161 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.161000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3596:22)] 空调状态发生了改变
07-29 10:22:56.162 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.162000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:22:56.164 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.164000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:56.165 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.165000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:56.166 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.166000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:56.167 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.167000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:56.168 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.167000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:56.169 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.168000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:56.172 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.171000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat1HotStatus 获取的状态是: 7
07-29 10:22:56.174 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.173000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat2HotStatus 获取的状态是: 7
07-29 10:22:56.175 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.174000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat3HotStatus 获取的状态是: 7
07-29 10:22:56.177 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.175000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat4HotStatus 获取的状态是: 7
07-29 10:22:56.178 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.177000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat1WindStatus 获取的状态是: 3
07-29 10:22:56.180 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.179000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat2WindStatus 获取的状态是: 7
07-29 10:22:56.181 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.180000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat3WindStatus 获取的状态是:
07-29 10:22:56.182 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.181000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:849:20)] 格式错误: seat3WindStatus 状态异常了，取本地状态，当前本地状态是: 0
07-29 10:22:56.183 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.182000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat4WindStatus 获取的状态是:
07-29 10:22:56.184 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.183000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:849:20)] 格式错误: seat4WindStatus 状态异常了，取本地状态，当前本地状态是: 0
07-29 10:22:56.214 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.214000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:22:56.222 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.221000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:22:56.224 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.223000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:22:56.226 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.225000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:22:56.230 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.229000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  1
07-29 10:22:56.268 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:56.268000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:22:58.824 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:58.823000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:22:59.182 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.180000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 1, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333809, longitude: 109.366767, position: null, collectTime: 2025-07-29 10:22:51, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 1.9, voltage: 420.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 33, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.42, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:22:59.184 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.183000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:22:51，输入时间为2025-07-29 10:22:51，相差0，状态已过期，更新输入时间为状态时间
07-29 10:22:59.185 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.184000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:22:51
07-29 10:22:59.192 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.191000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:1 ,_MQTTBackStatus:1 , _currentACStatus:0,_statusModel?.acStatus:1 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:22:59.193 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.192000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3592:20)] 旧空调状态：1, 新空调状态：1
07-29 10:22:59.194 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.193000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3608:22)] 空调状态没有发生改变
07-29 10:22:59.194 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.194000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:22:59.196 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.196000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:59.197 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.197000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:59.198 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.197000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:59.198 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.198000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:59.199 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.199000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:22:59.199 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.199000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:22:59.200 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.200000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:22:59.202 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.202000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat1HotStatus 获取的状态是: 7
07-29 10:22:59.203 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.203000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat2HotStatus 获取的状态是: 7
07-29 10:22:59.204 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.204000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat3HotStatus 获取的状态是: 7
07-29 10:22:59.205 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.205000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat4HotStatus 获取的状态是: 7
07-29 10:22:59.207 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.207000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat1WindStatus 获取的状态是: 3
07-29 10:22:59.208 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.208000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat2WindStatus 获取的状态是: 7
07-29 10:22:59.208 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.208000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat3WindStatus 获取的状态是:
07-29 10:22:59.209 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.209000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:849:20)] 格式错误: seat3WindStatus 状态异常了，取本地状态，当前本地状态是: 0
07-29 10:22:59.209 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.209000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat4WindStatus 获取的状态是:
07-29 10:22:59.210 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.209000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:849:20)] 格式错误: seat4WindStatus 状态异常了，取本地状态，当前本地状态是: 0
07-29 10:22:59.225 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.224000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:22:59.230 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.229000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:22:59.231 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.231000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:22:59.233 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.232000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:22:59.237 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.236000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  1
07-29 10:22:59.496 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:22:59.495000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 10:23:01.820 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:01.818000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:23:02.091 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.090000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 1, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333809, longitude: 109.366767, position: null, collectTime: 2025-07-29 10:22:51, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 1.9, voltage: 420.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 33, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.42, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:23:02.094 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.093000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:22:51，输入时间为2025-07-29 10:22:51，相差0，状态已过期，更新输入时间为状态时间
07-29 10:23:02.095 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.094000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:22:51
07-29 10:23:02.103 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.102000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:1 ,_MQTTBackStatus:1 , _currentACStatus:0,_statusModel?.acStatus:1 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:23:02.104 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.104000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3592:20)] 旧空调状态：1, 新空调状态：1
07-29 10:23:02.105 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.104000] [UseCarPageState.checkAcSettingDialogWithStatus (use_car_page.dart:3608:22)] 空调状态没有发生改变
07-29 10:23:02.106 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.105000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:23:02.108 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.107000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:02.109 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.108000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:02.110 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.109000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:02.111 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.110000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:02.112 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.111000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:02.112 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.112000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:02.114 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.114000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat1HotStatus 获取的状态是: 7
07-29 10:23:02.115 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.114000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat2HotStatus 获取的状态是: 7
07-29 10:23:02.116 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.115000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat3HotStatus 获取的状态是: 7
07-29 10:23:02.117 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.116000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat4HotStatus 获取的状态是: 7
07-29 10:23:02.118 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.117000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat1WindStatus 获取的状态是: 3
07-29 10:23:02.118 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.118000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat2WindStatus 获取的状态是: 7
07-29 10:23:02.119 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.119000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat3WindStatus 获取的状态是:
07-29 10:23:02.120 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.119000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:849:20)] 格式错误: seat3WindStatus 状态异常了，取本地状态，当前本地状态是: 0
07-29 10:23:02.120 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.120000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat4WindStatus 获取的状态是:
07-29 10:23:02.121 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.120000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:849:20)] 格式错误: seat4WindStatus 状态异常了，取本地状态，当前本地状态是: 0
07-29 10:23:02.137 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.137000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:23:02.145 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.143000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:23:02.146 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.145000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:23:02.148 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.147000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:23:02.152 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.151000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  1
07-29 10:23:02.181 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:02.181000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:23:03.274 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:03.274000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:23:03.282 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:03.281000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:23:03.283 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:03.282000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:23:03.285 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:03.284000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:23:03.288 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:03.287000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  1
07-29 10:23:04.393 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:04.393000] [UseCarPageState.doCarControlNetwork (UseCarPageState.doCarControlNetwork (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2808)] 开始车控，serviceStatusResultType: 0 ，backStatusValue： , serviceCode: acStatus , mAccType:4 , 车控accParameter:19 ,车控params:{buttonLayout: 1}
07-29 10:23:04.490 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:04.490000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 10:23:04.819 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:04.818000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:23:05.164 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:05.163000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 1, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333809, longitude: 109.366767, position: null, collectTime: 2025-07-29 10:22:51, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 1.9, voltage: 420.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 33, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.42, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:23:05.167 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:05.166000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:22:51，输入时间为2025-07-29 10:22:51，相差0，状态已过期，更新输入时间为状态时间
07-29 10:23:05.167 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:05.167000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:22:51
07-29 10:23:05.174 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:05.174000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:1 ,_MQTTBackStatus:1 , _currentACStatus:0,_statusModel?.acStatus:1 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:23:05.175 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:05.175000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:23:05.178 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:05.177000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:05.178 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:05.178000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:05.179 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:05.179000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:05.180 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:05.179000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:05.181 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:05.180000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:05.181 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:05.181000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:05.182 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:05.182000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:23:05.184 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:05.183000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:23:05.198 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:05.196000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:23:05.200 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:05.198000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:23:05.203 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:05.201000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:23:05.210 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:05.209000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  1
07-29 10:23:06.213 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:06.211000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:152:18)] MQTTManager: 接收消息参数
07-29 10:23:06.215 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:06.214000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:153:18)] MQTTManager:   主题: LK6AEAE44SB192188/prod/sgmw/vehicle/app/status
07-29 10:23:06.218 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:06.216000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:154:18)] MQTTManager:   数据长度: 156
07-29 10:23:06.219 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:06.218000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:155:18)] MQTTManager:   QoS级别: 1
07-29 10:23:06.221 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:06.220000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:156:18)] MQTTManager:   是否Base64编码: true
07-29 10:23:06.223 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:06.222000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:160:18)] MQTTManager:   匹配的主题枚举: MQTTTopic.carControlAllStatus
07-29 10:23:06.226 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:06.225000] [MQTTManager._convertCarStatusToMap (mqtt_manager.dart:698:18)] MQTTManager:将SgmwAppCarStatus protobuf对象转换为Map ,carStatus.collectTime:1753755165812 , carStatus.collectTime.toInt():1753755165812
07-29 10:23:06.227 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:06.227000] [MQTTManager._notifyListeners (mqtt_manager.dart:492:18)] MQTTManager: 通知监听器 - 主题: MQTTTopic.carControlAllStatus, 监听器数量: 1
07-29 10:23:06.229 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:06.228000] [UseCarPageState._onCarControlAllStatusReceived (use_car_page.dart:735:18)] [MQTT]===收到车辆控制全状态MQTT消息,data: {collectTime: 1753755165812, acStatus: 0, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 0, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753755786226}
07-29 10:23:06.230 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:06.229000] [UseCarPageState._handleCarControlAllStatusFromMap (use_car_page.dart:822:18)] [MQTT]===处理从protobuf转换而来的Map格式数据，_handleCarControlAllStatusFromMap: {collectTime: 1753755165812, acStatus: 0, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 0, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753755786226}
07-29 10:23:06.232 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:06.231000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:866:24)] [MQTT]===时间戳1753755165812转换为2025-07-29 10:12:45
07-29 10:23:06.234 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:06.233000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:22:51，输入时间为2025-07-29 10:12:45，相差-606000，状态未过期
07-29 10:23:06.235 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:06.234000] [UseCarPageState._refreshCarControlUI (use_car_page.dart:1020:18)] 车辆控制状态已更新，刷新UI
07-29 10:23:06.235 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:06.235000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:875:22)] [MQTT]===是否需要更新车辆数据: isNeedUpdate:false
07-29 10:23:06.251 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:06.250000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:06.252 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:06.251000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:06.252 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:06.252000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:06.253 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:06.253000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:06.254 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:06.253000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:06.255 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:06.254000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:06.256 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:06.255000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:23:05
07-29 10:23:06.265 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:06.264000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:23:06.281 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:06.279000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:23:06.283 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:06.281000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:23:06.286 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:06.284000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1333:20)] isNewAir() 当前是旧空调
07-29 10:23:06.294 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:06.292000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：
07-29 10:23:07.820 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:07.818000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:23:08.126 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:08.125000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 1, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333806, longitude: 109.366766, position: null, collectTime: 2025-07-29 10:23:01, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 3, voltage: 420.2, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 33, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.02, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:23:08.129 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:08.128000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:23:05，输入时间为2025-07-29 10:23:01，相差-4000，状态未过期
07-29 10:23:09.490 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:09.489000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 10:23:10.820 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:10.819000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:23:11.054 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:11.053000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 1, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333806, longitude: 109.366766, position: null, collectTime: 2025-07-29 10:23:01, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 3, voltage: 420.2, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 33, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.02, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:23:11.057 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:11.056000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:23:05，输入时间为2025-07-29 10:23:01，相差-4000，状态未过期
07-29 10:23:13.819 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:13.818000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:23:14.048 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:14.047000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 1, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333806, longitude: 109.366766, position: null, collectTime: 2025-07-29 10:23:01, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 3, voltage: 420.2, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 33, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.02, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:23:14.051 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:14.050000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:23:05，输入时间为2025-07-29 10:23:01，相差-4000，状态未过期
07-29 10:23:14.489 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:14.488000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 10:23:15.210 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:15.209000] [UseCarPageState.handleControlActionWithItem (use_car_page.dart:2340:11)] 点击了第0个车控按钮:空调 座椅通风加热-F511C LV2高配主副通风加热都有  后排只有加热 当前状态:通风已开
07-29 10:23:15.210 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:15.210000] [UseCarPageState.handleControlActionWithItem (use_car_page.dart:2401:17)] 处理 车控 类型 4 , serviceCode =acStatus , acStatus =
07-29 10:23:15.221 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:15.219000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat1HotStatus 获取的状态是: 7
07-29 10:23:15.224 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:15.221000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat2HotStatus 获取的状态是: 7
07-29 10:23:15.226 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:15.224000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat3HotStatus 获取的状态是: 7
07-29 10:23:15.227 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:15.226000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat4HotStatus 获取的状态是: 7
07-29 10:23:15.229 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:15.228000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat1WindStatus 获取的状态是: 3
07-29 10:23:15.231 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:15.229000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat2WindStatus 获取的状态是: 7
07-29 10:23:15.232 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:15.231000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat3WindStatus 获取的状态是:
07-29 10:23:15.234 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:15.233000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:849:20)] 格式错误: seat3WindStatus 状态异常了，取本地状态，当前本地状态是: 0
07-29 10:23:15.235 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:15.234000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat4WindStatus 获取的状态是:
07-29 10:23:15.236 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:15.235000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:849:20)] 格式错误: seat4WindStatus 状态异常了，取本地状态，当前本地状态是: 0
07-29 10:23:16.597 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:16.597000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:23:16.603 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:16.602000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:23:16.604 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:16.603000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:23:16.606 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:16.605000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1333:20)] isNewAir() 当前是旧空调
07-29 10:23:16.609 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:16.608000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：
07-29 10:23:16.818 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:16.817000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:23:17.142 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:17.140000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 1, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333806, longitude: 109.366766, position: null, collectTime: 2025-07-29 10:23:01, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 3, voltage: 420.2, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 33, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.02, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:23:17.144 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:17.144000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:23:05，输入时间为2025-07-29 10:23:01，相差-4000，状态未过期
07-29 10:23:17.739 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:17.738000] [UseCarPageState.handleControlActionWithItem (use_car_page.dart:2340:11)] 点击了第0个车控按钮:空调 座椅通风加热-F511C LV2高配主副通风加热都有  后排只有加热 当前状态:通风已开
07-29 10:23:17.739 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:17.739000] [UseCarPageState.handleControlActionWithItem (use_car_page.dart:2401:17)] 处理 车控 类型 4 , serviceCode =acStatus , acStatus =
07-29 10:23:17.751 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:17.749000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat1HotStatus 获取的状态是: 7
07-29 10:23:17.753 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:17.751000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat2HotStatus 获取的状态是: 7
07-29 10:23:17.756 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:17.754000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat3HotStatus 获取的状态是: 7
07-29 10:23:17.758 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:17.756000] [_AcSettingDialogState.setSeatHeating (car_control_ac_setting_dialog.dart:706:18)] seat4HotStatus 获取的状态是: 7
07-29 10:23:17.760 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:17.758000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat1WindStatus 获取的状态是: 3
07-29 10:23:17.761 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:17.760000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat2WindStatus 获取的状态是: 7
07-29 10:23:17.763 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:17.761000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat3WindStatus 获取的状态是:
07-29 10:23:17.765 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:17.763000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:849:20)] 格式错误: seat3WindStatus 状态异常了，取本地状态，当前本地状态是: 0
07-29 10:23:17.766 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:17.765000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:789:18)] seat4WindStatus 获取的状态是:
07-29 10:23:17.767 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:17.766000] [_AcSettingDialogState.setSeatVentilation (car_control_ac_setting_dialog.dart:849:20)] 格式错误: seat4WindStatus 状态异常了，取本地状态，当前本地状态是: 0
07-29 10:23:18.718 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:18.718000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:23:18.724 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:18.723000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:23:18.725 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:18.724000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:23:18.727 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:18.726000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1333:20)] isNewAir() 当前是旧空调
07-29 10:23:18.731 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:18.729000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：
07-29 10:23:19.488 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:19.487000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 10:23:19.646 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:19.645000] [UseCarPageState.doCarControlNetwork (UseCarPageState.doCarControlNetwork (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2808)] 开始车控，serviceStatusResultType: 0 ，backStatusValue： , serviceCode: acStatus , mAccType:4 , 车控accParameter:19 ,车控params:{buttonLayout: 1}
07-29 10:23:19.818 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:19.817000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:23:20.124 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.123000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333804, longitude: 109.36676, position: null, collectTime: 2025-07-29 10:23:11, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.5, voltage: 420.2, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 33, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.05, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:23:20.127 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.126000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:23:05，输入时间为2025-07-29 10:23:11，相差6000，状态已过期，更新输入时间为状态时间
07-29 10:23:20.128 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.127000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:23:11
07-29 10:23:20.143 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.142000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus: ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:23:20.143 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.143000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:23:20.146 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.145000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:20.146 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.146000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:20.147 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.147000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:20.148 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.148000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:20.149 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.149000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:20.150 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.149000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:20.152 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.151000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:23:20.170 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.166000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:23:20.173 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.170000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:23:20.178 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.175000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:23:20.187 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.185000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:23:20.226 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.226000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:23:20.955 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.955000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:20.956 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.956000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:20.956 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.956000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:20.957 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.957000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:20.957 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.957000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:20.958 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.957000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:20.958 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.958000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:23:20
07-29 10:23:20.978 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.978000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:23:20.987 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.986000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:23:20.987 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.987000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:23:20.989 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.988000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1333:20)] isNewAir() 当前是旧空调
07-29 10:23:20.992 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:20.991000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：
07-29 10:23:22.819 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:22.818000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:23:23.402 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:23.401000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333804, longitude: 109.36676, position: null, collectTime: 2025-07-29 10:23:11, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.5, voltage: 420.2, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 33, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.05, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:23:23.404 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:23.404000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:23:20，输入时间为2025-07-29 10:23:11，相差-9000，状态未过期
07-29 10:23:24.490 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:24.489000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 10:23:25.819 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:25.818000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:23:26.127 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:26.125000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333809, longitude: 109.366766, position: null, collectTime: 2025-07-29 10:23:21, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.5, voltage: 420.1, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.05, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:23:26.129 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:26.128000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:23:20，输入时间为2025-07-29 10:23:21，相差1000，状态已过期，更新输入时间为状态时间
07-29 10:23:26.130 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:26.129000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:23:21
07-29 10:23:26.148 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:26.147000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus: ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:23:26.149 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:26.148000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:23:26.152 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:26.151000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:26.153 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:26.152000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:26.153 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:26.153000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:26.154 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:26.154000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:26.155 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:26.155000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:26.156 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:26.155000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:26.161 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:26.160000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:23:26.172 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:26.171000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:23:26.174 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:26.173000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:23:26.175 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:26.174000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:23:26.181 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:26.179000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:23:26.212 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:26.212000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:23:28.820 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:28.819000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:23:29.130 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:29.129000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333809, longitude: 109.366766, position: null, collectTime: 2025-07-29 10:23:21, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.5, voltage: 420.1, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.05, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:23:29.131 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:29.131000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:23:21，输入时间为2025-07-29 10:23:21，相差0，状态已过期，更新输入时间为状态时间
07-29 10:23:29.133 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:29.132000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:23:21
07-29 10:23:29.136 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:29.136000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:23:29.137 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:29.137000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:23:29.138 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:29.138000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:29.138 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:29.138000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:29.139 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:29.139000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:29.139 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:29.139000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:29.140 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:29.140000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:29.140 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:29.140000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:29.141 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:29.140000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:23:29.150 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:29.149000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:23:29.159 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:29.157000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:23:29.160 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:29.159000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:23:29.163 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:29.161000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:23:29.169 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:29.167000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:23:29.496 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:29.495000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 10:23:31.820 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:31.818000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:23:32.030 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:32.029000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333809, longitude: 109.366766, position: null, collectTime: 2025-07-29 10:23:21, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.5, voltage: 420.1, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.05, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:23:32.033 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:32.032000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:23:21，输入时间为2025-07-29 10:23:21，相差0，状态已过期，更新输入时间为状态时间
07-29 10:23:32.033 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:32.033000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:23:21
07-29 10:23:32.039 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:32.038000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:23:32.039 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:32.039000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:23:32.042 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:32.041000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:32.043 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:32.042000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:32.043 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:32.043000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:32.044 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:32.044000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:32.045 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:32.045000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:32.046 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:32.045000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:32.048 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:32.047000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:23:32.066 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:32.062000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:23:32.070 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:32.066000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:23:32.073 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:32.072000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:23:32.079 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:32.077000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:23:32.110 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:32.109000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:23:34.489 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:34.488000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 10:23:34.820 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:34.818000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:23:35.092 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:35.091000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333809, longitude: 109.366766, position: null, collectTime: 2025-07-29 10:23:21, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.5, voltage: 420.1, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.05, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:23:35.093 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:35.093000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:23:21，输入时间为2025-07-29 10:23:21，相差0，状态已过期，更新输入时间为状态时间
07-29 10:23:35.094 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:35.094000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:23:21
07-29 10:23:35.099 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:35.099000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:23:35.100 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:35.100000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:23:35.101 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:35.101000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:35.102 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:35.102000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:35.102 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:35.102000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:35.103 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:35.102000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:35.103 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:35.103000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:35.103 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:35.103000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:35.104 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:35.104000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:23:35.110 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:35.110000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:23:35.120 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:35.119000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:23:35.122 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:35.121000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:23:35.125 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:35.123000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:23:35.132 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:35.130000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:23:37.826 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:37.824000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:23:38.085 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:38.084000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333809, longitude: 109.366767, position: null, collectTime: 2025-07-29 10:23:31, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.5, voltage: 420.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.05, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:23:38.089 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:38.088000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:23:21，输入时间为2025-07-29 10:23:31，相差10000，状态已过期，更新输入时间为状态时间
07-29 10:23:38.091 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:38.090000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:23:31
07-29 10:23:38.110 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:38.109000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:23:38.111 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:38.110000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:23:38.114 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:38.113000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:38.115 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:38.114000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:38.116 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:38.115000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:38.117 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:38.116000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:38.118 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:38.117000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:38.118 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:38.118000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:38.120 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:38.120000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:23:38.136 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:38.135000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:23:38.138 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:38.136000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:23:38.140 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:38.139000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:23:38.146 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:38.144000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:23:38.174 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:38.174000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:23:39.504 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:39.503000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 10:23:40.819 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:40.818000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:23:41.115 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:41.114000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333809, longitude: 109.366767, position: null, collectTime: 2025-07-29 10:23:31, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.5, voltage: 420.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.05, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:23:41.118 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:41.117000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:23:31，输入时间为2025-07-29 10:23:31，相差0，状态已过期，更新输入时间为状态时间
07-29 10:23:41.118 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:41.118000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:23:31
07-29 10:23:41.122 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:41.122000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:23:41.123 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:41.123000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:23:41.126 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:41.125000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:41.127 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:41.126000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:41.127 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:41.127000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:41.128 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:41.128000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:41.129 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:41.129000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:41.130 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:41.129000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:41.132 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:41.131000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:23:41.141 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:41.140000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:23:41.143 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:41.141000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:23:41.145 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:41.144000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:23:41.150 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:41.149000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:23:41.177 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:41.176000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:23:43.820 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:43.818000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:23:44.054 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:44.052000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333809, longitude: 109.366767, position: null, collectTime: 2025-07-29 10:23:31, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.5, voltage: 420.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.05, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:23:44.055 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:44.055000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:23:31，输入时间为2025-07-29 10:23:31，相差0，状态已过期，更新输入时间为状态时间
07-29 10:23:44.056 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:44.055000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:23:31
07-29 10:23:44.059 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:44.059000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:23:44.060 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:44.060000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:23:44.062 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:44.061000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:44.062 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:44.062000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:44.063 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:44.062000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:44.064 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:44.063000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:44.064 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:44.064000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:44.064 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:44.064000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:44.067 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:44.066000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:23:44.077 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:44.075000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:23:44.078 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:44.077000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:23:44.080 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:44.079000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:23:44.084 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:44.083000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:23:44.111 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:44.110000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:23:44.489 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:44.488000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 10:23:46.826 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:46.825000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:23:47.127 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:47.125000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333809, longitude: 109.366767, position: null, collectTime: 2025-07-29 10:23:31, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.5, voltage: 420.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.05, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:23:47.129 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:47.129000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:23:31，输入时间为2025-07-29 10:23:31，相差0，状态已过期，更新输入时间为状态时间
07-29 10:23:47.130 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:47.130000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:23:31
07-29 10:23:47.134 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:47.134000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:23:47.135 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:47.135000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:23:47.138 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:47.137000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:47.139 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:47.138000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:47.140 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:47.139000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:47.141 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:47.140000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:47.141 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:47.141000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:47.142 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:47.142000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:47.144 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:47.143000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:23:47.157 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:47.155000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:23:47.159 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:47.157000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:23:47.161 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:47.160000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:23:47.167 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:47.165000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:23:47.193 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:47.193000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:23:49.489 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:49.488000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 10:23:49.819 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:49.818000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:23:50.106 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:50.104000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.3338, longitude: 109.366762, position: null, collectTime: 2025-07-29 10:23:41, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.5, voltage: 420.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.06, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:23:50.107 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:50.107000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:23:31，输入时间为2025-07-29 10:23:41，相差10000，状态已过期，更新输入时间为状态时间
07-29 10:23:50.108 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:50.107000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:23:41
07-29 10:23:50.122 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:50.121000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:23:50.122 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:50.122000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:23:50.123 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:50.123000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:50.123 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:50.123000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:50.124 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:50.123000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:50.125 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:50.124000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:50.125 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:50.125000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:50.126 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:50.125000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:50.127 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:50.127000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:23:50.139 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:50.137000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:23:50.141 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:50.139000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:23:50.144 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:50.142000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:23:50.152 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:50.150000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:23:50.187 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:50.186000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:23:52.819 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:52.818000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:23:53.112 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:53.111000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.3338, longitude: 109.366762, position: null, collectTime: 2025-07-29 10:23:41, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.5, voltage: 420.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.8125, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.06, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:23:53.115 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:53.114000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:23:41，输入时间为2025-07-29 10:23:41，相差0，状态已过期，更新输入时间为状态时间
07-29 10:23:53.115 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:53.115000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:23:41
07-29 10:23:53.124 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:53.123000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:23:53.125 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:53.125000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:23:53.129 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:53.128000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:53.129 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:53.129000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:53.130 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:53.130000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:53.131 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:53.131000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:53.132 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:53.132000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:53.133 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:53.132000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:53.135 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:53.135000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:23:53.153 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:53.150000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:23:53.154 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:53.153000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:23:53.156 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:53.155000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:23:53.161 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:53.160000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:23:53.188 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:53.187000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:23:54.492 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:54.491000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 10:23:55.819 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:55.818000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:23:56.117 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:56.115000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333806, longitude: 109.36676, position: null, collectTime: 2025-07-29 10:23:51, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.5, voltage: 419.9, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.05, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:23:56.119 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:56.118000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:23:41，输入时间为2025-07-29 10:23:51，相差10000，状态已过期，更新输入时间为状态时间
07-29 10:23:56.120 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:56.119000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:23:51
07-29 10:23:56.134 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:56.133000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:23:56.136 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:56.135000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:23:56.139 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:56.138000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:56.140 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:56.139000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:56.141 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:56.140000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:56.143 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:56.142000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:56.144 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:56.143000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:56.145 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:56.144000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:56.148 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:56.147000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:23:56.159 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:56.157000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:23:56.161 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:56.160000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:23:56.164 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:56.162000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:23:56.170 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:56.169000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:23:56.203 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:56.203000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:23:58.820 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:58.818000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:23:59.056 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:59.055000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333806, longitude: 109.36676, position: null, collectTime: 2025-07-29 10:23:51, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.5, voltage: 419.9, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.05, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:23:59.058 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:59.058000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:23:51，输入时间为2025-07-29 10:23:51，相差0，状态已过期，更新输入时间为状态时间
07-29 10:23:59.059 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:59.059000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:23:51
07-29 10:23:59.067 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:59.066000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:23:59.067 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:59.067000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:23:59.070 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:59.069000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:59.070 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:59.070000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:59.071 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:59.071000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:59.072 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:59.071000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:59.073 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:59.072000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:23:59.073 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:59.073000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:23:59.074 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:59.074000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:23:59.076 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:59.076000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:23:59.088 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:59.086000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:23:59.090 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:59.089000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:23:59.093 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:59.092000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:23:59.100 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:59.098000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:23:59.492 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:23:59.489000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 10:24:01.821 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:01.819000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:24:02.116 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:02.115000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333806, longitude: 109.36676, position: null, collectTime: 2025-07-29 10:23:51, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.5, voltage: 419.9, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.05, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:24:02.118 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:02.118000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:23:51，输入时间为2025-07-29 10:23:51，相差0，状态已过期，更新输入时间为状态时间
07-29 10:24:02.119 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:02.118000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:23:51
07-29 10:24:02.124 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:02.123000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:24:02.125 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:02.124000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:24:02.127 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:02.126000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:02.128 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:02.127000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:02.128 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:02.128000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:24:02.130 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:02.129000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:02.130 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:02.130000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:02.131 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:02.131000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:24:02.133 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:02.132000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:24:02.134 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:02.134000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:24:02.146 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:02.144000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:24:02.149 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:02.147000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:24:02.154 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:02.151000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:24:02.159 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:02.158000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:24:04.494 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:04.493000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 10:24:04.819 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:04.818000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:24:05.019 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:05.019000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333806, longitude: 109.36676, position: null, collectTime: 2025-07-29 10:23:51, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.5, voltage: 419.9, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.05, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:24:05.022 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:05.021000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:23:51，输入时间为2025-07-29 10:23:51，相差0，状态已过期，更新输入时间为状态时间
07-29 10:24:05.022 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:05.022000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:23:51
07-29 10:24:05.029 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:05.028000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:24:05.030 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:05.029000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:24:05.032 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:05.031000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:05.033 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:05.033000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:05.034 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:05.033000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:24:05.034 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:05.034000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:05.034 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:05.034000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:05.034 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:05.034000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:24:05.035 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:05.035000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:24:05.039 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:05.038000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:24:05.048 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:05.046000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:24:05.049 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:05.048000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:24:05.052 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:05.050000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:24:05.057 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:05.056000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:24:07.820 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:07.818000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:24:08.121 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:08.120000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333809, longitude: 109.366761, position: null, collectTime: 2025-07-29 10:24:01, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.5, voltage: 420.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.04, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:24:08.123 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:08.123000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:23:51，输入时间为2025-07-29 10:24:01，相差10000，状态已过期，更新输入时间为状态时间
07-29 10:24:08.124 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:08.124000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:24:01
07-29 10:24:08.139 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:08.138000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:24:08.140 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:08.139000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:24:08.142 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:08.141000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:08.143 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:08.142000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:08.144 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:08.143000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:24:08.145 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:08.144000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:08.145 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:08.145000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:08.146 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:08.146000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:24:08.149 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:08.148000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:24:08.170 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:08.164000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:24:08.174 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:08.170000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:24:08.176 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:08.175000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:24:08.181 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:08.180000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:24:08.208 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:08.208000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:24:08.995 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:08.993000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:152:18)] MQTTManager: 接收消息参数
07-29 10:24:08.997 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:08.995000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:153:18)] MQTTManager:   主题: LK6AEAE44SB192188/prod/sgmw/vehicle/app/status
07-29 10:24:08.998 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:08.997000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:154:18)] MQTTManager:   数据长度: 156
07-29 10:24:09.000 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:08.999000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:155:18)] MQTTManager:   QoS级别: 1
07-29 10:24:09.001 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:09.000000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:156:18)] MQTTManager:   是否Base64编码: true
07-29 10:24:09.003 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:09.002000] [MQTTManager._handleMessageReceived (mqtt_manager.dart:160:18)] MQTTManager:   匹配的主题枚举: MQTTTopic.carControlAllStatus
07-29 10:24:09.005 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:09.004000] [MQTTManager._convertCarStatusToMap (mqtt_manager.dart:698:18)] MQTTManager:将SgmwAppCarStatus protobuf对象转换为Map ,carStatus.collectTime:1753755320262 , carStatus.collectTime.toInt():1753755320262
07-29 10:24:09.007 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:09.006000] [MQTTManager._notifyListeners (mqtt_manager.dart:492:18)] MQTTManager: 通知监听器 - 主题: MQTTTopic.carControlAllStatus, 监听器数量: 1
07-29 10:24:09.009 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:09.008000] [UseCarPageState._onCarControlAllStatusReceived (use_car_page.dart:735:18)] [MQTT]===收到车辆控制全状态MQTT消息,data: {collectTime: 1753755320262, acStatus: 0, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 0, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753755849006}
07-29 10:24:09.011 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:09.010000] [UseCarPageState._handleCarControlAllStatusFromMap (use_car_page.dart:822:18)] [MQTT]===处理从protobuf转换而来的Map格式数据，_handleCarControlAllStatusFromMap: {collectTime: 1753755320262, acStatus: 0, doorLockStatus: 0, windowStatus: 0, engineStatus: , tailDoorLockStatus: 0, lowBeamLight: 0, dipHeadLight: 0, sentinelModeStatus: , tailDoorOpenStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, doorOpenStatus: 0, door1OpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, window1Status: 0, window2Status: 0, window3Status: 0, window4Status: 0, topWindowStatus: , autoGearStatus: , manualGearStatus: , keyStatus: 2, acTemperatureGear: , acWindGear: 0, leftBatteryPower: , leftFuel: , mileage: , leftMileage: , batterySoc: , current: , voltage: , batAvgTemp: , batMaxTemp: , batMinTemp: , tmActTemp: , invActTemp: , accActPos: , brakPedalPos: , strWhAng: , vehSpdAvgDrvn: , obcOtpCur: , vecChrgingSts: , vecChrgStsIndOn: , obcTemp: , batSOH: , lowBatVol: , leftTurnLight: , rightTurnLight: , positionLight: , frontFogLight: , rearFogLight: , latitude: , longitude: , position: , charging: , wireConnect: , rechargeStatus: , seat1WindStatus: 7, seat2WindStatus: 7, seat3WindStatus: 0, seat4WindStatus: 0, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, timestamp: 1753755849006}
07-29 10:24:09.012 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:09.011000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:866:24)] [MQTT]===时间戳1753755320262转换为2025-07-29 10:15:20
07-29 10:24:09.014 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:09.013000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:43:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:24:01，输入时间为2025-07-29 10:15:20，相差-521000，状态未过期
07-29 10:24:09.016 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:09.015000] [UseCarPageState._refreshCarControlUI (use_car_page.dart:1020:18)] 车辆控制状态已更新，刷新UI
07-29 10:24:09.017 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:09.016000] [UseCarPageState._updateCarStatusFromMap (use_car_page.dart:875:22)] [MQTT]===是否需要更新车辆数据: isNeedUpdate:false
07-29 10:24:09.490 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:09.488000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 10:24:10.821 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:10.820000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:24:11.427 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:11.426000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333809, longitude: 109.366761, position: null, collectTime: 2025-07-29 10:24:01, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.5, voltage: 420.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.04, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:24:11.429 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:11.429000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:24:01，输入时间为2025-07-29 10:24:01，相差0，状态已过期，更新输入时间为状态时间
07-29 10:24:11.430 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:11.430000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:24:01
07-29 10:24:11.438 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:11.437000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:24:11.439 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:11.439000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:24:11.442 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:11.441000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:11.443 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:11.442000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:11.443 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:11.443000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:24:11.444 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:11.444000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:11.445 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:11.444000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:11.446 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:11.445000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:24:11.447 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:11.446000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:24:11.451 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:11.450000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:24:11.462 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:11.460000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:24:11.463 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:11.462000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:24:11.466 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:11.464000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:24:11.472 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:11.471000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:24:13.822 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:13.821000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:24:14.122 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:14.121000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333809, longitude: 109.366761, position: null, collectTime: 2025-07-29 10:24:01, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.5, voltage: 420.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.04, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:24:14.125 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:14.124000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:24:01，输入时间为2025-07-29 10:24:01，相差0，状态已过期，更新输入时间为状态时间
07-29 10:24:14.126 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:14.125000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:24:01
07-29 10:24:14.133 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:14.132000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:24:14.134 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:14.134000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:24:14.137 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:14.136000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:14.138 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:14.138000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:14.139 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:14.138000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:24:14.140 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:14.139000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:14.141 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:14.140000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:14.142 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:14.141000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:24:14.144 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:14.143000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:24:14.157 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:14.155000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:24:14.158 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:14.157000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:24:14.160 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:14.159000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:24:14.165 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:14.163000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:24:14.189 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:14.189000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:24:14.499 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:14.498000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 10:24:16.820 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:16.818000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:24:17.124 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:17.123000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333809, longitude: 109.366761, position: null, collectTime: 2025-07-29 10:24:01, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.5, voltage: 420.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 34, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.04, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:24:17.126 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:17.125000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:24:01，输入时间为2025-07-29 10:24:01，相差0，状态已过期，更新输入时间为状态时间
07-29 10:24:17.126 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:17.126000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:24:01
07-29 10:24:17.132 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:17.131000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:24:17.133 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:17.132000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:24:17.135 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:17.134000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:17.135 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:17.135000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:17.136 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:17.135000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:24:17.137 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:17.136000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:17.137 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:17.137000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:17.138 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:17.138000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:24:17.139 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:17.139000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:24:17.145 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:17.144000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:24:17.159 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:17.156000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:24:17.161 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:17.159000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:24:17.165 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:17.163000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:24:17.174 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:17.171000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:24:19.521 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:19.520000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 10:24:19.824 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:19.823000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:24:20.113 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:20.112000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333804, longitude: 109.36676, position: null, collectTime: 2025-07-29 10:24:11, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.5, voltage: 420.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 35, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.06, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:24:20.118 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:20.117000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:24:01，输入时间为2025-07-29 10:24:11，相差10000，状态已过期，更新输入时间为状态时间
07-29 10:24:20.119 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:20.118000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:24:11
07-29 10:24:20.133 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:20.132000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:24:20.134 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:20.133000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:24:20.136 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:20.135000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:20.137 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:20.137000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:20.138 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:20.137000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:24:20.139 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:20.138000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:20.139 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:20.139000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:20.140 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:20.139000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:24:20.142 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:20.141000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:24:20.157 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:20.155000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:24:20.159 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:20.158000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:24:20.161 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:20.160000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:24:20.167 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:20.165000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:24:20.196 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:20.196000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:24:22.820 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:22.818000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:24:23.098 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:23.097000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333804, longitude: 109.36676, position: null, collectTime: 2025-07-29 10:24:11, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.5, voltage: 420.4, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 35, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.06, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:24:23.101 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:23.100000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:24:11，输入时间为2025-07-29 10:24:11，相差0，状态已过期，更新输入时间为状态时间
07-29 10:24:23.101 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:23.101000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:24:11
07-29 10:24:23.107 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:23.106000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:24:23.109 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:23.107000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:24:23.112 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:23.111000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:23.114 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:23.113000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:23.114 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:23.114000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:24:23.115 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:23.115000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:23.116 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:23.116000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:23.117 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:23.116000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:24:23.119 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:23.118000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:24:23.136 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:23.134000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:24:23.137 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:23.136000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:24:23.140 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:23.138000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:24:23.145 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:23.144000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:24:23.171 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:23.171000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:24:24.491 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:24.490000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
07-29 10:24:25.820 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:25.818000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:24:26.022 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:26.020000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333803, longitude: 109.366762, position: null, collectTime: 2025-07-29 10:24:21, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.5, voltage: 420.2, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 35, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.06, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:24:26.025 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:26.024000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:24:11，输入时间为2025-07-29 10:24:21，相差10000，状态已过期，更新输入时间为状态时间
07-29 10:24:26.025 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:26.025000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:24:21
07-29 10:24:26.041 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:26.040000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:24:26.042 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:26.041000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:24:26.044 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:26.043000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:26.044 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:26.044000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:26.045 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:26.045000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:24:26.047 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:26.046000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:26.047 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:26.047000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:26.048 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:26.048000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:24:26.050 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:26.050000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:24:26.067 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:26.064000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:24:26.070 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:26.068000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:24:26.072 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:26.071000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:24:26.078 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:26.077000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:24:26.105 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:26.105000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:24:28.828 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:28.827000] [UseCarPageState.autoRefresh (UseCarPageState.autoRefresh (package:wuling_flutter_app/page/use_car_page/use_car_page.dart:2243)] 开始刷新车况，isLogin：true
07-29 10:24:29.100 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:29.099000] [UseCarPageState.fetchDefaultCarStatus (use_car_page.dart:2233:11)] 自动刷新接口请求成功，carStatus：{carStatus: {vin: null, status: 0, statusToast: , statusName: , banRecharge: null, batHealth: 100, outCurrent: null, charging: 0, cdjTemp: , wireConnect: , controlStatus: null, headerOpenStatus: null, limitFeedback: -1, batteryStatus: 0, drStHtStatus: null, otTemp: null, accCntTemp: 17.0, clstrRange: null, rfMvmntPstnSts: null, yesterMileage: 0, leftMileage: 217, acStatus: 0, acPwrStatus: null, leftFuel: 0, rechargeStatus: , avgFuel: 0.0, pressure1Status: null, pressure2Status: null, pressure3Status: null, pressure4Status: null, CarSearch: null, tankerTopWindowStatus: null, engineSoonStatus: null, engineOilStatus: null, transStatus: null, epbSysStatus: null, doorLockStatus: 0, door1LockStatus: 0, door2LockStatus: 0, door3LockStatus: 0, door4LockStatus: 0, door1OpenStatus: 0, doorOpenStatus: 0, door2OpenStatus: 0, door3OpenStatus: 0, door4OpenStatus: 0, autoGearStatus: 10, acTemperatureGear: null, acWindGear: null, mileage: 110, latitude: 24.333803, longitude: 109.366762, position: null, collectTime: 2025-07-29 10:24:21, tailDoorOpenStatus: 0, tailDoorLockStatus: 0, windowStatus: 0, window1Status: 0, window1OpenDegree: 0, window2Status: 0, window2OpenDegree: 0, window3Status: 0, window3OpenDegree: 0, window4Status: 0, window4OpenDegree: 0, topWindowStatus: null, keyStatus: 2, leftBatteryPower: 24.2, batterySoc: 36, current: 0.5, voltage: 420.2, batAvgTemp: 31, batMaxTemp: 31, batMinTemp: 31, tmActTemp: null, invActTemp: 35, accActPos: 0, brakPedalPos: 0, strWhAng: 1.75, vehSpdAvgDrvn: , obcOtpCur: 0, vecChrgingSts: 0, vecChrgStsIndOn: 0, obcTemp: null, batSOH: 100, lowBatVol: 14.06, sentinelModeStatus: 0, lowBeamLight: 0, dipHeadLight: 0, leftTurnLight: 0, rightTurnLight: 0, positionLight: 0, frontFogLight: null, rearFogLight: null, manualGearStatus: null, engineStatus: null, interiorTemperature: 43, avgElectronFuel: null, oilLeftMileage: 0, pressureStatus: null, leftChargeTime: null, batteryIndicate: , hybridMileage: , chargePower: , parkingAssSwitch: null, tailDoorStatus: null, seat1HotStatus: 7, seat2HotStatus: 7, seat3HotStatus: 7, seat4HotStatus: 7, seat1WindStatus: 3, seat2WindStatus: 7, seat3WindStatus: , seat4WindStatus: }, carInfo: {vin: LK6AEAE44SB192188, relation: 2, carName: 五菱星光, carPlate: , vsn: T149, providerCode: desai, carTypeName: 五菱星光, model: 智能旗舰型, level: LV2, engineType: 1, image: https://cdn-df.00bang.cn/images/T1aSCTBsJv1RCvBVdK.png, controlView: 3, bleType: 2, hasMoreCar: 1, folderUrl: , shakeLock: 1, physicsEngine: 1, supportMqtt: 1, supportCarConditionPoll: 1, conditionPollTime: 3, isAuthIdentity: 0, bluetoothKeyConnectMark: 1, imageNameRule: [], telematicsPlatform: 0, showWidgets: true, telematicsCarStatus: 1, carPosition: 1, carYear: 2025, colorCode: LX, carInfoId: 819801, bindCarUserMobile: 17376444796, onlyLocalInfo: false, colorName: 星耀黑, supportBatteryIndicate: 0, supportChargeRemain: 1, supportChargePower: 0, supportAvgFuel: 0, supportHybridMileage: 0, supportAvgElectronFuel: 0, mqttPush: null, licenseStatus: 1, appCode: null, purchaseDate: null, purchaseShopNum: null}}
07-29 10:24:29.103 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:29.102000] [CarStatusChecker.isCarStatusOutOfDateWithTimeStr (car_status_checker.dart:48:13)] [TIME_TEST]===LK6AEAE44SB192188保存时间为2025-07-29 10:24:21，输入时间为2025-07-29 10:24:21，相差0，状态已过期，更新输入时间为状态时间
07-29 10:24:29.104 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:29.103000] [CarStatusChecker.saveCarStatusTimeWithTimeStr (car_status_checker.dart:58:18)] [TIME_TEST]===保存LK6AEAE44SB192188的新状态时间为2025-07-29 10:24:21
07-29 10:24:29.114 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:29.113000] [use_car_page.dart:2109:30] [更新车况]===轮询车辆状态成功，_currentServiceCode:acStatus,statusMap[_currentServiceCode]:0 ,_MQTTBackStatus:0 , _currentACStatus:0,_statusModel?.acStatus:0 ,_statusModel?.accCntTemp:17.0  ,IS_MQTT_CAR_CONTROL:false , IS_AC_HOT_WIND_SETTING:false
07-29 10:24:29.115 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:29.114000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4526:20)] _cacheCarStatus - userIdStr: MTcwMDc0ODg=, vin: LK6AEAE44SB192188
07-29 10:24:29.117 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:29.117000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:29.118 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:29.118000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:29.119 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:29.119000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:24:29.120 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:29.120000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:29.121 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:29.121000] [UseCarPageState.getSeatVentilationIsOpen (use_car_page.dart:3372:20)] 获取座椅通风状态：seat1WindStatus = 3 ，seat2WindStatus = 7 ，seat3WindStatus = 7 ，seat4WindStatus = 7
07-29 10:24:29.122 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:29.121000] [UseCarPageState.getSeatHeatingIsOpen (use_car_page.dart:3432:20)] 获取座椅加热状态：seat1HotStatus = 7 ，seat2HotStatus = 7 ，seat3HotStatus = 7 ，seat4HotStatus = 7
07-29 10:24:29.125 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:29.124000] [UseCarPageState.getWidgetList (use_car_page.dart:3692:12)] DEBUG:---- controlView: 3
07-29 10:24:29.138 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:29.137000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---ChargingTime--: false
07-29 10:24:29.140 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:29.139000] [_ControlTopCarInfoEVWidgetState.getShowNotificationModel (use_car_page_control_title_widget.dart:995:20)] 参数---repairRemind--: false
07-29 10:24:29.142 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:29.141000] [_ControlTopCarInfoEVWidgetState._isNewAir (use_car_page_control_title_widget.dart:1317:32)] isNewAir() 当前是新空调
07-29 10:24:29.148 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:29.146000] [_MyCarPageState.getACBackgroundImage (car_control_page.dart:968:20)] 当前空调状态：  0
07-29 10:24:29.172 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:29.171000] [UseCarPageState._cacheCarStatus (use_car_page.dart:4534:22)] 车辆状态缓存成功，key: travel_car_status_MTcwMDc0ODg=_LK6AEAE44SB192188, 电量: 36%
07-29 10:24:29.494 23415 23602 W A00000/com.sgmw.wuling/XComFlutterOHOS_Native: flutter settings log message: [2025-07-29 10:24:29.493000] [mqtt_manager.dart:649:20] MQTTManager: 连接状态检查 - 已连接
