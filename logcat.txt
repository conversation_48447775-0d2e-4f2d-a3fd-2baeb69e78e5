te_common-2.5.4%2B2.tar.gz","archive_sha256":"7b41b6c3507854a159e24ae90a8e3e9cc01eb26a477c118d6dca065b5f55453e","published":"2024-08-17T10:59:11.397259Z"},{"version":"2.5.4+3","pubspec
":{"name":"sqflite_common","homepage":"https://github.com/tekartik/sqflite/tree/master/sqflite_common","description":"Dart wrapper on SQLite, a self-contained, high-reliability, embedd
ed, SQL database engine.","version":"2.5.4+3","funding":["https://github.com/sponsors/alextekartik"],"environment":{"sdk":"^3.5.0"},"dependencies":{"synchronized":">=3.0.0 <5.0.0","pat
h":">=1.8.0 <3.0.0","meta":">=1.14.0 <3.0.0"},"dev_dependencies":{"lints":">=4.0.0","process_run":">=1.1.0","http":">=1.2.2","test":">=1.25.8","test_api":">=0.7.3","pub_semver":">=2.1.
4"}},"archive_url":"https://pub.flutter-io.cn/api/archives/sqflite_common-2.5.4%2B3.tar.gz","archive_sha256":"4058172e418eb7e7f2058dcb7657d451a8fc264afa0dea4dbd0f304a57131611","publish
ed":"2024-09-16T08:33:13.100045Z"},{"version":"2.5.4+4","pubspec":{"name":"sqflite_common","homepage":"https://github.com/tekartik/sqflite/tree/master/sqflite_common","description":"Da
rt wrapper on SQLite, a self-contained, high-reliability, embedded, SQL database engine.","version":"2.5.4+4","funding":["https://github.com/sponsors/alextekartik"],"environment":{"sdk
":"^3.5.0"},"dependencies":{"synchronized":">=3.0.0 <5.0.0","path":">=1.8.0 <3.0.0","meta":">=1.14.0 <3.0.0"},"dev_dependencies":{"lints":">=4.0.0","process_run":">=1.1.0","http":">=1.
2.2","test":">=1.25.8","test_api":">=0.7.3","pub_semver":">=2.1.4"}},"archive_url":"https://pub.flutter-io.cn/api/archives/sqflite_common-2.5.4%2B4.tar.gz","archive_sha256":"2d8e607db7
2e9cb7748c9c6e739e2c9618320a5517de693d5a24609c4671b1a4","published":"2024-09-27T10:34:28.111226Z"},{"version":"2.5.4+5","pubspec":{"name":"sqflite_common","homepage":"https://github.co
m/tekartik/sqflite/tree/master/sqflite_common","description":"Dart wrapper on SQLite, a self-contained, high-reliability, embedded, SQL database engine.","version":"2.5.4+5","funding":
["https://github.com/sponsors/alextekartik"],"environment":{"sdk":"^3.5.0"},"dependencies":{"synchronized":">=3.0.0 <5.0.0","path":">=1.8.0 <3.0.0","meta":">=1.14.0 <3.0.0"},"dev_depen
dencies":{"lints":">=4.0.0","process_run":">=1.1.0","http":">=1.2.2","test":">=1.25.8","test_api":">=0.7.3","pub_semver":">=2.1.4"}},"archive_url":"https://pub.flutter-io.cn/api/archiv
es/sqflite_common-2.5.4%2B5.tar.gz","archive_sha256":"4468b24876d673418a7b7147e5a08a715b4998a7ae69227acafaab762e0e5490","published":"2024-10-07T19:42:50.963933Z"},{"version":"2.5.4+6",
"pubspec":{"name":"sqflite_common","homepage":"https://github.com/tekartik/sqflite/tree/master/sqflite_common","description":"Dart wrapper on SQLite, a self-contained, high-reliability
, embedded, SQL database engine.","version":"2.5.4+6","funding":["https://github.com/sponsors/alextekartik"],"environment":{"sdk":"^3.5.0"},"dependencies":{"synchronized":">=3.0.0 <5.0
.0","path":">=1.8.0 <3.0.0","meta":">=1.14.0 <3.0.0"},"dev_dependencies":{"lints":">=4.0.0","process_run":">=1.1.0","http":">=1.2.2","test":">=1.25.8","test_api":">=0.7.3","pub_semver"
:">=2.1.4"}},"archive_url":"https://pub.flutter-io.cn/api/archives/sqflite_common-2.5.4%2B6.tar.gz","archive_sha256":"761b9740ecbd4d3e66b8916d784e581861fd3c3553eda85e167bc49fdb68f709",
"published":"2024-11-21T11:24:55.913420Z"},{"version":"2.5.5","pubspec":{"name":"sqflite_common","homepage":"https://github.com/tekartik/sqflite/tree/master/sqflite_common","descriptio
n":"Dart wrapper on SQLite, a self-contained, high-reliability, embedded, SQL database engine.","version":"2.5.5","funding":["https://github.com/sponsors/alextekartik"],"environment":{
"sdk":"^3.7.0"},"dependencies":{"synchronized":">=3.0.0 <5.0.0","path":">=1.8.0 <3.0.0","meta":">=1.14.0 <3.0.0"},"dev_dependencies":{"lints":">=4.0.0","process_run":">=1.1.0","http":"
>=1.2.2","test":">=1.25.8","test_api":">=0.7.3","pub_semver":">=2.1.4"}},"archive_url":"https://pub.flutter-io.cn/api/archives/sqflite_common-2.5.5.tar.gz","archive_sha256":"84731e8bfd
8303a3389903e01fb2141b6e59b5973cacbb0929021df08dddbe8b","published":"2025-02-13T08:54:41.493322Z"}],"_fetchedAt":"2025-07-15T16:34:44.859838"}
IO  : HTTP response 200 OK for GET https://pub.flutter-io.cn/api/packages/synchronized
    | took 0:00:00.127205
    | transfer-encoding: chunked
    | date: Thu, 26 Jun 2025 20:38:16 GMT
    | content-encoding: gzip
    | vary: Accept-Encoding,Accept-Encoding
    | x-goog-stored-content-length: 4021
    | server-loc: KIWI-HK03
    | ali-swift-global-savetime: 1750970296
    | x-guploader-uploadid: ABgVH8-pCHMIhut_xbpQO_qV8hWUgNn49paviYY_yIeUjcW-sLhJVbjdvDgku66t3uROgOE91U_rOYY
    | x-swift-cachetime: 6429687
    | server: Tengine
    | x-goog-meta-validated: 2025-06-26T20:11:34.638554
    | x-swift-savetime: Sat, 12 Jul 2025 10:36:49 GMT
    | server-proj: CHART
    | etag: W/"d7c73393185b51e52740bf962bbc7b4c"
    | x-goog-metageneration: 1
    | eagleid: 78e9b69917525684856213603e
    | x-goog-storage-class: STANDARD
    | connection: keep-alive
    | access-control-allow-origin: https://pub-web.flutter-io.cn
    | strict-transport-security: max-age=5184000
    | age: 1598189
    | x-goog-hash: crc32c=fTN2Kg==,md5=18czkxhbUeUnQL+WK7x7TA==
    | content-type: application/json; charset="utf-8"
    | x-goog-generation: 1750968694710358
    | timing-allow-origin: *
    | alt-svc: h3=":443"; ma=2592000,h3-29=":443"; ma=2592000
    | x-guploader-response-body-transformations: gunzipped
    | x-goog-stored-content-encoding: gzip
    | via: 1.1 google, cache5.l2cn1852[0,0,200-0,H], cache54.l2cn1852[3,0], cache9.cn4368[0,4,200-0,H], cache5.cn4368[44,0]
    | warning: 214 UploadServer gunzipped
    | x-cache: HIT TCP_HIT dirn:3:334785532
IO  : Finished git. Exit code 0.
    | stdout:
    | | true
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-list --max-count=1 770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc" in D:/PUB\git\cache\flutter_packages-0a4d7447ca89e3e387a011f31be855a7af59a651
IO  : Writing 33662 characters to text file D:/PUB\hosted\pub.flutter-io.cn\.cache\synchronized-versions.json.
FINE: Contents:
    | {"name":"synchronized","latest":{"version":"3.4.0","pubspec":{"name":"synchronized","description":"Lock mechanism to prevent concurrent access to asynchronous code.","version":"3
.4.0","homepage":"https://github.com/tekartik/synchronized.dart/tree/master/synchronized","funding":["https://github.com/sponsors/alextekartik"],"topics":["mutex","lock","async","concu
rrency"],"environment":{"sdk":"^3.8.0"},"dev_dependencies":{"web":">=0.5.0","lints":">=1.0.1","pub_semver":">=2.0.0","test":">=1.16.2","build_runner":">=1.11.2","build_test":">=1.3.7",
"build_web_compilers":">=2.16.4","process_run":">=0.12.0-0","yaml":">=3.0.0"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-3.4.0.tar.gz","archive_sha256":"c254ad
e258ec8282947a0acbbc90b9575b4f19673533ee46f2f6e9b3aeefd7c0","published":"2025-06-26T20:11:33.815765Z"},"versions":[{"version":"0.1.0","pubspec":{"version":"0.1.0","name":"synchronized"
,"dependencies":null,"author":"Tekartik <<EMAIL>>","description":"Basic lock mechanism to prevent concurrent access to asynchronous code","homepage":"https://github.com/tekart
ik/synchronized.dart","environment":{"sdk":">=1.9.1 <2.0.0"},"dev_dependencies":{"test":">=0.12.0","chrome_travis":{"git":"git://github.com/tekartik/chrome_travis.dart"},"dev_test":">=
0.9.3"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-0.1.0.tar.gz","archive_sha256":"f79a22014b4aa4d9d60ae6cbfdd11d15a3df119af27a6dcad89e7b4b76177870","published
":"2016-10-14T20:20:07.695Z"},{"version":"1.0.0","pubspec":{"version":"1.0.0","name":"synchronized","dependencies":null,"author":"Tekartik <<EMAIL>>","description":"Basic lock
 mechanism to prevent concurrent access to asynchronous code","homepage":"https://github.com/tekartik/synchronized.dart","environment":{"sdk":">=1.9.1 <2.0.0"},"dev_dependencies":{"tes
t":">=0.12.0","chrome_travis":{"git":"git://github.com/tekartik/chrome_travis.dart"},"dev_test":">=0.9.3"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-1.0.0.tar
.gz","archive_sha256":"f013d5d6e86a9434abe7ce6f9d0a7215e3d0495ab4f56aeb7675d5cc4c99294b","published":"2017-02-12T11:41:22.222Z"},{"version":"1.0.1","pubspec":{"version":"1.0.1","name":
"synchronized","dependencies":null,"author":"Tekartik <<EMAIL>>","description":"Basic lock mechanism to prevent concurrent access to asynchronous code","web":{"compiler":{"deb
ug":"dartdevc"}},"homepage":"https://github.com/tekartik/synchronized.dart","environment":{"sdk":">=1.24.0 <2.0.0"},"dev_dependencies":{"test":">=0.12.0","chrome_travis":{"git":"git://
github.com/tekartik/chrome_travis.dart"},"browser":"any","dev_test":">=0.9.3"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-1.0.1.tar.gz","archive_sha256":"42281
ebda5479fc61953451fb75c45773b8fe64944e688996483f6b0f15ae094","published":"2017-10-15T15:37:07.520713Z"},{"version":"1.0.2","pubspec":{"version":"1.0.2","name":"synchronized","dependenc
ies":null,"author":"Tekartik <<EMAIL>>","description":"Basic lock mechanism to prevent concurrent access to asynchronous code","web":{"compiler":{"debug":"dartdevc"}},"homepag
e":"https://github.com/tekartik/synchronized.dart","environment":{"sdk":">=1.24.0 <2.0.0"},"dev_dependencies":{"test":">=0.12.0","chrome_travis":{"git":"git://github.com/tekartik/chrom
e_travis.dart"},"browser":"any","dev_test":">=0.9.3"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-1.0.2.tar.gz","archive_sha256":"54d62393c4da1115f550695b8f469a
7f6849d9d9879f18a8fc31868a22dc5a7e","published":"2017-10-15T16:02:11.121989Z"},{"version":"1.1.0","pubspec":{"version":"1.1.0","name":"synchronized","dependencies":null,"author":"Tekar
tik <<EMAIL>>","description":"Basic lock mechanism to prevent concurrent access to asynchronous code","web":{"compiler":{"debug":"dartdevc"}},"homepage":"https://github.com/te
kartik/synchronized.dart","environment":{"sdk":">=1.24.0 <2.0.0"},"dev_dependencies":{"test":">=0.12.0","chrome_travis":{"git":"git://github.com/tekartik/chrome_travis.dart"},"browser"
:"any","dev_test":">=0.9.3"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-1.1.0.tar.gz","archive_sha256":"8f0dab5e6f55f758e94867b485a270a4399e976f66ff23651794d6c
26460d5e7","published":"2017-10-15T16:47:46.336819Z"},{"version":"1.2.0","pubspec":{"version":"1.2.0","name":"synchronized","author":"Tekartik <<EMAIL>>","description":"Basic
lock mechanism to prevent concurrent access to asynchronous code","web":{"compiler":{"debug":"dartdevc"}},"homepage":"https://github.com/tekartik/synchronized.dart","environment":{"sdk
":">=1.24.0 <2.0.0"},"dev_dependencies":{"test":">=0.12.0","chrome_travis":{"git":"git://github.com/tekartik/chrome_travis.dart"},"browser":"any","dev_test":">=0.9.3"}},"archive_url":"
https://pub.flutter-io.cn/api/archives/synchronized-1.2.0.tar.gz","archive_sha256":"aafb9d0df48323f9f9fa18ce6e1c4cf0e6a6b8934f5096b5970999cabe0c0e70","published":"2018-01-26T09:51:19.2
51237Z"},{"version":"1.2.1","pubspec":{"version":"1.2.1","name":"synchronized","author":"Tekartik <<EMAIL>>","description":"Basic lock mechanism to prevent concurrent access t
o asynchronous code","web":{"compiler":{"debug":"dartdevc"}},"homepage":"https://github.com/tekartik/synchronized.dart","environment":{"sdk":">=1.24.0 <2.0.0"},"dev_dependencies":{"tes
t":">=0.12.0","chrome_travis":{"git":"git://github.com/tekartik/chrome_travis.dart"},"browser":"any","dev_test":">=0.9.3"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchr
onized-1.2.1.tar.gz","archive_sha256":"7c7e786f04206b638941afbfd198af30f4bae0d7290a9fbb166b501f8564f4ea","published":"2018-03-01T00:19:23.142548Z"},{"version":"1.3.0","pubspec":{"versi
on":"1.3.0","name":"synchronized","author":"Tekartik <<EMAIL>>","description":"Basic lock mechanism to prevent concurrent access to asynchronous code","web":{"compiler":{"debu
g":"dartdevc"}},"homepage":"https://github.com/tekartik/synchronized.dart","environment":{"sdk":">=1.24.0 <2.0.0"},"dev_dependencies":{"test":">=0.12.0","chrome_travis":{"git":"git://g
ithub.com/tekartik/chrome_travis.dart"},"browser":"any","dev_test":">=0.9.3"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-1.3.0.tar.gz","archive_sha256":"ee06e0
07aa3daf508512308cf5b9a0314a4db50590aadfd07369e5a761f75d1b","published":"2018-03-04T12:33:36.707931Z"},{"version":"1.4.0","pubspec":{"version":"1.4.0","name":"synchronized","author":"T
ekartik <<EMAIL>>","description":"Lock mechanism to prevent concurrent access to asynchronous code","web":{"compiler":{"debug":"dartdevc"}},"homepage":"https://github.com/teka
rtik/synchronized.dart","environment":{"sdk":">=1.24.0 <2.0.0"},"dev_dependencies":{"test":">=0.12.0","chrome_travis":{"git":"git://github.com/tekartik/chrome_travis.dart"},"browser":"
any","dev_test":">=0.9.3"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-1.4.0.tar.gz","archive_sha256":"e2c417672152c514bdfa9f9108955b9aa69ea63dcac7abd84e74046a8
6d209f8","published":"2018-04-05T08:52:31.587133Z"},{"version":"1.5.0-dev.2","pubspec":{"version":"1.5.0-dev.2","name":"synchronized","author":"Tekartik <<EMAIL>>","descriptio
n":"Lock mechanism to prevent concurrent access to asynchronous code","homepage":"https://github.com/tekartik/synchronized.dart","environment":{"sdk":">=1.24.3 <2.0.0"},"dev_dependenci
es":{"test":">=0.12.0","chrome_travis":{"git":"git://github.com/tekartik/chrome_travis.dart"},"browser":"any","dev_test":">=0.9.3"}},"archive_url":"https://pub.flutter-io.cn/api/archiv
es/synchronized-1.5.0-dev.2.tar.gz","archive_sha256":"2ee0b4ba997bbac45281bf827d120c178b113befb3285f6ca7427c46933327ae","published":"2018-06-04T15:48:12.621630Z"},{"version":"1.5.0","p
ubspec":{"version":"1.5.0","name":"synchronized","author":"Tekartik <<EMAIL>>","description":"Lock mechanism to prevent concurrent access to asynchronous code","homepage":"htt
ps://github.com/tekartik/synchronized.dart","environment":{"sdk":">=1.24.3 <2.0.0"},"dev_dependencies":{"test":">=0.12.0","chrome_travis":{"git":"git://github.com/tekartik/chrome_travi
s.dart"},"browser":"any","dev_test":">=0.9.3"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-1.5.0.tar.gz","archive_sha256":"6dd37fd18c3f1ce8b8dbebc9e9da45244d4ad
056420c8a3e52f9032c60747caa","published":"2018-06-12T10:48:51.345953Z"},{"version":"1.5.0+1","pubspec":{"version":"1.5.0+1","name":"synchronized","author":"Tekartik <<EMAIL>>"
,"description":"Lock mechanism to prevent concurrent access to asynchronous code","homepage":"https://github.com/tekartik/synchronized.dart","environment":{"sdk":">=1.24.3 <2.0.0"},"de
v_dependencies":{"test":">=0.12.0","chrome_travis":{"git":"git://github.com/tekartik/chrome_travis.dart"},"browser":"any","dev_test":">=0.9.3"}},"archive_url":"https://pub.flutter-io.c
n/api/archives/synchronized-1.5.0%2B1.tar.gz","archive_sha256":"dbed62e38c9abd793fe99a4a97329504e987f6d0cf81fec6fcc401a3b940cd0b","published":"2018-06-12T10:51:07.541947Z"},{"version":
"1.5.1","pubspec":{"version":"1.5.1","name":"synchronized","author":"Tekartik <<EMAIL>>","description":"Lock mechanism to prevent concurrent access to asynchronous code","home
page":"https://github.com/tekartik/synchronized.dart","environment":{"sdk":">=2.0.0-dev.58 <3.0.0"},"dev_dependencies":{"build_runner":">=0.9.2","test":">=0.12.0","chrome_travis":{"git
":{"ref":"dart2","url":"git://github.com/tekartik/chrome_travis.dart"}},"browser":"any","dev_test":">=0.9.3","build_test":">=0.10.3"}},"archive_url":"https://pub.flutter-io.cn/api/arch
ives/synchronized-1.5.1.tar.gz","archive_sha256":"1383963824bf16ec168fe63794a97135d1c32587261a9a7c3a8fa482511ef0c4","published":"2018-07-24T08:37:52.813948Z"},{"version":"1.5.1+1","pub
spec":{"version":"1.5.1+1","name":"synchronized","author":"Tekartik <<EMAIL>>","description":"Lock mechanism to prevent concurrent access to asynchronous code","homepage":"htt
ps://github.com/tekartik/synchronized.dart","environment":{"sdk":">=2.0.0-dev.58 <3.0.0"},"dev_dependencies":{"build_runner":">=0.9.2","test":">=0.12.0","chrome_travis":{"git":{"ref":"
dart2","url":"git://github.com/tekartik/chrome_travis.dart"}},"dev_test":">=0.12.0","build_test":">=0.10.3"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-1.5.1%2
B1.tar.gz","archive_sha256":"53f3615993573e0791b01cdcbc36f5566a6fae42d752d3229d938eba6fb64352","published":"2018-08-09T16:01:01.252787Z"},{"version":"1.5.2","pubspec":{"version":"1.5.2
","name":"synchronized","author":"Tekartik <<EMAIL>>","description":"Lock mechanism to prevent concurrent access to asynchronous code","homepage":"https://github.com/tekartik/
synchronized.dart","environment":{"sdk":">=2.0.0-dev.58 <3.0.0"},"dev_dependencies":{"build_runner":">=0.9.2","test":">=0.12.0","chrome_travis":{"git":{"ref":"dart2","url":"git://githu
b.com/tekartik/chrome_travis.dart"}},"build_test":">=0.10.3"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-1.5.2.tar.gz","archive_sha256":"8ab2fab7884146534e305d
9ad05e8f6a87a026270d5857192156424e11b18eac","published":"2018-09-13T18:53:17.440826Z"},{"version":"1.5.3","pubspec":{"version":"1.5.3","name":"synchronized","author":"Tekartik <dev@tek
artik.com>","description":"Lock mechanism to prevent concurrent access to asynchronous code","homepage":"https://github.com/tekartik/synchronized.dart","environment":{"sdk":">=2.0.0 <3
.0.0"},"dev_dependencies":{"build_runner":">=0.9.2","test":">=0.12.0","chrome_travis":{"git":{"ref":"dart2","url":"git://github.com/tekartik/chrome_travis.dart"}},"build_test":">=0.10.
3"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-1.5.3.tar.gz","archive_sha256":"c652be258cd86124895979ce319b174492f036ce750d948c2afd6112acd0aa1d","published":"2
018-09-20T16:42:56.817973Z"},{"version":"1.5.3+1","pubspec":{"version":"1.5.3+1","name":"synchronized","author":"Tekartik <<EMAIL>>","description":"Lock mechanism to prevent c
oncurrent access to asynchronous code","homepage":"https://github.com/tekartik/synchronized.dart","environment":{"sdk":">=2.0.0 <3.0.0"},"dev_dependencies":{"build_runner":">=0.9.2","t
est":">=0.12.0","chrome_travis":{"git":{"ref":"dart2","url":"git://github.com/tekartik/chrome_travis.dart"}},"build_test":">=0.10.3"}},"archive_url":"https://pub.flutter-io.cn/api/arch
ives/synchronized-1.5.3%2B1.tar.gz","archive_sha256":"13fffd900f9586cbd7b323d4c513ebb880585ffe36ece671111d1767f8658630","published":"2018-12-22T12:08:32.675716Z"},{"version":"1.5.3+2",
"pubspec":{"version":"1.5.3+2","name":"synchronized","author":"Tekartik <<EMAIL>>","description":"Lock mechanism to prevent concurrent access to asynchronous code","homepage":
"https://github.com/tekartik/synchronized.dart","environment":{"sdk":">=2.0.0 <3.0.0"},"dev_dependencies":{"build_runner":">=0.9.2","test":">=0.12.0","chrome_travis":{"git":{"ref":"dar
t2","url":"git://github.com/tekartik/chrome_travis.dart"}},"build_test":">=0.10.3"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-1.5.3%2B2.tar.gz","archive_sha25
6":"7c84434cb4e2cef1799e934130115709d19837bfdeadea8a63b6ced0dc93fe67","published":"2018-12-22T12:15:23.737091Z"},{"version":"2.0.0","pubspec":{"version":"2.0.0","name":"synchronized","
author":"Tekartik <<EMAIL>>","description":"Lock mechanism to prevent concurrent access to asynchronous code.","homepage":"https://github.com/tekartik/synchronized.dart","envi
ronment":{"sdk":">=2.0.0 <3.0.0"},"dev_dependencies":{"build_runner":">=0.9.2","test":">=1.5.0","build_web_compilers":">=1.1.0","chrome_travis":{"git":{"ref":"dart2","url":"git://githu
b.com/tekartik/chrome_travis.dart"}},"pedantic":">=1.4.0 <3.0.0","build_test":">=0.10.3"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-2.0.0.tar.gz","archive_sha
256":"b0b6eeb6582b8162d6fa1f7144577a114e8d9fa840396c6e05a15a092f922b9c","published":"2019-02-11T10:28:03.428482Z"},{"version":"2.0.1","pubspec":{"version":"2.0.1","name":"synchronized"
,"author":"Tekartik <<EMAIL>>","description":"Lock mechanism to prevent concurrent access to asynchronous code.","homepage":"https://github.com/tekartik/synchronized.dart","en
vironment":{"sdk":">=2.0.0 <3.0.0"},"dev_dependencies":{"build_runner":">=0.9.2","test":">=1.5.0","build_web_compilers":">=1.1.0","chrome_travis":{"git":{"ref":"dart2","url":"git://git
hub.com/tekartik/chrome_travis.dart"}},"pedantic":">=1.4.0 <3.0.0","build_test":">=0.10.3"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-2.0.1.tar.gz","archive_s
ha256":"b9051c3d0dd0cae376f7aabed24fafb25af52768bc249dcae968066d00ab4b45","published":"2019-02-12T14:12:24.606264Z"},{"version":"2.0.2","pubspec":{"version":"2.0.2","name":"synchronize
d","author":"Tekartik <<EMAIL>>","description":"Lock mechanism to prevent concurrent access to asynchronous code.","homepage":"https://github.com/tekartik/synchronized.dart","
environment":{"sdk":">=2.0.0 <3.0.0"},"dev_dependencies":{"build_runner":">=0.9.2","test":">=1.5.0","build_web_compilers":">=1.1.0","chrome_travis":{"git":{"ref":"dart2","url":"git://g
ithub.com/tekartik/chrome_travis.dart"}},"pedantic":">=1.4.0 <3.0.0","build_test":">=0.10.3"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-2.0.2.tar.gz","archive
_sha256":"4542c7d302935b98bf2ab6894d2ff267dfc93d7ea426770df5200bf5e70a62d4","published":"2019-02-12T15:55:31.890817Z"},{"version":"2.0.2+1","pubspec":{"version":"2.0.2+1","name":"synch
ronized","author":"Tekartik <<EMAIL>>","description":"Lock mechanism to prevent concurrent access to asynchronous code.","homepage":"https://github.com/tekartik/synchronized.d
art","environment":{"sdk":">=2.0.0 <3.0.0"},"dev_dependencies":{"build_runner":">=0.9.2","test":">=1.5.0","build_web_compilers":">=1.1.0","chrome_travis":{"git":{"ref":"dart2","url":"g
it://github.com/tekartik/chrome_travis.dart"}},"pedantic":">=1.4.0 <3.0.0","build_test":">=0.10.3"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-2.0.2%2B1.tar.gz
","archive_sha256":"36fe1ff6f762f09f62f7399a6b5e73b5ba2b4d62b6bebd7c3e93c2f661a3da95","published":"2019-02-12T16:26:52.565102Z"},{"version":"2.1.0","pubspec":{"version":"2.1.0","name":
"synchronized","author":"Tekartik <<EMAIL>>","description":"Lock mechanism to prevent concurrent access to asynchronous code.","homepage":"https://github.com/tekartik/synchron
ized.dart","environment":{"sdk":">=2.1.0-dev <3.0.0"},"dev_dependencies":{"build_runner":">=0.9.2","test":">=1.5.0","build_web_compilers":">=1.1.0","chrome_travis":{"git":{"ref":"dart2
","url":"git://github.com/tekartik/chrome_travis.dart"}},"pedantic":">=1.4.0 <3.0.0","build_test":">=0.10.3"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-2.1.0.
tar.gz","archive_sha256":"886b95731a4eeecf5b51d6d2f3f612e408492f70f0a3ad6b051e4e3b4b90f760","published":"2019-02-21T18:53:41.660418Z"},{"version":"2.1.0+1","pubspec":{"version":"2.1.0+
1","name":"synchronized","author":"Tekartik <<EMAIL>>","description":"Lock mechanism to prevent concurrent access to asynchronous code.","homepage":"https://github.com/tekarti
k/synchronized.dart","environment":{"sdk":">=2.1.0-dev <3.0.0"},"dev_dependencies":{"build_runner":">=0.9.2","test":">=1.5.0","build_web_compilers":">=1.1.0","chrome_travis":{"git":{"r
ef":"dart2","url":"git://github.com/tekartik/chrome_travis.dart"}},"pedantic":">=1.4.0 <3.0.0","build_test":">=0.10.3"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchroni
zed-2.1.0%2B1.tar.gz","archive_sha256":"de067bff7b08a743828f94e78115e6f535e62d031095dcfbaac8aaca449ba9f2","published":"2019-06-18T16:40:02.302098Z"},{"version":"2.1.0+2","pubspec":{"na
me":"synchronized","description":"Lock mechanism to prevent concurrent access to asynchronous code.","version":"2.1.0+2","author":"Tekartik <<EMAIL>>","homepage":"https://gith
ub.com/tekartik/synchronized.dart","environment":{"sdk":">=2.1.0-dev <3.0.0"},"dev_dependencies":{"pedantic":">=1.4.0 <3.0.0","test":">=1.5.0","chrome_travis":{"git":{"url":"git://gith
ub.com/tekartik/chrome_travis.dart","ref":"dart2"}},"build_runner":">=0.9.2","build_test":">=0.10.3","build_web_compilers":">=1.1.0","process_run":null}},"archive_url":"https://pub.flu
tter-io.cn/api/archives/synchronized-2.1.0%2B2.tar.gz","archive_sha256":"5a92f7406926f6ac8629fba883f50220c3aafeaa6390252bfbd59dec3906fe75","published":"2019-11-21T11:03:39.352640Z"},{"
version":"2.1.1","pubspec":{"name":"synchronized","description":"Lock mechanism to prevent concurrent access to asynchronous code.","version":"2.1.1","author":"Tekartik <dev@tekartik.c
om>","homepage":"https://github.com/tekartik/synchronized.dart","environment":{"sdk":">=2.5.0 <3.0.0"},"dev_dependencies":{"pedantic":">=1.4.0 <3.0.0","pub_semver":null,"test":">=1.5.0
","chrome_travis":{"git":{"url":"git://github.com/tekartik/chrome_travis.dart","ref":"dart2"}},"build_runner":">=0.9.2","build_test":">=0.10.3","build_web_compilers":">=1.1.0","process
_run":null,"io":null,"yaml":null}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-2.1.1.tar.gz","archive_sha256":"a73e0ff1ae32e33446805c9815d1001b79344f66b6b1868c9b
ce2305dc2be1b3","published":"2019-12-06T20:03:32.399047Z"},{"version":"2.2.0","pubspec":{"name":"synchronized","description":"Lock mechanism to prevent concurrent access to asynchronou
s code.","version":"2.2.0","homepage":"https://github.com/tekartik/synchronized.dart","environment":{"sdk":">=2.6.0 <3.0.0"},"dev_dependencies":{"pedantic":">=1.9.0 <3.0.0","pub_semver
":null,"test":">=1.5.0","chrome_travis":{"git":{"url":"git://github.com/tekartik/chrome_travis.dart","ref":"dart2"}},"build_runner":">=0.9.2","build_test":">=0.10.3","build_web_compile
rs":">=1.1.0","process_run":null,"io":null,"yaml":null}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-2.2.0.tar.gz","archive_sha256":"3aec6b8760ed0b7e571575d1a98c
5589f1dab70561a6db248846b47e89ba759c","published":"2020-01-23T18:03:03.923319Z"},{"version":"2.2.0+1","pubspec":{"name":"synchronized","description":"Lock mechanism to prevent concurre
nt access to asynchronous code.","version":"2.2.0+1","homepage":"https://github.com/tekartik/synchronized.dart/tree/master/synchronized","environment":{"sdk":">=2.8.0 <3.0.0"},"dev_dep
endencies":{"pedantic":">=1.9.0 <3.0.0","pub_semver":null,"test":">=1.5.0","build_runner":">=0.9.2","build_test":">=0.10.3","build_web_compilers":">=1.1.0","process_run":null,"io":null
,"yaml":null}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-2.2.0%2B1.tar.gz","archive_sha256":"70f1e8fe8b76b7c7279647e09d8a47a755baada5d2d5d1eb71b175b35ed793be",
"published":"2020-06-18T09:49:12.782658Z"},{"version":"2.2.0+2","pubspec":{"name":"synchronized","description":"Lock mechanism to prevent concurrent access to asynchronous code.","vers
ion":"2.2.0+2","homepage":"https://github.com/tekartik/synchronized.dart/tree/master/synchronized","environment":{"sdk":">=2.8.0 <3.0.0"},"dev_dependencies":{"pedantic":">=1.9.0 <3.0.0
","pub_semver":null,"test":">=1.5.0","build_runner":">=0.9.2","build_test":">=0.10.3","build_web_compilers":">=1.1.0","process_run":null,"io":null,"yaml":null}},"archive_url":"https://
pub.flutter-io.cn/api/archives/synchronized-2.2.0%2B2.tar.gz","archive_sha256":"294228ee6183405c4053a60e8b47c22956da37c92fd410c89ddf0279c188f735","published":"2020-07-15T21:34:34.64970
6Z"},{"version":"3.0.0-nullsafety.0","pubspec":{"name":"synchronized","description":"Lock mechanism to prevent concurrent access to asynchronous code.","version":"3.0.0-nullsafety.0","
homepage":"https://github.com/tekartik/synchronized.dart/tree/master/synchronized","environment":{"sdk":">=2.12.29-0 <3.0.0"},"dev_dependencies":{"pedantic":">=1.10.0-nullsafety.3 <3.0
.0","pub_semver":">=1.4.4","test":">=1.16.0-nullsafety","build_runner":">=1.10.2","build_test":">=1.2.1","build_web_compilers":">=1.1.0","process_run":">=0.10.12+3","io":">=0.3.4","yam
l":">=2.2.1"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-3.0.0-nullsafety.0.tar.gz","archive_sha256":"c2e5ef9697f1aec27f27858bbb9cd78246849dca5decc9f436e002241
e971497","published":"2020-11-19T19:27:03.775464Z"},{"version":"3.0.0-nullsafety.1","pubspec":{"name":"synchronized","description":"Lock mechanism to prevent concurrent access to async
hronous code.","version":"3.0.0-nullsafety.1","homepage":"https://github.com/tekartik/synchronized.dart/tree/master/synchronized","environment":{"sdk":">=2.12.0-29 <3.0.0"},"dev_depend
encies":{"pedantic":">=1.10.0-nullsafety.3 <3.0.0","pub_semver":">=1.4.4","test":">=1.16.0-nullsafety","build_runner":">=1.10.2","build_test":">=1.2.1","build_web_compilers":">=1.1.0",
"process_run":">=0.10.12+3","io":">=0.3.4","yaml":">=2.2.1"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-3.0.0-nullsafety.1.tar.gz","archive_sha256":"1bce1e5756
a42d5879d759110028419515e100e4073d8975d022d90abb6f1ab1","published":"2020-11-19T19:36:43.405088Z"},{"version":"3.0.0","pubspec":{"name":"synchronized","description":"Lock mechanism to
prevent concurrent access to asynchronous code.","version":"3.0.0","homepage":"https://github.com/tekartik/synchronized.dart/tree/master/synchronized","environment":{"sdk":">=2.12.0-0
<3.0.0"},"dev_dependencies":{"pedantic":">=1.10.0 <3.0.0","pub_semver":">=2.0.0","test":">=1.16.2","build_runner":">=1.11.2","build_test":">=1.3.7","build_web_compilers":">=2.16.4","pr
ocess_run":">=0.12.0-0","yaml":">=3.0.0"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-3.0.0.tar.gz","archive_sha256":"271977ff1e9e82ceefb4f08424b8839f577c1852e0
726b5ce855311b46d3ef83","published":"2021-02-17T16:24:02.392108Z"},{"version":"3.0.0+1","pubspec":{"name":"synchronized","description":"Lock mechanism to prevent concurrent access to a
synchronous code.","version":"3.0.0+1","homepage":"https://github.com/tekartik/synchronized.dart/tree/master/synchronized","environment":{"sdk":">=2.14.0 <3.0.0"},"dev_dependencies":{"
lints":">=1.0.1","pub_semver":">=2.0.0","test":">=1.16.2","build_runner":">=1.11.2","build_test":">=1.3.7","build_web_compilers":">=2.16.4","process_run":">=0.12.0-0","yaml":">=3.0.0"}
},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-3.0.0%2B1.tar.gz","archive_sha256":"a4c404dabb28d0ed6bac6e48b2aa2f1f97a9193e05dd5f48c6c2521ca2a9f3df","published":"
2022-03-24T21:37:44.011155Z"},{"version":"3.0.0+2","pubspec":{"name":"synchronized","description":"Lock mechanism to prevent concurrent access to asynchronous code.","version":"3.0.0+2
","homepage":"https://github.com/tekartik/synchronized.dart/tree/master/synchronized","environment":{"sdk":">=2.14.0 <3.0.0"},"dev_dependencies":{"lints":">=1.0.1","pub_semver":">=2.0.
0","test":">=1.16.2","build_runner":">=1.11.2","build_test":">=1.3.7","build_web_compilers":">=2.16.4","process_run":">=0.12.0-0","yaml":">=3.0.0"}},"archive_url":"https://pub.flutter-
io.cn/api/archives/synchronized-3.0.0%2B2.tar.gz","archive_sha256":"a7f0790927c0806ae0d5eb061c713748fa6070ef0037e391a2d53c3844c09dc2","published":"2022-03-24T21:46:45.006067Z"},{"versi
on":"3.0.0+3","pubspec":{"name":"synchronized","description":"Lock mechanism to prevent concurrent access to asynchronous code.","version":"3.0.0+3","homepage":"https://github.com/teka
rtik/synchronized.dart/tree/master/synchronized","funding":["https://github.com/sponsors/alextekartik"],"environment":{"sdk":">=2.14.0 <3.0.0"},"dev_dependencies":{"lints":">=1.0.1","p
ub_semver":">=2.0.0","test":">=1.16.2","build_runner":">=1.11.2","build_test":">=1.3.7","build_web_compilers":">=2.16.4","process_run":">=0.12.0-0","yaml":">=3.0.0"}},"archive_url":"ht
tps://pub.flutter-io.cn/api/archives/synchronized-3.0.0%2B3.tar.gz","archive_sha256":"7b530acd9cb7c71b0019a1e7fa22c4105e675557a4400b6a401c71c5e0ade1ac","published":"2022-09-03T08:10:47
.687767Z"},{"version":"3.0.1","pubspec":{"name":"synchronized","description":"Lock mechanism to prevent concurrent access to asynchronous code.","version":"3.0.1","homepage":"https://g
ithub.com/tekartik/synchronized.dart/tree/master/synchronized","funding":["https://github.com/sponsors/alextekartik"],"environment":{"sdk":">=2.18.0 <3.0.0"},"dev_dependencies":{"lints
":">=1.0.1","pub_semver":">=2.0.0","test":">=1.16.2","build_runner":">=1.11.2","build_test":">=1.3.7","build_web_compilers":">=2.16.4","process_run":">=0.12.0-0","yaml":">=3.0.0"}},"ar
chive_url":"https://pub.flutter-io.cn/api/archives/synchronized-3.0.1.tar.gz","archive_sha256":"33b31b6beb98100bf9add464a36a8dd03eb10c7a8cf15aeec535e9b054aaf04b","published":"2023-01-1
1T17:42:05.764732Z"},{"version":"3.1.0","pubspec":{"name":"synchronized","description":"Lock mechanism to prevent concurrent access to asynchronous code.","version":"3.1.0","homepage":
"https://github.com/tekartik/synchronized.dart/tree/master/synchronized","funding":["https://github.com/sponsors/alextekartik"],"environment":{"sdk":">=2.18.0 <4.0.0"},"dev_dependencie
s":{"lints":">=1.0.1","pub_semver":">=2.0.0","test":">=1.16.2","build_runner":">=1.11.2","build_test":">=1.3.7","build_web_compilers":">=2.16.4","process_run":">=0.12.0-0","yaml":">=3.
0.0"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-3.1.0.tar.gz","archive_sha256":"5fcbd27688af6082f5abd611af56ee575342c30e87541d0245f7ff99faa02c60","published":
"2023-04-20T13:59:21.132613Z"},{"version":"3.1.0+1","pubspec":{"name":"synchronized","description":"Lock mechanism to prevent concurrent access to asynchronous code.","version":"3.1.0+
1","homepage":"https://github.com/tekartik/synchronized.dart/tree/master/synchronized","funding":["https://github.com/sponsors/alextekartik"],"environment":{"sdk":">=3.0.0 <4.0.0"},"de
v_dependencies":{"lints":">=1.0.1","pub_semver":">=2.0.0","test":">=1.16.2","build_runner":">=1.11.2","build_test":">=1.3.7","build_web_compilers":">=2.16.4","process_run":">=0.12.0-0"
,"yaml":">=3.0.0"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-3.1.0%2B1.tar.gz","archive_sha256":"539ef412b170d65ecdafd780f924e5be3f60032a1128df156adad6c5b373d
558","published":"2023-12-17T16:27:54.627788Z"},{"version":"3.2.0","pubspec":{"name":"synchronized","description":"Lock mechanism to prevent concurrent access to asynchronous code.","v
ersion":"3.2.0","homepage":"https://github.com/tekartik/synchronized.dart/tree/master/synchronized","funding":["https://github.com/sponsors/alextekartik"],"topics":["mutex","lock"],"en
vironment":{"sdk":"^3.5.0"},"dev_dependencies":{"web":">=0.5.0","lints":">=1.0.1","pub_semver":">=2.0.0","test":">=1.16.2","build_runner":">=1.11.2","build_test":">=1.3.7","build_web_c
ompilers":">=2.16.4","process_run":">=0.12.0-0","yaml":">=3.0.0"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-3.2.0.tar.gz","archive_sha256":"a824e842b8a054f91a
728b783c177c1e4731f6b124f9192468457a8913371255","published":"2024-08-14T17:23:59.340977Z"},{"version":"3.3.0","pubspec":{"name":"synchronized","description":"Lock mechanism to prevent
concurrent access to asynchronous code.","version":"3.3.0","homepage":"https://github.com/tekartik/synchronized.dart/tree/master/synchronized","funding":["https://github.com/sponsors/a
lextekartik"],"topics":["mutex","lock","async","concurrency"],"environment":{"sdk":"^3.5.0"},"dev_dependencies":{"web":">=0.5.0","lints":">=1.0.1","pub_semver":">=2.0.0","test":">=1.16
.2","build_runner":">=1.11.2","build_test":">=1.3.7","build_web_compilers":">=2.16.4","process_run":">=0.12.0-0","yaml":">=3.0.0"}},"archive_url":"https://pub.flutter-io.cn/api/archive
s/synchronized-3.3.0.tar.gz","archive_sha256":"977a21aebd44be65d76f099a9066596455175b7d2f63340f7b8d5f74658c9e16","published":"2024-09-15T14:49:44.228716Z"},{"version":"3.3.0+1","pubspe
c":{"name":"synchronized","description":"Lock mechanism to prevent concurrent access to asynchronous code.","version":"3.3.0+1","homepage":"https://github.com/tekartik/synchronized.dar
t/tree/master/synchronized","funding":["https://github.com/sponsors/alextekartik"],"topics":["mutex","lock","async","concurrency"],"environment":{"sdk":"^3.5.0"},"dev_dependencies":{"w
eb":">=0.5.0","lints":">=1.0.1","pub_semver":">=2.0.0","test":">=1.16.2","build_runner":">=1.11.2","build_test":">=1.3.7","build_web_compilers":">=2.16.4","process_run":">=0.12.0-0","y
aml":">=3.0.0"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-3.3.0%2B1.tar.gz","archive_sha256":"647979790299e614e0e81e63a2ac26b94d90e550b2b01d1a3f810ffad0fd250e
","published":"2024-09-15T14:53:12.533717Z"},{"version":"3.3.0+2","pubspec":{"name":"synchronized","description":"Lock mechanism to prevent concurrent access to asynchronous code.","ve
rsion":"3.3.0+2","homepage":"https://github.com/tekartik/synchronized.dart/tree/master/synchronized","funding":["https://github.com/sponsors/alextekartik"],"topics":["mutex","lock","as
ync","concurrency"],"environment":{"sdk":"^3.5.0"},"dev_dependencies":{"web":">=0.5.0","lints":">=1.0.1","pub_semver":">=2.0.0","test":">=1.16.2","build_runner":">=1.11.2","build_test"
:">=1.3.7","build_web_compilers":">=2.16.4","process_run":">=0.12.0-0","yaml":">=3.0.0"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-3.3.0%2B2.tar.gz","archive_
sha256":"51b08572b9f091f8c3eb4d9d4be253f196ff0075d5ec9b10a884026d5b55d7bc","published":"2024-09-15T15:01:49.338641Z"},{"version":"3.3.0+3","pubspec":{"name":"synchronized","description
":"Lock mechanism to prevent concurrent access to asynchronous code.","version":"3.3.0+3","homepage":"https://github.com/tekartik/synchronized.dart/tree/master/synchronized","funding":
["https://github.com/sponsors/alextekartik"],"topics":["mutex","lock","async","concurrency"],"environment":{"sdk":"^3.5.0"},"dev_dependencies":{"web":">=0.5.0","lints":">=1.0.1","pub_s
emver":">=2.0.0","test":">=1.16.2","build_runner":">=1.11.2","build_test":">=1.3.7","build_web_compilers":">=2.16.4","process_run":">=0.12.0-0","yaml":">=3.0.0"}},"archive_url":"https:
//pub.flutter-io.cn/api/archives/synchronized-3.3.0%2B3.tar.gz","archive_sha256":"69fe30f3a8b04a0be0c15ae6490fc859a78ef4c43ae2dd5e8a623d45bfcf9225","published":"2024-09-27T10:08:01.048
307Z"},{"version":"3.3.1","pubspec":{"name":"synchronized","description":"Lock mechanism to prevent concurrent access to asynchronous code.","version":"3.3.1","homepage":"https://githu
b.com/tekartik/synchronized.dart/tree/master/synchronized","funding":["https://github.com/sponsors/alextekartik"],"topics":["mutex","lock","async","concurrency"],"environment":{"sdk":"
^3.7.0"},"dev_dependencies":{"web":">=0.5.0","lints":">=1.0.1","pub_semver":">=2.0.0","test":">=1.16.2","build_runner":">=1.11.2","build_test":">=1.3.7","build_web_compilers":">=2.16.4
","process_run":">=0.12.0-0","yaml":">=3.0.0"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-3.3.1.tar.gz","archive_sha256":"0669c70faae6270521ee4f05bffd2919892d4
2d1276e6c495be80174b6bc0ef6","published":"2025-02-13T08:22:34.073971Z"},{"version":"3.4.0","pubspec":{"name":"synchronized","description":"Lock mechanism to prevent concurrent access t
o asynchronous code.","version":"3.4.0","homepage":"https://github.com/tekartik/synchronized.dart/tree/master/synchronized","funding":["https://github.com/sponsors/alextekartik"],"topi
cs":["mutex","lock","async","concurrency"],"environment":{"sdk":"^3.8.0"},"dev_dependencies":{"web":">=0.5.0","lints":">=1.0.1","pub_semver":">=2.0.0","test":">=1.16.2","build_runner":
">=1.11.2","build_test":">=1.3.7","build_web_compilers":">=2.16.4","process_run":">=0.12.0-0","yaml":">=3.0.0"}},"archive_url":"https://pub.flutter-io.cn/api/archives/synchronized-3.4.
0.tar.gz","archive_sha256":"c254ade258ec8282947a0acbbc90b9575b4f19673533ee46f2f6e9b3aeefd7c0","published":"2025-06-26T20:11:33.815765Z"}],"_fetchedAt":"2025-07-15T16:34:44.914838"}
IO  : Finished git. Exit code 0.
    | stdout:
    | | 770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-parse --is-inside-git-dir" in D:/PUB\git\cache\flutter_uikit-02c9488341ccd2efd86ef56eeb7a9605026c44a3
IO  : Finished git. Exit code 0.
    | stdout:
    | | true
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-list --max-count=1 f6aae98c19d47c510abe52a34f11da967f646924" in D:/PUB\git\cache\flutter_uikit-02c9488341ccd2efd86ef56eeb7a9605026c44a3
IO  : Finished git. Exit code 0.
    | stdout:
    | | f6aae98c19d47c510abe52a34f11da967f646924
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-parse --is-inside-git-dir" in D:/PUB\git\cache\flutter_blue_plus-cf59159b4dd4b8f3a23001ab12bb08df78c6f345
IO  : Finished git. Exit code 0.
    | stdout:
    | | true
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-list --max-count=1 25f148c4c7822a786de56ec50a254846027431b0" in D:/PUB\git\cache\flutter_blue_plus-cf59159b4dd4b8f3a23001ab12bb08df78c6f345
IO  : Finished git. Exit code 0.
    | stdout:
    | | 25f148c4c7822a786de56ec50a254846027431b0
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-parse --is-inside-git-dir" in D:/PUB\git\cache\flutter_plus_plugins-af8b5b23c6c7ab153d742c5b3d5d48f5c666df36
IO  : Finished git. Exit code 0.
    | stdout:
    | | true
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-list --max-count=1 452bfb40465722552f31cf78cde60f99cc691e36" in D:/PUB\git\cache\flutter_plus_plugins-af8b5b23c6c7ab153d742c5b3d5d48f5c666df36
IO  : Finished git. Exit code 0.
    | stdout:
    | | 452bfb40465722552f31cf78cde60f99cc691e36
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-parse --is-inside-git-dir" in D:/PUB\git\cache\flutter_image_scanner-5d8656ae038b5d3e13d6f01ca9bfd9c841f2fc0c
IO  : Finished git. Exit code 0.
    | stdout:
    | | true
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-list --max-count=1 b9f20a88ca1c738c700a15655b8fab4c305c4d32" in D:/PUB\git\cache\flutter_image_scanner-5d8656ae038b5d3e13d6f01ca9bfd9c841f2fc0c
IO  : Finished git. Exit code 0.
    | stdout:
    | | b9f20a88ca1c738c700a15655b8fab4c305c4d32
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-parse --is-inside-git-dir" in D:/PUB\git\cache\fluttertpc_mobile_scanner-04db0187d24007960ea2afd14d71ac89d4074f4f
IO  : Finished git. Exit code 0.
    | stdout:
    | | true
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-list --max-count=1 8d353dca7678d28dbbec24e3566e10823ddccf37" in D:/PUB\git\cache\fluttertpc_mobile_scanner-04db0187d24007960ea2afd14d71ac89d4074f4f
IO  : Finished git. Exit code 0.
    | stdout:
    | | 8d353dca7678d28dbbec24e3566e10823ddccf37
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-parse --is-inside-git-dir" in D:/PUB\git\cache\flutter_packages-0a4d7447ca89e3e387a011f31be855a7af59a651
IO  : Finished git. Exit code 0.
    | stdout:
    | | true
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-list --max-count=1 056bced4b677ed4f168b245823570c40fe55bddb" in D:/PUB\git\cache\flutter_packages-0a4d7447ca89e3e387a011f31be855a7af59a651
IO  : Finished git. Exit code 0.
    | stdout:
    | | 056bced4b677ed4f168b245823570c40fe55bddb
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-parse --is-inside-git-dir" in D:/PUB\git\cache\flutter_plus_plugins-af8b5b23c6c7ab153d742c5b3d5d48f5c666df36
IO  : Finished git. Exit code 0.
    | stdout:
    | | true
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-list --max-count=1 a1347adcca3a46346a6ddd127cebcec9970cad6c" in D:/PUB\git\cache\flutter_plus_plugins-af8b5b23c6c7ab153d742c5b3d5d48f5c666df36
IO  : Finished git. Exit code 0.
    | stdout:
    | | a1347adcca3a46346a6ddd127cebcec9970cad6c
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-parse --is-inside-git-dir" in D:/PUB\git\cache\flutter_permission_handler-4f98d7bb73c33ba0ebe389afefc4478304e3eeaf
IO  : Finished git. Exit code 0.
    | stdout:
    | | true
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-list --max-count=1 1e7c80f0720aff742d9f1e08017e1753bcad76ef" in D:/PUB\git\cache\flutter_permission_handler-4f98d7bb73c33ba0ebe389afefc4478304e3eeaf
IO  : Finished git. Exit code 0.
    | stdout:
    | | 1e7c80f0720aff742d9f1e08017e1753bcad76ef
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-parse --is-inside-git-dir" in D:/PUB\git\cache\fluttertpc_open_app_settings-dd32b1dae261aaea0fdf218547ada8e15b112fed
IO  : Finished git. Exit code 0.
    | stdout:
    | | true
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-list --max-count=1 038c0dd8ffd45bcf84691d62cfff957748cd493a" in D:/PUB\git\cache\fluttertpc_open_app_settings-dd32b1dae261aaea0fdf218547ada8e15b112fed
IO  : Finished git. Exit code 0.
    | stdout:
    | | 038c0dd8ffd45bcf84691d62cfff957748cd493a
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-parse --is-inside-git-dir" in D:/PUB\git\cache\flutter_packages-0a4d7447ca89e3e387a011f31be855a7af59a651
IO  : Finished git. Exit code 0.
    | stdout:
    | | true
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-list --max-count=1 770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc" in D:/PUB\git\cache\flutter_packages-0a4d7447ca89e3e387a011f31be855a7af59a651
IO  : Finished git. Exit code 0.
    | stdout:
    | | 770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-parse --is-inside-git-dir" in D:/PUB\git\cache\flutter_packages-0a4d7447ca89e3e387a011f31be855a7af59a651
IO  : Finished git. Exit code 0.
    | stdout:
    | | true
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-list --max-count=1 c030f4f22899ee7c436687008c673a6218b6cdf3" in D:/PUB\git\cache\flutter_packages-0a4d7447ca89e3e387a011f31be855a7af59a651
IO  : Finished git. Exit code 0.
    | stdout:
    | | c030f4f22899ee7c436687008c673a6218b6cdf3
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-parse --is-inside-git-dir" in D:/PUB\git\cache\flutter_packages-0a4d7447ca89e3e387a011f31be855a7af59a651
IO  : Finished git. Exit code 0.
    | stdout:
    | | true
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-list --max-count=1 770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc" in D:/PUB\git\cache\flutter_packages-0a4d7447ca89e3e387a011f31be855a7af59a651
IO  : Finished git. Exit code 0.
    | stdout:
    | | 770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-parse --is-inside-git-dir" in D:/PUB\git\cache\wuling-flutter-cashier-0438f482e77bf1db3b1ebf7ba71ca8103f25cc3a
IO  : Finished git. Exit code 0.
    | stdout:
    | | true
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-list --max-count=1 b63021fe6a6d5e988a11174579669ac7b38cde60" in D:/PUB\git\cache\wuling-flutter-cashier-0438f482e77bf1db3b1ebf7ba71ca8103f25cc3a
IO  : Finished git. Exit code 0.
    | stdout:
    | | b63021fe6a6d5e988a11174579669ac7b38cde60
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-parse --is-inside-git-dir" in D:/PUB\git\cache\flutter_plus_plugins-af8b5b23c6c7ab153d742c5b3d5d48f5c666df36
IO  : Finished git. Exit code 0.
    | stdout:
    | | true
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-list --max-count=1 ff774d947bd15d9be08629e3ccfb29dda2e864bd" in D:/PUB\git\cache\flutter_plus_plugins-af8b5b23c6c7ab153d742c5b3d5d48f5c666df36
IO  : Finished git. Exit code 0.
    | stdout:
    | | ff774d947bd15d9be08629e3ccfb29dda2e864bd
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-parse --is-inside-git-dir" in D:/PUB\git\cache\flutter_packages-0a4d7447ca89e3e387a011f31be855a7af59a651
IO  : Finished git. Exit code 0.
    | stdout:
    | | true
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-list --max-count=1 056bced4b677ed4f168b245823570c40fe55bddb" in D:/PUB\git\cache\flutter_packages-0a4d7447ca89e3e387a011f31be855a7af59a651
IO  : Finished git. Exit code 0.
    | stdout:
    | | 056bced4b677ed4f168b245823570c40fe55bddb
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-parse --is-inside-git-dir" in D:/PUB\git\cache\flutter_refresh_null_safe-372a569fd599372f9d4236bff05b88ca7e6b8c18
IO  : Finished git. Exit code 0.
    | stdout:
    | | true
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-list --max-count=1 0e07678e85497619f1f0b88f1585e313b26dc717" in D:/PUB\git\cache\flutter_refresh_null_safe-372a569fd599372f9d4236bff05b88ca7e6b8c18
IO  : Finished git. Exit code 0.
    | stdout:
    | | 0e07678e85497619f1f0b88f1585e313b26dc717
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-parse --is-inside-git-dir" in D:/PUB\git\cache\flutter_permission_handler-4f98d7bb73c33ba0ebe389afefc4478304e3eeaf
IO  : Finished git. Exit code 0.
    | stdout:
    | | true
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-list --max-count=1 f139dc532352dcecb8e9132a910e4aa607f5d9c9" in D:/PUB\git\cache\flutter_permission_handler-4f98d7bb73c33ba0ebe389afefc4478304e3eeaf
IO  : Finished git. Exit code 0.
    | stdout:
    | | f139dc532352dcecb8e9132a910e4aa607f5d9c9
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-parse --is-inside-git-dir" in D:/PUB\git\cache\flutter_packages-0a4d7447ca89e3e387a011f31be855a7af59a651
IO  : Finished git. Exit code 0.
    | stdout:
    | | true
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-list --max-count=1 056bced4b677ed4f168b245823570c40fe55bddb" in D:/PUB\git\cache\flutter_packages-0a4d7447ca89e3e387a011f31be855a7af59a651
IO  : Finished git. Exit code 0.
    | stdout:
    | | 056bced4b677ed4f168b245823570c40fe55bddb
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-parse --is-inside-git-dir" in D:/PUB\git\cache\flutter_packages-0a4d7447ca89e3e387a011f31be855a7af59a651
IO  : Finished git. Exit code 0.
    | stdout:
    | | true
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-list --max-count=1 056bced4b677ed4f168b245823570c40fe55bddb" in D:/PUB\git\cache\flutter_packages-0a4d7447ca89e3e387a011f31be855a7af59a651
IO  : Finished git. Exit code 0.
    | stdout:
    | | 056bced4b677ed4f168b245823570c40fe55bddb
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-parse --is-inside-git-dir" in D:/PUB\git\cache\flutter_packages-0a4d7447ca89e3e387a011f31be855a7af59a651
IO  : Finished git. Exit code 0.
    | stdout:
    | | true
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-list --max-count=1 056bced4b677ed4f168b245823570c40fe55bddb" in D:/PUB\git\cache\flutter_packages-0a4d7447ca89e3e387a011f31be855a7af59a651
IO  : Finished git. Exit code 0.
    | stdout:
    | | 056bced4b677ed4f168b245823570c40fe55bddb
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-parse --is-inside-git-dir" in D:/PUB\git\cache\flutter_packages-0a4d7447ca89e3e387a011f31be855a7af59a651
IO  : Finished git. Exit code 0.
    | stdout:
    | | true
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-list --max-count=1 056bced4b677ed4f168b245823570c40fe55bddb" in D:/PUB\git\cache\flutter_packages-0a4d7447ca89e3e387a011f31be855a7af59a651
IO  : Finished git. Exit code 0.
    | stdout:
    | | 056bced4b677ed4f168b245823570c40fe55bddb
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-parse --is-inside-git-dir" in D:/PUB\git\cache\flutter_packages-0a4d7447ca89e3e387a011f31be855a7af59a651
IO  : Finished git. Exit code 0.
    | stdout:
    | | true
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-list --max-count=1 056bced4b677ed4f168b245823570c40fe55bddb" in D:/PUB\git\cache\flutter_packages-0a4d7447ca89e3e387a011f31be855a7af59a651
IO  : Finished git. Exit code 0.
    | stdout:
    | | 056bced4b677ed4f168b245823570c40fe55bddb
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-parse --is-inside-git-dir" in D:/PUB\git\cache\flutter_packages-0a4d7447ca89e3e387a011f31be855a7af59a651
IO  : Finished git. Exit code 0.
    | stdout:
    | | true
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-list --max-count=1 056bced4b677ed4f168b245823570c40fe55bddb" in D:/PUB\git\cache\flutter_packages-0a4d7447ca89e3e387a011f31be855a7af59a651
IO  : Finished git. Exit code 0.
    | stdout:
    | | 056bced4b677ed4f168b245823570c40fe55bddb
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-parse --is-inside-git-dir" in D:/PUB\git\cache\fluwx-c2b4abdee893df70230a3df93b80006dab24dad6
IO  : Finished git. Exit code 0.
    | stdout:
    | | true
    | Nothing output on stderr.
IO  : Spawning "cmd /c git rev-list --max-count=1 9fe4d9f1d3c05de5de18d25cc53852866d9c34d7" in D:/PUB\git\cache\fluwx-c2b4abdee893df70230a3df93b80006dab24dad6
IO  : Finished git. Exit code 0.
    | stdout:
    | | 9fe4d9f1d3c05de5de18d25cc53852866d9c34d7
    | Nothing output on stderr.
MSG :   _fe_analyzer_shared 61.0.0 (85.0.0 available)
    |   analyzer 5.13.0 (7.5.9 available)
    |   archive 3.4.10 (4.0.7 available)
    |   args 2.4.2 (2.7.0 available)
    |   asn1lib 1.4.1 (1.6.5 available)
    |   async 2.10.0 (2.13.0 available)
    |   badges 3.1.1 (3.1.2 available)
    |   boolean_selector 2.1.1 (2.1.2 available)
    |   build 2.3.1 (2.5.4 available)
    |   build_config 1.1.1 (1.1.2 available)
    |   build_daemon 3.1.1 (4.0.4 available)
    |   build_resolvers 2.4.0 (2.5.4 available)
    |   build_runner 2.3.3 (2.5.4 available)
    |   build_runner_core 7.2.7+1 (9.1.2 available)
    |   built_value 8.8.0 (8.10.1 available)
    |   cached_network_image 3.2.3 (3.4.1 available)
    |   cached_network_image_platform_interface 2.0.0 (4.1.1 available)
    |   cached_network_image_web 1.0.2 (1.3.1 available)
    |   carousel_slider 4.2.1 (5.1.1 available)
    |   characters 1.2.1 (1.4.1 available)
    |   checked_yaml 2.0.3 (2.0.4 available)
    |   clock 1.1.1 (1.1.2 available)
    |   code_builder 4.7.0 (4.10.1 available)
    |   collection 1.17.0 (1.19.1 available)
    |   connectivity_plus_platform_interface 1.2.4 (2.0.1 available)
    |   convert 3.1.1 (3.1.2 available)
    |   cross_file 0.3.3+6 (0.3.4+2 available)
    |   crypto 3.0.3 (3.0.6 available)
    |   cupertino_icons 1.0.6 (1.0.8 available)
    |   dart_style 2.3.2 (3.1.0 available)
    |   device_info_plus_platform_interface 7.0.2 (7.0.3 available)
    |   dio_web_adapter 1.1.1 (2.1.1 available)
    |   encrypt 5.0.1 (5.0.3 available)
    |   fading_edge_scrollview 3.0.0 (4.1.1 available)
    |   fake_async 1.3.1 (1.3.3 available)
    |   ffi 2.0.2 (2.1.4 available)
    |   file 6.1.4 (7.0.1 available)
    |   fixnum 1.1.0 (1.1.1 available)
    |   flutter_aliplayer 5.5.6 (7.3.0 available)
    |   flutter_blurhash 0.7.0 (0.9.1 available)
    |   flutter_cache_manager 3.3.1 (3.4.1 available)
    |   flutter_lints 2.0.3 (6.0.0 available)
    |   flutter_plugin_android_lifecycle 2.0.17 (2.0.28 available)
    |   flutter_slidable 2.0.0 (4.0.0 available)
    |   flutter_svg 2.0.5 (2.2.0 available)
    |   frontend_server_client 3.2.0 (4.0.0 available)
    |   glob 2.1.2 (2.1.3 available)
    |   graphs 2.3.1 (2.3.2 available)
    |   http 0.13.6 (1.4.0 available)
    |   http_multi_server 3.2.1 (3.2.2 available)
    |   http_parser 4.0.2 (4.1.2 available)
    |   image 4.2.0 (4.5.4 available)
    |   image_picker_platform_interface 2.9.1 (2.10.1 available)
    |   intl 0.18.1 (0.20.2 available)
    |   io 1.0.4 (1.0.5 available)
    |   js 0.6.5 (0.7.2 available)
    |   json_annotation 4.8.1 (4.9.0 available)
    |   json_serializable 6.6.2 (6.9.5 available)
    |   lints 2.0.1 (6.0.0 available)
    |   logging 1.2.0 (1.3.0 available)
    |   lottie 2.3.2 (3.3.1 available)
    |   marquee 2.2.3 (2.3.0 available)
    |   matcher 0.12.13 (0.12.17 available)
    |   material_color_utilities 0.2.0 (0.13.0 available)
    |   meta 1.8.0 (1.17.0 available)
    |   mime 1.0.4 (2.0.0 available)
    |   octo_image 1.0.2 (2.1.0 available)
    |   package_config 2.1.0 (2.2.0 available)
    |   package_info_plus_platform_interface 2.0.1 (3.2.0 available)
    |   path 1.8.2 (1.9.1 available)
    |   path_parsing 1.0.1 (1.1.0 available)
    |   path_provider_android 2.2.1 (2.2.17 available)
    |   path_provider_foundation 2.3.1 (2.4.1 available)
    |   path_provider_platform_interface 2.1.1 (2.1.2 available)
    |   path_provider_windows 2.2.1 (2.3.0 available)
    |   permission_handler_android 10.3.6 (13.0.1 available)
    |   permission_handler_apple 9.1.4 (9.4.7 available)
    |   permission_handler_platform_interface 3.12.0 (4.3.0 available)
    |   permission_handler_windows 0.1.3 (0.2.1 available)
    |   petitparser 5.1.0 (7.0.0 available)
    |   platform 3.1.3 (3.1.6 available)
    |   plugin_platform_interface 2.1.6 (2.1.8 available)
    |   pointycastle 3.7.3 (4.0.0 available)
    |   popover 0.2.8+2 (0.3.1 available)
    |   pub_semver 2.1.4 (2.2.0 available)
    |   pubspec_parse 1.2.3 (1.5.0 available)
    |   qr 3.0.1 (3.0.2 available)
    |   retrofit 4.4.2 (4.6.0 available)
    |   retrofit_generator 7.0.8 (9.7.0 available)
    |   rxdart 0.27.7 (0.28.0 available)
    |   shared_preferences_android 2.2.1 (2.4.10 available)
    |   shared_preferences_foundation 2.3.4 (2.5.4 available)
    |   shared_preferences_linux 2.3.2 (2.4.1 available)
    |   shared_preferences_platform_interface 2.3.1 (2.4.1 available)
    |   shared_preferences_web 2.2.1 (2.4.3 available)
    |   shared_preferences_windows 2.3.2 (2.4.1 available)
    |   shelf 1.4.1 (1.4.2 available)
    |   shelf_web_socket 1.0.4 (3.0.0 available)
    |   source_gen 1.3.2 (2.0.0 available)
    |   source_helper 1.3.4 (1.3.6 available)
    |   source_span 1.9.1 (1.10.1 available)
    |   sqflite 2.2.8+4 (2.4.2 available)
    |   sqflite_common 2.4.5+1 (2.5.5 available)
    |   ssl_checker 1.0.0 (1.1.0 available)
    |   stack_trace 1.11.0 (1.12.1 available)
    |   stream_channel 2.1.1 (2.1.4 available)
    |   stream_transform 2.1.0 (2.1.1 available)
    |   string_scanner 1.2.0 (1.4.1 available)
    |   synchronized 3.1.0 (3.4.0 available)
    |   term_glyph 1.2.1 (1.2.2 available)
    |   test_api 0.4.16 (0.7.6 available)
    |   timing 1.0.1 (1.0.2 available)
    |   typed_data 1.3.2 (1.4.0 available)
    |   url_launcher_android 6.2.0 (6.3.16 available)
    |   url_launcher_ios 6.2.0 (6.3.3 available)
    |   url_launcher_linux 3.1.0 (3.2.1 available)
    |   url_launcher_macos 3.1.0 (3.2.2 available)
    |   url_launcher_platform_interface 2.2.0 (2.3.2 available)
    |   url_launcher_web 2.0.19 (2.4.1 available)
    |   url_launcher_windows 3.1.0 (3.1.4 available)
    |   uuid 4.1.0 (4.5.1 available)
    |   vector_graphics 1.1.5 (1.1.19 available)
    |   vector_graphics_codec 1.1.5 (1.1.13 available)
    |   vector_graphics_compiler 1.1.5 (1.1.17 available)
    |   vector_math 2.1.4 (2.2.0 available)
    |   watcher 1.0.2 (1.1.2 available)
    |   web_socket_channel 2.4.0 (3.0.3 available)
    |   win32 4.1.4 (5.14.0 available)
    |   win32_registry 1.1.0 (2.1.0 available)
    |   xdg_directories 1.0.3 (1.1.0 available)
    |   xml 6.2.2 (6.6.0 available)
    |   yaml 3.1.2 (3.1.3 available)
WARN: Warning: You are using these overridden dependencies:
    | ! path_provider 2.1.0 from git https://gitee.com/openharmony-sig/flutter_packages.git at 770bd6 in packages/path_provider/path_provider
MSG : Got dependencies!
IO  : Writing 48481 characters to text file pubspec.lock.
FINE: Contents:
    | # Generated by pub
    | # See https://dart.dev/tools/pub/glossary#lockfile
    | packages:
    |   _fe_analyzer_shared:
    |     dependency: transitive
    |     description:
    |       name: _fe_analyzer_shared
    |       sha256: ae92f5d747aee634b87f89d9946000c2de774be1d6ac3e58268224348cd0101a
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "61.0.0"
    |   analyzer:
    |     dependency: transitive
    |     description:
    |       name: analyzer
    |       sha256: ea3d8652bda62982addfd92fdc2d0214e5f82e43325104990d4f4c4a2a313562
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "5.13.0"
    |   archive:
    |     dependency: transitive
    |     description:
    |       name: archive
    |       sha256: "22600aa1e926be775fa5fe7e6894e7fb3df9efda8891c73f70fb3262399a432d"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "3.4.10"
    |   args:
    |     dependency: transitive
    |     description:
    |       name: args
    |       sha256: eef6c46b622e0494a36c5a12d10d77fb4e855501a91c1b9ef9339326e58f0596
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.4.2"
    |   asn1lib:
    |     dependency: transitive
    |     description:
    |       name: asn1lib
    |       sha256: b74e3842a52c61f8819a1ec8444b4de5419b41a7465e69d4aa681445377398b0
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.4.1"
    |   async:
    |     dependency: transitive
    |     description:
    |       name: async
    |       sha256: bfe67ef28df125b7dddcea62755991f807aa39a2492a23e1550161692950bbe0
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.10.0"
    |   badges:
    |     dependency: "direct main"
    |     description:
    |       name: badges
    |       sha256: "6e7f3ec561ec08f47f912cfe349d4a1707afdc8dda271e17b046aa6d42c89e77"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "3.1.1"
    |   boolean_selector:
    |     dependency: transitive
    |     description:
    |       name: boolean_selector
    |       sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.1.1"
    |   build:
    |     dependency: transitive
    |     description:
    |       name: build
    |       sha256: "3fbda25365741f8251b39f3917fb3c8e286a96fd068a5a242e11c2012d495777"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.3.1"
    |   build_config:
    |     dependency: transitive
    |     description:
    |       name: build_config
    |       sha256: bf80fcfb46a29945b423bd9aad884590fb1dc69b330a4d4700cac476af1708d1
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.1.1"
    |   build_daemon:
    |     dependency: transitive
    |     description:
    |       name: build_daemon
    |       sha256: "757153e5d9cd88253cb13f28c2fb55a537dc31fefd98137549895b5beb7c6169"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "3.1.1"
    |   build_resolvers:
    |     dependency: transitive
    |     description:
    |       name: build_resolvers
    |       sha256: "0713a05b0386bd97f9e63e78108805a4feca5898a4b821d6610857f10c91e975"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.4.0"
    |   build_runner:
    |     dependency: "direct dev"
    |     description:
    |       name: build_runner
    |       sha256: b0a8a7b8a76c493e85f1b84bffa0588859a06197863dba8c9036b15581fd9727
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.3.3"
    |   build_runner_core:
    |     dependency: transitive
    |     description:
    |       name: build_runner_core
    |       sha256: "0671ad4162ed510b70d0eb4ad6354c249f8429cab4ae7a4cec86bbc2886eb76e"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "7.2.7+1"
    |   built_collection:
    |     dependency: transitive
    |     description:
    |       name: built_collection
    |       sha256: "376e3dd27b51ea877c28d525560790aee2e6fbb5f20e2f85d5081027d94e2100"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "5.1.1"
    |   built_value:
    |     dependency: transitive
    |     description:
    |       name: built_value
    |       sha256: "69acb7007eb2a31dc901512bfe0f7b767168be34cb734835d54c070bfa74c1b2"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "8.8.0"
    |   cached_network_image:
    |     dependency: "direct main"
    |     description:
    |       name: cached_network_image
    |       sha256: fd3d0dc1d451f9a252b32d95d3f0c3c487bc41a75eba2e6097cb0b9c71491b15
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "3.2.3"
    |   cached_network_image_platform_interface:
    |     dependency: transitive
    |     description:
    |       name: cached_network_image_platform_interface
    |       sha256: bb2b8403b4ccdc60ef5f25c70dead1f3d32d24b9d6117cfc087f496b178594a7
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.0.0"
    |   cached_network_image_web:
    |     dependency: transitive
    |     description:
    |       name: cached_network_image_web
    |       sha256: b8eb814ebfcb4dea049680f8c1ffb2df399e4d03bf7a352c775e26fa06e02fa0
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.0.2"
    |   camera_ohos:
    |     dependency: "direct main"
    |     description:
    |       path: "plugins/camera_ohos"
    |       relative: true
    |     source: path
    |     version: "1.1.1+39"
    |   camera_platform_interface:
    |     dependency: "direct main"
    |     description:
    |       path: "plugins/camera_platform_interface"
    |       relative: true
    |     source: path
    |     version: "1.1.1+39"
    |   carousel_slider:
    |     dependency: "direct main"
    |     description:
    |       name: carousel_slider
    |       sha256: "9c695cc963bf1d04a47bd6021f68befce8970bcd61d24938e1fb0918cf5d9c42"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "4.2.1"
    |   characters:
    |     dependency: transitive
    |     description:
    |       name: characters
    |       sha256: e6a326c8af69605aec75ed6c187d06b349707a27fbff8222ca9cc2cff167975c
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.2.1"
    |   checked_yaml:
    |     dependency: transitive
    |     description:
    |       name: checked_yaml
    |       sha256: feb6bed21949061731a7a75fc5d2aa727cf160b91af9a3e464c5e3a32e28b5ff
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.0.3"
    |   city_pickers:
    |     dependency: "direct main"
    |     description:
    |       name: city_pickers
    |       sha256: "583102c8d9eecb1f7abc5ff52a22d7cb019b9808cdb24b80c7692c769f8da153"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.3.0"
    |   clock:
    |     dependency: transitive
    |     description:
    |       name: clock
    |       sha256: cb6d7f03e1de671e34607e909a7213e31d7752be4fb66a86d29fe1eb14bfb5cf
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.1.1"
    |   code_builder:
    |     dependency: transitive
    |     description:
    |       name: code_builder
    |       sha256: "1be9be30396d7e4c0db42c35ea6ccd7cc6a1e19916b5dc64d6ac216b5544d677"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "4.7.0"
    |   collection:
    |     dependency: transitive
    |     description:
    |       name: collection
    |       sha256: cfc915e6923fe5ce6e153b0723c753045de46de1b4d63771530504004a45fae0
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.17.0"
    |   connectivity_plus:
    |     dependency: "direct main"
    |     description:
    |       path: "packages/connectivity_plus/connectivity_plus"
    |       ref: HEAD
    |       resolved-ref: ff774d947bd15d9be08629e3ccfb29dda2e864bd
    |       url: "https://gitee.com/openharmony-sig/flutter_plus_plugins.git"
    |     source: git
    |     version: "5.0.1"
    |   connectivity_plus_platform_interface:
    |     dependency: transitive
    |     description:
    |       name: connectivity_plus_platform_interface
    |       sha256: cf1d1c28f4416f8c654d7dc3cd638ec586076255d407cef3ddbdaf178272a71a
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.2.4"
    |   convert:
    |     dependency: transitive
    |     description:
    |       name: convert
    |       sha256: "0f08b14755d163f6e2134cb58222dd25ea2a2ee8a195e53983d57c075324d592"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "3.1.1"
    |   cross_file:
    |     dependency: transitive
    |     description:
    |       name: cross_file
    |       sha256: "445db18de832dba8d851e287aff8ccf169bed30d2e94243cb54c7d2f1ed2142c"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "0.3.3+6"
    |   crypto:
    |     dependency: "direct main"
    |     description:
    |       name: crypto
    |       sha256: ff625774173754681d66daaf4a448684fb04b78f902da9cb3d308c19cc5e8bab
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "3.0.3"
    |   cupertino_icons:
    |     dependency: "direct main"
    |     description:
    |       name: cupertino_icons
    |       sha256: d57953e10f9f8327ce64a508a355f0b1ec902193f66288e8cb5070e7c47eeb2d
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.0.6"
    |   dart_style:
    |     dependency: transitive
    |     description:
    |       name: dart_style
    |       sha256: "1efa911ca7086affd35f463ca2fc1799584fb6aa89883cf0af8e3664d6a02d55"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.3.2"
    |   dbus:
    |     dependency: transitive
    |     description:
    |       name: dbus
    |       sha256: "79e0c23480ff85dc68de79e2cd6334add97e48f7f4865d17686dd6ea81a47e8c"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "0.7.11"
    |   device_info_plus:
    |     dependency: "direct main"
    |     description:
    |       path: "packages/device_info_plus/device_info_plus"
    |       ref: a1347ad
    |       resolved-ref: a1347adcca3a46346a6ddd127cebcec9970cad6c
    |       url: "https://gitee.com/openharmony-sig/flutter_plus_plugins.git"
    |     source: git
    |     version: "9.1.0"
    |   device_info_plus_platform_interface:
    |     dependency: transitive
    |     description:
    |       name: device_info_plus_platform_interface
    |       sha256: "0b04e02b30791224b31969eb1b50d723498f402971bff3630bca2ba839bd1ed2"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "7.0.2"
    |   dio:
    |     dependency: "direct main"
    |     description:
    |       name: dio
    |       sha256: "253a18bbd4851fecba42f7343a1df3a9a4c1d31a2c1b37e221086b4fa8c8dbc9"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "5.8.0+1"
    |   dio_web_adapter:
    |     dependency: transitive
    |     description:
    |       name: dio_web_adapter
    |       sha256: "0a2e95fc6bdeb623bb623fc41e90e6924e9a3bbd65089f9221f83c185366b479"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.1.1"
    |   encrypt:
    |     dependency: "direct main"
    |     description:
    |       name: encrypt
    |       sha256: "4fd4e4fdc21b9d7d4141823e1e6515cd94e7b8d84749504c232999fba25d9bbb"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "5.0.1"
    |   fading_edge_scrollview:
    |     dependency: transitive
    |     description:
    |       name: fading_edge_scrollview
    |       sha256: c25c2231652ce774cc31824d0112f11f653881f43d7f5302c05af11942052031
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "3.0.0"
    |   fake_async:
    |     dependency: transitive
    |     description:
    |       name: fake_async
    |       sha256: "511392330127add0b769b75a987850d136345d9227c6b94c96a04cf4a391bf78"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.3.1"
    |   ffi:
    |     dependency: transitive
    |     description:
    |       name: ffi
    |       sha256: ed5337a5660c506388a9f012be0288fb38b49020ce2b45fe1f8b8323fe429f99
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.0.2"
    |   file:
    |     dependency: transitive
    |     description:
    |       name: file
    |       sha256: "1b92bec4fc2a72f59a8e15af5f52cd441e4a7860b49499d69dfa817af20e925d"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "6.1.4"
    |   fixnum:
    |     dependency: transitive
    |     description:
    |       name: fixnum
    |       sha256: "25517a4deb0c03aa0f32fd12db525856438902d9c16536311e76cdc57b31d7d1"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.1.0"
    |   flutter:
    |     dependency: "direct main"
    |     description: flutter
    |     source: sdk
    |     version: "0.0.0"
    |   flutter_aliplayer:
    |     dependency: "direct main"
    |     description:
    |       name: flutter_aliplayer
    |       sha256: e099110ad3c6153f1d2a7d41f5d94da5560eb8373f4dfafaa5c695137d75646d
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "5.5.6"
    |   flutter_blue_plus:
    |     dependency: "direct main"
    |     description:
    |       path: "."
    |       ref: "25f148c4c7822a786de56ec50a254846027431b0"
    |       resolved-ref: "25f148c4c7822a786de56ec50a254846027431b0"
    |       url: "https://gitee.com/jennistian/flutter_blue_plus.git"
    |     source: git
    |     version: "1.33.5"
    |   flutter_blurhash:
    |     dependency: transitive
    |     description:
    |       name: flutter_blurhash
    |       sha256: "05001537bd3fac7644fa6558b09ec8c0a3f2eba78c0765f88912882b1331a5c6"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "0.7.0"
    |   flutter_cache_manager:
    |     dependency: "direct main"
    |     description:
    |       name: flutter_cache_manager
    |       sha256: "8207f27539deb83732fdda03e259349046a39a4c767269285f449ade355d54ba"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "3.3.1"
    |   flutter_easyloading:
    |     dependency: "direct main"
    |     description:
    |       name: flutter_easyloading
    |       sha256: ba21a3c883544e582f9cc455a4a0907556714e1e9cf0eababfcb600da191d17c
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "3.0.5"
    |   flutter_lints:
    |     dependency: "direct dev"
    |     description:
    |       name: flutter_lints
    |       sha256: a25a15ebbdfc33ab1cd26c63a6ee519df92338a9c10f122adda92938253bef04
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.0.3"
    |   flutter_pickers:
    |     dependency: "direct main"
    |     description:
    |       name: flutter_pickers
    |       sha256: f38a9d9229afed75f76bae64e628b78b9c20194873e3c141783523cf21ac8a95
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.1.9"
    |   flutter_platform_utils:
    |     dependency: "direct main"
    |     description:
    |       name: flutter_platform_utils
    |       sha256: cb06a696d0b027edb7a4e95527dde847f5c4b7bd4f90e894336f58fefe5561ce
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.0.0"
    |   flutter_plugin_android_lifecycle:
    |     dependency: transitive
    |     description:
    |       name: flutter_plugin_android_lifecycle
    |       sha256: b068ffc46f82a55844acfa4fdbb61fad72fa2aef0905548419d97f0f95c456da
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.0.17"
    |   flutter_refresh:
    |     dependency: transitive
    |     description:
    |       path: "."
    |       ref: "0.0.2"
    |       resolved-ref: "0e07678e85497619f1f0b88f1585e313b26dc717"
    |       url: "https://gitee.com/ybr815/flutter_refresh_null_safe.git"
    |     source: git
    |     version: "0.0.2"
    |   flutter_slidable:
    |     dependency: "direct main"
    |     description:
    |       name: flutter_slidable
    |       sha256: "6c68e1fad129b4b807b2218ef4cf7f7f6f61c5ec8861c990dc2278d9d03cb09f"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.0.0"
    |   flutter_spinkit:
    |     dependency: transitive
    |     description:
    |       name: flutter_spinkit
    |       sha256: d2696eed13732831414595b98863260e33e8882fc069ee80ec35d4ac9ddb0472
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "5.2.1"
    |   flutter_staggered_grid_view:
    |     dependency: "direct main"
    |     description:
    |       name: flutter_staggered_grid_view
    |       sha256: "19e7abb550c96fbfeb546b23f3ff356ee7c59a019a651f8f102a4ba9b7349395"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "0.7.0"
    |   flutter_svg:
    |     dependency: "direct main"
    |     description:
    |       name: flutter_svg
    |       sha256: f991fdb1533c3caeee0cdc14b04f50f0c3916f0dbcbc05237ccbe4e3c6b93f3f
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.0.5"
    |   flutter_test:
    |     dependency: "direct dev"
    |     description: flutter
    |     source: sdk
    |     version: "0.0.0"
    |   flutter_uikit:
    |     dependency: "direct main"
    |     description:
    |       path: "."
    |       ref: "3.1.2"
    |       resolved-ref: f6aae98c19d47c510abe52a34f11da967f646924
    |       url: "https://gitee.com/yubangjin/flutter_uikit.git"
    |     source: git
    |     version: "0.0.1"
    |   flutter_web_plugins:
    |     dependency: transitive
    |     description: flutter
    |     source: sdk
    |     version: "0.0.0"
    |   flutter_xlider:
    |     dependency: "direct main"
    |     description:
    |       name: flutter_xlider
    |       sha256: b83da229b8a2153adeefc5d9e08e0060689c8dc2187b30e3502cf67c1a6495be
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "3.5.0"
    |   fluwx:
    |     dependency: transitive
    |     description:
    |       path: "."
    |       ref: "9fe4d9f"
    |       resolved-ref: "9fe4d9f1d3c05de5de18d25cc53852866d9c34d7"
    |       url: "https://github.com/yeliulee/fluwx.git"
    |     source: git
    |     version: "5.2.6"
    |   frontend_server_client:
    |     dependency: transitive
    |     description:
    |       name: frontend_server_client
    |       sha256: "408e3ca148b31c20282ad6f37ebfa6f4bdc8fede5b74bc2f08d9d92b55db3612"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "3.2.0"
    |   glob:
    |     dependency: transitive
    |     description:
    |       name: glob
    |       sha256: "0e7014b3b7d4dac1ca4d6114f82bf1782ee86745b9b42a92c9289c23d8a0ab63"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.1.2"
    |   graphs:
    |     dependency: transitive
    |     description:
    |       name: graphs
    |       sha256: aedc5a15e78fc65a6e23bcd927f24c64dd995062bcd1ca6eda65a3cff92a4d19
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.3.1"
    |   http:
    |     dependency: transitive
    |     description:
    |       name: http
    |       sha256: "5895291c13fa8a3bd82e76d5627f69e0d85ca6a30dcac95c4ea19a5d555879c2"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "0.13.6"
    |   http_multi_server:
    |     dependency: transitive
    |     description:
    |       name: http_multi_server
    |       sha256: "97486f20f9c2f7be8f514851703d0119c3596d14ea63227af6f7a481ef2b2f8b"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "3.2.1"
    |   http_parser:
    |     dependency: "direct main"
    |     description:
    |       name: http_parser
    |       sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "4.0.2"
    |   iamgeqr_flutter_plugin:
    |     dependency: "direct main"
    |     description:
    |       path: "."
    |       ref: HEAD
    |       resolved-ref: b9f20a88ca1c738c700a15655b8fab4c305c4d32
    |       url: "https://gitee.com/jifamxd/flutter_image_scanner.git"
    |     source: git
    |     version: "0.0.1"
    |   image:
    |     dependency: "direct main"
    |     description:
    |       name: image
    |       sha256: "2237616a36c0d69aef7549ab439b833fb7f9fb9fc861af2cc9ac3eedddd69ca8"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "4.2.0"
    |   image_picker_ohos:
    |     dependency: "direct main"
    |     description:
    |       path: "packages/image_picker/image_picker_ohos"
    |       ref: HEAD
    |       resolved-ref: "056bced4b677ed4f168b245823570c40fe55bddb"
    |       url: "https://gitee.com/openharmony-sig/flutter_packages.git"
    |     source: git
    |     version: "0.8.7+4"
    |   image_picker_platform_interface:
    |     dependency: transitive
    |     description:
    |       name: image_picker_platform_interface
    |       sha256: ed9b00e63977c93b0d2d2b343685bed9c324534ba5abafbb3dfbd6a780b1b514
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.9.1"
    |   intl:
    |     dependency: "direct main"
    |     description:
    |       name: intl
    |       sha256: "3bc132a9dbce73a7e4a21a17d06e1878839ffbf975568bc875c60537824b0c4d"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "0.18.1"
    |   io:
    |     dependency: transitive
    |     description:
    |       name: io
    |       sha256: "2ec25704aba361659e10e3e5f5d672068d332fc8ac516421d483a11e5cbd061e"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.0.4"
    |   js:
    |     dependency: transitive
    |     description:
    |       name: js
    |       sha256: "5528c2f391ededb7775ec1daa69e65a2d61276f7552de2b5f7b8d34ee9fd4ab7"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "0.6.5"
    |   json_annotation:
    |     dependency: "direct main"
    |     description:
    |       name: json_annotation
    |       sha256: b10a7b2ff83d83c777edba3c6a0f97045ddadd56c944e1a23a3fdf43a1bf4467
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "4.8.1"
    |   json_serializable:
    |     dependency: "direct dev"
    |     description:
    |       name: json_serializable
    |       sha256: "43793352f90efa5d8b251893a63d767b2f7c833120e3cc02adad55eefec04dc7"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "6.6.2"
    |   json_to_model:
    |     dependency: "direct dev"
    |     description:
    |       name: json_to_model
    |       sha256: a1e2628df8e9d2b32c4d473a5e923648bed7f267be021a0d3148f4d056d1a5b6
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "3.2.4"
    |   lints:
    |     dependency: transitive
    |     description:
    |       name: lints
    |       sha256: "5e4a9cd06d447758280a8ac2405101e0e2094d2a1dbdd3756aec3fe7775ba593"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.0.1"
    |   logger:
    |     dependency: "direct main"
    |     description:
    |       name: logger
    |       sha256: "2621da01aabaf223f8f961e751f2c943dbb374dc3559b982f200ccedadaa6999"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.6.0"
    |   logging:
    |     dependency: transitive
    |     description:
    |       name: logging
    |       sha256: "623a88c9594aa774443aa3eb2d41807a48486b5613e67599fb4c41c0ad47c340"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.2.0"
    |   lottie:
    |     dependency: "direct main"
    |     description:
    |       name: lottie
    |       sha256: "23522951540d20a57a60202ed7022e6376bed206a4eee1c347a91f58bd57eb9f"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.3.2"
    |   lpinyin:
    |     dependency: transitive
    |     description:
    |       name: lpinyin
    |       sha256: "0bb843363f1f65170efd09fbdfc760c7ec34fc6354f9fcb2f89e74866a0d814a"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.0.3"
    |   marquee:
    |     dependency: "direct main"
    |     description:
    |       name: marquee
    |       sha256: "4b5243d2804373bdc25fc93d42c3b402d6ec1f4ee8d0bb72276edd04ae7addb8"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.2.3"
    |   matcher:
    |     dependency: transitive
    |     description:
    |       name: matcher
    |       sha256: "16db949ceee371e9b99d22f88fa3a73c4e59fd0afed0bd25fc336eb76c198b72"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "0.12.13"
    |   material_color_utilities:
    |     dependency: transitive
    |     description:
    |       name: material_color_utilities
    |       sha256: d92141dc6fe1dad30722f9aa826c7fbc896d021d792f80678280601aff8cf724
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "0.2.0"
    |   meta:
    |     dependency: transitive
    |     description:
    |       name: meta
    |       sha256: "6c268b42ed578a53088d834796959e4a1814b5e9e164f147f580a386e5decf42"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.8.0"
    |   mime:
    |     dependency: transitive
    |     description:
    |       name: mime
    |       sha256: e4ff8e8564c03f255408decd16e7899da1733852a9110a58fe6d1b817684a63e
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.0.4"
    |   mobile_scanner:
    |     dependency: "direct main"
    |     description:
    |       path: "."
    |       ref: HEAD
    |       resolved-ref: "8d353dca7678d28dbbec24e3566e10823ddccf37"
    |       url: "https://gitee.com/openharmony-sig/fluttertpc_mobile_scanner.git"
    |     source: git
    |     version: "3.5.6"
    |   nm:
    |     dependency: transitive
    |     description:
    |       name: nm
    |       sha256: "2c9aae4127bdc8993206464fcc063611e0e36e72018696cd9631023a31b24254"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "0.5.0"
    |   octo_image:
    |     dependency: transitive
    |     description:
    |       name: octo_image
    |       sha256: "107f3ed1330006a3bea63615e81cf637433f5135a52466c7caa0e7152bca9143"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.0.2"
    |   open_app_settings:
    |     dependency: "direct main"
    |     description:
    |       path: "."
    |       ref: "038c0dd"
    |       resolved-ref: "038c0dd8ffd45bcf84691d62cfff957748cd493a"
    |       url: "https://gitee.com/openharmony-sig/fluttertpc_open_app_settings.git"
    |     source: git
    |     version: "2.0.1"
    |   package_config:
    |     dependency: transitive
    |     description:
    |       name: package_config
    |       sha256: "1c5b77ccc91e4823a5af61ee74e6b972db1ef98c2ff5a18d3161c982a55448bd"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.1.0"
    |   package_info_plus:
    |     dependency: "direct main"
    |     description:
    |       path: "packages/package_info_plus/package_info_plus"
    |       ref: "452bfb4"
    |       resolved-ref: "452bfb40465722552f31cf78cde60f99cc691e36"
    |       url: "https://gitee.com/openharmony-sig/flutter_plus_plugins.git"
    |     source: git
    |     version: "4.2.0"
    |   package_info_plus_platform_interface:
    |     dependency: transitive
    |     description:
    |       name: package_info_plus_platform_interface
    |       sha256: "9bc8ba46813a4cc42c66ab781470711781940780fd8beddd0c3da62506d3a6c6"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.0.1"
    |   path:
    |     dependency: transitive
    |     description:
    |       name: path
    |       sha256: db9d4f58c908a4ba5953fcee2ae317c94889433e5024c27ce74a37f94267945b
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.8.2"
    |   path_parsing:
    |     dependency: transitive
    |     description:
    |       name: path_parsing
    |       sha256: e3e67b1629e6f7e8100b367d3db6ba6af4b1f0bb80f64db18ef1fbabd2fa9ccf
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.0.1"
    |   path_provider:
    |     dependency: "direct overridden"
    |     description:
    |       path: "packages/path_provider/path_provider"
    |       ref: "770bd6e"
    |       resolved-ref: "770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc"
    |       url: "https://gitee.com/openharmony-sig/flutter_packages.git"
    |     source: git
    |     version: "2.1.0"
    |   path_provider_android:
    |     dependency: transitive
    |     description:
    |       name: path_provider_android
    |       sha256: e595b98692943b4881b219f0a9e3945118d3c16bd7e2813f98ec6e532d905f72
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.2.1"
    |   path_provider_foundation:
    |     dependency: transitive
    |     description:
    |       name: path_provider_foundation
    |       sha256: "19314d595120f82aca0ba62787d58dde2cc6b5df7d2f0daf72489e38d1b57f2d"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.3.1"
    |   path_provider_linux:
    |     dependency: transitive
    |     description:
    |       name: path_provider_linux
    |       sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.2.1"
    |   path_provider_ohos:
    |     dependency: transitive
    |     description:
    |       path: "packages/path_provider/path_provider_ohos"
    |       ref: HEAD
    |       resolved-ref: "056bced4b677ed4f168b245823570c40fe55bddb"
    |       url: "https://gitee.com/openharmony-sig/flutter_packages.git"
    |     source: git
    |     version: "2.2.1"
    |   path_provider_platform_interface:
    |     dependency: transitive
    |     description:
    |       name: path_provider_platform_interface
    |       sha256: "94b1e0dd80970c1ce43d5d4e050a9918fce4f4a775e6142424c30a29a363265c"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.1.1"
    |   path_provider_windows:
    |     dependency: transitive
    |     description:
    |       name: path_provider_windows
    |       sha256: "8bc9f22eee8690981c22aa7fc602f5c85b497a6fb2ceb35ee5a5e5ed85ad8170"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.2.1"
    |   permission_handler:
    |     dependency: "direct main"
    |     description:
    |       path: permission_handler
    |       ref: "1e7c80f"
    |       resolved-ref: "1e7c80f0720aff742d9f1e08017e1753bcad76ef"
    |       url: "https://gitee.com/openharmony-sig/flutter_permission_handler.git"
    |     source: git
    |     version: "10.4.3"
    |   permission_handler_android:
    |     dependency: transitive
    |     description:
    |       name: permission_handler_android
    |       sha256: "59c6322171c29df93a22d150ad95f3aa19ed86542eaec409ab2691b8f35f9a47"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "10.3.6"
    |   permission_handler_apple:
    |     dependency: transitive
    |     description:
    |       name: permission_handler_apple
    |       sha256: "99e220bce3f8877c78e4ace901082fb29fa1b4ebde529ad0932d8d664b34f3f5"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "9.1.4"
    |   permission_handler_ohos:
    |     dependency: transitive
    |     description:
    |       path: permission_handler_ohos
    |       ref: master
    |       resolved-ref: f139dc532352dcecb8e9132a910e4aa607f5d9c9
    |       url: "https://gitee.com/openharmony-sig/flutter_permission_handler.git"
    |     source: git
    |     version: "10.3.2"
    |   permission_handler_platform_interface:
    |     dependency: transitive
    |     description:
    |       name: permission_handler_platform_interface
    |       sha256: "6760eb5ef34589224771010805bea6054ad28453906936f843a8cc4d3a55c4a4"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "3.12.0"
    |   permission_handler_windows:
    |     dependency: transitive
    |     description:
    |       name: permission_handler_windows
    |       sha256: cc074aace208760f1eee6aa4fae766b45d947df85bc831cde77009cdb4720098
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "0.1.3"
    |   petitparser:
    |     dependency: transitive
    |     description:
    |       name: petitparser
    |       sha256: "49392a45ced973e8d94a85fdb21293fbb40ba805fc49f2965101ae748a3683b4"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "5.1.0"
    |   photo_view:
    |     dependency: "direct main"
    |     description:
    |       name: photo_view
    |       sha256: "1fc3d970a91295fbd1364296575f854c9863f225505c28c46e0a03e48960c75e"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "0.15.0"
    |   platform:
    |     dependency: transitive
    |     description:
    |       name: platform
    |       sha256: "0a279f0707af40c890e80b1e9df8bb761694c074ba7e1d4ab1bc4b728e200b59"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "3.1.3"
    |   plugin_platform_interface:
    |     dependency: transitive
    |     description:
    |       name: plugin_platform_interface
    |       sha256: da3fdfeccc4d4ff2da8f8c556704c08f912542c5fb3cf2233ed75372384a034d
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.1.6"
    |   pointycastle:
    |     dependency: transitive
    |     description:
    |       name: pointycastle
    |       sha256: "7c1e5f0d23c9016c5bbd8b1473d0d3fb3fc851b876046039509e18e0c7485f2c"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "3.7.3"
    |   pool:
    |     dependency: transitive
    |     description:
    |       name: pool
    |       sha256: "20fe868b6314b322ea036ba325e6fc0711a22948856475e2c2b6306e8ab39c2a"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.5.1"
    |   popover:
    |     dependency: "direct main"
    |     description:
    |       name: popover
    |       sha256: "59f4a55ebb484d012c8aaa273ad58eee571945231b71fb938c5a69f63b5a94d4"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "0.2.8+2"
    |   pub_semver:
    |     dependency: transitive
    |     description:
    |       name: pub_semver
    |       sha256: "40d3ab1bbd474c4c2328c91e3a7df8c6dd629b79ece4c4bd04bee496a224fb0c"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.1.4"
    |   pubspec_parse:
    |     dependency: transitive
    |     description:
    |       name: pubspec_parse
    |       sha256: c63b2876e58e194e4b0828fcb080ad0e06d051cb607a6be51a9e084f47cb9367
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.2.3"
    |   pull_to_refresh:
    |     dependency: "direct main"
    |     description:
    |       name: pull_to_refresh
    |       sha256: bbadd5a931837b57739cf08736bea63167e284e71fb23b218c8c9a6e042aad12
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.0.0"
    |   qr:
    |     dependency: transitive
    |     description:
    |       name: qr
    |       sha256: "64957a3930367bf97cc211a5af99551d630f2f4625e38af10edd6b19131b64b3"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "3.0.1"
    |   qr_flutter:
    |     dependency: "direct main"
    |     description:
    |       name: qr_flutter
    |       sha256: "5095f0fc6e3f71d08adef8feccc8cea4f12eec18a2e31c2e8d82cb6019f4b097"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "4.1.0"
    |   quiver:
    |     dependency: "direct main"
    |     description:
    |       name: quiver
    |       sha256: ea0b925899e64ecdfbf9c7becb60d5b50e706ade44a85b2363be2a22d88117d2
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "3.2.2"
    |   retrofit:
    |     dependency: "direct main"
    |     description:
    |       name: retrofit
    |       sha256: c6cc9ad3374e6d07008343140a67afffaaa34cdf6bf08d4847d91417a99dcf45
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "4.4.2"
    |   retrofit_generator:
    |     dependency: "direct dev"
    |     description:
    |       name: retrofit_generator
    |       sha256: "9499eb46b3657a62192ddbc208ff7e6c6b768b19e83c1ee6f6b119c864b99690"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "7.0.8"
    |   rxdart:
    |     dependency: transitive
    |     description:
    |       name: rxdart
    |       sha256: "0c7c0cedd93788d996e33041ffecda924cc54389199cde4e6a34b440f50044cb"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "0.27.7"
    |   scrollable_positioned_list:
    |     dependency: transitive
    |     description:
    |       name: scrollable_positioned_list
    |       sha256: "1b54d5f1329a1e263269abc9e2543d90806131aa14fe7c6062a8054d57249287"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "0.3.8"
    |   shared_preferences:
    |     dependency: "direct main"
    |     description:
    |       path: "packages/shared_preferences/shared_preferences"
    |       ref: "770bd6e"
    |       resolved-ref: "770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc"
    |       url: "https://gitee.com/openharmony-sig/flutter_packages.git"
    |     source: git
    |     version: "2.2.0"
    |   shared_preferences_android:
    |     dependency: transitive
    |     description:
    |       name: shared_preferences_android
    |       sha256: "8568a389334b6e83415b6aae55378e158fbc2314e074983362d20c562780fb06"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.2.1"
    |   shared_preferences_foundation:
    |     dependency: transitive
    |     description:
    |       name: shared_preferences_foundation
    |       sha256: "7bf53a9f2d007329ee6f3df7268fd498f8373602f943c975598bbb34649b62a7"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.3.4"
    |   shared_preferences_linux:
    |     dependency: transitive
    |     description:
    |       name: shared_preferences_linux
    |       sha256: "9f2cbcf46d4270ea8be39fa156d86379077c8a5228d9dfdb1164ae0bb93f1faa"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.3.2"
    |   shared_preferences_ohos:
    |     dependency: transitive
    |     description:
    |       path: "packages/shared_preferences/shared_preferences_ohos"
    |       ref: HEAD
    |       resolved-ref: "056bced4b677ed4f168b245823570c40fe55bddb"
    |       url: "https://gitee.com/openharmony-sig/flutter_packages.git"
    |     source: git
    |     version: "2.2.0"
    |   shared_preferences_platform_interface:
    |     dependency: transitive
    |     description:
    |       name: shared_preferences_platform_interface
    |       sha256: d4ec5fc9ebb2f2e056c617112aa75dcf92fc2e4faaf2ae999caa297473f75d8a
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.3.1"
    |   shared_preferences_web:
    |     dependency: transitive
    |     description:
    |       name: shared_preferences_web
    |       sha256: d762709c2bbe80626ecc819143013cc820fa49ca5e363620ee20a8b15a3e3daf
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.2.1"
    |   shared_preferences_windows:
    |     dependency: transitive
    |     description:
    |       name: shared_preferences_windows
    |       sha256: "841ad54f3c8381c480d0c9b508b89a34036f512482c407e6df7a9c4aa2ef8f59"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.3.2"
    |   shelf:
    |     dependency: transitive
    |     description:
    |       name: shelf
    |       sha256: ad29c505aee705f41a4d8963641f91ac4cee3c8fad5947e033390a7bd8180fa4
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.4.1"
    |   shelf_web_socket:
    |     dependency: transitive
    |     description:
    |       name: shelf_web_socket
    |       sha256: "9ca081be41c60190ebcb4766b2486a7d50261db7bd0f5d9615f2d653637a84c1"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.0.4"
    |   sky_engine:
    |     dependency: transitive
    |     description: flutter
    |     source: sdk
    |     version: "0.0.99"
    |   source_gen:
    |     dependency: transitive
    |     description:
    |       name: source_gen
    |       sha256: "373f96cf5a8744bc9816c1ff41cf5391bbdbe3d7a96fe98c622b6738a8a7bd33"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.3.2"
    |   source_helper:
    |     dependency: transitive
    |     description:
    |       name: source_helper
    |       sha256: "6adebc0006c37dd63fe05bca0a929b99f06402fc95aa35bf36d67f5c06de01fd"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.3.4"
    |   source_span:
    |     dependency: transitive
    |     description:
    |       name: source_span
    |       sha256: dd904f795d4b4f3b870833847c461801f6750a9fa8e61ea5ac53f9422b31f250
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.9.1"
    |   sprintf:
    |     dependency: transitive
    |     description:
    |       name: sprintf
    |       sha256: "1fc9ffe69d4df602376b52949af107d8f5703b77cda567c4d7d86a0693120f23"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "7.0.0"
    |   sqflite:
    |     dependency: transitive
    |     description:
    |       name: sqflite
    |       sha256: b4d6710e1200e96845747e37338ea8a819a12b51689a3bcf31eff0003b37a0b9
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.2.8+4"
    |   sqflite_common:
    |     dependency: transitive
    |     description:
    |       name: sqflite_common
    |       sha256: "8f7603f3f8f126740bc55c4ca2d1027aab4b74a1267a3e31ce51fe40e3b65b8f"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.4.5+1"
    |   ssl_checker:
    |     dependency: "direct main"
    |     description:
    |       name: ssl_checker
    |       sha256: "647f4cd8c2b1623a28d77d70f424b06b623bc38d66d1fa4af97ad258493aff6d"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.0.0"
    |   stack_trace:
    |     dependency: transitive
    |     description:
    |       name: stack_trace
    |       sha256: c3c7d8edb15bee7f0f74debd4b9c5f3c2ea86766fe4178eb2a18eb30a0bdaed5
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.11.0"
    |   stream_channel:
    |     dependency: transitive
    |     description:
    |       name: stream_channel
    |       sha256: "83615bee9045c1d322bbbd1ba209b7a749c2cbcdcb3fdd1df8eb488b3279c1c8"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.1.1"
    |   stream_transform:
    |     dependency: transitive
    |     description:
    |       name: stream_transform
    |       sha256: "14a00e794c7c11aa145a170587321aedce29769c08d7f58b1d141da75e3b1c6f"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.1.0"
    |   string_scanner:
    |     dependency: transitive
    |     description:
    |       name: string_scanner
    |       sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.2.0"
    |   synchronized:
    |     dependency: transitive
    |     description:
    |       name: synchronized
    |       sha256: "5fcbd27688af6082f5abd611af56ee575342c30e87541d0245f7ff99faa02c60"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "3.1.0"
    |   term_glyph:
    |     dependency: transitive
    |     description:
    |       name: term_glyph
    |       sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.2.1"
    |   test_api:
    |     dependency: transitive
    |     description:
    |       name: test_api
    |       sha256: ad540f65f92caa91bf21dfc8ffb8c589d6e4dc0c2267818b4cc2792857706206
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "0.4.16"
    |   timing:
    |     dependency: transitive
    |     description:
    |       name: timing
    |       sha256: "70a3b636575d4163c477e6de42f247a23b315ae20e86442bebe32d3cabf61c32"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.0.1"
    |   tuple:
    |     dependency: transitive
    |     description:
    |       name: tuple
    |       sha256: a97ce2013f240b2f3807bcbaf218765b6f301c3eff91092bcfa23a039e7dd151
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.0.2"
    |   typed_data:
    |     dependency: transitive
    |     description:
    |       name: typed_data
    |       sha256: facc8d6582f16042dd49f2463ff1bd6e2c9ef9f3d5da3d9b087e244a7b564b3c
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.3.2"
    |   url_launcher:
    |     dependency: "direct main"
    |     description:
    |       path: "packages/url_launcher/url_launcher"
    |       ref: "770bd6e"
    |       resolved-ref: "770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc"
    |       url: "https://gitee.com/openharmony-sig/flutter_packages.git"
    |     source: git
    |     version: "6.1.12"
    |   url_launcher_android:
    |     dependency: transitive
    |     description:
    |       name: url_launcher_android
    |       sha256: "31222ffb0063171b526d3e569079cf1f8b294075ba323443fdc690842bfd4def"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "6.2.0"
    |   url_launcher_ios:
    |     dependency: transitive
    |     description:
    |       name: url_launcher_ios
    |       sha256: "4ac97281cf60e2e8c5cc703b2b28528f9b50c8f7cebc71df6bdf0845f647268a"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "6.2.0"
    |   url_launcher_linux:
    |     dependency: transitive
    |     description:
    |       name: url_launcher_linux
    |       sha256: "9f2d390e096fdbe1e6e6256f97851e51afc2d9c423d3432f1d6a02a8a9a8b9fd"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "3.1.0"
    |   url_launcher_macos:
    |     dependency: transitive
    |     description:
    |       name: url_launcher_macos
    |       sha256: b7244901ea3cf489c5335bdacda07264a6e960b1c1b1a9f91e4bc371d9e68234
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "3.1.0"
    |   url_launcher_ohos:
    |     dependency: transitive
    |     description:
    |       path: "packages/url_launcher/url_launcher_ohos"
    |       ref: HEAD
    |       resolved-ref: "056bced4b677ed4f168b245823570c40fe55bddb"
    |       url: "https://gitee.com/openharmony-sig/flutter_packages.git"
    |     source: git
    |     version: "6.0.38"
    |   url_launcher_platform_interface:
    |     dependency: transitive
    |     description:
    |       name: url_launcher_platform_interface
    |       sha256: "980e8d9af422f477be6948bdfb68df8433be71f5743a188968b0c1b887807e50"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.2.0"
    |   url_launcher_web:
    |     dependency: transitive
    |     description:
    |       name: url_launcher_web
    |       sha256: ba140138558fcc3eead51a1c42e92a9fb074a1b1149ed3c73e66035b2ccd94f2
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.0.19"
    |   url_launcher_windows:
    |     dependency: transitive
    |     description:
    |       name: url_launcher_windows
    |       sha256: "7754a1ad30ee896b265f8d14078b0513a4dba28d358eabb9d5f339886f4a1adc"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "3.1.0"
    |   uuid:
    |     dependency: "direct main"
    |     description:
    |       name: uuid
    |       sha256: b715b8d3858b6fa9f68f87d20d98830283628014750c2b09b6f516c1da4af2a7
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "4.1.0"
    |   vector_graphics:
    |     dependency: transitive
    |     description:
    |       name: vector_graphics
    |       sha256: ea8d3fc7b2e0f35de38a7465063ecfcf03d8217f7962aa2a6717132cb5d43a79
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.1.5"
    |   vector_graphics_codec:
    |     dependency: transitive
    |     description:
    |       name: vector_graphics_codec
    |       sha256: a5eaa5d19e123ad4f61c3718ca1ed921c4e6254238d9145f82aa214955d9aced
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.1.5"
    |   vector_graphics_compiler:
    |     dependency: transitive
    |     description:
    |       name: vector_graphics_compiler
    |       sha256: "15edc42f7eaa478ce854eaf1fbb9062a899c0e4e56e775dd73b7f4709c97c4ca"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.1.5"
    |   vector_math:
    |     dependency: transitive
    |     description:
    |       name: vector_math
    |       sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.1.4"
    |   watcher:
    |     dependency: transitive
    |     description:
    |       name: watcher
    |       sha256: "6a7f46926b01ce81bfc339da6a7f20afbe7733eff9846f6d6a5466aa4c6667c0"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.0.2"
    |   web_socket_channel:
    |     dependency: transitive
    |     description:
    |       name: web_socket_channel
    |       sha256: d88238e5eac9a42bb43ca4e721edba3c08c6354d4a53063afaa568516217621b
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "2.4.0"
    |   webview_flutter:
    |     dependency: "direct main"
    |     description:
    |       path: "packages/webview_flutter/webview_flutter"
    |       ref: c030f4f
    |       resolved-ref: c030f4f22899ee7c436687008c673a6218b6cdf3
    |       url: "https://gitee.com/openharmony-sig/flutter_packages.git"
    |     source: git
    |     version: "4.6.0"
    |   webview_flutter_android:
    |     dependency: transitive
    |     description:
    |       path: "packages/webview_flutter/webview_flutter_android"
    |       ref: HEAD
    |       resolved-ref: "056bced4b677ed4f168b245823570c40fe55bddb"
    |       url: "https://gitee.com/openharmony-sig/flutter_packages.git"
    |     source: git
    |     version: "3.15.0"
    |   webview_flutter_ohos:
    |     dependency: transitive
    |     description:
    |       path: "packages/webview_flutter/webview_flutter_ohos"
    |       ref: HEAD
    |       resolved-ref: "056bced4b677ed4f168b245823570c40fe55bddb"
    |       url: "https://gitee.com/openharmony-sig/flutter_packages.git"
    |     source: git
    |     version: "3.15.0"
    |   webview_flutter_platform_interface:
    |     dependency: transitive
    |     description:
    |       path: "packages/webview_flutter/webview_flutter_platform_interface"
    |       ref: HEAD
    |       resolved-ref: "056bced4b677ed4f168b245823570c40fe55bddb"
    |       url: "https://gitee.com/openharmony-sig/flutter_packages.git"
    |     source: git
    |     version: "2.10.0"
    |   webview_flutter_wkwebview:
    |     dependency: transitive
    |     description:
    |       path: "packages/webview_flutter/webview_flutter_wkwebview"
    |       ref: HEAD
    |       resolved-ref: "056bced4b677ed4f168b245823570c40fe55bddb"
    |       url: "https://gitee.com/openharmony-sig/flutter_packages.git"
    |     source: git
    |     version: "3.12.0"
    |   win32:
    |     dependency: transitive
    |     description:
    |       name: win32
    |       sha256: "5a751eddf9db89b3e5f9d50c20ab8612296e4e8db69009788d6c8b060a84191c"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "4.1.4"
    |   win32_registry:
    |     dependency: transitive
    |     description:
    |       name: win32_registry
    |       sha256: "1c52f994bdccb77103a6231ad4ea331a244dbcef5d1f37d8462f713143b0bfae"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.1.0"
    |   wuling_flutter_cashier:
    |     dependency: "direct main"
    |     description:
    |       path: "."
    |       ref: b63021fe6a6d5e988a11174579669ac7b38cde60
    |       resolved-ref: b63021fe6a6d5e988a11174579669ac7b38cde60
    |       url: "https://gitee.com/saic_general_wuling/wuling-flutter-cashier.git"
    |     source: git
    |     version: "1.0.0+1"
    |   xdg_directories:
    |     dependency: transitive
    |     description:
    |       name: xdg_directories
    |       sha256: "589ada45ba9e39405c198fe34eb0f607cddb2108527e658136120892beac46d2"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "1.0.3"
    |   xml:
    |     dependency: transitive
    |     description:
    |       name: xml
    |       sha256: "979ee37d622dec6365e2efa4d906c37470995871fe9ae080d967e192d88286b5"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "6.2.2"
    |   yaml:
    |     dependency: transitive
    |     description:
    |       name: yaml
    |       sha256: "75769501ea3489fca56601ff33454fe45507ea3bfb014161abc3b43ae25989d5"
    |       url: "https://pub.flutter-io.cn"
    |     source: hosted
    |     version: "3.1.2"
    | sdks:
    |   dart: ">=2.19.6 <4.0.0"
    |   flutter: ">=3.7.0"
IO  : Writing 36014 characters to text file .dart_tool\package_config.json.
FINE: Contents:
    | {
    |   "configVersion": 2,
    |   "packages": [
    |     {
    |       "name": "_fe_analyzer_shared",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/_fe_analyzer_shared-61.0.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "analyzer",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/analyzer-5.13.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "archive",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/archive-3.4.10",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "args",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/args-2.4.2",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "asn1lib",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/asn1lib-1.4.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "async",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/async-2.10.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "badges",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/badges-3.1.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "boolean_selector",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/boolean_selector-2.1.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "build",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/build-2.3.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "build_config",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/build_config-1.1.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.14"
    |     },
    |     {
    |       "name": "build_daemon",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/build_daemon-3.1.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.14"
    |     },
    |     {
    |       "name": "build_resolvers",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/build_resolvers-2.4.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "build_runner",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/build_runner-2.3.3",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.14"
    |     },
    |     {
    |       "name": "build_runner_core",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/build_runner_core-7.2.7+1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "built_collection",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/built_collection-5.1.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "built_value",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/built_value-8.8.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "cached_network_image",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/cached_network_image-3.2.3",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "cached_network_image_platform_interface",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/cached_network_image_platform_interface-2.0.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "cached_network_image_web",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/cached_network_image_web-1.0.2",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "camera_ohos",
    |       "rootUri": "../plugins/camera_ohos",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "camera_platform_interface",
    |       "rootUri": "../plugins/camera_platform_interface",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "carousel_slider",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/carousel_slider-4.2.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "characters",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/characters-1.2.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "checked_yaml",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/checked_yaml-2.0.3",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "city_pickers",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/city_pickers-1.3.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "clock",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/clock-1.1.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "code_builder",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/code_builder-4.7.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "collection",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/collection-1.17.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "connectivity_plus",
    |       "rootUri": "file:///D:/PUB/git/flutter_plus_plugins-ff774d947bd15d9be08629e3ccfb29dda2e864bd/packages/connectivity_plus/connectivity_plus",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "connectivity_plus_platform_interface",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/connectivity_plus_platform_interface-1.2.4",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "convert",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/convert-3.1.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "cross_file",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/cross_file-0.3.3+6",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "crypto",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/crypto-3.0.3",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "cupertino_icons",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/cupertino_icons-1.0.6",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "dart_style",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/dart_style-2.3.2",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "dbus",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/dbus-0.7.11",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "device_info_plus",
    |       "rootUri": "file:///D:/PUB/git/flutter_plus_plugins-a1347adcca3a46346a6ddd127cebcec9970cad6c/packages/device_info_plus/device_info_plus",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "device_info_plus_platform_interface",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/device_info_plus_platform_interface-7.0.2",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "dio",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/dio-5.8.0+1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "dio_web_adapter",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/dio_web_adapter-1.1.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "encrypt",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/encrypt-5.0.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "fading_edge_scrollview",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/fading_edge_scrollview-3.0.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "fake_async",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/fake_async-1.3.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "ffi",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/ffi-2.0.2",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "file",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/file-6.1.4",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "fixnum",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/fixnum-1.1.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "flutter",
    |       "rootUri": "file:///D:/flutter_flutter/packages/flutter",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "flutter_aliplayer",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/flutter_aliplayer-5.5.6",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "flutter_blue_plus",
    |       "rootUri": "file:///D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.15"
    |     },
    |     {
    |       "name": "flutter_blurhash",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/flutter_blurhash-0.7.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.15"
    |     },
    |     {
    |       "name": "flutter_cache_manager",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/flutter_cache_manager-3.3.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "flutter_easyloading",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/flutter_easyloading-3.0.5",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "flutter_lints",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/flutter_lints-2.0.3",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "flutter_pickers",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/flutter_pickers-2.1.9",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "flutter_platform_utils",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/flutter_platform_utils-1.0.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "flutter_plugin_android_lifecycle",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/flutter_plugin_android_lifecycle-2.0.17",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "flutter_refresh",
    |       "rootUri": "file:///D:/PUB/git/flutter_refresh_null_safe-0e07678e85497619f1f0b88f1585e313b26dc717/",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "flutter_slidable",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/flutter_slidable-2.0.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "flutter_spinkit",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/flutter_spinkit-5.2.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "flutter_staggered_grid_view",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/flutter_staggered_grid_view-0.7.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "flutter_svg",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/flutter_svg-2.0.5",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "flutter_test",
    |       "rootUri": "file:///D:/flutter_flutter/packages/flutter_test",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "flutter_uikit",
    |       "rootUri": "file:///D:/PUB/git/flutter_uikit-f6aae98c19d47c510abe52a34f11da967f646924/",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "flutter_web_plugins",
    |       "rootUri": "file:///D:/flutter_flutter/packages/flutter_web_plugins",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "flutter_xlider",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/flutter_xlider-3.5.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "fluwx",
    |       "rootUri": "file:///D:/PUB/git/fluwx-9fe4d9f1d3c05de5de18d25cc53852866d9c34d7/",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "frontend_server_client",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/frontend_server_client-3.2.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "glob",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/glob-2.1.2",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "graphs",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/graphs-2.3.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "http",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/http-0.13.6",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "http_multi_server",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/http_multi_server-3.2.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "http_parser",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/http_parser-4.0.2",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "iamgeqr_flutter_plugin",
    |       "rootUri": "file:///D:/PUB/git/flutter_image_scanner-b9f20a88ca1c738c700a15655b8fab4c305c4d32/",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "image",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/image-4.2.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.15"
    |     },
    |     {
    |       "name": "image_picker_ohos",
    |       "rootUri": "file:///D:/PUB/git/flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb/packages/image_picker/image_picker_ohos",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "image_picker_platform_interface",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/image_picker_platform_interface-2.9.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "intl",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/intl-0.18.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "io",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/io-1.0.4",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "js",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/js-0.6.5",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.16"
    |     },
    |     {
    |       "name": "json_annotation",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/json_annotation-4.8.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "json_serializable",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/json_serializable-6.6.2",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "json_to_model",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/json_to_model-3.2.4",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "lints",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/lints-2.0.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "logger",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/logger-2.6.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "logging",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/logging-1.2.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "lottie",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/lottie-2.3.2",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "lpinyin",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/lpinyin-2.0.3",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "marquee",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/marquee-2.2.3",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "matcher",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/matcher-0.12.13",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "material_color_utilities",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/material_color_utilities-0.2.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.13"
    |     },
    |     {
    |       "name": "meta",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/meta-1.8.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "mime",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/mime-1.0.4",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "mobile_scanner",
    |       "rootUri": "file:///D:/PUB/git/fluttertpc_mobile_scanner-8d353dca7678d28dbbec24e3566e10823ddccf37/",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "nm",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/nm-0.5.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "octo_image",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/octo_image-1.0.2",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "open_app_settings",
    |       "rootUri": "file:///D:/PUB/git/fluttertpc_open_app_settings-038c0dd8ffd45bcf84691d62cfff957748cd493a/",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "package_config",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/package_config-2.1.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "package_info_plus",
    |       "rootUri": "file:///D:/PUB/git/flutter_plus_plugins-452bfb40465722552f31cf78cde60f99cc691e36/packages/package_info_plus/package_info_plus",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "package_info_plus_platform_interface",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/package_info_plus_platform_interface-2.0.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "path",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/path-1.8.2",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "path_parsing",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/path_parsing-1.0.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "path_provider",
    |       "rootUri": "file:///D:/PUB/git/flutter_packages-770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc/packages/path_provider/path_provider",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "path_provider_android",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/path_provider_android-2.2.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "path_provider_foundation",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/path_provider_foundation-2.3.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "path_provider_linux",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/path_provider_linux-2.2.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "path_provider_ohos",
    |       "rootUri": "file:///D:/PUB/git/flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb/packages/path_provider/path_provider_ohos",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "path_provider_platform_interface",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/path_provider_platform_interface-2.1.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "path_provider_windows",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/path_provider_windows-2.2.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "permission_handler",
    |       "rootUri": "file:///D:/PUB/git/flutter_permission_handler-1e7c80f0720aff742d9f1e08017e1753bcad76ef/permission_handler",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.15"
    |     },
    |     {
    |       "name": "permission_handler_android",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/permission_handler_android-10.3.6",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.15"
    |     },
    |     {
    |       "name": "permission_handler_apple",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/permission_handler_apple-9.1.4",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.15"
    |     },
    |     {
    |       "name": "permission_handler_ohos",
    |       "rootUri": "file:///D:/PUB/git/flutter_permission_handler-f139dc532352dcecb8e9132a910e4aa607f5d9c9/permission_handler_ohos",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.15"
    |     },
    |     {
    |       "name": "permission_handler_platform_interface",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/permission_handler_platform_interface-3.12.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.14"
    |     },
    |     {
    |       "name": "permission_handler_windows",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/permission_handler_windows-0.1.3",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "petitparser",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/petitparser-5.1.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "photo_view",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/photo_view-0.15.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "platform",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/platform-3.1.3",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "plugin_platform_interface",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/plugin_platform_interface-2.1.6",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "pointycastle",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/pointycastle-3.7.3",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.14"
    |     },
    |     {
    |       "name": "pool",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/pool-1.5.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "popover",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/popover-0.2.8+2",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "pub_semver",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/pub_semver-2.1.4",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "pubspec_parse",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/pubspec_parse-1.2.3",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "pull_to_refresh",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/pull_to_refresh-2.0.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "qr",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/qr-3.0.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.16"
    |     },
    |     {
    |       "name": "qr_flutter",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/qr_flutter-4.1.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "quiver",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/quiver-3.2.2",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "retrofit",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/retrofit-4.4.2",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "retrofit_generator",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/retrofit_generator-7.0.8",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "rxdart",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/rxdart-0.27.7",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "scrollable_positioned_list",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/scrollable_positioned_list-0.3.8",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.15"
    |     },
    |     {
    |       "name": "shared_preferences",
    |       "rootUri": "file:///D:/PUB/git/flutter_packages-770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc/packages/shared_preferences/shared_preferences",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "shared_preferences_android",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/shared_preferences_android-2.2.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "shared_preferences_foundation",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.3.4",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "shared_preferences_linux",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/shared_preferences_linux-2.3.2",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "shared_preferences_ohos",
    |       "rootUri": "file:///D:/PUB/git/flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb/packages/shared_preferences/shared_preferences_ohos",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "shared_preferences_platform_interface",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/shared_preferences_platform_interface-2.3.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "shared_preferences_web",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/shared_preferences_web-2.2.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "shared_preferences_windows",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/shared_preferences_windows-2.3.2",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "shelf",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/shelf-1.4.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "shelf_web_socket",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/shelf_web_socket-1.0.4",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "sky_engine",
    |       "rootUri": "file:///D:/flutter_flutter/bin/cache/pkg/sky_engine",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "source_gen",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/source_gen-1.3.2",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "source_helper",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/source_helper-1.3.4",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "source_span",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/source_span-1.9.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.14"
    |     },
    |     {
    |       "name": "sprintf",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/sprintf-7.0.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "sqflite",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/sqflite-2.2.8+4",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "sqflite_common",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/sqflite_common-2.4.5+1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "ssl_checker",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/ssl_checker-1.0.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "stack_trace",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/stack_trace-1.11.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "stream_channel",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/stream_channel-2.1.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.14"
    |     },
    |     {
    |       "name": "stream_transform",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/stream_transform-2.1.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.14"
    |     },
    |     {
    |       "name": "string_scanner",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/string_scanner-1.2.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "synchronized",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/synchronized-3.1.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "term_glyph",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/term_glyph-1.2.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.12"
    |     },
    |     {
    |       "name": "test_api",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/test_api-0.4.16",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "timing",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/timing-1.0.1",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.14"
    |     },
    |     {
    |       "name": "tuple",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/tuple-2.0.2",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "typed_data",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/typed_data-1.3.2",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "url_launcher",
    |       "rootUri": "file:///D:/PUB/git/flutter_packages-770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc/packages/url_launcher/url_launcher",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "url_launcher_android",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/url_launcher_android-6.2.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "url_launcher_ios",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/url_launcher_ios-6.2.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "url_launcher_linux",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/url_launcher_linux-3.1.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "url_launcher_macos",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/url_launcher_macos-3.1.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "url_launcher_ohos",
    |       "rootUri": "file:///D:/PUB/git/flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb/packages/url_launcher/url_launcher_ohos",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "url_launcher_platform_interface",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/url_launcher_platform_interface-2.2.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "url_launcher_web",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/url_launcher_web-2.0.19",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "url_launcher_windows",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/url_launcher_windows-3.1.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "uuid",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/uuid-4.1.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.14"
    |     },
    |     {
    |       "name": "vector_graphics",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/vector_graphics-1.1.5",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "vector_graphics_codec",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/vector_graphics_codec-1.1.5",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "vector_graphics_compiler",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/vector_graphics_compiler-1.1.5",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "vector_math",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/vector_math-2.1.4",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.14"
    |     },
    |     {
    |       "name": "watcher",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/watcher-1.0.2",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.14"
    |     },
    |     {
    |       "name": "web_socket_channel",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/web_socket_channel-2.4.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.15"
    |     },
    |     {
    |       "name": "webview_flutter",
    |       "rootUri": "file:///D:/PUB/git/flutter_packages-c030f4f22899ee7c436687008c673a6218b6cdf3/packages/webview_flutter/webview_flutter",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "webview_flutter_android",
    |       "rootUri": "file:///D:/PUB/git/flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb/packages/webview_flutter/webview_flutter_android",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "webview_flutter_ohos",
    |       "rootUri": "file:///D:/PUB/git/flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb/packages/webview_flutter/webview_flutter_ohos",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "webview_flutter_platform_interface",
    |       "rootUri": "file:///D:/PUB/git/flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb/packages/webview_flutter/webview_flutter_platform_interface",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "webview_flutter_wkwebview",
    |       "rootUri": "file:///D:/PUB/git/flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb/packages/webview_flutter/webview_flutter_wkwebview",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "win32",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/win32-4.1.4",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "win32_registry",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/win32_registry-1.1.0",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.17"
    |     },
    |     {
    |       "name": "wuling_flutter_cashier",
    |       "rootUri": "file:///D:/PUB/git/wuling-flutter-cashier-b63021fe6a6d5e988a11174579669ac7b38cde60/",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "xdg_directories",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/xdg_directories-1.0.3",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "xml",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/xml-6.2.2",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.18"
    |     },
    |     {
    |       "name": "yaml",
    |       "rootUri": "file:///D:/PUB/hosted/pub.flutter-io.cn/yaml-3.1.2",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     },
    |     {
    |       "name": "wuling_flutter_app",
    |       "rootUri": "../",
    |       "packageUri": "lib/",
    |       "languageVersion": "2.19"
    |     }
    |   ],
    |   "generated": "2025-07-15T08:35:00.509800Z",
    |   "generator": "pub",
    |   "generatorVersion": "2.19.6"
    | }
IO  : Writing 2746294 characters to text file D:/PUB\log\pub_log.txt.
MSG : Logs written to D:/PUB\log\pub_log.txt.
[+23938 ms] Found plugin flutter_blue_plus at D:\PUB\git\flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0\
[  +12 ms] Found plugin iamgeqr_flutter_plugin at D:\PUB\git\flutter_image_scanner-b9f20a88ca1c738c700a15655b8fab4c305c4d32\
[   +3 ms] Found plugin image_picker_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\image_picker\image_picker_ohos\
[   +3 ms] Found plugin path_provider_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\path_provider\path_provider_ohos\
[   +2 ms] Found plugin shared_preferences_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\shared_preferences\shared_preferences_ohos\
[   +2 ms] Found plugin url_launcher_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\url_launcher\url_launcher_ohos\
[   +7 ms] Found plugin webview_flutter_android at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\webview_flutter\webview_flutter_android\
[   +3 ms] Found plugin webview_flutter_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\webview_flutter\webview_flutter_ohos\
[   +6 ms] Found plugin webview_flutter_wkwebview at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\webview_flutter\webview_flutter_wkwebview\
[   +2 ms] Found plugin path_provider at D:\PUB\git\flutter_packages-770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc\packages\path_provider\path_provider\
[   +2 ms] Found plugin shared_preferences at D:\PUB\git\flutter_packages-770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc\packages\shared_preferences\shared_preferences\
[   +3 ms] Found plugin url_launcher at D:\PUB\git\flutter_packages-770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc\packages\url_launcher\url_launcher\
[   +4 ms] Found plugin webview_flutter at D:\PUB\git\flutter_packages-c030f4f22899ee7c436687008c673a6218b6cdf3\packages\webview_flutter\webview_flutter\
[   +3 ms] Found plugin permission_handler at D:\PUB\git\flutter_permission_handler-1e7c80f0720aff742d9f1e08017e1753bcad76ef\permission_handler\
[   +4 ms] Found plugin permission_handler_ohos at D:\PUB\git\flutter_permission_handler-f139dc532352dcecb8e9132a910e4aa607f5d9c9\permission_handler_ohos\
[   +4 ms] Found plugin package_info_plus at D:\PUB\git\flutter_plus_plugins-452bfb40465722552f31cf78cde60f99cc691e36\packages\package_info_plus\package_info_plus\
[   +7 ms] Found plugin device_info_plus at D:\PUB\git\flutter_plus_plugins-a1347adcca3a46346a6ddd127cebcec9970cad6c\packages\device_info_plus\device_info_plus\
[   +3 ms] Found plugin connectivity_plus at D:\PUB\git\flutter_plus_plugins-ff774d947bd15d9be08629e3ccfb29dda2e864bd\packages\connectivity_plus\connectivity_plus\
[   +9 ms] Found plugin mobile_scanner at D:\PUB\git\fluttertpc_mobile_scanner-8d353dca7678d28dbbec24e3566e10823ddccf37\
[   +2 ms] Found plugin open_app_settings at D:\PUB\git\fluttertpc_open_app_settings-038c0dd8ffd45bcf84691d62cfff957748cd493a\
[   +3 ms] Found plugin fluwx at D:\PUB\git\fluwx-9fe4d9f1d3c05de5de18d25cc53852866d9c34d7\
[  +73 ms] Found plugin flutter_aliplayer at D:\PUB\hosted\pub.flutter-io.cn\flutter_aliplayer-5.5.6\
[   +9 ms] Found plugin flutter_plugin_android_lifecycle at D:\PUB\hosted\pub.flutter-io.cn\flutter_plugin_android_lifecycle-2.0.17\
[  +38 ms] Found plugin path_provider_android at D:\PUB\hosted\pub.flutter-io.cn\path_provider_android-2.2.1\
[   +3 ms] Found plugin path_provider_foundation at D:\PUB\hosted\pub.flutter-io.cn\path_provider_foundation-2.3.1\
[   +1 ms] Found plugin path_provider_linux at D:\PUB\hosted\pub.flutter-io.cn\path_provider_linux-2.2.1\
[   +3 ms] Found plugin path_provider_windows at D:\PUB\hosted\pub.flutter-io.cn\path_provider_windows-2.2.1\
[   +2 ms] Found plugin permission_handler_android at D:\PUB\hosted\pub.flutter-io.cn\permission_handler_android-10.3.6\
[   +1 ms] Found plugin permission_handler_apple at D:\PUB\hosted\pub.flutter-io.cn\permission_handler_apple-9.1.4\
[   +2 ms] Found plugin permission_handler_windows at D:\PUB\hosted\pub.flutter-io.cn\permission_handler_windows-0.1.3\
[  +16 ms] Found plugin shared_preferences_android at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_android-2.2.1\
[   +1 ms] Found plugin shared_preferences_foundation at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_foundation-2.3.4\
[   +1 ms] Found plugin shared_preferences_linux at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_linux-2.3.2\
[   +2 ms] Found plugin shared_preferences_web at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_web-2.2.1\
[   +1 ms] Found plugin shared_preferences_windows at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_windows-2.3.2\
[  +10 ms] Found plugin sqflite at D:\PUB\hosted\pub.flutter-io.cn\sqflite-2.2.8+4\
[  +14 ms] Found plugin url_launcher_android at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_android-6.2.0\
[   +1 ms] Found plugin url_launcher_ios at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_ios-6.2.0\
[   +1 ms] Found plugin url_launcher_linux at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_linux-3.1.0\
[   +1 ms] Found plugin url_launcher_macos at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_macos-3.1.0\
[   +4 ms] Found plugin url_launcher_web at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_web-2.0.19\
[   +2 ms] Found plugin url_launcher_windows at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_windows-3.1.0\
[  +20 ms] Found plugin camera_ohos at D:\wulingFlutter\wuling-flutter-app\plugins\camera_ohos\
[  +53 ms] Found plugin flutter_blue_plus at D:\PUB\git\flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0\
[   +2 ms] Found plugin iamgeqr_flutter_plugin at D:\PUB\git\flutter_image_scanner-b9f20a88ca1c738c700a15655b8fab4c305c4d32\
[   +3 ms] Found plugin image_picker_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\image_picker\image_picker_ohos\
[   +2 ms] Found plugin path_provider_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\path_provider\path_provider_ohos\
[   +1 ms] Found plugin shared_preferences_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\shared_preferences\shared_preferences_ohos\
[   +1 ms] Found plugin url_launcher_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\url_launcher\url_launcher_ohos\
[   +1 ms] Found plugin webview_flutter_android at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\webview_flutter\webview_flutter_android\
[   +1 ms] Found plugin webview_flutter_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\webview_flutter\webview_flutter_ohos\
[   +1 ms] Found plugin webview_flutter_wkwebview at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\webview_flutter\webview_flutter_wkwebview\
[   +1 ms] Found plugin path_provider at D:\PUB\git\flutter_packages-770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc\packages\path_provider\path_provider\
[   +3 ms] Found plugin shared_preferences at D:\PUB\git\flutter_packages-770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc\packages\shared_preferences\shared_preferences\
[   +2 ms] Found plugin url_launcher at D:\PUB\git\flutter_packages-770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc\packages\url_launcher\url_launcher\
[   +1 ms] Found plugin webview_flutter at D:\PUB\git\flutter_packages-c030f4f22899ee7c436687008c673a6218b6cdf3\packages\webview_flutter\webview_flutter\
[   +1 ms] Found plugin permission_handler at D:\PUB\git\flutter_permission_handler-1e7c80f0720aff742d9f1e08017e1753bcad76ef\permission_handler\
[   +1 ms] Found plugin permission_handler_ohos at D:\PUB\git\flutter_permission_handler-f139dc532352dcecb8e9132a910e4aa607f5d9c9\permission_handler_ohos\
[   +1 ms] Found plugin package_info_plus at D:\PUB\git\flutter_plus_plugins-452bfb40465722552f31cf78cde60f99cc691e36\packages\package_info_plus\package_info_plus\
[   +1 ms] Found plugin device_info_plus at D:\PUB\git\flutter_plus_plugins-a1347adcca3a46346a6ddd127cebcec9970cad6c\packages\device_info_plus\device_info_plus\
[   +1 ms] Found plugin connectivity_plus at D:\PUB\git\flutter_plus_plugins-ff774d947bd15d9be08629e3ccfb29dda2e864bd\packages\connectivity_plus\connectivity_plus\
[   +4 ms] Found plugin mobile_scanner at D:\PUB\git\fluttertpc_mobile_scanner-8d353dca7678d28dbbec24e3566e10823ddccf37\
[   +1 ms] Found plugin open_app_settings at D:\PUB\git\fluttertpc_open_app_settings-038c0dd8ffd45bcf84691d62cfff957748cd493a\
[   +1 ms] Found plugin fluwx at D:\PUB\git\fluwx-9fe4d9f1d3c05de5de18d25cc53852866d9c34d7\
[  +35 ms] Found plugin flutter_aliplayer at D:\PUB\hosted\pub.flutter-io.cn\flutter_aliplayer-5.5.6\
[   +5 ms] Found plugin flutter_plugin_android_lifecycle at D:\PUB\hosted\pub.flutter-io.cn\flutter_plugin_android_lifecycle-2.0.17\
[  +26 ms] Found plugin path_provider_android at D:\PUB\hosted\pub.flutter-io.cn\path_provider_android-2.2.1\
[   +1 ms] Found plugin path_provider_foundation at D:\PUB\hosted\pub.flutter-io.cn\path_provider_foundation-2.3.1\
[   +1 ms] Found plugin path_provider_linux at D:\PUB\hosted\pub.flutter-io.cn\path_provider_linux-2.2.1\
[   +1 ms] Found plugin path_provider_windows at D:\PUB\hosted\pub.flutter-io.cn\path_provider_windows-2.2.1\
[   +1 ms] Found plugin permission_handler_android at D:\PUB\hosted\pub.flutter-io.cn\permission_handler_android-10.3.6\
[   +3 ms] Found plugin permission_handler_apple at D:\PUB\hosted\pub.flutter-io.cn\permission_handler_apple-9.1.4\
[   +2 ms] Found plugin permission_handler_windows at D:\PUB\hosted\pub.flutter-io.cn\permission_handler_windows-0.1.3\
[  +12 ms] Found plugin shared_preferences_android at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_android-2.2.1\
[   +1 ms] Found plugin shared_preferences_foundation at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_foundation-2.3.4\
[   +1 ms] Found plugin shared_preferences_linux at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_linux-2.3.2\
[   +1 ms] Found plugin shared_preferences_web at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_web-2.2.1\
[   +1 ms] Found plugin shared_preferences_windows at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_windows-2.3.2\
[   +4 ms] Found plugin sqflite at D:\PUB\hosted\pub.flutter-io.cn\sqflite-2.2.8+4\
[  +12 ms] Found plugin url_launcher_android at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_android-6.2.0\
[   +1 ms] Found plugin url_launcher_ios at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_ios-6.2.0\
[   +1 ms] Found plugin url_launcher_linux at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_linux-3.1.0\
[   +1 ms] Found plugin url_launcher_macos at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_macos-3.1.0\
[   +1 ms] Found plugin url_launcher_web at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_web-2.0.19\
[   +1 ms] Found plugin url_launcher_windows at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_windows-3.1.0\
[  +17 ms] Found plugin camera_ohos at D:\wulingFlutter\wuling-flutter-app\plugins\camera_ohos\
[ +105 ms] Generating D:\wulingFlutter\wuling-flutter-app\android\app\src\main\java\io\flutter\plugins\GeneratedPluginRegistrant.java
[  +33 ms] Found plugin flutter_blue_plus at D:\PUB\git\flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0\
[   +1 ms] Found plugin iamgeqr_flutter_plugin at D:\PUB\git\flutter_image_scanner-b9f20a88ca1c738c700a15655b8fab4c305c4d32\
[   +2 ms] Found plugin image_picker_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\image_picker\image_picker_ohos\
[   +1 ms] Found plugin path_provider_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\path_provider\path_provider_ohos\
[   +1 ms] Found plugin shared_preferences_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\shared_preferences\shared_preferences_ohos\
[   +1 ms] Found plugin url_launcher_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\url_launcher\url_launcher_ohos\
[   +1 ms] Found plugin webview_flutter_android at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\webview_flutter\webview_flutter_android\
[   +1 ms] Found plugin webview_flutter_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\webview_flutter\webview_flutter_ohos\
[   +2 ms] Found plugin webview_flutter_wkwebview at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\webview_flutter\webview_flutter_wkwebview\
[   +1 ms] Found plugin path_provider at D:\PUB\git\flutter_packages-770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc\packages\path_provider\path_provider\
[   +3 ms] Found plugin shared_preferences at D:\PUB\git\flutter_packages-770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc\packages\shared_preferences\shared_preferences\
[   +1 ms] Found plugin url_launcher at D:\PUB\git\flutter_packages-770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc\packages\url_launcher\url_launcher\
[   +1 ms] Found plugin webview_flutter at D:\PUB\git\flutter_packages-c030f4f22899ee7c436687008c673a6218b6cdf3\packages\webview_flutter\webview_flutter\
[   +1 ms] Found plugin permission_handler at D:\PUB\git\flutter_permission_handler-1e7c80f0720aff742d9f1e08017e1753bcad76ef\permission_handler\
[   +1 ms] Found plugin permission_handler_ohos at D:\PUB\git\flutter_permission_handler-f139dc532352dcecb8e9132a910e4aa607f5d9c9\permission_handler_ohos\
[   +1 ms] Found plugin package_info_plus at D:\PUB\git\flutter_plus_plugins-452bfb40465722552f31cf78cde60f99cc691e36\packages\package_info_plus\package_info_plus\
[   +1 ms] Found plugin device_info_plus at D:\PUB\git\flutter_plus_plugins-a1347adcca3a46346a6ddd127cebcec9970cad6c\packages\device_info_plus\device_info_plus\
[   +3 ms] Found plugin connectivity_plus at D:\PUB\git\flutter_plus_plugins-ff774d947bd15d9be08629e3ccfb29dda2e864bd\packages\connectivity_plus\connectivity_plus\
[   +2 ms] Found plugin mobile_scanner at D:\PUB\git\fluttertpc_mobile_scanner-8d353dca7678d28dbbec24e3566e10823ddccf37\
[   +1 ms] Found plugin open_app_settings at D:\PUB\git\fluttertpc_open_app_settings-038c0dd8ffd45bcf84691d62cfff957748cd493a\
[   +1 ms] Found plugin fluwx at D:\PUB\git\fluwx-9fe4d9f1d3c05de5de18d25cc53852866d9c34d7\
[  +31 ms] Found plugin flutter_aliplayer at D:\PUB\hosted\pub.flutter-io.cn\flutter_aliplayer-5.5.6\
[   +4 ms] Found plugin flutter_plugin_android_lifecycle at D:\PUB\hosted\pub.flutter-io.cn\flutter_plugin_android_lifecycle-2.0.17\
[  +22 ms] Found plugin path_provider_android at D:\PUB\hosted\pub.flutter-io.cn\path_provider_android-2.2.1\
[   +1 ms] Found plugin path_provider_foundation at D:\PUB\hosted\pub.flutter-io.cn\path_provider_foundation-2.3.1\
[   +1 ms] Found plugin path_provider_linux at D:\PUB\hosted\pub.flutter-io.cn\path_provider_linux-2.2.1\
[   +1 ms] Found plugin path_provider_windows at D:\PUB\hosted\pub.flutter-io.cn\path_provider_windows-2.2.1\
[        ] Found plugin permission_handler_android at D:\PUB\hosted\pub.flutter-io.cn\permission_handler_android-10.3.6\
[        ] Found plugin permission_handler_apple at D:\PUB\hosted\pub.flutter-io.cn\permission_handler_apple-9.1.4\
[   +1 ms] Found plugin permission_handler_windows at D:\PUB\hosted\pub.flutter-io.cn\permission_handler_windows-0.1.3\
[  +12 ms] Found plugin shared_preferences_android at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_android-2.2.1\
[   +1 ms] Found plugin shared_preferences_foundation at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_foundation-2.3.4\
[   +1 ms] Found plugin shared_preferences_linux at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_linux-2.3.2\
[   +1 ms] Found plugin shared_preferences_web at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_web-2.2.1\
[   +1 ms] Found plugin shared_preferences_windows at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_windows-2.3.2\
[   +3 ms] Found plugin sqflite at D:\PUB\hosted\pub.flutter-io.cn\sqflite-2.2.8+4\
[  +10 ms] Found plugin url_launcher_android at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_android-6.2.0\
[   +2 ms] Found plugin url_launcher_ios at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_ios-6.2.0\
[   +1 ms] Found plugin url_launcher_linux at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_linux-3.1.0\
[   +1 ms] Found plugin url_launcher_macos at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_macos-3.1.0\
[   +1 ms] Found plugin url_launcher_web at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_web-2.0.19\
[   +2 ms] Found plugin url_launcher_windows at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_windows-3.1.0\
[  +14 ms] Found plugin camera_ohos at D:\wulingFlutter\wuling-flutter-app\plugins\camera_ohos\
[ +109 ms] Initializing file store
[  +12 ms] Skipping target: gen_localizations
[   +7 ms] gen_dart_plugin_registrant: Starting due to {InvalidatedReasonKind.inputChanged: The following inputs have updated contents:
D:\wulingFlutter\wuling-flutter-app\.dart_tool\package_config_subset}
[  +20 ms] Found plugin flutter_blue_plus at D:\PUB\git\flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0\
[   +1 ms] Found plugin iamgeqr_flutter_plugin at D:\PUB\git\flutter_image_scanner-b9f20a88ca1c738c700a15655b8fab4c305c4d32\
[   +1 ms] Found plugin image_picker_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\image_picker\image_picker_ohos\
[   +1 ms] Found plugin path_provider_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\path_provider\path_provider_ohos\
[   +3 ms] Found plugin shared_preferences_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\shared_preferences\shared_preferences_ohos\
[   +1 ms] Found plugin url_launcher_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\url_launcher\url_launcher_ohos\
[   +1 ms] Found plugin webview_flutter_android at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\webview_flutter\webview_flutter_android\
[   +1 ms] Found plugin webview_flutter_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\webview_flutter\webview_flutter_ohos\
[   +2 ms] Found plugin webview_flutter_wkwebview at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\webview_flutter\webview_flutter_wkwebview\
[   +2 ms] Found plugin path_provider at D:\PUB\git\flutter_packages-770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc\packages\path_provider\path_provider\
[   +1 ms] Found plugin shared_preferences at D:\PUB\git\flutter_packages-770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc\packages\shared_preferences\shared_preferences\
[   +1 ms] Found plugin url_launcher at D:\PUB\git\flutter_packages-770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc\packages\url_launcher\url_launcher\
[   +4 ms] Found plugin webview_flutter at D:\PUB\git\flutter_packages-c030f4f22899ee7c436687008c673a6218b6cdf3\packages\webview_flutter\webview_flutter\
[   +1 ms] Found plugin permission_handler at D:\PUB\git\flutter_permission_handler-1e7c80f0720aff742d9f1e08017e1753bcad76ef\permission_handler\
[   +1 ms] Found plugin permission_handler_ohos at D:\PUB\git\flutter_permission_handler-f139dc532352dcecb8e9132a910e4aa607f5d9c9\permission_handler_ohos\
[   +1 ms] Found plugin package_info_plus at D:\PUB\git\flutter_plus_plugins-452bfb40465722552f31cf78cde60f99cc691e36\packages\package_info_plus\package_info_plus\
[   +1 ms] Found plugin device_info_plus at D:\PUB\git\flutter_plus_plugins-a1347adcca3a46346a6ddd127cebcec9970cad6c\packages\device_info_plus\device_info_plus\
[   +1 ms] Found plugin connectivity_plus at D:\PUB\git\flutter_plus_plugins-ff774d947bd15d9be08629e3ccfb29dda2e864bd\packages\connectivity_plus\connectivity_plus\
[   +2 ms] Found plugin mobile_scanner at D:\PUB\git\fluttertpc_mobile_scanner-8d353dca7678d28dbbec24e3566e10823ddccf37\
[   +3 ms] Found plugin open_app_settings at D:\PUB\git\fluttertpc_open_app_settings-038c0dd8ffd45bcf84691d62cfff957748cd493a\
[   +2 ms] Found plugin fluwx at D:\PUB\git\fluwx-9fe4d9f1d3c05de5de18d25cc53852866d9c34d7\
[  +26 ms] Found plugin flutter_aliplayer at D:\PUB\hosted\pub.flutter-io.cn\flutter_aliplayer-5.5.6\
[   +6 ms] Found plugin flutter_plugin_android_lifecycle at D:\PUB\hosted\pub.flutter-io.cn\flutter_plugin_android_lifecycle-2.0.17\
[  +23 ms] Found plugin path_provider_android at D:\PUB\hosted\pub.flutter-io.cn\path_provider_android-2.2.1\
[   +1 ms] Found plugin path_provider_foundation at D:\PUB\hosted\pub.flutter-io.cn\path_provider_foundation-2.3.1\
[   +1 ms] Found plugin path_provider_linux at D:\PUB\hosted\pub.flutter-io.cn\path_provider_linux-2.2.1\
[   +3 ms] Found plugin path_provider_windows at D:\PUB\hosted\pub.flutter-io.cn\path_provider_windows-2.2.1\
[   +1 ms] Found plugin permission_handler_android at D:\PUB\hosted\pub.flutter-io.cn\permission_handler_android-10.3.6\
[   +1 ms] Found plugin permission_handler_apple at D:\PUB\hosted\pub.flutter-io.cn\permission_handler_apple-9.1.4\
[   +1 ms] Found plugin permission_handler_windows at D:\PUB\hosted\pub.flutter-io.cn\permission_handler_windows-0.1.3\
[  +13 ms] Found plugin shared_preferences_android at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_android-2.2.1\
[   +1 ms] Found plugin shared_preferences_foundation at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_foundation-2.3.4\
[   +1 ms] Found plugin shared_preferences_linux at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_linux-2.3.2\
[   +1 ms] Found plugin shared_preferences_web at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_web-2.2.1\
[   +1 ms] Found plugin shared_preferences_windows at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_windows-2.3.2\
[   +4 ms] Found plugin sqflite at D:\PUB\hosted\pub.flutter-io.cn\sqflite-2.2.8+4\
[   +8 ms] Found plugin url_launcher_android at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_android-6.2.0\
[   +1 ms] Found plugin url_launcher_ios at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_ios-6.2.0\
[   +1 ms] Found plugin url_launcher_linux at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_linux-3.1.0\
[        ] Found plugin url_launcher_macos at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_macos-3.1.0\
[   +1 ms] Found plugin url_launcher_web at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_web-2.0.19\
[   +1 ms] Found plugin url_launcher_windows at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_windows-3.1.0\
[  +16 ms] Found plugin camera_ohos at D:\wulingFlutter\wuling-flutter-app\plugins\camera_ohos\
[  +10 ms] gen_dart_plugin_registrant: Complete
[   +3 ms] _composite: Starting due to {}
[        ] _composite: Complete
[   +4 ms] complete
[  +13 ms] Launching lib\main.dart on 29Q0223813005436 in debug mode...
[   +6 ms] D:\flutter_flutter\bin\cache\dart-sdk\bin\dart.exe --disable-dart-dev D:\flutter_flutter\bin\cache\dart-sdk\bin\snapshots\frontend_server.dart.snapshot --sdk-root
D:\flutter_flutter\bin\cache\artifacts\engine\common\flutter_patched_sdk/ --incremental --target=flutter --experimental-emit-debug-metadata -DFLUTTER_WEB_AUTO_DETECT=true --output-dill
C:\Users\<USER>\AppData\Local\Temp\flutter_tools.cf639622\flutter_tool.724acb47\app.dill --packages D:\wulingFlutter\wuling-flutter-app\.dart_tool\package_config.json
-Ddart.vm.profile=false -Ddart.vm.product=false --enable-asserts --track-widget-creation --filesystem-scheme org-dartlang-root --initialize-from-dill
build\c075001b96339384a97db4862b8ab8db.cache.dill.track.dill --source file:///D:/wulingFlutter/wuling-flutter-app/.dart_tool/flutter_build/dart_plugin_registrant.dart --source
package:flutter/src/dart_plugin_registrant.dart -Dflutter.dart_plugin_registrant=file:///D:/wulingFlutter/wuling-flutter-app/.dart_tool/flutter_build/dart_plugin_registrant.dart
--verbosity=error --flutter-widget-cache --enable-experiment=alternative-invalidation-strategy
[  +21 ms] executing: D:\Huawei\DevEco Studio\sdk\default\openharmony\toolchains\hdc.exe -t 29Q0223813005436 shell hilog -r
[ +356 ms] Exit code 0 from: D:\Huawei\DevEco Studio\sdk\default\openharmony\toolchains\hdc.exe -t 29Q0223813005436 shell hilog -r
[   +1 ms] Log type core,app,only_prerelease buffer clear successfully
[ +156 ms] <- compile package:wuling_flutter_app/main.dart
[ +102 ms] Building Hap
[   +7 ms] start hap build...
[   +5 ms] Running Hvigor task assembleHap...
[  +13 ms] Found plugin flutter_blue_plus at D:\PUB\git\flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0\
[   +1 ms] Found plugin iamgeqr_flutter_plugin at D:\PUB\git\flutter_image_scanner-b9f20a88ca1c738c700a15655b8fab4c305c4d32\
[   +1 ms] Found plugin image_picker_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\image_picker\image_picker_ohos\
[   +2 ms] Found plugin path_provider_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\path_provider\path_provider_ohos\
[   +3 ms] Found plugin shared_preferences_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\shared_preferences\shared_preferences_ohos\
[   +1 ms] Found plugin url_launcher_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\url_launcher\url_launcher_ohos\
[   +1 ms] Found plugin webview_flutter_android at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\webview_flutter\webview_flutter_android\
[   +1 ms] Found plugin webview_flutter_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\webview_flutter\webview_flutter_ohos\
[   +2 ms] Found plugin webview_flutter_wkwebview at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\webview_flutter\webview_flutter_wkwebview\
[   +1 ms] Found plugin path_provider at D:\PUB\git\flutter_packages-770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc\packages\path_provider\path_provider\
[   +1 ms] Found plugin shared_preferences at D:\PUB\git\flutter_packages-770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc\packages\shared_preferences\shared_preferences\
[   +2 ms] Found plugin url_launcher at D:\PUB\git\flutter_packages-770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc\packages\url_launcher\url_launcher\
[   +2 ms] Found plugin webview_flutter at D:\PUB\git\flutter_packages-c030f4f22899ee7c436687008c673a6218b6cdf3\packages\webview_flutter\webview_flutter\
[   +1 ms] Found plugin permission_handler at D:\PUB\git\flutter_permission_handler-1e7c80f0720aff742d9f1e08017e1753bcad76ef\permission_handler\
[   +2 ms] Found plugin permission_handler_ohos at D:\PUB\git\flutter_permission_handler-f139dc532352dcecb8e9132a910e4aa607f5d9c9\permission_handler_ohos\
[   +1 ms] Found plugin package_info_plus at D:\PUB\git\flutter_plus_plugins-452bfb40465722552f31cf78cde60f99cc691e36\packages\package_info_plus\package_info_plus\
[   +1 ms] Found plugin device_info_plus at D:\PUB\git\flutter_plus_plugins-a1347adcca3a46346a6ddd127cebcec9970cad6c\packages\device_info_plus\device_info_plus\
[   +1 ms] Found plugin connectivity_plus at D:\PUB\git\flutter_plus_plugins-ff774d947bd15d9be08629e3ccfb29dda2e864bd\packages\connectivity_plus\connectivity_plus\
[   +3 ms] Found plugin mobile_scanner at D:\PUB\git\fluttertpc_mobile_scanner-8d353dca7678d28dbbec24e3566e10823ddccf37\
[   +2 ms] Found plugin open_app_settings at D:\PUB\git\fluttertpc_open_app_settings-038c0dd8ffd45bcf84691d62cfff957748cd493a\
[   +1 ms] Found plugin fluwx at D:\PUB\git\fluwx-9fe4d9f1d3c05de5de18d25cc53852866d9c34d7\
[  +43 ms] Found plugin flutter_aliplayer at D:\PUB\hosted\pub.flutter-io.cn\flutter_aliplayer-5.5.6\
[   +5 ms] Found plugin flutter_plugin_android_lifecycle at D:\PUB\hosted\pub.flutter-io.cn\flutter_plugin_android_lifecycle-2.0.17\
[  +30 ms] Found plugin path_provider_android at D:\PUB\hosted\pub.flutter-io.cn\path_provider_android-2.2.1\
[   +1 ms] Found plugin path_provider_foundation at D:\PUB\hosted\pub.flutter-io.cn\path_provider_foundation-2.3.1\
[   +1 ms] Found plugin path_provider_linux at D:\PUB\hosted\pub.flutter-io.cn\path_provider_linux-2.2.1\
[   +1 ms] Found plugin path_provider_windows at D:\PUB\hosted\pub.flutter-io.cn\path_provider_windows-2.2.1\
[   +1 ms] Found plugin permission_handler_android at D:\PUB\hosted\pub.flutter-io.cn\permission_handler_android-10.3.6\
[   +1 ms] Found plugin permission_handler_apple at D:\PUB\hosted\pub.flutter-io.cn\permission_handler_apple-9.1.4\
[   +1 ms] Found plugin permission_handler_windows at D:\PUB\hosted\pub.flutter-io.cn\permission_handler_windows-0.1.3\
[  +13 ms] Found plugin shared_preferences_android at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_android-2.2.1\
[   +1 ms] Found plugin shared_preferences_foundation at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_foundation-2.3.4\
[        ] Found plugin shared_preferences_linux at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_linux-2.3.2\
[   +1 ms] Found plugin shared_preferences_web at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_web-2.2.1\
[   +1 ms] Found plugin shared_preferences_windows at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_windows-2.3.2\
[   +5 ms] Found plugin sqflite at D:\PUB\hosted\pub.flutter-io.cn\sqflite-2.2.8+4\
[   +7 ms] Found plugin url_launcher_android at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_android-6.2.0\
[   +1 ms] Found plugin url_launcher_ios at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_ios-6.2.0\
[   +2 ms] Found plugin url_launcher_linux at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_linux-3.1.0\
[   +1 ms] Found plugin url_launcher_macos at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_macos-3.1.0\
[   +3 ms] Found plugin url_launcher_web at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_web-2.0.19\
[   +1 ms] Found plugin url_launcher_windows at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_windows-3.1.0\
[  +21 ms] Found plugin camera_ohos at D:\wulingFlutter\wuling-flutter-app\plugins\camera_ohos\
[  +24 ms] Found plugin flutter_blue_plus at D:\PUB\git\flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0\
[   +1 ms] Found plugin iamgeqr_flutter_plugin at D:\PUB\git\flutter_image_scanner-b9f20a88ca1c738c700a15655b8fab4c305c4d32\
[   +1 ms] Found plugin image_picker_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\image_picker\image_picker_ohos\
[   +1 ms] Found plugin path_provider_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\path_provider\path_provider_ohos\
[   +1 ms] Found plugin shared_preferences_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\shared_preferences\shared_preferences_ohos\
[   +1 ms] Found plugin url_launcher_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\url_launcher\url_launcher_ohos\
[   +2 ms] Found plugin webview_flutter_android at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\webview_flutter\webview_flutter_android\
[   +1 ms] Found plugin webview_flutter_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\webview_flutter\webview_flutter_ohos\
[   +2 ms] Found plugin webview_flutter_wkwebview at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\webview_flutter\webview_flutter_wkwebview\
[   +1 ms] Found plugin path_provider at D:\PUB\git\flutter_packages-770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc\packages\path_provider\path_provider\
[   +1 ms] Found plugin shared_preferences at D:\PUB\git\flutter_packages-770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc\packages\shared_preferences\shared_preferences\
[   +1 ms] Found plugin url_launcher at D:\PUB\git\flutter_packages-770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc\packages\url_launcher\url_launcher\
[   +1 ms] Found plugin webview_flutter at D:\PUB\git\flutter_packages-c030f4f22899ee7c436687008c673a6218b6cdf3\packages\webview_flutter\webview_flutter\
[   +1 ms] Found plugin permission_handler at D:\PUB\git\flutter_permission_handler-1e7c80f0720aff742d9f1e08017e1753bcad76ef\permission_handler\
[   +1 ms] Found plugin permission_handler_ohos at D:\PUB\git\flutter_permission_handler-f139dc532352dcecb8e9132a910e4aa607f5d9c9\permission_handler_ohos\
[   +1 ms] Found plugin package_info_plus at D:\PUB\git\flutter_plus_plugins-452bfb40465722552f31cf78cde60f99cc691e36\packages\package_info_plus\package_info_plus\
[   +1 ms] Found plugin device_info_plus at D:\PUB\git\flutter_plus_plugins-a1347adcca3a46346a6ddd127cebcec9970cad6c\packages\device_info_plus\device_info_plus\
[   +2 ms] Found plugin connectivity_plus at D:\PUB\git\flutter_plus_plugins-ff774d947bd15d9be08629e3ccfb29dda2e864bd\packages\connectivity_plus\connectivity_plus\
[   +4 ms] Found plugin mobile_scanner at D:\PUB\git\fluttertpc_mobile_scanner-8d353dca7678d28dbbec24e3566e10823ddccf37\
[   +1 ms] Found plugin open_app_settings at D:\PUB\git\fluttertpc_open_app_settings-038c0dd8ffd45bcf84691d62cfff957748cd493a\
[   +1 ms] Found plugin fluwx at D:\PUB\git\fluwx-9fe4d9f1d3c05de5de18d25cc53852866d9c34d7\
[  +32 ms] Found plugin flutter_aliplayer at D:\PUB\hosted\pub.flutter-io.cn\flutter_aliplayer-5.5.6\
[   +6 ms] Found plugin flutter_plugin_android_lifecycle at D:\PUB\hosted\pub.flutter-io.cn\flutter_plugin_android_lifecycle-2.0.17\
[  +23 ms] Found plugin path_provider_android at D:\PUB\hosted\pub.flutter-io.cn\path_provider_android-2.2.1\
[   +1 ms] Found plugin path_provider_foundation at D:\PUB\hosted\pub.flutter-io.cn\path_provider_foundation-2.3.1\
[   +1 ms] Found plugin path_provider_linux at D:\PUB\hosted\pub.flutter-io.cn\path_provider_linux-2.2.1\
[   +1 ms] Found plugin path_provider_windows at D:\PUB\hosted\pub.flutter-io.cn\path_provider_windows-2.2.1\
[   +1 ms] Found plugin permission_handler_android at D:\PUB\hosted\pub.flutter-io.cn\permission_handler_android-10.3.6\
[   +2 ms] Found plugin permission_handler_apple at D:\PUB\hosted\pub.flutter-io.cn\permission_handler_apple-9.1.4\
[   +2 ms] Found plugin permission_handler_windows at D:\PUB\hosted\pub.flutter-io.cn\permission_handler_windows-0.1.3\
[  +14 ms] Found plugin shared_preferences_android at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_android-2.2.1\
[   +1 ms] Found plugin shared_preferences_foundation at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_foundation-2.3.4\
[   +1 ms] Found plugin shared_preferences_linux at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_linux-2.3.2\
[   +1 ms] Found plugin shared_preferences_web at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_web-2.2.1\
[   +1 ms] Found plugin shared_preferences_windows at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_windows-2.3.2\
[   +6 ms] Found plugin sqflite at D:\PUB\hosted\pub.flutter-io.cn\sqflite-2.2.8+4\
[  +11 ms] Found plugin url_launcher_android at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_android-6.2.0\
[   +1 ms] Found plugin url_launcher_ios at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_ios-6.2.0\
[   +1 ms] Found plugin url_launcher_linux at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_linux-3.1.0\
[   +1 ms] Found plugin url_launcher_macos at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_macos-3.1.0\
[   +2 ms] Found plugin url_launcher_web at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_web-2.0.19\
[   +1 ms] Found plugin url_launcher_windows at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_windows-3.1.0\
[  +16 ms] Found plugin camera_ohos at D:\wulingFlutter\wuling-flutter-app\plugins\camera_ohos\
[  +48 ms] Found plugin flutter_blue_plus at D:\PUB\git\flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0\
[   +3 ms] Found plugin iamgeqr_flutter_plugin at D:\PUB\git\flutter_image_scanner-b9f20a88ca1c738c700a15655b8fab4c305c4d32\
[   +1 ms] Found plugin image_picker_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\image_picker\image_picker_ohos\
[   +1 ms] Found plugin path_provider_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\path_provider\path_provider_ohos\
[   +1 ms] Found plugin shared_preferences_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\shared_preferences\shared_preferences_ohos\
[   +1 ms] Found plugin url_launcher_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\url_launcher\url_launcher_ohos\
[   +1 ms] Found plugin webview_flutter_android at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\webview_flutter\webview_flutter_android\
[   +1 ms] Found plugin webview_flutter_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\webview_flutter\webview_flutter_ohos\
[   +5 ms] Found plugin webview_flutter_wkwebview at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\webview_flutter\webview_flutter_wkwebview\
[   +3 ms] Found plugin path_provider at D:\PUB\git\flutter_packages-770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc\packages\path_provider\path_provider\
[   +1 ms] Found plugin shared_preferences at D:\PUB\git\flutter_packages-770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc\packages\shared_preferences\shared_preferences\
[   +1 ms] Found plugin url_launcher at D:\PUB\git\flutter_packages-770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc\packages\url_launcher\url_launcher\
[   +1 ms] Found plugin webview_flutter at D:\PUB\git\flutter_packages-c030f4f22899ee7c436687008c673a6218b6cdf3\packages\webview_flutter\webview_flutter\
[   +1 ms] Found plugin permission_handler at D:\PUB\git\flutter_permission_handler-1e7c80f0720aff742d9f1e08017e1753bcad76ef\permission_handler\
[   +1 ms] Found plugin permission_handler_ohos at D:\PUB\git\flutter_permission_handler-f139dc532352dcecb8e9132a910e4aa607f5d9c9\permission_handler_ohos\
[   +1 ms] Found plugin package_info_plus at D:\PUB\git\flutter_plus_plugins-452bfb40465722552f31cf78cde60f99cc691e36\packages\package_info_plus\package_info_plus\
[   +2 ms] Found plugin device_info_plus at D:\PUB\git\flutter_plus_plugins-a1347adcca3a46346a6ddd127cebcec9970cad6c\packages\device_info_plus\device_info_plus\
[   +1 ms] Found plugin connectivity_plus at D:\PUB\git\flutter_plus_plugins-ff774d947bd15d9be08629e3ccfb29dda2e864bd\packages\connectivity_plus\connectivity_plus\
[   +2 ms] Found plugin mobile_scanner at D:\PUB\git\fluttertpc_mobile_scanner-8d353dca7678d28dbbec24e3566e10823ddccf37\
[   +1 ms] Found plugin open_app_settings at D:\PUB\git\fluttertpc_open_app_settings-038c0dd8ffd45bcf84691d62cfff957748cd493a\
[   +1 ms] Found plugin fluwx at D:\PUB\git\fluwx-9fe4d9f1d3c05de5de18d25cc53852866d9c34d7\
[  +33 ms] Found plugin flutter_aliplayer at D:\PUB\hosted\pub.flutter-io.cn\flutter_aliplayer-5.5.6\
[   +5 ms] Found plugin flutter_plugin_android_lifecycle at D:\PUB\hosted\pub.flutter-io.cn\flutter_plugin_android_lifecycle-2.0.17\
[  +31 ms] Found plugin path_provider_android at D:\PUB\hosted\pub.flutter-io.cn\path_provider_android-2.2.1\
[   +2 ms] Found plugin path_provider_foundation at D:\PUB\hosted\pub.flutter-io.cn\path_provider_foundation-2.3.1\
[   +3 ms] Found plugin path_provider_linux at D:\PUB\hosted\pub.flutter-io.cn\path_provider_linux-2.2.1\
[   +2 ms] Found plugin path_provider_windows at D:\PUB\hosted\pub.flutter-io.cn\path_provider_windows-2.2.1\
[   +1 ms] Found plugin permission_handler_android at D:\PUB\hosted\pub.flutter-io.cn\permission_handler_android-10.3.6\
[   +1 ms] Found plugin permission_handler_apple at D:\PUB\hosted\pub.flutter-io.cn\permission_handler_apple-9.1.4\
[   +1 ms] Found plugin permission_handler_windows at D:\PUB\hosted\pub.flutter-io.cn\permission_handler_windows-0.1.3\
[  +15 ms] Found plugin shared_preferences_android at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_android-2.2.1\
[   +1 ms] Found plugin shared_preferences_foundation at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_foundation-2.3.4\
[   +1 ms] Found plugin shared_preferences_linux at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_linux-2.3.2\
[   +2 ms] Found plugin shared_preferences_web at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_web-2.2.1\
[   +2 ms] Found plugin shared_preferences_windows at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_windows-2.3.2\
[   +9 ms] Found plugin sqflite at D:\PUB\hosted\pub.flutter-io.cn\sqflite-2.2.8+4\
[   +9 ms] Found plugin url_launcher_android at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_android-6.2.0\
[   +1 ms] Found plugin url_launcher_ios at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_ios-6.2.0\
[   +1 ms] Found plugin url_launcher_linux at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_linux-3.1.0\
[   +1 ms] Found plugin url_launcher_macos at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_macos-3.1.0\
[   +2 ms] Found plugin url_launcher_web at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_web-2.0.19\
[   +1 ms] Found plugin url_launcher_windows at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_windows-3.1.0\
[  +18 ms] Found plugin camera_ohos at D:\wulingFlutter\wuling-flutter-app\plugins\camera_ohos\
[  +19 ms] Compiling debug_ohos_application for the Ohos...
[   +6 ms] Initializing file store
[   +9 ms] Skipping target: gen_localizations
[  +16 ms] gen_dart_plugin_registrant: Starting due to {InvalidatedReasonKind.inputChanged: The following inputs have updated contents:
D:\wulingFlutter\wuling-flutter-app\.dart_tool\package_config_subset}
[  +14 ms] Found plugin flutter_blue_plus at D:\PUB\git\flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0\
[   +1 ms] Found plugin iamgeqr_flutter_plugin at D:\PUB\git\flutter_image_scanner-b9f20a88ca1c738c700a15655b8fab4c305c4d32\
[   +1 ms] Found plugin image_picker_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\image_picker\image_picker_ohos\
[   +1 ms] Found plugin path_provider_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\path_provider\path_provider_ohos\
[   +1 ms] Found plugin shared_preferences_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\shared_preferences\shared_preferences_ohos\
[   +1 ms] Found plugin url_launcher_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\url_launcher\url_launcher_ohos\
[   +1 ms] Found plugin webview_flutter_android at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\webview_flutter\webview_flutter_android\
[   +1 ms] Found plugin webview_flutter_ohos at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\webview_flutter\webview_flutter_ohos\
[   +3 ms] Found plugin webview_flutter_wkwebview at D:\PUB\git\flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb\packages\webview_flutter\webview_flutter_wkwebview\
[   +2 ms] Found plugin path_provider at D:\PUB\git\flutter_packages-770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc\packages\path_provider\path_provider\
[   +2 ms] Found plugin shared_preferences at D:\PUB\git\flutter_packages-770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc\packages\shared_preferences\shared_preferences\
[   +3 ms] Found plugin url_launcher at D:\PUB\git\flutter_packages-770bd6e5b980cc64e1da1fd6568e5cc9c7bd6ecc\packages\url_launcher\url_launcher\
[   +2 ms] Found plugin webview_flutter at D:\PUB\git\flutter_packages-c030f4f22899ee7c436687008c673a6218b6cdf3\packages\webview_flutter\webview_flutter\
[   +1 ms] Found plugin permission_handler at D:\PUB\git\flutter_permission_handler-1e7c80f0720aff742d9f1e08017e1753bcad76ef\permission_handler\
[   +1 ms] Found plugin permission_handler_ohos at D:\PUB\git\flutter_permission_handler-f139dc532352dcecb8e9132a910e4aa607f5d9c9\permission_handler_ohos\
[   +1 ms] Found plugin package_info_plus at D:\PUB\git\flutter_plus_plugins-452bfb40465722552f31cf78cde60f99cc691e36\packages\package_info_plus\package_info_plus\
[   +2 ms] Found plugin device_info_plus at D:\PUB\git\flutter_plus_plugins-a1347adcca3a46346a6ddd127cebcec9970cad6c\packages\device_info_plus\device_info_plus\
[   +3 ms] Found plugin connectivity_plus at D:\PUB\git\flutter_plus_plugins-ff774d947bd15d9be08629e3ccfb29dda2e864bd\packages\connectivity_plus\connectivity_plus\
[   +2 ms] Found plugin mobile_scanner at D:\PUB\git\fluttertpc_mobile_scanner-8d353dca7678d28dbbec24e3566e10823ddccf37\
[   +1 ms] Found plugin open_app_settings at D:\PUB\git\fluttertpc_open_app_settings-038c0dd8ffd45bcf84691d62cfff957748cd493a\
[   +1 ms] Found plugin fluwx at D:\PUB\git\fluwx-9fe4d9f1d3c05de5de18d25cc53852866d9c34d7\
[  +30 ms] Found plugin flutter_aliplayer at D:\PUB\hosted\pub.flutter-io.cn\flutter_aliplayer-5.5.6\
[   +5 ms] Found plugin flutter_plugin_android_lifecycle at D:\PUB\hosted\pub.flutter-io.cn\flutter_plugin_android_lifecycle-2.0.17\
[  +25 ms] Found plugin path_provider_android at D:\PUB\hosted\pub.flutter-io.cn\path_provider_android-2.2.1\
[   +1 ms] Found plugin path_provider_foundation at D:\PUB\hosted\pub.flutter-io.cn\path_provider_foundation-2.3.1\
[   +1 ms] Found plugin path_provider_linux at D:\PUB\hosted\pub.flutter-io.cn\path_provider_linux-2.2.1\
[   +1 ms] Found plugin path_provider_windows at D:\PUB\hosted\pub.flutter-io.cn\path_provider_windows-2.2.1\
[   +1 ms] Found plugin permission_handler_android at D:\PUB\hosted\pub.flutter-io.cn\permission_handler_android-10.3.6\
[   +1 ms] Found plugin permission_handler_apple at D:\PUB\hosted\pub.flutter-io.cn\permission_handler_apple-9.1.4\
[   +2 ms] Found plugin permission_handler_windows at D:\PUB\hosted\pub.flutter-io.cn\permission_handler_windows-0.1.3\
[  +13 ms] Found plugin shared_preferences_android at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_android-2.2.1\
[   +1 ms] Found plugin shared_preferences_foundation at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_foundation-2.3.4\
[   +3 ms] Found plugin shared_preferences_linux at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_linux-2.3.2\
[   +4 ms] Found plugin shared_preferences_web at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_web-2.2.1\
[   +1 ms] Found plugin shared_preferences_windows at D:\PUB\hosted\pub.flutter-io.cn\shared_preferences_windows-2.3.2\
[   +4 ms] Found plugin sqflite at D:\PUB\hosted\pub.flutter-io.cn\sqflite-2.2.8+4\
[   +9 ms] Found plugin url_launcher_android at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_android-6.2.0\
[   +1 ms] Found plugin url_launcher_ios at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_ios-6.2.0\
[   +1 ms] Found plugin url_launcher_linux at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_linux-3.1.0\
[   +1 ms] Found plugin url_launcher_macos at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_macos-3.1.0\
[   +1 ms] Found plugin url_launcher_web at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_web-2.0.19\
[   +1 ms] Found plugin url_launcher_windows at D:\PUB\hosted\pub.flutter-io.cn\url_launcher_windows-3.1.0\
[  +18 ms] Found plugin camera_ohos at D:\wulingFlutter\wuling-flutter-app\plugins\camera_ohos\
[   +6 ms] gen_dart_plugin_registrant: Complete
[   +5 ms] kernel_snapshot: Starting due to {}
[  +16 ms] D:\flutter_flutter\bin\cache\dart-sdk\bin\dart.exe --disable-dart-dev D:\flutter_flutter\bin\cache\dart-sdk\bin\snapshots\frontend_server.dart.snapshot --sdk-root
D:\flutter_flutter\bin\cache\artifacts\engine\common\flutter_patched_sdk/ --target=flutter --no-print-incremental-dependencies -DFLUTTER_WEB_AUTO_DETECT=true -Ddart.vm.profile=false
-Ddart.vm.product=false --enable-asserts --track-widget-creation --no-link-platform --packages D:\wulingFlutter\wuling-flutter-app\.dart_tool\package_config.json --output-dill
D:\wulingFlutter\wuling-flutter-app\.dart_tool\flutter_build\1281345a13f19b71d0d559f4def3be90\app.dill --depfile
D:\wulingFlutter\wuling-flutter-app\.dart_tool\flutter_build\1281345a13f19b71d0d559f4def3be90\kernel_snapshot.d --filesystem-scheme org-dartlang-root --incremental
--initialize-from-dill D:\wulingFlutter\wuling-flutter-app\.dart_tool\flutter_build\1281345a13f19b71d0d559f4def3be90\app.dill --source
file:///D:/wulingFlutter/wuling-flutter-app/.dart_tool/flutter_build/dart_plugin_registrant.dart --source package:flutter/src/dart_plugin_registrant.dart
-Dflutter.dart_plugin_registrant=file:///D:/wulingFlutter/wuling-flutter-app/.dart_tool/flutter_build/dart_plugin_registrant.dart --verbosity=error package:wuling_flutter_app/main.dart
[+32228 ms] kernel_snapshot: Complete
[+3126 ms] debug_ohos_application: Starting due to {}
[+1276 ms] shaderc command: [D:\flutter_flutter\bin\cache\artifacts\engine\windows-x64\impellerc.exe, --sksl, --iplr,
--sl=D:\wulingFlutter\wuling-flutter-app\build\ohos\flutter_assets\shaders/ink_sparkle.frag,
--spirv=D:\wulingFlutter\wuling-flutter-app\build\ohos\flutter_assets\shaders/ink_sparkle.frag.spirv,
--input=D:\flutter_flutter\packages\flutter\lib\src\material\shaders\ink_sparkle.frag, --input-type=frag, --remap-samplers,
--include=D:\flutter_flutter\packages\flutter\lib\src\material\shaders, --include=D:\flutter_flutter\bin\cache\artifacts\engine\windows-x64\shader_lib]
[ +713 ms] debug_ohos_application: Complete
[+1792 ms] Persisting file store
[  +26 ms] Done persisting file store
[  +31 ms] Compiling debug_ohos_application for the Ohos... (completed in 39.4s)
[   +3 ms] copy flutter assets to project start
[ +185 ms] copy from "D:\wulingFlutter\wuling-flutter-app\build\ohos\flutter_assets" to "D:\wulingFlutter\wuling-flutter-app\ohos\entry\src/main/resources/rawfile\flutter_assets"
[ +892 ms] copy flutter assets to project end
[   +2 ms] copy flutter runtime to project start
[  +37 ms] copy from "D:\flutter_flutter\bin\cache\artifacts\engine\ohos-arm64\flutter.har" to "D:\wulingFlutter\wuling-flutter-app\ohos\har\flutter.har"
[        ] copy flutter runtime to project end
[        ] executing: [D:\wulingFlutter\wuling-flutter-app\ohos/] ohpm clean
[+3534 ms] Exit code 0 from: ohpm clean
[   +1 ms] clean completed in 0s 725ms
[        ] executing: [D:\wulingFlutter\wuling-flutter-app\ohos/] ohpm install --all
[+6234 ms] Exit code 0 from: ohpm install --all
[   +1 ms] ohpm INFO: MetaDataFetcher fetching meta info of package '@pura/harmony-utils' from https://ohpm.openharmony.cn/ohpm/
           ohpm INFO: MetaDataFetcher fetching meta info of package '@bdmap/base' from https://ohpm.openharmony.cn/ohpm/
           ohpm INFO: MetaDataFetcher fetching meta info of package '@bdmap/search' from https://ohpm.openharmony.cn/ohpm/
           ohpm INFO: MetaDataFetcher fetching meta info of package '@bdmap/map' from https://ohpm.openharmony.cn/ohpm/
           ohpm INFO: MetaDataFetcher fetching meta info of package '@tencent/wechat_open_sdk' from https://ohpm.openharmony.cn/ohpm/
           ohpm INFO: MetaDataFetcher fetching meta info of package '@tencent/wechat_open_sdk' from https://ohpm.openharmony.cn/ohpm/
           ohpm INFO: MetaDataFetcher fetching meta info of package '@tencent/wechat_open_sdk' from https://ohpm.openharmony.cn/ohpm/
           ohpm INFO: MetaDataFetcher fetching meta info of package '@free/global' from https://ohpm.openharmony.cn/ohpm/
           ohpm INFO: MetaDataFetcher fetching meta info of package '@rongcloud/imkit' from https://ohpm.openharmony.cn/ohpm/
           ohpm INFO: MetaDataFetcher fetching meta info of package '@rongcloud/imlib' from https://ohpm.openharmony.cn/ohpm/
           ohpm INFO: MetaDataFetcher fetching meta info of package '@ohos/hypium' from https://ohpm.openharmony.cn/ohpm/
           ohpm INFO: fetch meta info of package '@bdmap/map' success https://ohpm.openharmony.cn/ohpm/@bdmap/map
           ohpm INFO: fetch meta info of package '@rongcloud/imkit' success https://ohpm.openharmony.cn/ohpm/@rongcloud/imkit
           ohpm INFO: MetaDataFetcher fetching meta info of package '@rongcloud/imlib' from https://ohpm.openharmony.cn/ohpm/
           ohpm INFO: MetaDataFetcher fetching meta info of package '@charles/amrnbconverter' from https://ohpm.openharmony.cn/ohpm/
           ohpm INFO: fetch meta info of package '@bdmap/search' success https://ohpm.openharmony.cn/ohpm/@bdmap/search
           ohpm INFO: fetch meta info of package '@tencent/wechat_open_sdk' success https://ohpm.openharmony.cn/ohpm/@tencent/wechat_open_sdk
           ohpm INFO: fetch meta info of package '@free/global' success https://ohpm.openharmony.cn/ohpm/@free/global
           ohpm INFO: fetch meta info of package '@rongcloud/imlib' success https://ohpm.openharmony.cn/ohpm/@rongcloud/imlib
           ohpm INFO: fetch meta info of package '@ohos/hypium' success https://ohpm.openharmony.cn/ohpm/@ohos/hypium
           ohpm INFO: fetch meta info of package '@tencent/wechat_open_sdk' success https://ohpm.openharmony.cn/ohpm/@tencent/wechat_open_sdk
           ohpm INFO: fetch meta info of package '@bdmap/base' success https://ohpm.openharmony.cn/ohpm/@bdmap/base
           ohpm INFO: fetch meta info of package '@pura/harmony-utils' success https://ohpm.openharmony.cn/ohpm/@pura/harmony-utils
           ohpm INFO: MetaDataFetcher fetching meta info of package 'class-transformer' from https://ohpm.openharmony.cn/ohpm/
           ohpm INFO: fetch meta info of package '@tencent/wechat_open_sdk' success https://ohpm.openharmony.cn/ohpm/@tencent/wechat_open_sdk
           ohpm INFO: MetaDataFetcher fetching meta info of package '@bdmap/verify' from https://ohpm.openharmony.cn/ohpm/
           ohpm INFO: fetch meta info of package '@charles/amrnbconverter' success https://ohpm.openharmony.cn/ohpm/@charles/amrnbconverter
           ohpm INFO: fetch meta info of package '@rongcloud/imlib' success https://ohpm.openharmony.cn/ohpm/@rongcloud/imlib
           ohpm INFO: fetch meta info of package 'class-transformer' success https://ohpm.openharmony.cn/ohpm/class-transformer
           ohpm INFO: fetch meta info of package '@bdmap/verify' success https://ohpm.openharmony.cn/ohpm/@bdmap/verify
           ohpm INFO: remove useless folder succeed: "D:\wulingFlutter\wuling-flutter-app\ohos\oh_modules\.tmp"
           ohpm WARN: Found version conflict(s) in dependencies of project, and we have helped you resolve it automatically.
           ohpm WARN: dependency "@tencent/wechat_open_sdk" has conflict versions: "1.0.14", "1.0.6", "^1.0.11", and has been resolved as "1.0.14", the affected modules are as follows:
                 - "D:\PUB\git\fluwx-9fe4d9f1d3c05de5de18d25cc53852866d9c34d7\ohos"
                 - "D:\wulingFlutter\wuling-flutter-app\ohos"
                 - "D:\wulingFlutter\wuling-flutter-app\ohos\entry"

           install completed in 3s 814ms
[  +24 ms] Invoke cmd: hvigorw --mode module -p
module=flutter_blue_plus@default,iamgeqr_flutter_plugin@default,image_picker_ohos@default,path_provider_ohos@default,shared_preferences_ohos@default,url_launcher_ohos@default,webview_f
lutter_ohos@default,permission_handler_ohos@default,package_info_plus@default,device_info_plus@default,connectivity_plus@default,mobile_scanner@default,open_app_settings@default,fluwx@
default,camera_ohos@default -p product=default assembleHar --no-daemon
[+10773 ms] > hvigor Finished :flutter_blue_plus:default@PreBuild... after 289 ms
[  +49 ms] > hvigor Finished :iamgeqr_flutter_plugin:default@PreBuild... after 33 ms
[  +35 ms] > hvigor Finished :image_picker_ohos:default@PreBuild... after 20 ms
[  +37 ms] > hvigor Finished :path_provider_ohos:default@PreBuild... after 17 ms
[  +44 ms] > hvigor Finished :shared_preferences_ohos:default@PreBuild... after 23 ms
[  +47 ms] > hvigor Finished :url_launcher_ohos:default@PreBuild... after 24 ms
[  +45 ms] > hvigor Finished :webview_flutter_ohos:default@PreBuild... after 24 ms
[  +42 ms] > hvigor Finished :permission_handler_ohos:default@PreBuild... after 20 ms
[  +48 ms] > hvigor WARN: Set either main or types, or both for this HSP/HAR module. at package_info_plus oh-package.json5.
[  +12 ms] > hvigor Finished :package_info_plus:default@PreBuild... after 28 ms
[  +57 ms] > hvigor Finished :device_info_plus:default@PreBuild... after 25 ms
[  +56 ms] > hvigor Finished :connectivity_plus:default@PreBuild... after 33 ms
[  +53 ms] > hvigor Finished :mobile_scanner:default@PreBuild... after 32 ms
[  +37 ms] > hvigor Finished :open_app_settings:default@PreBuild... after 18 ms
[  +34 ms] > hvigor Finished :fluwx:default@PreBuild... after 18 ms
[  +36 ms] > hvigor Finished :camera_ohos:default@PreBuild... after 22 ms
[  +15 ms] > hvigor Finished :flutter_blue_plus:default@ProcessOHPackageJson... after 3 ms
[  +24 ms] > hvigor Finished :flutter_blue_plus:default@CreateHarBuildProfile... after 10 ms
[  +29 ms] > hvigor UP-TO-DATE :flutter_blue_plus:default@CreateHarModuleInfo...
[  +20 ms] > hvigor Finished :flutter_blue_plus:default@ConfigureCmake... after 1 ms
[  +31 ms] > hvigor Finished :flutter_blue_plus:default@MergeProfile... after 18 ms
[  +51 ms] > hvigor UP-TO-DATE :flutter_blue_plus:default@GeneratePkgContextInfo...
[  +28 ms] > hvigor Finished :flutter_blue_plus:default@ProcessIntegratedHsp... after 5 ms
[  +18 ms] > hvigor Finished :iamgeqr_flutter_plugin:default@ProcessOHPackageJson... after 2 ms
[  +13 ms] > hvigor Finished :iamgeqr_flutter_plugin:default@CreateHarBuildProfile... after 3 ms
[  +14 ms] > hvigor UP-TO-DATE :iamgeqr_flutter_plugin:default@CreateHarModuleInfo...
[  +16 ms] > hvigor Finished :iamgeqr_flutter_plugin:default@ConfigureCmake... after 1 ms
[  +25 ms] > hvigor Finished :iamgeqr_flutter_plugin:default@MergeProfile... after 13 ms
[  +39 ms] > hvigor UP-TO-DATE :iamgeqr_flutter_plugin:default@GeneratePkgContextInfo...
[  +25 ms] > hvigor Finished :iamgeqr_flutter_plugin:default@ProcessIntegratedHsp... after 4 ms
[  +18 ms] > hvigor Finished :image_picker_ohos:default@ProcessOHPackageJson... after 4 ms
[  +14 ms] > hvigor Finished :image_picker_ohos:default@CreateHarBuildProfile... after 4 ms
[  +16 ms] > hvigor UP-TO-DATE :image_picker_ohos:default@CreateHarModuleInfo...
[  +16 ms] > hvigor Finished :image_picker_ohos:default@ConfigureCmake... after 1 ms
[  +23 ms] > hvigor Finished :image_picker_ohos:default@MergeProfile... after 15 ms
[  +27 ms] > hvigor UP-TO-DATE :image_picker_ohos:default@GeneratePkgContextInfo...
[  +19 ms] > hvigor Finished :image_picker_ohos:default@ProcessIntegratedHsp... after 3 ms
[  +19 ms] > hvigor Finished :path_provider_ohos:default@ProcessOHPackageJson... after 3 ms
[  +11 ms] > hvigor Finished :path_provider_ohos:default@CreateHarBuildProfile... after 3 ms
[  +13 ms] > hvigor UP-TO-DATE :path_provider_ohos:default@CreateHarModuleInfo...
[  +13 ms] > hvigor Finished :path_provider_ohos:default@ConfigureCmake... after 1 ms
[  +22 ms] > hvigor Finished :path_provider_ohos:default@MergeProfile... after 11 ms
[  +31 ms] > hvigor UP-TO-DATE :path_provider_ohos:default@GeneratePkgContextInfo...
[  +23 ms] > hvigor Finished :path_provider_ohos:default@ProcessIntegratedHsp... after 4 ms
[  +16 ms] > hvigor Finished :shared_preferences_ohos:default@ProcessOHPackageJson... after 3 ms
[  +18 ms] > hvigor Finished :shared_preferences_ohos:default@CreateHarBuildProfile... after 4 ms
[  +11 ms] > hvigor UP-TO-DATE :shared_preferences_ohos:default@CreateHarModuleInfo...
[  +14 ms] > hvigor Finished :shared_preferences_ohos:default@ConfigureCmake... after 1 ms
[  +26 ms] > hvigor Finished :shared_preferences_ohos:default@MergeProfile... after 15 ms
[  +32 ms] > hvigor UP-TO-DATE :shared_preferences_ohos:default@GeneratePkgContextInfo...
[  +23 ms] > hvigor Finished :shared_preferences_ohos:default@ProcessIntegratedHsp... after 4 ms
[  +14 ms] > hvigor Finished :url_launcher_ohos:default@ProcessOHPackageJson... after 2 ms
[  +13 ms] > hvigor Finished :url_launcher_ohos:default@CreateHarBuildProfile... after 3 ms
[  +12 ms] > hvigor UP-TO-DATE :url_launcher_ohos:default@CreateHarModuleInfo...
[  +11 ms] > hvigor Finished :url_launcher_ohos:default@ConfigureCmake... after 1 ms
[  +22 ms] > hvigor Finished :url_launcher_ohos:default@MergeProfile... after 13 ms
[  +29 ms] > hvigor UP-TO-DATE :url_launcher_ohos:default@GeneratePkgContextInfo...
[  +25 ms] > hvigor Finished :url_launcher_ohos:default@ProcessIntegratedHsp... after 3 ms
[  +17 ms] > hvigor Finished :webview_flutter_ohos:default@ProcessOHPackageJson... after 4 ms
[  +13 ms] > hvigor Finished :webview_flutter_ohos:default@CreateHarBuildProfile... after 3 ms
[  +11 ms] > hvigor UP-TO-DATE :webview_flutter_ohos:default@CreateHarModuleInfo...
[  +13 ms] > hvigor Finished :webview_flutter_ohos:default@ConfigureCmake... after 1 ms
[  +25 ms] > hvigor Finished :webview_flutter_ohos:default@MergeProfile... after 13 ms
[  +26 ms] > hvigor UP-TO-DATE :webview_flutter_ohos:default@GeneratePkgContextInfo...
[  +20 ms] > hvigor Finished :webview_flutter_ohos:default@ProcessIntegratedHsp... after 3 ms
[  +14 ms] > hvigor Finished :permission_handler_ohos:default@ProcessOHPackageJson... after 2 ms
[  +14 ms] > hvigor Finished :permission_handler_ohos:default@CreateHarBuildProfile... after 3 ms
[  +13 ms] > hvigor UP-TO-DATE :permission_handler_ohos:default@CreateHarModuleInfo...
[  +13 ms] > hvigor Finished :permission_handler_ohos:default@ConfigureCmake... after 1 ms
[  +23 ms] > hvigor Finished :permission_handler_ohos:default@MergeProfile... after 12 ms
[  +25 ms] > hvigor UP-TO-DATE :permission_handler_ohos:default@GeneratePkgContextInfo...
[  +18 ms] > hvigor Finished :permission_handler_ohos:default@ProcessIntegratedHsp... after 3 ms
[  +14 ms] > hvigor Finished :package_info_plus:default@ProcessOHPackageJson... after 3 ms
[  +12 ms] > hvigor Finished :package_info_plus:default@CreateHarBuildProfile... after 3 ms
[  +10 ms] > hvigor UP-TO-DATE :package_info_plus:default@CreateHarModuleInfo...
[  +13 ms] > hvigor Finished :package_info_plus:default@ConfigureCmake... after 1 ms
[  +22 ms] > hvigor Finished :package_info_plus:default@MergeProfile... after 14 ms
[  +28 ms] > hvigor UP-TO-DATE :package_info_plus:default@GeneratePkgContextInfo...
[  +26 ms] > hvigor Finished :package_info_plus:default@ProcessIntegratedHsp... after 3 ms
[  +13 ms] > hvigor Finished :device_info_plus:default@ProcessOHPackageJson... after 2 ms
[  +14 ms] > hvigor Finished :device_info_plus:default@CreateHarBuildProfile... after 3 ms
[   +9 ms] > hvigor UP-TO-DATE :device_info_plus:default@CreateHarModuleInfo...
[  +10 ms] > hvigor Finished :device_info_plus:default@ConfigureCmake... after 1 ms
[  +21 ms] > hvigor Finished :device_info_plus:default@MergeProfile... after 13 ms
[  +26 ms] > hvigor UP-TO-DATE :device_info_plus:default@GeneratePkgContextInfo...
[  +19 ms] > hvigor Finished :device_info_plus:default@ProcessIntegratedHsp... after 3 ms
[  +16 ms] > hvigor Finished :connectivity_plus:default@ProcessOHPackageJson... after 3 ms
[  +15 ms] > hvigor Finished :connectivity_plus:default@CreateHarBuildProfile... after 4 ms
[   +9 ms] > hvigor UP-TO-DATE :connectivity_plus:default@CreateHarModuleInfo...
[  +15 ms] > hvigor Finished :connectivity_plus:default@ConfigureCmake... after 1 ms
[  +20 ms] > hvigor Finished :connectivity_plus:default@MergeProfile... after 11 ms
[  +26 ms] > hvigor UP-TO-DATE :connectivity_plus:default@GeneratePkgContextInfo...
[  +21 ms] > hvigor Finished :connectivity_plus:default@ProcessIntegratedHsp... after 3 ms
[  +10 ms] > hvigor Finished :mobile_scanner:default@ProcessOHPackageJson... after 2 ms
[  +15 ms] > hvigor Finished :mobile_scanner:default@CreateHarBuildProfile... after 3 ms
[  +11 ms] > hvigor UP-TO-DATE :mobile_scanner:default@CreateHarModuleInfo...
[  +15 ms] > hvigor Finished :mobile_scanner:default@ConfigureCmake... after 1 ms
[ +100 ms] > hvigor Finished :mobile_scanner:default@MergeProfile... after 91 ms
[  +27 ms] > hvigor UP-TO-DATE :mobile_scanner:default@GeneratePkgContextInfo...
[  +23 ms] > hvigor Finished :mobile_scanner:default@ProcessIntegratedHsp... after 4 ms
[  +12 ms] > hvigor Finished :open_app_settings:default@ProcessOHPackageJson... after 2 ms
[  +14 ms] > hvigor Finished :open_app_settings:default@CreateHarBuildProfile... after 3 ms
[  +11 ms] > hvigor UP-TO-DATE :open_app_settings:default@CreateHarModuleInfo...
[  +12 ms] > hvigor Finished :open_app_settings:default@ConfigureCmake... after 1 ms
[  +24 ms] > hvigor Finished :open_app_settings:default@MergeProfile... after 15 ms
[  +29 ms] > hvigor UP-TO-DATE :open_app_settings:default@GeneratePkgContextInfo...
[  +21 ms] > hvigor Finished :open_app_settings:default@ProcessIntegratedHsp... after 3 ms
[  +19 ms] > hvigor Finished :fluwx:default@ProcessOHPackageJson... after 5 ms
[  +20 ms] > hvigor Finished :fluwx:default@CreateHarBuildProfile... after 5 ms
[  +12 ms] > hvigor UP-TO-DATE :fluwx:default@CreateHarModuleInfo...
[  +12 ms] > hvigor Finished :fluwx:default@ConfigureCmake... after 1 ms
[  +29 ms] > hvigor Finished :fluwx:default@MergeProfile... after 15 ms
[  +39 ms] > hvigor UP-TO-DATE :fluwx:default@GeneratePkgContextInfo...
[  +23 ms] > hvigor Finished :fluwx:default@ProcessIntegratedHsp... after 3 ms
[  +14 ms] > hvigor Finished :camera_ohos:default@ProcessOHPackageJson... after 3 ms
[  +18 ms] > hvigor Finished :camera_ohos:default@CreateHarBuildProfile... after 4 ms
[  +13 ms] > hvigor UP-TO-DATE :camera_ohos:default@CreateHarModuleInfo...
[  +13 ms] > hvigor Finished :camera_ohos:default@ConfigureCmake... after 1 ms
[  +26 ms] > hvigor Finished :camera_ohos:default@MergeProfile... after 14 ms
[  +25 ms] > hvigor UP-TO-DATE :camera_ohos:default@GeneratePkgContextInfo...
[  +22 ms] > hvigor Finished :camera_ohos:default@ProcessIntegratedHsp... after 5 ms
[  +16 ms] > hvigor Finished :flutter_blue_plus:default@BuildNativeWithCmake... after 1 ms
[ +508 ms] > hvigor Finished :flutter_blue_plus:default@ProcessProfile... after 497 ms
[  +26 ms] > hvigor Finished :flutter_blue_plus:default@ProcessRouterMap... after 11 ms
[  +18 ms] > hvigor Finished :flutter_blue_plus:default@ProcessStartupConfig... after 10 ms
[   +9 ms] > hvigor Finished :iamgeqr_flutter_plugin:default@BuildNativeWithCmake... after 1 ms
[  +27 ms] > hvigor Finished :iamgeqr_flutter_plugin:default@ProcessProfile... after 15 ms
[  +32 ms] > hvigor Finished :iamgeqr_flutter_plugin:default@ProcessRouterMap... after 14 ms
[  +20 ms] > hvigor Finished :iamgeqr_flutter_plugin:default@ProcessStartupConfig... after 8 ms
[  +11 ms] > hvigor Finished :image_picker_ohos:default@BuildNativeWithCmake... after 1 ms
[  +16 ms] > hvigor Finished :image_picker_ohos:default@ProcessProfile... after 6 ms
[  +27 ms] > hvigor Finished :image_picker_ohos:default@ProcessRouterMap... after 11 ms
[  +20 ms] > hvigor Finished :image_picker_ohos:default@ProcessStartupConfig... after 9 ms
[  +13 ms] > hvigor Finished :path_provider_ohos:default@BuildNativeWithCmake... after 4 ms
[  +16 ms] > hvigor Finished :path_provider_ohos:default@ProcessProfile... after 6 ms
[  +25 ms] > hvigor Finished :path_provider_ohos:default@ProcessRouterMap... after 13 ms
[  +17 ms] > hvigor Finished :path_provider_ohos:default@ProcessStartupConfig... after 8 ms
[  +12 ms] > hvigor Finished :shared_preferences_ohos:default@BuildNativeWithCmake... after 1 ms
[  +27 ms] > hvigor Finished :shared_preferences_ohos:default@ProcessProfile... after 16 ms
[  +36 ms] > hvigor Finished :shared_preferences_ohos:default@ProcessRouterMap... after 14 ms
[  +18 ms] > hvigor Finished :shared_preferences_ohos:default@ProcessStartupConfig... after 8 ms
[  +15 ms] > hvigor Finished :url_launcher_ohos:default@BuildNativeWithCmake... after 1 ms
[  +17 ms] > hvigor Finished :url_launcher_ohos:default@ProcessProfile... after 6 ms
[  +28 ms] > hvigor Finished :url_launcher_ohos:default@ProcessRouterMap... after 11 ms
[  +20 ms] > hvigor Finished :url_launcher_ohos:default@ProcessStartupConfig... after 9 ms
[  +11 ms] > hvigor Finished :webview_flutter_ohos:default@BuildNativeWithCmake... after 1 ms
[  +13 ms] > hvigor Finished :webview_flutter_ohos:default@ProcessProfile... after 5 ms
[  +24 ms] > hvigor Finished :webview_flutter_ohos:default@ProcessRouterMap... after 9 ms
[  +17 ms] > hvigor Finished :webview_flutter_ohos:default@ProcessStartupConfig... after 8 ms
[  +12 ms] > hvigor Finished :permission_handler_ohos:default@BuildNativeWithCmake... after 1 ms
[  +15 ms] > hvigor Finished :permission_handler_ohos:default@ProcessProfile... after 6 ms
[  +24 ms] > hvigor Finished :permission_handler_ohos:default@ProcessRouterMap... after 12 ms
[  +18 ms] > hvigor Finished :permission_handler_ohos:default@ProcessStartupConfig... after 9 ms
[  +13 ms] > hvigor Finished :package_info_plus:default@BuildNativeWithCmake... after 1 ms
[  +18 ms] > hvigor Finished :package_info_plus:default@ProcessProfile... after 5 ms
[  +25 ms] > hvigor Finished :package_info_plus:default@ProcessRouterMap... after 10 ms
[  +19 ms] > hvigor Finished :package_info_plus:default@ProcessStartupConfig... after 11 ms
[   +9 ms] > hvigor Finished :device_info_plus:default@BuildNativeWithCmake... after 1 ms
[  +15 ms] > hvigor Finished :device_info_plus:default@ProcessProfile... after 8 ms
[  +24 ms] > hvigor Finished :device_info_plus:default@ProcessRouterMap... after 10 ms
[  +19 ms] > hvigor Finished :device_info_plus:default@ProcessStartupConfig... after 8 ms
[   +9 ms] > hvigor Finished :connectivity_plus:default@BuildNativeWithCmake... after 1 ms
[  +17 ms] > hvigor Finished :connectivity_plus:default@ProcessProfile... after 8 ms
[  +25 ms] > hvigor Finished :connectivity_plus:default@ProcessRouterMap... after 10 ms
[  +16 ms] > hvigor Finished :connectivity_plus:default@ProcessStartupConfig... after 8 ms
[   +9 ms] > hvigor Finished :mobile_scanner:default@BuildNativeWithCmake... after 1 ms
[  +17 ms] > hvigor Finished :mobile_scanner:default@ProcessProfile... after 6 ms
[  +29 ms] > hvigor Finished :mobile_scanner:default@ProcessRouterMap... after 15 ms
[  +18 ms] > hvigor Finished :mobile_scanner:default@ProcessStartupConfig... after 8 ms
[  +12 ms] > hvigor Finished :open_app_settings:default@BuildNativeWithCmake... after 1 ms
[  +21 ms] > hvigor Finished :open_app_settings:default@ProcessProfile... after 8 ms
[  +31 ms] > hvigor Finished :open_app_settings:default@ProcessRouterMap... after 11 ms
[  +20 ms] > hvigor Finished :open_app_settings:default@ProcessStartupConfig... after 10 ms
[  +13 ms] > hvigor Finished :fluwx:default@BuildNativeWithCmake... after 1 ms
[  +18 ms] > hvigor Finished :fluwx:default@ProcessProfile... after 7 ms
[  +30 ms] > hvigor Finished :fluwx:default@ProcessRouterMap... after 15 ms
[  +20 ms] > hvigor Finished :fluwx:default@ProcessStartupConfig... after 11 ms
[   +9 ms] > hvigor Finished :camera_ohos:default@BuildNativeWithCmake... after 1 ms
[  +15 ms] > hvigor Finished :camera_ohos:default@ProcessProfile... after 8 ms
[  +26 ms] > hvigor Finished :camera_ohos:default@ProcessRouterMap... after 11 ms
[  +17 ms] > hvigor Finished :camera_ohos:default@ProcessStartupConfig... after 8 ms
[  +15 ms] > hvigor Finished :flutter_blue_plus:default@BuildNativeWithNinja... after 3 ms
[  +24 ms] > hvigor UP-TO-DATE :flutter_blue_plus:default@ProcessResource...
[  +93 ms] > hvigor UP-TO-DATE :flutter_blue_plus:default@GenerateLoaderJson...
[  +13 ms] > hvigor Finished :iamgeqr_flutter_plugin:default@BuildNativeWithNinja... after 2 ms
[  +31 ms] > hvigor UP-TO-DATE :iamgeqr_flutter_plugin:default@ProcessResource...
[  +60 ms] > hvigor UP-TO-DATE :iamgeqr_flutter_plugin:default@GenerateLoaderJson...
[  +15 ms] > hvigor Finished :image_picker_ohos:default@BuildNativeWithNinja... after 2 ms
[  +23 ms] > hvigor UP-TO-DATE :image_picker_ohos:default@ProcessResource...
[  +60 ms] > hvigor UP-TO-DATE :image_picker_ohos:default@GenerateLoaderJson...
[  +15 ms] > hvigor Finished :path_provider_ohos:default@BuildNativeWithNinja... after 2 ms
[  +25 ms] > hvigor UP-TO-DATE :path_provider_ohos:default@ProcessResource...
[  +55 ms] > hvigor UP-TO-DATE :path_provider_ohos:default@GenerateLoaderJson...
[  +13 ms] > hvigor Finished :shared_preferences_ohos:default@BuildNativeWithNinja... after 2 ms
[  +26 ms] > hvigor UP-TO-DATE :shared_preferences_ohos:default@ProcessResource...
[  +52 ms] > hvigor UP-TO-DATE :shared_preferences_ohos:default@GenerateLoaderJson...
[  +12 ms] > hvigor Finished :url_launcher_ohos:default@BuildNativeWithNinja... after 3 ms
[  +28 ms] > hvigor UP-TO-DATE :url_launcher_ohos:default@ProcessResource...
[  +54 ms] > hvigor UP-TO-DATE :url_launcher_ohos:default@GenerateLoaderJson...
[  +14 ms] > hvigor Finished :webview_flutter_ohos:default@BuildNativeWithNinja... after 2 ms
[  +27 ms] > hvigor UP-TO-DATE :webview_flutter_ohos:default@ProcessResource...
[  +67 ms] > hvigor UP-TO-DATE :webview_flutter_ohos:default@GenerateLoaderJson...
[  +12 ms] > hvigor Finished :permission_handler_ohos:default@BuildNativeWithNinja... after 2 ms
[  +23 ms] > hvigor UP-TO-DATE :permission_handler_ohos:default@ProcessResource...
[  +66 ms] > hvigor UP-TO-DATE :permission_handler_ohos:default@GenerateLoaderJson...
[  +15 ms] > hvigor Finished :package_info_plus:default@BuildNativeWithNinja... after 2 ms
[  +26 ms] > hvigor UP-TO-DATE :package_info_plus:default@ProcessResource...
[  +50 ms] > hvigor UP-TO-DATE :package_info_plus:default@GenerateLoaderJson...
[  +13 ms] > hvigor Finished :device_info_plus:default@BuildNativeWithNinja... after 2 ms
[  +20 ms] > hvigor UP-TO-DATE :device_info_plus:default@ProcessResource...
[  +73 ms] > hvigor UP-TO-DATE :device_info_plus:default@GenerateLoaderJson...
[  +15 ms] > hvigor Finished :connectivity_plus:default@BuildNativeWithNinja... after 3 ms
[  +35 ms] > hvigor UP-TO-DATE :connectivity_plus:default@ProcessResource...
[  +59 ms] > hvigor UP-TO-DATE :connectivity_plus:default@GenerateLoaderJson...
[  +16 ms] > hvigor Finished :mobile_scanner:default@BuildNativeWithNinja... after 2 ms
[  +24 ms] > hvigor UP-TO-DATE :mobile_scanner:default@ProcessResource...
[  +54 ms] > hvigor UP-TO-DATE :mobile_scanner:default@GenerateLoaderJson...
[  +12 ms] > hvigor Finished :open_app_settings:default@BuildNativeWithNinja... after 3 ms
[  +31 ms] > hvigor UP-TO-DATE :open_app_settings:default@ProcessResource...
[  +48 ms] > hvigor UP-TO-DATE :open_app_settings:default@GenerateLoaderJson...
[  +13 ms] > hvigor Finished :fluwx:default@BuildNativeWithNinja... after 4 ms
[  +22 ms] > hvigor UP-TO-DATE :fluwx:default@ProcessResource...
[  +50 ms] > hvigor UP-TO-DATE :fluwx:default@GenerateLoaderJson...
[  +13 ms] > hvigor Finished :camera_ohos:default@BuildNativeWithNinja... after 2 ms
[  +24 ms] > hvigor UP-TO-DATE :camera_ohos:default@ProcessResource...
[  +78 ms] > hvigor UP-TO-DATE :camera_ohos:default@GenerateLoaderJson...
[  +26 ms] > hvigor Finished :flutter_blue_plus:default@ProcessLibs... after 17 ms
[ +666 ms] > hvigor Finished :flutter_blue_plus:default@CompileResource... after 649 ms
[  +19 ms] > hvigor Finished :iamgeqr_flutter_plugin:default@ProcessLibs... after 9 ms
[ +516 ms] > hvigor Finished :iamgeqr_flutter_plugin:default@CompileResource... after 500 ms
[  +21 ms] > hvigor Finished :image_picker_ohos:default@ProcessLibs... after 10 ms
[ +548 ms] > hvigor Finished :image_picker_ohos:default@CompileResource... after 522 ms
[  +22 ms] > hvigor Finished :path_provider_ohos:default@ProcessLibs... after 10 ms
[ +524 ms] > hvigor Finished :path_provider_ohos:default@CompileResource... after 507 ms
[  +19 ms] > hvigor Finished :shared_preferences_ohos:default@ProcessLibs... after 9 ms
[ +504 ms] > hvigor Finished :shared_preferences_ohos:default@CompileResource... after 486 ms
[  +18 ms] > hvigor Finished :url_launcher_ohos:default@ProcessLibs... after 9 ms
[ +550 ms] > hvigor Finished :url_launcher_ohos:default@CompileResource... after 526 ms
[  +24 ms] > hvigor Finished :webview_flutter_ohos:default@ProcessLibs... after 13 ms
[ +653 ms] > hvigor Finished :webview_flutter_ohos:default@CompileResource... after 630 ms
[  +21 ms] > hvigor Finished :permission_handler_ohos:default@ProcessLibs... after 11 ms
[ +579 ms] > hvigor Finished :permission_handler_ohos:default@CompileResource... after 562 ms
[  +21 ms] > hvigor Finished :package_info_plus:default@ProcessLibs... after 10 ms
[ +514 ms] > hvigor Finished :package_info_plus:default@CompileResource... after 496 ms
[  +20 ms] > hvigor Finished :device_info_plus:default@ProcessLibs... after 10 ms
[ +513 ms] > hvigor Finished :device_info_plus:default@CompileResource... after 496 ms
[  +19 ms] > hvigor Finished :connectivity_plus:default@ProcessLibs... after 10 ms
[ +502 ms] > hvigor Finished :connectivity_plus:default@CompileResource... after 485 ms
[  +19 ms] > hvigor Finished :mobile_scanner:default@ProcessLibs... after 9 ms
[ +536 ms] > hvigor Finished :mobile_scanner:default@CompileResource... after 520 ms
[  +28 ms] > hvigor Finished :open_app_settings:default@ProcessLibs... after 14 ms
[ +521 ms] > hvigor Finished :open_app_settings:default@CompileResource... after 504 ms
[  +18 ms] > hvigor Finished :fluwx:default@ProcessLibs... after 9 ms
[ +523 ms] > hvigor Finished :fluwx:default@CompileResource... after 506 ms
[  +22 ms] > hvigor Finished :camera_ohos:default@ProcessLibs... after 12 ms
[ +533 ms] > hvigor Finished :camera_ohos:default@CompileResource... after 512 ms
[  +15 ms] > hvigor UP-TO-DATE :flutter_blue_plus:default@DoNativeStrip...
[  +12 ms] > hvigor Finished :flutter_blue_plus:default@ProcessObfuscationFiles... after 1 ms
[ +177 ms] > hvigor UP-TO-DATE :iamgeqr_flutter_plugin:default@DoNativeStrip...
[  +16 ms] > hvigor Finished :iamgeqr_flutter_plugin:default@ProcessObfuscationFiles... after 1 ms
[ +153 ms] > hvigor UP-TO-DATE :image_picker_ohos:default@DoNativeStrip...
[  +24 ms] > hvigor Finished :image_picker_ohos:default@ProcessObfuscationFiles... after 1 ms
[ +170 ms] > hvigor UP-TO-DATE :path_provider_ohos:default@DoNativeStrip...
[  +17 ms] > hvigor Finished :path_provider_ohos:default@ProcessObfuscationFiles... after 1 ms
[ +182 ms] > hvigor UP-TO-DATE :shared_preferences_ohos:default@DoNativeStrip...
[  +15 ms] > hvigor Finished :shared_preferences_ohos:default@ProcessObfuscationFiles... after 1 ms
[ +193 ms] > hvigor UP-TO-DATE :url_launcher_ohos:default@DoNativeStrip...
[  +15 ms] > hvigor Finished :url_launcher_ohos:default@ProcessObfuscationFiles... after 1 ms
[ +218 ms] > hvigor UP-TO-DATE :webview_flutter_ohos:default@DoNativeStrip...
[  +26 ms] > hvigor Finished :webview_flutter_ohos:default@ProcessObfuscationFiles... after 1 ms
[ +258 ms] > hvigor UP-TO-DATE :permission_handler_ohos:default@DoNativeStrip...
[  +21 ms] > hvigor Finished :permission_handler_ohos:default@ProcessObfuscationFiles... after 1 ms
[ +198 ms] > hvigor UP-TO-DATE :package_info_plus:default@DoNativeStrip...
[  +64 ms] > hvigor Finished :package_info_plus:default@ProcessObfuscationFiles... after 1 ms
[ +217 ms] > hvigor UP-TO-DATE :device_info_plus:default@DoNativeStrip...
[  +19 ms] > hvigor Finished :device_info_plus:default@ProcessObfuscationFiles... after 1 ms
[ +210 ms] > hvigor UP-TO-DATE :connectivity_plus:default@DoNativeStrip...
[  +21 ms] > hvigor Finished :connectivity_plus:default@ProcessObfuscationFiles... after 1 ms
[ +165 ms] > hvigor UP-TO-DATE :mobile_scanner:default@DoNativeStrip...
[  +14 ms] > hvigor Finished :mobile_scanner:default@ProcessObfuscationFiles... after 1 ms
[ +177 ms] > hvigor UP-TO-DATE :open_app_settings:default@DoNativeStrip...
[  +17 ms] > hvigor Finished :open_app_settings:default@ProcessObfuscationFiles... after 1 ms
[ +171 ms] > hvigor UP-TO-DATE :fluwx:default@DoNativeStrip...
[  +19 ms] > hvigor Finished :fluwx:default@ProcessObfuscationFiles... after 1 ms
[ +156 ms] > hvigor UP-TO-DATE :camera_ohos:default@DoNativeStrip...
[  +12 ms] > hvigor Finished :camera_ohos:default@ProcessObfuscationFiles... after 1 ms
[ +177 ms] > hvigor UP-TO-DATE :flutter_blue_plus:default@CacheNativeLibs...
[  +20 ms] > hvigor UP-TO-DATE :iamgeqr_flutter_plugin:default@CacheNativeLibs...
[  +22 ms] > hvigor UP-TO-DATE :image_picker_ohos:default@CacheNativeLibs...
[  +21 ms] > hvigor UP-TO-DATE :path_provider_ohos:default@CacheNativeLibs...
[  +23 ms] > hvigor UP-TO-DATE :shared_preferences_ohos:default@CacheNativeLibs...
[  +20 ms] > hvigor UP-TO-DATE :url_launcher_ohos:default@CacheNativeLibs...
[  +22 ms] > hvigor UP-TO-DATE :webview_flutter_ohos:default@CacheNativeLibs...
[  +22 ms] > hvigor UP-TO-DATE :permission_handler_ohos:default@CacheNativeLibs...
[  +20 ms] > hvigor UP-TO-DATE :package_info_plus:default@CacheNativeLibs...
[  +19 ms] > hvigor UP-TO-DATE :device_info_plus:default@CacheNativeLibs...
[  +22 ms] > hvigor UP-TO-DATE :connectivity_plus:default@CacheNativeLibs...
[  +21 ms] > hvigor UP-TO-DATE :mobile_scanner:default@CacheNativeLibs...
[  +22 ms] > hvigor UP-TO-DATE :open_app_settings:default@CacheNativeLibs...
[  +24 ms] > hvigor UP-TO-DATE :fluwx:default@CacheNativeLibs...
[  +24 ms] > hvigor UP-TO-DATE :camera_ohos:default@CacheNativeLibs...
[+29448 ms] > hvigor ERROR: Failed :path_provider_ohos:default@HarCompileArkTS...
[   +6 ms] > hvigor ERROR: EBUSY: resource busy or locked, open 'D:\wulingFlutter\wuling-flutter-app\ohos\.hvigor\outputs\build-logs\build.log'
[        ] > hvigor ERROR: Error: EBUSY: resource busy or locked, open 'D:\wulingFlutter\wuling-flutter-app\ohos\.hvigor\outputs\build-logs\build.log'
[        ]     at Object.openSync (node:fs:596:3)
[        ]     at Object.writeFileSync (node:fs:2322:35)
[        ]     at Object.appendFileSync (node:fs:2384:6)
[        ]     at RollingFileStream._renewWriteStream (D:\Huawei\DevEco Studio\tools\hvigor\hvigor\node_modules\streamroller\lib\RollingFileWriteStream.js:312:8)
[        ]     at RollingFileStream._moveOldFiles (D:\Huawei\DevEco Studio\tools\hvigor\hvigor\node_modules\streamroller\lib\RollingFileWriteStream.js:232:10)
[        ]     at async RollingFileStream._shouldRoll (D:\Huawei\DevEco Studio\tools\hvigor\hvigor\node_modules\streamroller\lib\RollingFileWriteStream.js:170:7)
[  +35 ms] > hvigor ERROR: BUILD FAILED in 55 s 106 ms
[+7259 ms] > hvigor WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/plugin/common/Any.e
ts:7:20
[   +1 ms]  Usage of 'ESObject' type is restricted (arkts-limited-esobj)
[        ]
[        ] WARN: ArkTS:WARN File:
D:/PUB/git/flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb/packages/image_picker/image_picker_ohos/ohos/src/main/ets/image_picker/Messages.ets:573:24
[   +1 ms]  Usage of 'ESObject' type is restricted (arkts-limited-esobj)
[        ]
[        ] WARN: ArkTS:WARN File:
D:/PUB/git/flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb/packages/image_picker/image_picker_ohos/ohos/src/main/ets/image_picker/Messages.ets:573:43
[        ]  Usage of 'ESObject' type is restricted (arkts-limited-esobj)
[        ]
[        ] WARN: ArkTS:WARN File:
D:/PUB/git/flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb/packages/image_picker/image_picker_ohos/ohos/src/main/ets/image_picker/Messages.ets:584:9
[        ]  Usage of 'ESObject' type is restricted (arkts-limited-esobj)
[        ]
[        ] WARN: ArkTS:WARN File:
D:/PUB/git/flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb/packages/image_picker/image_picker_ohos/ohos/src/main/ets/image_picker/ImagePickerCache.ets:189:78
[        ]  Usage of 'ESObject' type is restricted (arkts-limited-esobj)
[        ]
[        ] WARN: ArkTS:WARN File:
D:/PUB/git/flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb/packages/image_picker/image_picker_ohos/ohos/src/main/ets/image_picker/ImagePickerCache.ets:189:150
[        ]  Usage of 'ESObject' type is restricted (arkts-limited-esobj)
[        ]
[        ] WARN: ArkTS:WARN File:
D:/PUB/git/flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb/packages/image_picker/image_picker_ohos/ohos/src/main/ets/image_picker/ImagePickerDelegate.ets:370:36
[        ]  Argument of type 'string | null' is not assignable to parameter of type 'string'.
[        ]   Type 'null' is not assignable to type 'string'.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/PUB/git/flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb/packages/image_picker/image_picker_ohos/ohos/src/main/ets/image_picker/ImagePickerDelegate.ets:92:35
[        ]  Usage of 'ESObject' type is restricted (arkts-limited-esobj)
[        ]
[        ] WARN: ArkTS:WARN File:
D:/PUB/git/flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb/packages/image_picker/image_picker_ohos/ohos/src/main/ets/image_picker/ImagePickerDelegate.ets:438:53
[        ]  Usage of 'ESObject' type is restricted (arkts-limited-esobj)
[        ]
[        ] WARN: ArkTS:WARN File:
D:/PUB/git/flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb/packages/image_picker/image_picker_ohos/ohos/src/main/ets/image_picker/ImagePickerDelegate.ets:438:101
[        ]  Usage of 'ESObject' type is restricted (arkts-limited-esobj)
[        ]
[        ] WARN: ArkTS:WARN: For details about ArkTS syntax errors, see FAQs
[        ] WARN: ArkTS:WARN File:
D:/PUB/git/flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb/packages/image_picker/image_picker_ohos/ohos/src/main/ets/image_picker/ExifDataCopier.ets:39:26
[        ]  'getImageProperty' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/PUB/git/flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb/packages/image_picker/image_picker_ohos/ohos/src/main/ets/image_picker/ExifDataCopier.ets:41:30
[        ]  'modifyImageProperty' has been deprecated.
[        ]
[   +2 ms] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/util/StringUtils.et
s:7:21
[        ]  Currently module for 'libflutter.so' is not verified. If you're importing napi, its verification will be enabled in later SDK version. Please make sure the corresponding
.d.ts file is provided and the napis are correctly declared.
[   +1 ms]
[   +3 ms] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/util/ByteBuffer.ets
:745:20
[   +2 ms]  'decode' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/util/ByteBuffer.ets
:781:30
[   +1 ms]  'encodeInto' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/PUB/git/flutter_packages-056bced4b677ed4f168b245823570c40fe55bddb/packages/image_picker/image_picker_ohos/ohos/src/main/ets/image_picker/ImageResizer.ets:131:45
[        ]  'packing' has been deprecated.
[        ]
[   +3 ms] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/view/DynamicView/dy
namicView.ets:164:6
[        ]  'clip' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/view/DynamicView/dy
namicView.ets:169:6
[        ]  'clip' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/engine/sy
stemchannels/AccessibilityChannel.ets:20:21
[        ]  Currently module for 'libflutter.so' is not verified. If you're importing napi, its verification will be enabled in later SDK version. Please make sure the corresponding
.d.ts file is provided and the napis are correctly declared.
[   +1 ms]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/engine/sy
stemchannels/TextInputChannel.ets:455:23
[        ]  'getContext' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:112:44
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:113:44
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:114:43
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:115:43
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:116:33
[        ]  'screenX' has been deprecated.
[        ]
[   +2 ms] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:116:43
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:116:73
[        ]  'screenX' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:117:33
[        ]  'screenY' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:117:43
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:117:73
[        ]  'screenY' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:118:37
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:119:37
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:125:37
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:126:37
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:127:36
[        ]  'vp2px' has been deprecated.
[   +2 ms]
[   +7 ms] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:128:36
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:129:26
[        ]  'screenX' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:129:36
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:129:59
[        ]  'screenX' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:130:26
[   +3 ms]  'screenY' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:130:36
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:130:59
[        ]  'screenY' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:131:30
[        ]  'vp2px' has been deprecated.
[   +1 ms]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:132:30
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/view/FlutterView.et
s:420:14
[        ]  'px2vp' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/view/FlutterView.et
s:421:15
[        ]  'px2vp' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/view/FlutterView.et
s:508:44
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/view/FlutterView.et
s:509:45
[        ]  'vp2px' has been deprecated.
[   +2 ms]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Flut
terManager.ets:206:24
[        ]  'getContext' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/engine/Fl
utterNapi.ets:7:21
[        ]  Currently module for 'libflutter.so' is not verified. If you're importing napi, its verification will be enabled in later SDK version. Please make sure the corresponding
.d.ts file is provided and the napis are correctly declared.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/plugin/platform/Pla
tformViewsController.ets:435:23
[        ]  'px2vp' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/plugin/PlatformPlug
in.ets:122:18
[        ]  To use this API, you need to apply for the permissions: ohos.permission.VIBRATE
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/plugin/PlatformPlug
in.ets:126:18
[        ]  To use this API, you need to apply for the permissions: ohos.permission.VIBRATE
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/plugin/PlatformPlug
in.ets:130:18
[        ]  To use this API, you need to apply for the permissions: ohos.permission.VIBRATE
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/plugin/PlatformPlug
in.ets:134:18
[   +1 ms]  To use this API, you need to apply for the permissions: ohos.permission.VIBRATE
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/plugin/PlatformPlug
in.ets:138:18
[        ]  To use this API, you need to apply for the permissions: ohos.permission.VIBRATE
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/plugin/PlatformPlug
in.ets:199:12
[        ]  'back' has been deprecated.
[        ]
[   +2 ms] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/plugin/PlatformPlug
in.ets:223:28
[   +4 ms]  To use this API, you need to apply for the permissions: ohos.permission.READ_PASTEBOARD
[   +6 ms]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/engine/re
nderer/FlutterRenderer.ets:76:47
[        ]  'createImageReceiver' has been deprecated.
[        ]
[        ]
[   +1 ms] > hvigor WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/plugin/common/Any.e
ts:7:20
[   +1 ms]  Usage of 'ESObject' type is restricted (arkts-limited-esobj)
[        ]
[        ] WARN: ArkTS:WARN: For details about ArkTS syntax errors, see FAQs
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/util/StringUtils.et
s:7:21
[        ]  Currently module for 'libflutter.so' is not verified. If you're importing napi, its verification will be enabled in later SDK version. Please make sure the corresponding
.d.ts file is provided and the napis are correctly declared.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/util/ByteBuffer.ets
:745:20
[        ]  'decode' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/util/ByteBuffer.ets
:781:30
[        ]  'encodeInto' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/view/DynamicView/dy
namicView.ets:164:6
[   +1 ms]  'clip' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/view/DynamicView/dy
namicView.ets:169:6
[        ]  'clip' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/engine/sy
stemchannels/AccessibilityChannel.ets:20:21
[        ]  Currently module for 'libflutter.so' is not verified. If you're importing napi, its verification will be enabled in later SDK version. Please make sure the corresponding
.d.ts file is provided and the napis are correctly declared.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Flut
terManager.ets:206:24
[        ]  'getContext' has been deprecated.
[   +2 ms]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/engine/sy
stemchannels/TextInputChannel.ets:455:23
[   +1 ms]  'getContext' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:112:44
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:113:44
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:114:43
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:115:43
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:116:33
[        ]  'screenX' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:116:43
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:116:73
[        ]  'screenX' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:117:33
[        ]  'screenY' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:117:43
[   +1 ms]  'vp2px' has been deprecated.
[   +3 ms]
[   +5 ms] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:117:73
[   +1 ms]  'screenY' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:118:37
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:119:37
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:125:37
[        ]  'vp2px' has been deprecated.
[   +3 ms]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:126:37
[   +1 ms]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:127:36
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:128:36
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:129:26
[        ]  'screenX' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:129:36
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:129:59
[        ]  'screenX' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:130:26
[        ]  'screenY' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:130:36
[   +1 ms]  'vp2px' has been deprecated.
[   +3 ms]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:130:59
[        ]  'screenY' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:131:30
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:132:30
[        ]  'vp2px' has been deprecated.
[   +1 ms]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/view/FlutterView.et
s:420:14
[   +1 ms]  'px2vp' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/view/FlutterView.et
s:421:15
[   +1 ms]  'px2vp' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/view/FlutterView.et
s:508:44
[        ]  'vp2px' has been deprecated.
[   +4 ms]
[   +5 ms] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/view/FlutterView.et
s:509:45
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/plugin/platform/Pla
tformViewsController.ets:435:23
[        ]  'px2vp' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/plugin/PlatformPlug
in.ets:122:18
[        ]  To use this API, you need to apply for the permissions: ohos.permission.VIBRATE
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/plugin/PlatformPlug
in.ets:126:18
[   +2 ms]  To use this API, you need to apply for the permissions: ohos.permission.VIBRATE
[   +1 ms]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/plugin/PlatformPlug
in.ets:130:18
[        ]  To use this API, you need to apply for the permissions: ohos.permission.VIBRATE
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/plugin/PlatformPlug
in.ets:134:18
[        ]  To use this API, you need to apply for the permissions: ohos.permission.VIBRATE
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/plugin/PlatformPlug
in.ets:138:18
[        ]  To use this API, you need to apply for the permissions: ohos.permission.VIBRATE
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/plugin/PlatformPlug
in.ets:199:12
[   +1 ms]  'back' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/plugin/PlatformPlug
in.ets:223:28
[        ]  To use this API, you need to apply for the permissions: ohos.permission.READ_PASTEBOARD
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/engine/re
nderer/FlutterRenderer.ets:76:47
[        ]  'createImageReceiver' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/engine/Fl
utterNapi.ets:7:21
[        ]  Currently module for 'libflutter.so' is not verified. If you're importing napi, its verification will be enabled in later SDK version. Please make sure the corresponding
.d.ts file is provided and the napis are correctly declared.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Flut
terPage.ets:11:21
[        ]  Currently module for 'libflutter.so' is not verified. If you're importing napi, its verification will be enabled in later SDK version. Please make sure the corresponding
.d.ts file is provided and the napis are correctly declared.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Flut
terPage.ets:287:5
[   +2 ms]  'getContext' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Flut
terPage.ets:294:5
[        ]  'getContext' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_image_scanner-b9f20a88ca1c738c700a15655b8fab4c305c4d32/ohos/src/main/ets/components/plugin/IamgeqrFlutterPlugin.ets:51:19
[        ]  The system capacity of this api 'scanCore' is not supported on all devices
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_image_scanner-b9f20a88ca1c738c700a15655b8fab4c305c4d32/ohos/src/main/ets/components/plugin/IamgeqrFlutterPlugin.ets:51:28
[        ]  The system capacity of this api 'ScanType' is not supported on all devices
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_image_scanner-b9f20a88ca1c738c700a15655b8fab4c305c4d32/ohos/src/main/ets/components/plugin/IamgeqrFlutterPlugin.ets:51:37
[        ]  The system capacity of this api 'ALL' is not supported on all devices
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_image_scanner-b9f20a88ca1c738c700a15655b8fab4c305c4d32/ohos/src/main/ets/components/plugin/IamgeqrFlutterPlugin.ets:64:9
[        ]  The system capacity of this api 'detectBarcode' is not supported on all devices
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_image_scanner-b9f20a88ca1c738c700a15655b8fab4c305c4d32/ohos/src/main/ets/components/plugin/IamgeqrFlutterPlugin.ets:64:23
[        ]  The system capacity of this api 'decode' is not supported on all devices
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_image_scanner-b9f20a88ca1c738c700a15655b8fab4c305c4d32/ohos/src/main/ets/components/plugin/IamgeqrFlutterPlugin.ets:67:48
[        ]  The system capacity of this api 'originalValue' is not supported on all devices
[        ]
[        ]
[        ] > hvigor WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/plugin/common/Any.e
ts:7:20
[        ]  Usage of 'ESObject' type is restricted (arkts-limited-esobj)
[        ]
[        ] WARN: ArkTS:WARN: For details about ArkTS syntax errors, see FAQs
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/util/StringUtils.et
s:7:21
[        ]  Currently module for 'libflutter.so' is not verified. If you're importing napi, its verification will be enabled in later SDK version. Please make sure the corresponding
.d.ts file is provided and the napis are correctly declared.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/util/ByteBuffer.ets
:745:20
[        ]  'decode' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/util/ByteBuffer.ets
:781:30
[        ]  'encodeInto' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/view/DynamicView/dy
namicView.ets:164:6
[   +1 ms]  'clip' has been deprecated.
[   +2 ms]
[   +5 ms] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/view/DynamicView/dy
namicView.ets:169:6
[        ]  'clip' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/engine/sy
stemchannels/AccessibilityChannel.ets:20:21
[        ]  Currently module for 'libflutter.so' is not verified. If you're importing napi, its verification will be enabled in later SDK version. Please make sure the corresponding
.d.ts file is provided and the napis are correctly declared.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Flut
terManager.ets:206:24
[        ]  'getContext' has been deprecated.
[  +11 ms]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/engine/sy
stemchannels/TextInputChannel.ets:455:23
[        ]  'getContext' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:112:44
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:113:44
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:114:43
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:115:43
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:116:33
[        ]  'screenX' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:116:43
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:116:73
[   +1 ms]  'screenX' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:117:33
[        ]  'screenY' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:117:43
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:117:73
[        ]  'screenY' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:118:37
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:119:37
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:125:37
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:126:37
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:127:36
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:128:36
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:129:26
[        ]  'screenX' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:129:36
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:129:59
[        ]  'screenX' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:130:26
[        ]  'screenY' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:130:36
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:130:59
[        ]  'screenY' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:131:30
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Embe
ddingNodeController.ets:132:30
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/view/FlutterView.et
s:420:14
[        ]  'px2vp' has been deprecated.
[   +2 ms]
[   +6 ms] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/view/FlutterView.et
s:421:15
[        ]  'px2vp' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/view/FlutterView.et
s:508:44
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/view/FlutterView.et
s:509:45
[        ]  'vp2px' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/plugin/platform/Pla
tformViewsController.ets:435:23
[        ]  'px2vp' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/plugin/PlatformPlug
in.ets:122:18
[        ]  To use this API, you need to apply for the permissions: ohos.permission.VIBRATE
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/plugin/PlatformPlug
in.ets:126:18
[        ]  To use this API, you need to apply for the permissions: ohos.permission.VIBRATE
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/plugin/PlatformPlug
in.ets:130:18
[        ]  To use this API, you need to apply for the permissions: ohos.permission.VIBRATE
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/plugin/PlatformPlug
in.ets:134:18
[        ]  To use this API, you need to apply for the permissions: ohos.permission.VIBRATE
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/plugin/PlatformPlug
in.ets:138:18
[   +1 ms]  To use this API, you need to apply for the permissions: ohos.permission.VIBRATE
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/plugin/PlatformPlug
in.ets:199:12
[        ]  'back' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/plugin/PlatformPlug
in.ets:223:28
[        ]  To use this API, you need to apply for the permissions: ohos.permission.READ_PASTEBOARD
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/engine/re
nderer/FlutterRenderer.ets:76:47
[        ]  'createImageReceiver' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/engine/Fl
utterNapi.ets:7:21
[        ]  Currently module for 'libflutter.so' is not verified. If you're importing napi, its verification will be enabled in later SDK version. Please make sure the corresponding
.d.ts file is provided and the napis are correctly declared.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Flut
terPage.ets:11:21
[        ]  Currently module for 'libflutter.so' is not verified. If you're importing napi, its verification will be enabled in later SDK version. Please make sure the corresponding
.d.ts file is provided and the napis are correctly declared.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Flut
terPage.ets:287:5
[        ]  'getContext' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File:
D:/wulingFlutter/wuling-flutter-app/ohos/oh_modules/.ohpm/@ohos+flutter_ohos@m63y+ydlm4bvtxm893x9yka70iqs913fwbk+42+oh+a=/oh_modules/@ohos/flutter_ohos/src/main/ets/embedding/ohos/Flut
terPage.ets:294:5
[        ]  'getContext' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:51:30
[        ]  'getContext' has been deprecated.
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:97:16
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:168:46
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:191:20
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:212:22
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:256:17
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:264:19
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:266:19
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[   +2 ms]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:286:15
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:287:15
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:320:31
[   +3 ms]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:322:31
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:356:36
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:389:61
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:412:41
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:424:24
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:427:22
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:437:22
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:485:16
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:527:22
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:528:22
[   +1 ms]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[   +3 ms]
[   +3 ms] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:543:20
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:554:20
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:577:18
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:606:56
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:626:45
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:634:16
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:659:60
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:705:24
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:750:56
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:779:41
[   +2 ms]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:823:64
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:861:18
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:891:51
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:895:73
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:905:54
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:920:54
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:933:24
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:943:54
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:1217:29
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:1449:19
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:1460:15
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:1461:15
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ] WARN: ArkTS:WARN File: D:/PUB/git/flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0/ohos/src/main/ets/components/plugin/FlutterBluePlusPlugin.ets:1489:47
[        ]  To use this API, you need to apply for the permissions: ohos.permission.ACCESS_BLUETOOTH
[        ]
[        ]
[   +1 ms] Invoke error: hvigorw --mode module -p

module=flutter_blue_plus@default,iamgeqr_flutter_plugin@default,image_picker_ohos@default,path_provider_ohos@default,shared_preferences_ohos@default,url_launcher_ohos@default,webview_f

lutter_ohos@default,permission_handler_ohos@default,package_info_plus@default,device_info_plus@default,connectivity_plus@default,mobile_scanner@default,open_app_settings@default,fluwx@
           default,camera_ohos@default -p product=default assembleHar --no-daemon
[   +4 ms] Exception: Oops! assembleHars failed! please check log.
[   +4 ms] "flutter run" took 144,507ms.
[   +4 ms]
           #0      throwToolExit (package:flutter_tools/src/base/common.dart:10:3)
           #1      RunCommand.runCommand (package:flutter_tools/src/commands/run.dart:722:9)
           <asynchronous suspension>
           #2      FlutterCommand.run.<anonymous closure> (package:flutter_tools/src/runner/flutter_command.dart:1257:27)
           <asynchronous suspension>
           #3      AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:150:19)
           <asynchronous suspension>
           #4      CommandRunner.runCommand (package:args/command_runner.dart:209:13)
           <asynchronous suspension>
           #5      FlutterCommandRunner.runCommand.<anonymous closure> (package:flutter_tools/src/runner/flutter_command_runner.dart:283:9)
           <asynchronous suspension>
           #6      AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:150:19)
           <asynchronous suspension>
           #7      FlutterCommandRunner.runCommand (package:flutter_tools/src/runner/flutter_command_runner.dart:229:5)
           <asynchronous suspension>
           #8      run.<anonymous closure>.<anonymous closure> (package:flutter_tools/runner.dart:64:9)
           <asynchronous suspension>
           #9      AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:150:19)
           <asynchronous suspension>
           #10     main (package:flutter_tools/executable.dart:91:3)
           <asynchronous suspension>


[ +260 ms] ensureAnalyticsSent: 259ms
[   +1 ms] Running 1 shutdown hook
