import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/widgets/common/custom_text_indicator.dart';
import 'package:wuling_flutter_app/widgets/common/custom_page_view.dart';

import '../../utils/manager/log_manager.dart';
class PageViewPage extends StatefulWidget {
  @override
  _PageViewPageState createState() => _PageViewPageState();
}

class _PageViewPageState extends State<PageViewPage> {
  final List<String> titles = ['A', 'AB', 'ABC', 'ABCD', 'ABCDE'];
  late PageController _pageController;
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('PageView Demo')),
      body: Column(
        children: [
          TextIndicator(
            titles: titles,
            currentIndex: _currentPage,
            onTap: (index) {
              _pageController.animateToPage(
                index,
                duration: Duration(milliseconds: 300),
                curve: Curves.easeInOut,
              );
            },
            // itemWidth: 100, // 设置固定宽度，注释掉这行可以使用自适应宽度
            itemSpacing: 10,
          ),
          Container(
            height: 400,
            width: 300,
            child: PageView.builder(
              controller: _pageController,
              itemCount: titles.length,
              onPageChanged: (index) {
                setState(() => _currentPage = index);
              },
              itemBuilder: (context, index) {
                return Container(
                  color: Colors.blue,
                  child: Stack(
                    children: [
                      Positioned(
                        left:20,
                          child:
                          Text('Content of ${titles[index]}', style: TextStyle(fontSize: 24))),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}


class CustomPageViewPage extends StatefulWidget {
  const CustomPageViewPage({super.key});

  @override
  State<CustomPageViewPage> createState() => _CustomPageViewPageState();
}

class _CustomPageViewPageState extends State<CustomPageViewPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Custom PageView Example')),
      body: CustomPageViewExample(),
    );
  }
}


class CustomPageViewExample extends StatefulWidget {
  @override
  _CustomPageViewExampleState createState() => _CustomPageViewExampleState();
}

class _CustomPageViewExampleState extends State<CustomPageViewExample> {
  PageController _controller = PageController();
  bool _isLooping = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: CustomPageView(
            controller: _controller,
            itemCount: 5,
            allowLooping: _isLooping,
            itemBuilder: (context, index) {
              return Container(
                color: Colors.primaries[index % Colors.primaries.length],
                child: Center(
                  child: Text(
                    'Page $index',
                    style: TextStyle(fontSize: 32, color: Colors.white),
                  ),
                ),
              );
            },
            onPageChanged: (index) {
              LogManager().debug('Page changed to $index');
            },
          ),
        ),
        SwitchListTile(
          title: Text('Allow Looping'),
          value: _isLooping,
          onChanged: (value) {
            setState(() {
              _isLooping = value;
            });
          },
        ),
      ],
    );
  }
}