import List from '@ohos.util.List';
import { ChatroomInfo } from './chatroom/model/ChatroomInfo';
import { ChatroomJoinedInfo } from './chatroom/model/ChatroomJoinedInfo';
import { ConversationIdentifier } from './conversation/ConversationIdentifier';
import { InitOption } from './InitOption';
import { ConnectionStatus, ConversationType, DatabaseStatus, Order, PushNotificationLevel, ReceivedStatus } from './MacroDefine';
import { RecallNotificationMessage } from './message/content/normal/RecallNotificationMessage';
import { Message } from './message/Message';
import { MessageBlockInfo } from './message/model/MessageBlockInfo';
import { ReceivedInfo } from './message/model/ReceivedInfo';
import { ConversationStatusInfo } from './conversation/model/ConversationStatusInfo';
import { Conversation } from './conversation/Conversation';
import { IHistoryMessageOption, ICountOption, IGetLocalMsgByIdOption, IGetLocalMsgByTimeOption, IGetRemoteMsgOption, ISearchMessageInTimeRangeOption, ISendMsgOption } from './message/option/MessageOption';
import common from '@ohos.app.ability.common';
import { MessageContentConstructor, MessageFlag } from './message/content/MessageContent';
import { IGetConversationOption, IQuietHoursOption, ISetConversationTopOption } from './conversation/option/ConversationOption';
import { EngineError } from './EngineError';
import { IAsyncResult, IConnectResult } from './IResult';
import { ChatroomStatusListener } from './chatroom/listener/ChatroomStatusListener';
import { LogLevel } from './log/Log';
import { PublicServiceInfo } from './publicservice/model/PublicServiceInfo';
import { ChatroomKVStatusListener } from './chatroom/listener/ChatroomKVStatusListener';
import { ChatroomMemberActionListener } from './chatroom/listener/ChatroomMemberActionListener';
import { ChatroomNotifyEventListener } from './chatroom/listener/ChatroomNotifyEventListener';
import { TypingStatus } from './message/model/TypingStatus';
import { MessageExpansionListener } from './message/listener/MessageExpansionListener';
import { HashMap } from '@kit.ArkTS';
import { ConversationStatusListener } from './conversation/listener/ConversationStatusListener';
import { SyncConversationReadStatusListener } from './conversation/listener/SyncConversationReadStatusListener';
import { SearchConversationResult } from './conversation/model/SearchConversationResult';
import { MessageReceivedListener } from './message/listener/MessageReceivedListener';
import { ConnectionStatusListener } from './connection/listener/ConnectionStatusListener';
import { MessageDestructionListener } from './message/listener/MessageDestructionListener';
import { MessageReadReceiptListener } from './message/listener/MessageReadReceiptListener';
import { DatabaseStatusListener } from './connection/listener/DatabaseStatusListener';
import { MessageRecalledListener } from './message/listener/MessageRecalledListener';
import { MessageBlockedListener } from './message/listener/MessageBlockedListener';
import { TypingStatusListener } from './message/listener/TypingStatusListener';
import { MediaMessageTransfer } from './message/listener/MediaMessageTransfer';
import { MessageReadReceiptV2Listener } from './message/listener/MessageReadReceiptV2Listener';
import { MessageReadReceiptV5Listener } from './message/listener/MessageReadReceiptV5Listener';
import { ReadReceiptInfoV5 } from './message/model/ReadReceiptInfoV5';
import { ReadReceiptUsersResult } from './message/model/ReadReceiptUserByPageResult';
import { ReadReceiptUsersOption } from './message/model/ReadReceiptUsersOption';
import { HistoryMessageResult } from './message/model/HistoryMessageResult';
import { MessageIdentifier } from './message/option/MessageIdentifier';
/**
 * IM SDK 核心类
 * @version 1.0.0
 */
declare class IMEngine {
    private static engine;
    private impl;
    private constructor();
    /**
     * 单例对象
     * @version 1.0.0
     */
    static getInstance(): IMEngine;
    /**
     * 初始化 SDK
     * # 重要
     *```
     * 整个 App 生命周期内，原则上 SDK 只能初始化一次
     * SDK 允许调用该方法重复初始化 SDK，但是我们不建议这么做
     * 因为 SDK 重复初始化之后，之前的连接、监听等均会失效，需要将初始化后的操作重新做一遍，如重新连接，并重新设置各种监听
     *```
     * # 示例代码
     *```ts
     * // 在 UIAbility 中获取 context
     * let context = this.context
     * let initOption = new InitOption();
     * let appKey = "从融云后台获取的 appKey";
     * IMEngine.getInstance().init(context, appKey, initOption);
     *```
     * @param context 上下文，为空则初始化失败
     * @param appKey 融云 appKey，为空则初始化失败
     * @param initOption 初始化配置，见 InitOption
     * @version 1.0.0
     */
    init(context: common.Context, appKey: string, initOption: InitOption): void;
    /**
     * 获取 SDK 版本号
     * # 示例代码
     *```
     * let ver = IMEngine.getInstance().getVersion();
     *```
     * @returns 版本号
     * @version 1.0.0
     */
    getVersion(): string;
    /**
     * 获取本地时间与服务器时间的时间差。
     * 通过当前本地时间减去此接口返回的时间，即当前服务器时间。
     *
     * # 示例代码
     *```
     * IMEngine.getInstance().getDeltaTime().then(result => {
     *   if (EngineError.Success === result.code) {
     *     let deltaTime = result.data as number
     *   }
     * });
     *```
     * @returns 本地时间与服务器时间的时间差。
     * @version 1.3.2
     */
    getDeltaTime(): Promise<IAsyncResult<number>>;
    /**
     * 上报 APP 版本信息，服务端支持按上报的 App 版本处理自定义消息的推送内容
     * # 示例代码
     *```
     * let appVersion = "1.0.0";
     * IMEngine.getInstance().setAppVersion(appVersion);
     *```
     * @param ver APP 版本，string 类型，非空，长度小于 20, 示例如 "1.1.0"
     * @note 当 SDK 初始化时就会用该字段，所以必须在 init 之前调用
     * @version 1.0.0
     */
    setAppVersion(ver: string): void;
    /**
     * 上设置断线重连时是否踢出重连设备。
     * # 说明
     *```
     * 用户没有开通多设备登录功能的前提下，同一个账号在一台新设备上登录的时候，会把这个账号在之前登录的设备上踢出。
     * 由于 SDK 有断线重连功能，存在下面情况： 用户在 A 设备登录，A 设备网络不稳定，没有连接成功，SDK 启动重连机制。 用户此时又在 B 设备登录，B 设备连接成功。 A
     * 设备网络稳定之后，用户在 A 设备连接成功，B 设备被踢出。 这个接口就是为这种情况加的。
     *```
     * # 示例代码
     *```
     * let kickEnable = true;
     * IMEngine.getInstance().setReconnectKickEnable(kickEnable);
     *```
     * @param enable 设置为 true 时，SDK 重连的时候发现此时已有别的设备连接成功，踢出当前重连设备，不再强行踢出别的设备
     * @note 当 SDK 初始化时就会用该字段，所以必须在 init 之前调用
     * @note 该功能需要提工单，在服务端开通此功能后，客户端调用该方法才生效
     * @version 1.2.0
     */
    setReconnectKickEnable(enable: boolean): void;
    /**
     * 设置鸿蒙推送 token
     * # 说明
     *```
     * 1. SDK 初始化之前设置：SDK 会将推送 token 缓存，连接成功后上报
     * 2. SDK 初始化之后连接之前设置：连接成功后 SDK 自动上报
     * 3. SDK 连接成功后设置：SDK 立即上报
     *```
     * # 示例代码
     *```
     * import { pushService } from '@kit.PushKit';
     *
     * pushService.getToken((error: BusinessError, token: string) => {
     *  if (token) {
     *    IMEngine.getInstance().setPushToken(token);
     *  }
     * });
     *```
     * @param pushToken 推送 token
     * @version 1.0.0
     */
    setPushToken(pushToken: string): void;
    /**
     * 设置数据库状态监听
     * # 说明
     *```
     * 数据库打开的时机：参考 DatabaseStatusListener.onDatabaseStatusChange()
     *```
     * # 示例代码
     *```ts
     * IMEngine.getInstance().setDatabaseStatusListener((status: DatabaseStatus) => {
     *  hilog.info(0x0000, 'IM-App', 'setDatabaseStatusListener onChanged status:%{public}d', status);
     * });
     *```
     * @param listener 监听
     * @discussion init 之后 connect 之前调用。connect 成功之后调用不会有任何效果
     * @version 1.1.0
     * @deprecated 使用 addDatabaseStatusListener 替代，多次调用该方法内部会产生多个监听
     */
    setDatabaseStatusListener(listener: (status: DatabaseStatus) => void): void;
    /**
     * 增加数据库状态监听
     * # 说明
     *```
     * 数据库打开的时机：参考 DatabaseStatusListener.onDatabaseStatusChange()
     *```
     * # 示例代码
     *```ts
     * let dbListener: DatabaseStatusListener = {
     *  onDatabaseStatusChange: (status: DatabaseStatus): void => {
     *    // status 为具体的数据库状态
     *  }
     * }
     * IMEngine.getInstance().addDatabaseStatusListener(dbListener);
     *```
     * @param listener 监听
     * @discussion init 之后 connect 之前调用。connect 成功之后调用不会有任何效果
     * @warning addDatabaseStatusListener & removeDatabaseStatusListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     * @version 1.3.0
     */
    addDatabaseStatusListener(listener: DatabaseStatusListener): void;
    /**
     * 移除数据库状态监听
     * # 示例代码
     *```
     * IMEngine.getInstance().removeDatabaseStatusListener(dbListener);
     *```
     * @param listener 监听
     * @warning addDatabaseStatusListener & removeDatabaseStatusListener 配合使用，避免内存泄露
     * @version 1.3.0
     */
    removeDatabaseStatusListener(listener: DatabaseStatusListener): void;
    /**
     * 设置控制台日志级别
     * # 说明
     *```
     * 例如：填入 LogLevel.None ，SDK 将不再控制台输出日志
     * 例如：填入 LogLevel.Warn ，SDK 将会在控制台输出 Warn 和 Error 的日志
     *```
     * # 示例代码
     *```
     * IMEngine.getInstance().setLogLevel(LogLevel.Info)
     *```
     * @param level 日志级别，用于过滤控制台的日志输出，初始化之后设置
     * @version 1.0.0
     */
    setLogLevel(level: LogLevel): void;
    /**
     * 连接 IM
     *
     * # 说明
     *```
     * 调用该接口，SDK 会在 timeLimit 秒内尝试重连，直到出现下面三种情况之一
     * 第一、连接成功，回调 IConnectResult.code === EngineError.Success
     * 第二、超时，回调 IConnectResult.code === EngineError.ConnectionTimeout，需要手动调用该接口继续连接
     * 第三、出现 SDK 无法处理的错误，回调 IConnectResult.code 为具体的错误码
     *
     * 常见的错误如下：
     * ClientNotInit ：SDK 没有初始化，请先调用 init 接口
     *
     * NaviRespTokenIncorrect ：检查一下 APP 使用的 appKey 和 APP Server 使用的 appKey 是否相同
     * ConnectTokenIncorrect ：检查一下 APP 使用的 appKey 和 APP Server 使用的 appKey 是否相同
     *
     * ConnectOneTimePasswordUsed ：重新请求 Token
     * ConnectPlatformError ：重新请求 Token
     * ConnectTokenExpired ：重新请求 Token
     *
     * ConnectAppBlockOrDelete ： 给用户提示 AppKey 已经封禁或删除
     *
     * ConnectUserBlocked ：给用户提示被封禁
     * DisconnectUserKicked ：给用户提示被提掉线
     * DisconnectUserBlocked ：给用户提示被封禁
     * ConnectUserDeleteAccount ：给用户提示已销号
     *```
     * # 示例代码
     *```
     * let token = "IMToken";
     * let timeout = 5;
     * IMEngine.getInstance().connect(token, timeout).then(result => {
     *  if (EngineError.Success === result.code) {
     *    // 连接成功
     *    let userId = result.userId;
     *    return;
     *  }
     *  if (EngineError.ConnectTokenExpired === result.code) {
     *    // Token 过期，从 APP 服务请求新 token，获取到新 token 后重新 connect()
     *  } else if (EngineError.ConnectionTimeout === result.code) {
     *    // 连接超时，弹出提示，可以引导用户等待网络正常的时候再次点击进行连接
     *  } else {
     *    //其它业务错误码，请根据相应的错误码作出对应处理。
     *  }
     * });
     *```
     * @param token 从您服务器端获取的 token (用户身份令牌)
     * @param timeout 超时时间，整型，单位秒，timeout <= 0：不设置超时时长，一直连接直到成功或失败；timeout > 0: 在对应的时间内连接未成功则返回超时错误
     * @returns 连接结果
     *
     * @note 连接成功后，SDK 将接管所有的重连处理。当因为网络原因断线的情况下，SDK 会不停重连直到连接成功为止，不需要您做额外的连接操作。
     *
     * @see IConnectResult
     * @version 1.0.0
     */
    connect(token: string, timeout: number): Promise<IConnectResult>;
    /**
     * 设置连接状态监听
     *
     * # 说明
     *```
     * 每个连接状态都有详细的描述和处理意见，参考 ConnectionStatusListener.onConnectionStatusChanged()
     *```
     * # 示例代码
     *```
     * IMEngine.getInstance().setConnectionStatusListener((status: ConnectionStatus) => {
     *  // status 为具体的连接状态
     * });
     *```
     * @param listener 监听
     * @see ConnectionStatus
     * @version 1.0.0
     * @deprecated 使用 addConnectionStatusListener 替代，多次调用该方法内部会产生多个监听
     */
    setConnectionStatusListener(listener: (status: ConnectionStatus) => void): void;
    /**
     * 增加连接监听。每个连接状态都有详细的描述和处理意见
     *
     * # 说明
     *```
     * 每个连接状态都有详细的描述和处理意见，参考 ConnectionStatusListener.onConnectionStatusChanged()
     *```
     * # 示例代码
     *```
     *  let statusListener: ConnectionStatusListener = {
     *    onConnectionStatusChanged: (status: ConnectionStatus): void => {
     *      // status 为具体的连接状态
     *    }
     *  }
     *  IMEngine.getInstance().addConnectionStatusListener(statusListener);
     *```
     * @param listener 监听
     * @see ConnectionStatus
     * @warning addConnectionStatusListener & removeConnectionStatusListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     * @version 1.3.0
     */
    addConnectionStatusListener(listener: ConnectionStatusListener): void;
    /**
     * 移除连接监听
     * # 示例代码
     *```
     * IMEngine.getInstance().removeConnectionStatusListener(statusListener);
     *```
     * @param listener 连接监听
     * @warning addConnectionStatusListener & removeConnectionStatusListener 配合使用，避免内存泄露
     * @version 1.3.0
     */
    removeConnectionStatusListener(listener: ConnectionStatusListener): void;
    /**
     * 获取当前连接状态
     * # 示例代码
     *```
     *  IMEngine.getInstance().getCurrentConnectionStatus().then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取当前连接状态失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 获取的当前连接状态为空
     *      return;
     *    }
     *    let status = result.data as ConnectionStatus;
     *  });
     *```
     * @returns 连接状态
     * @see ConnectionStatus
     * @version 1.0.0
     */
    getCurrentConnectionStatus(): Promise<IAsyncResult<ConnectionStatus>>;
    /**
     * 获取当前用户 ID
     *
     * # 示例代码
     *```
     *  IMEngine.getInstance().getCurrentUserId().then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取用户 ID 失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 获取的用户 ID 为空
     *      return;
     *    }
     *    let userId = result.data as string;
     *  });
     *```
     * @returns 连接成功后才会有值
     * @version 1.0.0
     */
    getCurrentUserId(): Promise<IAsyncResult<string>>;
    /**
     * 断开连接
     *
     * # 示例代码
     *```
     * let isReceivePush = true;
     * IMEngine.getInstance().disconnect(isReceivePush);
     *```
     * @param isReceivePush 是否接受推送
     * @note SDK 在前后台切换或者网络出现异常都会自动重连，会保证连接的可靠性，除非您的 App 逻辑需要登出，否则一般不需要调用此方法进行手动断开
     * @version 1.0.0
     */
    disconnect(isReceivePush: boolean): void;
    /**
     * 设置消息接收监听
     *
     * # 示例代码
     *```
     *  IMEngine.getInstance().setMessageReceivedListener((message: Message, info: ReceivedInfo) => {
     *    //  针对接收离线消息时，服务端会将 200 条消息打成一个包发到客户端，客户端对这包数据进行解析。该参数表示每个数据包数据逐条上抛后，还剩余的条数
     *    let left = info.left;
     *    // 消息是否离线消息
     *    let isOffline = info.isOffline;
     *    // 是否在服务端还存在未下发的消息包
     *    let hasPackage = info.hasPackage;
     *  });
     *```
     * @param listener 监听
     * @note 刷新逻辑参考 ReceivedInfo
     * @version 1.0.0
     * @deprecated 已废弃，使用 addMessageReceivedListener() 方法，多次调用该方法内部会产生多个监听
     */
    setMessageReceivedListener(listener: (message: Message, info: ReceivedInfo) => void): void;
    /**
     * 添加消息接收监听，可以添加多个监听
     *
     * # 示例代码
     *```
     *  let messageReceiveListener: MessageReceivedListener = {
     *    onMessageReceived: (message: Message, info: ReceivedInfo): void => {
     *      // 收到了单条消息
     *    },
     *    onOfflineMessageSyncCompleted: (): void => {
     *      // 离线消息已全部拉取完成
     *    }
     *  };
     *  IMEngine.getInstance().addMessageReceivedListener(messageReceiveListener);
     *```
     * @param listener 监听
     * @discussion 刷新逻辑参考 ReceivedInfo，当回调 onOfflineMessageSyncCompleted 时，代表离线消息拉取完成
     * @warning addMessageReceivedListener & removeMessageReceivedListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     * @version 1.3.0
     */
    addMessageReceivedListener(listener: MessageReceivedListener): void;
    /**
     * 移除消息接收监听
     *
     * # 示例代码
     *```
     * IMEngine.getInstance().removeMessageReceivedListener(messageReceiveListener);
     *```
     * @param listener 监听
     * @warning addMessageReceivedListener & removeMessageReceivedListener 配合使用，避免内存泄露
     * @version 1.3.0
     */
    removeMessageReceivedListener(listener: MessageReceivedListener): void;
    /**
     * 设置消息撤回监听，撤回了之后，原始消息会变成 RecallNotificationMessage 消息
     *
     * # 示例代码
     *```
     * IMEngine.getInstance().setMessageRecalledListener((message: Message, recallMessage: RecallNotificationMessage) => {
     *
     * });
     *```
     * @param listener 监听
     * @version 1.0.0
     * @deprecated 使用 addMessageRecalledListener 替代，多次调用该方法内部会产生多个监听
     */
    setMessageRecalledListener(listener: (message: Message, recallMessage: RecallNotificationMessage) => void): void;
    /**
     * 增加消息撤回监听。撤回了之后，原始消息会变成 RecallNotificationMessage 消息
     * # 示例代码
     *```
     *  let recalledListener : MessageRecalledListener = {
     *    onMessageRecalled: (message: Message, recallMessage: RecallNotificationMessage): void => {
     *      // 消息撤回
     *    }
     *  }
     *  IMEngine.getInstance().addMessageRecalledListener(recalledListener);
     *```
     * @param listener 监听
     * @warning addMessageRecalledListener & removeMessageRecalledListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     * @version 1.3.0
     */
    addMessageRecalledListener(listener: MessageRecalledListener): void;
    /**
     * 移除消息撤回监听
     * # 示例代码
     *```
     * IMEngine.getInstance().removeMessageRecalledListener(recalledListener);
     *```
     * @param listener 监听
     * @warning addMessageRecalledListener & removeMessageRecalledListener 配合使用，避免内存泄露
     * @version 1.3.0
     */
    removeMessageRecalledListener(listener: MessageRecalledListener): void;
    /**
     * 设置消息敏感词拦截监听
     * # 示例代码
     *```
     *  IMEngine.getInstance().setMessageBlockedListener((blockInfo: MessageBlockInfo) => {
     *
     *  });
     *```
     * @param listener 监听
     * @discussion 触发时间参考 MessageBlockedListener.onMessageBlocked
     * @version 1.0.0
     * @deprecated 使用 addMessageBlockedListener 替代，多次调用该方法内部会产生多个监听
     */
    setMessageBlockedListener(listener: (blockInfo: MessageBlockInfo) => void): void;
    /**
     * 增加消息敏感词拦截监听
     * # 示例代码
     *```
     *  let blockedListener : MessageBlockedListener = {
     *    onMessageBlocked: (blockInfo: MessageBlockInfo): void => {
     *
     *    }
     *  }
     *  IMEngine.getInstance().addMessageBlockedListener(blockedListener);
     *```
     * @param listener 监听
     * @discussion 触发时间参考 MessageBlockedListener.onMessageBlocked
     * @warning addMessageBlockedListener & removeMessageBlockedListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     * @version 1.3.0
     */
    addMessageBlockedListener(listener: MessageBlockedListener): void;
    /**
     * 移除消息敏感词拦截监听
     * # 示例代码
     *```
     * IMEngine.getInstance().removeMessageBlockedListener(blockedListener);
     *```
     * @param listener 监听
     * @warning addMessageBlockedListener & removeMessageBlockedListener 配合使用，避免内存泄露
     * @version 1.3.0
     */
    removeMessageBlockedListener(listener: MessageBlockedListener): void;
    /**
     * 发送消息
     * # 说明
     *```
     * 如果遇到 DatabaseNotOpened = 34301 ，原因是在 IM 连接成功前发送了消息
     * IM 连接成功后 SDK 才会打开对应用户的消息数据库
     *```
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "会话 id";
     *
     *  let textMsg = new TextMessage();
     *  textMsg.content = "文本消息的内容";
     *
     *  let msg = new Message(conId, textMsg);
     *
     *  let option: ISendMsgOption = {};
     *
     *  IMEngine.getInstance().sendMessage(msg, option, (msg: Message) => {
     *    // 消息入库回调
     *  }).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 发送消息失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 消息数据为空
     *      return;
     *    }
     *    let msg = result.data as Message;
     *  })
     *```
     * @param message 消息对象
     * @param option 消息发送的配置
     * @param saveCallback 消息入库的回调
     * @returns 消息发送结果
     * @discussion 只有入库成功才会走 savedCallback，其他的情况：非法参数、入库失败、发送不入库的消息等都不会走 savedCallback 直接走 resultCallback
     * @note SDK 内置消息都有推送，自定义消息必须设置 Message.pushConfig
     * @version 1.0.0
     */
    sendMessage(message: Message, option: ISendMsgOption, saveCallback: (msg: Message) => void): Promise<IAsyncResult<Message>>;
    /**
     * 发送媒体消息
     * # 说明
     *```
     * 如果遇到 DatabaseNotOpened = 34301 ，原因是在 IM 连接成功前发送了消息
     * IM 连接成功后 SDK 才会打开对应用户的消息数据库
     *```
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "会话 id";
     *
     *  let imgMsg = new ImageMessage();
     *  // 使用沙盒路径创建图片消息。系统路径的图片 SDK 无法访问（例如相册的图片路径 SDK 就无法访问）
     *  imgMsg.localPath = localPath;
     *
     *  let msg = new Message(conId, imgMsg);
     *
     *  let option: ISendMsgOption = {};
     *
     *  IMEngine.getInstance().sendMediaMessage(msg, option, (msg: Message) => {
     *    // 消息保存到数据库
     *  }, (msg: Message, progress: number) => {
     *    // 媒体上传进度 [1 ~ 100]
     *  }).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      //发送消息失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 消息为空
     *      return;
     *    }
     *    let msg = result.data as Message;
     *  })
     *```
     * @param message 消息体
     * @param option 消息发送的配置
     * @param saveCallback 消息入库的回调
     * @param progressCallback 媒体文件上传进度
     * @returns 媒体消息发送结果
     * @discussion 只有入库成功才会走 savedCallback，其他的情况：非法参数、入库失败、发送不入库的消息等都不会走 savedCallback 直接走 resultCallback
     * @note 调用 cancelSendMediaMessage 接口成功取消发送会回调 RequestCanceled
     * @version 1.0.0
     */
    sendMediaMessage(message: Message, option: ISendMsgOption, saveCallback: (msg: Message) => void, progressCallback: (msg: Message, progress: number) => void): Promise<IAsyncResult<Message>>;
    /**
     * 使用自定义上传发送媒体消息
     * # 说明
     *```
     * 如果遇到 DatabaseNotOpened = 34301 ，原因是在 IM 连接成功前发送了消息
     * IM 连接成功后 SDK 才会打开对应用户的消息数据库
     *```
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "会话 id";
     *
     *  let imgMsg = new ImageMessage();
     *  // 使用沙盒路径创建图片消息。系统路径的图片 SDK 无法访问（例如相册的图片路径 SDK 就无法访问）
     *  imgMsg.localPath = localPath;
     *
     *  let msg = new Message(conId, imgMsg);
     *
     *  let option: ISendMsgOption = {};
     *
     *  IMEngine.getInstance().sendMediaMessageWithUploader(msg, option, (msg: Message) => {
     *    // 消息保存到数据库
     *  }, (msg: Message, progress: number) => {
     *    // 媒体上传进度 [1 ~ 100]
     *  }, (uploader: MediaMessageTransfer) => {
     *      uploadImg(imgMsg.getPicFilePath(), new uploadListener() {
     *        public onSuccess(localPath: string): void {
     *          uploader.success(localPath);
     *        }
     *      }
     *   }).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      //发送消息失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 消息为空
     *      return;
     *    }
     *    let msg = result.data as Message;
     *  })
     *```
     * @param message 消息体
     * @param option 消息发送的配置
     * @param saveCallback 消息入库的回调
     * @param progressCallback 媒体文件上传进度
     * @param uploadCallback [可选参数]自定义媒体文件上传回调，如果此参数不传则执行SDK默认流程
     * @returns 媒体消息发送结果
     * @discussion 只有入库成功才会走 savedCallback，其他的情况：非法参数、入库失败、发送不入库的消息等都不会走 savedCallback 直接走 resultCallback
     * @version 1.4.0
     */
    sendMediaMessageWithUploader(message: Message, option: ISendMsgOption, saveCallback: (msg: Message) => void, progressCallback: (msg: Message, progress: number) => void, uploadCallback?: (uploader: MediaMessageTransfer) => void): Promise<IAsyncResult<Message>>;
    /**
     * 取消发送媒体消息下载
     *
     * # 示例代码
     * ```
     *  let messageId = 123;
     *  IMEngine.getInstance().cancelSendMediaMessage(messageId).then(result => {
     *    if (EngineError.Success === result.code) {
     *      // 取消发送媒体消息成功
     *    } else {
     *      // 取消发送媒体消息成功失败
     *    }
     *  });
     * ```
     * @param messageId 消息 Id
     * @returns 取消发送媒体消息结果
     * @version 1.4.3
     * */
    cancelSendMediaMessage(messageId: number): Promise<IAsyncResult<void>>;
    /**
     * 发送普通定向消息
     * # 说明
     * ```
     * 此方法用于在群组中发送消息给其中的部分用户，其它用户不会收到这条消息。
     * 如果遇到 DatabaseNotOpened = 34301 ，原因是在 IM 连接成功前发送了消息
     * IM 连接成功后 SDK 才会打开对应用户的消息数据库
     * ```
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "会话 id";
     *
     *  let textMsg = new TextMessage();
     *  textMsg.content = "文本消息的内容";
     *
     *  let msg = new Message(conId, textMsg);
     *
     *  let option: ISendMsgOption = {};
     *
     *  // 填入实际的用户 id
     *  let userIdArray = ["userId1", "userId2"];
     *
     *  IMEngine.getInstance().sendDirectionalMessage(msg, option, userIdArray, (msg: Message) => {
     *      // 消息入库回调
     *    }).then(result => {
     *      if (EngineError.Success !== result.code) {
     *        // 发送消息失败
     *        return;
     *      }
     *      if (!result.data) {
     *        // 消息数据为空
     *        return;
     *      }
     *      let msg = result.data as Message;
     *  })
     *```
     * @param message 消息对象
     * @param option 消息发送的配置
     * @param userIdArray 消息指定接收者
     * @param saveCallback 消息入库的回调，
     * @returns 消息发送结果
     * @discussion 只有入库成功才会走 savedCallback，其他的情况：非法参数、入库失败、发送不入库的消息等都不会走 savedCallback 直接走 resultCallback
     * @note SDK 内置消息都有推送，自定义消息必须设置 Message.pushConfig
     * @version 1.3.0
     */
    sendDirectionalMessage(message: Message, option: ISendMsgOption, userIdArray: Array<string>, saveCallback: (msg: Message) => void): Promise<IAsyncResult<Message>>;
    /**
     * 发送媒体定向消息
     * # 说明
     * ```
     * 此方法用于在群组中发送消息给其中的部分用户，其它用户不会收到这条消息。
     * 如果遇到 DatabaseNotOpened = 34301 ，原因是在 IM 连接成功前发送了消息
     * IM 连接成功后 SDK 才会打开对应用户的消息数据库
     * ```
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "会话 id";
     *
     *  let imgMsg = new ImageMessage();
     *  // 使用沙盒路径创建图片消息。系统路径的图片 SDK 无法访问（例如相册的图片路径 SDK 就无法访问）
     *  imgMsg.localPath = localPath;
     *
     *  let msg = new Message(conId, imgMsg);
     *
     *  let option: ISendMsgOption = {};
     *
     *  // 填入实际的用户 id
     *  let userIdArray = ["userId1", "userId2"];
     *
     *  IMEngine.getInstance().sendDirectionalMediaMessage(msg, option, userIdArray, (msg: Message) => {
     *    // 消息保存到数据库
     *  }, (msg: Message, progress: number) => {
     *    // 媒体上传进度 [1 ~ 100]
     *  }).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      //发送消息失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 消息为空
     *      return;
     *    }
     *    let msg = result.data as Message;
     *  })
     *```
     * @param message 消息体
     * @param option 消息发送的配置
     * @param userIdArray 消息指定接收者
     * @param saveCallback 消息发送的配置
     * @param progressCallback 媒体文件上传进度
     * @returns 媒体消息发送结果
     * @discussion 只有入库成功才会走 savedCallback，其他的情况：非法参数、入库失败、发送不入库的消息等都不会走 savedCallback 直接走 resultCallback
     * @version 1.3.0
     */
    sendDirectionalMediaMessage(message: Message, option: ISendMsgOption, userIdArray: Array<string>, saveCallback: (msg: Message) => void, progressCallback: (msg: Message, progress: number) => void): Promise<IAsyncResult<Message>>;
    /**
     * 下载媒体消息
     * # 示例代码
     *```
     *  let messageId = 234;
     *  IMEngine.getInstance().downloadMediaMessage(messageId).then(result => {
     *    if (EngineError.Success === result.code) {
     *      // 本地路径
     *      let localPath = result.data as string;
     *    }
     *  });
     *```
     * @param messageId 消息 id，对应的消息必须是 MediaMessageContent 子类
     * @returns 媒体消息下载成功的本地路径，存储路径见 InitOption.mediaSavePath
     * @note 调用该接口下载成功后，消息的本地路径会保存数据库中；相同的消息重复下载，会直接返回本地路径
     * @note SDK 下载媒体过程中，调用了 cancelDownloadMediaMessage() ，该方法会返回 EngineError.RequestCanceled
     * @version 1.0.0
     * @deprecated 使用 downloadMediaMessageWithProgress() 替代
     */
    downloadMediaMessage(messageId: number): Promise<IAsyncResult<string>>;
    /**
     * 下载媒体消息，含下载进度
     * # 示例代码
     *```
     *  let messageId = 123;
     *  IMEngine.getInstance().downloadMediaMessageWithProgress(messageId, (messageId: number, progress: number) => {
     *    // 下载进度，取值范围 [0 , 100]
     *  }).then(result => {
     *    if (EngineError.Success === result.code) {
     *      // 本地路径
     *      let localPath = result.data as string;
     *    }
     *  });
     *```
     * @param messageId 消息 Id，对应的消息必须是 MediaMessageContent 子类
     * @param progressListener 下载进度监听，取值范围 [0 , 100]
     * @returns 媒体消息下载成功的本地路径，存储路径见 InitOption.mediaSavePath
     * @note 调用该接口下载成功后，消息的本地路径会保存数据库中；相同的消息重复下载，会直接返回本地路径
     * @note SDK 下载媒体过程中，调用了 cancelDownloadMediaMessage() ，该方法会返回 EngineError.RequestCanceled
     * @version 1.3.0
     */
    downloadMediaMessageWithProgress(messageId: number, progressListener: (messageId: number, progress: number) => void): Promise<IAsyncResult<string>>;
    /**
     * 使用自定义方法下载媒体消息
     * # 示例代码
     *```
     *  let messageId = 234;
     *  IMEngine.getInstance().downloadMediaMessageWithDownloader(
     *    messageId,
     *    (messageId: number, progress: number) => {
     *       // 下载进度，取值范围 [0 , 100]
     *    },
     *    (downloader: MediaMessageTransfer) => {
     *      downloadImg(imgMsg.getPicFilePath(), new downloadListener() {
     *        public onSuccess(localPath: string): void {
     *          downloader.success(localPath);
     *        }
     *      }
     *    }
     *  ).then(result => {
     *    if (EngineError.Success === result.code) {
     *      // 本地路径
     *      let localPath = result.data as string;
     *    }
     *  });
     *```
     * @param messageId 消息 id，对应的消息必须是 MediaMessageContent 子类
     * @param downloadCallback [可选参数]自定义媒体文件下载回调，如果此参数不传则执行SDK默认流程
     * @returns 媒体消息下载成功的本地路径，存储路径见 InitOption.mediaSavePath
     * @note 调用该接口下载成功后，消息的本地路径会保存数据库中；相同的消息重复下载，会直接返回本地路径
     * @note SDK 下载媒体过程中，调用了 cancelDownloadMediaMessage() ，该方法会返回 EngineError.RequestCanceled
     * @version 1.4.0
     */
    downloadMediaMessageWithDownloader(messageId: number, progressCallback: (messageId: number, progress: number) => void, downloadCallback?: (downloader: MediaMessageTransfer) => void): Promise<IAsyncResult<string>>;
    /**
     * 暂停媒体消息下载
     *
     * # 示例代码
     * ```
     *  let messageId = 123;
     *  IMEngine.getInstance().pauseDownloadMediaMessage(messageId).then(result => {
     *    if (EngineError.Success === result.code) {
     *      // 暂停下载成功，此时 downloadMediaMessageWithProgress 会返回 EngineError.RequestPaused
     *    } else {
     *      // 暂停下载失败
     *    }
     *  });
     * ```
     * @param messageId 消息 Id
     * @returns 暂停下载结果
     * @note 暂停之后，再次调用下载方法，SDK 会继续前面的下载。
     * @note 如果媒体资源尺寸太小（例如是几十 KB 的小图片）可能立即就下载完了，不容易暂停成功。最好是较大的媒体资源使用该方法
     * @version 1.4.0
     * */
    pauseDownloadMediaMessage(messageId: number): Promise<IAsyncResult<void>>;
    /**
     * 取消下载媒体消息，配合 downloadMediaMessageWithProgress 方法
     * # 示例代码
     *```
     *  let messageId = 123;
     *  IMEngine.getInstance().cancelDownloadMediaMessage(messageId).then(result => {
     *    if (EngineError.Success === result.code) {
     *      // 下载取消成功，此时 downloadMediaMessageWithProgress 会返回 EngineError.RequestCanceled
     *    } else {
     *      // 下载取消失败
     *    }
     *  });
     *```
     * @param messageId 消息 Id
     * @returns 取消下载结果
     * @note 取消之后，再次调用下载方法，SDK 会重新下载。
     * @note 如果媒体资源尺寸太小（例如是几十 KB 的小图片）不容易取消成功，可能立即就下载完了。最好是较大的媒体资源使用该方法
     * @version 1.3.0
     */
    cancelDownloadMediaMessage(messageId: number): Promise<IAsyncResult<void>>;
    /**
     * 下载文件（带下载进度）
     * # 说明
     *```
     * 如果是下载媒体消息，请调用 downloadMediaMessageWithProgress 方法，下载成功 SDK 会更新媒体消息的本地路径
     * 不是媒体消息的媒体文件需要下载时，才调用 downloadFileWithProgress
     *
     * 会将 file_name 最后一个 . 之前的特殊替换为下划线。如 <EMAIL> 会被替换为 abc_123.png。
     * 如果多次请求 file_name 相同，后一次下载会加入序号，如 abc.png，abc(1).png，abc(2).png。
     *
     *```
     * # 示例代码
     *```
     *  let remoteUrl = "https://expamle.com/1.jpg";
     *  let fileName = "1.jpg";
     *
     *  IMEngine.getInstance().downloadFileWithProgress(remoteUrl, fileName, (uniqueId: number) => {
     *    // 开始下载，下载唯一标识 uniqueId
     *  }, (uniqueId: number, progress: number) => {
     *    // 下载进度
     *  }).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 下载失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 下载的本地路径为空
     *      return;
     *    }
     *    // 本地路径
     *    let localPath = result.data as string;
     *  });
     *```
     * @param remoteUrl 媒体的远端地址
     * @param fileName  本地文件名
     * @param startCallback 开始下载。uniqueId 下载唯一标识，可以调用 cancelDownloadFile 取消下载
     * @param progressListener 下载进度监听，取值范围 [0 , 100]
     * @returns 媒体文件下载成功的本地路径，存储路径见 InitOption.mediaSavePath
     * @note 调用该接口下载成功后，消息的本地路径会保存数据库中；相同的消息重复下载，会直接返回本地路径
     * @note SDK 下载媒体过程中，调用了 cancelDownloadFile() ，该方法会返回 EngineError.RequestCanceled
     * @deprecated 该接口已被废弃，下载媒体文件功能可使用 downloadMediaFile 接口代替。
     * @version 1.3.0
     */
    downloadFileWithProgress(remoteUrl: string, fileName: string, startCallback: (uniqueId: number) => void, progressListener: (uniqueId: number, progress: number) => void): Promise<IAsyncResult<string>>;
    /**
     * 下载媒体文件
     * ## 示例代码
     * ```
     * // uniqueId 是下载媒体文件任务的标识，可以用来发起下载、暂停下载和取消下载。
     * // 与 pauseDownloadMediaFile 和 cancelDownloadFile 中的 uniqueId 保持一致。
     * let uniqueId = "123"
     * let remoteUrl = "https://expamle.com/1.jpg";
     * let fileName = "1.jpg";
     *
     * IMEngine.getInstance().downloadMediaFile(uniqueId, remoteUrl, fileName,
     *   (uniqueId: string) => {
     *     // 开始下载，下载唯一标识 uniqueId
     *   },
     *   (uniqueId: string, progress: number) => {
     *     // 下载进度
     * }).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 下载失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 下载的本地路径为空
     *      return;
     *    }
     *    // 本地路径
     *    let localPath = result.data as string;
     *  });
     * ```
     * @param uniqueId  文件唯一标示
     * @param remoteUrl 文件的远端地址
     * @param fileName  本地文件名
     * @param progressListener 下载进度监听，取值范围 [0 , 100]
     * @returns 媒体文件下载成功的本地路径，存储路径见 InitOption.mediaSavePath
     * @note 调用该接口下载成功后，消息的本地路径会保存数据库中；相同的消息重复下载，会直接返回本地路径
     * @note SDK 下载媒体过程中，调用了 cancelDownloadFile() ，该方法会返回 EngineError.RequestPaused
     * @version 1.4.0
     * */
    downloadMediaFile(uniqueId: string, remoteUrl: string, fileName: string, startCallback: (uniqueId: string) => void, progressListener: (uniqueId: string, progress: number) => void): Promise<IAsyncResult<string>>;
    /**
     * 暂停下载媒体文件
     * ## 示例代码
     * ```
     * // messageId 与 downloadMediaFile 的第一个参数对应
     * let messageId = "123";
     *  IMEngine.getInstance().pauseDownloadMediaFile(messageId).then(result => {
     *    if (EngineError.Success === result.code) {
     *      // 暂停下载成功，此时 downloadMediaFile 会返回 EngineError.RequestCanceled
     *    } else {
     *      // 暂停下载失败
     *    }
     *  });
     * ```
     * @param uniqueId  文件唯一标示
     * @returns 下载的结果
     * @note 暂停之后，再次调用下载方法，SDK 会接着上次下载的进度断点续传。
     * @note 如果媒体资源尺寸太小（例如是几十 KB 的小图片）不容易取消成功，可能立即就下载完了。最好是较大的媒体资源使用该方法
     * @version 1.4.0
     * */
    pauseDownloadMediaFile(messageId: string): Promise<IAsyncResult<string>>;
    /**
     * 取消下载文件，配合 downloadFileWithProgress 方法
     * # 示例代码
     *```
     *  // uniqueId 必须是调用 downloadFileWithProgress 时由 SDK 生成的，此处进行了简写
     *  let uniqueId = "1234";
     *
     *  IMEngine.getInstance().cancelDownloadFile(uniqueId).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 取消下载失败
     *      return;
     *    }
     *    // 取消下载成功，此时 downloadFileWithProgress 会返回 EngineError.RequestCanceled
     *  })
     *```
     * @param uniqueId 下载唯一标识
     * @returns 下载的结果
     * @note 取消之后，再次调用下载方法，SDK 会重新下载。
     * @note 如果媒体资源尺寸太小（例如是几十 KB 的小图片）不容易取消成功，可能立即就下载完了。最好是较大的媒体资源使用该方法
     * @version 1.3.0
     */
    cancelDownloadFile(uniqueId: string | number): Promise<IAsyncResult<void>>;
    /**
     * 撤回消息
     * # 示例代码
     *```
     *  // 必须用发送成功的消息，此处进行了简写
     *  let message : Message;
     *  IMEngine.getInstance().recallMessage(message).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 消息撤回失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 撤回小灰条消息为空
     *      return;
     *    }
     *    // 撤回小灰条消息
     *    let recallNtfMsg = result.data as RecallNotificationMessage;
     *  })
     *```
     * @param message 需要撤回的消息，发送成功的消息才能撤回（必须有有效的 MessageUid）
     * @returns 撤回成功后的小灰条消息
     * @discussion pushContent 用 Message.pushConfig
     * @version 1.0.0
     */
    recallMessage(message: Message): Promise<IAsyncResult<RecallNotificationMessage>>;
    /**
     * 注册自定义消息，初始化之后，连接之前调用
     * # 说明
     *```
     * 自定义消息 CustomOrderMessage 示例代码见 MessageContent
     * 自定义媒体消息 CustomFileMessage 示例代码见 MediaMessageContent
     *```
     * # 示例代码
     *```
     * let clazzList: List<MessageContentConstructor> = new List();
     * clazzList.add(CustomOrderMessage);
     * IMEngine.getInstance().registerMessageType(clazzList);
     *```
     * @param msgClassList 自定义消息数组
     * @note 自定义消息需要继承 MessageContent|MediaMessageContent 并且实现无参构造方法才能注册给 SDK
     * @note 相同 objectName 的消息重复注册，后注册的消息会覆盖先注册的消息
     * @version 1.0.0
     */
    registerMessageType(msgClassList: List<MessageContentConstructor>): void;
    /**
     * 获取 SDK 中所有的消息 objectName 和存储标识的映射关系
     * # 注意事项
     *```
     * 1. 映射关系集合包含 内置消息 和 自定义消息
     * 2. 必须在所有自定义消息注册完成之后再调用该方法，否则会导致无法正确获取自定义消息的映射关系
     * 3. 不要频繁调用该方法：建议 app 调用该方法之后， app 自行保存整个集合
     *```
     * # 其他平台说明
     *```
     * iOS 通过 RCMessagePersistentCompatible.persistentFlag 获取消息存储标识
     * Android 通过 getClass().getAnnotation(MessageTag.class) 获取消息存储标识
     * 鸿蒙需要通过本方法获取
     *```
     * # 如何获取 objectName？
     *```
     * 1. 发送消息时需要手动构造指定的消息体，可以直接获取到 objectName：例如创建文本消息时一定知道是文本消息的 objectName
     * 2. 接收消息时通过 Message 对象获取：Message.objectName
     * 3. 读取会话时通过 Conversation 对象获取：Conversation.objectName
     *```
     * # 示例代码
     *```
     *let hashMap = IMEngine.getInstance().getMessageTypeMap();
     *```
     * @returns 映射关系集合。 key ：objectName 、 value ： MessageFlag
     * @version 1.2.0
     */
    getMessageTypeMap(): HashMap<string, MessageFlag>;
    /**
     * 消息批量入库（多用于数据迁移）
     *
     * # Message 下列属性会被入库，其他属性会被抛弃：
     *```
     * conversationType   会话类型
     * targetId           会话 ID
     * channelId          所属会话的业务标识，长度限制 20 字符
     * direction          消息方向
     * senderId           发送者 ID
     * receivedStatus     接收状态
     * sentStatus         发送状态
     * sentTime           发送时间
     * content            消息内容
     * objectName         消息类型，设置 content 的时候 SDK 会自动赋值对应的 objectName
     * messageUid         服务端生产的消息唯一 ID，如要携带该字段需要保证入库后是唯一的
     * extra              扩展信息
     *```
     * # 示例代码
     *```
     *  // 必须是有效的消息集合，此处进行了简写
     *  let msgList : List<Message> ;
     *  IMEngine.getInstance().batchInsertMessage(msgList).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 批量入库失败
     *      return;
     *    }
     *    // 批量入库成功
     *  });
     *```
     * @param msgList 需要入库的消息，范围 [1 ~ 500]，会话类型不支持聊天室和超级群
     * @returns 入库结果
     * @version 1.0.0
     */
    batchInsertMessage(msgList: List<Message>): Promise<IAsyncResult<void>>;
    /**
     * 单条消息入库
     *
     * # Message 下列属性会被入库，其他属性会被抛弃：
     *```
     * conversationType   会话类型
     * targetId           会话 ID
     * direction          消息方向，默认为发送
     * senderId           发送者 ID
     * receivedStatus     接收状态，默认为未读
     * sentStatus         发送状态，默认为发送失败
     * sentTime           发送时间
     * content            消息内容
     * objectName         消息类型，设置 content 的时候 SDK 会自动赋值对应的 objectName
     * messageUid         服务端生产的消息唯一 ID，如要携带该字段需要保证入库后是唯一的
     * extra              扩展信息
     *```
     * # 示例代码
     *```
     *  // 必须是有效的消息对象，此处进行了简写
     *  let msg : Message;
     *
     *  IMEngine.getInstance().insertMessage(msg).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 入库失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 入库的消息体为空
     *      return;
     *    }
     *    // 入库成功的消息，含有效的 messageId
     *    let dbMsg = result.data as Message;
     *  });
     *```
     * @param Message 需要入库的消息，会话类型不支持聊天室和超级群
     * @returns 入库结果
     * @version 1.2.0
     */
    insertMessage(msg: Message): Promise<IAsyncResult<Message>>;
    /**
     * 批量获取本地和远端消息，支持单聊、群聊、系统消息。
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  let option: IHistoryMessageOption = {
     *    sendTime: 0,
     *    count: 100,
     *    order: Order.Ascending
     *  }
     *
     *  IMEngine.getInstance().getMessages(conId, option).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取历史消息失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 获取历史消息为空
     *      return;
     *    }
     *    // 获取到有效的历史消息
     *    let dbMsg = result.data as HistoryMessageResult;
     *  })
     *```
     * @param conId   会话标识
     * @param option  历史消息选项
     * @returns 历史消息数据，详见 HistoryMessageResult
     * @version 1.5.0
     * */
    getMessages(conId: ConversationIdentifier, option: IHistoryMessageOption): Promise<IAsyncResult<HistoryMessageResult>>;
    /**
     * 通过 messageId 获取单条消息
     * # 示例代码
     *```
     *  let messageId = 123;
     *  IMEngine.getInstance().getMessageById(messageId).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取本地消息失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 获取本地消息为空
     *      return;
     *    }
     *    // 获取到有效的本地消息
     *    let dbMsg = result.data as Message;
     *  })
     *```
     * @param messageId 消息的本地数据库自增 ID
     * @returns 消息数据
     * @version 1.0.0
     */
    getMessageById(messageId: number): Promise<IAsyncResult<Message>>;
    /**
     * 通过 messageUid 获取单条消息
     * # 示例代码
     *```
     *  let messageUid = "CH2C-A072-OGM5-E3HL";
     *  IMEngine.getInstance().getMessageByUid(messageUid).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取本地消息失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 获取本地消息为空
     *      return;
     *    }
     *    // 获取到有效的本地消息
     *    let dbMsg = result.data as Message;
     *  })
     *```
     * @param messageUid 消息发送成功后的服务唯一 ID，固定格式的字符串，例如 ： CH2C-A072-OGM5-E3HL
     * @returns 消息数据
     * @version 1.0.0
     */
    getMessageByUid(messageUid: string): Promise<IAsyncResult<Message>>;
    /**
     * 获取批量本地消息，基于 messageId 获取
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  let option: IGetLocalMsgByIdOption = {
     *    messageId: 100,
     *    beforeCount: 5,
     *    afterCount: 5
     *  }
     *
     *  IMEngine.getInstance().getHistoryMessagesById(conId, option).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取消息失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 消息不存在
     *      return;
     *    }
     *    // 消息体集合
     *    let msgList = result.data as List<Message>;
     *  });
     *```
     * @param conId 会话标识
     * @param option 配置
     * @returns 返回本地消息结果
     * @see ConversationIdentifier
     * @see IGetLocalMsgByIdOption
     * @version 1.0.0
     */
    getHistoryMessagesById(conId: ConversationIdentifier, option: IGetLocalMsgByIdOption): Promise<IAsyncResult<List<Message>>>;
    /**
     * 获取批量本地消息，基于 time 获取
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  let option: IGetLocalMsgByTimeOption = {
     *    time: Date.now(),
     *    beforeCount: 5,
     *    afterCount: 5
     *  };
     *
     *  IMEngine.getInstance().getHistoryMessagesByTime(conId, option).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取消息失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 消息不存在
     *      return;
     *    }
     *    // 消息列表
     *    let msgList = result.data as List<Message>;
     *  });
     *```
     * @param conId 会话标识
     * @param option 配置
     * @returns 返回本地消息结果
     * @see ConversationIdentifier
     * @see IGetLocalMsgByTimeOption
     * @version 1.0.0
     */
    getHistoryMessagesByTime(conId: ConversationIdentifier, option: IGetLocalMsgByTimeOption): Promise<IAsyncResult<List<Message>>>;
    /**
     * 获取批量远端消息
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  let option: IGetRemoteMsgOption = {
     *    time: Date.now(),
     *    count: 10,
     *    order: Order.Descending,
     *    isCheckDup: true
     *  }
     *
     *  IMEngine.getInstance().getRemoteHistoryMessages(conId, option).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取聊天室消息失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 消息数据为空
     *      return;
     *    }
     *    // 消息列表
     *    let msgList = result.data as List<Message>;
     *  });
     *```
     * @param conId 会话标识
     * @param option 配置
     * @returns 返回远端消息结果
     * @note 此方法从服务器端获取之前的历史消息，但是必须先开通历史消息云存储功能
     * @see ConversationIdentifier
     * @see IGetLocalMsgByTimeOption
     * @version 1.0.0
     */
    getRemoteHistoryMessages(conId: ConversationIdentifier, option: IGetRemoteMsgOption): Promise<IAsyncResult<List<Message>>>;
    /**
     * 获取本地会话中 @ 自己的未读消息列表
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  let option: ICountOption = {
     *    count: 10,
     *    order: Order.Descending
     *  };
     *
     *  IMEngine.getInstance().getUnreadMentionedMessages(conId, option).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取消息失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 消息不存在
     *      return;
     *    }
     *    // 消息列表
     *    let msgList = result.data as List<Message>;
     *  });
     *```
     * @param conId 会话标识
     * @param option 配置
     * @returns 返回本地消息结果
     * @see ConversationIdentifier
     * @see ICountOption
     * @version 1.0.0
     */
    getUnreadMentionedMessages(conId: ConversationIdentifier, option: ICountOption): Promise<IAsyncResult<List<Message>>>;
    /**
     * 获取会话里第一条未读消息
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  IMEngine.getInstance().getFirstUnreadMessage(conId).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取消息失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 消息不存在
     *      return;
     *    }
     *    let msg = result.data as Message;
     *  });
     *```
     * @param conId 会话标识
     * @returns 消息，如果该会话没有未读，返回 null
     * @see ConversationIdentifier
     * @version 1.0.0
     */
    getFirstUnreadMessage(conId: ConversationIdentifier): Promise<IAsyncResult<Message>>;
    /**
     * 删除本地会话的指定一批消息
     * # 示例代码
     *```
     *  let idList = new List<number>();
     *  idList.add(msg.messageId);
     *
     *  IMEngine.getInstance().deleteHistoryMessagesByIds(idList).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 删除消息失败
     *      return;
     *    }
     *    // 删除消息成功
     *  })
     *```
     * @param messageIds 消息 ID 列表
     * @returns 删除结果
     * @version 1.0.0
     */
    deleteHistoryMessagesByIds(messageIds: List<number>): Promise<IAsyncResult<void>>;
    /**
     * 清空本地会话的消息
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  IMEngine.getInstance().deleteMessages(conId).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 删除消息失败
     *      return;
     *    }
     *    // 删除消息成功
     *  });
     *```
     * @param conId 会话标识
     * @returns 结果
     * @see ConversationIdentifier
     * @version 1.0.0
     */
    deleteMessages(conId: ConversationIdentifier): Promise<IAsyncResult<void>>;
    /**
     * 删除本地会话特定时间前的所有消息
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  let sentTime = Date.now();
     *
     *  IMEngine.getInstance().deleteHistoryMessagesByTime(conId, time).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 删除消息失败
     *      return;
     *    }
     *    // 删除消息成功
     *  })
     *```
     * @param conId 会话标识
     * @param sentTime 毫秒时间戳。清除 <= sentTime 的所有历史消息，若为 0 则代表清除所有消息
     * @returns 结果
     * @see ConversationIdentifier
     * @version 1.0.0
     */
    deleteHistoryMessagesByTime(conId: ConversationIdentifier, sentTime: number): Promise<IAsyncResult<void>>;
    /**
     * 删除远端会话特定时间前的消息
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  let sentTime = Date.now();
     *
     *  IMEngine.getInstance().cleanRemoteHistoryMessagesByTime(conId, time).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 删除消息失败
     *      return;
     *    }
     *    // 删除消息成功
     *  });
     *```
     * @param conId 会话标识
     * @param sentTime 毫秒时间戳。清除 <= sentTime 的所有历史消息，若为 0 则代表清除所有消息
     * @returns 结果
     * @see ConversationIdentifier
     * @version 1.0.0
     */
    cleanRemoteHistoryMessagesByTime(conId: ConversationIdentifier, sentTime: number): Promise<IAsyncResult<void>>;
    /**
     * 批量删除远端消息，发送成功或者接收到的消息才可以从远端删除
     * # 说明
     *```
     * msgList 里面 Message 下列属性是必须的
     * uid:               服务端生产的消息唯一 id
     * direction:         消息方向
     * sentTime:          发送时间，不能小于等于 0
     *```
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "会话 ID";
     *
     *  let msgList = new List<Message>();
     *  for (let i = 0; i < 10; i++) {
     *    // msg 必须是发送成功的消息，此处进行简写
     *    let msg : Message;
     *    msgList.add(msg);
     *  }
     *
     *  // 是否删除本地消息
     *  let isDeleteLocal = true;
     *
     *  IMEngine.getInstance().deleteRemoteMessages(conId, msgList, isDeleteLocal).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 删除远端消息失败
     *      return;
     *    }
     *    // 删除远端消息成功
     *  })
     *```
     * @param conId 会话标识
     * @param msgList 消息列表，长度范围 [1, 100]
     * @param isDeleteLocal 是否删除本地消息。只有远端消息被删除成功时设置 true 才会删除本地消息。
     * @returns
     * @version 1.1.0
     */
    deleteRemoteMessages(conId: ConversationIdentifier, msgList: List<Message>, isDeleteLocal: boolean): Promise<IAsyncResult<void>>;
    /**
     * 设置输入状态的监听
     * # 示例代码
     *```
     * IMEngine.getInstance().setTypingStatusListener((conId: ConversationIdentifier, typingStatusList: List<TypingStatus>) => {
     *    // conId 具体的会话标识
     *    // typingStatusList 具体的输入状态
     * });
     *```
     * @param listener 监听，conId 会话标识；typingStatusList 输入状态的列表
     * @version 1.1.0
     * @deprecated 使用 addTypingStatusListener 替代，多次调用该方法内部会产生多个监听
     */
    setTypingStatusListener(listener: (conId: ConversationIdentifier, typingStatusList: List<TypingStatus>) => void): void;
    /**
     * 增加输入状态的监听
     * # 示例代码
     *```
     *  let typingListener: TypingStatusListener = {
     *    onTypingStatusChange: (conId: ConversationIdentifier, typingStatusList: List<TypingStatus>): void => {
     *      // conId 具体的会话标识
     *      // typingStatusList 具体的输入状态
     *    }
     *  }
     *  IMEngine.getInstance().addTypingStatusListener(typingListener);
     *```
     * @param listener 监听
     * @warning addTypingStatusListener & removeTypingStatusListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     * @version 1.3.0
     */
    addTypingStatusListener(listener: TypingStatusListener): void;
    /**
     * 移除输入状态的监听
     * # 示例代码
     *```
     *  IMEngine.getInstance().removeTypingStatusListener(typingListener);
     *```
     * @param listener 监听，对方调用 sendTypingStatus() 时本端会出发该监听
     * @warning addTypingStatusListener & removeTypingStatusListener 配合使用，避免内存泄露
     * @version 1.3.0
     */
    removeTypingStatusListener(listener: TypingStatusListener): void;
    /**
     * 发送输入状态，仅支持单聊
     * # 说明
     *```
     * 常见的使用方式如下：
     * 在聊天页面输入文本时，可以发送 TextMessageObjectName ，对方收到后可以展示"正在输入中"
     * 在录音时，可以发送 HQVoiceMessageObjectName ，对方收到后可以展示"正在说话"
     *```
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "会话 ID";
     *
     *  let objectName = TextMessageObjectName;
     *
     *  IMEngine.getInstance().sendTypingStatus(conId, objectName).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 发送失败
     *      return;
     *    }
     *    // 发送成功
     *  });
     *```
     * @param conId 会话标识
     * @param objectName 正在输入的消息 ObjectName
     * @returns
     * @version 1.1.0
     */
    sendTypingStatus(conId: ConversationIdentifier, objectName: string): Promise<IAsyncResult<void>>;
    /**
     * 设置输入状态更新时间间隔
     * # 说明
     *```
     * 控制输入状态发送频率，在规定时间内多次调用发送输入状态方法，最终只会发送一次输入状态
     * 例如输入状态时间间隔为 6000 毫秒，在这段时间多次调用输入状态方法，只会发出一次输入状态，对方也只会收到一次输入状态
     *```
     * # 示例代码
     *```
     *  let interval = 5000;
     *  IMEngine.getInstance().setTypingStatusInterval(interval).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 设置输入状态间隔失败
     *      return;
     *    }
     *    // 设置成功
     *  });
     *```
     * @param interval 时间间隔，单位毫秒，默认为 6000 毫秒。有效值 [1000，60000] 毫秒；超过范围，则设置不成功
     * @returns
     * @version 1.1.0
     */
    setTypingStatusInterval(interval: number): Promise<IAsyncResult<void>>;
    /**
     * 修改消息接收状态
     * # 说明
     *```
     * 目前该方法仅用于设置 isListened 来标记高清语音消息是否已被听过
     *```
     * # 示例代码
     *```
     *  let messageId = 123;
     *  let receivedStatus = new ReceivedStatus();
     *  receivedStatus.isRead = false;
     *  receivedStatus.isListened = false;
     *
     *  IMEngine.getInstance().setMessageReceivedStatus(messageId, receivedStatus).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 设置接收状态失败
     *      return;
     *    }
     *    // 设置接收状态成功
     *  })
     *```
     * @param messageId 消息 Id
     * @param receiveStatus 接收状态
     * @returns 结果
     * @version 1.3.0
     */
    setMessageReceivedStatus(messageId: number, receiveStatus: ReceivedStatus): Promise<IAsyncResult<void>>;
    /**
     * 设置本地消息的附加信息
     *
     * # 说明
     * 用于扩展消息的使用场景。设置后可以通过 getHistoryMessages 取出带附加信息的消息。
     *
     * # 示例代码
     *```
     *  let messageId = 123;
     *  let extra = "这是要更新的 extra 字段内容";
     *
     *  IMEngine.getInstance().setMessageExtra(messageId, extra).then(result => {
     *    if (EngineError.Success == result.code) {
     *      // 设置 extra 字段内容成功
     *    } else {
     *      // 设置 extra 字段内容失败
     *    }
     *  })
     *```
     *
     * @param messageId  消息 Id
     * @param value      附加信息，最大 1024 字节
     * @returns          返回设置成功或者失败
     * @note             只能用于本地使用，无法同步给远程用户
     * @version          1.4.1
     * */
    setMessageExtra(messageId: number, extra: string): Promise<IAsyncResult<void>>;
    /**
     * 设置消息扩展监听
     * # 示例代码
     *```
     *  let listener: MessageExpansionListener = {
     *    onMessageExpansionUpdate: (expansion: Map<string, string>, message: Message): void => {
     *
     *    },
     *    onMessageExpansionRemove: (keyArray: string[], message: Message): void => {
     *
     *    }
     *  }
     *
     *  IMEngine.getInstance().setMessageExpansionListener(listener);
     *```
     * @param listener 监听
     * @version 1.2.0
     * @deprecated 使用 addMessageExpansionListener 替代
     */
    setMessageExpansionListener(listener: MessageExpansionListener): void;
    /**
     * 增加消息扩展监听
     * # 示例代码
     *```
     *  let listener: MessageExpansionListener = {
     *    onMessageExpansionUpdate: (expansion: Map<string, string>, message: Message): void => {
     *
     *    },
     *    onMessageExpansionRemove: (keyArray: string[], message: Message): void => {
     *
     *    }
     *  }
     *
     *  IMEngine.getInstance().addMessageExpansionListener(listener);
     *```
     * @param listener 监听
     * @warning addMessageExpansionListener & removeMessageExpansionListener 配对使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     * @version 1.3.0
     */
    addMessageExpansionListener(listener: MessageExpansionListener): void;
    /**
     * 移除消息扩展监听
     * # 示例代码
     *```
     *  IMEngine.getInstance().removeMessageExpansionListener(listener);
     *```
     * @param listener 监听
     * @warning addMessageExpansionListener & removeMessageExpansionListener 配对使用，避免内存泄露
     * @version 1.3.0
     */
    removeMessageExpansionListener(listener: MessageExpansionListener): void;
    /**
     * 更新消息扩展信息
     * # 说明
     *```
     * 调用更新扩展的一方必须通过成功回调来处理本端的数据刷新。
     * 仅被动接收扩展变更的用户（包含本用户的其他端）通过监听方法 MessageExpansionListener.onMessageExpansionUpdate 获取通知。
     * 消息扩展信息是以字典形式存在。设置的时候从 expansion 中读取 key，如果原有的扩展信息中 key 不存在则添加新的 KV 对，如果 key 存在则替换成新的 value。
     * 扩展信息字典中的 Key 支持大小写英文字母、数字、部分特殊符号 + = - _ 的组合方式，最大长度 32；Value 最长长度 4096，单次设置扩展数量最大为 20，消息的扩展总数不能超过 300
     *```
     * # 示例代码
     *```
     *  // 消息 UId 是固定格式的字符串，例如 ： CH2C-A072-OGM5-E3HL
     *  let msgUid = "消息 UId";
     *
     *  // 更新的消息扩展数据
     *  let map = new Map<string,string>();
     *  map.set("k1","v1");
     *  map.set("k2","v2");
     *
     *  IMEngine.getInstance().updateMessageExpansion(map, msgUid).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 消息扩展更新失败
     *      return;
     *    }
     *    // 消息扩展更新成功
     *  });
     *```
     * @param expansion 要更新的消息扩展信息键值对
     * @param messageUid 消息 messageUId
     * @returns 结果
     * @discussion 扩展信息只支持单聊和群组，其它会话类型不能设置扩展信息
     * @version 1.2.0
     */
    updateMessageExpansion(expansion: Map<string, string>, messageUid: string): Promise<IAsyncResult<void>>;
    /**
     * 删除消息扩展信息中特定的键值对
     * # 说明
     *```
     * 调用删除扩展的一方必须通过成功回调来处理本端的数据刷新。
     * 仅被动接收扩展变更的用户（包含本用户的其他端）通过监听方法 MessageExpansionListener.onMessageExpansionRemove 获取通知。
     *```
     * # 示例代码
     *```
     *  // 消息 UId 是固定格式的字符串，例如 ： CH2C-A072-OGM5-E3HL
     *  let msgUid = "消息 UId";
     *
     *  // 需要删除的 key 数组
     *  let keyArray = new Array<string>();
     *  keyArray.push("k1");
     *
     *  IMEngine.getInstance().removeMessageExpansion(keyArray, msgUid).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 删除消息扩展失败
     *      return;
     *    }
     *    // 删除消息扩展成功
     *  });
     *```
     * @param keyArray 消息扩展信息中待删除的 key 的列表
     * @param messageUid 消息 messageUId
     * @returns 结果
     * @discussion 扩展信息只支持单聊和群组，其它会话类型不能设置扩展信息
     * @version 1.2.0
     */
    removeMessageExpansion(keyArray: Array<string>, messageUid: string): Promise<IAsyncResult<void>>;
    /**
     * 增加消息阅后即焚监听，仅支持单聊，仅限阅后即焚消息的接收方调用
     * # 示例代码
     *```
     *  let destructListener: MessageDestructionListener = {
     *    onMessageDestructing: (message: Message, leftDuration: number): void => {
     *
     *    },
     *    onMessageDestructionStop: (message: Message): void => {
     *
     *    }
     *  }
     *  IMEngine.getInstance().addMessageDestructionListener(destructListener);
     *```
     * @param listener 监听
     * @warning 国内不允许带阅后即焚功能的 App 上架
     * @warning addMessageDestructionListener & removeMessageDestructionListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     * @version 1.3.0
     */
    addMessageDestructionListener(listener: MessageDestructionListener): void;
    /**
     * 移除消息阅后即焚监听，仅支持单聊，仅限阅后即焚消息的接收方调用
     * # 示例代码
     *```
     *  IMEngine.getInstance().removeMessageDestructionListener(destructListener);
     *```
     * @param listener 监听
     * @version 1.3.0
     * @warning addMessageDestructionListener & removeMessageDestructionListener 配合使用，避免内存泄露
     */
    removeMessageDestructionListener(listener: MessageDestructionListener): void;
    /**
     * 消息开始阅后即焚倒计时，仅支持单聊，仅限阅后即焚消息的接收方调用
     * # 示例代码
     *```
     *  // 必须使用有效的消息体，此处进行了简写
     *  let msg: Message;
     *  IMEngine.getInstance().messageBeginDestruct(msg).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 失败
     *      return;
     *    }
     *    // 成功
     *  });
     *```
     * @param message 消息体
     * @warning 国内不允许带阅后即焚功能的 App 上架
     * @note 调用之后，阅后即焚消息开始倒计时，触发 MessageDestructionListener.onMessageDestructing
     * @version 1.3.0
     */
    messageBeginDestruct(message: Message): Promise<IAsyncResult<void>>;
    /**
     * 消息停止阅后即焚倒计时，仅支持单聊，仅限阅后即焚消息的接收方调用
     * # 说明
     *```
     * 调用之后，该消息终止阅后即焚
     * 阅后即焚倒计时停止，并触发 MessageDestructionListener.onMessageDestructionStop
     * 可以调用 messageBeginDestruct() 重新进行倒计时
     *```
     * # 示例代码
     *```
     *  // 必须使用有效的消息体，此处进行了简写
     *  let msg: Message;
     *  IMEngine.getInstance().messageStopDestruct(msg).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 失败
     *      return;
     *    }
     *    // 成功
     *  });
     *```
     * @param message 消息体
     * @warning 国内不允许带阅后即焚功能的 App 上架
     * @version 1.3.0
     */
    messageStopDestruct(message: Message): Promise<IAsyncResult<void>>;
    /**
     * 设置显示推送详情
     * # 说明
     *```
     * 通知栏显示的消息推送默认显示的是消息内容，如果消息包含敏感信息，不希望在推送通知栏上显示消息内容，可以调用此方法设置 showStatus 为 false
     * 注意：此功能需要从服务端开启用户设置功能。
     *```
     * # 示例代码
     *```
     *  let showStatus = true;
     *  IMEngine.getInstance().setPushContentShowStatus(showStatus).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 失败
     *      return;
     *    }
     *    // 成功
     *  });
     *```
     * @param showStatus 是否显示远程推送内容，true 显示，false 不显示
     * @returns 设置的结果
     * @version 1.3.0
     */
    setPushContentShowStatus(showStatus: boolean): Promise<IAsyncResult<void>>;
    /**
     * 获取是否显示远程推送内容详情设置
     * # 示例代码
     *```
     *  IMEngine.getInstance().getPushContentShowStatus().then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 数据为空
     *      return;
     *    }
     *    let showStatus = result.data as boolean;
     *  });
     *```
     * @returns 是否显示远程推送内容，true 显示，false 不显示
     * @version 1.3.0
     */
    getPushContentShowStatus(): Promise<IAsyncResult<boolean>>;
    /**
     * 设置 Web 端在线时，手机端是否接收推送
     * # 说明
     *```
     * 注意：此功能需要从服务端开启用户设置功能。
     *```
     * # 示例代码
     *```
     *  let receiveStatus = true;
     *  IMEngine.getInstance().setPushReceiveStatus(receiveStatus).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 失败
     *      return;
     *    }
     *    // 成功
     *  });
     *```
     * @param receiveStatus 是否接收推送，true 接收，false 不接收
     * @returns 结果
     * @version 1.3.0
     */
    setPushReceiveStatus(receiveStatus: boolean): Promise<IAsyncResult<void>>;
    /**
     * 获取是否接收远程推送的设置
     * # 说明
     *```
     * 前提: 移动端不在线，Web 、MAC/PC 终端在线，移动端是否接收远程推送。
     *```
     * # 示例代码
     *```
     *  IMEngine.getInstance().getPushReceiveStatus().then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 数据为空
     *      return;
     *    }
     *    let receiveStatus = result.data as boolean;
     *  });
     *```
     * @returns 是否接收远程推送，true 接收，false 不接收
     * @version 1.3.0
     */
    getPushReceiveStatus(): Promise<IAsyncResult<boolean>>;
    /**
     * 设置会话状态（置顶，消息免打扰）变化监听
     * # 示例代码
     *```
     *  IMEngine.getInstance().setConversationStatusListener((items: List<ConversationStatusInfo>) => {
     *
     *  });
     *```
     * @param listener 监听
     * @version 1.0.0
     * @deprecated 使用 addConversationStatusListener 替代，多次调用该方法内部会产生多个监听
     */
    setConversationStatusListener(listener: (items: List<ConversationStatusInfo>) => void): void;
    /**
     * 增加会话状态（置顶，消息免打扰）变化监听
     * # 示例代码
     *```
     *  let statusListener : ConversationStatusListener = {
     *    onConversationStatusChange: (items: List<ConversationStatusInfo>): void => {
     *
     *    }
     *  }
     *  IMEngine.getInstance().addConversationStatusListener(statusListener);
     *```
     * @param listener 监听
     * @warning addConversationStatusListener & removeConversationStatusListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     * @version 1.3.0
     */
    addConversationStatusListener(listener: ConversationStatusListener): void;
    /**
     * 移除会话状态（置顶，消息免打扰）变化监听
     * # 示例代码
     *```
     *  IMEngine.getInstance().removeConversationStatusListener(statusListener);
     *```
     * @param listener 监听
     * @warning addConversationStatusListener & removeConversationStatusListener 配合使用，避免内存泄露
     * @version 1.3.0
     */
    removeConversationStatusListener(listener: ConversationStatusListener): void;
    /**
     * 获取单个会话
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  IMEngine.getInstance().getConversation(conId).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取回话失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 会话为空
     *      return;
     *    }
     *    let con = result.data as Conversation;
     *  });
     *```
     * @param conId 会话标识
     * @returns 会话数据
     * @see ConversationIdentifier
     * @version 1.0.0
     */
    getConversation(conId: ConversationIdentifier): Promise<IAsyncResult<Conversation>>;
    /**
     * 分页获取本地会话列表
     * # 示例代码
     *```
     *  let conTypeList = new List<ConversationType>();
     *  conTypeList.add(ConversationType.Private);
     *  conTypeList.add(ConversationType.Group);
     *
     *  let option: IGetConversationOption = {
     *    time: Date.now(),
     *    count: 10
     *  }
     *
     *  IMEngine.getInstance().getConversationListByPage(conTypeList, option).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取会话列表失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 会话列表为空
     *      return;
     *    }
     *    let conList = result.data as List<Conversation>;
     *  });
     *```
     * @param conTypeList 会话类型列表
     * @param option 配置
     * @returns 本地会话列表数据
     * @see IGetConversationOption
     * @version 1.0.0
     */
    getConversationListByPage(conTypeList: List<ConversationType>, option: IGetConversationOption): Promise<IAsyncResult<List<Conversation>>>;
    /**
     * 分页获取本地置顶会话列表
     * # 示例代码
     *```
     *  let conTypeList = new List<ConversationType>();
     *  conTypeList.add(ConversationType.Private);
     *  conTypeList.add(ConversationType.Group);
     *
     *  let option: IGetConversationOption = {
     *    time: Date.now(),
     *    count: 10
     *  }
     *
     *  IMEngine.getInstance().getTopConversationListByPage(conTypeList, option).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取会话列表失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 会话列表为空
     *      return;
     *    }
     *    let conList = result.data as List<Conversation>;
     *  });
     *```
     * @param conTypeList 会话类型列表
     * @param option 配置
     * @returns 本地会话列表数据
     * @see IGetConversationOption
     * @version 1.0.0
     */
    getTopConversationListByPage(conTypeList: List<ConversationType>, option: IGetConversationOption): Promise<IAsyncResult<List<Conversation>>>;
    /**
     * 分页获取本地免打扰会话列表
     * # 示例代码
     *```
     *  let conTypeList = new List<ConversationType>();
     *  conTypeList.add(ConversationType.Private);
     *  conTypeList.add(ConversationType.Group);
     *
     *  let option: IGetConversationOption = {
     *    time: Date.now(),
     *    count: 10
     *  }
     *
     *  IMEngine.getInstance().getBlockedConversationListByPage(conTypeList, option).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取会话列表失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 会话列表为空
     *      return;
     *    }
     *    let conList = result.data as List<Conversation>;
     *  });
     *```
     * @param conTypeList 会话类型列表
     * @param option 配置
     * @returns 本地会话列表数据
     * @see IGetConversationOption
     * @version 1.0.0
     */
    getBlockedConversationListByPage(conTypeList: List<ConversationType>, option: IGetConversationOption): Promise<IAsyncResult<List<Conversation>>>;
    /**
     * 获取本地未读会话列表，该接口仅支持单聊、群聊、系统三种会话类型，不支持聊天室、超级群。
     * # 示例代码
     *```
     *  let conTypeList = new List<ConversationType>();
     *  conTypeList.add(ConversationType.Private);
     *  conTypeList.add(ConversationType.Group);
     *
     *  IMEngine.getInstance().getUnreadConversations(conTypeList).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取未读会话列表失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 获取未读会话列表为空
     *      return;
     *    }
     *    // 未读会话列表
     *    let conList = result.data as List<Conversation>;
     *  });
     *```
     * @param conTypeList 会话类型数组，长度范围 [1, 100]
     * @returns 会话数组
     * @version 1.1.0
     */
    getUnreadConversations(conTypeList: List<ConversationType>): Promise<IAsyncResult<List<Conversation>>>;
    /**
     * 删除本地会话同时删除会话中的消息
     * # 示例代码
     *```
     *  let conTypeList = new List<ConversationType>();
     *  conTypeList.add(ConversationType.Private);
     *
     *  IMEngine.getInstance().clearConversations(conTypeList).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 删除会话失败
     *      return;
     *    }
     *  });
     *```
     * @param conTypeList 会话类型列表
     * @returns 结果
     * @version 1.0.0
     */
    clearConversations(conTypeList: List<ConversationType>): Promise<IAsyncResult<void>>;
    /**
     * 批量删除本地会话，但是不会删除消息
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  let list = new List<ConversationIdentifier>();
     *  list.add(conId);
     *
     *  IMEngine.getInstance().removeConversations(conIdList).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 删除会话失败
     *      return;
     *    }
     *  });
     *```
     * @param conIdList 会话标识数组
     * @returns 结果
     * @version 1.0.0
     */
    removeConversations(conIdList: List<ConversationIdentifier>): Promise<IAsyncResult<void>>;
    /**
     * 批量删除远端会话
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  let conIds : Array<ConversationIdentifier> = [];
     *  conIds.push(conId);
     *
     *  IMEngine.getInstance().removeRemoteConversations(conIds).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 删除会话失败
     *      return;
     *    }
     *  });
     *```
     * @param conIds 会话标识数组
     * @returns 结果
     * @version 1.6.0
     * */
    removeRemoteConversations(conIdList: Array<ConversationIdentifier>): Promise<IAsyncResult<void>>;
    /**
     * 设置实时会话的已读时间
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  let timestamp = 1234567; // 按需填写实际的 timestamp
     *
     *  IMEngine.getInstance().setReadTimestamp(conId, timestamp).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 设置实时会话的已读时间失败
     *
     *    } else {
     *      // 设置实时会话的已读时间成功
     *
     *    }
     *  });
     *```
     * @param conId 会话标识
     * @param timestamp 时间戳
     * @returns 结果
     * @version 1.6.0
     * */
    setReadTimestamp(conId: ConversationIdentifier, timestamp: number): Promise<IAsyncResult<void>>;
    /**
     * 批量 设置/取消 会话置顶
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  let list = new List<ConversationIdentifier>();
     *  list.add(conId);
     *
     *  let option: ISetConversationTopOption = {
     *    isTop: true, // 是否置顶
     *    isNeedCreate: true // 没有会话时是否创建该会话
     *  }
     *
     *  IMEngine.getInstance().setConversationsToTop(conIdList, option).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 设置置顶失败
     *      return;
     *    }
     *  });
     *```
     * @param conIdList 会话标识列表
     * @param option 配置
     * @returns 结果
     * @see ISetConversationTopOption
     * @version 1.0.0
     */
    setConversationsToTop(conIdList: List<ConversationIdentifier>, option: ISetConversationTopOption): Promise<IAsyncResult<void>>;
    /**
     * 获取会话置顶状态
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  IMEngine.getInstance().getConversationTopStatus(conId).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取置顶状态失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 置顶状态为空
     *      return;
     *    }
     *    let isTop = result.data as boolean;
     *  });
     *```
     * @param conId 会话标识
     * @returns 是否置顶
     * @version 1.0.0
     */
    getConversationTopStatus(conId: ConversationIdentifier): Promise<IAsyncResult<boolean>>;
    /**
     * 批量设置会话免打扰
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  let list = new List<ConversationIdentifier>();
     *  list.add(conId);
     *
     *  let level = PushNotificationLevel.Blocked;
     *
     *  IMEngine.getInstance().setConversationsNotificationLevel(conIdList, level).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 设置免打扰失败
     *      return;
     *    }
     *  });
     *```
     * @param conIdList 会话标识列表
     * @param level 会话免打扰级别
     * @returns 结果
     * @version 1.0.0
     */
    setConversationsNotificationLevel(conIdList: List<ConversationIdentifier>, level: PushNotificationLevel): Promise<IAsyncResult<void>>;
    /**
     * 获取单个会话免打扰状态
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  IMEngine.getInstance().getConversationNotificationLevel(conId).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取免打扰状态失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 免打扰状态为空
     *      return;
     *    }
     *    let level = result.data as PushNotificationLevel;
     *  });
     *```
     * @param conId 会话标识
     * @returns 会话免打扰级别
     * @version 1.0.0
     */
    getConversationNotificationLevel(conId: ConversationIdentifier): Promise<IAsyncResult<PushNotificationLevel>>;
    /**
     * 保存/清空 会话草稿
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *  let draft = "draft From 鸿蒙";
     *
     *  IMEngine.getInstance().saveTextMessageDraft(conId, draft).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 保存草稿失败
     *      return;
     *    }
     *  });
     *```
     * @param conId 会话标识
     * @param draft 草稿：传入有效值代表保存草稿；传入空字符串代表清空草稿
     * @returns 结果
     * @discussion 保存成功的草稿可以通过 Conversation.draft 获取
     * @version 1.0.0
     */
    saveTextMessageDraft(conId: ConversationIdentifier, draft: string): Promise<IAsyncResult<void>>;
    /**
     * 获取会话草稿
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  IMEngine.getInstance().getTextMessageDraft(conId).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取草稿失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 草稿为空
     *      return;
     *    }
     *    let draft = result.data as string;
     *  })
     *```
     * @param conId 会话标识
     * @returns 草稿
     * @version 1.0.0
     */
    getTextMessageDraft(conId: ConversationIdentifier): Promise<IAsyncResult<string>>;
    /**
     * 屏蔽某个时间段的消息提醒
     * # 示例代码
     *```
     *  let option: IQuietHoursOption = {
     *    startTime: "00:30:00",
     *    duration: 300,
     *    level: PushNotificationLevel.Blocked
     *  }
     *
     *  IMEngine.getInstance().setNotificationQuietHoursLevel(option).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 设置免打扰失败
     *      return;
     *    }
     *  })
     *```
     * @param option 配置，见 IQuietHoursOption
     * @returns 结果
     * @discussion 此方法设置的屏蔽时间会在每天该时间段时生效。
     * @version 1.0.0
     */
    setNotificationQuietHoursLevel(option: IQuietHoursOption): Promise<IAsyncResult<void>>;
    /**
     * 查询已设置的时间段消息提醒屏蔽
     * # 示例代码
     *```
     *  IMEngine.getInstance().getNotificationQuietHoursLevel().then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取免打扰失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 免打扰数据为空
     *      return;
     *    }
     *    let info = result.data as IQuietHoursOption;
     *  })
     *```
     * @returns 具体的配置
     * @version 1.0.0
     */
    getNotificationQuietHoursLevel(): Promise<IAsyncResult<IQuietHoursOption>>;
    /**
     * 删除已设置的全局时间段消息提醒屏蔽
     * # 示例代码
     *```
     *  IMEngine.getInstance().removeNotificationQuietHours().then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 移除免打扰失败
     *      return;
     *    }
     *    // 移除免打扰成功
     *  });
     *```
     * @returns 结果
     * @version 1.0.0
     */
    removeNotificationQuietHours(): Promise<IAsyncResult<void>>;
    /**
     * 获取本地会话的全部未读数
     * # 示例代码
     *```
     *  IMEngine.getInstance().getTotalUnreadCount().then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取未读数失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 未读数为 null
     *      return;
     *    }
     *    let unreadCount = result.data as number;
     *  })
     *```
     * @returns 未读数
     * @version 1.0.0
     */
    getTotalUnreadCount(): Promise<IAsyncResult<number>>;
    /**
     * 获取本地批量会话的未读数之和
     * # 示例代码
     *```
     *  let conIdList = new List<ConversationIdentifier>();
     *  let conId1 = new ConversationIdentifier();
     *  conId1.conversationType = ConversationType.Private;
     *  conId1.targetId = "会话 id";
     *  conIdList.add(conId1)
     *
     *  let conId2 = new ConversationIdentifier();
     *  conId2.conversationType = ConversationType.Private;
     *  conId2.targetId = "会话 id";
     *  conIdList.add(conId2)
     *
     *  IMEngine.getInstance().getTotalUnreadCountByIds(conIdList).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取未读数失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 未读数为 null
     *      return;
     *    }
     *    let unreadCount = result.data as number;
     *  })
     *```
     * @param conIds 会话标识数组
     * @returns 未读数
     * @version 1.0.0
     */
    getTotalUnreadCountByIds(conIds: List<ConversationIdentifier>): Promise<IAsyncResult<number>>;
    /**
     * 获取单个会话的未读数
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  IMEngine.getInstance().getUnreadCount(conId).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取未读数失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 未读数为 null
     *      return;
     *    }
     *    let unreadCount = result.data as number;
     *  });
     *```
     * @param conId 会话标识
     * @returns 该会话的未读数
     * @version 1.0.0
     */
    getUnreadCount(conId: ConversationIdentifier): Promise<IAsyncResult<number>>;
    /**
     * 清空单个会话未读数
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  IMEngine.getInstance().clearMessagesUnreadStatus(conId).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 清空未读数失败
     *      return;
     *    }
     *    / 清空未读数成功
     *  })
     *```
     * @param conId 会话标识
     * @returns 结果
     * @version 1.0.0
     */
    clearMessagesUnreadStatus(conId: ConversationIdentifier): Promise<IAsyncResult<void>>;
    /**
     * 清除单个会话的未读数：按照时间戳清除
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  let time = Date.now();
     *
     *  IMEngine.getInstance().clearMessagesUnreadStatusByTime(conId, time).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 清空未读数失败
     *      return;
     *    }
     *    // 清空未读数成功
     *  })
     *```
     * @param conId 会话标识
     * @param time 时间，清理小于该时间戳的消息未读
     * @returns 结果
     * @version 1.0.0
     */
    clearMessagesUnreadStatusByTime(conId: ConversationIdentifier, time: number): Promise<IAsyncResult<void>>;
    /**
     * 会话未读数，是否包含免打扰会话的未读数
     * # 示例代码
     *```
     *  let typeList = new List<ConversationType>();
     *  typeList.add(ConversationType.Private);
     *  typeList.add(ConversationType.Group);
     *
     *  let isContainBlocked = false;
     *
     *  IMEngine.getInstance().getUnreadCountByTypes(conTypeList, isContainBlocked).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取未读数失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 未读数为 null
     *      return;
     *    }
     *    let unreadCount = result.data as number;
     *  });
     *```
     * @param typeList 会话类型数组
     * @param isContainBlocked 是否包含免打扰；true 代表获取所有会话未读数之和； false 代表获取不包含免打扰会话的正常会话未读数之和
     * @returns 未读数
     * @discussion 正常单聊会话 A 的未读数为1，免打扰单聊会话 B 的未读数为 2。true 代表获取两个单聊会话的未读数之和，其结果为 3。false 代表获取正常会话 A 的未读数，结果为 1
     * @version 1.0.0
     */
    getUnreadCountByTypes(typeList: List<ConversationType>, isContainBlocked: boolean): Promise<IAsyncResult<number>>;
    /**
     * 同步会话已读状态
     * # 说明
     *```
     * 用于相同账号的多端已读同步
     * 例如用户 A 同时登录鸿蒙和 Android，两端同时收到消息，同时未读数增加
     * Android 调用该方法将某个会话同步已读之后， 鸿蒙会触发 SyncConversationReadStatusListener
     *```
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "会话 Id";
     *
     *  // 会话中已读的最后一条消息的发送时间戳，此处用了当前时间
     *  let time = Date.now();
     *
     *  IMEngine.getInstance().syncConversationReadStatus(conId,time).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 同步已读失败
     *      return;
     *    }
     *    // 同步已读成功
     *  });
     *```
     * @param conId 会话标识
     * @param timestamp 会话中已读的最后一条消息的发送时间戳
     * @returns 结果
     * @version 1.2.0
     */
    syncConversationReadStatus(conId: ConversationIdentifier, timestamp: number): Promise<IAsyncResult<void>>;
    /**
     * 设置会话已读状态监听
     * # 示例代码
     *```
     *  let listener : SyncConversationReadStatusListener = {
     *    onSyncConversationReadStatus: (conId: ConversationIdentifier, timestamp: number): void => {
     *     // 该会话的 timestamp 之前的消息未读已清空
     *    }
     *  }
     *  IMEngine.getInstance().setSyncConversationReadStatusListener(listener);
     *```
     * @param listener 监听
     * @version 1.2.0
     * @deprecated 使用 addSyncConversationReadStatusListener 替换，多次调用该方法内部会产生多个监听
     */
    setSyncConversationReadStatusListener(listener: SyncConversationReadStatusListener): void;
    /**
     * 增加会话已读状态监听
     * # 示例代码
     *```
     *  let listener : SyncConversationReadStatusListener = {
     *    onSyncConversationReadStatus: (conId: ConversationIdentifier, timestamp: number): void => {
     *     // 该会话的 timestamp 之前的消息未读已清空
     *    }
     *  }
     *  IMEngine.getInstance().addSyncConversationReadStatusListener(listener);
     *```
     * @param listener 监听
     * @warning addSyncConversationReadStatusListener & removeSyncConversationReadStatusListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     * @version 1.3.0
     */
    addSyncConversationReadStatusListener(listener: SyncConversationReadStatusListener): void;
    /**
     * 移除会话已读状态监听
     * # 示例代码
     *```
     *  IMEngine.getInstance().removeSyncConversationReadStatusListener(listener);
     *```
     * @param listener
     * @warning addSyncConversationReadStatusListener & removeSyncConversationReadStatusListener 配合使用，避免内存泄露
     * @version 1.3.0
     */
    removeSyncConversationReadStatusListener(listener: SyncConversationReadStatusListener): void;
    /**
     * 增加消息已读回执监听
     * # 示例代码
     *```
     *  let listener: MessageReadReceiptListener = {
     *    onMessageReadReceiptReceived: (message: Message): void => {
     *
     *    },
     *    onMessageReceiptRequest: (conId: ConversationIdentifier, messageUid: string): void => {
     *
     *    },
     *    onMessageReceiptResponse: (conId: ConversationIdentifier, messageUid: string, respondUserIdList: Map<string, number>): void => {
     *
     *    }
     *  }
     *  IMEngine.getInstance().addMessageReadReceiptListener(listener);
     *```
     * @param listener 监听
     * @discussion 业务流程请参考 ReadReceiptInfo
     * @warning addMessageReadReceiptListener & removeMessageReadReceiptListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     * @version 1.3.0
     */
    addMessageReadReceiptListener(listener: MessageReadReceiptListener): void;
    /**
     * 移除消息已读回执监听
     * # 示例代码
     *```
     *  IMEngine.getInstance().removeMessageReadReceiptListener(listener);
     *```
     * @param listener 监听
     * @warning addMessageReadReceiptListener & removeMessageReadReceiptListener 配合使用，避免内存泄露
     * @version 1.3.0
     */
    removeMessageReadReceiptListener(listener: MessageReadReceiptListener): void;
    /**
     * 单聊，发送某个会话中的消息阅读回执，由原始消息的接收方调用
     * # 说明
     *```
     * 单聊调用该方法后，原始消息发送方会触发 MessageReadReceiptListener.onMessageReadReceiptReceived
     * 原始消息发送方本地对应单聊会话的已读消息 sentStatus 均为 SentStatus.Read
     *```
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  let time = Date.now();
     *
     *  IMEngine.getInstance().sendReadReceiptMessage(conId, time).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 失败
     *      return;
     *    }
     *    // 成功
     *  });
     *```
     * @param conId 会话标识
     * @param timestamp 会话中已读的最后一条消息的发送时间戳， Message.sentTime
     * @returns
     * @discussion 业务流程请参考 ReadReceiptInfo
     * @version 1.3.0
     */
    sendReadReceiptMessage(conId: ConversationIdentifier, timestamp: number): Promise<IAsyncResult<void>>;
    /**
     * 群聊，发送某个会话的消息已读请求，由原始消息的发送方调用
     * # 说明
     *```
     * 群聊调用该方法后，原始消息的接收方会触发 MessageReadReceiptListener.onMessageReceiptRequest
     * 原始消息接收方就知道需要对该消息做已读响应。调用 sendReadReceiptResponse
     *```
     * # 示例代码
     *```
     *  // 应该使用发送成功的消息，此处进行了简写
     *  let msg : Message;
     *
     *  IMEngine.getInstance().sendReadReceiptRequest(msg).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 失败
     *      return;
     *    }
     *    // 成功
     *  });
     *```
     * @param message 消息体，messageUid 必须有效
     * @returns 消息已读请求的结果
     * @discussion 业务流程请参考 ReadReceiptInfo
     * @version 1.3.0
     */
    sendReadReceiptRequest(message: Message): Promise<IAsyncResult<void>>;
    /**
     * 群聊，发送某个会话已读响应，由原始消息的接收方调用
     * # 说明
     *```
     * 群聊调用该方法后，原始消息的发送方会触发 MessageReadReceiptListener.onMessageReceiptResponse
     * 原始消息的发送方就知道自己该消息有哪些人已读了
     *```
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  // 需要具体的消息，此处进行了简写
     *  let msgArray = new Array<Message>();
     *  let msg : Message;
     *  msgArray.push(msg);
     *
     *  IMEngine.getInstance().sendReadReceiptResponse(conId, msgArray).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 成功的消息体列表为空
     *      return;
     *    }
     *    // 发送响应成功的消息列表
     *    let msgList = result.data as List<Message>;
     *  });
     *```
     * @param conId 会话标识
     * @param messageArray 消息体数组，消息体必须存在有效的 senderId 和 messageUid
     * @returns 消息已读响应结果，返回的 Message 集合可以通过 Message.readReceiptInfo.hasRespond 确认该消息是否已经发送了响应
     * @discussion 业务流程请参考 ReadReceiptInfo
     * @version 1.3.0
     */
    sendReadReceiptResponse(conId: ConversationIdentifier, messageArray: Array<Message>): Promise<IAsyncResult<List<Message>>>;
    /**
     * 增加消息已读V2回执监听
     * # 示例代码
     *```
     *  let listener: MessageReadReceiptV2Listener = {
     *    onMessageReceiptResponse: (conId: ConversationIdentifier, messageUid: string, readCount: number, totalCount: number): void => {
     *
     *    }
     *  }
     *  IMEngine.getInstance().addMessageReadReceiptV2Listener(listener);
     *```
     * @param listener 监听
     * @discussion 业务流程请参考 ReadReceiptInfo
     * @warning addMessageReadReceiptV2Listener & removeMessageReadReceiptV2Listener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     * @version  1.4.0
     */
    addMessageReadReceiptV2Listener(listener: MessageReadReceiptV2Listener): void;
    /**
     * 移除消息已读v2回执监听
     * # 示例代码
     *```
     *  IMEngine.getInstance().removeMessageReadReceiptV2Listener(listener);
     *```
     * @param listener 监听
     * @warning addMessageReadReceiptV2Listener & removeMessageReadReceiptV2Listener 配合使用，避免内存泄露
     * @version 1.4.0
     */
    removeMessageReadReceiptV2Listener(listener: MessageReadReceiptV2Listener): void;
    /**
     * 群聊，发送某个会话已读响应，由原始消息的接收方调用
     * # 说明
     *```
     * 群聊调用该方法后，原始消息的发送方会触发 MessageReadReceiptV2Listener.onMessageReceiptResponse
     * 原始消息的发送方就知道自己该消息有多少人已读了
     *```
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Group;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  // 需要具体的消息，此处进行了简写
     *  let msgArray = new Array<Message>();
     *  let msg : Message;
     *  msgArray.push(msg);
     *
     *  IMEngine.getInstance().sendReadReceiptResponseV2(conId, msgArray).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 成功的消息体列表为空
     *      return;
     *    }
     *    // 发送响应成功的消息列表
     *    let msgList = result.data as List<Message>;
     *  });
     *```
     * @param conId 会话标识
     * @param messageArray 消息体数组，消息体必须存在有效的 senderId 和 messageUid
     * @returns 消息已读响应结果，返回的 Message 集合可以通过 Message.readReceiptInfo.hasRespond 确认该消息是否已经发送了响应
     * @discussion 业务流程请参考 ReadReceiptInfo
     * @version 1.4.0
     */
    sendReadReceiptResponseV2(conId: ConversationIdentifier, messageArray: Array<Message>): Promise<IAsyncResult<List<Message>>>;
    /**
     * 群聊，获取已读人员列表
     * # 说明
     *```
     * 在服务端配置已读V2后，原始消息的发送方调用该方法后，就知道自己该消息有哪些人已读了
     *```
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Group;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  // 需要具体的消息，此处进行了简写
     *  let msgUid = "123456";  // 按需填写实际的Message Uid
     *
     *  IMEngine.getInstance().getGroupMessageReaderList(conId, msgUid, (totalCount: number, readerList: Map<string, number>) => {
     *    // 处理已读人员信息
     *
     *  }).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 失败
     *      return;
     *    }
     *    // 成功，获取已读人数
     *    let readCount = result.data;
     *  });
     *```
     * @param conId      会话标识
     * @param messageUid 消息Uid
     * @returns          消息已读人数
     *
     * @version 1.4.0
     * */
    getGroupMessageReaderList(conId: ConversationIdentifier, messageUid: string, successCallback: (totalCount: number, readerList: Map<string, number>) => void): Promise<IAsyncResult<number>>;
    /**
     * 增加消息已读V5回执监听
     * # 示例代码
     *```
     *  let listener: MessageReadReceiptV5Listener = {
     *    onMessageReceiptResponse: (readReceiptNotifyArray: Array<ReadReceiptNotifyV5>): void => {
     *
     *    }
     *  }
     *  IMEngine.getInstance().addMessageReadReceiptV5Listener(listener);
     *```
     * @param listener 已读 V5 监听器
     * @discussion 业务流程请参考 ReadReceiptInfo
     * @warning addMessageReadReceiptV5Listener & removeMessageReadReceiptV5Listener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     * @version  1.5.0
     */
    addMessageReadReceiptV5Listener(listener: MessageReadReceiptV5Listener): void;
    /**
     * 移除消息已读 v5 回执监听
     * # 示例代码
     *```
     *  IMEngine.getInstance().removeMessageReadReceiptV5Listener(listener);
     *```
     * @param listener 监听
     * @warning addMessageReadReceiptV5Listener & removeMessageReadReceiptV5Listener 配合使用，避免内存泄露
     * @version 1.5.0
     */
    removeMessageReadReceiptV5Listener(listener: MessageReadReceiptV5Listener): void;
    /**
     * 发送消息已读回执 V5，由原始消息的接收方调用
     * # 说明
     *```
     * 调用该方法后，原始消息的发送方会触发 MessageReadReceiptV5Listener.onMessageReceiptResponse
     * 原始消息的发送方就知道自己该消息有多少人已读了
     *```
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Group;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  // 需要具体的消息，此处进行了简写
     *  let msgUidArray = new Array<String>();
     *  let messageUid1 = "MessageUid1"; // 按需填写实际的 MessageUid
     *  let messageUid2 = "MessageUid2"; // 按需填写实际的 MessageUid
     *  msgUidArray.push(messageUid1);
     *  msgUidArray.push(messageUid2);
     *
     *  IMEngine.getInstance().sendReadReceiptResponseV5(conId, msgUidArray).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 成功的消息体列表为空
     *      return;
     *    }
     *    // 发送响应成功的消息列表
     *    let msgList = result.data as List<Message>;
     *  });
     *```
     * @param conId 会话标识
     * @param messageUidArray 消息 Uid 数组
     * @returns 消息已读响应结果，成功返回 EngineError.Success，失败返回对应错误码。
     * @version 1.5.0
     */
    sendReadReceiptResponseV5(conId: ConversationIdentifier, messageUidArray: Array<string>): Promise<IAsyncResult<void>>;
    /**
     * 批量获取消息已读信息（V5）
     *
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Group;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  // 需要具体的消息，此处进行了简写
     *  let msgUidArray = new Array<string>();
     *  let messageUid1 = "MessageUid1"; // 按需填写实际的 MessageUid
     *  let messageUid2 = "MessageUid2"; // 按需填写实际的 MessageUid
     *  msgUidArray.push(messageUid1);
     *  msgUidArray.push(messageUid2);
     *
     *  IMEngine.getInstance().getMessageReadReceiptInfoV5(conId, msgUidArray).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 成功的消息体列表为空
     *      return;
     *    }
     *    // 发送响应成功的消息列表
     *    let msgList = result.data as List<ReadReceiptInfoV5>;
     *  });
     *```
     *
     * @param conId 会话标识
     * @param messageUidArray 消息 Uid 数组
     * @returns 消息已读响应结果，返回的 Message 集合
     * @version 1.5.0
     * */
    getMessageReadReceiptInfoV5(conId: ConversationIdentifier, messageUidArray: Array<string>): Promise<IAsyncResult<List<ReadReceiptInfoV5>>>;
    /**
     * 批量获取消息已读回执 V5 信息
     *
     * @param identifiers 消息会话标识
     * @returns 消息已读响应结果，返回的 Message 集合
     * @version 1.6.0
     * */
    getMessageReadReceiptInfoV5ByIdentifiers(identifiers: Array<MessageIdentifier>): Promise<IAsyncResult<List<ReadReceiptInfoV5>>>;
    /**
     * 分页获取消息已读未读信息
     * # 说明
     *```
     * 在服务端配置已读V5后，原始消息的发送方调用该方法后，就知道自己该消息有哪些人已读了
     *```
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Group;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  // 需要具体的消息，此处进行了简写
     *  let msgUid = "123456";  // 按需填写实际的Message Uid
     *
     *  let pageToken = "yourPageToken";
     *  let count = 100;
     *  let readStatus = ReadStatus.All;
     *  let order = Order.Descending;
     *
     *  IMEngine.getInstance().getMessagesReadReceiptUsersByPageV5(
     *    conId,
     *    msgUid,
     *    pageToken,
     *    count,
     *    readStatus,
     *    order,
     * ).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 失败
     *      return;
     *    }
     *    // 成功，获取已读人数
     *    let readReceiptUserResult = result.data;
     *  });
     *```
     * @param conId      会话标识
     * @param messageUid 消息Uid
     * @param pageToken  当前分页请求 Token，首次可以不传
     * @param count      分页请求条数，范围 [1, 100]
     * @param readStatus 已读状态，参见 ReadReceiptStatus
     * @param order      排序规则，参见 Order 。默认优先显示被 AT 用户的已读未读状态
     * @returns          消息已读人数
     *
     * @version 1.5.0
     * */
    getMessagesReadReceiptUsersByPageV5(conId: ConversationIdentifier, messageUid: string, option: ReadReceiptUsersOption): Promise<IAsyncResult<ReadReceiptUsersResult>>;
    /**
     * 批量获取用户指定群组消息是否已读状态（V5）
     *
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Group;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  // 需要具体的消息，此处进行了简写
     *  let msgUid = "123456";  // 按需填写实际的Message Uid
     *
     *  // 需要具体的消息，此处进行了简写
     *  let userIdArray = new Array<string>();
     *  let userId1 = "userId1"; // 按需填写实际的 userId
     *  let userId2 = "userId2"; // 按需填写实际的 userId
     *  userIdArray.push(userId1);
     *  userIdArray.push(userId2);
     *
     *  IMEngine.getInstance().getMessagesReadReceiptByUsersV5(
     *    conId,
     *    msgUid,
     *    userIdArray,
     * ).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 失败
     *      return;
     *    }
     *    // 成功，获取已读人数
     *    let readReceiptUserResult = result.data;
     *  });
     *```
     *
     * @param conId      会话标识
     * @param messageUid 消息Uid
     * @param userList   要查询的用户 userId 列表
     *
     * @version 1.5.0
     * */
    getMessagesReadReceiptByUsersV5(conId: ConversationIdentifier, messageUid: string, userList: Array<string>): Promise<IAsyncResult<ReadReceiptUsersResult>>;
    /**
     * 根据关键字搜索本地会话。
     * # 示例代码
     *```
     *  let conTypeList = new List<ConversationType>();
     *  conTypeList.add(ConversationType.Private);
     *  conTypeList.add(ConversationType.Group);
     *
     *  let keyword = "需要搜索的关键字";
     *
     *  let objNameList = new List<string>();
     *  // 例如文本消息传入文本消息的 objectName: RC:TxtMsg
     *  objNameList.add(TextMessageObjectName);
     *
     *  IMEngine.getInstance().searchConversations(conTypeList, keyword, objNameList).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 搜索会话失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 搜索的会话为空
     *      return;
     *    }
     *    // 搜索到的会话列表
     *    let conList = result.data as List<Conversation>;
     *  });
     *```
     * @param conTypes 搜索的会话类型列表
     * @param keyword 搜索的关键字，长度范围 [1, 256]
     * @param objNameList 消息类型数组。用于搜索指定类型的消息；为空代表所有所有类型消息
     * @returns 搜索到的会话列表
     * @version 1.1.0
     * @deprecated 已废弃，使用 searchConversationsWithResult() 方法
     */
    searchConversations(typeList: List<ConversationType>, keyword: string, objNameList: List<string> | null): Promise<IAsyncResult<List<Conversation>>>;
    /**
     * 根据关键字搜索本地会话。
     * # 示例代码
     *```
     *  let conTypeList = new List<ConversationType>();
     *  conTypeList.add(ConversationType.Private);
     *  conTypeList.add(ConversationType.Group);
     *
     *  let keyword = "关键字";
     *
     *  IMEngine.getInstance().searchConversationsWithResult(conTypeList, keyword, null).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 搜索会话失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 搜索的会话内容为空
     *      return;
     *    }
     *    // 搜索的结果
     *    let searchResultList = result.data as List<SearchConversationResult>;
     *  });
     *```
     * @param conTypes 搜索的会话类型列表
     * @param keyword 搜索的关键字，长度范围 [1, 256]
     * @param objNameList 消息类型数组。用于搜索指定类型的消息；为空代表所有所有类型消息
     * @returns 搜索到的会话列表
     * @version 1.2.0
     */
    searchConversationsWithResult(typeList: List<ConversationType>, keyword: string, objNameList: List<string> | null): Promise<IAsyncResult<List<SearchConversationResult>>>;
    /**
     * 根据关键字搜索指定消息类型的本地消息。
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "会话 ID";
     *
     *  let keyword = "需要搜索的关键字";
     *  let objNameList = new List<string>();
     *  objNameList.add(TextMessageObjectName);
     *
     *  let startTime = Date.now();
     *  let count = 10;
     *
     *  IMEngine.getInstance().searchMessages(conId, keyword, objNameList, startTime, count).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 搜索消息失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 搜索消息为空
     *      return;
     *    }
     *    // 搜索到的消息
     *    let msgList = result.data as List<Message>;
     *  });
     *```
     * @param conId 会话标识
     * @param keyword 关键字，长度范围 [1, 256]
     * @param objNameList 消息类型数组。用于搜索指定类型的消息；为空代表搜索所有类型消息
     * @param startTime 查询的起始发送时间，返回小于该时间的消息，毫秒时间戳；如果为 0，则查询全部。当分页时，可以传入上一批消息的最小发送时间，取值范围 [0, INT64_MAX]
     * @param count 消息个数，传 0 时会返回所有搜索到的消息，非 0 时根据 startTime 逐页返回，取值范围 [0, 100]
     * @returns 消息列表
     * @version 1.1.0
     */
    searchMessages(conId: ConversationIdentifier, keyword: string, objNameList: List<string> | null, startTime: number, count: number): Promise<IAsyncResult<List<Message>>>;
    /**
     * 根据关键字和指定时间段搜索指定会话中的消息。
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "会话 ID";
     *
     *  let keyword = "需要搜索的关键字";
     *
     *  // 查找当前时间 24 小时内的消息
     *  let option: ISearchMessageInTimeRangeOption = {
     *    startTime: Date.now() - 24 * 60 * 60 * 1000,
     *    endTime: Date.now(),
     *    offset: 0,
     *    limit: 10
     *  }
     *
     *  IMEngine.getInstance().searchMessagesInTimeRange(conId, keyword, option).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 搜索消息失败
     *      result;
     *    }
     *    if (!result.data) {
     *      // 搜素消息为空
     *      return;
     *    }
     *    // 搜索的消息列表
     *    let msgList = result.data as List<Message>;
     *  });
     *```
     * @param conId 会话标识
     * @param keyword 关键字，长度范围 [1, 256]
     * @param option 在时间区间内搜索消息的参数配置
     * @returns 消息列表
     * @version 1.1.0
     */
    searchMessagesInTimeRange(conId: ConversationIdentifier, keyword: string, option: ISearchMessageInTimeRangeOption): Promise<IAsyncResult<List<Message>>>;
    /**
     * 根据用户 id 搜索指定会话中的本地消息。
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "会话 ID";
     *
     *  let userId = "用户 ID";
     *
     *  let startTime = Date.now();
     *  let count = 10;
     *
     *  IMEngine.getInstance().searchMessagesByUser(conId, userId, startTime, count).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 搜索消息失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 搜素消息为空
     *      return;
     *    }
     *    // 搜索的消息列表
     *    let msgList = result.data as List<Message>;
     *  });
     *```
     * @param conId 会话标识
     * @param userId  用户 id
     * @param startTime 查询的起始发送时间，返回小于该时间的消息，毫秒时间戳；如果为 0，则查询全部。当分页时，可以传入上一批消息的最小发送时间，取值范围 [0, INT64_MAX]
     * @param count 消息个数，传 0 时会返回所有搜索到的消息；非 0 时根据 startTime 逐页返回，取值范围 [0, 100]
     * @returns 消息列表
     * @version 1.1.0
     */
    searchMessagesByUser(conId: ConversationIdentifier, userId: string, startTime: number, count: number): Promise<IAsyncResult<List<Message>>>;
    /**
     * 在指定的一批会话中搜索消息
     * # 示例代码
     *```
     *  let conTypeArray = new Array<ConversationType>();
     *  conTypeArray.push(ConversationType.Private);
     *
     *  let targetIdArray: Array<string> | null = null;
     *  let channelIdArray: Array<string> | null = null;
     *  let objNameArray: Array<string> | null = null;
     *
     *  let keyword = "关键字";
     *  let startTime = Date.now();
     *  let count = 10;
     *  let order = Order.Ascending;
     *
     *  IMEngine.getInstance().searchMessagesByConversations(
     *    conTypeArray,
     *    targetIdArray,
     *    channelIdArray,
     *    objNameArray,
     *    keyword,
     *    startTime,
     *    count, order
     *  ).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 数据为空
     *      return;
     *    }
     *    let msgList = result.data as List<Message>;
     *  })
     *```
     * @param conTypes 会话类型数组，非空
     * @param targetIds 会话 Id 数组，为空代表所有会话
     * @param channelIds 频道 Id 数组，为空代表所有频道
     * @param objNameArray 消息类型数组。用于搜索指定类型的消息；为空代表搜索所有类型消息
     * @param keyword 关键字，长度范围 [1, 256]
     * @param startTime 查询的起始发送时间，返回小于该时间的消息，毫秒时间戳；如果为 0，则查询全部。当分页时，可以传入上一批消息的最小发送时间，取值范围 [0, INT64_MAX]
     * @param count 消息个数，传 0 时会返回所有搜索到的消息；非 0 时根据 startTime 逐页返回，取值范围 [0, 100]
     * @param order 返回排序。Ascending 升序，返回比 startTime 时间戳大的消息。Descending 降序，返回比 startTime 时间戳消的消息
     * @param callback 消息列表
     * @version 1.3.0
     */
    searchMessagesByConversations(conTypes: Array<ConversationType>, targetIds: Array<string> | null, channelIds: Array<string> | null, objNameArray: Array<string> | null, keyword: string, startTime: number, count: number, order: Order): Promise<IAsyncResult<List<Message>>>;
    /**
     * 在本地指定会话中搜索多个成员指定的消息类型
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.Private;
     *  conId.targetId = "TestTargetId"; // 按需填写实际的会话 id
     *
     *  let userIdArray = ["UserId1","UserId2"];
     *
     *  let objNameArray: Array<string> | null = null;
     *
     *  let startTime = Date.now();
     *  let count = 10;
     *  let order = Order.Ascending;
     *
     *  IMEngine.getInstance().searchMessagesByUsers(conId, userIdArray, objNameArray, startTime, count, order).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 数据为空
     *      return;
     *    }
     *    // 搜索到的消息列表
     *    let msgList = result.data as List<Message>;
     *  })
     *```
     * @param conId 会话标识
     * @param sendIds 消息发送者 Id 数组，搜索由这些用户发送的消息
     * @param objNameArray 消息类型数组。用于搜索指定类型的消息；为空代表搜索所有类型消息
     * @param startTime 查询的起始发送时间，返回小于该时间的消息，毫秒时间戳；如果为 0，则查询全部。当分页时，可以传入上一批消息的最小发送时间，取值范围 [0, INT64_MAX]
     * @param count 消息个数；非 0 时根据 startTime 逐页返回，传 0 默认取值为 100，取值范围 [0, 100]
     * @param order 返回排序。Ascending 升序，返回比 startTime 时间戳大的消息。Descending 降序，返回比 startTime 时间戳消的消息
     * @returns 消息列表
     * @version 1.3.0
     */
    searchMessagesByUsers(conId: ConversationIdentifier, sendIds: Array<string>, objNameArray: Array<string> | null, startTime: number, count: number, order: Order): Promise<IAsyncResult<List<Message>>>;
    /**
     * 获取本地订阅的所有公众号（仅支持私有云）
     * # 示例代码
     *```
     *  IMEngine.getInstance().getPublicServiceList().then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取公众号失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 公众号为空
     *      return;
     *    }
     *    // 公众号列表
     *    let list = result.data as List<PublicServiceInfo>;
     *  });
     *```
     * @returns
     * @version 1.1.0
     */
    getPublicServiceList(): Promise<IAsyncResult<List<PublicServiceInfo>>>;
    /**
     * 获取本地订阅的指定公众号
     * # 示例代码
     *```
     *  let conId = new ConversationIdentifier();
     *  conId.conversationType = ConversationType.AppPublicService;
     *  conId.targetId = this.targetId;
     *
     *  IMEngine.getInstance().getPublicService(conId).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取公众号失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 公众号为空
     *      return;
     *    }
     *    // 公众号信息
     *    let info = result.data as PublicServiceInfo;
     *  });
     *```
     * @param conId 会话标识，会话类型不管为何值，SDK 均会按照 AppPublicService 处理
     * @returns
     * @version 1.1.0
     */
    getPublicService(conId: ConversationIdentifier): Promise<IAsyncResult<PublicServiceInfo>>;
    /**
     * 设置聊天室状态监听
     * # 示例代码
     *```
     *  let listener: ChatroomStatusListener = {
     *    onChatroomJoining(roomId: string): void {
     *
     *    },
     *    onChatroomJoined(roomId: string, info: ChatroomJoinedInfo): void {
     *
     *    },
     *    onChatroomJoinFailed(roomId: string, code: EngineError): void {
     *
     *    },
     *    onChatroomQuited(roomId: string): void {
     *
     *    },
     *    onChatroomDestroyed(roomId: string, type: ChatroomDestroyType): void {
     *
     *    },
     *  }
     *  IMEngine.getInstance().setChatroomStatusListener(listener);
     *```
     * @param listener 监听
     * @see ChatroomStatusListener
     * @version 1.0.0
     * @deprecated 使用 addChatroomStatusListener 替代
     */
    setChatroomStatusListener(listener: ChatroomStatusListener): void;
    /**
     * 增加聊天室状态监听
     * # 示例代码
     *```
     * let listener: ChatroomStatusListener = {
     *  onChatroomJoining(roomId: string): void {
     *    hilog.info(0x0000, 'IM-App', 'onChatroomJoining roomId:%{public}s', roomId);
     *  },
     *
     *  onChatroomJoined(roomId: string, info: ChatroomJoinedInfo): void {
     *    hilog.info(0x0000, 'IM-App', 'onChatroomJoined roomId:%{public}s info:%{public}s', roomId, JSON.stringify(info));
     *  },
     *
     *  onChatroomJoinFailed(roomId: string, code: EngineError): void {
     *    hilog.info(0x0000, 'IM-App', 'onChatroomJoined roomId:%{public}s code:%{public}d', roomId, code);
     *  },
     *
     *  onChatroomQuited(roomId: string): void {
     *    hilog.info(0x0000, 'IM-App', 'onChatroomQuited roomId:%{public}s', roomId);
     *  },
     *
     *  onChatroomDestroyed(roomId: string, type: ChatroomDestroyType): void {
     *    hilog.info(0x0000, 'IM-App', 'onChatroomDestroyed roomId:%{public}s type:%{public}d', roomId, type);
     *  },
     * }
     * IMEngine.getInstance().addChatroomStatusListener(listener);
     *```
     * @param listener 监听
     * @see ChatroomStatusListener
     * @warning addChatroomStatusListener & removeChatroomStatusListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     * @version 1.3.0
     */
    addChatroomStatusListener(listener: ChatroomStatusListener): void;
    /**
     * 移除聊天室状态监听
     * # 示例代码
     *```
     * IMEngine.getInstance().removeChatroomStatusListener(listener);
     *```
     * @param listener 监听
     * @warning addChatroomStatusListener & removeChatroomStatusListener 配合使用，避免内存泄露
     * @version 1.3.0
     */
    removeChatroomStatusListener(listener: ChatroomStatusListener): void;
    /**
     * 加入聊天室，如果聊天室不存在则创建聊天室
     * # 示例代码
     *```
     *  let roomId = "TestChatroomId"; // 按需填写实际的聊天室 id
     *  let msgCount = 5;
     *
     *  IMEngine.getInstance().joinChatroom(roomId, msgCount).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 加入聊天室失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 聊天室加入信息失败
     *      return;
     *    }
     *    let joinedInfo = result.data as ChatroomJoinedInfo;
     *  });
     *```
     * @param roomId roomId 聊天室 ID
     * @param msgCount msgCount 消息个数，-1 时不拉取任何消息，0 时拉取 10 条消息，最多只能拉取 50
     * @returns 结果
     * @version 1.0.2
     */
    joinChatroom(roomId: string, msgCount: number): Promise<IAsyncResult<ChatroomJoinedInfo>>;
    /**
     * 加入已经存在的聊天室
     * # 示例代码
     *```
     *  let roomId = "TestChatroomId"; // 按需填写实际的聊天室 id
     *  let msgCount = 5;
     *
     *  IMEngine.getInstance().joinExistingChatroom(roomId, msgCount).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 加入聊天室失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 聊天室加入信息失败
     *      return;
     *    }
     *    let joinedInfo = result.data as ChatroomJoinedInfo;
     *  });
     *```
     * @param roomId 聊天室 ID
     * @param msgCount 消息个数，-1 时不拉取任何消息，0 时拉取 10 条消息，最多只能拉取 50
     * @returns 结果
     * @discussion 如果加入不存在的聊天室会报错 ChatroomNotExist
     * @note 聊天室的创建需要通过 AppServer 调用 IM 服务的 ServerAPI 创建
     * @version 1.0.0
     */
    joinExistingChatroom(roomId: string, msgCount: number): Promise<IAsyncResult<ChatroomJoinedInfo>>;
    /**
     * 退出聊天室
     * # 示例代码
     *```
     *  let roomId = "TestChatroomId"; // 按需填写实际的聊天室 id
     *  IMEngine.getInstance().quitChatroom(roomId).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 退出聊天室失败
     *      return;
     *    }
     *  });
     *```
     * @param roomId 聊天室 ID
     * @returns 结果
     * @version 1.0.0
     */
    quitChatroom(roomId: string): Promise<IAsyncResult<void>>;
    /**
     * 获取聊天室信息
     * # 示例代码
     *```
     *  let roomId = "TestChatroomId"; // 按需填写实际的聊天室 id
     *
     *  let option: ICountOption = {
     *    count: 10,
     *    order: Order.Descending
     *  }
     *
     *  IMEngine.getInstance().getChatroomInfo(roomId, option).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取聊天室信息失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 聊天室信息为空
     *      return;
     *    }
     *    let chatroomInfo = result.data as ChatroomInfo;
     *  });
     *```
     * @param roomId 聊天室 ID
     * @param option 配置
     * @returns 聊天室信息
     * @see ICountOption
     * @version 1.0.0
     */
    getChatroomInfo(roomId: string, option: ICountOption): Promise<IAsyncResult<ChatroomInfo>>;
    /**
     * 设置聊天室自定义属性
     * # 说明
     *```
     * entries 最大限制为 10
     * key ： 聊天室属性名称，长度范围 [1~128]，支持大小写英文字母、数字、部分特殊符号 + = - _ 的组合方式
     * value : 聊天室属性对应的值，长度范围 [1~4096]
     *```
     * # 示例代码
     *```
     *  let roomId = "聊天室 ID";
     *
     *  let map = new Map<string, string>();
     *  map.set("key1","value1");
     *  map.set("key2","value2");
     *
     *  let autoDelete = true;
     *  let overWrite = true;
     *
     *  IMEngine.getInstance().setChatroomEntries(roomId, map, autoDelete, overWrite).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 设置 KV 失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 所有 KV 都设置成功
     *      return;
     *    }
     *    // 设置失败的 key 和对应的失败错误码，视情况决定是否重新设置失败的 KV
     *    let errorMap = result.data as Map<string, EngineError>;
     *  });
     *```
     * @param roomId 聊天室 ID
     * @param entries key-value 字典，长度范围 [1 ~ 10]
     * @param autoDelete 用户掉线或退出时，是否自动删除该 Key、Value 值；自动删除时不会发送通知
     * @param overWrite 是否强制覆盖
     * @returns 返回的具体结果，会明确特定 key 的具体错误
     * @discussion 每个聊天室支持设置最大 KV 数为 100，如果聊天室已经有 100 个 KV，则无法再增加新的 KV
     * @version 1.1.0
     */
    setChatroomEntries(roomId: string, entries: Map</* key */ string, /* value */ string>, autoDelete: boolean, overWrite: boolean): Promise<IAsyncResult<Map</* key */ string, EngineError>>>;
    /**
     * 删除聊天室自定义属性
     * # 示例代码
     *```
     *  let roomId = "聊天室 ID";
     *
     *  let list = new List<string>();
     *  list.add("key1");
     *  list.add("key2");
     *
     *  let isForce = true;
     *
     *  IMEngine.getInstance().deleteChatroomEntries(roomId, list, isForce).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 删除 KV 失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // 删除 KV 成功
     *      return;
     *    }
     *    // 删除失败的 K 和对应的错误，视情况决定是否重新删除失败的 KV
     *    let errorMap = result.data as Map<string,EngineError>;
     *
     *  });
     *```
     * @param roomId 聊天室 ID
     * @param keys key 数组，长度范围 [1,10]
     * @param isForce 是否强制删除
     * @returns 返回的具体结果，会明确特定 key 的具体错误
     * @version 1.1.0
     */
    deleteChatroomEntries(roomId: string, keys: List</* key */ string>, isForce: boolean): Promise<IAsyncResult<Map</* key */ string, EngineError>>>;
    /**
     * 获取本地指定一批聊天室自定义属性
     * # 示例代码
     *```
     *  let roomId = "聊天室 ID";
     *
     *  let list = new List<string>();
     *  list.add("key1");
     *  list.add("key2");
     *
     *  IMEngine.getInstance().getChatroomEntries(roomId, list).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取 KV 失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // KV 为空
     *      return;
     *    }
     *    // KV map
     *    let kvMap = result.data as Map<string,string>;
     *
     *  });
     *```
     * @param roomId 聊天室 ID
     * @param keys key 数组，长度范围 [1,100]
     * @returns 对应的 kv 信息
     * @discussion 见 ChatroomKVStatusListener.onChatroomKVSync
     * @version 1.1.0
     */
    getChatroomEntries(roomId: string, keys: List</* key */ string>): Promise<IAsyncResult<Map<string, string>>>;
    /**
     * 获取本地聊天室全部自定义属性
     * # 示例代码
     *```
     *  let roomId = "聊天室 ID";
     *
     *  IMEngine.getInstance().getAllChatroomEntries(roomId).then(result => {
     *    if (EngineError.Success !== result.code) {
     *      // 获取 KV 失败
     *      return;
     *    }
     *    if (!result.data) {
     *      // KV 为空
     *      return;
     *    }
     *    // KV map
     *    let kvMap = result.data as Map<string,string>;
     *  });
     *```
     * @param roomId 聊天室 ID
     * @returns 对应的 kv 信息
     * @discussion 见 ChatroomKVStatusListener.onChatroomKVSync
     * @version 1.1.0
     */
    getAllChatroomEntries(roomId: string): Promise<IAsyncResult<Map<string, string>>>;
    /**
     * 设置聊天室 KV 状态变化的监听
     * # 示例代码
     *```
     *  let listener: ChatroomKVStatusListener = {
     *    onChatroomKVSync: (roomId: string): void => {
     *
     *    },
     *    onChatroomKVUpdate: (roomId: string, entries: Map<string, string>): void => {
     *
     *    },
     *    onChatroomKVRemove: (roomId: string, entries: Map<string, string>): void => {
     *
     *    }
     *  }
     *  IMEngine.getInstance().setChatroomKVStatusListener(listener);
     *```
     * @param listener 聊天室 KV 状态变化的监听
     * @version 1.1.0
     * @deprecated 使用 addChatroomKVStatusListener 替代
     */
    setChatroomKVStatusListener(listener: ChatroomKVStatusListener): void;
    /**
     * 增加聊天室 KV 状态变化的监听
     * # 示例代码
     *```
     *  let listener: ChatroomKVStatusListener = {
     *    onChatroomKVSync: (roomId: string): void => {
     *
     *    },
     *    onChatroomKVUpdate: (roomId: string, entries: Map<string, string>): void => {
     *
     *    },
     *    onChatroomKVRemove: (roomId: string, entries: Map<string, string>): void => {
     *
     *    }
     *  }
     *  IMEngine.getInstance().addChatroomKVStatusListener(listener);
     *```
     * @param listener 监听
     * @warning addChatroomKVStatusListener & removeChatroomKVStatusListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     * @version 1.3.0
     */
    addChatroomKVStatusListener(listener: ChatroomKVStatusListener): void;
    /**
     * 移除聊天室 KV 状态变化的监听
     * # 示例代码
     *```
     *  IMEngine.getInstance().removeChatroomKVStatusListener(listener);
     *```
     * @param listener 监听
     * @warning addChatroomKVStatusListener & removeChatroomKVStatusListener 配合使用，避免内存泄露
     * @version 1.3.0
     */
    removeChatroomKVStatusListener(listener: ChatroomKVStatusListener): void;
    /**
     * 设置聊天室成员变化监听
     * # 示例代码
     *```
     *  let listener: ChatroomMemberActionListener = {
     *    onMemberChange: (actionModel: ChatRoomMemberActionModel): void => {
     *
     *    }
     *  }
     *  IMEngine.getInstance().setChatroomMemberListener(listener);
     *```
     * @param listener 成员变化监听
     * @version 1.1.0
     * @deprecated 使用 addChatroomMemberListener 替代
     */
    setChatroomMemberListener(listener: ChatroomMemberActionListener): void;
    /**
     * 增加聊天室成员变化监听
     * # 示例代码
     *```
     *  let listener: ChatroomMemberActionListener = {
     *    onMemberChange: (actionModel: ChatRoomMemberActionModel): void => {
     *
     *    }
     *  }
     *  IMEngine.getInstance().addChatroomMemberListener(listener);
     *```
     * @param listener 成员变化监听
     * @warning addChatroomMemberListener & removeChatroomMemberListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     * @version 1.3.0
     */
    addChatroomMemberListener(listener: ChatroomMemberActionListener): void;
    /**
     * 移除聊天室成员变化监听
     * # 示例代码
     *```
     *  IMEngine.getInstance().removeChatroomMemberListener(listener);
     *```
     * @param listener 成员变化监听
     * @warning addChatroomMemberListener & removeChatroomMemberListener 配合使用，避免内存泄露
     * @version 1.3.0
     */
    removeChatroomMemberListener(listener: ChatroomMemberActionListener): void;
    /**
     * 设置聊天室事件通知监听器
     * # 示例代码
     *```
     *  let listener: ChatroomNotifyEventListener = {
     *    onChatroomNotifyMultiLoginSync: (syncEvent: ChatroomSyncEvent): void => {
     *
     *    },
     *    onChatroomNotifyBlock: (blockEvent: ChatroomMemberBlockEvent): void => {
     *
     *    },
     *    onChatRoomNotifyBan: (banEvent: ChatroomMemberBanEvent): void => {
     *
     *    }
     *  }
     *  IMEngine.getInstance().setChatroomNotifyEventListener(listener);
     *```
     * @param listener 监听
     * @version 1.1.0
     * @deprecated 使用 addChatroomNotifyEventListener 替代
     */
    setChatroomNotifyEventListener(listener: ChatroomNotifyEventListener): void;
    /**
     * 增加聊天室事件通知监听器
     * # 示例代码
     *```
     *  let listener: ChatroomNotifyEventListener = {
     *    onChatroomNotifyMultiLoginSync: (syncEvent: ChatroomSyncEvent): void => {
     *
     *    },
     *    onChatroomNotifyBlock: (blockEvent: ChatroomMemberBlockEvent): void => {
     *
     *    },
     *    onChatRoomNotifyBan: (banEvent: ChatroomMemberBanEvent): void => {
     *
     *    }
     *  }
     *  IMEngine.getInstance().addChatroomNotifyEventListener(listener);
     *```
     * @param listener 监听
     * @warning addChatroomNotifyEventListener & removeChatroomNotifyEventListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     * @version 1.3.0
     */
    addChatroomNotifyEventListener(listener: ChatroomNotifyEventListener): void;
    /**
     * 移除聊天室事件通知监听器
     * # 示例代码
     *```
     *  IMEngine.getInstance().removeChatroomNotifyEventListener(listener);
     *```
     * @param listener 监听
     * @warning addChatroomNotifyEventListener & removeChatroomNotifyEventListener 配合使用，避免内存泄露
     * @version 1.3.0
     */
    removeChatroomNotifyEventListener(listener: ChatroomNotifyEventListener): void;
}
export { IMEngine };
