// @keepTs
// @ts-nocheck
/**
 * Created on 2024/10/23
 * <AUTHOR>
 */
import { UiMessage } from '../../../../conversation/model/UiMessage';
@Component
export declare struct MessageItem {
    @Prop
    isEdit: boolean;
    @ObjectLink
    messageModel: UiMessage;
    onSelected?: ((messageModel: UiMessage) => void);
    @Builder
    customBuilder(): void;
    @BuilderParam
    customBuilderParam: () => void;
    build(): void;
}
