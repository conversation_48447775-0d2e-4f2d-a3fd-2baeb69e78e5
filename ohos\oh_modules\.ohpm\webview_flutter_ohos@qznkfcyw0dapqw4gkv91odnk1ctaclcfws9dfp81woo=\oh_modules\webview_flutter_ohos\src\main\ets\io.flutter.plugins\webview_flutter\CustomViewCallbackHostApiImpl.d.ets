import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import { CustomViewCallback, CustomViewCallbackHostApi } from './GeneratedOhosWebView';
import { InstanceManager } from './InstanceManager';
export declare class CustomViewCallbackHostApiImpl extends CustomViewCallbackHostApi {
    binaryMessenger: BinaryMessenger;
    instanceManager: InstanceManager;
    /**
     * Constructs a {@link CustomViewCallbackHostApiImpl}.
     *
     * @param binaryMessenger used to communicate with Dart over asynchronous messages
     * @param instanceManager maintains instances stored to communicate with attached Dart objects
     */
    constructor(binaryMessenger: BinaryMessenger, instanceManager: InstanceManager);
    onCustomViewHidden(identifier: number): void;
    getCustomViewCallbackInstance(identifier: number): CustomViewCallback;
}
