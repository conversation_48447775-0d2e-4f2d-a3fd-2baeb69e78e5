                     export default class WinRound { private _left; private _right; private _top; private _bottom; private _g_width; private _g_height;                     constructor(left?: number, right?: number, top?: number, bottom?: number, width?: number, height?: number); setWinRound(left: number, right: number, top: number, bottom: number): void; setWinRoundLB(left: number, bottom: number): void; setWinRoundRT(right: number, top: number): void; get left(): number; set left(val: number); get right(): number; set right(val: number); get top(): number; set top(val: number); get bottom(): number; set bottom(val: number); getWidth(): number; getInnerWidth(): number; getHeight(): number; getInnerHeight(): number; getRightSpan(): number; getBottomSpan(): number; destroy(): void; toString(): string; } 