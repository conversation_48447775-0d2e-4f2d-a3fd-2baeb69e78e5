import { SearchResult } from "../../../d/e/h/f1";
import { TransitRoutePlanOption } from "../../../d/e/h/n";
import { RoutePlanParser, SearchRequest } from '../../base/base';
import { UrlProvider } from "../../base/d3";
export declare class TransitRouteParser extends RoutePlanParser {
    parseResult(p29: string, q29: SearchResult): void;
    private parseJsonToTransitRouteResult;
    private parseTaxiInfo;
}
export declare class TransitRouteRequest extends SearchRequest {
    constructor(e28: TransitRoutePlanOption);
    private transitRouteBuildParam;
    getUrlDomain(c28: UrlProvider): string;
}
