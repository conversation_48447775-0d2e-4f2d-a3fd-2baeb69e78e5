import { ConversationIdentifier } from "../../conversation/ConversationIdentifier";
import { ReadReceiptUserInfo } from "./ReadReceiptUserInfo";
/**
 * 已读回执通知，用于已读 V5 接收被动通知信息。
 *
 * @version 1.5.0
 */
export declare class ReadReceiptResponseV5 {
    /**
     * 消息会话信息
     */
    conId: ConversationIdentifier;
    /**
     * 消息 UID
     */
    messageUid: string;
    /**
     * 已读用户信息列表
     */
    responseUserArray: Array<ReadReceiptUserInfo>;
    /**
     * 已读用户信息列表长度
     */
    responseUserArrayLen: number;
    /**
     * 消息发送时间
     */
    messageTime: number;
    /**
     * 已读回执人数
     * */
    readCount: number;
    /**
     * 未读人数
     * */
    unreadCount: number;
    /**
     * 全部人数
     * */
    totalCount: number;
}
