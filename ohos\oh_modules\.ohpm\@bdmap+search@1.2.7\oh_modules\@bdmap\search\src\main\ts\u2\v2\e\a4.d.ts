import { SearchResult } from "../../../d/e/h/f1";
import { SearchParser } from '../../base/base';
/**
 * @version 2.1
 * @description 骑行路线规划封装和解析逻辑
 * @date 2024-6-14
 */
export declare class BikeRouteParser extends SearchParser {
    parseSearchResult(r19: string): SearchResult;
    private parseStringToBikeRouteResult;
    private parseBikeSteps;
    private parseLatLng;
    private parseStringtoMKRouteAddress;
    private parseBKStringToCityListInfo;
    private parseBRAddrPoiList;
    private parseRouteBikeNode;
}
