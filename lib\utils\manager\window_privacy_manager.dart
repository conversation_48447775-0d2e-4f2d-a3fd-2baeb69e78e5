import 'package:flutter/services.dart';
import 'package:flutter_platform_utils/flutter_platform_utils.dart';

import 'log_manager.dart';
class WindowPrivacyManager {
  static const MethodChannel _channel = MethodChannel('com.sgmw.flutter/privacy');

  static Future<void> enterPrivacyMode() async {
    if(PlatformUtils.isOhos) {
      try {
        await _channel.invokeMethod('enterPrivacyMode');
        LogManager().debug("Succeed to enter privacy mode");
      } on PlatformException catch (e) {
        LogManager().debug("Failed to enter privacy mode: '${e.message}'.");
        throw e;
      }
    }
  }

  static Future<void> exitPrivacyMode() async {
    if (PlatformUtils.isOhos) {
      try {
        await _channel.invokeMethod('exitPrivacyMode');
        LogManager().debug("Succeed to exit privacy mode");
      } on PlatformException catch (e) {
        LogManager().debug("Failed to exit privacy mode: '${e.message}'.");
        throw e;
      }
    }
  }
}