// @keepTs
// @ts-nocheck
import { ConnectionService } from './connect/ConnectionService';
import { ConversationService } from './conversation/ConversationService';
import { ConversationListService } from './conversationlist/ConversationListService';
import { MessageService } from './message/MessageService';
import { NotificationService } from './notification/NotificationService';
import { UserDataService } from './user/UserDataService';
/**
 * IMKit 核心类
 * @version 1.0.0
 */
declare class RongIM {
    private static instance;
    private impl;
    private constructor();
    /**
     * 单例方法
     * @returns 核心类
     */
    static getInstance(): RongIM;
    /**
     * 连接服务
     * @returns
     */
    connectionService(): ConnectionService;
    /**
     * 消息服务
     * @returns
     */
    messageService(): MessageService;
    /**
     * 会话服务（聊天页面）
     * @returns
     */
    conversationService(): ConversationService;
    /**
     * 会话列表服务（会话列表页面）
     * @returns
     */
    conversationListService(): ConversationListService;
    /**
     * 用户数据服务（用户信息、群组信息、群成员信息）
     * @returns
     */
    userDataService(): UserDataService;
    /**
     * 本地通知管理服务
     * @returns
     */
    notificationService(): NotificationService;
}
export { RongIM };
