import resourceManager from "@kit.LocalizationKit"; import image from '@ohos.multimedia.image'; interface ImageInfo { image_hashcode: string; image_data: image.PixelMap; image_width: number; image_height: number; image_scaleDpi: number; image_format: number; } export function setSysValues(filesDir: string, cacheDir: string, densityDPI: number, resourceManager: resourceManager.ResourceManager, callback?: (result: string) => void); export function NapiBmObject_Finalizer(h73: number); export function NapiBmObject_SetLayerTag(f73: number, g73: string); export function NapiBmDrawItem_SetVisibility(e73: number, visibility: number); export function NapiBmDrawItem_SetShowLevel(d73: number, from: number, to: number); export function NapiBmDrawItem_SetOpacity(c73: number, opacity: number); export function NapiBmDrawItem_SetClickable(a73: number, b73: boolean); export function NapiBmDrawItem_SetHoleClickable(y72: number, z72: boolean); export function NapiBmDrawItem_SetAnimation(w72: number, x72: number); export function NapiBmLayer_Create(); export function NapiBmLayer_GetLayerId(v72: number): number; export function NapiBmLayer_SetCollisionBaseMap(t72: number, u72: boolean); export function NapiBmLayer_CommitUpdate(s72: number); export function NapiBmLayer_SetVisibility(r72: number, visibility: number); export function NapiBmLayer_SetClickable(q72: number, clickable: boolean); export function NapiBmLayer_SetShowLevel(n72: number, o72: number, p72: number); export function NapiBmLayer_AddDrawItem(l72: number, m72: number); export function NapiBmLayer_AddDrawItemByZIndex(i72: number, j72: number, k72: number); export function NapiBmLayer_AddDrawItemBelow(f72: number, g72: number, h72: number); export function NapiBmLayer_AddDrawItemAbove(c72: number, d72: number, e72: number); export function NapiBmLayer_RemoveDrawItem(a72: number, b72: number); export function NapiBmLayer_ClearDrawItems(z71: number); export function NapiBmLayer_GetDrawItemRect(y71: number, left: number, top: number, right: number, bottom: number); export function NapiBmLayer_HandleClick(v71: number, w71: number, x71: number, radius: number); export function NapiBmCircle_Create(); export function NapiBmCircle_SetCenter(u71: number, x: number, y: number, z: number); export function NapiBmCircle_SetRadius(t71: number, radius: number); export function NapiBmCircle_SetPixelRadius(s71: number, radius: number); export function NapiBmCircle_SetLineStyle(q71: number, r71: number); export function NapiBmCircle_SetSurfaceStyle(o71: number, p71: number); export function NapiBmCircle_SetIsGradientCircle(m71: number, n71: boolean); export function NapiBmCircle_SetGradientRadiusWeight(k71: number, l71: number); export function NapiBmCircle_SetGradientColorWeight(i71: number, j71: number); export function NapiBmCircle_SetGradientColors(h71: number, type: number, colors: number[]); export function NapiBmSurfaceStyle_Create(); export function NapiBmSurfaceStyle_SetColor(g71: number, color: number); export function NapiBmSurfaceStyle_SetBitmapResource(e71: number, f71); export function NapiBmSurfaceStyle_SetBmpResId(c71: number, d71: number); export function NapiBmBaseMarker_SetXYZ(b71: number, x: number, y: number, z: number); export function NapiBmBaseMarker_SetX(a71: number, x: number); export function NapiBmBaseMarker_SetY(z70: number, y: number); export function NapiBmBaseMarker_SetZ(y70: number, z: number); export function NapiBmBaseMarker_SetPerspective(x70: number, perspective: number); export function NapiBmBaseMarker_SetIsFix(v70: number, w70: number); export function NapiBmBaseMarker_SetFixX(t70: number, u70: number); export function NapiBmBaseMarker_SetFixY(r70: number, s70: number); export function NapiBmBaseMarker_SetWidth(q70: number, width: number); export function NapiBmBaseMarker_SetHeight(p70: number, height: number); export function NapiBmBaseMarker_SetLocated(n70: number, o70: number); export function NapiBmBaseMarker_SetOffsetX(m70: number, offsetX: number, scaleMode: number); export function NapiBmBaseMarker_SetOffsetY(l70: number, offsetY: number, scaleMode: number); export function NapiBmBaseMarker_SetScale(k70: number, scale: number); export function NapiBmBaseMarker_SetScaleX(j70: number, scaleX: number); export function NapiBmBaseMarker_SetScaleY(i70: number, scaleY: number); export function NapiBmBaseMarker_SetRotate(h70: number, rotate: number); export function NapiBmBaseMarker_SetCollisionBehavior(f70: number, g70: number); export function NapiBmBaseMarker_SetCollisionPriority(e70: number, priority: number); export function NapiBmBaseMarker_SetTrackBy(c70: number, d70: number); export function NapiBmBaseMarker_SetRotateFeature(b70: number, feature: number); export function NapiBmBaseMarker_SetFollowMapRotateAxis(z69: number, a70: number); export function NapiBmBaseMarker_SetId(x69: number, y69: string); export function NapiBmBaseMarker_SetBuildingId(v69: number, w69: string); export function NapiBmBaseMarker_SetFloorId(t69: number, u69: string); export function NapiBmBaseMarker_AddRichView(r69: number, s69: number); export function NapiBmBaseMarker_RemoveRichView(p69: number, q69: number); export function NapiBmBaseMarker_ClearRichViews(o69: number); export function NapiBmBaseMarker_SetDrawFullscreenMaskFlag(n69: number, flag: boolean); export function NapiBmIconMarker_Create(); export function NapiBmIconMarker_SetBmpResId(m69: number, resId: number); export function NapiBmIconMarker_SetDrawableResource(k69: number, l69: number); export function NapiBmIconMarker_SetColor(i69: number, j69: number); export function NapiBmIconMarker_SetAnimationType(h69: number, type: number); export function NapiBmPrism_Create(); export function NapiBmPrism_AddGeoElement(f69: number, g69: number); export function NapiBmPrism_ClearGeoElements(e69: number); export function NapiBmPrism_SetSurfaceTopStyle(c69: number, d69: number); export function NapiBmPrism_SetSurfaceSideStyle(a69: number, b69: number); export function NapiBmPrism_SetSurfaceFloorTopStyle(y68: number, z68: number); export function NapiBmPrism_SetSurfaceFloorSideStyle(w68: number, x68: number); export function NapiBmPrism_SetHeight(v68: number, height: number); export function NapiBmPrism_SetLastFloorHeight(u68: number, height: number); export function NapiBmPrism_SetFloorHeight(t68: number, height: number); export function NapiBmPrism_SetBuildingID(r68: number, s68: string); export function NapiBmPrism_SetIsAnimation(p68: number, q68: boolean); export function NapiBmPrism_SetHasFloor(n68: number, o68: boolean); export function NapiBmPrism_SetIsBuilding(l68: number, m68: boolean); export function NapiBmPrism_SetAnimateType(j68: number, k68: number); export function NapiBmPrism_SetFloorAnimateType(h68: number, i68: number); export function NapiBmPrism_SetIsRoundedCorner(f68: number, g68: boolean); export function NapiBmPrism_SetRoundedCornerRadius(d68: number, e68: number); export function NapiBmBitmapResource_Create(); export function NapiBmBitmapResource_SetBitmap(c68: number, image: ImageInfo); export function NapiBmBitmapResource_SetScaleX(b68: number, scale: number[], size: number); export function NapiBmBitmapResource_SetScaleY(a68: number, scale: number[], size: number); export function NapiBmBitmapResource_SetFillArea(z67: number, x1: number, x2: number, y1: number, y2: number); export function NapiBmTextStyle_Create(); export function NapiBmTextStyle_SetTextColor(x67: number, y67: number); export function NapiBmTextStyle_SetTextSize(w67: number, textSize: number); export function NapiBmTextStyle_SetBorderColor(u67: number, v67: number); export function NapiBmTextStyle_SetBorderWidth(t67: number, width: number); export function NapiBmTextStyle_SetFontOption(s67: number, option: number); export function NapiBmTextStyle_SetTextBackColor(r67: number, option: number); export function NapiBmBaseUI_SetBackground(p67: number, q67: number); export function NapiBmBaseUI_SetBackgroundResId(o67: number, resId: number); export function NapiBmBaseUI_SetBackgroundColor(m67: number, n67: number); export function NapiBmBaseUI_SetBkColorOfLeft(k67: number, l67: number); export function NapiBmBaseUI_SetBkColorOfRight(i67: number, j67: number); export function NapiBmBaseUI_SetWidth(h67: number, width: number); export function NapiBmBaseUI_SetHeight(g67: number, height: number); export function NapiBmBaseUI_SetVisibility(f67: number, visibility: number); export function NapiBmBaseUI_SetGravity(e67: number, gravity: number); export function NapiBmBaseUI_SetAlignParent(c67: number, d67: number); export function NapiBmBaseUI_SetLayoutWeight(b67: number, layoutWeight: number); export function NapiBmBaseUI_SetPadding(a67: number, left: number, top: number, right: number, bottom: number); export function NapiBmBaseUI_SetMargin(z66: number, left: number, top: number, right: number, bottom: number); export function NapiBmBaseUI_SetClickable(x66: number, y66: boolean); export function NapiBmLabelUI_Create(); export function NapiBmLabelUI_SetText(instance: number, text: string); export function NapiBmLabelUI_SetStyle(instance: number, w66: number); export function NapiBmLabelUI_SetMinLines(instance: number, v66: number); export function NapiBmLabelUI_SetMaxLines(instance: number, maxLines: number); export function NapiBmRichView_Create(); export function NapiBmRichView_SetView(t66: number, u66: number); export function NapiBmRichView_SetAnimation(r66: number, s66: number); export function NapiBmRichView_SetCollisionBehavior(p66: number, q66: number); export function NapiBmRichView_SetCollisionPriority(o66: number, priority: number); export function NapiBmRichView_SetCollisionBorder(n66: number, left: number, top: number, right: number, bottom: number); export function NapiBmRichView_SetCollisionLineTagId(l66: number, m66: number); export function NapiBmRichView_SetVisibility(k66: number, visibility: number); export function NapiBmRichView_SetShowLevel(j66: number, from: number, to: number); export function NapiBmRichView_SetLocated(h66: number, i66: number); export function NapiBmRichView_SetOffsetX(g66: number, offsetX: number, scaleMode: number); export function NapiBmRichView_SetOffsetY(f66: number, offsetY: number, scaleMode: number); export function NapiBmRichView_SetOpacity(e66: number, opacity: number); export function NapiBmRichView_SetScale(d66: number, scale: number); export function NapiBmRichView_SetScaleX(c66: number, scaleX: number); export function NapiBmRichView_SetScaleY(b66: number, scaleY: number); export function NapiBmRichView_AddRichUIOption(z65: number, a66: number); export function NapiBmRichView_DelRichUIOption(x65: number, y65: number); export function NapiBmRichView_SetDrawFullscreenMaskFlag(instance: number, flag: boolean); export function NapiBmLineStyle_Create(); export function NapiBmLineStyle_SetColor(w65: number, color: number); export function NapiBmLineStyle_SetBitmapResource(u65: number, v65: number); export function NapiBmLineStyle_SetBmpResId(s65: number, t65: number); export function NapiBmLineStyle_SetLineResId(q65: number, r65: number); export function NapiBmLineStyle_SetWidth(p65: number, width: number); export function NapiBmLineStyle_SetStrokeWidth(o65: number, width: number); export function NapiBmLineStyle_SetStrokeColor(n65: number, color: number); export function NapiBmLineStyle_SetTextureOption(m65: number, option: number); export function NapiBmLineStyle_SetLineType(l65: number, lineType: number); export function NapiBmGround_Create(); export function NapiBmGround_SetPosition(k65: number, x: number, y: number, z: number); export function NapiBmGround_SetWidth(j65: number, width: number); export function NapiBmGround_SetHeight(i65: number, height: number); export function NapiBmGround_SetAnchorX(g65: number, h65: number); export function NapiBmGround_SetAnchorY(e65: number, f65: number); export function NapiBmGround_SetDrawableResource(c65: number, d65: number); export function NapiBmGeoElement_Create(b65: number); export function NapiBmGeoElement_AddPoint(x64: number, y64: number, z64: number, a65: number); export function NapiBmGeoElement_SetPoints(v64: number, w64: Array<number>, stride: number); export function NapiBmGeoElement_SetStyle(t64: number, u64: number); export function NapiBmGeoElement_SetTrackStyle(r64: number, s64: number); export function NapiBmGeoElement_AddStyleOption(p64: number, q64: number); export function NapiBmGeoElement_RemoveStyleOption(n64: number, o64: number); export function NapiBmGeoElement_SetGradientColors(m64: number, type: number, colors: Array<number>); export function NapiBmGeoElement_DelGradientColors(l64: number, type: number); export function NapiBmGeoElement_ClearGradientColors(k64: number); export function NapiBmGeoElement_SetCoordChainType(i64: number, j64: number); export function NapiBmGeoElement_SetCoordChainHandle(g64: number, h64: number); export function NapiBmTrackStyle_Create(); export function NapiBmTrackStyle_SetColor(f64: number, color: number); export function NapiBmTrackStyle_SetWidth(e64: number, width: number); export function NapiBmTrackStyle_SetBitmapResource(c64: number, d64: number); export function NapiBmTrackStyle_SetPaletteBitmapResource(a64: number, b64: number); export function NapiBmTrackStyle_SetTrackType(z63: number, lineType: number); export function NapiBmTrackStyle_SetOpacity(y63: number, opacity: number); export function NapiBmTrackStyle_SetPaletteOpacity(x63: number, opacity: number); export function NapiBmLineStyleOption_Create(); export function NapiBmLineStyleOption_BuildStyleOption(v63: number, state: number, w63: number); export function NapiBmCoordChainHandle_Create(); export function NapiBmCoordChainHandle_SetCoordChainType(t63: number, u63: number); export function NapiBmCoordChainHandle_SetCoordAlgorithm(r63: number, s63: number); export function NapiBmCoordChainHandle_SetThreshold(q63: number, threshold: number); export function NapiBmCoordChainHandle_Handle(o63: number, p63: Array<number>, stride: number); export function NapiBmCoordChainHandle_GetIndexs(n63: number); export function NapiBmCoordChainHandle_GetP0Points(m63: number); export function NapiBmPolygon_Create(); export function NapiBmPolygon_AddGeoElement(k63: number, l63: number); export function NapiBmPolygon_AddHoleGeoElement(i63: number, j63: number); export function NapiBmPolygon_ClearGeoElements(h63: number); export function NapiBmPolygon_SetThin(f63: number, g63: number); export function NapiBmPolygon_SetThinFactor(e63: number, factor: number); export function NapiBmPolygon_SetJointType(d63: number, jointType: number); export function NapiBmPolygon_SetSurfaceStyle(b63: number, c63: number); export function NapiBmPolygon_SetDrawFullscreenMaskFlag(a63: number, flag: boolean); export function NapiBmBaseLine_SetGeoElement(y62: number, z62: number); export function NapiBmBaseLine_AddGeoElement(w62: number, x62: number); export function NapiBmBaseLine_ClearGeoElements(v62: number); export function NapiBmBaseLine_SetSmooth(u62: number, smooth: number); export function NapiBmBaseLine_SetThin(s62: number, t62: number); export function NapiBmBaseLine_SetSmoothFactor(r62: number, factor: number); export function NapiBmBaseLine_SetThinFactor(q62: number, factor: number); export function NapiBmBaseLine_SetStartCapType(o62: number, p62: number); export function NapiBmBaseLine_SetEndCapType(m62: number, n62: number); export function NapiBmBaseLine_SetJointType(l62: number, jointType: number); export function NapiBmBaseLine_SetCollisionTagId(j62: number, k62: number); export function NapiBmBaseLine_SetCollisionBehavior(h62: number, i62: number); export function NapiBmBaseLine_SetLineBloomMode(g62: number, mode: number); export function NapiBmBaseLine_SetBloomBlurTimes(f62: number, time: number); export function NapiBmBaseLine_SetLineDirectionCrossType(e62: number, type: number); export function NapiBmBaseLine_SetBloomAlpha(d62: number, alpha: number); export function NapiBmBaseLine_SetBloomWidth(c62: number, width: number); export function NapiBmBaseLine_SetBloomGradientASpeed(b62: number, speed: number); export function NapiBmGradientLine_Create(); export function NapiBmPolyline_Create(); export function NapiBmPolyline_UseGeodesic(z61: number, a62: boolean); export function NapiBmTextMarker_Create(); export function NapiBmTextMarker_SetText(y61: number, text: string); export function NapiBmTextMarker_SetStyle(w61: number, x61: number); export function NapiBmImageUI_Create(); export function NapiBmImageUI_SetBmpResId(v61: number, resId: number); export function NapiBmImageUI_SetDrawableResource(t61: number, u61: number); export function NapiBmImageUI_SetMaskResource(r61: number, s61: number); export function NapiBmImageUI_SetColor(p61: number, q61: number); export function NapiBmGroupUI_AddView(n61: number, o61: number, index: number); export function NapiBmGroupUI_RemoveAllViews(m61: number); export function NapiBmFrameLayout_Create(); export function NapiBmHorizontalLayout_Create(); export function NapiBmVerticalLayout_Create(); 