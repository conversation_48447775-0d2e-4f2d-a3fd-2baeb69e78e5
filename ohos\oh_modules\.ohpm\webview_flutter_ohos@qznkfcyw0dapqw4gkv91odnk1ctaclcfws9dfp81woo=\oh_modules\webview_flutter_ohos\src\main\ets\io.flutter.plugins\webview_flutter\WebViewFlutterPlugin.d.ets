import { FlutterPluginBinding, FlutterPlugin } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import PlatformViewRegistry from '@ohos/flutter_ohos/src/main/ets/plugin/platform/PlatformViewRegistry';
import { FlutterAssetManager } from './FlutterAssetManager';
import { InstanceManager } from './InstanceManager';
export declare class WebViewFlutterPlugin implements FlutterPlugin {
    getUniqueClassName(): string;
    private instanceManager;
    setUp(binaryMessenger: BinaryMessenger, viewRegistry: PlatformViewRegistry, context: Context, flutterAssetManager: FlutterAssetManager): void;
    onAttachedToEngine(binding: FlutterPluginBinding): void;
    onDetachedFromEngine(binding: FlutterPluginBinding): void;
    getInstanceManager(): InstanceManager;
}
