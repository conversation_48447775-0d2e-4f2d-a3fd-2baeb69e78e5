          import { UnitOption } from "../../util/b1/c1"; import Bundle from "../o/i1"; import Stroke from "../o/p"; import { LatLng } from '@bdmap/base'; import Overlay from "./m"; import type { ICircleOption, Nullable, ColorString } from "../../g1/a2"; import type OverlayMgr from "./j2"; import type { ImageOverlayData } from "../../g1/i1"; import type OverlayListener from "./i2";             export default class Circle extends Overlay { private mCenter; private mRadius; private mRadiusUnit; private mFillcolor; private mStroke; private mIsGradientCircle; private mGradientColors; private mGradientRadiusWeight; private mGradientColorWeight; private surfaceStyle; private bmCircle;                                   constructor(x36?: ICircleOption);       init(): void;         center(center: LatLng): this;         getCenter(): LatLng;         setCenter(center: LatLng): void;         radius(radius: number): this;         getRadius(): number;         setRadius(radius: number): void;         radiusUnit(v36: UnitOption): this;         setRadiusUnit(u36: UnitOption): void;         getRadiusUnit(): UnitOption;         fillcolor(color: ColorString): this;         getFillcolor(): any;         setFillcolor(color: ColorString): void;         isGradientCircle(enable: boolean): this;         setIsGradientCircle(enable: boolean): void;         getIsGradientCircle(): boolean;         gradientColors(colors: Array<ColorString>): this;         setGradientColors(colors: Array<ColorString>): void;         getGradientColors(): string[];         gradientRadiusWeight(weight: number): this;         setGradientRadiusWeight(weight: number): void;         getGradientRadiusWeight(): number;         gradientColorWeight(weight: number): this;         setGradientColorWeight(weight: number): void;         getGradientColorWeight(): number;       setListener(listener: OverlayListener): void;         stroke(stroke: Stroke): this;         getStroke(): Stroke;         setStroke(stroke: Stroke): void; get typeName(): string;           emitByStroke(): void;       dataFormat(q36: Array<ImageOverlayData>): Nullable<Bundle>;       preUpdatePoint(): void;       toBundle(l36: OverlayMgr): Promise<Bundle>;       toString(): string; } 