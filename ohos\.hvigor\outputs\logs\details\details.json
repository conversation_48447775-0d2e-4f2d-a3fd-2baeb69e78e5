{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode", "INCREMENTAL_TASKS": {"COMPILE_ARKTS": true}}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": true}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": false, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"PreBuild": 237554800, "CreateModuleInfo": 4018700, "ConfigureCmake": 542200, "MergeProfile": 252947900, "CreateBuildProfile": 7792400, "PreCheckSyscap": 889900, "ProcessIntegratedHsp": 4716700, "BuildNativeWithCmake": 621500, "MakePackInfo": 10000900, "SyscapTransform": 26580000, "ProcessProfile": 458992900, "ProcessRouterMap": 62845800, "ProcessStartupConfig": 19744600, "BuildNativeWithNinja": 3287200, "ProcessResource": 17858200, "GenerateLoaderJson": 251512000, "ProcessLibs": 195957900, "CompileResource": 1523275600, "BuildJS": 23698600, "DoNativeStrip": 5234462700, "CacheNativeLibs": 30508600, "CompileArkTS": 32596451500, "GeneratePkgModuleJson": 11578200, "PackageHap": 2221678200, "SignHap": 6319285100, "CollectDebugSymbol": 9617700, "assembleHap": 491100}}, "APIS": ["getHvigorConfig", "getRootNodeDescriptor", "getChildNode", "getNodeByName", "getAllNodes", "getRootNode", "getNodeName", "getStartParams", "buildFinished", "getNodePath", "subNodes", "getExtraOption", "getExtParam", "getExtParams", "getProperty", "getProperties", "getProductName", "nodesEvaluated", "getParameter", "getCommandEntryTask"], "CONFIG_PROPERTIES": {"enableSignTask": true, "skipNativeIncremental": false, "hvigor_keepDependency": true, "ohos_nativeResolver": false}, "BUILD_ID": "202507291021007090", "TOTAL_TIME": 51365590500}}