{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode", "INCREMENTAL_TASKS": {"COMPILE_ARKTS": true}}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": true}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": false, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"PreBuild": 443836400, "CreateModuleInfo": 4042900, "ConfigureCmake": 757600, "MergeProfile": 423522900, "CreateBuildProfile": 8032500, "PreCheckSyscap": 1151600, "ProcessIntegratedHsp": 6076500, "BuildNativeWithCmake": 1283300, "MakePackInfo": 12614700, "SyscapTransform": 26473200, "ProcessProfile": 510914200, "ProcessRouterMap": 70197600, "ProcessStartupConfig": 24982400, "BuildNativeWithNinja": 3887900, "ProcessResource": 22459000, "GenerateLoaderJson": 314177300, "ProcessLibs": 249191300, "CompileResource": 2984564200, "BuildJS": 23415600, "DoNativeStrip": 15333776900, "CacheNativeLibs": 33262200, "CompileArkTS": 50543058300, "GeneratePkgModuleJson": 9909700, "PackageHap": 2758910100, "SignHap": 8381904500, "CollectDebugSymbol": 14036200, "assembleHap": 472400}}, "APIS": ["getHvigorConfig", "getRootNodeDescriptor", "getChildNode", "getNodeByName", "getAllNodes", "getRootNode", "getNodeName", "getStartParams", "buildFinished", "getNodePath", "subNodes", "getExtraOption", "getExtParam", "getExtParams", "getProperty", "getProperties", "getProductName", "nodesEvaluated", "getParameter", "getCommandEntryTask"], "CONFIG_PROPERTIES": {"enableSignTask": true, "skipNativeIncremental": false, "hvigor_keepDependency": true, "ohos_nativeResolver": false}, "BUILD_ID": "202507291000425430", "TOTAL_TIME": 75513353400}}