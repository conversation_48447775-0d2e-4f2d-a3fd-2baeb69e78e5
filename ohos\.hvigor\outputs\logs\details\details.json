{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode", "INCREMENTAL_TASKS": {"COMPILE_ARKTS": true}}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": true}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": false, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"PreBuild": 324916800, "CreateModuleInfo": 5133700, "ConfigureCmake": 907400, "MergeProfile": 389143100, "CreateBuildProfile": 8126800, "PreCheckSyscap": 941300, "ProcessIntegratedHsp": 5793800, "BuildNativeWithCmake": 667500, "MakePackInfo": 12827200, "SyscapTransform": 24291300, "ProcessProfile": 711941500, "ProcessRouterMap": 96201200, "ProcessStartupConfig": 28901700, "BuildNativeWithNinja": 3935000, "ProcessResource": 23516200, "GenerateLoaderJson": 313911900, "ProcessLibs": 274433700, "CompileResource": 2403620400, "BuildJS": 43445700, "DoNativeStrip": 7035568100, "CacheNativeLibs": 36599000, "CompileArkTS": 54342825000, "GeneratePkgModuleJson": 16875300, "PackageHap": 3611564500, "SignHap": 8035709200, "CollectDebugSymbol": 10909500, "assembleHap": 436800}}, "APIS": ["getHvigorConfig", "getRootNodeDescriptor", "getChildNode", "getNodeByName", "getAllNodes", "getRootNode", "getNodeName", "getStartParams", "buildFinished", "getNodePath", "subNodes", "getExtraOption", "getExtParam", "getExtParams", "getProperty", "getProperties", "getProductName", "nodesEvaluated", "getParameter", "getCommandEntryTask"], "CONFIG_PROPERTIES": {"enableSignTask": true, "skipNativeIncremental": false, "hvigor_keepDependency": true, "ohos_nativeResolver": false}, "BUILD_ID": "202507291052179120", "TOTAL_TIME": 79944039500}}