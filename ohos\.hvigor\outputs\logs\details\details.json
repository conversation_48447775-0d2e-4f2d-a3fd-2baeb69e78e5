{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode", "INCREMENTAL_TASKS": {"COMPILE_ARKTS": true}}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": true}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": false, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"PreBuild": 306510600, "CreateModuleInfo": 5975800, "ConfigureCmake": 2320600, "MergeProfile": 361535500, "CreateBuildProfile": 9157000, "PreCheckSyscap": 1065300, "ProcessIntegratedHsp": 4724000, "BuildNativeWithCmake": 643600, "MakePackInfo": 10873900, "SyscapTransform": 28351800, "ProcessProfile": 206296300, "ProcessRouterMap": 65179300, "ProcessStartupConfig": 22218500, "BuildNativeWithNinja": 2979500, "ProcessResource": 20377100, "GenerateLoaderJson": 290420700, "ProcessLibs": 223432600, "CompileResource": 2742290900, "BuildJS": 22090400, "DoNativeStrip": 5206292000, "CacheNativeLibs": 280780200, "CompileArkTS": 48457953400, "GeneratePkgModuleJson": 7205700, "PackageHap": 3114416300, "SignHap": 7083502500, "CollectDebugSymbol": 9912200, "assembleHap": 484100}}, "APIS": ["getHvigorConfig", "getRootNodeDescriptor", "getChildNode", "getNodeByName", "getAllNodes", "getRootNode", "getNodeName", "getStartParams", "buildFinished", "getNodePath", "subNodes", "getExtraOption", "getExtParam", "getExtParams", "getProperty", "getProperties", "getProductName", "nodesEvaluated", "getParameter", "getCommandEntryTask"], "CONFIG_PROPERTIES": {"enableSignTask": true, "skipNativeIncremental": false, "hvigor_keepDependency": true, "ohos_nativeResolver": false}, "BUILD_ID": "202507281031318450", "TOTAL_TIME": 75571091500}}