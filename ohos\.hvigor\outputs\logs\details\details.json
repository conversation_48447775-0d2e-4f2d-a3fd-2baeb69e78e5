{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode", "INCREMENTAL_TASKS": {"COMPILE_ARKTS": true}}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": true}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": false, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"PreBuild": 394056000, "CreateModuleInfo": 4825800, "ConfigureCmake": 847000, "MergeProfile": 384485900, "CreateBuildProfile": 11164900, "PreCheckSyscap": 1223000, "ProcessIntegratedHsp": 6385500, "BuildNativeWithCmake": 799000, "MakePackInfo": 13188000, "SyscapTransform": 33916500, "ProcessProfile": 676182800, "ProcessRouterMap": 71783200, "ProcessStartupConfig": 35976300, "BuildNativeWithNinja": 3458300, "ProcessResource": 26041800, "GenerateLoaderJson": 436729000, "ProcessLibs": 562639100, "CompileResource": 3736454600, "BuildJS": 39629100, "DoNativeStrip": 7989763300, "CacheNativeLibs": 43465400, "CompileArkTS": 52057049500, "GeneratePkgModuleJson": 12192400, "PackageHap": 3123191600, "SignHap": 8702565200, "CollectDebugSymbol": 11611000, "assembleHap": 645600}}, "APIS": ["getHvigorConfig", "getRootNodeDescriptor", "getChildNode", "getNodeByName", "getAllNodes", "getRootNode", "getNodeName", "getStartParams", "buildFinished", "getNodePath", "subNodes", "getExtraOption", "getExtParam", "getExtParams", "getProperty", "getProperties", "getProductName", "nodesEvaluated", "getParameter", "getCommandEntryTask"], "CONFIG_PROPERTIES": {"enableSignTask": true, "skipNativeIncremental": false, "hvigor_keepDependency": true, "ohos_nativeResolver": false}, "BUILD_ID": "202507291037468850", "TOTAL_TIME": 81557194900}}