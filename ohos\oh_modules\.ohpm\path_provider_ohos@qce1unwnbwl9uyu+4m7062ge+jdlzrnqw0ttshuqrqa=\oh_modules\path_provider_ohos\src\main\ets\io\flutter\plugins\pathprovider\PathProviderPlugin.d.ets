import common from '@ohos.app.ability.common';
import AbilityAware from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/ability/AbilityAware';
import { AbilityPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/ability/AbilityPluginBinding';
import { FlutterPlugin, FlutterPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import { PathProviderApi, StorageDirectory } from './Messages';
export default class PathProviderPlugin extends Path<PERSON><PERSON>iderApi implements FlutterPlugin, AbilityAware {
    private pluginBinding;
    private context;
    constructor(context?: common.Context);
    getUniqueClassName(): string;
    onAttachedToEngine(binding: FlutterPluginBinding): void;
    onDetachedFromEngine(binding: FlutterPluginBinding): void;
    onAttachedToAbility(binding: AbilityPluginBinding): void;
    onDetachedFromAbility(): void;
    static registerWith(): void;
    setup(messenger: BinaryMessenger, context: common.Context): void;
    getTemporaryPath(): string;
    getApplicationSupportPath(): string;
    getApplicationDocumentsPath(): string;
    getApplicationCachePath(): string;
    getExternalStoragePath(): string;
    getExternalCachePaths(): Array<string>;
    getExternalStoragePaths(directory: StorageDirectory): Array<string>;
    private getPathProviderTemporaryDirectory;
    private getApplicationSupportDirectory;
    private getPathProviderApplicationDocumentsDirectory;
    private getPathProviderStorageDirectory;
    private getPathProviderExternalCacheDirectories;
    private getStorageDirectoryString;
    private getPathProviderExternalStorageDirectories;
}
