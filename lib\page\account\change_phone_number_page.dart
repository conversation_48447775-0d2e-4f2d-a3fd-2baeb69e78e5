import 'dart:async';
import 'package:flutter/services.dart';
import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/api/user_api.dart';
import 'package:wuling_flutter_app/page/base_page/base_page.dart';
import 'package:wuling_flutter_app/utils/http/api_exception.dart';
import 'package:wuling_flutter_app/utils/str_util.dart';
import 'package:wuling_flutter_app/utils/manager/loading_manager.dart';
import 'package:wuling_flutter_app/utils/manager/dialog_manager.dart';
import 'package:wuling_flutter_app/models/global_data.dart';

import '../../utils/manager/log_manager.dart';

class ChangePhoneNumberPage extends BasePage {
  ChangePhoneNumberPage({
    Key? key,
    bool hideAppBar = false,
    bool isWithinSafeArea = true,
    String appBarTitle = '更换手机号',
    Color appBarColor = Colors.transparent,
    Color pageBackgroundColor = Colors.white,
  }) : super(
            key: key,
            hideAppBar: hideAppBar,
            isWithinSafeArea: isWithinSafeArea,
            appBarTitle: appBarTitle,
            appBarColor: appBarColor,
            pageBackgroundColor: pageBackgroundColor,
            enablePrivacyMode: true);

  @override
  _ChangePhoneNumberPageState createState() => _ChangePhoneNumberPageState();
}

class _ChangePhoneNumberPageState extends BasePageState<ChangePhoneNumberPage> {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _codeController = TextEditingController();
  final FocusNode _phoneFocusNode = FocusNode();
  final FocusNode _codeFocusNode = FocusNode();
  bool _isCountingDown = false;
  int _countDown = 60;
  Timer? _timer;
  bool _isTextVisible = true;

  @override
  void pageInitState() {
    super.pageInitState();
  }

  void _startCountDown() {
    setState(() {
      _isCountingDown = true;
      _countDown = 60;
    });
    _timer = Timer.periodic(Duration(milliseconds: 1000), (timer) {
      setState(() {
        if (_countDown > 0) {
          _countDown--;
          _isTextVisible = !_isTextVisible;
        } else {
          _isCountingDown = false;
          _isTextVisible = true;
          _timer?.cancel();
        }
      });
    });
  }

  bool _isValidPhoneNumber(String phone) {
    return StrUtil.isPhoneNumber(phone);
  }

  void _getChangeMobileOnLoginSmsCode(String phoneNumber) async {
    try {
      LoadingManager.show(status: '验证码请求中...');
      bool isSucceed = await userAPI.getChangeMobileOnLoginSmsCode(phoneNumber);
      if (isSucceed) {
        LoadingManager.showSuccess('验证码已发送');
        _startCountDown();
      } else {
        LoadingManager.showError('获取验证码失败 请稍后重试');
      }
    } catch (e) {
      LoadingManager.showError('获取验证码失败 请稍后重试');
    }
  }

  void _replacePhoneNumber() async {
    String phoneNumber = _phoneController.text;
    if (!StrUtil.isPhoneNumber(phoneNumber)) {
      LoadingManager.showToast('手机号格式错误');
      return;
    }
    String code = _codeController.text;
    if (code.isEmpty) {
      LoadingManager.showToast('请输入验证码');
      return;
    }
    LoadingManager.show(status: '手机号修改中...');
    try {
      bool isSucceed = await userAPI.changeMobileOnLogin(phoneNumber, code);
      LogManager().debug("是否成功: $isSucceed");
      if (isSucceed) {
        LoadingManager.showSuccess('修改成功');
        if (GlobalData().isLogin) {
          await GlobalData().clearUser();
        }
        Future.delayed(const Duration(seconds: 1), () {
          Navigator.pop(context);
        });
      } else {
        LoadingManager.showError('修改失败失败 请稍后重试');
      }
    } catch (e) {
      if (e is APIException) {
        LoadingManager.dismiss();
        if (e.errorCode == '1000009') {
          DialogManager()
              .showSingleButtonDialog('提示', e.message ?? '', '确认', () {});
        }
      }
    }
  }

  @override
  Widget buildPageContent(BuildContext context) {
    const double widgetHeight = 60;
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          SizedBox(height: 20),
          Padding(
            padding: EdgeInsets.only(left: 20),
            child: Divider(height: 1, thickness: 1, color: Colors.grey[300]),
          ),
          Container(
            height: widgetHeight,
            padding: EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: TextField(
                    controller: _phoneController,
                    focusNode: _phoneFocusNode,
                    decoration: InputDecoration(
                      hintText: '请输入手机号码',
                      hintStyle: TextStyle(color: Color(0xFFC7C7CD)),
                      border: UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.transparent),
                      ),
                      enabledBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.transparent),
                      ),
                      focusedBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.transparent),
                      ),
                      contentPadding: EdgeInsets.symmetric(vertical: 12),
                    ),
                    keyboardType: TextInputType.phone,
                    cursorColor: Colors.black,
                  ),
                ),
                SizedBox(width: 10),
                SizedBox(
                  width: 100,
                  height: 35,
                  child: ElevatedButton(
                    onPressed: _isCountingDown
                        ? null
                        : () {
                            if (_isValidPhoneNumber(_phoneController.text)) {
                              _getChangeMobileOnLoginSmsCode(
                                  _phoneController.text);
                            } else {
                              LoadingManager.showToast('请输入有效的手机号');
                            }
                          },
                    child: _isCountingDown
                        ? AnimatedOpacity(
                            opacity: _isTextVisible ? 1.0 : 0.2,
                            duration: Duration(milliseconds: 500),
                            child: Text('${_countDown}s'),
                          )
                        : Text('获取验证码'),
                    style: ButtonStyle(
                      backgroundColor: MaterialStateProperty.all(Colors.white),
                      foregroundColor:
                          MaterialStateProperty.all(Colors.black54),
                      side: MaterialStateProperty.all(
                          BorderSide(color: Colors.grey[300]!)),
                      shape: MaterialStateProperty.all(RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      )),
                      padding: MaterialStateProperty.all(EdgeInsets.zero),
                      elevation: MaterialStateProperty.all(0), // 移除阴影
                    ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 20),
            child: Divider(height: 1, thickness: 1, color: Colors.grey[300]),
          ),
          Container(
            height: widgetHeight,
            padding: EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: TextField(
                    controller: _codeController,
                    focusNode: _codeFocusNode,
                    decoration: InputDecoration(
                      hintText: '请输入验证码',
                      hintStyle: TextStyle(color: Color(0xFFC7C7CD)),
                      border: UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.transparent),
                      ),
                      enabledBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.transparent),
                      ),
                      focusedBorder: UnderlineInputBorder(
                        borderSide: BorderSide(color: Colors.transparent),
                      ),
                      contentPadding: EdgeInsets.symmetric(vertical: 12),
                    ),
                    keyboardType: TextInputType.number,
                    cursorColor: Colors.black,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 20),
            child: Divider(height: 1, thickness: 1, color: Colors.grey[300]),
          ),
          SizedBox(height: 40),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 60),
            child: SizedBox(
              height: 40,
              child: ElevatedButton(
                child: Text(
                  '提交',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.w500),
                ),
                style: ButtonStyle(
                  backgroundColor: MaterialStateProperty.all(Color(0xFF6E7FFF)),
                  foregroundColor: MaterialStateProperty.all(Colors.white),
                  shape: MaterialStateProperty.all(
                    RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                  padding: MaterialStateProperty.all(EdgeInsets.zero),
                  minimumSize:
                      MaterialStateProperty.all(Size(double.infinity, 40)),
                  maximumSize:
                      MaterialStateProperty.all(Size(double.infinity, 40)),
                ),
                onPressed: () {
                  _replacePhoneNumber();
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _timer?.cancel();
    _phoneController.dispose();
    _codeController.dispose();
    _phoneFocusNode.dispose();
    _codeFocusNode.dispose();
    super.dispose();
  }
}
