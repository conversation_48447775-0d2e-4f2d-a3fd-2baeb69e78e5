import UIAbility from '@ohos.app.ability.UIAbility';
import { BinaryM<PERSON>enger, MethodCall, TextureRegistry } from '@ohos/flutter_ohos';
import { Method<PERSON>allHandler, MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import { CameraPermissions } from './CameraPermissions';
export declare class Method<PERSON>allHandlerImpl implements MethodCallHandler {
    private ability;
    private context;
    private messenger;
    private cameraPermissions;
    private textureRegistry;
    private methodChannel;
    private imageStreamChannel;
    private camera;
    private flutterTexture;
    private textureId;
    private curStatus;
    constructor(ability: UIAbility, messenger: BinaryMessenger, cameraPermissions: CameraPermissions, textureRegistry: TextureRegistry);
    onMethodCall(call: MethodCall, result: MethodResult): void;
    private registerCameraTexture;
    private unregisterTexture;
    stopListening(): void;
    private instantiateCamera;
    private handleException;
}
