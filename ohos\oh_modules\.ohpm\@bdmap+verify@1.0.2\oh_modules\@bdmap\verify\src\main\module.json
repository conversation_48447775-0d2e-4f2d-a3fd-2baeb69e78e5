{"app": {"bundleName": "com.baidu.lbsmapsdk", "debug": false, "versionCode": 1000000, "versionName": "1.0.0", "minAPIVersion": 50000012, "targetAPIVersion": 50000012, "apiReleaseType": "Release", "compileSdkVersion": "5.0.2.123", "compileSdkType": "HarmonyOS", "appEnvironments": [], "bundleType": "app", "buildMode": "release"}, "module": {"name": "verify", "type": "har", "deviceTypes": ["default", "tablet", "2in1"], "packageName": "@bdmap/verify", "installationFree": false, "virtualMachine": "ark12.0.2.0", "compileMode": "esmodule", "dependencies": []}}