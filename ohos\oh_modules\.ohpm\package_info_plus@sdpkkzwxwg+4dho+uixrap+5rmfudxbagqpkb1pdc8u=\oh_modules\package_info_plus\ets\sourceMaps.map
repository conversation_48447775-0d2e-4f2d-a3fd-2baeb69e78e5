{"package_info_plus|package_info_plus|1.0.0|build/default/generated/profile/default/ModuleInfo.js": {"version": 3, "file": "ModuleInfo.ts", "sources": ["ohos/build/default/generated/profile/default/ModuleInfo.ts"], "names": [], "mappings": "AACA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EAAE,CAAA,CAAA;AACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EAAE,CAAA,CAAA;", "entry-package-info": "package_info_plus|1.0.0"}, "package_info_plus|package_info_plus|1.0.0|index.ts": {"version": 3, "file": "index.ets", "sourceRoot": "", "sources": ["ohos/index.ets"], "names": [], "mappings": "OAcO,EAAC,iBAAiB,EAAC;AAC1B,eAAe,iBAAiB,CAAC", "entry-package-info": "package_info_plus|1.0.0"}, "package_info_plus|package_info_plus|1.0.0|src/main/ets/dev/fluttercommunity/plus/packageinfo/PackageInfoPlugin.ts": {"version": 3, "file": "PackageInfoPlugin.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/dev/fluttercommunity/plus/packageinfo/PackageInfoPlugin.ets"], "names": [], "mappings": "cAiBS,aAAa,EAAE,oBAAoB;cACnC,iBAAiB,EAAE,YAAY;OACjC,aAAa;YACb,UAAU;OACV,GAAG;OAIH,aAAa;AACpB,IAAI,WAAW,GAAI,aAAa,CAAC,UAAU,CAAC,gCAAgC,GAAG,aAAa,CAAC,UAAU,CAAC,mCAAmC,CAAA;AAE3I,MAAM,GAAG,EAAC,MAAM,GAAE,mBAAmB,CAAA;AAErC,MAAM,YAAY,GAAG,wCAAwC,CAAC;AAC9D,MAAM,OAAO,iBAAkB,YAAW,aAAa,EAAC,iBAAiB;IAGvE,kBAAkB,IAAI,MAAM;QAC1B,OAAO,mBAAmB,CAAC;IAC7B,CAAC;IAGD,OAAO,CAAC,aAAa,EAAE,aAAa,GAAG,IAAI,GAAG,IAAI,CAAC;IAEnD,OAAO,CAAC,kBAAkB,EAAE,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC;IAElD,kBAAkB,CAAC,OAAO,EAAE,oBAAoB,GAAG,IAAI;QACrD,GAAG,CAAC,CAAC,CAAC,GAAG,EAAC,uCAAuC,CAAC,CAAA;QAClD,IAAI,CAAC,kBAAkB,GAAE,OAAO,CAAC,qBAAqB,EAAE,CAAC;QACzD,IAAI,CAAC,aAAa,GAAG,IAAI,aAAa,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,YAAY,CAAC,CAAC;QACnF,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;IAEhD,CAAC;IAED,oBAAoB,CAAC,OAAO,EAAE,oBAAoB,GAAG,IAAI;QACvD,GAAG,CAAC,CAAC,CAAC,GAAG,EAAC,yCAAyC,CAAC,CAAA;QACpD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC/B,IAAI,CAAC,aAAa,EAAE,oBAAoB,CAAC,IAAI,CAAC,CAAC;QAC/C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;IAC5B,CAAC;IAED,YAAY,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,IAAI;QACxD,GAAG,CAAC,CAAC,CAAC,GAAG,EAAC,kCAAkC,CAAC,CAAA;QAC7C,IAAI;YACK,IAAI,IAAI,CAAC,MAAM,IAAI,QAAQ,EAAE;gBAE3B,MAAM,YAAY,GAAG,aAAa,CAAC,wBAAwB,CAAC,WAAW,CAAC,CAAC;gBACzE,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC;gBAErC,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC,MAAM,EAAC,MAAM,GAAG,CAAC;gBACzC,MAAM,cAAc,GAAG,YAAY,CAAC,aAAa,CAAC,WAAW,CAAC;gBAC9D,MAAM,OAAO,GAAG,IAAI,CAAC,kBAAkB,EAAE,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;gBAC9F,OAAO,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;gBAChC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAC,0CAA0C,GAAE,OAAO,CAAC,CAAC;gBAC/D,OAAO,CAAC,GAAG,CAAC,aAAa,EAAC,YAAY,CAAC,IAAI,CAAC,CAAC;gBAC7C,GAAG,CAAC,CAAC,CAAC,GAAG,EAAC,8CAA8C,GAAC,YAAY,CAAC,IAAI,CAAC,CAAA;gBAC3E,OAAO,CAAC,GAAG,CAAC,SAAS,EAAC,YAAY,CAAC,WAAW,CAAC,CAAC;gBAChD,GAAG,CAAC,CAAC,CAAC,GAAG,EAAC,0CAA0C,GAAC,YAAY,CAAC,WAAW,CAAC,CAAA;gBAC9E,OAAO,CAAC,GAAG,CAAC,aAAa,EAAC,YAAY,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;gBAC/D,GAAG,CAAC,CAAC,CAAC,GAAG,EAAC,8CAA8C,GAAC,YAAY,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAA;gBAC7F,IAAI,cAAc,IAAI,IAAI,EAAC;oBACzB,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAC,cAAc,CAAC,CAAC;oBAC7C,GAAG,CAAC,CAAC,CAAC,GAAG,EAAC,iDAAiD,GAAC,cAAc,CAAC,CAAA;iBAC5E;gBACF,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAC,EAAE,CAAC,CAAC;gBAEhC,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;aAC1B;iBAAM;gBACJ,MAAM,CAAC,cAAc,EAAE,CAAA;aACxB;SACT;QAAC,OAAM,GAAG,EAAC;YACV,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAA;SAClD;IACH,CAAC;CAEF", "entry-package-info": "package_info_plus|1.0.0"}}