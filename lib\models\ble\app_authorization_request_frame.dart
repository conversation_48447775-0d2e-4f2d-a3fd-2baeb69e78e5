import 'dart:convert';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:encrypt/encrypt.dart';
import 'package:wuling_flutter_app/utils/str_util.dart';

import '../../utils/manager/log_manager.dart';

class AppAuthorizationRequestFrame {
  String? msgResource;
  late String serviceId;
  late String subFunction;
  late String randomData2;
  late String randomData1;
  late String bleKey;
  late String payloadLength;
  late String dataCheck;
  late String finalDataStr;
  late String encryptedDataStr;
  late Uint8List encryptedData;

  // 构造方法，用于创建鉴权响应报文
  AppAuthorizationRequestFrame.withRandomData({
    required String randomData2,
    required String randomData1,
    required String bleKey,
    required String key,
  }) {
    _createAuthorizationResponseFrame(randomData2, randomData1, bleKey, key);
  }

  // 构造方法，用于创建KeyID报文
  AppAuthorizationRequestFrame.withCurrentTime({
    required String currentTime,
    required String bleKey,
  }) {
    _createKeyIDFrame(currentTime, bleKey);
  }

  // 辅助方法：在字符串前补零至指定长度
  String _addZero(String str, int length) {
    return str.padLeft(length, '0');
  }

  // 创建鉴权响应报文
  void _createAuthorizationResponseFrame(
      String randomData2, String randomData1, String bleKey, String key) {
    StringBuffer dataStr = StringBuffer();
    LogManager().debug("[BleKey]---App创建鉴权响应报文---");

    serviceId = "38C7";
    LogManager().debug("[BleKey]---服务类型 = 0x$serviceId---");
    dataStr.write(serviceId);

    subFunction = "0002";
    LogManager().debug("[BleKey]---功能类型 = 0x$subFunction---");
    dataStr.write(subFunction);

    this.randomData2 = _addZero(randomData2, 8);
    LogManager().debug("[BleKey]---随机数2 = 0x${this.randomData2}---");
    dataStr.write(this.randomData2);

    this.randomData1 = _addZero(randomData1, 8);
    LogManager().debug("[BleKey]---随机数1 = 0x${this.randomData1}---");
    dataStr.write(this.randomData1);

    this.bleKey = _addZero(bleKey, 8);
    LogManager().debug("[BleKey]---bleKey = 0x${this.bleKey}---");
    dataStr.write(this.bleKey);

    payloadLength = "06";
    LogManager().debug("[BleKey]---payload长度 = 0x$payloadLength---");
    dataStr.write(payloadLength);

    LogManager().debug("[BleKey]---源数据帧为：${dataStr.toString()}---");

    // 补充6个"00"
    for (int i = 0; i < 6; i++) {
      dataStr.write("00");
    }

    // 计算CRC校验码
    Uint8List data = StrUtil.hexStringToBytes(dataStr.toString());
    int crc = StrUtil.crc16CcittFalse(data);
    String crcCode = crc.toRadixString(16).toUpperCase();
    dataCheck = _addZero(crcCode, 4);
    LogManager().debug("[BleKey]---CRC校验码：$dataCheck---");
    dataStr.write(dataCheck);

    // 补充7个"00"
    for (int i = 0; i < 7; i++) {
      dataStr.write("00");
    }

    finalDataStr = dataStr.toString();
    LogManager().debug("[BleKey]---完整数据帧为：$finalDataStr---");

    // 数据加密
    encryptedDataStr = StrUtil.aes128Encrypt(finalDataStr, key);
    LogManager().debug("[BleKey]---加密后数据帧为：$encryptedDataStr---");

    encryptedData = StrUtil.hexStringToBytes(encryptedDataStr);
    LogManager().debug("[BleKey]---实际发送的数据：$encryptedData---");
  }

  // 创建KeyID报文
  void _createKeyIDFrame(String currentTime, String bleKey) {
    StringBuffer dataStr = StringBuffer();
    LogManager().debug("[BleKey]---App创建KeyID报文---");

    serviceId = "38C7";
    LogManager().debug("[BleKey]---服务类型 = 0x$serviceId---");
    dataStr.write(serviceId);

    subFunction = "0001";
    LogManager().debug("[BleKey]---功能类型 = 0x$subFunction---");
    dataStr.write(subFunction);

    randomData2 = "00000000";
    dataStr.write(randomData2);

    randomData1 = _addZero(currentTime, 8);
    LogManager().debug("[BleKey]---当前时间 = 0x$randomData1---");
    dataStr.write(randomData1);

    this.bleKey = _addZero(bleKey, 8);
    LogManager().debug("[BleKey]---bleKey = 0x${this.bleKey}---");
    dataStr.write(this.bleKey);

    payloadLength = "06";
    LogManager().debug("[BleKey]---payload长度 = 0x$payloadLength---");
    dataStr.write(payloadLength);

    LogManager().debug("[BleKey]---源数据帧为：${dataStr.toString()}---");

    // 补充6个"00"
    for (int i = 0; i < 6; i++) {
      dataStr.write("00");
    }

    // 计算CRC校验码
    Uint8List data = StrUtil.hexStringToBytes(dataStr.toString());
    int crc =  StrUtil.crc16CcittFalse(data);
    String crcCode = crc.toRadixString(16).toUpperCase();
    dataCheck = _addZero(crcCode, 4);
    LogManager().debug("[BleKey]---CRC校验码：$dataCheck---");
    dataStr.write(dataCheck);

    // 补充7个"00"
    for (int i = 0; i < 7; i++) {
      dataStr.write("00");
    }

    finalDataStr = dataStr.toString();
    LogManager().debug("[BleKey]---完整数据帧为：$finalDataStr---");

    // 不需要加密，直接转换为字节数组
    encryptedData = StrUtil.hexStringToBytes(finalDataStr);
    LogManager().debug("[BleKey]---实际发送的数据：$encryptedData---");
  }
}