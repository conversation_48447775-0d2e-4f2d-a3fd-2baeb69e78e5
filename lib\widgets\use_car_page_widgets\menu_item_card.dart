import '../../../models/car/car_control_item_model.dart';
import '../../../models/car/car_service_model.dart';

class MenuItemCard {
  final String icon;
  final String label;
  final String? info;
  final CarControlItemModel? controlItem; // 添加控制项数据
  final CarServiceModel? serviceModel; // 添加服务项数据

  MenuItemCard({
    required this.icon,
    required this.label,
    this.info,
    this.controlItem,
    this.serviceModel,
  });
}
