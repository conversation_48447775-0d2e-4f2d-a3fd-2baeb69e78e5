import { LatLng } from '@bdmap/base';
import { PoiInfo, SearchResult } from "../../e/h/f1";
/**
 * 反 Geo Code 结果
 */
export interface ReverseGeoCodeResult extends SearchResult {
    /**
     * 商圈名称
     * 如 "人民大学,中关村,苏州街"，一个坐标检索最多返回3个
     */
    businessCircle?: string;
    /**
     * 地址名称
     */
    address?: string;
    /**
     * 层次化地址信息
     */
    addressDetail?: AddressComponent | null;
    /**
     * 地址坐标
     */
    location?: LatLng | null;
    /**
     * 百度定义的城市id
     */
    cityCode?: number;
    /**
     * 地址周边Poi信息，只有在type为MK_REVERSEGEOCODE时才有效
     */
    poiList?: PoiInfo[];
    /**
     * 当前位置结合POI的语义化结果描述
     */
    sematicDescription?: string;
    /**
     * 请求中的坐标与POI对应的区域面（AOI）的归属关系信息
     * 以数组形式返回
     */
    poiRegionsInfoList?: PoiRegionsInfo[];
    /**
     * 召回坐标周围最近的3条道路信息
     */
    roadInfoList?: RoadInfo[];
    /**
     * 行政区域编码
     */
    adcode?: number;
}
/**
 * 此类表示地址解析结果的层次化地址信息。
 */
export interface AddressComponent {
    /**
     * 门牌号码
     */
    streetNumber?: string;
    /**
     * 街道名称（行政区划中的街道层级）
     */
    street?: string;
    /**
     * 乡镇名称
     */
    town?: string;
    /**
     * 区县名称
     */
    district?: string;
    /**
     * 城市名称
     */
    city?: string;
    /**
     * 省份名称
     */
    province?: string;
    /**
     * 国家名称
     */
    countryName?: string;
    /**
     * 国家号码
     */
    countryCode?: number;
    /**
     * 行政区域编码
     * http://mapopen-pub-webserviceapi.bj.bcebos.com/geocoding/%E5%8C%BA%E5%8E%BF%E7%BA%A7%E8%A1%8C%E6%94%BF%E5%8C%BA%E5%88%92%E6%B8%85%E5%8D%95V35.xlsx
     */
    adcode?: number;
    /**
     * 相对当前坐标点的方向，当有门牌号的时候返回数据
     */
    direction?: string;
    /**
     * 相对当前坐标点的距离，当有门牌号的时候返回数据
     */
    distance?: string;
    /**
     * 国家英文缩写（三位）
     */
    countryCodeIso?: string;
    /**
     * 国家英文缩写（两位）
     */
    countryCodeIso2?: string;
    /**
     * 乡镇id
     */
    townCode?: string;
    /**
     * 城市所在级别（仅国外有参考意义。国外行政区划与中国有差异，
     * 城市对应的层级不一定为『city』。country、province、city、district、town分别对应0-4级，
     * 若city_level=3，则district层级为该国家的city层级）
     */
    cityLevel?: number;
}
/**
 * Poi归属面信息封装
 */
export interface PoiRegionsInfo {
    /**
     * 请求中的坐标与所归属区域面的相对位置关系
     */
    directionDesc?: string;
    /**
     * 归属区域面名称
     */
    regionName?: string;
    /**
     * 归属区域面类型
     */
    regionTag?: string;
}
/**
 * 周边道路信息封装
 */
export interface RoadInfo {
    /**
     * 周边道路名称
     */
    name?: string;
    /**
     * 传入的坐标点距离道路的大概距离
     * 单位：米
     */
    distance?: string;
}
