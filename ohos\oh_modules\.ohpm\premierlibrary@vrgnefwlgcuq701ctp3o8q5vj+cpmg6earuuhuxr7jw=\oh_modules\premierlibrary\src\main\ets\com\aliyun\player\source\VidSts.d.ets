import { VidSourceBase } from './VidSourceBase';
export declare class VidSts extends VidSourceBase {
    private mVid;
    private mAccessKeyId;
    private mAccessKeySecret;
    private mSecurityToken;
    private mRegion;
    constructor();
    protected nativeGetVid: Function;
    protected nativeSetVid: Function;
    protected nativeGetAccessKeyId: Function;
    protected nativeSetAccessKeyId: Function;
    protected nativeGetAccessKeySecret: Function;
    protected nativeSetAccessKeySecret: Function;
    protected nativeGetSecurityToken: Function;
    protected nativeSetSecurityToken: Function;
    protected nativeGetRegion: Function;
    protected nativeSetRegion: Function;
    /**
     * 设置清晰度相关信息
     *
     * @param quality      期望播放的清晰度
     * @param forceQuality 是否强制使用此清晰度。如果强制，则在没有对应清晰度的情况下播放不了。
     */
    /****
     * Definition settings
     *
     * @param quality      Specify a definition for playback.
     * @param forceQuality Indicate whether to force the player to play the media with the specified definition. However, if the media does not support the specified definition, then it cannot be played.
     */
    setQuality(b40: string, c40: boolean): void;
    /**
     * 获取vid
     *
     * @return vid
     */
    /****
     * Query the VID.
     *
     * @return The VID.
     */
    getVid(): string;
    /**
     * 设置vid
     *
     * @param mVid vid。
     */
    /****
     * Set the VID.
     *
     * @param mVid The VID.
     */
    setVid(a40: string): void;
    /**
     * 获取安全token
     *
     * @return 安全token
     */
    /****
     * Query the token.
     *
     * @return The token.
     */
    getSecurityToken(): string;
    /**
     * 设置安全token
     *
     * @param mSecurityToken 安全token
     */
    /****
     * Set a token.
     *
     * @param mSecurityToken The specified token.
     */
    setSecurityToken(z39: string): void;
    /**
     * 获取鉴权id
     *
     * @return 鉴权id
     */
    /****
     * Query the AccessKey ID for authentication.
     *
     * @return The AccessKey ID for authentication.
     */
    getAccessKeyId(): string;
    /**
     * 设置鉴权id
     *
     * @param mAccessKeyId 鉴权id
     */
    /****
     * Set the AccessKey ID for authentication.
     *
     * @param mAccessKeyId The AccessKey ID for authentication
     */
    setAccessKeyId(y39: string): void;
    /**
     * 获取鉴权秘钥
     *
     * @return 鉴权秘钥
     */
    /****
     * Query the AccessKey Secret for authentication.
     *
     * @return The AccessKey Secret for authentication.
     */
    getAccessKeySecret(): string;
    /**
     * 设置鉴权秘钥
     *
     * @param mAccessKeySecret 鉴权秘钥
     */
    /****
     * Set the AccessKey Secret for authentication.
     *
     * @param mAccessKeySecret The AccessKey Secret for authentication.
     */
    setAccessKeySecret(x39: string): void;
    /**
     * 获取地域
     *
     * @return 地域
     */
    /****
     * Query region information.
     *
     * @return The region information.
     */
    getRegion(): string;
    /**
     * 设置地域
     *
     * @param mRegion 地域
     */
    /****
     * Specify regions.
     *
     * @param mRegion The specified regions.
     */
    setRegion(w39: string): void;
}
