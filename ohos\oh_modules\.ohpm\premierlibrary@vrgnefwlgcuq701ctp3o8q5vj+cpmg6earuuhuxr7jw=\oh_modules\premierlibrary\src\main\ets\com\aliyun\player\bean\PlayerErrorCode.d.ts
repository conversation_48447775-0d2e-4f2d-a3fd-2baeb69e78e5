export declare enum PlayerErrorCode {
    ERROR_SERVER_NO_RESPONSE = 536936449,
    ERROR_SERVER_WRONG_JSON = 536936450,
    ERROR_NO_MATCH_QUALITY = 536936451,
    ERROR_PLAYAUTH_WRONG = 536936452,
    ERROR_REQUEST_FAIL = 536936453,
    ERROR_NO_PLAY_INFO = 536936454,
    ERROR_SERVER_POP_UNKNOWN = 536936704,
    ERROR_SERVER_POP_MISSING_PARAMETER = 536936705,
    ERROR_SERVER_POP_INVALID_PARAMETER = 536936706,
    ERROR_SERVER_POP_OPERATION_DENIED = 536936707,
    ERROR_SERVER_POP_OPERATION_SUSPENED = 536936708,
    ERROR_SERVER_POP_FORBIDDEN = 536936709,
    ERROR_SERVER_POP_INTERNAL_ERROR = 536936710,
    ERROR_SERVER_POP_SERVICE_UNAVALIABLE = 536936711,
    ERROR_SERVER_POP_SIGNATUREANONCE_USED = 536936712,
    ERROR_SERVER_POP_SECURITYTOKEN_MAILFORMED = 536936713,
    ERROR_SERVER_POP_SECURITYTOKEN_MISMATCH_ACCESSKEY = 536936714,
    ERROR_SERVER_POP_SIGNATURE_NOT_MATCH = 536936715,
    ERROR_SERVER_POP_ACCESSKEYID_NOT_FOUND = 536936716,
    ERROR_SERVER_POP_TOKEN_EXPIRED = 536936717,
    ERROR_SERVER_VOD_UNKNOWN = 536936960,
    ERROR_SERVER_VOD_FORBIDDEN_ILLEGALSTATUS = 536936961,
    ERROR_SERVER_VOD_INVALIDVIDEO_NOTFOUND = 536936962,
    ERROR_SERVER_VOD_INVALIDVIDEO_NOSTREAM = 536936963,
    ERROR_SERVER_VOD_FORBIDDEN_ALIYUNVODENCRYPTION = 536936964,
    ERROR_SERVER_VOD_INVALIDAUTH_MEDIAID = 536936965,
    ERROR_SERVER_VOD_INVALIDAUTHINFO_EXPIRETIME = 536936966,
    ERROR_SERVER_MPS_UNKNOWN = 536937216,
    ERROR_SERVER_MPS_INVALID_MEDIAID = 536937217,
    ERROR_SERVER_MPS_INVALID_AUTHTIMEOUT = 536937218,
    ERROR_SERVER_MPS_INVALID_FORMATS = 536937219,
    ERROR_SERVER_MPS_INVALID_AUTHINFO = 536937220,
    ERROR_SERVER_MPS_SIGNATURE_CHECK_FAILED = 536937221,
    ERROR_SERVER_MPS_MEDIAID_NOT_EXIST = 536937222,
    ERROR_SERVER_MPS_MEDIA_RESOURCE_NOT_EXIST = 536937223,
    ERROR_SERVER_MPS_MEDIA_NOT_PUBLISHED = 536937224,
    ERROR_SERVER_MPS_MEDIA_NOT_ENCRYPTED = 536937225,
    ERROR_SERVER_MPS_INVALID_CIPHERTEXTBLOB = 536937226,
    ERROR_SERVER_MPS_CIPHERBLOB_NOT_EXIST = 536937227,
    ERROR_SERVER_MPS_INTERNAL_ERROR = 536937228,
    ERROR_SERVER_MPS_INVALID_IDENTITY_NOT_ORDER_VIDEO_SERVICE = 536937229,
    ERROR_SERVER_MPS_UPDATE_CDN_DOMAIN_CONFIGS_FAIL = 536937230,
    ERROR_SERVER_MPS_AUTH_KEY_EXIST = 536937231,
    ERROR_SERVER_MPS_AUTH_KEY_NOT_EXIST = 536937232,
    ERROR_SERVER_MPS_INVALID_PARAMETER_OUT_OF_BOUND = 536937233,
    ERROR_SERVER_MPS_INVALID_PARAMETER = 536937234,
    ERROR_SERVER_MPS_INVALID_PARAMETER_NULL_VALUE = 536937235,
    ERROR_SERVER_MPS_INVALID_PARAMETER_EMPTY_VALUE = 536937236,
    ERROR_SERVER_MPS_MEDIA_RESOURCE_NOT_MATCH = 536937237,
    ERROR_SERVER_MPS_MEDIA_NOT_FOUND_CIPHERTEXT = 536937238,
    ERROR_SERVER_MPS_INVALID_PARAMETER_RAND = 536937239,
    ERROR_SERVER_MPS_REDIS_POOL_IS_EMPTY = 536937240,
    ERROR_SERVER_MPS_SIGNATURE_CHECK_MEDIA_FAILED = 536937241,
    ERROR_SERVER_MPS_SIGNATURE_CHECK_EXPIREDTIME_FAILED = 536937242,
    ERROR_SERVER_MPS_INVALID_SESSION_TIME = 536937243,
    ERROR_SERVER_MPS_INVALID_END_USER_ID = 536937244,
    ERROR_SERVER_MPS_INVALID_URL = 536937245,
    ERROR_SERVER_MPS_HTTP_REQUEST_FAILED = 536937246,
    ERROR_SERVER_MPS_XML_FORMAT_ERROR = 536937247,
    ERROR_SERVER_MPS_SESSION_NOT_EXIST = 536937248,
    ERROR_SERVER_MPS_REGION_NOT_SUPPORTED_API = 536937249,
    ERROR_SERVER_MPS_DRM_NOT_ACTIVATED = 536937250,
    ERROR_SERVER_MPS_DRM_AUTH_ERROR = 536937251,
    ERROR_SERVER_MPS_CDN_CONFIG_NOT_EXIST = 536937252,
    ERROR_SERVER_LIVESHIFT_UNKNOWN = 536937472,
    ERROR_SERVER_LIVESHIFT_REQUEST_ERROR = 536937473,
    ERROR_SERVER_LIVESHIFT_DATA_PARSER_ERROR = 536937474,
    ERROR_KEYMANAGER_UNKNOWN = 536940544,
    ERROR_TBDRM_UNKNOWN = 536944640,
    ERROR_TBDRM_DEMUXER_UNIMPLEMENTED = 536944641,
    /**
     * @deprecated
     * */
    ERROR_ARTP_UNKNOWN = 536948736,
    ERROR_ARTP_DEMUXER_UNIMPLEMENTED = 536948737,
    ERROR_ARTP_LOAD_FAILED = 536948738,
    ERROR_ARTP_STREAM_ILLEGAL = 536948739,
    ERROR_ARTP_STREAM_FORBIDDEN = 536948740,
    ERROR_ARTP_STREAM_NOT_FOUND = 536948741,
    ERROR_ARTP_STREAM_STOPPED = 536948742,
    ERROR_ARTP_PLAY_TIMEOUT = 536948743,
    ERROR_ARTP_ARTP_MEDIA_INFO_TIMEOUT = 536948744,
    ERROR_ARTP_PACKET_RECV_TIMEOUT = 536948745,
    ERROR_ARTP_MEDIA_PROBE_FAILED = 536948746,
    ERROR_UNKNOWN_ERROR = 537001983,
    ERROR_DEMUXER_START = 537067520,
    ERROR_DEMUXER_OPENURL = 537067521,
    ERROR_DEMUXER_NO_VALID_STREAM = 537067522,
    ERROR_DEMUXER_OPENSTREAM = 537067523,
    ERROR_LOADING_TIMEOUT = 537067524,
    ERROR_DATASOURCE_EMPTYURL = 537067525,
    ERROR_DECODE_BASE = 537133056,
    ERROR_DECODE_VIDEO = 537133057,
    ERROR_DECODE_AUDIO = 537133058,
    ERROR_NETWORK_UNKNOWN = 537198592,
    ERROR_NETWORK_UNSUPPORTED = 537198593,
    ERROR_NETWORK_RESOLVE = 537198594,
    ERROR_NETWORK_CONNECT_TIMEOUT = 537198595,
    ERROR_NETWORK_COULD_NOT_CONNECT = 537198596,
    ERROR_NETWORK_HTTP_403 = 537198597,
    ERROR_NETWORK_HTTP_404 = 537198598,
    ERROR_NETWORK_HTTP_4XX = 537198599,
    ERROR_NETWORK_HTTP_5XX = 537198600,
    ERROR_NETWORK_HTTP_RANGE = 537198601,
    ERROR_NETWORK_HTTP_400 = 537198602,
    ERROR_NETWORK_HTTP_REDIRECT_NONMEDIA = 537198603,
    ERROR_CODEC_UNKNOWN = 537264128,
    ERROR_CODEC_VIDEO_NOT_SUPPORT = 537264129,
    ERROR_CODEC_AUDIO_NOT_SUPPORT = 537264130,
    ERROR_INERNAL_UNKNOWN = 537329664,
    ERROR_INERNAL_EXIT = 537329665,
    ERROR_GENERAL_UNKNOWN = 537395200,
    ERROR_GENERAL_EPERM = 537395201,
    ERROR_GENERAL_ENOENT = 537395202,
    ERROR_GENERAL_EIO = 537395205,
    ERROR_RENDER_UNKNOWN = 537460736,
    ERROR_RENDER_AUDIO_OPEN_DEVICE_FAILED = 537460737,
    ERROR_FORMAT_UNKNOWN = 537526272,
    ERROR_FORMAT_NOT_SUPPORT = 537526273,
    ERROR_FORMAT_URL_REQUIRE_ALIYUN_PRIVATE_TAG = 537526274,
    ERROR_FORMAT_INVALID_TBDRM_MP4_KEY = 537526275,
    ERROR_FORMAT_INVALID_TBDRM_HLS_KEY = 537526276,
    ERROR_UNKNOWN = 805306367,
    MEDIALOADER_ERROR_UNKNOWN = 1073807360,
    MEDIALOADER_ERROR_ADDED = 1073807361,
    MEDIALOADER_ERROR_NOT_ENABLE = 1073807362,
    MEDIALOADER_ERROR_NOT_SUPPORT = 1073807363,
    MEDIALOADER_ERROR_NO_TRACK = 1073807364,
    DOWNLOAD_ERROR_NOT_SELECT_ITEM = 805371904,
    DOWNLOAD_ERROR_NO_DOWNLOAD_ITEM = 805371905,
    DOWNLOAD_ERROR_STS_SOURCE_NULL = 805371906,
    DOWNLOAD_ERROR_AUTH_SOURCE_NULL = 805371907,
    DOWNLOAD_ERROR_AUTH_SOURCE_WRONG = 805371908,
    DOWNLOAD_ERROR_INVALID_ITEM = 805371909,
    DOWNLOAD_ERROR_URL_CANNOT_REACH = 805371910,
    DOWNLOAD_ERROR_NOT_SUPPORT_FORMAT = 805371911,
    DOWNLOAD_ERROR_ENCRYPT_FILE_NOT_MATCH = 805371912,
    DOWNLOAD_ERROR_DOWNLOAD_SWITCH_OFF = 805371913,
    DOWNLOAD_ERROR_NET_ERROR = 805371914,
    DOWNLOAD_ERROR_NOT_SET_SAVE_DIR = 805371915,
    DOWNLOAD_ERROR_CANNOT_CREATE_SAVE_DIR = 805371916,
    DOWNLOAD_ERROR_NO_SPACE = 805371917,
    DOWNLOAD_ERROR_WRITE_ERROR = 805371918,
    DOWNLOAD_ERROR_ENCRYPT_ERROR = 805371919,
    DOWNLOAD_ERROR_FILE_NOT_EXIST = 805371920,
    DOWNLOAD_ERROR_CLEAN_INVALID_PARAM = 805371921,
    DOWNLOAD_ERROR_CLEAN_WRONG_STATUS = 805371922,
    DOWNLOAD_ERROR_GET_AES_KEY_FAIL = 805371923,
    DOWNLOAD_ERROR_ENCRYPTION_NOT_SUPPORT = 805371924,
    LICENSE_ERROR_INVALID = **********,
    PREMIUM_LICENSE_ERROR_INVALID = **********,
    ERROR_FORMAT_PREMIUM_INVALID = **********,
    ERROR_MEDIALOADER_PREMIUM_INVALID = **********,
    LICENSE_ERROR_UNKNOWN = **********
}
export declare function findErrorCodeByValue(u20: number): PlayerErrorCode;
