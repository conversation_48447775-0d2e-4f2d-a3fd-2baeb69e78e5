import type { LatLng } from '@bdmap/base';
import { PoiDetailInfo } from "../../g1/h/a3";
/**
 * 路径规划中的出行节点信息,出行节点包括：起点，终点，途经点
 * <p>
 * 出行节点信息可以通过两种方式确定：
 * </p>
 * <p>
 * 1： 给定出行节点经纬度坐标, 添加poiID绑路更准确
 * </p>
 * <p>
 * 2： 给定出行节点地名和城市名, 添加poiID绑路更准确
 * </p>
 */
export declare class PlanNode {
    private _location;
    private _city;
    private _cityCode;
    private _cityName;
    private _name;
    private _poiId;
    constructor({ location, city, cityCode, cityName, name, poiId }: {
        location?: LatLng;
        city?: string;
        cityCode?: string;
        cityName?: string;
        name?: string;
        poiId?: string;
    });
    /**
     * 通过指定经纬度确定出行节点信息
     *
     * @param location
     *            经纬度
     * @return 出行节点对象
     */
    static withLocation(location: LatLng): PlanNode;
    /**
     * 通过指定经纬度坐标和poiId确定出行节点信息
     *
     * @param location 经纬度
     *
     * @param poiId poiId
     *
     * @return 出行节点信息
     */
    static withLocationAndPoiId(location: LatLng, poiId: string): PlanNode;
    /**
     * 通过地名和城市名确定出行节点信息
     *
     * @param placeName
     *            地点名
     * @param city
     *            城市名
     * @return 出行节点对象
     */
    static withCityNameAndPlaceName(city: string, placeName: string): PlanNode;
    /**
     * 通过地名和城市名和poiId确定出行节点信息
     *
     * @param placeName
     *            地点名
     * @param city
     *            城市名
     * @param  poiId
     *
     * @return 出行节点对象
     */
    static withCityNameAndPlaceNameAndPoiId(city: string, placeName: string, poiId: string): PlanNode;
    /**
     * 通过地名和城市编码确定出行节点信息
     *
     * @param placeName
     *            地点名
     * @param cityCode
     *            城市ID
     * @return 出行节点对象
     */
    static withCityCodeAndPlaceName(cityCode: number, placeName: string): PlanNode;
    /**
     * 通过地名和城市编码和PoiId确定出行节点信息
     *
     * @param placeName
     *            地点名
     * @param cityCode
     *            城市ID
     * @param poiId
     *
     * @return 出行节点对象
     */
    static withCityCodeAndPlaceNameAndPoiId(cityCode: number, placeName: string, poiId: string): PlanNode;
    get location(): LatLng | null;
    get city(): string | null;
    get cityCode(): string | null;
    get cityName(): string | null;
    get name(): string | null;
    get poiId(): string | null;
}
/**
 * 检索结果状态定义
 */
export declare enum ERRORNO {
    /**
     * 检索结果正常返回
     */
    NO_ERROR = 0,
    /**
     * 没有找到检索结果
     */
    RESULT_NOT_FOUND = 1,
    /**
     * 检索词有岐义
     */
    AMBIGUOUS_KEYWORD = 2,
    /**
     * 检索地址有岐义
     */
    AMBIGUOUS_ROURE_ADDR = 3,
    /**
     * 该城市不支持公交搜索
     */
    NOT_SUPPORT_BUS = 4,
    /**
     * 不支持跨城市公交
     */
    NOT_SUPPORT_BUS_2CITY = 5,
    /**
     * 起终点太近
     */
    ST_EN_TOO_NEAR = 6,
    /**
     * key有误
     */
    KEY_ERROR = 7,
    /**
     * 授权未完成
     */
    PERMISSION_UNFINISHED = 8,
    /**
     * 网络超时
     */
    NETWORK_TIME_OUT = 9,
    /**
     * 网络错误
     */
    NETWORK_ERROR = 10,
    /**
     * poi室内检索bid错误
     */
    POIINDOOR_BID_ERROR = 11,
    /**
     * poi室内检索floor错误
     */
    POIINDOOR_FLOOR_ERROR = 12,
    /**
     * poi室内检索服务错误
     */
    POIINDOOR_SERVER_ERROR = 13,
    /**
     * 室内路线规划起点、终点不在支持室内路径规划的位置，
     * 包括起终点在室内，但是该室内图不支持路线规划，对于该中场景，
     * 可以通过判断点是否在室内区分。
     */
    INDOOR_ROUTE_NO_IN_BUILDING = 14,
    /**
     * 室内路线规划起终点不在同一个室内
     */
    INDOOR_ROUTE_NO_IN_SAME_BUILDING = 15,
    /**
     * 跨城公共交通服务器内部错误
     */
    MASS_TRANSIT_SERVER_ERROR = 16,
    /**
     * 跨城公共交通错误码：参数无效
     */
    MASS_TRANSIT_OPTION_ERROR = 17,
    /**
     * 跨城公共交通没有匹配的POI
     */
    MASS_TRANSIT_NO_POI_ERROR = 18,
    /**
     * 服务器内部错误
     */
    SEARCH_SERVER_INTERNAL_ERROR = 19,
    /**
     * 参数错误
     */
    SEARCH_OPTION_ERROR = 20,
    /**
     * 请求错误
     */
    REQUEST_ERROR = 21,
    /**
     * 没有高级权限
     */
    NO_ADVANCED_PERMISSION = 22,
    /**
     * 区域编码无效
     */
    INVALID_DISTRICT_ID = 23,
    /**
     * 经纬度所在地区无数据覆盖
     */
    NO_DATA_FOR_LATLNG = 24,
    /**
     * 请求参数错误
     */
    PARAMER_ERROR = 25
}
/**
 * 返回给用户的搜索结果基类
 */
export interface SearchResult {
    /**
     * 检索结果错误码， 各错误值见{@link ERRORNO}
     */
    error?: ERRORNO;
    /**
     * 检索结果状态码，各状态值请见
     * {https://lbs.baidu.com/faq/api?title=webapi/guide/webservice-geocoding-abroad-base#%E6%9C%8D%E5%8A%A1%E7%8A%B6%E6%80%81%E7%A0%81}
     */
    status?: number;
}
/**
 * 服务端返回json字段status映射
 */
export declare enum StatusType {
    /**
     * 请求成功
     */
    SEARCH_SUCCESS = 0,
    /**
     * 服务器内部错误
     */
    SEARCH_SERVER_INTERNAL_ERROR = 1,
    /**
     * 参数错误
     */
    SEARCH_OPTION_ERROR = 2
}
/**
 * 路线打车信息
 */
export interface TaxiInfo {
    /**
     * 总价 , 单位： 元, 注：此价格为白天价格
     */
    totalPrice: number;
    /**
     * 路线打车描述信息
     */
    desc: string;
    /**
     * 总路程 ， 单位： m
     */
    distance: number;
    /**
     * 总耗时，单位： 秒
     */
    duration: number;
    /**
     * 每千米单价，单位 元 , 注：此价格为白天价格
     */
    perKMPrice: number;
    /**
     * 起步价，单位： 元, 注：此价格为白天价格
     */
    startPrice: number;
}
/**
 * poi信息类
 */
export interface PoiInfo {
    /**
     * poi名称
     */
    name?: string;
    /**
     * poi唯一标识
     * 如果为isPano为true，可用此参数调用街景组件PanoramaService类
     * 的requestPanoramaWithPoiUId方法检索街景数据
     */
    uid?: string;
    /**
     * poi地址信息
     */
    address?: string;
    /**
     * poi所在省份
     */
    province?: string;
    /**
     * poi所在城市
     */
    city?: string;
    /**
     * poi所在行政区域
     */
    area?: string;
    /**
     * poi对应的街景图id
     */
    street_id?: string;
    /**
     * poi电话信息
     */
    phoneNum?: string;
    /**
     * poi邮编
     */
    postCode?: string;
    /**
     * poi是否有详情页
     */
    detail?: number;
    /**
     * poi类型，0：普通点，1：公交站，2：公交线路，3：地铁站，4：地铁线路,
     */
    type?: POITYPE;
    /**
     * poi坐标, 当ePoiType为2或4时，pt 为空
     */
    location?: LatLng | null;
    /**
     * poi点是否有美食类详情页面
     */
    hasCaterDetails?: boolean;
    /**
     * poi点附近是否有街景，可使用uid检索全景组件的全景数据
     */
    isPano?: boolean;
    /**
     * poi分类，如："美食;中餐厅"
     */
    tag?: string;
    /**
     * 行政区划编码
     */
    adCode?: number;
    /**
     * poi扩展信息
     */
    poiDetailInfo?: PoiDetailInfo | null;
    /**
     * RGC请求结果中，周边POI和请求坐标点的方向关系，比如“内”，“西”，“南”等。
     * 判断时，取结果中的第一个POI的该参数即可，如果返回为“内”则说明经纬度坐标该POI所属的面内
     * V5.2.0版本开放
     */
    direction?: string;
    /**
     * RGC请求结果中，周边POI和请求坐标点的距离
     * distance = 0，说明经纬度位于POI所在的面内，但是也可能是POI的经纬度点
     * V5.2.0版本开放
     */
    distance?: number;
    /**
     * RGC请求结果中，poi对应的主点poi信息（如，海底捞的主点为上地华联，
     * 该字段则为上地华联的poi信息。如POI无主点，则无该字段为空）
     */
    parentPoiInfo?: ParentPoiInfo | null;
}
/**
 * Poi 类型枚举
 */
export declare enum POITYPE {
    POINT = 0,
    BUS_STATION = 1,
    BUS_LINE = 2,
    SUBWAY_STATION = 3,
    SUBWAY_LINE = 4
}
/**
 * POI子节点数据类，提供更准确的POI描述（城市检索，周边检索，Sug检索支持）
 */
export interface PoiChildrenInfo {
    /**
     * POI子点ID
     */
    uid?: string;
    /**
     * POI子点名称
     */
    name?: string;
    /**
     * POI子点简称
     */
    showName?: string;
    /**
     * POI子点类别
     */
    tag?: string;
    /**
     * POI子点坐标
     * Suggestion检索不支持该字段
     */
    location?: LatLng;
    /**
     * POI子点地址
     * 仅城市检索，周边检索支持该字段，Sug检索无此字段
     */
    address?: string;
}
/**
 * 搜索结果城市信息。
 * <p/>
 * 搜索结果城市城市信息，包含城市名和该城市搜索结果数量
 */
export interface CityInfo {
    /**
     * 城市名称
     */
    city: string;
    /**
     * 搜索结果数量
     */
    num: string;
}
/**
 * 路线数据结构的基础接口,表示一条路线，路线可能包括：路线规划中的换乘/驾车/步行路线
 * <p>
 * 此接口为路线数据结构的基础接口，一般关注其子接口即可，无需直接使用基础接口
 * </p>
 */
export interface RouteLine<T extends RouteStep> {
    starting: RouteNode | null;
    terminal: RouteNode | null;
    title: string;
    steps: T[];
    distance: number;
    duration: number;
}
/**
 * 路线中的一个路段
 */
export interface RouteStep {
    /**
     * 路段距离
     */
    distance: number;
    /**
     * 路段耗时
     */
    duration: number;
    /**
     * 路段道路名称
     */
    name: string;
    /**
     * 路段所经过的地理坐标集合
     */
    wayPoints?: LatLng[];
}
/**
 * 表示路线中的一节点，节点包括：路线起终点，公交站点等
 */
export interface RouteNode {
    title: string;
    location: LatLng | null;
    uid: string;
}
/**
 * 路线规划建议节点列表
 */
export interface SuggestAddrInfo {
    suggestStartNode: PoiInfo[];
    suggestEndNode: PoiInfo[];
    suggestWpNode: PoiInfo[][];
    suggestStartCity: CityInfo[];
    suggestEndCity: CityInfo[];
    suggestWpCity: CityInfo[][];
}
export interface ParentPoiInfo {
    /**
     * poi名称
     */
    parentPoiName?: string;
    /**
     * poi类型
     */
    parentPoiTag?: string;
    /**
     * 地址信息
     */
    parentPoiAddress?: string;
    /**
     * poi坐标
     */
    parentPoiLocation?: LatLng | null;
    /**
     * 和当前坐标点的方向
     */
    parentPoiDirection?: string;
    /**
     * 离定位坐标点距离
     */
    parentPoiDistance?: number;
    /**
     * poi唯一标识
     */
    parentPoiUid?: string;
}
