// @keepTs
// @ts-nocheck
/**
 * Created on 2024/8/12
 * <AUTHOR>
 */
import { GroupInfoModel } from '../model/GroupInfoModel';
import { GroupMemberInfoModel } from '../model/GroupMemberInfoModel';
import { UserInfoModel } from '../model/UserInfoModel';
/**
 * 用户数据变更监听
 * @version 1.0.0
 */
export interface UserDataListener {
    /**
     * 用户信息发生了更新
     * @param userInfo 用户信息
     */
    onUserInfoChanged?: (userInfo: UserInfoModel) => void;
    /**
     * 群组信息发生了更新
     * @param groupInfo 群组信息
     */
    onGroupInfoChanged?: (groupInfo: GroupInfoModel) => void;
    /**
     * 群组成员信息发生了更新
     * @param userInfo 群组成员信息
     */
    onGroupMemberInfoChanged?: (userInfo: GroupMemberInfoModel) => void;
}
