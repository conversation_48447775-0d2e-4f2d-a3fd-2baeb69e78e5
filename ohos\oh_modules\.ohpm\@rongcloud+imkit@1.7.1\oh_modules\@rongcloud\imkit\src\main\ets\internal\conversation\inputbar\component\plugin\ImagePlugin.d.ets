// @keepTs
// @ts-nocheck
/**
 * Created on 2024/09/06
 * <AUTHOR>
 */
import { ConversationIdentifier } from '@rongcloud/imlib';
import { IBoardPlugin } from '../../../../../conversation/inputbar/component/plugin/IBoardPlugin';
/**
 * 图片插件标识
 * @version 1.0.0
 */
declare const ImagePluginName: string;
/**
 * 加号扩展栏的图片插件
 * @version 1.0.0
 */
declare class ImagePlugin implements IBoardPlugin {
    private conId;
    pluginName(): string;
    obtainTitle(k146: Context): ResourceStr;
    obtainImage(j146: Context): ResourceStr;
    onClick(h146: Context, i146: ConversationIdentifier): void;
    onFilter(g146: ConversationIdentifier): boolean;
    sendMediaAction(b146: string[], c146: boolean): Promise<void>;
    private buildMediaMessage;
    private pushAlbum;
    private isGifByMimeType;
}
export { ImagePlugin, ImagePluginName };
