import { DistrictSearchOption } from "../../../d/i2/j2";
import { DistrictResult } from "../../../d/i2/k2";
import { BaseSearch } from '../../base/base';
export interface IDistrictSearch {
    searchDistrict(option: DistrictSearchOption): Promise<DistrictResult>;
}
export declare class DistrictSearchImp extends BaseSearch implements IDistrictSearch {
    private mIsFirstRequest;
    private mDistrictParser;
    searchDistrict(s9: DistrictSearchOption): Promise<DistrictResult>;
    private isNeedSecondRequest;
    private updateFirstRequestState;
    private sendSecondRequest;
}
