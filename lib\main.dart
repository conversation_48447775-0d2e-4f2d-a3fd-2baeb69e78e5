import 'dart:async';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:image_picker_ohos/image_picker_ohos.dart';
import 'package:wuling_flutter_app/constant/storage.dart';
import 'package:wuling_flutter_app/page/main_page.dart';
import 'package:wuling_flutter_app/page/privacy_page.dart';
import 'package:wuling_flutter_app/utils/error_util.dart';
import 'package:wuling_flutter_app/utils/sp_util.dart';
import 'package:wuling_flutter_app/utils/manager/dialog_manager.dart';
import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/utils/manager/loading_manager.dart';

import 'constant/constant.dart';
import 'constant/main_color.dart';
import 'global.dart';
import 'page/splash_screen_page.dart';
import 'package:image_picker_platform_interface/image_picker_platform_interface.dart';

import 'utils/manager/log_manager.dart';
void main() {
  Constant.printAllConstants();
  Global.init().then((e) {
    ErrorUtil.install();
    runZoned(
        () => runApp(const ColorFiltered(
              colorFilter:
                  ColorFilter.mode(Color(0x00000000), BlendMode.colorBurn),
              child: MyApp(),
            )), onError: (Object error, StackTrace stack) {
      LogManager().debug('${error.runtimeType}');
      // IBGlobalChannel.throwMassage(stack.toString());
      if (error.runtimeType == RangeError) {
        LogManager().debug("数组越界======");
      } else if (error.runtimeType == MissingPluginException) {
        LogManager().debug("通道错误======");
      } else {
        LogManager().debug("其他错误======");
      }
    });
  } );

  final platformImpl = ImagePickerPlatform.instance;
  if (platformImpl is ImagePickerOhos) {
    platformImpl.useOhosPhotoPicker = true;  // 启用鸿蒙相册选择器
  }
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    // 全局网络管理器已移除
  }

  @override
  void dispose() {
    // 全局网络管理器已移除
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: '五菱汽车',
      theme: ThemeData(
        primarySwatch: MainColors.white,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      debugShowCheckedModeBanner: false,
      initialRoute: _getInitialRoute(),
      navigatorKey: DialogManager.navigatorKey,
      routes: {
        '/privacy': (context) => const PrivacyPage(),
        '/splash': (context) => const SplashScreenPage(),
        '/home': (context) => const MainPage(
              centerButtonEnable: true,
            ),
      },
      builder: (context, child) {
        // 全局网络管理器已移除

        // 添加全局返回键处理
        return WillPopScope(
          onWillPop: () async {
            // 如果当前页面是隐私页面，直接退出应用
            if (ModalRoute.of(context)?.settings.name == '/privacy') {
              exit(0);
              return false;
            }
            return true; // 其他页面正常处理返回键
          },
          child: LoadingManager.init()(context, child),
        );
      },
    );
  }

  String _getInitialRoute() {
    bool approve = SpUtil().getBool(SP_USER_PRIVACY_APPROVE_KEY);
    if (approve) {
      return '/splash';
    } else {
      return '/privacy';
    }
  }
}
