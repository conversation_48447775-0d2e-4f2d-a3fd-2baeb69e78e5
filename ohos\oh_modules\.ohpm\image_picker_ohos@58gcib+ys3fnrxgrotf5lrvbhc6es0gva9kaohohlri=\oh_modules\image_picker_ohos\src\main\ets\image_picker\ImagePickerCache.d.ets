import common from '@ohos.app.ability.common';
import data_preferences from '@ohos.data.preferences';
import ArrayList from '@ohos.util.ArrayList';
import HashMap from '@ohos.util.HashMap';
import { ImageSelectionOptions } from './Messages';
export declare enum CacheType {
    IMAGE = 0,
    VIDEO = 1
}
export default class ImagePickerCache {
    static MAP_KEY_PATH_LIST: string;
    static MAP_KEY_MAX_WIDTH: string;
    static MAP_KEY_MAX_HEIGHT: string;
    static MAP_KEY_IMAGE_QUALITY: string;
    static MAP_KEY_TYPE: string;
    static MAP_KEY_ERROR: string;
    private static MAP_TYPE_VALUE_IMAGE;
    private static MAP_TYPE_VALUE_VIDEO;
    private static FLUTTER_IMAGE_PICKER_IMAGE_PATH_KEY;
    private static SHARED_PREFERENCE_ERROR_CODE_KEY;
    private static SHARED_PREFERENCE_ERROR_MESSAGE_KEY;
    private static SHARED_PREFERENCE_MAX_WIDTH_KEY;
    private static SHARED_PREFERENCE_MAX_HEIGHT_KEY;
    private static SHARED_PREFERENCE_IMAGE_QUALITY_KEY;
    private static SHARED_PREFERENCE_TYPE_KEY;
    private static SHARED_PREFERENCE_PENDING_IMAGE_URI_PATH_KEY;
    private static SHARED_PREFERENCES_NAME;
    private context;
    private preferences;
    constructor(context: common.Context);
    saveType(type: CacheType): void;
    private setType;
    private setPreferenceData;
    private getPreferenceData;
    private hasKey;
    saveDimensionWithOutputOptions(options: ImageSelectionOptions): void;
    savePendingCameraMediaUriPath(uri: string): void;
    retrievePendingCameraMediaUriPath(): Promise<string | null>;
    saveResult(path: ArrayList<string>, errorCode: string | null, errorMessage: string | null): void;
    clear(): void;
    getCacheMap(): Promise<HashMap<string, ArrayList<data_preferences.ValueType>>>;
}
