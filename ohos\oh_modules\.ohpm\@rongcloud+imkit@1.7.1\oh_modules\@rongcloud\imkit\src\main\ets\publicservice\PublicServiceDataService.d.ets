// @keepTs
// @ts-nocheck
import { PublicServiceInfo } from "@rongcloud/imlib";
import { PublicServiceDataListener } from "../internal/publicservice/PublicServiceDataListener";
import { PublicServiceDataProvider } from "../internal/publicservice/PublicServiceDataProvider";
/**
 * 公众号信息服务（私有云专用）
 * @version 1.3.2
 */
export interface PublicServiceDataService {
    /**
     * 设置公众号信息提供者，当 sdk 需要展示公众号信息时，如果 sdk 中没有对应的信息，就会调用 provider 的对应方法
     *
     * ## 注意
     *
     * 1，fetchPublicServiceInfo返回的PublicServiceInfo，必须设置targetId，否则SDK会认为无效数据而不会存储与返回。
     *
     * 2，假设App未获取到数据，返回 `new PublicServiceInfo()` 即可。
     *
     * ## 说明
     *
     * ```
     * import { PublicServiceDataProvider } from '@rongcloud/imkit/src/main/ets/internal/publicservice/PublicServiceDataProvider';
     * import { InnerRongIMImpl } from '@rongcloud/imkit/src/main/ets/internal/InnerRongIMImpl';
     *
     * let provider: PublicServiceDataProvider = {
     * fetchPublicServiceInfo: (id: string): Promise<PublicServiceInfo> => {
     *    // 真实数据需要从 App 获取
     *    // 1，请求到数据
     *    let info = new PublicServiceInfo()
     *    info.targetId = id
     *    info.name = "名称"
     *    info.portrait = "头像地址"
     *    return new Promise<PublicServiceInfo>((resolve) => { resolve(info) })
     *    // 2，没有请求到数据
     *    return new Promise<PublicServiceInfo>((resolve) => { resolve(new PublicServiceInfo()) })
     *  }
     * }
     *  // 设置Provider，需要在初始化的时候调用，只调用一次即可。
     * InnerRongIMImpl.getInstance().publicService().setPublicServiceDataProvider(provider)
     * ```
     * @param provider 提供者
     */
    setPublicServiceDataProvider(provider: PublicServiceDataProvider): void;
    /**
     * 获取设置的公众号信息提供者
     * @returns provider 提供者
     */
    getPublicServiceDataProvider(): PublicServiceDataProvider | null;
    /**
     * 增加公众号数据监听，当 sdk 的公众号信息发生变化时触发
     * @param listener 监听
     * @warning addPublicServiceDataListener & removePublicServiceDataListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     */
    addPublicServiceDataListener(listener: PublicServiceDataListener): void;
    /**
     * 移除公众号数据监听
     * @param listener 监听
     * @warning addPublicServiceDataListener & removePublicServiceDataListener 配合使用，避免内存泄露
     */
    removePublicServiceDataListener(listener: PublicServiceDataListener): void;
    /**
     * 获取公众号信息数据，优先返回 sdk 的内部数据。如果 sdk 没有对应的数据，会调用 provider 获取，然后触发 UserDataListener
     * ## 说明
     * 1，命中缓存，则返回缓存中的公众号信息；
     *
     * 2，如果设置了PublicServiceDataProvider，则调用 Provider 返回公众号信息；
     *
     * 3，如果 Provider 返回公众号信息不为空，刷新内存缓存。
     *
     * @param id 公众号 Id
     * @returns 公众号信息数据，undefined 则不会更新内存
     */
    getPublicServiceInfo(id: string): Promise<PublicServiceInfo | undefined>;
    /**
     * 获取内存中缓存的公众号信息数据
     * ## 说明
     * 命中缓存，则返回缓存中的公众号信息；否则返回 undefined。
     *
     * @param id 公众号 Id
     * @returns 公众号信息数据，内存没有则返回 undefined。
     */
    getPublicServiceInfoCache(id: string): PublicServiceInfo | undefined;
    /**
     * 更新公众号信息数据，然后触发 PublicServiceDataListener
     * @param info 公众号信息数据
     */
    updatePublicServiceInfo(info: PublicServiceInfo): void;
    /**
     * 清理内存缓存
     */
    clear(): void;
}
