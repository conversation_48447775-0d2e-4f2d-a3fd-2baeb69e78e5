import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_blue_plus/flutter_blue_plus.dart';
import 'package:wuling_flutter_app/models/car/car_info_model.dart';
import 'package:wuling_flutter_app/models/global_data.dart';
import 'package:wuling_flutter_app/models/index.dart';
import 'package:wuling_flutter_app/utils/manager/ble_manager.dart';
import 'package:wuling_flutter_app/models/car/car_ble_key_model.dart';
import 'package:wuling_flutter_app/utils/manager/loading_manager.dart';

import '../../models/user/user_model.dart';
import '../../utils/manager/log_manager.dart';
class BleScanPage extends StatefulWidget {
  @override
  _BleScanPageState createState() => _BleScanPageState();
}

class _BleScanPageState extends State<BleScanPage> {
  final BleManager _bleManager = BleManager();
  bool _isScanning = false;
  String _scanStatus = '';
  CarInfoModel? _carInfoModel;
  UserModel? _userModel;
  @override
  void initState() {
    super.initState();
    _initBleKey();
  }

  void _initBleKey() async {
    _carInfoModel = GlobalData().carInfoModel;
    _userModel = GlobalData().userModel;
    if(_carInfoModel?.bleType == 2){
      if(_carInfoModel?.vin != null) {
        String vin = _carInfoModel?.vin ?? '';
        String mobile = GlobalData().userModel?.mobile ?? '';
        LogManager().debug("蓝牙钥匙获取中...");
        BleManager().getBluetoothKey(
            vin: vin,
            mobile: mobile,
            onResult: (CarBleKeyModel? bleKeyModel, String? error){
              if (bleKeyModel != null) {
                LogManager().debug("蓝牙钥匙获取成功: ${bleKeyModel.bleMac}");
                // 在这里处理钥匙信息
              } else {
                LogManager().debug("蓝牙钥匙获取失败: $error");
                // 在这里处理错误信息
              }
            });
      }
    }
  }

  void _startScan() async {
    if (!_isScanning) {
      setState(() {
        _isScanning = true;
        _scanStatus = '正在扫描...';
      });

      await _bleManager.startScanWithTimeout(true,30, (bool found) {
        setState(() {
          _isScanning = false;
          _scanStatus = found ? '找到目标设备' : '未找到目标设备';
        });

        if (found) {
          BluetoothDevice? targetDevice = _bleManager.getTargetDevice();
          if (targetDevice != null) {
            LogManager().debug('找到目标设备: ${targetDevice.platformName}');
            // 这里可以执行连接设备的操作
            // _connectToDevice(targetDevice);
          }
        }
      });
    }
  }

  void _connectToDevice(bool isHandle,BluetoothDevice device) async {
    try {
      await _bleManager.connectToDevice(isHandle,device);
      LogManager().debug('成功连接到设备: ${device.platformName}');
      // 这里可以执行连接后的操作
    } catch (e) {
      LogManager().debug('连接设备失败: $e');
    }
  }

  void _lockCar()async {
    Map<String, dynamic> params = {
      'btParam': 0, // 锁车参数
    };
    LogManager().debug('[E300BLE]---开始锁车---');
    _bleManager.sendCommandWithServiceBleSkipTarget('kBtCarLock', params, (error, isSuccess) {
      if(isSuccess){
        LogManager().debug('[E300BLE]---锁车成功');
      }else{
        LogManager().debug('[E300BLE]---锁车失败 ${error.toString()}');
      }
    });
  }

  void _unlockCar()async {
    Map<String, dynamic> params = {
      'btParam': 1, // 锁车参数
    };
    LogManager().debug('[E300BLE]---开始解锁---');
    _bleManager.sendCommandWithServiceBleSkipTarget('kBtCarLock', params, (error, isSuccess) {
      if(isSuccess){
        LogManager().debug('[E300BLE]---解锁成功');
      }else{
        LogManager().debug('[E300BLE]---解锁失败 ${error.toString()}');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('BLE扫描示例')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Text(_scanStatus),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: _isScanning ? null : _startScan,
              child: Text(_isScanning ? '扫描中...' : '开始扫描'),
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: _lockCar,
              child: Text(' 锁车 '),
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: _unlockCar,
              child: Text(' 解锁 '),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _bleManager.stopScan(false);
    super.dispose();
  }
}

class BleTestPage extends StatefulWidget {
  const BleTestPage({Key? key}) : super(key: key);

  @override
  _BleTestPageState createState() => _BleTestPageState();
}

class _BleTestPageState extends State<BleTestPage> {
  final BleManager _bleManager = BleManager();
  late StreamSubscription<BleStatus> _statusSubscription;
  BleStatus _currentStatus = BleStatus.bleDefault;
  CarBleKeyModel? _bleKey;
  CarInfoModel? _carInfoModel;
  UserModel? _userModel;
  bool _isConnecting = false;

  @override
  void initState() {
    super.initState();
    _fetchBluetoothKey();
    _subscribeToStatusStream();
  }

  void _fetchBluetoothKey() {
    _carInfoModel = GlobalData().carInfoModel;
    _userModel = GlobalData().userModel;
    if(_carInfoModel?.bleType == 2) {
      if (_carInfoModel?.vin != null) {
        String vin = _carInfoModel?.vin ?? '';
        String mobile = GlobalData().userModel?.mobile ?? '';
        _bleManager.getBluetoothKey(
          vin: vin,
          mobile: mobile,
          onResult: (CarBleKeyModel? bleKeyModel, String? error) {
            if (bleKeyModel != null) {
              setState(() {
                _bleKey = bleKeyModel;
              });
            } else {
              _showErrorDialog('获取蓝牙钥匙失败', error ?? '未知错误');
            }
          },
        );
      }
    }
  }

  void _subscribeToStatusStream() {
    _statusSubscription = _bleManager.statusStream.listen((status) {
      setState(() {
        _currentStatus = status;
      });
    });
  }

  void _clickScanButton(){
    if(_currentStatus == BleStatus.bleDefault){
      _startScan();
    } else if (_currentStatus == BleStatus.bleSearching){
      _stopScan(true);
    } else if (_currentStatus == BleStatus.bleAuthorizing){
      LoadingManager.showInfo('正在进行鉴权');
    } else if (_currentStatus == BleStatus.bleAuthorized) {
      _disconnect(true);
    }
  }

  void _stopScan(bool isHandle){
    _bleManager.stopScan(isHandle);
  }

  void _startScan() {
    _bleManager.startScanWithTimeout(true,30, (bool found) {
      if (!found) {
        LoadingManager.showInfo('未找到目标车辆，请稍后重试');
      }
    });
  }

  void _disconnect(bool isHandle){
    _bleManager.disconnectDevice(isHandle);
  }

  void _sendCommand(int command) {
    String action = '';
    if(command == 1){
      action = '解锁';
    }else{
      action = '闭锁';
    }
    LoadingManager.show(status: '蓝牙$action中...');
    _bleManager.sendCommandWithServiceBleSkipTarget(
      kBtCarLock,
      {'btParam': command},
          (Exception? error, bool isSuccess) {
        if (!isSuccess) {
          LoadingManager.showError('蓝牙$action失败 ${error.toString()}');
        }else{
          LoadingManager.showSuccess('蓝牙$action成功');
        }
      },
    );
  }

  void _showErrorDialog(String title, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('确定'),
            ),
          ],
        );
      },
    );
  }

  String _getStatusText(BleStatus status) {
    switch (status) {
      case BleStatus.bleDefault:
        return '未连接';
      case BleStatus.bleSearching:
        return '搜索中';
      case BleStatus.bleAuthorizing:
        return '握手授权中';
      case BleStatus.bleAuthorized:
        return '鉴权成功';
      default:
        return '未知状态';
    }
  }

  String _getButtonStatusText(BleStatus status) {
    switch (status) {
      case BleStatus.bleDefault:
        return '开始搜索';
      case BleStatus.bleSearching:
        return '搜索中';
      case BleStatus.bleAuthorized:
        return '已连接';
      default:
        return '未知状态';
    }
  }

  @override
  void dispose() {
    _statusSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('BLE 测试页面'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text('当前蓝牙状态: ${_getStatusText(_currentStatus)}'),
            const SizedBox(height: 20),
            if (_bleKey != null)
              ElevatedButton(
                onPressed: _clickScanButton,
                child: Text(_getButtonStatusText(_currentStatus)),
              ),
            if (_currentStatus == BleStatus.bleAuthorized) ...[
              ElevatedButton(
                onPressed: () => _sendCommand(1),
                child: const Text('解锁'),
              ),
              ElevatedButton(
                onPressed: () => _sendCommand(0),
                child: const Text('闭锁'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}