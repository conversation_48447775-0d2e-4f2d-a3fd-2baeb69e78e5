import http from '@ohos.net.http';
import { AIOCrashLogger } from './utils/AIOCrashLogger';
const TAG = "AIOCrashApi";
export class AI<PERSON>rashApi {
    static reportExceptionFile(c, d) {
        let e = new Promise((g, h) => {
            let i = http.createHttp();
            i.request(AIOCrashApi.RequestUrl, {
                method: http.RequestMethod.POST,
                header: {
                    'content-type': 'multipart/form-data', 'cache-control': 'no-cache'
                },
                expectDataType: http.HttpDataType.STRING,
                multiFormDataList: [
                    {
                        name: "file",
                        contentType: 'text/plain',
                        data: d,
                        remoteFileName: c
                    }
                ]
            }, (k, l) => {
                if (!k) {
                    AIOCrashLogger.info(TAG, `Upload ${c} success!`);
                    AIOCrashLogger.info(TAG, 'Result:' + JSON.stringify(l.result));
                    AIOCrashLogger.info(TAG, 'code:' + JSON.stringify(l.responseCode));
                    g(l);
                }
                else {
                    AIOCrashLogger.error(TAG, `Upload ${c} error:'${JSON.stringify(k)}`);
                    h(k);
                }
                i.destroy();
            });
        });
        return e;
    }
}
AIOCrashApi.RequestUrl = 'https://px.ucweb.com/upload';
