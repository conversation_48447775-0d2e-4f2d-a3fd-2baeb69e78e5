import { AliLiveShiftPlayer, OnSeekLiveCompletionListener, OnTimeShiftUpdaterListener } from "./AliLiveShiftPlayer";
import { AVPBase } from "./AVPBase";
import { LiveShift } from "./source/LiveShift";
import { Context } from "@kit.AbilityKit";
import { NativePlayerBase } from "./nativeclass/NativePlayerBase";
import { OnLoadingStatusListener, OnPreparedListener, OnStateChangedListener } from "../player/IPlayer";
export declare class ApsaraLiveShiftPlayer extends AVPBase implements AliLiveShiftPlayer {
    private static tag;
    static SeekLive: number;
    private status;
    private statusWhenSeek;
    private liveSeekToTime;
    private liveSeekOffset;
    private mLiveShiftSource;
    private mLiveTimeUpdater;
    private mOutOnTimeShiftUpdaterListener;
    private mInnerOnTimeShiftUpdaterListener;
    private mOutLiveOnPreparedListener;
    private mInnerLiveOnPreparedListener;
    private mOutLiveOnStateChangedListener;
    private mInnerLiveOnStateChangedListener;
    private mOutLiveOnLoadingStatusListener;
    private mInnerLiveOnLoadingStatusListener;
    private mOutLiveSeekLiveCompletionListener;
    constructor(o9: Context, p9: string);
    protected createAlivcMediaPlayer(m9: Context): NativePlayerBase;
    setLiveShiftDataSource(j9: LiveShift): void;
    getCurrentLiveTime(): number;
    getCurrentTime(): number;
    seekToLiveTime(b9: number): void;
    start(): void;
    pause(): void;
    stop(): void;
    onUpdater(y8: number, z8: number, a9: number): void;
    onPrepared(): void;
    onStateChanged(w8: number): void;
    onLoadingBegin(): void;
    onLoadingProgress(u8: number, v8: number): void;
    onLoadingEnd(): void;
    setOnTimeShiftUpdaterListener(t8: OnTimeShiftUpdaterListener): void;
    setOnSeekLiveCompletionListener(s8: OnSeekLiveCompletionListener): void;
    setOnStateChangedListener(r8: OnStateChangedListener): void;
    setOnPreparedListener(q8: OnPreparedListener): void;
    setOnLoadingStatusListener(p8: OnLoadingStatusListener): void;
}
