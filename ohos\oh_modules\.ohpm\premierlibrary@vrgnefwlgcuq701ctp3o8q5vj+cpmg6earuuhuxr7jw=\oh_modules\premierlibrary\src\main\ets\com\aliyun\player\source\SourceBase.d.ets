export declare class SourceBase {
    private mCoverPath;
    private mTitle;
    protected mQuality: String;
    protected mForceQuality: boolean;
    private nativeGetCoverPath;
    private nativeSetCoverPath;
    private nativeGetTitle;
    private nativeSetTitle;
    private nativeGetQuality;
    private nativeIsForceQuality;
    /**
     * 获取封面地址
     *
     * @return 封面地址
     */
    /****
     * Query the URL of the album cover.
     *
     * @return The URL of the album cover.
     */
    getCoverPath(): String;
    /**
     * 设置封面地址
     *
     * @param mCoverPath 封面地址
     */
    /****
     * Set the URL of the album cover.
     *
     * @param mCoverPath The URL of the album cover.
     */
    setCoverPath(g35: string): void;
    /**
     * 获取标题
     *
     * @return 标题
     */
    /****
     * Query the title of the media.
     *
     * @return The title of the media.
     */
    getTitle(): String;
    /**
     * 设置标题
     *
     * @param mTitle 标题
     */
    /****
     * Set the title of the media.
     *
     * @param mTitle The title of the media.
     */
    setTitle(f35: String): void;
    /**
     * 获取清晰度
     *
     * @return 清晰度
     */
    /****
     * Query the definition of the media.
     *
     * @return The definition of the media.
     */
    getQuality(): String;
    /**
     * 是否强制清晰度
     *
     * @return true：是。
     */
    /****
     * Indicate whether to force the player to play the media with the specified definition.
     *
     * @return Value true indicates that the player is forced to play the media with the specified definition.
     */
    isForceQuality(): boolean;
}
