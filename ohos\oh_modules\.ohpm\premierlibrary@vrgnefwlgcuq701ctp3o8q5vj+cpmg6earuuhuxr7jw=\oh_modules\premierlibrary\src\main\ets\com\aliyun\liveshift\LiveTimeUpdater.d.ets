import { OnTimeShiftUpdaterListener } from "../player/AliLiveShiftPlayer";
import { PlayerConfig } from "../player/nativeclass/PlayerConfig";
import { LiveShift } from "../player/source/LiveShift";
export declare class LiveTimeUpdater {
    private static WHAT_UPDATE_LIVE_TIME;
    private static WHAT_UPDATE_PLAY_TIME;
    private mConfig;
    private mTimeShift;
    private timeShiftUpdaterListener;
    private playTime;
    private liveTime;
    private needPause;
    private lastLiveMsgID;
    private lastPlayMsgID;
    constructor(u3: LiveShift);
    private HandleMessage;
    private getStartTime;
    private getEndTime;
    private updateLiveTimer;
    private stopUpdatePlayTimer;
    private stopUpdateLiveTimer;
    private startUpdatePlayTimerDelay;
    private startUpdateLiveTimerDelay;
    private sendEmptyMessageDelayed;
    setStartPlayTime(t2: number): void;
    startUpdater(): void;
    setUpdaterListener(s2: OnTimeShiftUpdaterListener): void;
    pauseUpdater(): void;
    resumeUpdater(): void;
    stopUpdater(): void;
    getPlayTime(): number;
    getLiveTime(): number;
    setConfig(r2: PlayerConfig): void;
}
