import type { Point } from '@bdmap/base'; import BmDrawItem from "./a3"; import type BmSurfaceStyle from "./r3"; import BmLineStyle from "./f2/w3"; export default class BmCircle extends BmDrawItem { constructor();           setCenter(m10: Point): false | void;           setRadius(radius: number): void;           setPixelRadius(radius: number): any;           setLineStyle(style: BmLineStyle): void; setSurfaceStyle(style: BmSurfaceStyle): void;             setGradientColors(type: number, k10: Array<number>): void;           setIsGradientCircle(isGradient: boolean): void;           setGradientRadiusWeight(j10: number): void;           setGradientColorWeight(i10: number): void; } 