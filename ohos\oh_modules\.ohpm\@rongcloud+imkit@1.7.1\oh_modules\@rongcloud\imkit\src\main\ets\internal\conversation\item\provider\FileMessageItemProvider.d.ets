// @keepTs
// @ts-nocheck
/**
 * Created on 2024/07/09
 * <AUTHOR>
 */
import { FileMessage } from '@rongcloud/imlib';
import { BaseMessageItemProvider } from '../../../../conversation/item/provider/BaseMessageItemProvider';
import { UiMessage } from '../../../../conversation/model/UiMessage';
export declare class FileMessageItemProvider extends BaseMessageItemProvider<FileMessage> {
    getMessageWrapBuilder(): WrappedBuilder<[
        Context,
        UiMessage,
        number
    ]>;
    isShowSummaryName(c164: Context, d164: FileMessage): boolean;
    getSummaryTextByMessageContent(y163: Context, z163: FileMessage): Promise<MutableStyledString>;
}
@Builder
export declare function bindFileMessageData(n163: Context, o163: UiMessage, p163: number): void;
@Component
export declare struct FileMessageView {
    @Require
    @Prop
    context: Context;
    @Require
    @Prop
    index: number;
    @ObjectLink
    uiMessage: UiMessage;
    private messageContent;
    @State
    icon: ResourceStr;
    private columnWidth;
    @State
    firstLine: string;
    @State
    secondLine: string;
    aboutToAppear(): void;
    build(): void;
    private formatBytes;
    /**
     * 将字符串分割为两部分，第一部分为一行所能容纳的最大长度，第二部分为剩余部分
     * @param name
     * @param boxWidth
     */
    private splitFileName;
}
