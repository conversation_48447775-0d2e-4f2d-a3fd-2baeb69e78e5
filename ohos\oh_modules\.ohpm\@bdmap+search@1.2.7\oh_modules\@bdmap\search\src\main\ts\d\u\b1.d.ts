import { SearchResult } from "../e/h/f1";
import { WeatherSearchForecasts } from "./y";
import { WeatherSearchForecastForHours } from "./w";
import { WeatherLifeIndexes } from "./z";
import { WeatherSearchRealTime } from "./a1";
import { WeatherSearchAlerts } from "./c1";
import { WeatherSearchLocation } from "./d1";
/** 天气检索结果 */
export interface WeatherResult extends SearchResult {
    /** 天气实况数据 */
    realTimeWeather?: WeatherSearchRealTime;
    /** 地理位置信息 */
    location?: WeatherSearchLocation;
    /** 未来若干天天预报数据 */
    forecasts?: WeatherSearchForecasts[];
    /** 未来24小时逐小时预报，高级字段 */
    forecastHours?: WeatherSearchForecastForHours[];
    /** 生活指数数据，高级字段 */
    lifeIndexes?: WeatherLifeIndexes[];
    /** 气象预警数据，高级字段 */
    weatherAlerts?: WeatherSearchAlerts[];
}
