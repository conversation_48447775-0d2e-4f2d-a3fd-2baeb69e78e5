#!/bin/bash

# 设置protoc-gen-dart路径
PROTOC_GEN_DART="$HOME/.pub-cache/bin/protoc-gen-dart"

# 检查protoc-gen-dart是否存在
if [ ! -f "$PROTOC_GEN_DART" ]; then
    echo "Error: protoc-gen-dart not found at $PROTOC_GEN_DART"
    echo "Please install protoc_plugin: dart pub global activate protoc_plugin"
    exit 1
fi

# 检查protoc是否安装
if ! command -v protoc &> /dev/null; then
    echo "Error: protoc not found. Please install Protocol Buffers compiler."
    echo "On macOS: brew install protobuf"
    exit 1
fi

# 创建输出目录
mkdir -p lib/generated/proto

# 生成Dart文件
echo "Generating protobuf Dart files..."
protoc --plugin="protoc-gen-dart=$PROTOC_GEN_DART" --dart_out=lib/generated/proto --proto_path=protos protos/*.proto

if [ $? -eq 0 ]; then
    echo "Protobuf files generated successfully!"
else
    echo "Error generating protobuf files"
    exit 1
fi