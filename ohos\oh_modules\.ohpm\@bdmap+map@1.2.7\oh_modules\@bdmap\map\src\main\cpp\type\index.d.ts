// @ts-nocheck
import resourceManager from "@kit.LocalizationKit";
import image from '@ohos.multimedia.image';
interface ImageInfo {
  image_hashcode: string,
  image_data: image.PixelMap,
  image_width: number,
  image_height: number,
  image_scaleDpi: number,
  image_format: number
}
export function setSysValues(filesDir: string, cacheDir: string, densityDPI: number,
  resourceManager: resourceManager.ResourceManager, callback?: (result: string) => void);

export function NapiBmObject_Finalizer(nativeInstance: number);

export function NapiBmObject_SetLayerTag(nativeInstance: number, layerTag: string);

export function NapiBmDrawItem_SetVisibility(shellInstace: number, visibility: number);

export function NapiBmDrawItem_SetShowLevel(layerInstace: number, from: number, to: number);

export function NapiBmDrawItem_SetOpacity(shellInstace: number, opacity: number);

export function NapiBmDrawItem_SetClickable(shellInstace: number, bClickable: boolean);

export function NapiBmDrawItem_SetHoleClickable(shellInstace: number, bClickable: boolean);

export function NapiBmDrawItem_SetAnimation(shellInstace: number, animShell: number);

export function NapiBmLayer_Create();

export function NapiBmLayer_GetLayerId(layerInstace: number): number;

export function NapiBmLayer_SetCollisionBaseMap(layerInstace: number, bCollisionBaseMap: boolean);

export function NapiBmLayer_CommitUpdate(layerInstace: number);

export function NapiBmLayer_SetVisibility(layerInstace: number, visibility: number);

export function NapiBmLayer_SetClickable(layerInstace: number, clickable: boolean);

export function NapiBmLayer_SetShowLevel(layerInstace: number, startLevel: number, endLevel: number);

export function NapiBmLayer_AddDrawItem(layerInstance: number, itemInstance: number);

export function NapiBmLayer_AddDrawItemByZIndex(layerInstance: number, itemInstance: number, zIdx: number);

export function NapiBmLayer_AddDrawItemBelow(layerInstance: number, followInstance: number, itemInstance: number);

export function NapiBmLayer_AddDrawItemAbove(layerInstance: number, aboveInstance: number, itemInstance: number);

export function NapiBmLayer_RemoveDrawItem(layerInstance: number, itemInstance: number);

export function NapiBmLayer_ClearDrawItems(layerInstance: number);

export function NapiBmLayer_GetDrawItemRect(layerInstance: number, left: number, top: number, right: number,
  bottom: number);

export function NapiBmLayer_HandleClick(layerInstace: number, scrX: number, scrY: number, radius: number);

export function NapiBmCircle_Create();

export function NapiBmCircle_SetCenter(nativeInstance: number, x: number, y: number, z: number);

export function NapiBmCircle_SetRadius(shellInstance: number, radius: number);
export function NapiBmCircle_SetPixelRadius(nativeInstance: number, radius: number);
export function NapiBmCircle_SetLineStyle(nativeInstance: number, styleShell: number);
export function NapiBmCircle_SetSurfaceStyle(nativeInstance: number, styleShell: number);
export function NapiBmCircle_SetIsGradientCircle(nativeInstance:number, isGradientCircle:boolean);
export function NapiBmCircle_SetGradientRadiusWeight( nativeInstance:number,  gradientRadiusWeight:number);
export function NapiBmCircle_SetGradientColorWeight( nativeInstance:number,  gradientColorWeight:number);
export function NapiBmCircle_SetGradientColors( nativeInstance:number, type:number,  colors:number[]);


export function NapiBmSurfaceStyle_Create();

export function NapiBmSurfaceStyle_SetColor(shellInstace: number, color: number);
export function NapiBmSurfaceStyle_SetBitmapResource(shellInstace: number, resInstance);
export function NapiBmSurfaceStyle_SetBmpResId(shellInstace:number, bitmapId:number);

export function NapiBmBaseMarker_SetXYZ(nativeInstance: number, x: number, y: number, z: number)


export function NapiBmBaseMarker_SetX(nativeInstance: number, x: number)


export function NapiBmBaseMarker_SetY(nativeInstance: number, y: number)


export function NapiBmBaseMarker_SetZ(nativeInstance: number, z: number)


export function NapiBmBaseMarker_SetPerspective(shellInstace: number, perspective: number)


export function NapiBmBaseMarker_SetIsFix(shellInstace: number, isfix: number)


export function NapiBmBaseMarker_SetFixX(shellInstace: number, fixX: number)


export function NapiBmBaseMarker_SetFixY(shellInstace: number, fixY: number)


export function NapiBmBaseMarker_SetWidth(shellInstace: number, width: number)


export function NapiBmBaseMarker_SetHeight(shellInstace: number, height: number)


export function NapiBmBaseMarker_SetLocated(shellInstace: number, located: number)


export function NapiBmBaseMarker_SetOffsetX(shellInstace: number, offsetX: number, scaleMode: number)


export function NapiBmBaseMarker_SetOffsetY(shellInstace: number, offsetY: number, scaleMode: number)


export function NapiBmBaseMarker_SetScale(shellInstace: number, scale: number)


export function NapiBmBaseMarker_SetScaleX(shellInstace: number, scaleX: number)


export function NapiBmBaseMarker_SetScaleY(shellInstace: number, scaleY: number)


export function NapiBmBaseMarker_SetRotate(shellInstace: number, rotate: number)


export function NapiBmBaseMarker_SetCollisionBehavior(shellInstace: number, behavior: number)


export function NapiBmBaseMarker_SetCollisionPriority(shellInstace: number, priority: number)


export function NapiBmBaseMarker_SetTrackBy(shellInstace: number, trackBy: number)


export function NapiBmBaseMarker_SetRotateFeature(shellInstace: number, feature: number)


export function NapiBmBaseMarker_SetFollowMapRotateAxis(shellInstace: number, eRotateAxis: number)


export function NapiBmBaseMarker_SetId(shellInstace: number, buildingId: string)


export function NapiBmBaseMarker_SetBuildingId(shellInstace: number, buildingId: string)


export function NapiBmBaseMarker_SetFloorId(shellInstace: number, floorId: string)


export function NapiBmBaseMarker_AddRichView(shellInstace: number, viewInstance: number)


export function NapiBmBaseMarker_RemoveRichView(shellInstace: number, viewInstance: number)


export function NapiBmBaseMarker_ClearRichViews(shellInstace: number)


export function NapiBmBaseMarker_SetDrawFullscreenMaskFlag(nativeInstance: number, flag: boolean)

export function NapiBmIconMarker_Create();

export function NapiBmIconMarker_SetBmpResId(shellInstace: number, resId: number);

export function NapiBmIconMarker_SetDrawableResource(shellInstace: number, resInstance: number);

export function NapiBmIconMarker_SetColor(shellInstace: number, abgr: number);

export function NapiBmIconMarker_SetAnimationType(shellInstace: number, type: number);

export function NapiBmPrism_Create();
export function NapiBmPrism_AddGeoElement(nativeInstance: number, geoElemShell: number);
export function NapiBmPrism_ClearGeoElements(nativeInstance: number);
export function NapiBmPrism_SetSurfaceTopStyle(nativeInstance: number, styleShell: number);
export function NapiBmPrism_SetSurfaceSideStyle(nativeInstance: number, styleShell: number);
export function NapiBmPrism_SetSurfaceFloorTopStyle(nativeInstance: number, styleShell: number);
export function NapiBmPrism_SetSurfaceFloorSideStyle(nativeInstance: number, styleShell: number);
export function NapiBmPrism_SetHeight(nativeInstance: number, height: number);
export function NapiBmPrism_SetLastFloorHeight(nativeInstance: number, height: number);
export function NapiBmPrism_SetFloorHeight(nativeInstance: number, height: number);
export function NapiBmPrism_SetBuildingID(nativeInstance: number, buildingId: string);
export function NapiBmPrism_SetIsAnimation(nativeInstance: number, isAnimation: boolean);
export function NapiBmPrism_SetHasFloor(nativeInstance: number, hasFloor: boolean);
export function NapiBmPrism_SetIsBuilding(nativeInstance: number, isBuilding: boolean);
export function NapiBmPrism_SetAnimateType(nativeInstance: number, animationType: number);
export function NapiBmPrism_SetFloorAnimateType(nativeInstance: number, floorAnimationType: number);
export function NapiBmPrism_SetIsRoundedCorner(nativeInstance: number, isRoundedCorner: boolean);
export function NapiBmPrism_SetRoundedCornerRadius(nativeInstance: number, roundedCornerRadius: number);


export function NapiBmBitmapResource_Create();

export function NapiBmBitmapResource_SetBitmap(shellInstace: number, image: ImageInfo);

export function NapiBmBitmapResource_SetScaleX(shellInstace: number, scale: number[], size: number);

export function NapiBmBitmapResource_SetScaleY(shellInstace: number, scale: number[], size: number);

export function NapiBmBitmapResource_SetFillArea(shellInstace: number, x1: number, x2: number, y1: number, y2: number);

export function NapiBmTextStyle_Create();

export function NapiBmTextStyle_SetTextColor(shellInstace: number, argb: number);

export function NapiBmTextStyle_SetTextSize(shellInstace: number, textSize: number);

export function NapiBmTextStyle_SetBorderColor(shellInstace: number, argb: number);

export function NapiBmTextStyle_SetBorderWidth(shellInstace: number, width: number);

export function NapiBmTextStyle_SetFontOption(shellInstace: number, option: number);

export function NapiBmTextStyle_SetTextBackColor(shellInstace: number, option: number);

export function NapiBmBaseUI_SetBackground(shellInstace: number, resShell: number);

export function NapiBmBaseUI_SetBackgroundResId(shellInstace: number, resId: number);

export function NapiBmBaseUI_SetBackgroundColor(shellInstace: number, abgr: number);

export function NapiBmBaseUI_SetBkColorOfLeft(shellInstace: number, abgr: number);

export function NapiBmBaseUI_SetBkColorOfRight(shellInstace: number, abgr: number);

export function NapiBmBaseUI_SetWidth(shellInstace: number, width: number);

export function NapiBmBaseUI_SetHeight(shellInstace: number, height: number);

export function NapiBmBaseUI_SetVisibility(shellInstace: number, visibility: number);

export function NapiBmBaseUI_SetGravity(shellInstace: number, gravity: number);

export function NapiBmBaseUI_SetAlignParent(shellInstace: number, alignParent: number);

export function NapiBmBaseUI_SetLayoutWeight(shellInstace: number, layoutWeight: number);

export function NapiBmBaseUI_SetPadding(shellInstace: number, left: number, top: number, right: number, bottom: number);

export function NapiBmBaseUI_SetMargin(shellInstace: number, left: number, top: number, right: number, bottom: number);

export function NapiBmBaseUI_SetClickable(shellInstace: number, bClickable: boolean);

export function NapiBmLabelUI_Create();

export function NapiBmLabelUI_SetText(instance: number, text: string);

export function NapiBmLabelUI_SetStyle(instance: number, styleShell: number);

export function NapiBmLabelUI_SetMinLines(instance: number, minLines: number);

export function NapiBmLabelUI_SetMaxLines(instance: number, maxLines: number);

export function NapiBmRichView_Create();

export function NapiBmRichView_SetView(shellInstace: number, viewShell: number);

export function NapiBmRichView_SetAnimation(shellInstace: number, animShell: number);

export function NapiBmRichView_SetCollisionBehavior(layerInstace:number, behavior:number);

export function NapiBmRichView_SetCollisionPriority(layerInstace: number, priority: number);

export function NapiBmRichView_SetCollisionBorder(shellInstace: number, left: number, top: number, right: number,
  bottom: number);

export function NapiBmRichView_SetCollisionLineTagId(layerInstace: number, lineTagId: number);

export function NapiBmRichView_SetVisibility(shellInstace: number, visibility: number);

export function NapiBmRichView_SetShowLevel(layerInstace: number, from: number, to: number);

export function NapiBmRichView_SetLocated(shellInstace: number, located: number);

export function NapiBmRichView_SetOffsetX(shellInstace: number, offsetX: number, scaleMode: number);

export function NapiBmRichView_SetOffsetY(shellInstace: number, offsetY: number, scaleMode: number);

export function NapiBmRichView_SetOpacity(shellInstace: number, opacity: number);

export function NapiBmRichView_SetScale(shellInstace: number, scale: number);

export function NapiBmRichView_SetScaleX(shellInstace: number, scaleX: number);

export function NapiBmRichView_SetScaleY(shellInstace: number, scaleY: number);

export function NapiBmRichView_AddRichUIOption(shellInstace: number, optionShell: number);

export function NapiBmRichView_DelRichUIOption(shellInstace: number, optionShell: number);

export function NapiBmRichView_SetDrawFullscreenMaskFlag(instance: number, flag: boolean);

export function NapiBmLineStyle_Create();
export function  NapiBmLineStyle_SetColor(shellInstace:number, color:number);
export function  NapiBmLineStyle_SetBitmapResource(shellInstace:number, bmpResShell:number);
export function  NapiBmLineStyle_SetBmpResId(shellInstace:number, bmpResId:number);
export function  NapiBmLineStyle_SetLineResId(shellInstace:number, lineResId:number);
export function  NapiBmLineStyle_SetWidth(shellInstance:number, width:number);
export function  NapiBmLineStyle_SetStrokeWidth(shellInstance:number, width:number);
export function  NapiBmLineStyle_SetStrokeColor(shellInstace:number, color:number);
export function  NapiBmLineStyle_SetTextureOption(shellInstace:number, option:number);
export function  NapiBmLineStyle_SetLineType(shellInstace:number, lineType:number);

export function NapiBmGround_Create();
export function NapiBmGround_SetPosition(nativeInstance:number, x:number, y:number, z:number);
export function NapiBmGround_SetWidth(shellInstance:number, width:number);
export function NapiBmGround_SetHeight(shellInstance:number, height:number);
export function NapiBmGround_SetAnchorX(shellInstance:number, anchorX:number);
export function NapiBmGround_SetAnchorY(shellInstance:number, anchorY:number);
export function NapiBmGround_SetDrawableResource(shellInstace:number, resInstance:number);

export function NapiBmGeoElement_Create( coordChainType:number);
export function NapiBmGeoElement_AddPoint( nativeInstance:number,  mcX:number,  mcY:number,  mcZ:number);
export function NapiBmGeoElement_SetPoints( nativeInstance:number, dataArray:Array<number>,  stride:number);
export function NapiBmGeoElement_SetStyle( nativeInstance:number,  styleShell:number);
export function NapiBmGeoElement_SetTrackStyle( nativeInstance:number,  styleShell:number);
export function NapiBmGeoElement_AddStyleOption( nativeInstance:number,  styleShell:number);
export function NapiBmGeoElement_RemoveStyleOption( nativeInstance:number,  styleShell:number);

export function NapiBmGeoElement_SetGradientColors( nativeInstance:number,  type:number,  colors:Array<number>);
export function NapiBmGeoElement_DelGradientColors( nativeInstance:number,  type:number);
export function NapiBmGeoElement_ClearGradientColors( nativeInstance:number);

export function NapiBmGeoElement_SetCoordChainType( nativeInstance:number,  coordChainType:number);
export function NapiBmGeoElement_SetCoordChainHandle( nativeInstance:number,  handleShell:number);

export function NapiBmTrackStyle_Create();

export function NapiBmTrackStyle_SetColor( shellInstace:number,  color:number);

export function NapiBmTrackStyle_SetWidth( shellInstance:number,  width:number);

export function NapiBmTrackStyle_SetBitmapResource( shellInstace:number,  bmpResShell:number);

export function NapiBmTrackStyle_SetPaletteBitmapResource( shellInstace:number, bmpResShell:number);

export function NapiBmTrackStyle_SetTrackType( shellInstace:number,  lineType:number);

export function NapiBmTrackStyle_SetOpacity( shellInstace:number,  opacity:number);

export function NapiBmTrackStyle_SetPaletteOpacity( shellInstace:number,  opacity:number);

export function NapiBmLineStyleOption_Create();

export function NapiBmLineStyleOption_BuildStyleOption(shellInstance:number, state:number, styleShell:number);

export function NapiBmCoordChainHandle_Create();
export function NapiBmCoordChainHandle_SetCoordChainType( nativeInstance:number,  coordChainType:number);
export function NapiBmCoordChainHandle_SetCoordAlgorithm( nativeInstance:number,  coordAlgorithm:number);
export function NapiBmCoordChainHandle_SetThreshold( nativeInstance:number, threshold:number);
export function NapiBmCoordChainHandle_Handle( nativeInstance:number, dataArray:Array<number>, stride:number);
export function NapiBmCoordChainHandle_GetIndexs( nativeInstance:number);
export function NapiBmCoordChainHandle_GetP0Points( nativeInstance:number);
export function NapiBmPolygon_Create();
export function NapiBmPolygon_AddGeoElement(nativeInstance:number, geoElemShell:number);
export function NapiBmPolygon_AddHoleGeoElement(nativeInstance:number, geoElemShell:number);
export function NapiBmPolygon_ClearGeoElements(nativeInstance:number);

export function NapiBmPolygon_SetThin(shellInstance:number, thin:number);
export function NapiBmPolygon_SetThinFactor(shellInstance:number, factor:number);
export function NapiBmPolygon_SetJointType(shellInstance:number, jointType:number);
export function NapiBmPolygon_SetSurfaceStyle(nativeInstance:number, styleShell:number);
export function NapiBmPolygon_SetDrawFullscreenMaskFlag(nativeInstance:number, flag:boolean);

export function NapiBmBaseLine_SetGeoElement(nativeInstance:number, geoElemShell:number);
export function NapiBmBaseLine_AddGeoElement(nativeInstance:number, geoElemShell:number);
export function NapiBmBaseLine_ClearGeoElements(nativeInstance:number);
export function NapiBmBaseLine_SetSmooth(shellInstance:number,smooth:number);
export function NapiBmBaseLine_SetThin(shellInstance:number,thin:number);
export function NapiBmBaseLine_SetSmoothFactor(shellInstance:number,factor:number);
export function NapiBmBaseLine_SetThinFactor(shellInstance:number,factor:number);
export function NapiBmBaseLine_SetStartCapType(shellInstance:number,capType:number);
export function NapiBmBaseLine_SetEndCapType(shellInstance:number,capType:number);
export function NapiBmBaseLine_SetJointType(shellInstance:number,jointType:number);
export function NapiBmBaseLine_SetCollisionTagId(nativeInstance:number,collisionTagId:number);
export function NapiBmBaseLine_SetCollisionBehavior(nativeInstance:number,behavior:number);
export function NapiBmBaseLine_SetLineBloomMode(nativeInstance:number,mode:number);
export function NapiBmBaseLine_SetBloomBlurTimes(nativeInstance:number,time:number);
export function NapiBmBaseLine_SetLineDirectionCrossType(nativeInstance:number,type:number);
export function NapiBmBaseLine_SetBloomAlpha(nativeInstance:number,alpha:number);
export function NapiBmBaseLine_SetBloomWidth(nativeInstance:number,width:number);
export function NapiBmBaseLine_SetBloomGradientASpeed(nativeInstance:number,speed:number);

export function NapiBmGradientLine_Create();

export function NapiBmPolyline_Create();
export function NapiBmPolyline_UseGeodesic(nativeInstance:number, bUseGeodesic:boolean);

export function NapiBmTextMarker_Create();
export function NapiBmTextMarker_SetText(nativeInstance:number, text:string);
export function NapiBmTextMarker_SetStyle(nativeInstance:number, styleShell:number);

export function NapiBmImageUI_Create();

export function NapiBmImageUI_SetBmpResId(shellInstace:number, resId:number);
export function NapiBmImageUI_SetDrawableResource(shellInstace:number, resInstance:number);
export function NapiBmImageUI_SetMaskResource(shellInstace:number, maskInstance:number);
export function NapiBmImageUI_SetColor(shellInstace:number, abgr:number);

export function  NapiBmGroupUI_AddView(shellInstace:number, viewInstance:number, index:number);
export function  NapiBmGroupUI_RemoveAllViews(shellInstace:number);

export function  NapiBmFrameLayout_Create();

export function  NapiBmHorizontalLayout_Create();

export function  NapiBmVerticalLayout_Create();

