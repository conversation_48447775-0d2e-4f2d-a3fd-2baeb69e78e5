import type { DeviceInfo } from "../g1/a2";             export default class MapEntry { static px2vp: number; static vp2px: number; static resManager: any; static filePath: string; static cachePath: string; static abilityContext: any; private context; private mapOptions; private map; private erroNetwork; constructor(context: any, mapOptions?: any); build(deviceInfo: DeviceInfo, callback: Function): void; private _initCanvas; private _initMapInstance; private _registerMapEvent; } 