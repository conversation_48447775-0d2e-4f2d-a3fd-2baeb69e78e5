import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/models/index.dart';

enum ServiceButtonLayoutType {
  none, //无
  ac, //空调
  other, //其他
}

class CarServiceUtil {
  static ServiceButtonLayoutType getButtonLayoutTypeWithCarServiceModel(
      CarServiceModel serviceModel) {
    for (CarServiceParamModel paramM in serviceModel.serviceSkipParamList!) {
      if (paramM.paramName == "buttonLayout") {
        if (paramM.paramValue == "1") {
          return ServiceButtonLayoutType.ac;
        } else if (paramM.paramValue == "2") {
          return ServiceButtonLayoutType.other;
        }
      }
    }
    return ServiceButtonLayoutType.none;
  }

  static CarServiceStatusModel? getCurrentStatusModelWithCarStatusValue(
      CarServiceModel serviceModel, String statusValue) {
    Map<String, CarServiceStatusModel> statusDic =
        CarServiceUtil.statusDic(serviceModel);
    if (statusDic.keys.contains(statusValue)) {
      CarServiceStatusModel? currentStatus = statusDic[statusValue];
      return currentStatus;
    }
    return null;
  }

  static Map<String, CarServiceStatusModel> statusDic(
      CarServiceModel serviceModel) {
    Map<String, CarServiceStatusModel> statusDic = {};
    for (CarServiceStatusModel statusModel in serviceModel.serviceStatusList!) {
      statusDic[statusModel.serviceStatusValue] = statusModel;
    }

    return statusDic;
  }
}
