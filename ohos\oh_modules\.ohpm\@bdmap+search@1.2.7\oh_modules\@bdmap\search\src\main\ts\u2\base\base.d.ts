import { BaseParamBuilder } from "../w2";
import { UrlProvider } from "./d3";
import { CityInfo, PlanNode, PoiInfo, SearchResult, SuggestAddrInfo } from "../../d/e/h/f1";
import { SearchType } from "./f1";
/**
 * 搜索结果解析抽象类
 */
export declare abstract class SearchParser {
    searchType: SearchType;
    /**
     * 获取解析结果，由子类实现
     * @param json
     * @return SearchResult
     */
    abstract parseSearchResult(e36: string): SearchResult;
}
/**
 * 搜索基类
 */
export declare abstract class BaseSearch {
    private static readonly TAG;
    /**
     * 服务器处理了请求，但是没返回内容
     */
    private static readonly RESULT_NO_CONTENT;
    private mHttpClient;
    /**
     * 目前主要用来解析路线规划相关的检索
     */
    private parserWorker;
    constructor();
    /**
     * 发起检索请求
     * @param searchRequest 检索参数封装
     * @param listener 检索结果回调
     * @param searchParser 检索结果解析器
     * @returns
     */
    protected sendRequest(y3: SearchRequest, z3: Readonly<SearchParser>): Promise<SearchResult>;
    /**
     * 发起检索请求，并在子线程解析数据
     * @param searchRequest 检索参数封装
     * @param listener 检索结果回调
     * @param searchParser 检索结果解析器
     * @returns
     */
    protected sendMultiRequest(y2: SearchRequest, z2: Readonly<SearchParser>): Promise<SearchResult>;
    /**
     * 检查服务端返回结果是否异常，如果无异常则不返回错误码
     * @param status
     * @returns
     */
    private mapServiceErrorToSearchResultError;
    /**
     * 获取检索响应结果的错误码
     * @param result
     * @returns
     */
    private getResponseStatus;
    protected destroy(): void;
}
/**
 * 生成搜索请求url的相关方法封装
 */
export declare abstract class SearchRequest {
    protected withToken: boolean;
    protected withSign: boolean;
    protected optionBuilder: BaseParamBuilder;
    /**
     * 获取URL domain
     * @param provider URL domain provider
     * @return
     */
    abstract getUrlDomain(d36: UrlProvider): string;
    /**
     * 得到发送的url串
     * @param searchType     检索类型，依据检索类型决定是否对数据加密
     *
     @return
     */
    toUrlString(): string | null;
    /**
     * 用于路径规划中的起终点
     */
    protected planNodeBuildParam(l2: PlanNode): string;
}
/**
 * 公交、驾车、步行 路线规划的公共父类，解析返回建议列表的情况
 */
export declare abstract class RoutePlanParser extends SearchParser {
    mRouteSugAddr: SuggestAddrInfo;
    private static readonly TYPE_ROUTE_SUG_ADDR;
    /**
     * 是否成功解析出建议列表
     */
    protected hasSugAddr: boolean;
    /**
     * 没有建议信息的时候由子类处理json
     * @param json
     * @param searchResult
     */
    abstract parseResult(b36: string, c36: SearchResult): any;
    parseSearchResult(f2: string): SearchResult;
    /**
     * 解析路线规划，返回建议列表的情况
     * @param json
     * @return true 表示解析建议列表成功，false表示解析建议列表失败
     */
    private parseRouteSugAddr;
    parseStringToMKRouteAddrResult(e1: any): SuggestAddrInfo;
    parseStringToWayPoiList(w: any, x: string): PoiInfo[][];
    parseAddrPoiList(p: any, q: string): PoiInfo[];
    parseStringToWayPoint(h: any, i: string): CityInfo[][];
    parseStringToCityListInfo(b: any): CityInfo[];
}
export declare class SERVICE_ERROR_TYPE {
    /** 高级权限才允许使用location字段 */
    static readonly FORWARD_NO_ADVANCED_PERMISSION_FOR_LOCATION = 2;
    /** 缺少必要的参数 */
    static readonly FORWARD_MISS_NECESSARY_PARAM = 10;
    /** 请求参数格式错误 */
    static readonly FORWARD_PARAM_FORMAT_ERROR = 11;
    /** 查询的区域编码无效 */
    static readonly FORWARD_INVALID_DISTRICT_ID = 40;
    /** 查询的经纬度值范围无效 */
    static readonly FORWARD_INVALID_LATLNG = 41;
    /** 经纬度所在地区无数据覆盖 */
    static readonly FORWARD_NO_DATA_FOR_LOCATION = 44;
    /** 服务被禁用，请确认是否开通高级权限 */
    static readonly FORWARD_SERVICE_FORBIDDEN = 45;
    /** 没有携带token */
    static readonly FORWARD_TOKEN_NULL = 104;
    /** 携带的token不对，sdkproxy解析不出ak和mcode */
    static readonly FORWARD_TOKEN_ERROR = 105;
    /** Token过期 */
    static readonly FORWARD_TOKEN_EXPIRE = 106;
    /** 签名不存在，没有携带sign参数 */
    static readonly FORWARD_SIGN_NULL = 107;
    /** 签名有误，携带的sign参数sdkproxy计算不一致 */
    static readonly FORWARD_SIGN_ERROR = 108;
    /** APP不存在，根据请求的ak，找不到对应的APP */
    static readonly FORWARD_AK_ERROR = 200;
    /** APP Mcode码校验失败,服务器能解析到mcode，但和数据库中不一致，请携带正确的 */
    static readonly FORWARD_MCODE_ERROR = 230;
}
