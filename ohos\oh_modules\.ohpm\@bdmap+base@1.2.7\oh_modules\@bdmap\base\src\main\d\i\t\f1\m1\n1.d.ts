import { PreferencesManager } from "../j1"; import { RuntimeDataManager } from "../k1"; import { Context } from "@kit.AbilityKit"; import { IInfoManager } from "./l1"; export declare abstract class BaseInfoManager<T> implements IInfoManager<T> { protected abstract _preferencesManager: PreferencesManager<T>; protected abstract _runtimeDataManager: RuntimeDataManager<T>; constructor(); updateField(field: keyof T, value: T[keyof T]): Promise<void>; getField(field: keyof T): T[keyof T] | null; fetchField(field: keyof T, context?: Context): Promise<T[keyof T]>; init(context?: Context | undefined): Promise<void>;         update(value: Partial<T>): Promise<void>;       get(): T | null;         fetch(context?: Context): Promise<T>; } 