// @keepTs
// @ts-nocheck
/**
 * Created on 2024/10/17
 * <AUTHOR>
 */
import { UserInfoModel } from '../../../../user/model/UserInfoModel';
import { GroupInfoModel } from '../../../../user/model/GroupInfoModel';
import { GroupMemberInfoModel } from '../../../../user/model/GroupMemberInfoModel';
/**
 * 更新是否在数据库中缓存用户信息
 */
export declare const updateIsCacheUserInfoInDB: (val: boolean) => boolean;
/**
 * 更新是否在数据库中缓存群组信息
 */
export declare const updateIsGroupInfoInDB: (val: boolean) => boolean;
/**
 * 更新是否在数据库中缓存群组成员信息
 */
export declare const updateIsGroupMemberInfoInDB: (val: boolean) => boolean;
export declare function taskPoolExecuteQueryUserInfo(u331: string): Promise<UserInfoModel | undefined>;
export declare function taskPoolExecuteUpdateUserInfo(o331: UserInfoModel): Promise<void>;
export declare function taskPoolExecuteQueryGroupInfo(d331: string): Promise<GroupInfoModel | undefined>;
export declare function taskPoolExecuteUpdateGroupInfo(x330: GroupInfoModel): Promise<void>;
export declare function taskPoolExecuteQueryGroupMemberInfo(i330: string, j330: string): Promise<GroupMemberInfoModel | undefined>;
export declare function taskPoolExecuteUpdateGroupMemberInfo(c330: GroupMemberInfoModel): Promise<void>;
