/**
 * 路线检索短串分享的请求参数
 */
import { PlanNode } from "../e/h/f1";
/**
 * 路线检索短串分享的请求参数
 */
export interface RouteShareURLOption {
    /**
     * 起点
     */
    from: PlanNode;
    /**
     * 终点
     */
    to: PlanNode;
    /**
     * 要分享检索路线中的哪条
     */
    pn: number;
    /**
     * 城市ID,公交检索需要传入cityID
     */
    cityCode: number;
    routeShareMode: RouteShareMode;
}
export declare enum RouteShareMode {
    /**
     * 驾车短串分享
     */
    CAR_ROUTE_SHARE_MODE = 0,
    /**
     * 步行短串分享
     */
    FOOT_ROUTE_SHARE_MODE = 1,
    /**
     * 骑行短串分享
     */
    CYCLE_ROUTE_SHARE_MODE = 2,
    /**
     * 公交短串分享
     */
    BUS_ROUTE_SHARE_MODE = 3
}
