// @keepTs
// @ts-nocheck
/**
 * Created on 2024/10/30
 * <AUTHOR>
 */
import { FileDownloadType } from "../enum/FileDownloadType";
/**
 * 文件预览携带参数的类型
 */
export interface FileDownloadInfo {
    /**
     * 文件下载类型。
     *
     * 注意：
     * 1，FileDownloadType#MEDIA_MESSAGE，读取 messageId 进行文件下载；
     * 2，FileDownloadType#MEDIA_MESSAGE，读取 fileUrl + fileName + fileSize + fileType 进行文件下载；
     */
    fileDownloadType: FileDownloadType;
    /**
     * 原始类型消息的 messageId。包括文件类型消息、引用类型消息（引用了文件消息）。
     */
    messageId: number;
    /**
     * 媒体的远端地址
     */
    fileUrl: string;
    /**
     * 本地文件名
     */
    fileName: string;
    /**
     * 文件大小
     */
    fileSize: string;
    /**
     * 文件类型
     */
    fileType: string;
}
