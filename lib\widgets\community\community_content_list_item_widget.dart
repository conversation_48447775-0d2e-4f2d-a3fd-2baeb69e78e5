import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_uikit/flutter_uikit.dart';
import 'package:wuling_flutter_app/models/global_data.dart';
import 'package:wuling_flutter_app/page/community/img_text_post_details_page.dart';
import 'package:wuling_flutter_app/page/community/img_text_video_post_detail_page.dart';
import 'package:wuling_flutter_app/page/community/topic_post_details_page.dart';
import 'package:wuling_flutter_app/page/community/user_info_page.dart';
import 'package:wuling_flutter_app/widgets/common/image_widget.dart';

import '../../api/community_api.dart';
import '../../models/community/recommend_content_list.dart';
import '../../utils/manager/log_manager.dart';
import '../../utils/show_login_dialog.dart';

class CommunityContentListItemWidget extends StatefulWidget {
  const CommunityContentListItemWidget({super.key, required this.postWebVo,
    required this.itemH,required this.crossAxisCount});
  final PostWebVo postWebVo;
  final double itemH;
  final int crossAxisCount;

  @override
  State<CommunityContentListItemWidget> createState() => _CommunityContentListItemWidgetState();
}

class _CommunityContentListItemWidgetState extends State<CommunityContentListItemWidget> {

  bool isPraise = false;
  int praiseCount = 0;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    if(widget.postWebVo.isPraise == 1) {
      isPraise = true;
    }else {
      isPraise = false;
    }
    praiseCount = widget.postWebVo.postPraiseCount ?? 0;
  }

  @override
  Widget build(BuildContext context) {

    return GestureDetector(
      onTap: () {
        Navigator.of(context).push(MaterialPageRoute(builder: (context) {
          LogManager().debug("postTypeId: ${widget.postWebVo.postTypeId}");
          if(widget.postWebVo!.postTypeId == 0 || widget.postWebVo!.postTypeId == 6 || widget.postWebVo!.postTypeId == 5) {
            return ImgTextVideoPostDetailPage(postId: widget.postWebVo!.postId!,postTypeId: widget.postWebVo!.postTypeId!);
          }else {
            return TopicPostDetailsPage(postId: widget.postWebVo!.postId!,postTypeId: widget.postWebVo!.postTypeId!);
          }
        }));
      },
      child: SizedBox(
        height: widget.itemH+80,
        width: double.infinity,
        child: Card(
          elevation: 2.0,
          clipBehavior: Clip.antiAlias,
          color: Colors.white,
          shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10)
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Expanded(
                  child: ImageWidget(width: double.infinity, height: widget.itemH,url: "${widget.postWebVo?.firstImg?.img}")
              ),
              Padding(
                padding: const EdgeInsets.all(10),
                child: RichText(
                    softWrap: true,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    text: TextSpan(
                        children: <InlineSpan>[
                          WidgetSpan(child: _recommend("荐",const Color(0xFFF7F0EF),const Color(0xFFBC887A))),
                          WidgetSpan(child: _recommend("精",const Color(0xFFF7CFDE),const Color(0xFFDF555B))),
                          TextSpan(text: widget.postWebVo!.postTitle,style: const TextStyle(
                              color: Colors.black,
                              fontSize: 13
                          ))
                        ]
                    )),
              ),
              _author(context)
            ],
          ),
        ),
      ),
    );
  }

  Widget _recommend(String str,Color bgColor,Color fontColor) {
    return Container(
      margin: const EdgeInsets.only(right: 6),
      alignment: Alignment.center,
      width: 16,
      height: 16,
      decoration: BoxDecoration(
          color: bgColor,
          borderRadius: const BorderRadius.all(Radius.circular(8))
      ),
      child: Text(str,style: TextStyle(
          fontSize: 10,
          color: fontColor
      ),),
    );
  }

  Widget _author(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(left: 5,bottom: 5,right: 5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
              child: GestureDetector(
                onTap: () {
                  if(!GlobalData().isLogin) {
                    ShowLoginDialog().show();
                    return;
                  }
                  Navigator.of(context).push(MaterialPageRoute(builder: (context) {
                    return UserInfoPage(nickname: widget.postWebVo?.author?.nickname ?? "", userIdStr: widget.postWebVo?.author?.userIdStr ?? "");
                  }));
                },
                child: Row(
                  children: [
                    SizedBox(
                      width: 20,
                      height: 20,
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: CachedNetworkImage(imageUrl: widget.postWebVo?.author?.photo ?? "",
                          errorWidget: (context,url,error) => Image.asset("assets/images/profile_page/llb_default_avatar.png"),
                          fit: BoxFit.cover,),
                      ),
                    ),
                   Expanded(child:  Container(
                     margin: const EdgeInsets.only(left: 5),
                     child: Text("${widget.postWebVo?.author?.nickname}",style: const TextStyle(
                         fontSize: 10,
                         color: Colors.grey
                     ),maxLines: 1,overflow: TextOverflow.ellipsis,),
                   ))
                  ],
                ),
              )
          ),
          GestureDetector(
            onTap: () {
              if(!GlobalData().isLogin) {
                ShowLoginDialog().show();
                return;
              }
              if(!isPraise) {
                _praise(widget.postWebVo.postId ?? 0);
              }
            },
            child:  Row(
              children: [
                const SizedBox(width: 4,),
                Image.asset(isPraise ? "assets/images/community/praise_sel.png" : "assets/images/community/praise.png",width: 10,),
                const SizedBox(width: 4,),
                Text("$praiseCount",style: const TextStyle(
                    color: Colors.grey,
                    fontSize: 10
                ),)
              ],
            ),
          )
        ],
      ),
    );
  }

  void _praise(int postId ) async {
    await communityAPI.praisedPost(postId.toString());
    setState(() {
      isPraise = true;
      ++praiseCount;
    });
  }


}




