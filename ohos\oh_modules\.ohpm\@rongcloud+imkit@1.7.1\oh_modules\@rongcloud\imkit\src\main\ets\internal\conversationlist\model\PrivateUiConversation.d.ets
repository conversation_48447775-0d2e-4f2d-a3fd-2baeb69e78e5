// @keepTs
// @ts-nocheck
/**
 * Created on 2024/08/06
 * <AUTHOR>
 */
import { Conversation } from '@rongcloud/imlib';
import { BaseUiConversation } from '../../../conversationlist/model/BaseUiConversation';
import { image } from '@kit.ImageKit';
import { UserInfoModel } from '../../../user/model/UserInfoModel';
import { GroupInfoModel } from '../../../user/model/GroupInfoModel';
import { GroupMemberInfoModel } from '../../../user/model/GroupMemberInfoModel';
@Observed
export declare class PrivateUiConversation extends BaseUiConversation {
    imagePixelMap: image.PixelMap | undefined;
    onUserInfoUpdate(u279: UserInfoModel): void;
    onGroupInfoUpdate(t279: GroupInfoModel): void;
    onGroupMemberUpdate(s279: GroupMemberInfoModel): void;
    onConversationUpdate(x278: Conversation, y278: boolean): void;
    /**
     * 获取图片
     * @param resource
     * @returns
     */
    private getPixmapFromMedia;
    /**
     * 设置语音消息样式
     * @param lastMessage
     * @param value
     */
    private setVoiceMutableStyledString;
}
