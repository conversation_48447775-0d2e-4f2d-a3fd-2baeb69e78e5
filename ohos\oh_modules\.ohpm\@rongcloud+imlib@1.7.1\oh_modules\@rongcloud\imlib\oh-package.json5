{"name": "@rongcloud/imlib", "version": "1.7.1", "description": "融云 IM SDK for HarmonyOS", "author": "rongcloud", "homepage": "https://www.rongcloud.cn", "keywords": ["融云", "IM", "RongCloud"], "license": "MIT", "dependencies": {"librongimlib.so": "file:./src/main/cpp/types/librongimlib"}, "types": "Index.d.ts", "artifactType": "obfuscation", "metadata": {"byteCodeHar": true, "sourceRoots": ["./src/main"], "debug": false, "declarationEntry": [], "nativeDebugSymbol": true, "useNormalizedOHMUrl": true}, "compatibleSdkVersion": 12, "compatibleSdkType": "HarmonyOS", "obfuscated": true, "nativeComponents": [{"name": "librongimlib.so", "compatibleSdkVersion": 12, "compatibleSdkType": "HarmonyOS", "linkLibraries": []}]}