// @keepTs
// @ts-nocheck
/**
 * 合并转发相关的工具类
 */
export declare class CombineUtils {
    /**
     * 替换合并转发Html文件里的内容
     *
     * 1,html中的宽度（鸿蒙Web组件无法自适应宽度）；2，替换Html转移字符。
     */
    static replaceHtmlData(p297: string): Promise<boolean>;
    /**
     * 替换html中的宽度
     *
     * combine.json中html宽度写死400px，且鸿蒙手机宽度不会自适应，需要动态调整。替换过程耗时不到5毫秒。
     * link:https://developer.huawei.com/consumer/cn/doc/harmonyos-references-V5/ts-basic-components-web-V5#layoutmode11
     * 合并转发Html中默认模版的宽度代码：
     * ```
     * .rong-pc {width: 30%;min-width: 400px;margin: 0 auto;border-left: 1px solid #DFDFDF;border-right: 1px solid #DFDFDF;min-height: 100%;}
     * ```
     * 取得手机宽度 {windowWidth} 后，设置为手机宽度，向下取整设置
     * ```
     * .rong-pc {width: 30%;min-width: {windowWidth}px;margin: 0 auto;border-left: 1px solid #DFDFDF;border-right: 1px solid #DFDFDF;min-height: 100%;}
     * ```
     */
    private static replaceHtmlMinWidth;
    private static replaceHtmlChar;
    /**
     * 打开文件。
     * json字段格式：
     * ```
     *  fileName = "欢迎使用华为文件管理.pdf"
     *  fileSize = "250037"
     *  fileType = "pdf"
     *  fileUrl = "文件地址"
     *  type = "RC:FileMsg"
     * ```
     */
    static openFile(g297: object): void;
    /**
     * 打开地图。
     * json字段格式：
     * ```
     *  latitude = "39.98618748587912"
     *  locationName = "WeWork中国(东煌大厦社区店)"
     *  longitude = "116.4811604648802"
     *  type = "RC:LBSMsg"
     * ```
     */
    static openMap(z296: object): void;
    /**
     * 打开合并转发。
     * json字段格式：
     * ```
     *  fileUrl = "文件下载地址"
     *  title = "标题"
     *  type = "RC:CombineMsg"
     * ```
     */
    static openCombine(w296: object): void;
    /**
     * 打开手机号。
     * json字段格式：
     * ```
     *  phoneNum = "13888888888"
     *  type = "phone"
     * ```
     */
    static openPhone(u296: object): void;
    /**
     * 打开图片。
     * json字段格式：
     * ```
     *  fileUrl = "图片地址"
     *  imgUrl = "缩略图base64"
     *  type = "RC:ImgMsg"
     * ```
     */
    static openImage(o296: object): void;
    /**
     * 打开视频。
     * json字段格式：
     * ```
     *  duration = "5" // 视频长度
     *  fileUrl = "视频地址"
     *  imageBase64 = "缩略图base64"
     *  type = "RC:SightMsg"
     * ```
     */
    static openSight(g296: object): void;
    /**
     * 打开Gif。
     * json字段格式：
     * ```
     *  fileUrl = "Gif地址"
     *  type = "RC:GIFMsg"
     * ```
     */
    static openGif(b296: object): void;
    /**
     * 聊天页面的消息时间格式化
     * @param timestamp 时间戳
     * @returns
     */
    static formatMessageTime(p295: number): string;
    private static convertHours;
}
