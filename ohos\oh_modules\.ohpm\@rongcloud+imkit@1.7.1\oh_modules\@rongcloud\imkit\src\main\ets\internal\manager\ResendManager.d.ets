// @keepTs
// @ts-nocheck
import { ConversationIdentifier, EngineError, Message } from '../../../../../Index';
import { ArrayList } from '@kit.ArkTS';
import { ResendStatus } from '../../conversation/model/UiMessage';
/**
 * 消息重发管理类
 * @version 1.4.3
 */
declare class ResendManager {
    private static instance;
    private resendInterval;
    private maxResendTimeByFileMsg;
    private resendLinkedList;
    private resendHashMap;
    private isResending;
    private currentResendingTask;
    private status;
    private connectionStatusListener;
    private resendEventListeners;
    private constructor();
    static getInstance(): ResendManager;
    init(): void;
    addResendEventListener(g316: ResendEventListener): void;
    removeResendEventListener(f316: ResendEventListener): void;
    getResendEventListeners(): ArrayList<ResendEventListener>;
    /**
     * 添加需要重发的消息到重发队列
     */
    addResendMessage(c316: Message, d316: EngineError, e316: (isInResendTask: boolean) => void): void;
    /**
     * 添加需要重发的单聊已读回执消息到重发队列
     */
    addResendReadReceipt(z315: ConversationIdentifier, a316: number, b316: EngineError): void;
    /**
     * 添加需要重发的群聊已读回执消息列表到重发队列
     */
    addResendReadReceiptResponse(u315: ConversationIdentifier, v315: Array<Message>, w315: EngineError): void;
    removeResendMessages(r315: Message[]): void;
    removeResendMessage(p315: number): void;
    getMessageResendStatus(o315: number): ResendStatus;
    isErrorResultNeedResend(j315: EngineError, k315: number): boolean;
    private addResendTask;
    private beginResendTask;
    private loopResendMessage;
    private isAddResendTask;
    private isMessageAddResendTask;
    private isReadReceiptAddResendTask;
    private isErrorCodeNeedResend;
    private isFileMsgResendTimeOverLimit;
    private isSaveMessage;
    private isTaskResending;
    private currentResendingMessageId;
    private isMessageResending;
    private removeResendMessageTask;
    private removeAllResendMessage;
    private resendMessageImpl;
    private dispatchOnResendStart;
}
/**
 * 消息重发事件监听接口
 */
interface ResendEventListener {
    /**
     * 开始重发状态回调
     * @param messageId 重发的消息ID
     * @param resendStatus ResendStatus，重发状态
     */
    onResendStart?: (messageId: number, resendStatus: ResendStatus) => void;
}
export { ResendManager, ResendEventListener };
