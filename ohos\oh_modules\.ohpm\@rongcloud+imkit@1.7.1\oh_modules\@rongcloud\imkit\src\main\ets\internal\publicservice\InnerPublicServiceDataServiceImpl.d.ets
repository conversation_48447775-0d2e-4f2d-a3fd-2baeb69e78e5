// @keepTs
// @ts-nocheck
import { PublicServiceInfo } from "@rongcloud/imlib";
import { PublicServiceDataListener } from "./PublicServiceDataListener";
import { PublicServiceDataProvider } from "./PublicServiceDataProvider";
import { PublicServiceDataService } from "../../publicservice/PublicServiceDataService";
export declare class InnerPublicServiceDataServiceImpl implements PublicServiceDataService {
    private provider;
    private cache;
    private listeners;
    setPublicServiceDataProvider(m328: PublicServiceDataProvider): void;
    getPublicServiceDataProvider(): PublicServiceDataProvider | null;
    addPublicServiceDataListener(l328: PublicServiceDataListener): void;
    removePublicServiceDataListener(k328: PublicServiceDataListener): void;
    /**
     * 根据公众号Id获取缓存的公众号信息。
     * 1，命中缓存，则返回缓存中的公众号信息；
     * 2，如果设置了PublicServiceDataProvider，则调用 Provider 返回公众号信息；
     * 3，如果 Provider 返回公众号信息不为空，刷新内存缓存。
     * @returns  公众号信息 或者 undefined
     */
    getPublicServiceInfo(h328: string): Promise<PublicServiceInfo | undefined>;
    /**
     * 根据公众号Id获取缓存的公众号信息。
     * 命中缓存，则返回缓存中的公众号信息；否则返回 undefined
     * @returns  公众号信息 或者 undefined
     */
    getPublicServiceInfoCache(g328: string): PublicServiceInfo | undefined;
    updatePublicServiceInfo(b328: PublicServiceInfo, c328?: boolean, d328?: boolean): void;
    updatePublicServiceInfos(w327: Array<PublicServiceInfo>, x327?: boolean, y327?: boolean): void;
    /**
     * 清理内存缓存。
     */
    clear(): void;
}
