// @keepTs
// @ts-nocheck
import { NetworkType } from '@rongcloud/imlib/src/main/ets/engine/internal/InternalDefine';
/**
 * 系统状态监听。如网络、前后台
 */
declare class SystemMonitor {
    private isInit;
    private netConn;
    private listeners;
    private static instance;
    private constructor();
    static getInstance(): SystemMonitor;
    /**
     * 开始监听
     * @param listener 监听器
     */
    init(n355: Context): void;
    private registerAppStateChange;
    /**
     * 监听网络变化
     */
    private registerNet;
    private registerNetAvailable;
    private registerNetLost;
    private registerNetUnavailable;
    private notifyNet;
    private notifyApplicationState;
    addMonitor(r354: MonitorListener): void;
    removeMonitor(q354: MonitorListener): void;
    networkAvailability(): boolean;
}
/**
 * 网络变化监听
 */
interface MonitorListener {
    /**
     * 网络发生变化
     * @param type 网络类型
     */
    networkChanged: (type: NetworkType) => void;
    /**
     * 前后台发生变化
     * @param foreground true：前台；false：后台
     */
    applicationStateChanged: (foreground: boolean) => void;
}
export { SystemMonitor, MonitorListener, NetworkType };
