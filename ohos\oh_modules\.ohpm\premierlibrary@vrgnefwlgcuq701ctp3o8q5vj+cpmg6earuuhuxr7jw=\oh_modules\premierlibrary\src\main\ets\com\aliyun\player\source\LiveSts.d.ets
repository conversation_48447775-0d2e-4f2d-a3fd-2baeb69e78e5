import { SourceBase } from "./SourceBase";
export declare class LiveSts extends SourceBase {
    private mAccessKeyId;
    private mAccessKeySecret;
    private mSecurityToken;
    private mRegion;
    private mUrl;
    private mDomain;
    private mApp;
    private mStream;
    private mEncryptionType;
    constructor();
    protected nativeGetEncryptionTypeValue: Function;
    protected nativeSetEncryptionType: Function;
    protected nativeGetSecurityToken: Function;
    protected nativeSetSecurityToken: Function;
    protected nativeGetAccessKeyId: Function;
    protected nativeSetAccessKeyId: Function;
    protected nativeGetAccessKeySecret: Function;
    protected nativeSetAccessKeySecret: Function;
    protected nativeGetRegion: Function;
    protected nativeSetRegion: Function;
    protected nativeGetUrl: Function;
    protected nativeSetUrl: Function;
    protected nativeGetDomain: Function;
    protected nativeSetDomain: Function;
    protected nativeGetApp: Function;
    protected nativeSetApp: Function;
    protected nativeGetStream: Function;
    protected nativeSetStream: Function;
    /**
     * 获取直播加密类型。
     *
     * @return encryptionType 参见 {@linkplain LiveEncryptionType}.
     */
    getEncryptionTypeValue(): number;
    /**
     * 设置直播加密类型。需要正确配置，否则可能出现播放不了的问题。
     *
     * @param encryptionType 参见 {@linkplain LiveEncryptionType}.
     */
    setEncryptionType(y33: LiveEncryptionType): void;
    /**
     * 获取安全token
     *
     * @return 安全token
     */
    /****
     * Query the token.
     *
     * @return The token.
     */
    getSecurityToken(): string;
    /**
     * 设置安全token
     *
     * @param mSecurityToken 安全token
     */
    /****
     * Set a token.
     *
     * @param mSecurityToken The specified token.
     */
    setSecurityToken(x33: string): void;
    /**
     * 获取鉴权id
     *
     * @return 鉴权id
     */
    /****
     * Query the AccessKey ID for authentication.
     *
     * @return The AccessKey ID for authentication.
     */
    getAccessKeyId(): string;
    /**
     * 设置鉴权id
     *
     * @param mAccessKeyId 鉴权id
     */
    /****
     * Set the AccessKey ID for authentication.
     *
     * @param mAccessKeyId The AccessKey ID for authentication
     */
    setAccessKeyId(w33: string): void;
    /**
     * 获取鉴权秘钥
     *
     * @return 鉴权秘钥
     */
    /****
     * Query the AccessKey Secret for authentication.
     *
     * @return The AccessKey Secret for authentication.
     */
    getAccessKeySecret(): string;
    /**
     * 设置鉴权秘钥
     *
     * @param mAccessKeySecret 鉴权秘钥
     */
    /****
     * Set the AccessKey Secret for authentication.
     *
     * @param mAccessKeySecret The AccessKey Secret for authentication.
     */
    setAccessKeySecret(v33: string): void;
    /**
     * 获取地域
     *
     * @return 地域
     */
    /****
     * Query region information.
     *
     * @return The region information.
     */
    getRegion(): string;
    /**
     * 设置地域
     *
     * @param mRegion 地域
     */
    /****
     * Specify regions.
     *
     * @param mRegion The specified regions.
     */
    setRegion(u33: string): void;
    /**
     * 获取设置的播放地址
     *
     * @return 播放地址
     */
    /****
     * Query the playback URL.
     *
     * @return The playback URL.
     */
    getUrl(): string;
    /**
     * 设置播放的地址
     *
     * @param mUri 本地或网络地址
     */
    /****
     * Set the playback URL.
     *
     * @param mUri The playback URL: a local address or a URL.
     */
    setUrl(t33: string): void;
    getDomain(): string;
    setDomain(s33: string): void;
    getApp(): string;
    setApp(r33: string): string;
    getStream(): string;
    setStream(q33: string): void;
}
export declare enum LiveEncryptionType {
    /**
     * 不加密直播流
     */
    NoEncryption = 0,
    /**
     * 阿里私有加密直播流
     */
    AliEncryption = 1,
    /**
     * WideVine或者FairPlay加密流
     */
    WideVine_FairPlay = 2
}
