{"meta": {"stableOrder": true, "enableUnifiedLockfile": false}, "lockfileVersion": 3, "ATTENTION": "THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.", "specifiers": {"@charles/amrnbconverter@^1.0.1": "@charles/amrnbconverter@1.0.1", "@free/global@1.0.3": "@free/global@1.0.3", "@ohos/flutter_ohos@har/flutter.har": "@ohos/flutter_ohos@har/flutter.har", "@ohos/hypium@1.0.18": "@ohos/hypium@1.0.18", "@ohos/mqtt@^2.0.22": "@ohos/mqtt@2.0.23", "@rongcloud/imkit@^1.5.0": "@rongcloud/imkit@1.7.1", "@rongcloud/imlib@1.7.1": "@rongcloud/imlib@1.7.1", "@rongcloud/imlib@^1.5.0": "@rongcloud/imlib@1.7.1", "@tencent/wechat_open_sdk@1.0.14": "@tencent/wechat_open_sdk@1.0.14", "libamrconverter.so@oh_modules/.ohpm/@charles+amrnbconverter@1.0.1/oh_modules/@charles/amrnbconverter/src/main/cpp/types/libamrconverter": "libamrconverter.so@oh_modules/.ohpm/@charles+amrnbconverter@1.0.1/oh_modules/@charles/amrnbconverter/src/main/cpp/types/libamrconverter", "libmqttasync.so@oh_modules/.ohpm/@ohos+mqtt@2.0.23/oh_modules/@ohos/mqtt/src/main/cpp/types/libmqttasync": "libmqttasync.so@oh_modules/.ohpm/@ohos+mqtt@2.0.23/oh_modules/@ohos/mqtt/src/main/cpp/types/libmqttasync", "librongimlib.so@oh_modules/.ohpm/@rongcloud+imlib@1.7.1/oh_modules/@rongcloud/imlib/src/main/cpp/types/librongimlib": "librongimlib.so@oh_modules/.ohpm/@rongcloud+imlib@1.7.1/oh_modules/@rongcloud/imlib/src/main/cpp/types/librongimlib"}, "packages": {"@charles/amrnbconverter@1.0.1": {"name": "@charles/amrnbconverter", "version": "1.0.1", "integrity": "sha512-LIj24/k9BFUm9l7PTuvhTOnELax3GbtOAcJnXMQMCGCKmXmU6HZ/8IVok4je74kAyLYIVvvak2DzUOOdzWoaEw==", "resolved": "https://ohpm.openharmony.cn/ohpm/@charles/amrnbconverter/-/amrnbconverter-1.0.1.har", "registryType": "ohpm", "dependencies": {"libamrconverter.so": "file:./src/main/cpp/types/libamrconverter"}}, "@free/global@1.0.3": {"name": "@free/global", "version": "1.0.3", "integrity": "sha512-WBrNWmjFkDhQP4ZfegyFBm5VC5CXmH2tfRMfBG9HtyO5v0t0t80c3cUMNm7Uq9ahYV9jpPt5BTN2IMpoB4wM9w==", "resolved": "https://ohpm.openharmony.cn/ohpm/@free/global/-/global-1.0.3.har", "registryType": "ohpm"}, "@ohos/flutter_ohos@har/flutter.har": {"name": "@ohos/flutter_ohos", "version": "1.0.0-a9e521ff88", "resolved": "har/flutter.har", "registryType": "local"}, "@ohos/hypium@1.0.18": {"name": "@ohos/hypium", "version": "1.0.18", "integrity": "sha512-RGe/iLGdeywdQilMWZsHKUoiE9OJ+9QxQsorF92R2ImLNVHVhbpSePNITGpW7TnvLgOIP/jscOqfIOhk6X7XRQ==", "resolved": "https://ohpm.openharmony.cn/ohpm/@ohos/hypium/-/hypium-1.0.18.har", "registryType": "ohpm"}, "@ohos/mqtt@2.0.23": {"name": "@ohos/mqtt", "version": "2.0.23", "integrity": "sha512-YtI31ruqc+UMU1k15KEbkPrVCmN2yigjL5a4lJrgBZKM9HObuZTu7n9N0ftoMZm1APoG3Cqt01fozb1SkN34Xg==", "resolved": "https://ohpm.openharmony.cn/ohpm/@ohos/mqtt/-/mqtt-2.0.23.har", "registryType": "ohpm", "dependencies": {"libmqttasync.so": "file:./src/main/cpp/types/libmqttasync"}}, "@rongcloud/imkit@1.7.1": {"name": "@rongcloud/imkit", "version": "1.7.1", "integrity": "sha512-Jr57bo+OInjWMnF6lKq6qbQm5ofdLoaHlwBJFnFO28606tUjMLYUoEzITEwEODenSyJIoUGHpRjwgGpGJqcNlA==", "resolved": "https://ohpm.openharmony.cn/ohpm/@rongcloud/imkit/-/imkit-1.7.1.har", "registryType": "ohpm", "dependencies": {"@rongcloud/imlib": "1.7.1", "@charles/amrnbconverter": "^1.0.1"}}, "@rongcloud/imlib@1.7.1": {"name": "@rongcloud/imlib", "version": "1.7.1", "integrity": "sha512-P4u6gS277VbqAy7mHyd1E7Dd1hTq6vgkNDhfFyVcbjS8OrCqLRRLEUsLfBMyk5CtodR2U7aSF4MyPCPh6BlIOA==", "resolved": "https://ohpm.openharmony.cn/ohpm/@rongcloud/imlib/-/imlib-1.7.1.har", "registryType": "ohpm", "dependencies": {"librongimlib.so": "file:./src/main/cpp/types/librongimlib"}}, "@tencent/wechat_open_sdk@1.0.14": {"name": "@tencent/wechat_open_sdk", "version": "1.0.14", "integrity": "sha512-qTA/XkKqBp5Bji6D4ePCnxdGpaDvzcqeI/vK6FRUwdBIlO9T/o0xuO1akCMCEB9c6qshIUAYqyRlHYmkRIkm4g==", "resolved": "https://ohpm.openharmony.cn/ohpm/@tencent/wechat_open_sdk/-/wechat_open_sdk-1.0.14.har", "registryType": "ohpm"}, "libamrconverter.so@oh_modules/.ohpm/@charles+amrnbconverter@1.0.1/oh_modules/@charles/amrnbconverter/src/main/cpp/types/libamrconverter": {"name": "libamrconverter.so", "version": "1.0.0", "resolved": "oh_modules/.ohpm/@charles+amrnbconverter@1.0.1/oh_modules/@charles/amrnbconverter/src/main/cpp/types/libamrconverter", "registryType": "local"}, "libmqttasync.so@oh_modules/.ohpm/@ohos+mqtt@2.0.23/oh_modules/@ohos/mqtt/src/main/cpp/types/libmqttasync": {"name": "libmqttasync.so", "version": "0.0.0", "resolved": "oh_modules/.ohpm/@ohos+mqtt@2.0.23/oh_modules/@ohos/mqtt/src/main/cpp/types/libmqttasync", "registryType": "local"}, "librongimlib.so@oh_modules/.ohpm/@rongcloud+imlib@1.7.1/oh_modules/@rongcloud/imlib/src/main/cpp/types/librongimlib": {"name": "librongimlib.so", "version": "1.7.1", "resolved": "oh_modules/.ohpm/@rongcloud+imlib@1.7.1/oh_modules/@rongcloud/imlib/src/main/cpp/types/librongimlib", "registryType": "local"}}}