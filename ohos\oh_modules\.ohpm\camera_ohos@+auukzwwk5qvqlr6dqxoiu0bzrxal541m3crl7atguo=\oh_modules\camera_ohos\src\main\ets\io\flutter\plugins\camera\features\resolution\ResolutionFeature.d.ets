import camera from '@ohos.multimedia.camera';
import { CameraFeature } from '../CameraFeature';
import { ResolutionPreset } from './ResolutionPreset';
import { CameraProperties } from '../../CameraProperties';
export declare class ResolutionFeature extends CameraFeature<ResolutionPreset> {
    private captureSize;
    private previewSize;
    private currentSetting;
    private cameraId;
    constructor(cameraProperties: CameraProperties, resolutionPreset: ResolutionPreset, cameraName: string);
    getPreviewSize(): camera.Size;
    getCaptureSize(): camera.Size;
    getDebugName(): string;
    setValue(value: ResolutionPreset): void;
    getValue(): ResolutionPreset;
    checkIsSupported(): boolean;
}
