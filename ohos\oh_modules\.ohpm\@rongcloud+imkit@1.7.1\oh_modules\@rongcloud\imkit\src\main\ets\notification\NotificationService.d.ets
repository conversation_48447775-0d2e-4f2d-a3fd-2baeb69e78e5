// @keepTs
// @ts-nocheck
import { NotificationInterceptor } from './listener/NotificationInterceptor';
/**
 * 本地通知管理
 * @version 1.0.0
 */
export interface NotificationService {
    /**
     * 增加本地通知拦截器
     * @param listener 监听
     */
    setNotificationInterceptor(interceptor: NotificationInterceptor): void;
    /**
     * 移除本地通知拦截器
     * @param listener 监听
     * @discussion 配合 setNotificationInterceptor 使用，否则会出现内存泄露
     */
    removeNotificationInterceptor(interceptor: NotificationInterceptor): void;
}
