// @keepTs
// @ts-nocheck
/**
 * Created on 2024/08/26
 * <AUTHOR>
 */
import { ConversationIdentifier, ConversationStatusInfo, ISetConversationTopOption, PushNotificationLevel } from '@rongcloud/imlib';
import List from '@ohos.util.List';
import { BaseUiConversation } from '../model/BaseUiConversation';
/**
 * 会话列表事件监听
 * # 说明
 *```
 * 该监听是属于 IMKit 的监听，所以必须是调用 ConversationListService 的方法才会该监听的对应方法
 * 如果 App 直接调用 IMLib 的方法，不会触发该监听
 *```
 * @version 1.0.0
 */
export interface ConversationListEventListener {
    /**
     * 当某个会话内产生保存草稿的行为时
     */
    onSaveDraft?: (identifier: ConversationIdentifier, content: string) => void;
    /**
     * 当某个会话清除未读数时
     */
    onClearedUnreadStatus?: (identifier: ConversationIdentifier) => void;
    /**
     * 当批量删除某些会话时
     */
    onRemoveConversation?: (identifierList: List<ConversationIdentifier>) => void;
    /**
     * 当其他端修改会话的免打扰和置顶状态时
     */
    onSyncConversationStatus?: (items: List<ConversationStatusInfo>) => void;
    /**
     * 当本端修改会话的置顶状态时
     */
    onConversationTopStatusChange?: (identifierList: List<ConversationIdentifier>, option: ISetConversationTopOption) => void;
    /**
     * 当本端修改会话的免打扰状态时
     */
    onConversationNotificationLevelChange?: (identifierList: List<ConversationIdentifier>, level: PushNotificationLevel) => void;
    /**
     * 长按会话列表中的 item 时执行。
     *
     * @param uiConversation 长按时的会话条目。
     * @warning 如果存在多个 Listener，只要有1个 Listener 实现了该方法且返回 true，SDK 则不再处理该事件。
     * @returns 是否处理该事件。实现该方法并且返回 true 代表 app 处理该点击事件，SDK 不再处理该点击事件。不实现该方法或者返回 false，代表由 SDK 处理点击事件。
     * @version 1.4.3
     */
    onConversationLongClick?: (uiConversation: BaseUiConversation) => boolean;
    /**
     * 点击会话列表中的 item 时执行。
     *
     * @param uiConversation 会话条目。
     * @warning 优先执行 ConversationListComponent 传入的 onConversationItemClick ，没传则执行此接口逻辑。
     * @warning 如果存在多个 Listener，只要有1个 Listener 实现了该方法且返回 true，SDK 则不再处理该事件。
     * @returns 是否处理该事件。实现该方法并且返回 true 代表 app 处理该点击事件，SDK 不再处理该点击事件。不实现该方法或者返回 false，代表由 SDK 处理点击事件。
     * @version 1.4.3
     */
    onConversationClick?: (uiConversation: BaseUiConversation) => boolean;
}
