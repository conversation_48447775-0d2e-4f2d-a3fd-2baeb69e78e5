import Bounds from "../../../ts/bounds"; import LatLng from "../../../ts/latlng"; import Point from "../../../ts/point"; export interface NapiCvBundle { map_bound?: mapBound; type?: string; poly_line?: point_array[]; } export interface NapiSignRes { sign?: string; } export interface NapiUrlParamsRes { urlParmsValue?: string; } export interface point_array { point_array: Point[]; } export interface mapBound { ll: Point;   ru: Point;   } export interface NapiProjectionPtRes { ProjectionPt?: string; } export function ll2mc(value: LatLng); export function wgsll2gcjll(value: LatLng): LatLng | null; export function wgsll2bdll(value: LatLng): LatLng | null; export function gcjll2bdll(value: LatLng): LatLng | null; export function bdll2gcjll(value: LatLng): LatLng | null; export function getDistanceByLL(r6: LatLng, p2: LatLng): number | null; export function createsign(sign: string): NapiSignRes | null; export function encodeUrlParamsValue(q6: string): NapiUrlParamsRes | null; export function transGeoStr2ComplexPt(p6: string): NapiCvBundle; export function transGeoStr2ComplexPtBound(o6: string): NapiCvBundle; export function getAESSaltKey(token: string): string | null; export function getAESViKey(token: string): string | null; export function getProjectionPt(n6: string): NapiProjectionPtRes | null; export function decryptPNKD(m6: string): null; 