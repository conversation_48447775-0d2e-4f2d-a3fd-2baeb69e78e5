export default class ComplexPt { static readonly TYPE_NONE: number; static readonly TYPE_POINT: number; static readonly TYPE_LINE: number; static readonly TYPE_POLYGON: number; type: number; vecPart: ArrayBuffer; vecXY: ArrayBuffer; mGeoPt: Array<number[]>; llX: number; llY: number; ruX: number; ruY: number; constructor(); static createByGeoString(p1: string): ComplexPt | null; static createByArrayNumber(k1: number[]): ComplexPt | null;             toNumberArray(): number[]; } 