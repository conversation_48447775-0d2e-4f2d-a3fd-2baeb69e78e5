import 'dart:convert';
import 'dart:async';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_uikit/flutter_uikit.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wuling_flutter_app/api/store/store_api.dart';
import 'package:wuling_flutter_app/api/store/store_cart_api.dart';
import 'package:wuling_flutter_app/common/action.dart';
import 'package:wuling_flutter_app/constant/constant.dart';
import 'package:wuling_flutter_app/page/store/store_bottom.dart';
import 'package:wuling_flutter_app/page/store/store_navbar.dart';
import 'package:wuling_flutter_app/plugins/share_plugin/share_plugin.dart';
import 'package:wuling_flutter_app/utils/manager/notification_manager.dart';
import 'package:wuling_flutter_app/widgets/common/custom_refresher.dart';
import 'package:wuling_flutter_app/widgets/webview/webview.dart';

import '../../../constant/service_constant.dart';
import '../../../constant/web_view_url_tool.dart';
import '../../../models/global_data.dart';
import '../../../models/store/store_detail/store_detail_model.dart';
import '../../../models/store/store_detail/store_goods_sku_model.dart';
import '../../../models/store/store_item_model.dart';

import '../../../routes/jump_tool.dart';
import '../../../utils/manager/dialog_manager.dart';
import '../../../utils/manager/log_manager.dart';
import '../../../utils/manager/login_manager.dart';
import '../../../widgets/common/custom_dialog.dart';
import '../store_cart/store_cart_page.dart';
import '../store_item.dart';

class StoreDetailPage extends StatefulWidget {
  const StoreDetailPage(
      {super.key, required this.id, required this.code, this.shopId = 0});
  final int id;
  final int code;
  final int shopId;

  @override
  State<StoreDetailPage> createState() => _StoreDetailPageState();
}

class _StoreDetailPageState extends State<StoreDetailPage> {
  List<StoreItemModel> dataSource = [];
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  final ScrollController _scrollController = ScrollController();

  StoreDetailModel model = StoreDetailModel.fromJson({});

  int pageNo = 1;

  int tag = 0;

  StoreGoodsSKUModel selectSku = StoreGoodsSKUModel.fromJson({});

  Timer? _hideNoDataTimer;
  bool _isAtBottom = false;

  @override
  void initState() {
    _onRefresh();
    super.initState();
    // 延迟添加监听器，确保组件完全初始化
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollController.addListener(_scrollListener);
    });
  }

  void _onRefresh() async {
    // monitor network fetch
    pageNo = 1;
    model = await storeApi.storeGoodsDetail(
        {"commodityId": widget.id, "channelCode": widget.code});
    LogManager().debug('${model.commodityPublishStatus}');
    // 重置加载状态，允许用户再次上拉
    _refreshController.resetNoData();
    // 清除定时器
    _hideNoDataTimer?.cancel();
    _isAtBottom = false;
    // if failed,use refreshFailed()
    _refreshController.refreshCompleted();
    if (mounted) {
      setState(() {});
    }
  }

  void _onLoading() async {
    try {
      // 检查组件是否还在
      if (!mounted) return;

      // 模拟短暂延迟，给用户加载的反馈
      await Future.delayed(const Duration(milliseconds: 300));

      // 检查组件是否还在
      if (!mounted) return;

      // 添加触底回弹效果
      if (_scrollController.hasClients &&
          _scrollController.position.hasContentDimensions) {
        try {
          final maxExtent = _scrollController.position.maxScrollExtent;

          // 先向下滚动一点点制造回弹感
          await _scrollController.animateTo(
            maxExtent + 10, // 减小偏移量避免越界
            duration: const Duration(milliseconds: 150),
            curve: Curves.easeOut,
          );

          // 检查组件是否还在
          if (!mounted) return;

          // 然后回弹到正常位置
          await _scrollController.animateTo(
            maxExtent,
            duration: const Duration(milliseconds: 200),
            curve: Curves.elasticOut,
          );
        } catch (e) {
          // 滚动动画失败时静默处理
          LogManager().debug('滚动动画失败: $e');
        }
      }

      // 检查组件是否还在
      if (!mounted) return;

      // 显示"已经到底啦"提示
      _refreshController.loadNoData();
      _isAtBottom = true;

      // 2秒后自动隐藏提示
      _hideNoDataTimer?.cancel();
      _hideNoDataTimer = Timer(const Duration(seconds: 2), () {
        if (mounted && _isAtBottom) {
          _refreshController.resetNoData();
          _isAtBottom = false;
          if (mounted) {
            setState(() {});
          }
        }
      });
    } catch (e) {
      // 处理异常，确保加载状态正确
      LogManager().debug('_onLoading 异常: $e');
      if (mounted) {
        _refreshController.loadFailed();
      }
    }
  }

  double getHeaderOpacity() {
    if (_scrollController.offset < 0) {
      return 0.0;
    }
    if (_scrollController.offset > 80) {
      return 1.0;
    }

    double o = _scrollController.offset / 80;
    return o;
  }

  void showNotLoginAlertDialog(BuildContext context) {
    DialogManager().showCustomDialog(
      CustomDialog(
        content: "您还没有登录",
        contentFontSize: 18,
        contentColor: Colors.black,
        buttonHeight: 70,
        horizontalPadding: 20,
        buttons: [
          DialogButton(
              label: "继续逛逛",
              onPressed: () {},
              backgroundColor: Colors.white,
              textColor: const Color(0xFF9B9DA9)),
          DialogButton(
              label: "去登录",
              onPressed: () {
                LoginManager().showLoginModal();
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xffea0029)),
        ],
      ),
    );
  }

  String getAmount() {
    if (model.minPrice == "暂无售价" || model.maxPrice == "暂无售价" || double.parse(model.minPrice) >= 2000000 ||
        double.parse(model.maxPrice) >= 2000000) {
      return "暂无售价";
    } else {
      return model.minPrice == model.maxPrice
          ? model.minPrice
          : "${model.minPrice}-${model.maxPrice}";
    }
  }

  void _scrollListener() {
    if (!mounted) return;

    if (_scrollController.hasClients &&
        _scrollController.position.hasContentDimensions) {
      // 检测是否接近底部（距离底部20像素内）
      if (_scrollController.position.pixels >=
          _scrollController.position.maxScrollExtent - 20) {
        if (!_isAtBottom) {
          // 触底时的振动反馈（需要添加vibration包）
          // HapticFeedback.lightImpact();
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: model.commodityClassifyId == 0
          ? UIButton(
              onPressed: () {
                String url =
                    "${Constant.WEB_VIEW_BASE_URL}mall/newFinanceStaging.html?commodityId=${model.id}";
                if (widget.shopId > 0) {
                  url += "&shopId=${widget.shopId}";
                }
                if (selectSku.skuId > 0) {
                  url += "&skuId=${selectSku.skuId}";
                }
                NavigatorAction.init(context,
                    view: WebViewPage(
                      url: url,
                    ));
              },
              decoration: BoxDecoration(
                border: Border.all(color: Color(0xFFCCCCCC), width: 0.5),
                borderRadius: BorderRadius.all(Radius.circular(20)),
              ),
              radius: 20,
              width: 40,
              height: 40,
              buttonState: UIButtonState(
                  buttonType: UIButtonType.top,
                  imgStr: "assets/images/store/amount.png",
                  imageWidth: 20,
                  imageHeight: 20,
                  title: "金融",
                  spacing: 2,
                  fontSize: 8,
                  color: 0xFF333333))
          : Container(),
      bottomNavigationBar: BottomAppBar(
        elevation: 0.0,
        child: (model.commodityPublishStatus == 1) ? Container(
          color: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 15),
          height: 49,
          child: Row(
            children: [
              UIButton(
                  onPressed: () {
                    if (!GlobalData().isLogin) {
                      showNotLoginAlertDialog(context);
                      return;
                    }
                    String url = WebViewURLTool.kefuURLStrWithGroup(
                        KefuGroup.mm.value, '', '');
                    JumpTool().openWeb(context, url, true);
                  },
                  buttonState: UIButtonState(
                      buttonType: UIButtonType.top,
                      title: '客服',
                      color: 0xFF333333,
                      fontSize: 10,
                      imgStr:
                          "assets/images/profile_page/my_customer_service.png",
                      imageWidth: 25,
                      imageHeight: 25)),
              Expanded(
                  flex: 1,
                  child: UIButton(
                      onPressed: () {
                        if (!GlobalData().isLogin) {
                          showNotLoginAlertDialog(context);
                          return;
                        }
                        NavigatorAction.init(context,
                            view: const StoreCartPage());
                      },
                      buttonState: UIButtonState(
                        buttonType: UIButtonType.top,
                        title: '购物车',
                        color: 0xFF333333,
                        fontSize: 10,
                        imgStr: "assets/images/use_car_page/cart.png",
                        imageWidth: 25,
                        imageHeight: 25,
                      ))),
              (model.commodityClassifyId == 0 ||
                      model.commodityClassifyId == 3 ||
                      model.commodityClassifyId == 9)
                  ? (model.commodityClassifyId == 0?UIButton(
                onPressed: () {
                  if (!GlobalData().isLogin) {
                    showNotLoginAlertDialog(context);
                    return;
                  }
                  String url = WebViewURLTool
                      .newAskPriceOrAppointmentDriveWithPageAskPriceOrAppointmentDrive(
                      PageType.testDrive.value, model.id, 0);
                  JumpTool().openWeb(context, url, true);
                },
                margin: const EdgeInsets.only(right: 10),
                color: 0xFFFFEB3B,
                height: 40,
                width: 100,
                radius: 20,
                child: const UIText(
                  data: "预约试驾",
                  color: 0xFFFFFFFF,
                  fontSize: 13,
                ),
              ):Container(width: 100,margin: const EdgeInsets.only(right: 10),))
                  : UIButton(
                      onPressed: () {
                        if (!GlobalData().isLogin) {
                          showNotLoginAlertDialog(context);
                          return;
                        }
                        ShowAction.init(context)
                            .showSKU(widget.id, widget.code, model.name,
                                model.image, model.commodityClassifyId)
                            .then((value) {
                          if (value != null) {
                            setState(() {
                              selectSku = value['sku'];
                            });

                            // Capture the quantity before the API call
                            final selectedQuantity = value['quantity'] ?? 1;

                            // 加入购物车成功
                            storeCartApi.addCart({
                              "quantity": selectedQuantity,
                              "commoditySkuId": selectSku.skuId,
                              "commodityId": selectSku.id,
                              "commodityClassifyId":
                                  value['commodityClassifyId'],
                              "channelSourceId": widget.code,
                              "logisticsTakeMode": model.logisticsTakeMode,
                              "cartAdditions": null,
                            }).then((value) {
                              OverlayCreate.context = context;
                              UIOverlay.toast("添加购物车成功");

                              // 发送购物车数量变化通知
                              NotificationManager().postNotification(
                                  Constant.NOTIFICATION_CART_COUNT_CHANGED,
                                  userInfo: {
                                    'action': 'add',
                                    'quantity': selectedQuantity
                                  });
                            });
                          }
                        });
                      },
                      margin: const EdgeInsets.only(right: 10),
                      color: 0xFFFFEB3B,
                      height: 40,
                      width: 100,
                      radius: 20,
                      child: const UIText(
                        data: "加入购物车",
                        color: 0xFFFFFFFF,
                        fontSize: 13,
                      ),
                    ),
              UIButton(
                onPressed: () {
                  if (!GlobalData().isLogin) {
                    showNotLoginAlertDialog(context);
                    return;
                  }
                  ShowAction.init(context)
                      .showSKU(widget.id, widget.code, model.name, model.image,
                          model.commodityClassifyId)
                      .then((value) {
                    if (value != null) {
                      setState(() {
                        selectSku = value['sku'];
                      });
                      // 立即购买
                      Map<String, dynamic> skuData = {
                        "quantity": value['quantity'],
                        "commoditySkuId": selectSku.skuId,
                        "commodityId": selectSku.id,
                        "originalPrice": selectSku.originalPrice,
                        "logisticsTakeMode": model.logisticsTakeMode,
                        // "logisticsServiceDealerId": value['shopId'],
                        // "shopId": value['shopId']
                      };
                      if (model.commodityClassifyId == 0 ||
                          model.commodityClassifyId == 3) {
                        skuData.addAll({
                          "logisticsServiceDealerId": value['shopId'],
                          "shopId": value['shopId']
                        });
                      }
                      NavigatorAction.init(context,
                          view: WebViewPage(
                            url:
                                "${Constant.MALL_CENTER_WEB_VIEW_BASE_URL}Order/Confirm?skuData=${jsonEncode(skuData)}",
                          ));
                    }
                  });
                },
                color: 0xFFFF0000,
                height: 40,
                width: 100,
                radius: 20,
                child: const UIText(
                  data: "立即购买",
                  color: 0xFFFFFFFF,
                  fontSize: 13,
                ),
              ),
            ],
          ),
        ):Container(
          height: 49,
          color: Color(0xFFDDDDDD),
          child: Center(
            child: UIText(data:"已下架",),
          ),
        ),
      ),
      body: SmartRefresher(
          enablePullDown: true,
          enablePullUp: true,
          controller: _refreshController,
          onRefresh: _onRefresh,
          onLoading: _onLoading,
          header: AnimatedRefreshHeader(paddingTop: 20, headerHeight: 76),
          footer: AnimatedRefreshFooter(
            noDataText: "已经到底啦~",
          ),
          child: CustomScrollView(
            controller: _scrollController,
            physics: const BouncingScrollPhysics(),
            slivers: [
              SliverPersistentHeader(
                pinned: true,
                delegate: CustomSliverHeaderDelegate(
                    max: 500,
                    min: 44 + MediaQuery.of(context).padding.top,
                    buildView: (offset, content) {
                      return Stack(
                        children: [
                          UIImage(
                            imgStr: model.image,
                            fit: BoxFit.cover,
                            radius: 0,
                          ),
                          UINavbar(
                            decoration: BoxDecoration(
                                color: Color.fromRGBO(
                                    255, 255, 255, getHeaderOpacity())),
                            centerView: Opacity(
                              opacity: getHeaderOpacity(),
                              child: UIText(
                                data: "商品详情",
                              ),
                              // child: Row(
                              //   mainAxisAlignment: MainAxisAlignment.center,
                              //   crossAxisAlignment: CrossAxisAlignment.center,
                              //   children: ["商品","详情","评价","推荐"].map((e) => UIButton(
                              //     onPressed: (){
                              //     setState(() {
                              //       tag = ["商品","详情","评价","推荐"].indexOf(e);
                              //       _scrollController.animateTo(tag*150, duration: const Duration(milliseconds:300), curve: Curves.linear);
                              //     });
                              //   },padding: EdgeInsets.only(top: 5),color: 0x00,width: 50,child: Column(
                              //     mainAxisAlignment: MainAxisAlignment.center,
                              //     children: [
                              //       UIText(data: e,fontSize: 15),
                              //       Container(
                              //         margin: const EdgeInsets.only(top: 8),
                              //         decoration: BoxDecoration(
                              //             color: tag == ["商品","详情","评价","推荐"].indexOf(e) ? Colors.red : Colors.transparent,
                              //             borderRadius: BorderRadius.circular(2)
                              //         ),
                              //         width: 30,
                              //         height: 3,
                              //       )
                              //     ],
                              //   ),)).toList(),
                              // ),
                            ),
                            right: UIButton(
                              onPressed: () {
                                if (!GlobalData().isLogin) {
                                  showNotLoginAlertDialog(context);
                                  return;
                                }
                                ShowAction.init(context)
                                    .showShare()
                                    .then((value) {
                                  String path =
                                      "packages/wl/packageOrder/pages/wlMallDetail/wlMallDetail";
                                  if (model.shareActivityInfo != null) {
                                    path = model.shareActivityInfo!['miniPath'];
                                  }
                                  String? userIdStr =
                                      GlobalData().userModel?.userIdStr;
                                  if (value == "weixin") {
                                    SharePlugin.shareFriend(
                                            "$path?commodityId=${model.id}&shareUserIdStr=$userIdStr",
                                            title: model.name,
                                            description: model.name,
                                            userName: "gh_bd7a2c3f3483",
                                            cover: model.image)
                                        .then((value) {
                                      LogManager().debug(value);
                                    });
                                  } else if (value == "pyq") {
                                    SharePlugin.shareCommon(
                                      "$path?commodityId=${model.id}&shareUserIdStr=$userIdStr",
                                      title: model.name,
                                      description: model.name,
                                      cover: model.image,
                                      userName: "gh_bd7a2c3f3483",
                                    ).then((value) {
                                      LogManager().debug(value);
                                    });
                                  }
                                });
                              },
                              color: 0x00,
                              padding: EdgeInsets.symmetric(horizontal: 20),
                              child: UIImage(
                                imgStr: "assets/images/store/share.png",
                                width: 20,
                                height: 20,
                              ),
                            ),
                          )
                        ],
                      );
                    }),
              ),
              SliverToBoxAdapter(
                  child: Container(
                color: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        const UIText(
                          data: "￥",
                          color: 0xFFFF0000,
                          fontSize: 21,
                          padding: EdgeInsets.only(bottom: 2),
                        ),
                        UIText(
                          data: getAmount(),
                          color: 0xFFFF0000,
                          fontSize: 32,
                        ),
                      ],
                    ),
                    getAmount() == "暂无售价" ? UIText(data: "订金${model.depositStr}元",fontSize: 13,padding: EdgeInsets.symmetric(vertical: 10),):Container(),
                    (model.promotionActivity != null &&
                            model.promotionActivity!.activityLabelList
                                .isNotEmpty)
                        ? Row(
                            children: [
                              ...model.promotionActivity!.activityLabelList
                                  .map((e) => Container(
                                        decoration: BoxDecoration(
                                          color: Color(0xFFFAEAE9),
                                          borderRadius:
                                              BorderRadius.circular(2),
                                        ),
                                        padding: const EdgeInsets.only(
                                            left: 5,
                                            right: 5,
                                            top: 4,
                                            bottom: 2),
                                        child: UIText(
                                          data: e,
                                          fontSize: 10,
                                          color: 0xFFFF0000,
                                        ),
                                      ))
                                  .toList(),
                              Expanded(
                                flex: 1,
                                child: Container(),
                              ),
                              UIButton(
                                onPressed: () {
                                  if (!GlobalData().isLogin) {
                                    showNotLoginAlertDialog(context);
                                    return;
                                  }
                                  ShowAction.init(context)
                                      .showCoupon(widget.id, widget.code)
                                      .then((value) {
                                    if (value != null) {}
                                  });
                                },
                                color: 0xFFFF0000,
                                radius: 15,
                                padding: EdgeInsets.symmetric(
                                    horizontal: 10, vertical: 4),
                                buttonState:
                                    UIButtonState(title: "优惠", fontSize: 11),
                              )
                            ],
                          )
                        : Container(),
                    Row(
                      children: [
                        Container(
                          margin: const EdgeInsets.only(right: 8),
                          decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: BorderRadius.circular(2)),
                          child: UIText(
                            data: "平台",
                            color: 0xFFFFFFFF,
                            fontSize: 9,
                            padding: EdgeInsets.symmetric(
                                horizontal: 5, vertical: 1),
                          ),
                        ),
                        Expanded(
                          child: UIText(
                            data: model.name,
                            fontSize: 17,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                    UIText(
                      data: "专车定制",
                      fontSize: 11,
                      padding: EdgeInsets.only(top: 5),
                    ),
                    UIText(
                        data: "月销量：${model.sales} 笔",
                        fontSize: 11,
                        color: 0xFF333333,
                        padding: EdgeInsets.only(top: 5)),
                  ],
                ),
              )),
              SliverPadding(
                padding: EdgeInsets.only(top: 10),
                sliver: SliverToBoxAdapter(
                  child: GestureDetector(
                    onTap: () {
                      ShowAction.init(context)
                          .showSKU(widget.id, widget.code, model.name,
                              model.image, model.commodityClassifyId)
                          .then((value) {
                        if (value != null) {
                          setState(() {
                            selectSku = value['sku'];
                          });
                          Map<String, dynamic> skuData = {
                            "quantity": value['quantity'],
                            "commoditySkuId": selectSku.skuId,
                            "commodityId": selectSku.id,
                            "originalPrice": selectSku.originalPrice,
                            "commodityClassifyId": value['commodityClassifyId'],
                            "channelSourceId": widget.code,
                            "logisticsTakeMode": model.logisticsTakeMode,
                            "logisticsServiceDealerId": model.logisticsSendMode,
                            "cartAdditions": null,
                          };
                          // if(model.commodityClassifyId == 0 ||
                          //     model.commodityClassifyId == 3){
                          //   skuData.addAll({
                          //     "logisticsServiceDealerId": value['shopId'],
                          //     "shopId": value['shopId']
                          //   });
                          // }
                          // NavigatorAction.init(context,
                          //     view: WebViewPage(
                          //       url:
                          //           "${Constant.MALL_CENTER_WEB_VIEW_BASE_URL}Order/Confirm?skuData=$skuData",
                          //     ));
                        }
                      });
                    },
                    child: Container(
                      color: Colors.white,
                      padding:
                          EdgeInsets.symmetric(horizontal: 15, vertical: 15),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                              flex: 1,
                              child: UIText(
                                data: "选择",
                                fontSize: 15,
                              )),
                          UIText(
                            data: selectSku.name,
                            fontSize: 12,
                            color: 0xFF999999,
                          ),
                          Icon(Icons.chevron_right),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              SliverPadding(
                padding: EdgeInsets.only(top: 10),
                sliver: SliverToBoxAdapter(
                  child: Container(
                    color: Colors.white,
                    padding: EdgeInsets.symmetric(horizontal: 15, vertical: 15),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        UIText(
                          data: "服务",
                          fontSize: 15,
                        ),
                        UIText(
                          data: "七天无理由退货",
                          color: 0xFFFF0000,
                          fontSize: 15,
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              SliverPadding(
                padding: EdgeInsets.only(top: 10),
                sliver: SliverToBoxAdapter(
                  key: GlobalKey(),
                  child: Container(
                      color: Colors.white,
                      padding: EdgeInsets.symmetric(horizontal: 15),
                      child: model.detail['commodityDetailImage'] == null
                          ? null
                          : CachedNetworkImage(
                              imageUrl: model.detail['commodityDetailImage'],
                              fit: BoxFit.fitWidth,
                            )),
                ),
              ),
              // SliverPadding(padding: EdgeInsets.only(top:10,bottom: 10),sliver: SliverToBoxAdapter(
              //   child: Container(
              //     color: Colors.white,
              //     padding: EdgeInsets.symmetric(horizontal: 15,vertical: 15),
              //     child: Row(
              //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //       children: [
              //         UIText(data: "商品评价",fontSize: 15,),
              //         Icon(Icons.chevron_right),
              //       ],
              //     ),
              //   ),
              // ),),
              // SliverPadding(padding: const EdgeInsets.symmetric(horizontal: 15),sliver: SliverGrid(
              //     gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              //       crossAxisCount: 2,
              //       crossAxisSpacing: 15,
              //       mainAxisSpacing:15,
              //       childAspectRatio: 160/212,
              //     ),
              //     delegate: SliverChildBuilderDelegate(
              //           (BuildContext context, int index) {
              //         return StoreItem(model: dataSource[index],onClock: (m){
              //           NavigatorAction.init(context,view: StoreDetailPage(id: m.id,code: m.code,));
              //         },);
              //       },childCount: dataSource.length,
              //     )),),
            ],
          )),
    );
  }

  @override
  void dispose() {
    // 清理定时器
    _hideNoDataTimer?.cancel();
    // 安全移除监听器
    if (_scrollController.hasListeners) {
      _scrollController.removeListener(_scrollListener);
    }
    _refreshController.dispose();
    _scrollController.dispose();
    super.dispose();
  }
}
