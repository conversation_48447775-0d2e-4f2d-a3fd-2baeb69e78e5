import 'dart:convert';
import 'dart:async';
import 'dart:ffi';

import 'package:flutter/material.dart';
import 'package:flutter_uikit/flutter_uikit.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wuling_flutter_app/api/store/store_api.dart';
import 'package:wuling_flutter_app/common/action.dart';
import 'package:wuling_flutter_app/constant/constant.dart';
import 'package:wuling_flutter_app/models/global_data.dart';
import 'package:wuling_flutter_app/models/store/store_cart_list.dart';
import 'package:wuling_flutter_app/models/store/store_detail/store_config_model.dart';
import 'package:wuling_flutter_app/page/store/goods_list_page.dart';
import 'package:wuling_flutter_app/page/store/store_baojun/store_baojun_page.dart';
import 'package:wuling_flutter_app/page/store/store_cart/store_cart_page.dart';
import 'package:wuling_flutter_app/page/store/store_charge/store_charge_page.dart';
import 'package:wuling_flutter_app/page/store/store_custom/store_custom_page.dart';
import 'package:wuling_flutter_app/page/store/store_detail/store_detail_page.dart';
import 'package:wuling_flutter_app/page/store/store_focus/store_focus.dart';
import 'package:wuling_flutter_app/page/store/store_high/store_high_page.dart';
import 'package:wuling_flutter_app/page/store/store_item.dart';
import 'package:wuling_flutter_app/page/store/store_more/store_more_page.dart';
import 'package:wuling_flutter_app/page/store/store_navbar.dart';
import 'package:wuling_flutter_app/page/store/store_search/ui_search_view.dart';
import 'package:wuling_flutter_app/page/store/store_upkeep/store_upkeep_page.dart';
import 'package:wuling_flutter_app/page/store/store_wuling/store_wuling_page.dart';
import 'package:wuling_flutter_app/utils/manager/notification_manager.dart';
import 'package:wuling_flutter_app/utils/show_login_dialog.dart';
import 'package:wuling_flutter_app/widgets/common/custom_refresher.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

import '../../models/store/store_item_model.dart';
import '../../utils/manager/log_manager.dart';
import '../../widgets/webview/webview.dart';

class StorePage extends StatefulWidget {
  const StorePage({super.key});

  @override
  State<StorePage> createState() => _StorePageState();
}

class _StorePageState extends State<StorePage> with WidgetsBindingObserver {
  List<Map<String, dynamic>> iconList = [
    {"name": "宝骏专区", "img": "", "router": StoreHighPage()},
    {"name": "五菱专区", "img": "", "router": StoreHighPage()},
    {"name": "优品", "img": "", "router": StoreHighPage()},
    {"name": "Ling值商城", "img": "", "router": StoreUpkeepPage()},
    {"name": "保养", "img": "", "router": StoreUpkeepPage()},
    {"name": "充电产品", "img": "", "router": StoreChargePage()},
    {"name": "定制专区", "img": "", "router": StoreCustomPage()},
    {"name": "流量商城", "img": "", "router": StoreUpkeepPage()},
    {"name": "更多", "img": "", "router": StoreMorePage()},
    {"name": "订阅服务", "img": "", "router": StoreFocusPage()},
  ];

  List<StoreItemModel> dataSource = [];
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);
  int _pageNo = 1;
  int _waterfallId = 0;
  final int _channelCode = 0;

  List<StoreConfigModel> configList = [];
  List<StoreConfigElementModel> eleList = [];

  // 购物车商品数量
  int _cartCount = 0;

  // 购物车数量变化通知处理
  Timer? _cartCountTimer;

  @override
  void initState() {
    super.initState();
    // 添加页面生命周期监听
    WidgetsBinding.instance.addObserver(this);

    _onRefresh();
    _getCartCount(); // 初始化时获取购物车数量

    // 订阅登录状态变化通知
    NotificationManager().subscribe(
        Constant.NOTIFICATION_LOGIN_SUCCEED, _handleLoginStateChange);
    NotificationManager().subscribe(
        Constant.NOTIFICATION_LOGOUT_SUCCEED, _handleLoginStateChange);

    // 订阅购物车数量变化通知
    NotificationManager().subscribe(
        Constant.NOTIFICATION_CART_COUNT_CHANGED, _handleCartCountChanged);
  }

  @override
  void dispose() {
    // 移除页面生命周期监听
    WidgetsBinding.instance.removeObserver(this);

    // 取消通知订阅
    NotificationManager().unsubscribe(
        Constant.NOTIFICATION_LOGIN_SUCCEED, _handleLoginStateChange);
    NotificationManager().unsubscribe(
        Constant.NOTIFICATION_LOGOUT_SUCCEED, _handleLoginStateChange);
    NotificationManager().unsubscribe(
        Constant.NOTIFICATION_CART_COUNT_CHANGED, _handleCartCountChanged);

    _refreshController.dispose();
    super.dispose();
  }

  // 处理购物车数量变化通知
  void _handleCartCountChanged(CustomNotification notification) {
    // 获取通知中的操作信息
    final userInfo = notification.userInfo ?? {};
    final action = userInfo['action'] as String?;
    final quantity = userInfo['quantity'] as int? ?? 1;

    if (action != null && mounted) {
      setState(() {
        // 乐观更新：先更新UI，再请求真实数据
        switch (action) {
          case 'add':
            _cartCount += quantity;
            break;
          case 'remove':
            _cartCount = (_cartCount - quantity).clamp(0, 999);
            break;
          case 'batch_remove':
            _cartCount = (_cartCount - quantity).clamp(0, 999);
            break;
        }
      });
    }

    // 然后获取真实的购物车数量进行校正
    _getCartCount();
  }

  // 监听应用生命周期变化
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      // 当应用重新获得焦点时，刷新购物车数量
      _getCartCount();
    }
  }

  // Handle login/logout events
  void _handleLoginStateChange(CustomNotification notification) {
    // 登录状态变化时刷新页面和购物车数量
    _onRefresh();
    _getCartCount();
  }

  // 获取购物车商品数量（添加防抖机制）
  void _getCartCount() async {
    // 防抖机制，避免频繁请求
    _cartCountTimer?.cancel();
    _cartCountTimer = Timer(const Duration(milliseconds: 300), () async {
      final isLoggedIn = GlobalData().userModel != null;

      if (isLoggedIn) {
        try {
          final Map<String, dynamic> body = {"channelSourceId": 0};
          StoreCartList cartList = await storeApi.getCartList(body);

          if (mounted) {
            setState(() {
              _cartCount = cartList.count ?? 0;
            });
          }
        } catch (e) {
          LogManager().debug("获取购物车数量失败: $e");
          if (mounted) {
            setState(() {
              _cartCount = 0;
            });
          }
        }
      } else {
        // 用户未登录时重置购物车数量
        if (mounted) {
          setState(() {
            _cartCount = 0;
          });
        }
      }
    });
  }

  void _onRefresh() async {
    // monitor network fetch
    _refreshController.refreshCompleted();
    _pageNo = 1;
    configList = await storeApi.storeConfig("shoppingMall");

    for (var element in configList) {
      if (element.style == 21) {
        for (var element1 in element.elementVoList) {
          if (element1.waterfallVo.containsKey("waterfallId")) {
            _waterfallId = element1.waterfallVo['waterfallId'];
            LogManager().debug('$_waterfallId');
          }
        }
      }
      if (element.style == 20) {
        eleList = element.elementVoList;
      }
    }
    dataSource = await storeApi.postStoreList(body: {
      "pageNo": _pageNo,
      "pageSize": 20,
      "waterfallId": _waterfallId,
      "channelCode": _channelCode
    });

    if (mounted) {
      setState(() {});
    }
    // 刷新完成后更新购物车数量
    _getCartCount();
  }

  void _onLoading() async {
    _pageNo += 1;
    List<StoreItemModel> data = await storeApi.postStoreList(body: {
      "pageNo": _pageNo,
      "pageSize": 20,
      "waterfallId": _waterfallId,
      "channelCode": _channelCode
    });
    dataSource.addAll(data);
    setState(() => {});
    _refreshController.loadComplete();
  }

  void _checkLoginAndNavigateToCart() {
    // 检查用户是否已登录
    final isLoggedIn = GlobalData().userModel != null;

    if (isLoggedIn) {
      // 已登录，进入购物车页面，并在返回时刷新购物车数量
      NavigatorAction.init(context, view: const StoreCartPage()).then((_) {
        // 从购物车页面返回时刷新购物车数量
        _getCartCount();
      });
    } else {
      // 未登录，显示登录提示对话框
      ShowLoginDialog().show().then((_) {
        // 登录弹窗关闭后检查登录状态并刷新
        if (GlobalData().userModel != null) {
          _getCartCount();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    int crossAxisCount = 2;
    if (screenWidth > 1000) {
      crossAxisCount = 6;
    } else if (screenWidth > 600) {
      crossAxisCount = 4;
    }
    return Scaffold(
      appBar: UINavbar(
        title: UISearchView(
          readOnly: true,
          searchAction: (data) {},
          radius: 4,
          right: Padding(
            padding: const EdgeInsets.only(left: 50),
            child: Stack(
              clipBehavior: Clip.none,
              children: [
                UIButton(
                  margin: EdgeInsets.only(right: 15),
                  child: UIImage(
                    imgStr: "assets/images/use_car_page/cart.png",
                    width: 25,
                    height: 25,
                  ),
                  onPressed: () {
                    _checkLoginAndNavigateToCart();
                  },
                ),
                if (GlobalData().userModel != null && _cartCount > 0)
                  Positioned(
                    top: 5,
                    right: 5,
                    child: Container(
                      padding: EdgeInsets.all(3),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        shape: BoxShape.circle,
                      ),
                      constraints: BoxConstraints(
                        minWidth: 18,
                        minHeight: 18,
                      ),
                      child: Center(
                        child: Text(
                          _cartCount > 99 ? '99+' : '$_cartCount',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
      backgroundColor: Colors.white,
      body: SmartRefresher(
        enablePullDown: true,
        enablePullUp: true,
        controller: _refreshController,
        onRefresh: _onRefresh,
        onLoading: _onLoading,
        header: AnimatedRefreshHeader(),
        footer: AnimatedRefreshFooter(),
        child: CustomScrollView(
          slivers: [
            SliverPadding(
              padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
              sliver: SliverGrid(
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 5,
                    crossAxisSpacing: 5,
                    mainAxisSpacing: 5,
                  ),
                  delegate: SliverChildBuilderDelegate(
                    (BuildContext context, int index) {
                      return UIButton(
                        onPressed: () {
                          //print("item: ${jsonEncode(eleList[index])}");
                          if (eleList[index].linkType == 51) {
                            String linkUrl =
                                eleList[index].linkUrl.split(',').first;
                            Navigator.of(context)
                                .push(MaterialPageRoute(builder: (context) {
                              return GoodsListPage(id: int.parse(linkUrl));
                            }));
                          } else if (eleList[index].linkType == 52) {
                            String linkUrl =
                                eleList[index].linkUrl.split(',').first;
                            Navigator.of(context)
                                .push(MaterialPageRoute(builder: (context) {
                              return StoreBaoJunPage(id: int.parse(linkUrl));
                            }));
                          }
                          if (eleList[index].linkType == 53) {
                            String linkUrl =
                                eleList[index].linkUrl.split(',').first;
                            Navigator.of(context)
                                .push(MaterialPageRoute(builder: (context) {
                              return StoreWuLingPage(id: int.parse(linkUrl));
                            }));
                          } else if (eleList[index].linkType == 3) {
                            Navigator.of(context)
                                .push(MaterialPageRoute(builder: (context) {
                              return WebViewPage(url: eleList[index].linkUrl);
                            }));
                          }
                        },
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            UIImage(
                              imgStr: eleList[index].image,
                              width: 36,
                              height: 36,
                              margin: EdgeInsets.only(bottom: 5),
                            ),
                            UIText(
                              data: eleList[index].title,
                              color: 0xFF333333,
                              fontSize: 10,
                              maxLines: 1,
                              overflow: TextOverflow.clip,
                            )
                          ],
                        ),
                      );
                    },
                    childCount: eleList.length,
                  )),
            ),
            dataSource.isEmpty
                ? const SliverToBoxAdapter()
                : SliverPadding(
                    padding:
                        const EdgeInsets.only(left: 20, right: 20, bottom: 30),
                    sliver: SliverGrid(
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: crossAxisCount,
                        crossAxisSpacing: 15,
                        mainAxisSpacing: 15,
                        childAspectRatio: 160 / 260,
                      ),
                      delegate: SliverChildBuilderDelegate(
                        (BuildContext context, int index) {
                          return StoreItem(
                            model: dataSource[index],
                            onClock: (m) {
                              // 跳转到商品详情页，并在返回时刷新购物车数量
                              NavigatorAction.init(
                                context,
                                view: StoreDetailPage(
                                    id: m.id, code: _channelCode),
                              ).then((_) {
                                // 从商品详情页返回时刷新购物车数量
                                _getCartCount();
                              });
                            },
                          );
                        },
                        childCount: dataSource.length,
                      ),
                    ),
                  ),
          ],
        ),
      ),
    );
  }
}
