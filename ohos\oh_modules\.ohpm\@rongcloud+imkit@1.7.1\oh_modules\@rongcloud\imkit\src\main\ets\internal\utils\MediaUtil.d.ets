// @keepTs
// @ts-nocheck
import { image } from '@kit.ImageKit';
import { Message } from '@rongcloud/imlib';
export declare class MediaUtil {
    private static KILOBYTE;
    private static MEGABYTE;
    private static GIGABYTE;
    private static localMediaThumbnailInfoCache;
    static getUriFromPath(e344: string): string;
    /**
     * 获取视频信息
     *```
     *let result = await MySightUtil.getSightInfo(this.sightUri);
     *let base64 : string = result[0];
     *let duration : number = result[1]
     *```
     * @param path 视频地址
     * @returns 第一个数据是缩略图 base64 字符串，第二个是视频时长（单位秒）
     */
    static getSightInfo(g343: string): Promise<[
        string,
        number
    ]>;
    /**
     * 根据原图片地址，生成缩略图。
     * @param path 原图地址
     */
    static getImageThumbnail(o342: string): Promise<string>;
    static getFileName(l342: string): string;
    static fileLocalPath(g342: string, h342: string): Promise<string>;
    private static createFolder;
    /**
     * 格式化文件大小
     * @param size 文件大小，单位为字节
     * @returns 返回格式化后的字符串
     */
    static formatFileSize(c342: number): string;
    /**
     * 制作ImageMessage消息气泡的缩略图宽高
     * @param width 原图片宽
     * @param height 原图片高
     * @returns 计算后的缩略图宽高
     */
    static makeThumbnail(x341: number, y341: number): image.Size;
    /**
     * 构建媒体消息的宽高
     * @param message
     */
    static makeMediaMessageThumbnail(k341: Message): image.Size | undefined;
    /**
     * 图像数据 Base64字符串转为图像数据 PixelMap
     */
    private static base64ToPixelMap;
    private static getImageInfoSyncSafe;
}
