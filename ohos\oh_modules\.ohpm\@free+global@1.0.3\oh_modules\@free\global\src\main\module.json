{"app": {"bundleName": "com.example.myapplication", "debug": true, "versionCode": 1000000, "versionName": "1.0.0", "minAPIVersion": 50000012, "targetAPIVersion": 50000012, "apiReleaseType": "Beta5", "compileSdkVersion": "5.0.0.60", "compileSdkType": "HarmonyOS", "appEnvironments": [], "bundleType": "app"}, "module": {"name": "global", "type": "har", "deviceTypes": ["default", "tablet", "2in1"], "requestPermissions": [{"name": "ohos.permission.GYROSCOPE"}, {"name": "ohos.permission.ACCELEROMETER"}, {"name": "ohos.permission.CAMERA", "reason": "$string:camera_reason"}, {"name": "ohos.permission.PRIVACY_WINDOW", "reason": "$string:privacy_window_reason"}], "packageName": "@free/global", "installationFree": false, "virtualMachine": "ark12.0.2.0", "compileMode": "esmodule", "dependencies": []}}