import { CoordType } from "./i1"; export declare namespace DeviceMetadata { interface HardwareInfo { phoneType: string; screenWidth: number; screenHeight: number; densityDpi: number; cpuProcessor: string; glRenderer: string; glVersion: string; } interface SystemInfo { osVersion: string; apiLevel: string; safeOSVersion: string; netMode: string; } interface ApplicationInfo { packageName: string; softwareVersion: string; versionCode: number; channel: string; resourceId: string; } interface IdentityInfo { cuid: string; zid: string; authToken: string; appId: string; uid: string; } interface StorageInfo { moduleDirectory: string; secondCacheDirectory: string; } interface PrivacySettings { privacyMode: boolean; } interface Metadata { hardwareInfo: HardwareInfo; systemInfo: SystemInfo; applicationInfo: ApplicationInfo; identityInfo: IdentityInfo; storageInfo: StorageInfo; privacySettings: PrivacySettings; coordType: GlobalCoordType; }       interface GlobalCoordType { coordType: CoordType; } } 