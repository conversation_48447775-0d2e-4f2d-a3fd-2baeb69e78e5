// @keepTs
// @ts-nocheck
import { UserDataListener } from './listener/UserDataListener';
import { GroupInfoModel } from './model/GroupInfoModel';
import { GroupMemberInfoModel } from './model/GroupMemberInfoModel';
import { UserInfoModel } from './model/UserInfoModel';
import { UserDataProvider } from './provider/UserDataProvider';
/**
 * 用户信息服务
 * @version 1.0.0
 */
export interface UserDataService {
    /**
     * 设置用户信息提供者，当 sdk 需要展示用户信息时，如果 sdk 中没有对应的信息，就会调用 provider 的对应方法
     * @param provider 提供者
     */
    setUserDataProvider(provider: UserDataProvider): void;
    /**
     * 增加用户数据监听，当 sdk 的用户信息发生变化时触发
     * @param listener 监听
     * @warning addUserDataListener & removeUserDataListener 配合使用，避免内存泄露
     * @discussion 重复 add 同一个 Listener 对象，只有第一个 Listener 会被 add
     */
    addUserDataListener(listener: UserDataListener): void;
    /**
     * 移除用户数据监听
     * @param listener 监听
     * @warning addUserDataListener & removeUserDataListener 配合使用，避免内存泄露
     */
    removeUserDataListener(listener: UserDataListener): void;
    /**
     * 获取用户信息数据，优先返回 sdk 的内部数据。如果 sdk 没有对应的数据，会调用 provider 获取，然后触发 UserDataListener
     * @param userId 用户 Id
     * @returns 用户信息数据
     */
    getUserInfo(userId: string): Promise<UserInfoModel | undefined>;
    /**
     * 获取群组信息数据，优先返回 sdk 的内部数据。如果 sdk 没有对应的数据，会调用 provider 获取，然后触发 UserDataListener
     * @param groupId 群 Id
     * @returns 群组信息数据
     */
    getGroupInfo(groupId: string): Promise<GroupInfoModel | undefined>;
    /**
     * 获取群组成员信息数据，优先返回 sdk 的内部数据。如果 sdk 没有对应的数据，会调用 provider 获取，然后触发 UserDataListener
     * @param groupId 群 Id
     * @param userId 用户 Id
     * @returns 群组成员信息数据
     */
    getGroupMemberInfo(groupId: string, userId: string): Promise<GroupMemberInfoModel | undefined>;
    /**
     * 获取群成员信息数据列表，优先返回 sdk 的内部数据。如果 sdk 没有对应的数据，会调用 provider 获取，然后触发 UserDataListener
     * @param groupId 群 Id
     * @returns 群成员信息数据列表
     */
    getGroupMemberInfos(groupId: string): Promise<Array<GroupMemberInfoModel> | undefined>;
    /**
     * 更新用户信息数据，然后触发 UserDataListener
     * @param userInfo 用户信息数据
     * @warning 会完全覆盖 sdk 内的用户数据，请确保该对象的所有字段都是必要的数据
     */
    updateUserInfo(userInfo: UserInfoModel): void;
    /**
     * 更新群组信息数据，然后触发 UserDataListener
     * @param groupInfo 群组信息数据
     * @warning 会完全覆盖 sdk 内的用户数据，请确保该对象的所有字段都是必要的数据
     */
    updateGroupInfo(groupInfo: GroupInfoModel): void;
    /**
     * 更新群组成员信息数据，然后触发 UserDataListener
     * @param memberInfo 群组成员信息数据
     * @warning 会完全覆盖 sdk 内的用户数据，请确保该对象的所有字段都是必要的数据
     */
    updateGroupMemberInfo(memberInfo: GroupMemberInfoModel): void;
    /**
     * 设置消息体内是否携带用户信息。
     * 设置为 true 时， 需要调用 RongIM.getInstance().setCurrentUserInfo() 方法设置当前用户信息，
     * 这样可以在每条消息中携带当前用户的信息，IMKit会在接收到消息的时候取出用户信息并刷新到界面上。
     *
     * @param isAttached 是否携带用户信息，true 携带，false 不携带。
     */
    setMessageAttachedUserInfo(isAttached: boolean): void;
    /**
     * 设置当前用户信息。 如果开发者没有实现用户信息提供者，而是使用消息携带用户信息，需要使用这个方法设置当前用户的信息， 然后在{@link init()}之后调用{@link
     * RongIM.getInstance().userDataService.setMessageAttachedUserInfo(true)}，
     * 这样可以在每条消息中携带当前用户的信息，IMKit会在接收到消息的时候取出用户信息并刷新到界面上。
     *
     * @param userInfo 当前用户信息。
     */
    setCurrentUserInfo(userInfo?: UserInfoModel): void;
}
