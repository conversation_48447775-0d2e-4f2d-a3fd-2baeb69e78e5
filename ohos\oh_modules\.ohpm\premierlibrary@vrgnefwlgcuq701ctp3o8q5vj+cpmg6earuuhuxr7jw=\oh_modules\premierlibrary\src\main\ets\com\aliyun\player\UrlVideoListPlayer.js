import { AVPLBase } from './AVPLBase';
import { Log } from '../utils/Log';
import { UrlVideoPlayer } from './UrlVideoPlayer';
import { OhosUrlListPlayer } from './nativeclass/OhosUrlListPlayer';
export class UrlVideoListPlayer extends AVPLBase {
    constructor(n41, o41) {
        super(n41, o41);
        this.mLog = new Log("NativePlayerBase_UrlVideoListPlayer");
        this.mUrlVideoPlayer = null;
        this.mUrlPrerenderPlayer = null;
    }
    createListPlayer(j41, k41, l41, m41) {
        return new OhosUrlListPlayer(j41, l41, m41, false);
    }
    getNativePlayerWithContext(h41, i41) {
        if (this.mUrlVideoPlayer == null) {
            this.mUrlVideoPlayer = new UrlVideoPlayer(h41, i41);
        }
        return this.mUrlVideoPlayer;
    }
    getPrerenderPlayerWithContext(f41, g41) {
        if (this.mUrlPrerenderPlayer == null) {
            this.mUrlPrerenderPlayer = new UrlVideoPlayer(f41, g41);
        }
        return this.mUrlPrerenderPlayer;
    }
    getCurrentPlayerIndex() {
        let e41 = super.getCorePlayer();
        if (e41 instanceof OhosUrlListPlayer) {
            this.mLog.info("getCurrentPlayerIndex");
            return e41.getCurrentPlayerIndex();
        }
        return -1;
    }
    getPreRenderPlayer() {
        let c41 = super.getCorePlayer();
        if (c41 instanceof OhosUrlListPlayer) {
            this.mLog.info("getPreRenderPlayer");
            let d41 = c41.getPreRenderPlayerIndex();
            return this.getCurrentPrerenderPlayer(d41);
        }
        return undefined;
    }
    addUrl(z40, a41) {
        let b41 = super.getCorePlayer();
        if (b41 instanceof OhosUrlListPlayer) {
            this.mLog.info("addUrl = " + z40 + " , uid = " + a41);
            b41.addUrl(z40, a41);
        }
    }
    moveToNext() {
        let y40 = super.getCorePlayer();
        if (y40 instanceof OhosUrlListPlayer) {
            this.mLog.info("moveToNext");
            return y40.moveToNext(false);
        }
        return false;
    }
    moveToNextWithPrerendered() {
        let x40 = super.getCorePlayer();
        if (x40 instanceof OhosUrlListPlayer) {
            this.mLog.info("moveToNextWithPrerendered");
            return x40.moveToNext(true);
        }
        return false;
    }
    moveToPrev() {
        let w40 = super.getCorePlayer();
        if (w40 instanceof OhosUrlListPlayer) {
            this.mLog.info("moveToPrev");
            return w40.moveToPrev();
        }
        return false;
    }
    moveTo(u40) {
        let v40 = super.getCorePlayer();
        if (v40 instanceof OhosUrlListPlayer) {
            this.mLog.info("moveTo");
            return v40.moveTo(u40);
        }
        return false;
    }
    setUrlDataSource(s40) {
        let t40 = super.getNativePlayer();
        if (t40.setUrlDataSource !== undefined) {
            t40.setUrlDataSource(s40);
        }
    }
}
