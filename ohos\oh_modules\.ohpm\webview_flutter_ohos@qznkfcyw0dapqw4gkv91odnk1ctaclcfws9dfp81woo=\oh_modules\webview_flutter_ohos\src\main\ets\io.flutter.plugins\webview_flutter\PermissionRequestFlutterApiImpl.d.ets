import { InstanceManager } from './InstanceManager';
import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import { Reply } from "./GeneratedOhosWebView";
export declare class PermissionRequestFlutterApiImpl {
    private binaryMessenger;
    private instanceManager;
    private api;
    constructor(binaryMessenger: BinaryMessenger, instanceManager: InstanceManager);
    create(instance: PermissionRequest, resources: string[], callback: Reply<void>): void;
}
