import 'dart:convert';

import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:city_pickers/city_pickers.dart';
import 'package:flutter/services.dart';
import 'package:wuling_flutter_app/api/community_api.dart';
import 'package:wuling_flutter_app/models/user/user_address.dart';
import 'package:wuling_flutter_app/utils/manager/loading_manager.dart';

import '../../utils/manager/log_manager.dart';


class AddAddressPage extends StatefulWidget {
  const AddAddressPage({super.key,this.userAddress});
  final UserAddress? userAddress;

  @override
  State<AddAddressPage> createState() => _AddAddressPageState();
}

class _AddAddressPageState extends State<AddAddressPage> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _mobileController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  String selectAddress = '';
  bool isChecked = true;
  Result? result;
  String title = '新建地址';
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    if(widget.userAddress != null) {
      initData();
    }
  }

  void initData() {
    _nameController.text = widget.userAddress!.receiverName!;
    _mobileController.text = widget.userAddress!.receiverPhone!;
    _addressController.text = widget.userAddress!.detailAddress!;
    selectAddress = widget.userAddress!.areaAddress!;
    isChecked = (widget.userAddress!.defaultAddress == 1);
    title = '编辑地址';

    result = Result(provinceId: widget.userAddress!.provinceCode,
        provinceName: widget.userAddress!.provinceName,
    cityId: widget.userAddress!.cityCode,
    cityName: widget.userAddress!.cityName,
    areaId: widget.userAddress!.areaCode,
    areaName: widget.userAddress!.detailAddress);

  }
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        elevation: 0,
        title:Text(title),
        centerTitle: true,
        actions: [
          if(widget.userAddress != null) GestureDetector(
            onTap: () {
              _showDialog();
            },
            child: Container(
              padding: const EdgeInsets.all(10),
              alignment: Alignment.center,
              child: const Text("删除",style: TextStyle(
                fontSize: 16
              ),),
            ),
          )
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.only(left: 20,right: 20),
        child: Column(
          children: [
            _inputWidget('收货人', '请填写收货人姓名',_nameController),
            _inputWidget('手机号码', '请填写收货人手机号码',_mobileController,keyboardType: TextInputType.number),
            _selectAddress(),
            _inputWidget('详细地址', '乡镇，街道，楼牌号等',_addressController),
            const SizedBox(height: 20,),
            const Divider(color: Color(0xFFF1F2F3),),
            Container(
              margin: const EdgeInsets.only(top: 20,bottom: 30),
              alignment: Alignment.centerLeft,
              child: const Text('默认地址',style: TextStyle(
                  color: Color(0xFF454545),
                  fontSize: 16
              ),),
            ),
            Row(
              children: [
                const Expanded(child:  Text('注：每次下单时会默认使用改地址，实际使用地址以最终提交订单是选择的地址为准')),
                CupertinoSwitch(value: isChecked, activeColor: Colors.red,onChanged: (value) {
                  isChecked = value;
                  setState(() {

                  });
                })
              ],
            ),
            Expanded(child: Container(
              alignment: Alignment.bottomCenter,
              margin: const EdgeInsets.only(bottom: 30),
              child: GestureDetector(
                onTap: () {
                  _saveAddress();
                },
                child: Container(
                  height: 45,
                  width: double.infinity,
                  alignment: Alignment.center,
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.all(Radius.circular(24))
                  ),
                  child: const Text("保存",style: TextStyle(
                      color: Colors.white,
                      fontSize: 18
                  ),),
                ),
              ),
            ))
          ],
        ),
      ),
    );
  }
  
  Widget _inputWidget(String title,String hint,TextEditingController controller,
      {keyboardType = TextInputType.text, }) {
    return Container(
      height: 80,
      decoration: const BoxDecoration(
        border: Border(top: BorderSide(
          color: Color(0xFFF1F2F3)
        ))
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.only(right: 15),
            child: Text(title,style: const TextStyle(
              color: Color(0xFF454545),
              fontSize: 16
            ),),
          ),
          Expanded(
              child: TextField(
                cursorColor: Colors.grey,
                controller: controller,
                keyboardType: keyboardType,
                inputFormatters: (keyboardType == TextInputType.number) ? [
                  FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
                ] : [],
                decoration: InputDecoration(
                  border: InputBorder.none,
                  hintText: hint,
                  hintStyle: const TextStyle(
                    color: Color(0xFFC5C5C5),
                  )
                ),
              )
          )
        ],
      ),
    );
  }

  Widget _selectAddress() {
    return GestureDetector(
      onTap: () async {
        CityPickers.showCityPicker(
            context: context,
          cancelWidget: const Text("取消",style: TextStyle(
            color: Colors.grey,
            fontSize: 18
          ),),
          confirmWidget: const Text("确认",style: TextStyle(
            color: Colors.red,
            fontSize: 18
          ),)
        ).then((value) {
          if(value != null) {
            result = value;
            selectAddress = '${value?.provinceName}${value?.cityName}${value?.areaName}';
            setState(() {

            });
          }
        });
      },
      child: Container(
        height: 80,
        decoration: const BoxDecoration(
            border: Border(top: BorderSide(
                color: Color(0xFFF1F2F3)
            ))
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.only(right: 15),
              child: const Text('所在地区',style: TextStyle(
                  color: Color(0xFF454545),
                  fontSize: 16
              ),),
            ),
            Expanded(
                child: GestureDetector(
                  child: Text(selectAddress),
                )
            ),
            const Icon(Icons.chevron_right,
                color: CupertinoColors.lightBackgroundGray),
          ],
        ),
      ),
    );
  }

  _saveAddress() async {
    LogManager().debug("result: $result");
    if(_nameController.text.isEmpty) {
      LoadingManager.showToast("请填写收货人名字");
      return;
    }
    if(_mobileController.text.isEmpty) {
      LoadingManager.showToast("请填写收货人手机号码");
    }else {
      // 去除所有空格和特殊字符
      final cleanedValue = _mobileController.text.replaceAll(RegExp(r'\s+|-|\.'), '');
      if (!RegExp(r'^1[3-9]\d{9}$').hasMatch(cleanedValue)) {
        LoadingManager.showToast("请输入有效的11位手机号码");
        return;
      }
    }
    if(selectAddress.isEmpty) {
      LoadingManager.showToast("请选择所在地区");
      return;
    }
    if(_addressController.text.isEmpty) {
      LoadingManager.showToast("请填写详细地址");
      return;
    }
    if(widget.userAddress != null) {
      widget.userAddress!.receiverName = _nameController.text;
      widget.userAddress!.receiverPhone = _mobileController.text;
      widget.userAddress!.detailAddress = _addressController.text;
      widget.userAddress!.provinceCode = result!.provinceId;
      widget.userAddress!.provinceName = result!.provinceName;
      widget.userAddress!.cityCode = result!.cityId;
      widget.userAddress!.cityName = result!.cityName;
      widget.userAddress!.areaCode = result!.areaId;
      widget.userAddress!.areaAddress = result!.areaName;
      widget.userAddress!.defaultAddress = (isChecked ? 1 : 0);
      await communityAPI.editAddress(widget.userAddress!.toJson());
      LoadingManager.showToast("地址修改成功");

    }else {
      final response = {
        'receiverName': _nameController.text,
        'receiverPhone': _mobileController.text,
        'provinceCode': result?.provinceId,
        'cityCode': result?.cityId,
        'areaCode': result?.areaId,
        'provinceName': result?.provinceName,
        'cityName': result?.cityName,
        'countyName': result?.areaName,
        'detailAddress': _addressController.text,
        'defaultAddress': isChecked ? 1 : 0
      };
      LogManager().debug("response: ${jsonEncode(response)}");
      
      await communityAPI.addAddress(response);
      LoadingManager.showToast("地址添加成功");
    }

    if(mounted) {
      Navigator.of(context).pop(true);
    }
  }
  
  _showDialog() {
    showCupertinoDialog(context: context, builder: (context) {
      return CupertinoAlertDialog(
        title: const Text('是否删除该地址?',style: TextStyle(
          color: Colors.black,
          fontSize: 16,
          fontWeight: FontWeight.normal
        ),),
        actions: [
          CupertinoDialogAction(
            child: const Text('取消'),
            onPressed: () => Navigator.pop(context, false),
          ),
          CupertinoDialogAction(
            onPressed: () => Navigator.pop(context, true),
            isDestructiveAction: true,
            child: const Text('删除'), // 红色警示样式
          ),
        ],
      );
    }).then((value) {
      if(value == true) {
        _deleteAddress();
      }
    });
  }

  _deleteAddress() async {
    final body = FormData.fromMap({
      'addressId': widget.userAddress!.addressId
    });
    await communityAPI.deleteAddress(body);

    LoadingManager.showToast("地址删除成功");

    if(mounted) {
      Navigator.of(context).pop(true);
    }
  }

  @override
  void dispose() {
    // TODO: implement dispose
    _nameController?.dispose();
    _mobileController?.dispose();
    _addressController?.dispose();
    super.dispose();

  }
}
