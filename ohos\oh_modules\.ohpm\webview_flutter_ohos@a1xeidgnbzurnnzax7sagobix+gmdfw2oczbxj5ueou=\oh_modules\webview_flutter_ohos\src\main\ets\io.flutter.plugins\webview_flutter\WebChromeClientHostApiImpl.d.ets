import { WebChromeC<PERSON><PERSON>ost<PERSON><PERSON>, CustomViewCallback } from './GeneratedOhosWebView';
import { InstanceManager } from './InstanceManager';
import { WebChromeClientFlutterApiImpl } from './WebChromeClientFlutterApiImpl';
import { WebViewPlatformView } from './WebViewHostApiImpl';
import { DVModel } from '@ohos/flutter_ohos/src/main/ets/view/DynamicView/dynamicView';
export interface WebChromeClient {
    onProgressChanged(view: WebViewPlatformView, progress: number): void;
    onGeolocationPermissionsShowPrompt(origin: string, callback: JsGeolocation): void;
    onGeolocationPermissionsHidePrompt(): void;
    onShowFileChooser(webView: WebViewPlatformView, filePathCallback: FileSelectorResult, fileChooserParams: FileSelectorParam): boolean;
    onPermissionRequest(request: PermissionRequest): void;
    onConsoleMessage(message: ConsoleMessage): void;
    onCreateWindow(webView: WebViewPlatformView, isDialog: boolean, isUserGesture: boolean, handler: ControllerHandler): void;
    onJsAlert(webView: WebViewPlatformView, url: string, message: string, result: JsResult): boolean;
    onJsConfirm(webView: WebViewPlatformView, url: string, message: string, result: JsResult): boolean;
    onJsPrompt(webView: WebViewPlatformView, url: string, message: string, defaultValue: string, result: JsResult): boolean;
}
export declare class WebChromeClientHostApiImpl extends WebChromeClientHostApi {
    private instanceManager;
    private webChromeClientCreator;
    private flutterApi;
    constructor(instanceManager: InstanceManager, webChromeClientCreator: WebChromeClientCreator, flutterApi: WebChromeClientFlutterApiImpl);
    create(instanceId: number): void;
    setSynchronousReturnValueForOnShowFileChooser(instanceId: number, value: boolean): void;
    setSynchronousReturnValueForOnConsoleMessage(instanceId: number, value: boolean): void;
    setSynchronousReturnValueForOnJsAlert(instanceId: number, value: boolean): void;
    setSynchronousReturnValueForOnJsConfirm(instanceId: number, value: boolean): void;
    setSynchronousReturnValueForOnJsPrompt(instanceId: number, value: boolean): void;
}
export declare class WebChromeClientCreator {
    createWebChromeClient(flutterApi: WebChromeClientFlutterApiImpl): WebChromeClientImpl;
}
declare class WebChromeClientImpl implements WebChromeClient {
    private flutterApi;
    private returnValueForOnShowFileChooser;
    private returnValueForOnConsoleMessage;
    private returnValueForOnJsAlert;
    private returnValueForOnJsConfirm;
    private returnValueForOnJsPrompt;
    constructor(flutterApi: WebChromeClientFlutterApiImpl);
    private emptyReply;
    onCreateWindow(webView: WebViewPlatformView, isDialog: boolean, isUserGesture: boolean, handler: ControllerHandler): void;
    onProgressChanged(view: WebViewPlatformView, progress: number): void;
    onShowCustomView(view: DVModel, callback: CustomViewCallback): void;
    onHideCustomView(): void;
    onGeolocationPermissionsShowPrompt(origin: string, callback: JsGeolocation): void;
    onGeolocationPermissionsHidePrompt(): void;
    onShowFileChooser(webView: WebViewPlatformView, filePathCallback: FileSelectorResult, fileChooserParams: FileSelectorParam): boolean;
    onPermissionRequest(request: PermissionRequest): void;
    onConsoleMessage(consoleMessage: ConsoleMessage): boolean;
    setReturnValueForOnShowFileChooser(value: boolean): void;
    setReturnValueForOnConsoleMessage(value: boolean): void;
    setReturnValueForOnJsAlert(value: boolean): void;
    setReturnValueForOnJsConfirm(value: boolean): void;
    setReturnValueForOnJsPrompt(value: boolean): void;
    onJsAlert(webView: WebViewPlatformView, url: string, message: string, result: JsResult): boolean;
    onJsConfirm(webView: WebViewPlatformView, url: string, message: string, result: JsResult): boolean;
    onJsPrompt(webView: WebViewPlatformView, url: string, message: string, defaultValue: string, result: JsResult): boolean;
}
export {};
