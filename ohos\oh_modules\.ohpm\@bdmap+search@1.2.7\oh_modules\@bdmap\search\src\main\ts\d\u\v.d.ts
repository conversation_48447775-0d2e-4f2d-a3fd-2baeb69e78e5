export declare enum WeatherDataType {
    /** 实时天气预报 */
    WEATHER_DATA_TYPE_REAL_TIME = 0,
    /** 未来若干天天天气预报 */
    WEATHER_DATA_TYPE_FORECASTS_FOR_DAY = 1,
    /** 按小时天气预报，高级字段 */
    WEATHER_DATA_TYPE_FORECASTS_FOR_HOUR = 2,
    /** 生活指数，仅支持国内，高级字段 */
    WEATHER_DATA_TYPE_LIFE_INDEX = 3,
    /** 天气预警，仅支持国内，高级字段 */
    WEATHER_DATA_TYPE_ALERT = 4,
    /** 以上全部，高级字段获取对应权限后可返回 */
    WEATHER_DATA_TYPE_ALL = 5
}
