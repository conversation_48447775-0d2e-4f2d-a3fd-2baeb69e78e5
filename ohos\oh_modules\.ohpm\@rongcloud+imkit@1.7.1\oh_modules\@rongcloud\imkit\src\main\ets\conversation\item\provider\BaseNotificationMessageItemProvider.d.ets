// @keepTs
// @ts-nocheck
/**
 * Created on 2024/08/06
 * <AUTHOR>
 */
import { MessageContent } from '@rongcloud/imlib';
import { Context } from '@kit.AbilityKit';
import { IMessageItemProvider } from './IMessageItemProvider';
import { UiMessage } from '../../model/UiMessage';
/**
 * 小灰条消息 item 基类
 * @version 1.0.0
 */
export declare abstract class BaseNotificationMessageItemProvider<T extends MessageContent> implements IMessageItemProvider<T> {
    private wrap;
    constructor();
    isShowSummaryName(q26: Context, r26: T): boolean;
    getWrapBuilder(): WrappedBuilder<[
        Context,
        UiMessage,
        number
    ]>;
    builder(n26: Context, o26: UiMessage, p26: number): void;
    abstract getMessageWrapBuilder(): WrappedBuilder<[
        Context,
        UiMessage,
        number
    ]>;
    getSummaryText(l26: Context, m26: MessageContent | object): Promise<MutableStyledString>;
    /**
     * 获取当前消息类型需要在会话列表展示的数据
     * @returns 自定义样式的字符串
     */
    abstract getSummaryTextByMessageContent(j26: Context, k26: T): Promise<MutableStyledString>;
}
