// @keepTs
// @ts-nocheck
import { CustomListDialogAction } from '../enum/CustomListDialogAction';
import { CustomListDialogSafetyComponentType } from '../enum/CustomListDialogSafetyComponentType';
export declare class CustomListDialogItem {
    private action;
    private title;
    /**
     * 要是用的安全控件类型
     */
    private safetyComponentType;
    getAction(): CustomListDialogAction;
    getTitle(): ResourceStr;
    getSafetyComponentType(): CustomListDialogSafetyComponentType;
    constructor(u290: CustomListDialogAction, v290: ResourceStr, w290?: CustomListDialogSafetyComponentType);
}
