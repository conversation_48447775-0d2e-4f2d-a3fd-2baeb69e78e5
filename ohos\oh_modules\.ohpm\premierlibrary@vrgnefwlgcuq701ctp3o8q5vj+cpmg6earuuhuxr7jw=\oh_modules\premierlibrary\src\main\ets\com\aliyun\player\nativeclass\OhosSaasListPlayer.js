import { OhosUrlListPlayer } from './OhosUrlListPlayer';
import premierlibrary from 'libpremierlibrary.so';
export class OhosSaasListPlayer extends OhosUrlListPlayer {
    constructor(i30, j30, k30, l30) {
        super(i30, j30, k30, l30);
    }
    addVid(g30, h30) {
        premierlibrary.nListAddVid(this, g30, h30);
    }
    setDefinition(f30) {
        premierlibrary.nListSetDefinition(this, f30);
    }
    moveToNextWithSts(d30, e30) {
        return premierlibrary.nListMoveToNextWithSts(this, d30, e30);
    }
    moveToPrevWithSts(c30) {
        return premierlibrary.nListMoveToPrevWithSts(this, c30);
    }
    moveToWithSts(a30, b30) {
        return premierlibrary.nListMoveToWithSts(this, b30, a30);
    }
    moveToNextWithAuthInfo(y29, z29) {
        return premierlibrary.nListMoveToNextWithPlayAuth(this, y29, z29);
    }
    moveToPrevWithAuthInfo(x29) {
        return premierlibrary.nListMoveToPrevWithPlayAuth(this, x29);
    }
    moveToWithAuthInfo(v29, w29) {
        return premierlibrary.nListMoveToWithPlayAuth(this, w29, v29);
    }
}
