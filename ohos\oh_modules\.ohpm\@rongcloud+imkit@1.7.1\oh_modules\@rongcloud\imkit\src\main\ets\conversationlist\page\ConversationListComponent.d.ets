// @keepTs
// @ts-nocheck
import { Context } from '@kit.AbilityKit';
import { BaseUiConversation } from '../model/BaseUiConversation';
import { Conversation } from '@rongcloud/imlib';
/**
 * 会话列表组件
 * @version 1.0.0
 */
@Component
export declare struct ConversationListComponent {
    onConversationItemClick?: (conversation: Conversation, index: number) => void;
    @BuilderParam
    emptyComponent?: () => void;
    @BuilderParam
    clientNoticeComponent?: () => void;
    @BuilderParam
    connectionStatusNoticeComponent?: () => void;
    private context;
    @State
    private viewModel;
    @State
    private connectionStatusNoticeContent;
    @State
    private dataSource;
    @State
    private dataSize;
    @State
    private isUpRefreshing;
    @State
    private hasMoreData;
    aboutToAppear(): void;
    aboutToDisappear(): void;
    @Builder
    componentBuilder(b50: Context, c50: BaseUiConversation, d50: number): void;
    build(): void;
    @Builder
    connectionStatusNoticeContentComponent(): void;
    @Builder
    clientNoticeContentComponent(): void;
    /**
     * 处理长按事件
     * @param index 长按的数据下标
     */
    private handlerLongClick;
    /**
     * 处理点击事件
     * @param conversation 点击的会话
     */
    private onClickConversationItem;
}
