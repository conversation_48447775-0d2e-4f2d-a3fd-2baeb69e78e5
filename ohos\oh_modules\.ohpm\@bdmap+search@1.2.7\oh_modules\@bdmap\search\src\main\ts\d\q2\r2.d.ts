import { LatLng } from "@bdmap/base";
/**
 * 建议查询请求参数接口
 */
export interface SuggestionSearchOption {
    /**
     * 请求城市区域字段，必须参数
     * 为null时，全国范围检索
     */
    city: string | null;
    /**
     * 检索关键字
     * 必须参数
     */
    keyword: string;
    /**
     * 是否获取行政区域编码
     */
    isExtendAdcode?: boolean;
    /**
     * 设置是否限制城市范围
     * 非必须参数，默认为false
     * 取值为"true"时，仅返回city中指定城市检索结果（注：仅限大陆地区有效）
     */
    cityLimit?: boolean;
    /**
     * 检索坐标参数
     * 非必须参数
     * 会影响关键字不在设置城市范围内时的检索结果
     */
    location?: LatLng;
    /**
     * 返回结果类型 true：热词
     */
    hotWord?: boolean;
}
