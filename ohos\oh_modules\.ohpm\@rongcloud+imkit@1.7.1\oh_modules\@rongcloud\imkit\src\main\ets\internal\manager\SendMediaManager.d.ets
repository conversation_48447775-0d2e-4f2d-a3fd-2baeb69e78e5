// @keepTs
// @ts-nocheck
/**
 * Created on 2025/03/27
 * <AUTHOR>
 */
import { Message } from '../../../../../Index';
import { image } from '@kit.ImageKit';
/**
 * 媒体消息发送管理类
 * @version 1.4.3
 */
declare class SendMediaManager {
    private static instance;
    private waitSendLinkedList;
    private waitSendHashMap;
    private localMediaThumbnailInfoCache;
    private isSending;
    private constructor();
    static getInstance(): SendMediaManager;
    /**
     * 添加需要重发的消息到重发队列
     */
    /**
     * @deprecated 已废弃，直接使用底层sendMediaMessage，去掉insert
     */
    sendMediaMessages(e317: Message[]): Promise<void>;
    /**
     * @deprecated 已废弃，直接使用底层sendMediaMessage，去掉insert
     */
    insertMessages(z316: Message[]): Promise<void>;
    /**
     * @deprecated 已废弃，直接使用底层sendMediaMessage，去掉insert
     */
    cancelSendingMediaMessage(y316: Message): Promise<void>;
    /**
     * 消息是否等待发送中
     */
    /**
     * @deprecated 已废弃，直接使用底层sendMediaMessage，去掉insert
     */
    isMessageInWaiting(x316: number): boolean;
    /**
     * 保存准备发送媒体消息的缩略图宽高
     * @param localPath 文件路径路径
     * @param size 缩略图宽高信息
     */
    putLocalMediaThumbnailInfoCache(u316: string, v316: image.Size): void;
    /**
     * 获取准备发送媒体消息的缩略图宽高
     */
    getLocalMediaThumbnailInfoCacheCache(r316: string): image.Size | undefined;
    /**
     * @deprecated 已废弃，直接使用底层sendMediaMessage，去掉insert
     */
    cancelSendingMediaMessages(n316: Message[]): Promise<void>;
    private loopSendMediaMessage;
}
export { SendMediaManager };
