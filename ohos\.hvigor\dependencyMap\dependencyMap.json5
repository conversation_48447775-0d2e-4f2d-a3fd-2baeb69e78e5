{"basePath": "D:\\wulingFlutter\\wuling-flutter-app\\ohos\\.hvigor\\dependencyMap\\dependencyMap.json5", "rootDependency": "./oh-package.json5", "dependencyMap": {"entry": "./entry/oh-package.json5", "flutter_blue_plus": "./flutter_blue_plus/oh-package.json5", "iamgeqr_flutter_plugin": "./iamgeqr_flutter_plugin/oh-package.json5", "image_picker_ohos": "./image_picker_ohos/oh-package.json5", "path_provider_ohos": "./path_provider_ohos/oh-package.json5", "shared_preferences_ohos": "./shared_preferences_ohos/oh-package.json5", "url_launcher_ohos": "./url_launcher_ohos/oh-package.json5", "webview_flutter_ohos": "./webview_flutter_ohos/oh-package.json5", "permission_handler_ohos": "./permission_handler_ohos/oh-package.json5", "package_info_plus": "./package_info_plus/oh-package.json5", "connectivity_plus": "./connectivity_plus/oh-package.json5", "device_info_plus": "./device_info_plus/oh-package.json5", "mobile_scanner": "./mobile_scanner/oh-package.json5", "open_app_settings": "./open_app_settings/oh-package.json5", "fluwx": "./fluwx/oh-package.json5", "camera_ohos": "./camera_ohos/oh-package.json5"}, "modules": [{"name": "entry", "srcPath": "..\\..\\..\\entry"}, {"name": "flutter_blue_plus", "srcPath": "..\\..\\..\\..\\..\\..\\PUB\\git\\flutter_blue_plus-25f148c4c7822a786de56ec50a254846027431b0\\ohos"}, {"name": "iamgeqr_flutter_plugin", "srcPath": "..\\..\\..\\..\\..\\..\\PUB\\git\\flutter_image_scanner-b9f20a88ca1c738c700a15655b8fab4c305c4d32\\ohos"}, {"name": "image_picker_ohos", "srcPath": "..\\..\\..\\..\\..\\..\\PUB\\git\\flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c\\packages\\image_picker\\image_picker_ohos\\ohos"}, {"name": "path_provider_ohos", "srcPath": "..\\..\\..\\..\\..\\..\\PUB\\git\\flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c\\packages\\path_provider\\path_provider_ohos\\ohos"}, {"name": "shared_preferences_ohos", "srcPath": "..\\..\\..\\..\\..\\..\\PUB\\git\\flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c\\packages\\shared_preferences\\shared_preferences_ohos\\ohos"}, {"name": "url_launcher_ohos", "srcPath": "..\\..\\..\\..\\..\\..\\PUB\\git\\flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c\\packages\\url_launcher\\url_launcher_ohos\\ohos"}, {"name": "webview_flutter_ohos", "srcPath": "..\\..\\..\\..\\..\\..\\PUB\\git\\flutter_packages-35fb467533e174411a117b2a030c15d2a3a9687c\\packages\\webview_flutter\\webview_flutter_ohos\\ohos"}, {"name": "permission_handler_ohos", "srcPath": "..\\..\\..\\..\\..\\..\\PUB\\git\\flutter_permission_handler-1e7c80f0720aff742d9f1e08017e1753bcad76ef\\permission_handler_ohos\\ohos"}, {"name": "package_info_plus", "srcPath": "..\\..\\..\\..\\..\\..\\PUB\\git\\flutter_plus_plugins-452bfb40465722552f31cf78cde60f99cc691e36\\packages\\package_info_plus\\package_info_plus\\ohos"}, {"name": "connectivity_plus", "srcPath": "..\\..\\..\\..\\..\\..\\PUB\\git\\flutter_plus_plugins-8720286d1b4b664dbc1d87ba169125242d4f10fb\\packages\\connectivity_plus\\connectivity_plus\\ohos"}, {"name": "device_info_plus", "srcPath": "..\\..\\..\\..\\..\\..\\PUB\\git\\flutter_plus_plugins-a1347adcca3a46346a6ddd127cebcec9970cad6c\\packages\\device_info_plus\\device_info_plus\\ohos"}, {"name": "mobile_scanner", "srcPath": "..\\..\\..\\..\\..\\..\\PUB\\git\\fluttertpc_mobile_scanner-a50b8f5e377524f79dd143f1297e82d257ad53b1\\ohos"}, {"name": "open_app_settings", "srcPath": "..\\..\\..\\..\\..\\..\\PUB\\git\\fluttertpc_open_app_settings-038c0dd8ffd45bcf84691d62cfff957748cd493a\\ohos"}, {"name": "fluwx", "srcPath": "..\\..\\..\\..\\..\\..\\PUB\\git\\fluwx-9fe4d9f1d3c05de5de18d25cc53852866d9c34d7\\ohos"}, {"name": "camera_ohos", "srcPath": "..\\..\\..\\..\\plugins\\camera_ohos\\ohos"}]}