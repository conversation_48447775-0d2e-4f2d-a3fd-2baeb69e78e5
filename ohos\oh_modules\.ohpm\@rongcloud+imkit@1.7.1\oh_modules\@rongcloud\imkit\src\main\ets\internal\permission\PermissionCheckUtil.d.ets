// @keepTs
// @ts-nocheck
import { Permissions } from '@ohos.abilityAccessCtrl';
import { common } from '@kit.AbilityKit';
import { RequestPermissionInterceptor } from './listener/RequestPermissionInterceptor';
declare class PermissionCheckUtil {
    static readonly MICROPHONE_PERMISSION: Permissions;
    private static interceptor;
    static isShow: boolean;
    /**
     * 检查是否拥有指定的权限
     * @param permissions 检查的权限列表
     * @returns 如果应用拥有所有指定的权限，则为true；否则为false
     */
    static checkPermission(r327: Permissions[]): Promise<boolean>;
    /**
     * 请求用户授权权限
     * @param context表示请求权限的上下文
     * @param permissions ，表示需要请求的权限数组
     * @returns 表示权限请求的结果如果所有权限都被授权，则返回true；否则返回false
     */
    static requestPermissions(g327: common.Context, h327: Array<Permissions>): Promise<boolean>;
    private static showPermissionAlert;
    private static openSetting;
    static setRequestPermissionListListener(x326: RequestPermissionInterceptor): void;
    static removeRequestPermissionListListener(w326: RequestPermissionInterceptor): void;
}
export { PermissionCheckUtil };
