//
//  Generated code. Do not modify.
//  source: sgmw_app_control_result.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use sgmwAppControlResultDescriptor instead')
const SgmwAppControlResult$json = {
  '1': 'SgmwAppControlResult',
  '2': [
    {'1': 'code', '3': 1, '4': 1, '5': 9, '10': 'code'},
    {'1': 'message', '3': 2, '4': 1, '5': 9, '10': 'message'},
    {'1': 'serviceCode', '3': 3, '4': 1, '5': 9, '10': 'serviceCode'},
    {'1': 'collectTime', '3': 4, '4': 1, '5': 3, '10': 'collectTime'},
  ],
};

/// Descriptor for `SgmwAppControlResult`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List sgmwAppControlResultDescriptor = $convert.base64Decode(
    'ChRTZ213QXBwQ29udHJvbFJlc3VsdBISCgRjb2RlGAEgASgJUgRjb2RlEhgKB21lc3NhZ2UYAi'
    'ABKAlSB21lc3NhZ2USIAoLc2VydmljZUNvZGUYAyABKAlSC3NlcnZpY2VDb2RlEiAKC2NvbGxl'
    'Y3RUaW1lGAQgASgDUgtjb2xsZWN0VGltZQ==');

