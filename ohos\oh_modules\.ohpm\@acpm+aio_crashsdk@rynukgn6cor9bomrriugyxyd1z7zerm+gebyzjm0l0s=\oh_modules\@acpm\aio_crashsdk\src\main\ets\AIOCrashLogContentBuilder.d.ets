export declare class AIOCrashStackFrameInfo {
    index: number;
    filePath: string;
    symbol: string;
    pc: string;
    buildId: any;
    targetModule: string;
    isMatch(n4: string): boolean;
    private static readonly SystemSoNames;
    isSystemFile(): boolean;
    static createFromInfo(j4: number, k4: Map<string, string>): AIOCrashStackFrameInfo;
    static createFromJSFrameContent(e4: number, f4: string): AIOCrashStackFrameInfo;
}
export declare class AIOCrashMatchModuleInfo {
    firstMatchModuleName: string;
    firstModuleStackFrameIndex: number;
    firstAppStackFrameIndex: number;
    stackDeep: number;
    crashTag: string;
    crashMsg: string;
    stackEncodeContent: string;
    stackId: string;
    hasMatch(): boolean;
    isMatchExactly(): boolean;
    encodeStackFrames(w3: Array<AIOCrashStackFrameInfo>): void;
    toMap(): Map<string, string>;
}
export declare class AI<PERSON>rashLogContentBuilder {
    private userId;
    private content;
    private appId;
    private version;
    private versionCode;
    private subVersion;
    private buildNumber;
    private logType;
    private processId;
    private threadId;
    private processName;
    private threadName;
    private reportUUID;
    private crashTime;
    private fg;
    private params;
    private exceptionName;
    private exceptionMessage;
    private signal;
    private signalCode;
    private stackTrace;
    private stackFrames;
    private otherThreadStackTrace;
    private hiLog;
    private buildImages;
    private eventHandlerInfo;
    private eventHandler3sSize;
    private eventHandler6sSize;
    private peerBinder;
    private memory;
    private firstAppFrameIndex;
    static createJSCrash(v3: string): AIOCrashLogContentBuilder;
    static createNativeCrash(u3: string): AIOCrashLogContentBuilder;
    static createAppFreeze(t3: string): AIOCrashLogContentBuilder;
    constructor(r3: string, s3: string);
    setUserId(q3: string): void;
    setSubVersion(p3: string): void;
    setBuildNumber(o3: string): void;
    setProcessId(n3: string): void;
    setProcessName(m3: string): void;
    setThreadId(l3: string): void;
    setThreadName(k3: string): void;
    setReportUUID(j3: string): void;
    setVersion(i3: string): void;
    setVersionCode(h3: string): void;
    setCrashTime(g3: string): void;
    getCrashTime(): string;
    setForeground(f3: string): void;
    getForeground(): string;
    addParam(d3: string, e3: string): void;
    addCustomParams(y2: Map<string, string>): void;
    setParams(u2: Map<string, string>): void;
    setHiLog(t2: string): void;
    setExceptionName(s2: string): void;
    setExceptionMessage(r2: string): void;
    setSignal(q2: string): void;
    setSignalCode(p2: string): void;
    matchModules(l2: Array<string>): AIOCrashMatchModuleInfo | undefined;
    setStackTrace(i2: string): void;
    stringifyFrames(e2: Array<Map<string, string>>): string;
    setStackTraceByFrames(b2: Array<Map<string, string>>): void;
    addOtherThreads(x1: Array<Map<string, string>>): void;
    setEventHandlerInfo(w1: Array<string>): void;
    setEventHandler3sSize(v1: string): void;
    setEventHandler6sSize(u1: string): void;
    setPeerBinder(t1: Array<string>): void;
    setMemory(s1: Map<string, string>): void;
    getLogType(): string;
    isJSCrash(): boolean;
    isNativeCrash(): boolean;
    isAppFreeze(): boolean;
    formatIndex(r1: number): string;
    formatFrame(j1: Map<string, string>): string;
    appendLineContent(i1: string): void;
    start(): void;
    addSectionSep(): void;
    buildBasicInfoSection(d1: string, e1: string, f1: string, g1: string, h1: string): void;
    buildParamsSection(): void;
    buildLogcatSection(): void;
    buildJSExceptionStackSection(): void;
    buildNativeCrashStackSection(): void;
    buildOtherThreadSection(): void;
    buildImagesSection(): void;
    buildANRReasonSection(): void;
    buildANRTraceSection(): void;
    buildMemoryInfo(): void;
    buildReportName(): string;
    buildLog(): string;
}
