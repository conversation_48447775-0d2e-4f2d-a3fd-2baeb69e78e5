// @keepTs
// @ts-nocheck
import { Message } from '@rongcloud/imlib';
import { ImagePreviewParams } from '../model/ImagePreviewParams';
@Entry({ routeName: 'ImagePreviewPage' })
@Component
export declare struct ImagePreviewPage {
    @State
    imagePreviewParams: ImagePreviewParams;
    @State
    optionIndex: number;
    @State
    allMessage: Message[];
    @State
    conversationType: number;
    private isShow;
    private DestructRecallMessageEventCallback;
    aboutToAppear(): Promise<void>;
    aboutToDisappear(): void;
    private initData;
    build(): void;
    @Builder
    pageContentBuilderParam(): void;
    /**
     * 撤回消息
     */
    private showMessageRecalledDialog;
    /**
     * num 0左滑到底 >0 右滑到底 -1第一次滑动
     * */
    private getMessage;
    private isDestructMessage;
    private getReferMessage;
    private getForwardMessage;
    private downloadFile;
}
