// @keepTs
// @ts-nocheck
import { Context } from '@kit.AbilityKit';
export declare class PortraitUtil {
    static sizeVP: number;
    static maxMemberCount: number;
    static userDirName: string;
    static groupDirName: string;
    static httpDirName: string;
    /**
     * 是否是一个头像地址
     * 如果为一个网络地址或者本地文件则返回 true
     */
    static isPortraitUrl(v112: string): boolean;
    static isFile(t112: string): boolean;
    /**
     * 如果是网络图片，则通过指定规则拼接成本地的路径检查头像是否已经被下载到本地了
     * @returns 本地图片，如果没有被下载过则返回空字符
     */
    static http2LocalPath(r112: Context, s112: string): Promise<string>;
    /**
     * 根据名字生成个人头像，头像名字为 Id + Name 的方式
     * @param nickname 用户名
     * @param userId 用户id
     * @param sizeVP 生成的图片尺寸大小（单位 vp）
     * @param fileDir 图片保存路径
     * @returns 返回头像沙盒地址
     */
    static createPersonalImage(l112: Context, m112: string, n112: string): Promise<string>;
    static createPersonalPixel(f112: string, g112: string): Promise<PixelMap>;
    /**
     * 根据个人头像，拼接群组头像
     * @param fileName 该头像的名字 ，内部会转换成 MD5 当做文件名
     * @param pixelMaps 用户的头像数据
     * @returns
     */
    static createGroupImage(y111: Context, z111: string, a112: PixelMap[]): Promise<string>;
    static getUriFromPath(x111: string): string;
    /**
     * 获取群头像文件名，通过群 ID 和群成员 ID 进行拼接
     * @param groupId 群 ID
     * @param idsValue 拼接好的群成员 ID
     */
    static getGroupPortraitFileName(v111: string, w111: string): string;
    static getGroupPortraitFileNameByArray(r111: string, s111: Array<string>): string;
    static getGroupMemberIdsSpliceRuleValue(o111: string, p111: string, q111?: string): string;
    /**
     * 获取头像的存储路径
     * @param dirName 存储到哪个文件夹下的名称路径
     * @param fileName 文件名，内部会转成 MD5 当做文件名
     * @returns 路径格式：/data/storage/el2/base/files/portrait/${dirName}/${fileName}
     */
    static getPortraitSavePath(h111: Context, i111: string, j111: string): Promise<string>;
    private static createFolder;
    static combineImages(l110: PixelMap[], m110: number, n110?: number): Promise<PixelMap>;
    static maxCountOfRow(k110: number): number;
    static saveFile(e110: string, f110: PixelMap): Promise<string>;
    static createPixelMap(u109: number, v109: number, w109: number, x109: number): Promise<PixelMap | undefined>;
    /**
     * 根据本地的图片路径创建 PixelMap
     */
    static createPixelMapByPath(n109: string): PixelMap | undefined;
    static createPixelMapByOffscreenCanvas(h109: string, i109: PixelMap | undefined): PixelMap;
    static getBackgroundColor(e109: string): string;
    static hexToRGB(a109: string): number[];
    static isHttp(z108: string | undefined): boolean;
}
