import { LatLng } from "@bdmap/base";
import { PoiChildrenInfo, SearchResult } from "../e/h/f1";
/**
 * 建议查询请求结果接口
 */
export interface SuggestionResult extends SearchResult {
    /**
     * 检索结果列表
     */
    suggestionList?: SuggestionInfo[];
}
/**
 * suggestion信息接口
 */
export interface SuggestionInfo {
    /**
     * 联想词名称
     */
    key: string;
    /**
     * 联想词city
     */
    city: string;
    /**
     * 联想结果所在行政区
     */
    district: string;
    /**
     * 联想结果坐标点
     */
    pt: LatLng;
    /**
     * 联想结果uid
     */
    uid: string;
    /**
     * 联想结果标签
     */
    tag: string;
    /**
     * 联想结果地址
     */
    address: string;
    /**
     * 行政区划编码
     */
    adCode: number;
    /**
     * 联想词子点，以列表形式展示
     * V5.2.0版本新增字段，需要使用setter和getter方法操作
     * 该字段需要申请权限
     */
    poiChildrenInfoList: PoiChildrenInfo[];
}
