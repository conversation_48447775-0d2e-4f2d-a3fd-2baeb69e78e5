import premierlibrary from 'libpremierlibrary.so';
export class Logger {
    constructor() {
        this.mValue = -1;
        this.mLogCallback = null;
        this.mEnableConsoleLog = true;
        this.mCurrentLogLevel = LogLevel.AF_LOG_LEVEL_INFO;
        this.nOnLogCallback = (t44, u44) => {
            let v44 = Logger.getLevel(t44);
            if (Logger.sAppContext != null) {
                Logger.getInstance(Logger.sAppContext).callback(v44, u44);
            }
        };
        premierlibrary.nLoggerConstruct(this);
    }
    static getInstance(r44) {
        if (Logger.sInstance == null) {
            Logger.sInstance = new Logger();
            Logger.sInstance.setLogLevel(LogLevel.AF_LOG_LEVEL_INFO);
            if (r44 != null) {
                Logger.sAppContext = r44;
            }
        }
        return Logger.sInstance;
    }
    setLogCallback(q44) {
        this.mLogCallback = q44;
    }
    getLogCallback() {
        return this.mLogCallback;
    }
    setLogLevel(p44) {
        this.mCurrentLogLevel = p44;
        premierlibrary.nLoggerSetLevel(p44);
    }
    setLogOption(n44, o44) {
        switch (n44) {
            case LogOption.FRAME_LEVEL_LOGGING_ENABLED:
                premierlibrary.nLoggerSetOption("frame_level_logging_enabled", o44);
                break;
        }
    }
    getLogLevel() {
        let m44 = premierlibrary.nLoggerGetLevel();
        return Logger.getLevel(m44);
    }
    enableConsoleLog(l44) {
        this.mEnableConsoleLog = l44;
        premierlibrary.nLoggerEnableConsole(l44);
    }
    callback(j44, k44) {
        if (this.mLogCallback != null) {
            this.mLogCallback.onLog(j44, k44);
        }
    }
    static getLevel(i44) {
        switch (i44) {
            case 0:
                return LogLevel.AF_LOG_LEVEL_NONE;
            case 8:
                return LogLevel.AF_LOG_LEVEL_FATAL;
            case 16:
                return LogLevel.AF_LOG_LEVEL_ERROR;
            case 24:
                return LogLevel.AF_LOG_LEVEL_WARNING;
            case 32:
                return LogLevel.AF_LOG_LEVEL_INFO;
            case 48:
                return LogLevel.AF_LOG_LEVEL_DEBUG;
            case 56:
                return LogLevel.AF_LOG_LEVEL_TRACE;
            default:
                return LogLevel.AF_LOG_LEVEL_DEBUG;
        }
    }
}
export var LogLevel;
(function (h44) {
    h44[h44["AF_LOG_LEVEL_NONE"] = 0] = "AF_LOG_LEVEL_NONE";
    h44[h44["AF_LOG_LEVEL_FATAL"] = 8] = "AF_LOG_LEVEL_FATAL";
    h44[h44["AF_LOG_LEVEL_ERROR"] = 16] = "AF_LOG_LEVEL_ERROR";
    h44[h44["AF_LOG_LEVEL_WARNING"] = 24] = "AF_LOG_LEVEL_WARNING";
    h44[h44["AF_LOG_LEVEL_INFO"] = 32] = "AF_LOG_LEVEL_INFO";
    h44[h44["AF_LOG_LEVEL_DEBUG"] = 48] = "AF_LOG_LEVEL_DEBUG";
    h44[h44["AF_LOG_LEVEL_TRACE"] = 56] = "AF_LOG_LEVEL_TRACE";
})(LogLevel || (LogLevel = {}));
export var LogOption;
(function (g44) {
    g44[g44["FRAME_LEVEL_LOGGING_ENABLED"] = 1] = "FRAME_LEVEL_LOGGING_ENABLED";
})(LogOption || (LogOption = {}));
