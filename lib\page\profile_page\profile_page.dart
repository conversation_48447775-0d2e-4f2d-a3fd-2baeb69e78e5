import 'package:quiver/core.dart';
import 'package:popover/popover.dart';
import 'package:wuling_flutter_app/constant/web_view_url_tool.dart';
import 'package:wuling_flutter_app/models/index.dart';
import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/constant/storage.dart';
import 'package:wuling_flutter_app/constant/constant.dart';
import 'package:wuling_flutter_app/constant/service_constant.dart';
import 'package:wuling_flutter_app/page/community/user_info_page.dart';
import 'package:wuling_flutter_app/page/qrscan/BarcodeScannerPage.dart';
import 'package:wuling_flutter_app/routes/jump_tool.dart';
import 'package:wuling_flutter_app/utils/app_info_util.dart';
import 'package:wuling_flutter_app/utils/http/api_exception.dart';
import 'package:wuling_flutter_app/utils/manager/loading_manager.dart';
import 'package:wuling_flutter_app/utils/manager/log_manager.dart';
import 'package:wuling_flutter_app/utils/qr_scanner_util.dart';
import 'package:wuling_flutter_app/utils/show_login_dialog.dart';
import 'package:wuling_flutter_app/widgets/profile_page_widgets/profile_page_widgets_index.dart';
import 'package:wuling_flutter_app/widgets/common/custom_dialog.dart';
import 'package:wuling_flutter_app/widgets/common/image_view.dart';
import 'package:wuling_flutter_app/utils/manager/dialog_manager.dart';
import 'package:wuling_flutter_app/utils/manager/webview_url_manager.dart';
import 'package:wuling_flutter_app/utils/manager/login_manager.dart';
import 'package:wuling_flutter_app/utils/manager/phone_call_manager.dart';
import 'package:wuling_flutter_app/utils/manager/app_gallery_manager.dart';
import 'package:wuling_flutter_app/utils/sp_util.dart';
import 'package:wuling_flutter_app/utils/manager/notification_manager.dart';
import 'package:wuling_flutter_app/models/global_data.dart';
import 'package:wuling_flutter_app/page/profile_page/system_setting_page.dart';
import 'package:wuling_flutter_app/page/base_page/base_page.dart';
import 'package:wuling_flutter_app/routes/app_routes.dart';
import 'package:wuling_flutter_app/widgets/common/custom_refresher.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:wuling_flutter_app/api/user_api.dart';
import 'package:wuling_flutter_app/api/common_api.dart';

class MinePage extends BasePage {
  MinePage({
    Key? key,
    bool hideAppBar = false,
    bool isWithinSafeArea = true,
    String appBarTitle = '',
    Color appBarColor = Colors.transparent,
    Color pageBackgroundColor = Colors.transparent,
  }) : super(
          key: key,
          hideAppBar: hideAppBar,
          hideBottomBar: false,
          isWithinSafeArea: isWithinSafeArea,
          appBarTitle: appBarTitle,
          appBarColor: appBarColor,
          pageBackgroundColor: pageBackgroundColor,
        );

  @override
  _MinePageState createState() => _MinePageState();
}

class _MinePageState extends BasePageState<MinePage> {
  List<Advertise> _advertises = [];
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  UserModel? _userModel;
  UserHandleListModel? _functionHandleList;
  UserHandleListModel? _communityHandleList;
  UserHandleListModel? _shoppingHandleList;
  UserHandleListModel? _taskHandleList;
  AttachedAccountModel? _attachedAccountModel;
  MedalLevelModel? _medalLevelModel;
  // TaskCenterResponseModel? _taskCenterResponseModel;
  List<Advertise> _advertiseList = [];

  void QRBack(String data) {
    LogManager().log(data);
    //二维码扫描回调，这里再处理响应的操作
    QrScannerUtil().returnProcessing(context, data);
  }

  UserHandleModel messageModel() {
    return UserHandleModel(
      functionIconName: '消息',
      functionIconUrl: 'assets/images/profile_page/mine_message.png',
      triggerType: UserHandleType.message.value,
      needLogin: 1,
      redDotStyle: 1,
    );
  }

  UserHandleListModel? addMessageModelToList(
      UserHandleListModel? communityHandleList) {
    UserHandleListModel? newModel;
    if (communityHandleList?.appFunctionIcons != null) {
      // 创建 appFunctionIcons 的副本
      List<UserHandleModel> updatedIcons =
          List.from(communityHandleList!.appFunctionIcons!);
      // 在第一个位置插入新的 UserHandleModel 实例
      updatedIcons.insert(0, messageModel());
      // 使用 copyWith 方法更新 userHandleListModel 实例
      newModel = communityHandleList.copyWith(
        appFunctionIcons: Optional.of(updatedIcons),
      );
    }
    return newModel;
  }

  List<Widget> getWidgetList() {
    List<Widget> list = [];
    list.add(ProfilePageHeader(
      userModel: _userModel,
      onAvatarButtonTap: () {
        if (!GlobalData().isLogin) {
          // 未登录则
          LoginManager().showLoginModal();
        } else {
          Navigator.of(context).push(MaterialPageRoute(builder: (context) {
            return UserInfoPage(
                nickname: _userModel!.nickname!,
                userIdStr: _userModel!.userIdStr);
          }));
        }
      },
      onUserNameTap: () {
        if (!GlobalData().isLogin) {
          LoginManager().showLoginModal();
        }
      },
      onMyQRCodeButtonTap: () {
        if (!GlobalData().isLogin) {
          LoginManager().showLoginModal();
          return;
        }
        JumpTool().jumpToMyQRCodePage(context);
      },
      onSignatureTap: () {
        JumpTool().jumpToEditSignaturePage(context);
      },
    ));
    if (_medalLevelModel != null && GlobalData().isLogin) {
      list.add(MineMedalWidget(
        medalLevelModel: _medalLevelModel,
        onArrowButtonPressed: () {
          if (!GlobalData().isLogin) {
            LoginManager().showLoginModal();
            return;
          }
          JumpTool()
              .openWeb(context, WebViewUrlManager().getMyMedalURL(), false);
        },
      ));
    }
    list.add(MineHandleWidget(
      userHandleListModel: _communityHandleList,
      onButtonClicked: ({required UserHandleModel? userHandleModel}) {
        if (userHandleModel?.needLogin == 1) {
          if (!GlobalData().isLogin) {
            ShowLoginDialog().show();
            return;
          }
        }
        JumpTool().jumpToHandlePage(context, handleModel: userHandleModel);
      },
    ));
    list.add(const SizedBox(
      height: 20,
    ));
    list.add(MineHandleWidget(
      userHandleListModel: _shoppingHandleList,
      onButtonClicked: ({required UserHandleModel? userHandleModel}) {
        if (userHandleModel?.needLogin == 1) {
          if (!GlobalData().isLogin) {
            showNotLoginAlertDialog(context);
            return;
          }
        }
        JumpTool().jumpToHandlePage(context, handleModel: userHandleModel);
      },
    ));
    list.add(const SizedBox(
      height: 20,
    ));
    list.add(MineLingWidget(
      attachedAccountModel: _attachedAccountModel,
      onTap: () {
        if (!GlobalData().isLogin) {
          showNotLoginAlertDialog(context);
          return;
        }
        String linkUrl = WebViewURLTool.myLingURL();
        JumpTool().openWeb(context, linkUrl, false);
      },
    ));
    if (_taskHandleList != null && GlobalData().isLogin) {
      list.add(const SizedBox(
        height: 20,
      ));
      list.add(MineTaskCenterWidget(
        userHandleListModel: _taskHandleList,
        onItemTap: ({required UserHandleModel userHandleModel}) {
          if (userHandleModel.needLogin == 1) {
            if (!GlobalData().isLogin) {
              showNotLoginAlertDialog(context);
              return;
            }
          }
          JumpTool().jumpToHandlePage(context, handleModel: userHandleModel);
        },
      ));
    }
    if (_advertiseList.isNotEmpty) {
      list.add(ProfileAdWidget(
        ads: _advertiseList,
        onAdClicked: (Advertise ad) {
          JumpTool().jumpToAdvertisePage(
            context,
            linkType: ad.linkType ?? 0,
            linkUrl: ad.linkUrl ?? '',
            eventPage: '',
          );
        },
      ));
    }
    list.add(MineFunctionWidget(
      userHandleListModel: _functionHandleList,
      // hiddenHandelButtonNames: const [
      //   '停车充电',
      //   '合伙人',
      //   '车主认证',
      //   '保险订单'
      // ], //['领优惠券', '我的卡', '停车充电', '合伙人', '试驾订单', '保险订单', '钱包'],
      onButtonClicked: ({required UserHandleModel? userHandleModel}) {
        if (userHandleModel?.needLogin == 1) {
          if (!GlobalData().isLogin) {
            showNotLoginAlertDialog(context);
            return;
          }
        }
        JumpTool().jumpToHandlePage(context, handleModel: userHandleModel);
      },
    ));
    return list;
  }

  /*
  * 请求广告数据
  * */
  void _onRefresh() async {
    setState(() {
      _userModel = GlobalData().isLogin ? GlobalData().userModel : null;
    });
    try {
      MedalLevelModel? currentMedalLevelModel;
      List<MedalLevelModel> medalLevelList = [];
      AttachedAccountModel? attachedAccountModel;
      // TaskCenterResponseModel? taskCenterResponseModel;
      UserHandleListModel? userHandleListModel;
      if (GlobalData().isLogin) {
        medalLevelList = await commonAPI.getUserMedalLevel();
        for (MedalLevelModel medalLevelModel in medalLevelList) {
          if (medalLevelModel.currentLevel) {
            currentMedalLevelModel = medalLevelModel;
          }
        }
        attachedAccountModel = await commonAPI.getAttachedAccountBalance();
        // taskCenterResponseModel = await commonAPI.getMineTaskList();
      }
      UserHandleListModel? communityHandleList = await userAPI
          .getAppFunctionIconList('${UserHandleListType.mineCommunity.value}');
      communityHandleList = addMessageModelToList(communityHandleList);
      await SpUtil()
          .setJSON(SP_COMMUNITY_HANDLE_LIST_KEY, communityHandleList?.toJson());
      final UserHandleListModel shoppingHandleList = await userAPI
          .getAppFunctionIconList('${UserHandleListType.mineShopping.value}');
      await SpUtil()
          .setJSON(SP_SHOPPING_HANDLE_LIST_KEY, shoppingHandleList.toJson());
      final UserHandleListModel functionHandleList = await userAPI
          .getAppFunctionIconList('${UserHandleListType.mineBottom.value}');
      final UserHandleListModel taskHandleList = await userAPI
          .getAppFunctionIconList('${UserHandleListType.mineTask.value}');
      await SpUtil()
          .setJSON(SP_FUNCTION_HANDLE_LIST_KEY, functionHandleList.toJson());
      List<Advertise> advertiseList =
          await commonAPI.getAdvertiseWithPosition(49);
      await SpUtil().setJSON(SP_MINE_ADVERTISE_LIST_KEY,
          advertiseList.map((e) => e.toJson()).toList());
      setState(() {
        _medalLevelModel = currentMedalLevelModel;
        _communityHandleList = communityHandleList;
        _shoppingHandleList = shoppingHandleList;
        _functionHandleList = functionHandleList;
        _attachedAccountModel = attachedAccountModel;
        // _taskCenterResponseModel = taskCenterResponseModel;
        _taskHandleList = taskHandleList;
        _advertiseList = advertiseList;
      });
      if (_refreshController.isRefresh) {
        _refreshController.refreshCompleted();
      }
    } catch (e) {
      if (_refreshController.isRefresh) {
        _refreshController.refreshCompleted();
      }
    }
  }

  void showLoginDialog(BuildContext context) {
    DialogManager().showCustomDialog(
      CustomDialog(
        content: "您还没有登录",
        buttons: [
          DialogButton(
              label: "取消",
              onPressed: () {},
              backgroundColor: Colors.white,
              textColor: const Color(0xFF9B9DA9)),
          DialogButton(
              label: "去登录",
              onPressed: () {
                LoginManager().showLoginModal();
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xFF00BAFF)),
        ],
      ),
    );
  }

  void _receivedLoginSucceedNotification(dynamic payload) {
    _refreshController.requestRefresh();
    //登录自动打开自动签到
    SpUtil().setBool(Constant.AUTO_SIGN, true);
  }

  void _receivedLogoutSucceedNotification(dynamic payload) {
    _refreshController.requestRefresh();
  }

  void _receivedUserInfoUpdatedNotification(dynamic payload) {
    _refreshController.requestRefresh();
  }

  void showNotLoginAlertDialog(BuildContext context) {
    DialogManager().showCustomDialog(
      CustomDialog(
        content: "您还没有登录",
        contentFontSize: 18,
        contentColor: Colors.black,
        buttonHeight: 70,
        horizontalPadding: 20,
        buttons: [
          DialogButton(
              label: "继续逛逛",
              onPressed: () {},
              backgroundColor: Colors.white,
              textColor: const Color(0xFF9B9DA9)),
          DialogButton(
              label: "去登录",
              onPressed: () {
                LoginManager().showLoginModal();
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xffea0029)),
        ],
      ),
    );
  }

  Future<void> dealWithPopoverItemTap(ButtonModel buttonModel) async {
    Navigator.of(context).pop();
  }

  List<ButtonModel> getPopOverButtonList() {
    List<ButtonModel> list = [];
    list.add(ButtonModel(
        title: '客服热线',
        imageUrl: 'assets/images/profile_page/menu_hotline.png',
        type: ButtonType.customerService));
    list.add(ButtonModel(
        title: '联系客服',
        imageUrl: 'assets/images/profile_page/menu_service.png',
        type: ButtonType.onlineChat));
    return list;
  }

  @override
  Widget? setAppBarLeading() {
    return IconButton(
      padding: EdgeInsets.zero,
      onPressed: () async {
        Navigator.of(context).push(CustomCupertinoPageRoute(
          builder: (context) => SystemSettingsPage(),
          canSwipeBack: false, // 禁用手势返回
        ));
        // try {
        //   String currentVersion = AppInfoUtil().versionName;
        //   AppVersionModel versionModel = await commonAPI.getOhosAppVersion(currentVersion);
        //   DialogManager().showAppUpdateDialog(versionModel: versionModel);
        // }catch(e){
        //   if(e is APIException){
        //     LoadingManager.showError('获取失败 ${e.message}');
        //   }
        // }
      },
      icon: SizedBox(
        height: 22,
        width: 22,
        child: ImageView('assets/images/profile_page/mine_icon_setting.png'),
      ),
    );
  }

  @override
  List<Widget>? setAppBarActions() {
    double buttonHeight = 54;
    return [
      Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          AppBarButton(
            visible: GlobalData().isLogin ? false : true,
            imageUrl: 'assets/images/profile_page/my_new_scanf.png',
            text: '扫描',
            onPress: ({required BuildContext context}) {
              LogManager().log('扫描响应');
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => BarcodeScannerPage(
                      onPageCallBack: (data) => QRBack(data)),
                ),
              );
            },
          ),
          AppBarButton(
            imageUrl: 'assets/images/profile_page/my_customer_service.png',
            text: '客服',
            onPress: ({required BuildContext context}) {
              showPopover(
                  context: context,
                  bodyBuilder: (context) => ButtonList(
                      buttons: getPopOverButtonList(),
                      onButtonTap: (buttonModel) {
                        Navigator.of(context).pop();
                        if (buttonModel.type == ButtonType.customerService) {
                          PhoneCallManager().callTelWithPhoneNumber(
                              context, Constant.HOT_LINE_TELEPHONE_NUMBER);
                        } else if (buttonModel.type == ButtonType.onlineChat) {
                          if (!GlobalData().isLogin) {
                            showNotLoginAlertDialog(context);
                            return;
                          }
                          String url = WebViewURLTool.kefuURLStrWithGroup(
                              KefuGroup.mm.value, '', '');
                          JumpTool().openWeb(context, url, true);
                        }
                      }),
                  onPop: () => LogManager().debug('Popover was popped!'),
                  direction: PopoverDirection.bottom,
                  backgroundColor: Color(0xff383a40),
                  width: 140,
                  height: getPopOverButtonList().length * buttonHeight,
                  arrowHeight: 8,
                  arrowWidth: 16,
                  radius: 4);
            },
          ),
          const SizedBox(
            width: 10,
          )
        ],
      )
    ];
  }

  @override
  void pageInitState() {
    if (GlobalData().isLogin) {
      _userModel = GlobalData().userModel;
    }
    // 订阅通知
    NotificationManager().subscribe(
        Constant.NOTIFICATION_LOGIN_SUCCEED, _receivedLoginSucceedNotification);
    NotificationManager().subscribe(Constant.NOTIFICATION_LOGOUT_SUCCEED,
        _receivedLogoutSucceedNotification);
    NotificationManager().subscribe(Constant.NOTIFICATION_USER_INFO_UPDATED,
        _receivedUserInfoUpdatedNotification);
    //尝试读取缓存
    var communityHandleListJson =
        SpUtil().getJSON(SP_COMMUNITY_HANDLE_LIST_KEY);
    if (communityHandleListJson != null) {
      var communityHandleList =
          UserHandleListModel.fromJson(communityHandleListJson);
      _communityHandleList = communityHandleList;
    }
    var shoppingHandleListJson = SpUtil().getJSON(SP_SHOPPING_HANDLE_LIST_KEY);
    if (shoppingHandleListJson != null) {
      var shoppingHandleList =
          UserHandleListModel.fromJson(shoppingHandleListJson);
      _shoppingHandleList = shoppingHandleList;
    }
    var functionHandleListJson = SpUtil().getJSON(SP_FUNCTION_HANDLE_LIST_KEY);
    if (functionHandleListJson != null) {
      var functionHandleList =
          UserHandleListModel.fromJson(functionHandleListJson);
      _functionHandleList = functionHandleList;
    }

    List<dynamic>? advertiseJsonList =
        SpUtil().getJSON(SP_MINE_ADVERTISE_LIST_KEY);
    if (advertiseJsonList != null) {
      List<Advertise> advertiseList =
          advertiseJsonList.map((json) => Advertise.fromJson(json)).toList();
      _advertiseList = advertiseList;
    }
    // 延时执行是因为要等待页面构建完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _refreshController.requestRefresh();
    });
  }

  @override
  Widget buildPageContent(BuildContext context) {
    // 在此处添加页面ui设置，默认添加一个center text组件
    return CustomSmartRefresher(
      controller: _refreshController,
      onRefresh: _onRefresh,
      child: ListView(
        children: getWidgetList(),
      ),
    );
  }

  @override
  void dispose() {
    // 在此处添加页面销毁逻辑
    _refreshController?.dispose();
    // 订阅通知
    NotificationManager().unsubscribe(
        Constant.NOTIFICATION_LOGIN_SUCCEED, _receivedLoginSucceedNotification);
    NotificationManager().unsubscribe(Constant.NOTIFICATION_LOGOUT_SUCCEED,
        _receivedLogoutSucceedNotification);
    NotificationManager().unsubscribe(Constant.NOTIFICATION_USER_INFO_UPDATED,
        _receivedUserInfoUpdatedNotification);

    super.dispose();
  }
}
