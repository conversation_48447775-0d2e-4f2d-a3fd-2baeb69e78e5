import 'dart:convert';

import 'package:popover/popover.dart';
import 'package:wuling_flutter_app/api/index.dart';
import 'package:wuling_flutter_app/global.dart';
import 'package:wuling_flutter_app/models/common/advertise.dart';
import 'package:wuling_flutter_app/models/global_data.dart';
import 'package:wuling_flutter_app/models/index.dart';
import 'package:wuling_flutter_app/page/community/community_page.dart';
import 'package:wuling_flutter_app/page/lazy_index_stack.dart';
import 'package:wuling_flutter_app/page/store/store_page.dart';
import 'package:wuling_flutter_app/utils/manager/notification_manager.dart';
import 'package:wuling_flutter_app/constant/constant.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:wuling_flutter_app/widgets/common/custom_dialog.dart';
import '../common/action.dart';
import '../constant/service_constant.dart';
import '../constant/storage.dart';
import '../constant/web_view_url_tool.dart';
import '../models/user/user_model.dart';
import '../routes/jump_tool.dart';
import '../utils/manager/dialog_manager.dart';
import '../utils/manager/log_manager.dart';
import '../utils/manager/login_manager.dart';
import '../utils/manager/phone_call_manager.dart';
import '../utils/sp_util.dart';
import '../widgets/profile_page_widgets/customer_service_button_list.dart';
import '../widgets/webview/webview.dart';
import 'community/send_topic_page.dart';
import 'use_car_page/use_car_page.dart';
import 'profile_page/profile_page.dart';
import 'package:wuling_flutter_app/widgets/main_page_widgets.dart';
import 'package:wuling_flutter_app/utils/manager/post_manager.dart';

class MainPage extends StatefulWidget {
  final bool centerButtonEnable;
  const MainPage({super.key, this.centerButtonEnable = true});

  @override
  _MainPageState createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  int _selectedIndex = 0;
  bool _isCenterButtonSelect = false;
  UserHandleListModel? _servicrHandleListModel;

  // Tab数据
  final List<TabData> _tabs = [
    TabData(
        "发现",
        "assets/images/main_tab_bar/bj_tabbar_discover_selected.png",
        "assets/images/main_tab_bar/bj_tabbar_discover_normal.png",
        null,
        CommunityPage()),
    TabData(
        "出行",
        "assets/images/main_tab_bar/bj_tabbar_vehicle_use_selected.png",
        "assets/images/main_tab_bar/bj_tabbar_vehicle_use_normal.png",
        null,
        UseCarPage()),
    TabData(
        "",
        "assets/images/main_tab_bar/bj_tabbar_vehicle_use_selected.png",
        "assets/images/main_tab_bar/bj_tabbar_vehicle_use_selected.png",
        null,
         Container(width: 40,)),
    TabData(
        "好物",
        "assets/images/main_tab_bar/bj_tabbar_shop_selected.png",
        "assets/images/main_tab_bar/bj_tabbar_shop_normal.png",
        null,
        const StorePage()),
    TabData(
        "我的",
        "assets/images/main_tab_bar/bj_tabbar_mine_selected.png",
        "assets/images/main_tab_bar/bj_tabbar_mine_normal.png",
        null,
        MinePage()),
  ];

  final List<Widget> _pages = [
    CommunityPage(),
    UseCarPage(),
    Container(),
    const StorePage(),
    MinePage()
  ];

  @override
  void initState() {
    super.initState();
    setupNotification();
    fetchAdData();
  }

  void setupNotification() {
    NotificationManager().subscribe(Constant.NOTIFICATION_LOGOUT_SUCCEED,
        _receivedLogoutSucceedNotification);
    NotificationManager().subscribe(Constant.NOTIFICATION_MAIN_PAGE_SELECT_PAGE,_receivedSelectPageNotification);
    NotificationManager().subscribe(
        Constant.NOTIFICATION_LOGIN_SUCCEED, _receivedLoginSucceedNotification);
  }

  void _receivedLogoutSucceedNotification(dynamic payload) {
    fetchAdData();
    setState(() {
      Global.preSelectedIndex = _selectedIndex;
      _selectedIndex = 1;
      Global.currentSelectedIndex = 1;
    });
  }
  void _receivedLoginSucceedNotification(CustomNotification notification) {
    fetchAdData();
  }
  void _receivedSelectPageNotification (CustomNotification notification) {
    if(notification.userInfo != null){
      if(notification.userInfo?.containsKey('pageIndex') ?? false){
        setState(() {
          Global.preSelectedIndex = _selectedIndex;
          _selectedIndex = notification.userInfo!['pageIndex'];
          Global.currentSelectedIndex = _selectedIndex;
        });
      }
    }
  }

  void fetchAdData() async {
    try {
      if(GlobalData().isLogin) {
        CarStatusResponseModel carStatusResponseModel = await carAPI.getDefaultCarInfoAndStatus();
        final carInfoModel = carStatusResponseModel.carInfo;
        if(carInfoModel != null) {
          _servicrHandleListModel = await userAPI
              .getAppFunctionIconList('${UserHandleListType.publishHasCar.value}');
        }else {
          _servicrHandleListModel = await userAPI
              .getAppFunctionIconList('${UserHandleListType.publishNoCar.value}');
        }
      }else {
        _servicrHandleListModel = await userAPI
            .getAppFunctionIconList('${UserHandleListType.publishNoCar.value}');
      }
      setState(() {

      });
    } catch (e) {
      // 处理异常
      LogManager().debug('$e');
    }
  }

  @override
  Widget build(BuildContext context) {
    var json = SpUtil().getJSON(SP_SPLASH_ADVERTISE_DATA_KEY);
    if (json != null) {
      var advertise = Advertise.fromJson(json);
      String advertiseImage = advertise.advertiseImage ?? '';
      if (advertiseImage.isNotEmpty) {
        precacheImage(CachedNetworkImageProvider(advertiseImage), context);
      }
    }
    const double bottomNavigationBarHeight = kBottomNavigationBarHeight;
    final double bottomSafeAreaHeight = MediaQuery.of(context).padding.bottom;
    return Scaffold(
      body: Stack(
        children: [
         // 主页面内容
          LazyIndexedStack(
            index: _selectedIndex,
            children: _pages,
          ),

          // 底部导航栏
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: BottomNavigationBar(
              items: _tabs.asMap().entries.map((entry) {
                int index = entry.key;
                TabData tab = entry.value;
                bool isSelected = _selectedIndex == index;
                Key lottieKey = UniqueKey();
                return  BottomNavigationBarItem(
                  icon: tab.label == "" ? Container(width: 40,) : tab.selectedLottie != null
                      ? Lottie.asset(
                          tab.selectedLottie!,
                          width: 24,
                          height: 24,
                          animate: isSelected,
                          repeat: false,
                          key:
                              isSelected ? lottieKey : Key(tab.selectedLottie!),
                        )
                      : Image.asset(
                          isSelected ? tab.selectedImage : tab.unselectedImage,
                          width: tab.page == Container() ? 0 : 18,
                          height: tab.page == Container() ? 0 : 18,
                        ),
                  label: tab.label,
                );
              }).toList(),
              currentIndex: _selectedIndex,
              onTap: (index) {
                if(index != 2){
                  setState(() {
                    Global.preSelectedIndex = _selectedIndex;
                    _selectedIndex = index;
                    Global.currentSelectedIndex = index;
                  });
                }
              },
              selectedItemColor: const Color(0xFF383a40),
              unselectedItemColor: const Color(0xFF383a40),
              selectedLabelStyle: const TextStyle(fontSize: 12),
              unselectedLabelStyle: const TextStyle(fontSize: 12),
              type: BottomNavigationBarType.fixed,
            ),
          ),
          if (widget.centerButtonEnable) ...[
            // 中心按钮和弹窗
            if (_isCenterButtonSelect)
              Positioned.fill(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _isCenterButtonSelect = false;
                    });
                  },
                  child: Container(
                    color: Colors.black.withOpacity(0.3),
                  ),
                ),
              ),
            if (_isCenterButtonSelect)
              Positioned(
                bottom: kBottomNavigationBarHeight +
                    MediaQuery.of(context).padding.bottom,
                left: 40,
                right: 40,
                child: TabbarPublishWidget(
                  serviceList: _servicrHandleListModel,
                  toWeb: (url){
                    setState(() {
                      _isCenterButtonSelect = false;
                    });
                    NavigatorAction.init(context,view: WebViewPage(url: url,));
                  },
                  toKefu: () {
                    LogManager().debug("kefu");
                  setState(() {
                    _isCenterButtonSelect = false;
                  });
                   ShowAction.init(context).showDialog(msg: "请选择联系方式",leftStr: "客服热线",rightStr: "联系客服",).then((value) {
                     if(value!=null){
                       if(value){
                         if (!GlobalData().isLogin) {
                           showNotLoginAlertDialog(context);
                           return;
                         }
                         String url =
                         WebViewURLTool.kefuURLStrWithGroup(
                             KefuGroup.mm.value, '', '');
                         JumpTool().openWeb(context, url, true);
                       }else{
                         PhoneCallManager()
                             .callTelWithPhoneNumber(
                             context,
                             Constant
                                 .HOT_LINE_TELEPHONE_NUMBER);
                       }
                     }
                   });

                },
                ),
              ),
            // 自定义中心按钮
            Positioned(
              bottom: MediaQuery.of(context).padding.bottom,
              left: MediaQuery.of(context).size.width / 2 - kBottomNavigationBarHeight/2,
              child: Container(
                height: kBottomNavigationBarHeight,
                width: kBottomNavigationBarHeight,
                color: Colors.transparent,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Positioned(
                      top: 8,
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            _isCenterButtonSelect = !_isCenterButtonSelect;
                          });
                        },
                        child: Container(
                          width: 40,
                          height: 35,
                          decoration: BoxDecoration(
                            color: Colors.red,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Center(
                            child: _isCenterButtonSelect
                                ? const Icon(
                                    Icons.close_rounded,
                                    color: Colors.white,
                                    size: 21,
                                  )
                                : const Icon(
                                    Icons.add_rounded,
                                    color: Colors.white,
                                  ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  List<ButtonModel> getPopOverButtonList() {
    List<ButtonModel> list = [];
    list.add(ButtonModel(
        title: '客服热线',
        imageUrl: 'assets/images/profile_page/menu_hotline.png',
        type: ButtonType.customerService));
    list.add(ButtonModel(
        title: '联系客服',
        imageUrl: 'assets/images/profile_page/menu_service.png',
        type: ButtonType.onlineChat));
    return list;
  }

  void showNotLoginAlertDialog(BuildContext context) {
    DialogManager().showCustomDialog(
      CustomDialog(
        content: "您还没有登录",
        contentFontSize: 18,
        contentColor: Colors.black,
        buttonHeight: 70,
        horizontalPadding: 20,
        buttons: [
          DialogButton(
              label: "继续逛逛",
              onPressed: () {},
              backgroundColor: Colors.white,
              textColor: const Color(0xFF9B9DA9)),
          DialogButton(
              label: "去登录",
              onPressed: () {
                LoginManager().showLoginModal();
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xffea0029)),
        ],
      ),
    );
  }

  @override
  void dispose() {
    // TODO: implement dispose
    NotificationManager().unsubscribe(Constant.NOTIFICATION_LOGOUT_SUCCEED,
        _receivedLogoutSucceedNotification);
    NotificationManager().subscribe(Constant.NOTIFICATION_MAIN_PAGE_SELECT_PAGE,_receivedSelectPageNotification);
    NotificationManager().unsubscribe(
        Constant.NOTIFICATION_LOGIN_SUCCEED, _receivedLoginSucceedNotification);
    super.dispose();
  }
}

class TabData {
  final String label;
  final String selectedImage;
  final String unselectedImage;
  final String? selectedLottie;
  final Widget page;

  TabData(this.label, this.selectedImage, this.unselectedImage,
      this.selectedLottie, this.page);
}
