export declare class LiveShift {
    private mUrl;
    private mTimeLineUrl;
    private mFormat;
    private mCoverPath;
    private mTitle;
    constructor();
    /**
     * 设置视频格式
     * @param format 视频格式
     */
    /****
     * Set the format of the video.
     * @param format The format of the video.
     */
    setFormat(o33: string): void;
    /**
     * 获取视频格式
     * @return 视频格式
     */
    /****
     * Query the format of the video.
     * @return The format of the video.
     */
    getFormat(): String;
    /**
     * 获取播放地址
     * @return 播放地址
     */
    /****
     * Query the playback URL.
     * @return The playback URL.
     */
    getUrl(): string;
    /**
     * 设置播放地址
     * @param url 播放地址
     */
    /****
     * Set the playback URL.
     * @param url The playback URL.
     */
    setUrl(n33: string): void;
    /**
     * 获取封面地址
     * @return 封面地址
     */
    /****
     * Query the URL of the album cover.
     * @return The URL of the album cover.
     */
    getCoverPath(): string;
    /**
     * 设置封面地址
     * @param coverPath 封面地址
     */
    /****
     * Set the URL of the album cover.
     * @param coverPath The URL of the album cover.
     */
    setCoverPath(m33: string): void;
    /**
     * 获取标题
     * @return 标题
     */
    /****
     * Query the title of the video.
     * @return The title of the video.
     */
    getTitle(): string;
    /**
     * 设置标题
     * @param title 标题
     */
    /****
     * Set the title of the video.
     * @param title The title of the video.
     */
    setTitle(l33: string): void;
    /**
     * 获取时移请求地址
     * @return 时移请求地址
     */
    /****
     * Query the timeshift request URL.
     * @return The timeshift request URL.
     */
    getTimeLineUrl(): string;
    /**
     * 设置时移请求地址
     * @param timeLineUrl 时移请求地址
     */
    /****
     * Set the timeshift request URL.
     * @param timeLineUrl The timeshift request URL.
     */
    setTimeLineUrl(k33: string): void;
}
