import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/models/car/car_status_model.dart';

class BatteryStatusWidget extends StatefulWidget {
  final CarStatusModel? statusModel;
  final VoidCallback? onChargeMapClicked;

  const BatteryStatusWidget({
    Key? key,
    this.statusModel,
    this.onChargeMapClicked,
  }) : super(key: key);

  @override
  BatteryStatusWidgetState createState() => BatteryStatusWidgetState();
}

class BatteryStatusWidgetState extends State<BatteryStatusWidget> {
  // 获取电量百分比
  String getBatteryPercentage() {
    if (widget.statusModel?.batHealth != null) {
      return '${widget.statusModel!.batHealth}%';
    }
    return '0%';
  }

  // 获取电量数值（不带%）
  String getBatteryNumber() {
    if (widget.statusModel?.batHealth != null) {
      return '${widget.statusModel!.batHealth}';
    }
    return '0';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 180,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: const [
          BoxShadow(
            color: Color(0x0a000000),
            spreadRadius: 0,
            blurRadius: 4,
            offset: Offset(0, 0),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            // 上半部分：电量显示区域
            Expanded(
              child: Row(
                children: [
                  const SizedBox(width: 20),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        textBaseline: TextBaseline.alphabetic,
                        crossAxisAlignment: CrossAxisAlignment.baseline,
                        children: [
                          Text(
                            getBatteryNumber(),
                            style: const TextStyle(
                              color: Color(0xff3C8CF6),
                              fontSize: 30,
                              fontWeight: FontWeight.bold,
                              height: 1.0,
                            ),
                          ),
                          const Text(
                            "%",
                            style: TextStyle(
                              color: Color(0xff3C8CF6),
                              fontSize: 10,
                            ),
                          ),
                        ],
                      ),
                      const Text(
                        "剩余电量",
                        style: TextStyle(
                          color: Color(0xff383A40),
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          height: 1.0,
                        ),
                      ),
                    ],
                  ),
                  const Spacer(),
                  buildChargingWidget(),
                  const SizedBox(width: 20),
                ],
              ),
            ),
            // 分隔线
            const Divider(
              thickness: 1,
              color: Color(0x0D000000),
              indent: 20,
              endIndent: 20,
              height: 0,
            ),
            // 下半部分：充电地图按钮
            Expanded(
              child: Center(
                child: buildBatteryBottomWidget(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildChargingWidget() {
    double batteryPercent = double.tryParse(getBatteryNumber()) ?? 0.0;
    Color color;
    if (batteryPercent < 20) {
      color = Colors.red;
    } else if (batteryPercent < 30) {
      color = Colors.amber;
    } else {
      color = Colors.green;
    }
    return Stack(
      alignment: Alignment.center,
      children: [
        // 电池边框
        Container(
          width: 25,
          height: 25,
          decoration: BoxDecoration(
            border: Border.all(color: const Color(0xffE5E5E5), width: 2),
            borderRadius: BorderRadius.circular(3),
          ),
          child: Stack(
            children: [
              // 电池正极
              Positioned(
                top: -2,
                left: 8,
                right: 8,
                child: Container(
                  height: 3,
                  decoration: BoxDecoration(
                    color: const Color(0xffE5E5E5),
                    borderRadius: BorderRadius.circular(1),
                  ),
                ),
              ),
              // 电池电量显示
              Positioned(
                right: 2,
                left: 2,
                bottom: 2,
                top: 2,
                child: Align(
                  alignment: Alignment.bottomCenter,
                  child: FractionallySizedBox(
                    heightFactor: batteryPercent / 100,
                    child: Container(
                      decoration: BoxDecoration(
                        color: color,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget buildBatteryBottomWidget() {
    return ElevatedButton(
      onPressed: widget.onChargeMapClicked,
      style: ElevatedButton.styleFrom(
        padding: EdgeInsets.zero,
        elevation: 0,
        fixedSize: const Size(120, 50),
        backgroundColor: const Color(0xFFF9F9F9),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(25),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.location_on,
            size: 20,
            color: Color(0xFF383A40),
          ),
          SizedBox(width: 6),
          Text(
            '充电地图',
            style: TextStyle(
              color: Color(0xFF383A40),
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }
}
