import type { ColorString } from "../../g1/a2"; import type ImageEntity from "../o/s"; import BaseUI from "./q1";         export default class ImageUI extends BaseUI { private mImageUI; constructor();         setDrawableResource(x46: ImageEntity): void; private updateImageBitMap;         setBmpResourceId(v46: number): void;         setMaskResource(u46: ImageEntity): void; private updateMaskBitMap;         setColor(s46: ColorString): void; } 