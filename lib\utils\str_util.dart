import 'dart:convert';
import 'dart:ffi';
import 'dart:math';
import 'dart:typed_data';
import 'package:encrypt/encrypt.dart' as encrypt;
import 'package:wuling_flutter_app/utils/exceptions.dart';

import 'package:intl/intl.dart';

import 'manager/log_manager.dart';

class StrUtil {
  /// 将字符串的首字母转换为大写。
  ///
  /// - str: 要转换的字符串。
  /// - 返回值: 首字母大写后的字符串。
  /// - 示例：
  /// ```dart
  /// var capitalized = StrUtil.capitalize("hello");
  /// LogManager().debug(capitalized); // 输出: "Hello"
  /// ```
  static String capitalize(String str) {
    if (str.isEmpty) return str;
    return str[0].toUpperCase() + str.substring(1);
  }

  /// 将字符串中的特定单词替换为大写形式。
  ///
  /// - str: 原始字符串。
  /// - words: 要替换为大写的单词列表。
  /// - 返回值: 替换后的字符串。
  /// - 示例：
  /// ```dart
  /// var capitalizedWords = StrUtil.capitalizeWords("hello world", ["world"]);
  /// LogManager().debug(capitalizedWords); // 输出: "hello WORLD"
  /// ```
  static String capitalizeWords(String str, List<String> words) {
    for (var word in words) {
      str = str.replaceAll(word, word.toUpperCase());
    }
    return str;
  }

  /// 检查字符串是否包含中文字符。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串包含中文字符，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var containsChinese = StrUtil.containsChinese("你好");
  /// LogManager().debug(containsChinese); // 输出: true
  /// ```
  static bool containsChinese(String str) {
    return RegExp(r'[\u4e00-\u9fa5]').hasMatch(str);
  }

  /// 计算字符串中特定字符的出现次数。
  ///
  /// - str: 原始字符串。
  /// - char: 要计算的字符。
  /// - 返回值: 字符的出现次数。
  /// - 示例：
  /// ```dart
  /// var count = StrUtil.countCharOccurrences("hello world", "l");
  /// LogManager().debug(count); // 输出: 3
  /// ```
  static int countCharOccurrences(String str, String char) {
    return str.split(char).length - 1;
  }

  /// 计算字符串中特定字符或子串的出现次数。
  ///
  /// - str: 原始字符串。
  /// - sub: 要计算的字符或子串。
  /// - 返回值: 字符或子串的出现次数。
  /// - 示例：
  /// ```dart
  /// var count = StrUtil.countOccurrences("hello world", "o");
  /// LogManager().debug(count); // 输出: 2
  /// ```
  static int countOccurrences(String str, String sub) {
    return str.split(sub).length - 1;
  }

  /// 将字符串转换为驼峰命名格式。
  ///
  /// - str: 要转换的字符串。
  /// - 返回值: 驼峰命名格式的字符串。
  /// - 示例：
  /// ```dart
  /// var camelCaseStr = StrUtil.camelCase("hello world");
  /// LogManager().debug(camelCaseStr); // 输出: "helloWorld"
  /// ```
  static String camelCase(String str) {
    return str.toLowerCase().split(' ').map((word) {
      return word.isEmpty ? word : capitalize(word);
    }).join();
  }

  /// 检查字符串是否包含特定的子串。
  ///
  /// - str: 原始字符串。
  /// - sub: 检查的子串。
  /// - 返回值: 如果字符串包含该子串，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var containsSub = StrUtil.contains("hello world", "world");
  /// LogManager().debug(containsSub); // 输出: true
  /// ```
  static bool contains(String str, String sub) {
    return str.contains(sub);
  }

  /// 检查字符串是否以特定子串结尾。
  ///
  /// - str: 原始字符串。
  /// [suffix]: 检查的子串。
  /// - 返回值: 如果字符串以该子串结尾，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var endsWithSuffix = StrUtil.endsWith("hello world", "world");
  /// LogManager().debug(endsWithSuffix); // 输出: true
  /// ```
  static bool endsWith(String str, String suffix) {
    return str.endsWith(suffix);
  }

  /// 将字符串中的HTML特殊字符转义。
  ///
  /// - str: 要转义的字符串。
  /// - 返回值: 转义后的字符串。
  /// - 示例：
  /// ```dart
  /// var escapedHtml = StrUtil.escapeHtml("<div>Hello World</div>");
  /// LogManager().debug(escapedHtml); // 输出: "&lt;div&gt;Hello World&lt;/div&gt;"
  /// ```
  static String escapeHtml(String str) {
    return htmlEscape.convert(str);
  }

  /// 将字符串中的数字提取为列表。
  ///
  /// - str: 原始字符串。
  /// - 返回值: 包含字符串中所有数字的列表。
  /// - 示例：
  /// ```dart
  /// var numbers = StrUtil.extractNumbers("hello 123 world 456");
  /// LogManager().debug(numbers); // 输出: [123, 456]
  /// ```
  static List<int> extractNumbers(String str) {
    return RegExp(r'\d+')
        .allMatches(str)
        .map((match) => int.parse(match.group(0)!))
        .toList();
  }

  /// 从Base64格式解码字符串。
  ///
  /// - str: 要解码的Base64字符串。
  /// - 返回值: 解码后的字符串。
  /// - 示例：
  /// ```dart
  /// var decodedString = StrUtil.fromBase64("aGVsbG8gd29ybGQ=");
  /// LogManager().debug(decodedString); // 输出: "hello world"
  /// ```
  static String fromBase64(String str) {
    return utf8.decode(base64Decode(str));
  }

  /// 解析INI格式的字符串。
  ///
  /// - str: INI格式的字符串。
  /// - 返回值: 解析后的Map。
  ///
  /// - 示例：
  ///
  /// ```dart
  /// var iniData = '''
  ///   ; this is a comment
  ///   key1 = value1
  ///   key2 = 5
  ///
  ///   [section1]
  ///   keyA = valueA
  ///   keyB = 10
  ///
  ///   [section2]
  ///   keyX = valueX
  ///   keyY = 20
  /// ''';
  ///
  /// var parsedMap = StrUtil.fromIni(iniData);
  /// LogManager().debug(parsedMap);
  /// ```
  ///
  static Map<String, dynamic> fromIni(String str) {
    var result = <String, dynamic>{};
    String? currentSection;

    for (var line in str.split('\n')) {
      line = line.trim();

      // 忽略空行和注释
      if (line.isEmpty || line.startsWith(';')) continue;

      // 处理区段
      if (line.startsWith('[') && line.endsWith(']')) {
        currentSection = line.substring(1, line.length - 1);
        result[currentSection] = <String, dynamic>{};
      } else {
        // 处理键值对
        var index = line.indexOf('=');
        if (index != -1) {
          var key = line.substring(0, index).trim();
          var value = line.substring(index + 1).trim();

          // 尝试转换为数字
          var numericValue = num.tryParse(value);
          var finalValue = numericValue ?? value;

          if (currentSection != null) {
            result[currentSection][key] = finalValue;
          } else {
            result[key] = finalValue;
          }
        }
      }
    }
    return result;
  }

  /// 检查字符串是否是有效的YAML格式。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串是有效的YAML格式，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isValidYaml = StrUtil.isYaml("key: value");
  /// LogManager().debug(isValidYaml); // 输出: true
  /// ```
  static bool isYaml(String str) {
    // YAML格式检查通常需要解析库，这里仅提供一个简单的实现示例
    return str.trim().contains(':');
  }

  /// 检查字符串是否是有效的XML格式。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串是有效的XML格式，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isValidXml = StrUtil.isXml("<tag></tag>");
  /// LogManager().debug(isValidXml); // 输出: true
  /// ```
  static bool isXml(String str) {
    return RegExp(r'^<\?xml.*\?>|<(\w+)(\s+.*?>|>).*</\1>$', dotAll: true)
        .hasMatch(str.trim());
  }

  /// 检查字符串是否是有效的HTML格式。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串是有效的HTML格式，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isValidHtml = StrUtil.isHtml("<html></html>");
  /// LogManager().debug(isValidHtml); // 输出: true
  /// ```
  static bool isHtml(String str) {
    return RegExp(r'<!DOCTYPE html>|<html.*>.*</html>', dotAll: true)
        .hasMatch(str.trim());
  }

  /// 检查字符串是否是有效的SVG格式。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串是有效的SVG格式，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isValidSvg = StrUtil.isSvg("<svg></svg>");
  /// LogManager().debug(isValidSvg); // 输出: true
  /// ```
  static bool isSvg(String str) {
    return str.trim().startsWith('<svg') && str.trim().endsWith('</svg>');
  }

  /// 将字符串转换为逗号分隔的数字格式。
  ///
  /// - str: 数字字符串。
  /// - 返回值: 逗号分隔的数字格式字符串。
  /// - 示例：
  /// ```dart
  /// var formattedNumber = StrUtil.formatNumberWithCommas("1234567");
  /// LogManager().debug(formattedNumber); // 输出: "1,234,567"
  /// ```
  static String formatNumberWithCommas(String str) {
    return str.replaceAllMapped(
      RegExp(r'(\d)(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]},',
    );
  }

  /// 获取字符串的哈希值。
  ///
  /// - str: 要哈希的字符串。
  /// - 返回值: 字符串的哈希值。
  /// - 示例：
  /// ```dart
  /// var hashCode = StrUtil.getHashCode("hello");
  /// LogManager().debug(hashCode); // 输出: 字符串"hello"的哈希值
  /// ```
  static int getHashCode(String str) {
    return str.hashCode;
  }

  /// 检查字符串中的字符是否按照字母表顺序排列。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符按字母表顺序排列，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isAlphabetical = StrUtil.isZAlphabetical("abc");
  /// LogManager().debug(isAlphabetical); // 输出: true
  /// ```
  static bool isZAlphabetical(String str) {
    for (int i = 1; i < str.length; i++) {
      if (str[i - 1].compareTo(str[i]) > 0) return false;
    }
    return true;
  }

  /// 检查字符串是否是有效的十六进制数。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串是有效的十六进制数，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isHexadecimal = StrUtil.isHex("1a3f");
  /// LogManager().debug(isHexadecimal); // 输出: true
  /// ```
  static bool isHex(String str) {
    const hexPattern = r'^[0-9a-fA-F]+$';
    return RegExp(hexPattern).hasMatch(str);
  }

  /// 检查字符串是否只包含字母。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串只包含字母，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isAlphabetic = StrUtil.isAlpha("Hello");
  /// LogManager().debug(isAlphabetic); // 输出: true
  /// ```
  static bool isAlpha(String str) {
    final alphaPattern = RegExp(r'^[a-zA-Z]+$');
    return alphaPattern.hasMatch(str);
  }

  /// 检查字符串是否只包含字母和数字。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串只包含字母和数字，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isAlphanumeric = StrUtil.isAlphanumeric("Hello123");
  /// LogManager().debug(isAlphanumeric); // 输出: true
  /// ```
  static bool isAlphanumeric(String str) {
    final alphanumericPattern = RegExp(r'^[a-zA-Z0-9]+$');
    return alphanumericPattern.hasMatch(str);
  }

  /// 检查字符串是否只包含数字。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串只包含数字，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isNumeric = StrUtil.isNumeric("12345");
  /// LogManager().debug(isNumeric); // 输出: true
  /// ```
  static bool isNumeric(String str) {
    return double.tryParse(str) != null;
  }

  /// 检查字符串是否是有效的URL。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串是有效的URL，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isValidUrl = StrUtil.isUrl("https://www.example.com");
  /// LogManager().debug(isValidUrl); // 输出: true
  /// ```
  static bool isUrl(String str) {
    const urlPattern = r'^(https?:\/\/)?[\w-]+(\.[\w-]+)+\.?(:\d+)?(\/\S*)?$';

    return RegExp(urlPattern).hasMatch(str);
  }

  /// 检查字符串是否是有效的电子邮件地址。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串是有效的电子邮件地址，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isValidEmail = StrUtil.isEmail("<EMAIL>");
  /// LogManager().debug(isValidEmail); // 输出: true
  /// ```
  static bool isEmail(String str) {
    const emailPattern = r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$';
    return RegExp(emailPattern).hasMatch(str);
  }

  /// 检查字符串是否是有效的Flutter资源路径。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串是有效的Flutter资源路径，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isFlutterAsset = StrUtil.isAsset("assets/images/logo.png");
  /// LogManager().debug(isFlutterAsset); // 输出: true
  /// ```
  static bool isAsset(String str) {
    return str.startsWith('assets/');
  }

  /// 判断字符串是否是纯数字。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串是纯数字，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isDigitOnly = StrUtil.isDigitOnly("123456");
  /// LogManager().debug(isDigitOnly); // 输出: true
  /// ```
  static bool isDigitOnly(String str) {
    return RegExp(r'^\d+$').hasMatch(str);
  }

  /// 判断字符串是否只包含空格和换行符。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串只包含空格和换行符，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isWhitespace = StrUtil.isWhitespace("\n   \n");
  /// LogManager().debug(isWhitespace); // 输出: true
  /// ```
  static bool isWhitespace(String str) {
    return str.trim().isEmpty;
  }

  /// 检查字符串是否为空。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串为空，则返回true，否则返回false。
  static bool isEmpty(String? str) {
    return str?.isEmpty ?? true;
  }

  /// 检查字符串是否为null或空。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串为null或空，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isNullOrEmpty = StrUtil.isNullOrEmpty(null);
  /// LogManager().debug(isNullOrEmpty); // 输出: true
  /// ```
  static bool isNullOrEmpty(String? str) {
    return str == null || str == 'null' || str.isEmpty;
  }

  /// 检查字符串是否为null、空或仅包含空白字符。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串为null、空或仅包含空白字符，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isEmptyOrWhitespace = StrUtil.isNullEmptyOrWhitespace("   ");
  /// LogManager().debug(isEmptyOrWhitespace); // 输出: true
  /// ```
  static bool isNullEmptyOrWhitespace(String? str) {
    return str == null || str.isEmpty || str.trim().isEmpty;
  }

  /// 检查字符串是否是有效的用户名（字母、数字、下划线，5-15字符）。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串是有效的用户名，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isValidUsername = StrUtil.isValidUsername("user_123");
  /// LogManager().debug(isValidUsername); // 输出: true
  /// ```
  static bool isValidUsername(String str) {
    return RegExp(r'^\w{5,15}$').hasMatch(str);
  }

  /// 检查字符串是否不为空。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串不为空，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isNotEmpty = StrUtil.isNotEmpty("Hello");
  /// LogManager().debug(isNotEmpty); // 输出: true
  /// ```
  static bool isNotEmpty(String? str) {
    return str?.isNotEmpty ?? false;
  }

  /// 检查字符串是否只包含空格。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串只包含空格，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isBlank = StrUtil.isBlank("   ");
  /// LogManager().debug(isBlank); // 输出: true
  /// ```
  static bool isBlank(String str) {
    return str.trim().isEmpty;
  }

  /// 检查字符串是否全部由小写字母组成。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串全部由小写字母组成，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isLowerCase = StrUtil.isLowerCase("hello");
  /// LogManager().debug(isLowerCase); // 输出: true
  /// ```
  static bool isLowerCase(String str) {
    return str == str.toLowerCase();
  }

  /// 检查字符串是否全部由大写字母组成。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串全部由大写字母组成，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isUpperCase = StrUtil.isUpperCase("HELLO");
  /// LogManager().debug(isUpperCase); // 输出: true
  /// ```
  static bool isUpperCase(String str) {
    return str == str.toUpperCase();
  }

  /// 检查字符串是否是一个回文。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串是回文，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isPalindrome = StrUtil.isPalindrome("madam");
  /// LogManager().debug(isPalindrome); // 输出: true
  /// ```
  static bool isPalindrome(String str) {
    String reversed = reverse(str); // 使用之前定义的reverse函数
    return str == reversed;
  }

  /// 检查字符串是否是有效的日期时间格式。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串是有效的日期时间格式，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isDateTime = StrUtil.isDateTime("2021-01-01");
  /// LogManager().debug(isDateTime); // 输出: true
  /// ```
  static bool isDateTime(String str) {
    try {
      DateTime.parse(str);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 检查字符串是否是有效的信用卡号。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串是有效的信用卡号，则返回true，否则返回false。
  /// 检查字符串是否是有效的信用卡号。
  /// - 示例：
  /// ```dart
  /// var isCreditCard = StrUtil.isCreditCard("1234567890123456");
  /// LogManager().debug(isCreditCard); // 输出: false
  /// ```
  static bool isCreditCard(String str) {
    final creditCardPattern = RegExp(r'^[0-9]{16}$');
    return creditCardPattern.hasMatch(str);
  }

  /// 检查字符串是否是有效的文件路径。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串是有效的文件路径，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isFilePath = StrUtil.isFilePath("C:/Users/<USER>");
  /// LogManager().debug(isFilePath); // 输出: true
  /// ```
  static bool isFilePath(String str) {
    final filePathPattern =
    RegExp(r'^[a-zA-Z0-9_\-/\\]+[a-zA-Z0-9]+\.[a-zA-Z0-9]+$');
    return filePathPattern.hasMatch(str);
  }

  /// 检查字符串是否是有效的IPv4地址。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串是有效的IPv4地址，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isIPv4 = StrUtil.isIPv4("***********");
  /// LogManager().debug(isIPv4); // 输出: true
  /// ```
  static bool isIPv4(String str) {
    const ipv4Pattern =
        r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$';
    return RegExp(ipv4Pattern).hasMatch(str);
  }

  /// 检查字符串是否是有效的IPv6地址。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串是有效的IPv6地址，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isIPv6 = StrUtil.isIPv6("2001:0db8:85a3:0000:0000:8a2e:0370:7334");
  /// LogManager().debug(isIPv6); // 输出: true
  /// ```
  static bool isIPv6(String str) {
    const ipv6Pattern =
        r'^\s*((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9]))))(?<![^:]:)(?<!\.)\s*$';
    return RegExp(ipv6Pattern).hasMatch(str);
  }

  /// 检查字符串是否符合Base64格式。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串符合Base64格式，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isBase64 = StrUtil.isBase64("SGVsbG8gV29ybGQ=");
  /// LogManager().debug(isBase64); // 输出: true
  /// ```
  static bool isBase64(String str) {
    const base64Pattern =
        r'^(?:[A-Za-z0-9+\/]{4})*(?:[A-Za-z0-9+\/]{2}==|[A-Za-z0-9+\/]{3}=)?$';
    return RegExp(base64Pattern).hasMatch(str);
  }

  /// 检查字符串是否是有效的JSON格式。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串是有效的JSON格式，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isJson = StrUtil.isJson('{"name":"John", "age":30}');
  /// LogManager().debug(isJson); // 输出: true
  /// ```
  static bool isJson(String str) {
    try {
      json.decode(str);
      return true;
    } catch (_) {
      return false;
    }
  }

  /// 检查字符串是否符合电话号码格式。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串符合电话号码格式，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isPhoneNumber = StrUtil.isPhoneNumber("+1234567890");
  /// LogManager().debug(isPhoneNumber); // 输出: true
  /// ```
  static bool isPhoneNumber(String str) {
    final phonePattern = RegExp(r'^(?:\+?\d{1,3})?\s?\d{3,}$');
    return phonePattern.hasMatch(str);
  }

  /// 检查字符串是否符合简单的密码规则（至少6个字符）。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串符合密码规则，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isSimplePassword = StrUtil.isSimplePassword("123456");
  /// LogManager().debug(isSimplePassword); // 输出: true
  /// ```
  static bool isSimplePassword(String str) {
    return str.length >= 6;
  }

  /// 检查字符串是否只包含空格。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串只包含空格，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isSpace = StrUtil.isSpace("   ");
  /// LogManager().debug(isSpace); // 输出: true
  /// ```
  static bool isSpace(String str) {
    return str.trim().isEmpty;
  }

  /// 检查字符串是否是有效的车牌号。
  ///
  /// - str: 要检查的字符串。
  /// - 返回值: 如果字符串是有效的车牌号，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var isLicensePlate = StrUtil.isLicensePlate("ABC1234");
  /// LogManager().debug(isLicensePlate); // 输出: true
  /// ```
  static bool isLicensePlate(String str) {
    final licensePlatePattern = RegExp(r'^[A-Z0-9]{6,8}$');
    return licensePlatePattern.hasMatch(str);
  }

  /// 将字符串转换为短横线命名格式。
  ///
  /// - str: 要转换的字符串。
  /// - 返回值: 短横线命名格式的字符串。
  /// - 示例：
  /// ```dart
  /// var kebabCaseStr = StrUtil.kebabCase("helloWorld");
  /// LogManager().debug(kebabCaseStr); // 输出: "hello-world"
  /// ```
  static String kebabCase(String str) {
    return str.replaceAllMapped(RegExp(r'[A-Z]'), (Match match) {
      return '-${match[0]!.toLowerCase()}';
    }).toLowerCase();
  }

  /// 将字符串中的特定单词替换为小写形式。
  ///
  /// - str: 原始字符串。
  /// - words: 要替换为小写的单词列表。
  /// - 返回值: 替换后的字符串。
  /// - 示例：
  /// ```dart
  /// var lowercasedWords = StrUtil.lowercaseWords("Hello World", ["world"]);
  /// LogManager().debug(lowercasedWords); // 输出: "Hello world"
  /// ```
  static String lowercaseWords(String str, List<String> words) {
    for (var word in words) {
      str = str.replaceAll(word, word.toLowerCase());
    }
    return str;
  }

  /// 将字符串中的每个单词首字母转换为小写。
  ///
  /// - str: 要处理的字符串。
  /// - 返回值: 处理后的字符串。
  /// - 示例：
  /// ```dart
  /// var lowercaseFirstLetter = StrUtil.lowercaseFirstLetterOfEachWord("Hello World");
  /// LogManager().debug(lowercaseFirstLetter); // 输出: "hello world"
  /// ```
  static String lowercaseFirstLetterOfEachWord(String str) {
    return str.replaceAllMapped(
      RegExp(r'\b\w'),
          (Match match) => match.group(0)!.toLowerCase(),
    );
  }

  /// 检查字符串是否符合指定的正则表达式。
  ///
  /// - str: 要检查的字符串。
  /// [pattern]: 正则表达式。
  /// - 返回值: 如果字符串符合正则表达式，则返回true，否则返回false。
  /// - 示例：
  /// ```dart
  /// var matchesPattern = StrUtil.matchesPattern("12345", r'\d+');
  /// LogManager().debug(matchesPattern); // 输出: true
  /// ```
  static bool matchesPattern(String str, String pattern) {
    return RegExp(pattern).hasMatch(str);
  }

  /// 将字符串转换为n倍重复的形式。
  ///
  /// - str: 原始字符串。
  /// - n: 重复的次数。
  /// - 返回值: 重复后的字符串。
  /// - 示例：
  /// ```dart
  /// var repeatedString = StrUtil.nTimesRepeat("abc", 3);
  /// LogManager().debug(repeatedString); // 输出: "abcabcabc"
  /// ```
  static String nTimesRepeat(String str, int n) {
    return List.filled(n, str).join();
  }

  /// 获取字符串中的数字。
  ///
  /// - str: 原始字符串。
  /// - 返回值: 字符串中的数字。
  /// - 示例：
  /// ```dart
  /// var onlyDigits = StrUtil.onlyDigits("abc123");
  /// LogManager().debug(onlyDigits); // 输出: "123"
  /// ```
  static String onlyDigits(String str) {
    return str.replaceAll(RegExp(r'\D'), '');
  }

  /// 将字符串左侧填充到指定长度。
  ///
  /// - str: 要填充的字符串。
  /// - length: 指定的长度。
  /// [padWith]: 用于填充的字符。
  /// - 返回值: 左侧填充后的字符串。
  /// - 示例：
  /// ```dart
  /// var paddedLeft = StrUtil.padLeft("123", 5, "0");
  /// LogManager().debug(paddedLeft); // 输出: "00123"
  /// ```
  static String padLeft(String str, int length, String padWith) {
    if (str.length >= length) return str;
    return str.padLeft(length, padWith);
  }

  /// 将字符串右侧填充到指定长度。
  ///
  /// - str: 要填充的字符串。
  /// - length: 指定的长度。
  /// [padWith]: 用于填充的字符。
  /// - 返回值: 右侧填充后的字符串。
  /// - 示例：
  /// ```dart
  /// var paddedRight = StrUtil.padRight("123", 5, "0");
  /// LogManager().debug(paddedRight); // 输出: "12300"
  /// ```
  static String padRight(String str, int length, String padWith) {
    if (str.length >= length) return str;
    return str.padRight(length, padWith);
  }

  /// 将字符串重复指定次数。
  ///
  /// - str: 要重复的字符串。
  /// [times]: 重复的次数。
  /// - 返回值: 重复后的字符串。
  /// - 示例：
  /// ```dart
  /// var repeatedString = StrUtil.repeat("abc", 3);
  /// LogManager().debug(repeatedString); // 输出: "abcabcabc"
  /// ```
  static String repeat(String str, int times) {
    return List.filled(times, str).join();
  }

  /// 生成指定长度的随机字符串。
  ///
  /// - length: 随机字符串的长度。
  /// - 返回值: 生成的随机字符串。
  /// - 示例：
  /// ```dart
  /// var randomStr = StrUtil.randomString(5);
  /// LogManager().debug(randomStr); // 输出为一个5位的字符串
  /// ```
  static String randomString(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    final rnd = Random();
    return String.fromCharCodes(Iterable.generate(
        length, (_) => chars.codeUnitAt(rnd.nextInt(chars.length))));
  }

  /// 替换字符串中的子串。
  ///
  /// - str: 原始字符串。
  /// [oldSub]: 要替换的子串。
  /// [newSub]: 新的子串。
  /// - 返回值: 替换后的字符串。
  /// - 示例：
  /// ```dart
  /// var replacedStr = StrUtil.replace("Hello world", "world", "Dart");
  /// LogManager().debug(replacedStr); // 输出: "Hello Dart"
  /// ```
  static String replace(String str, String oldSub, String newSub) {
    return str.replaceAll(oldSub, newSub);
  }

  /// 将字符串中的特定单词替换为指定的映射。
  ///
  /// - str: 原始字符串。
  /// - wordMap: 单词映射，例如 {'hello': 'hi', 'world': 'earth'}。
  /// - 返回值: 替换后的字符串。
  /// - 示例：
  /// ```dart
  /// var replacedWords = StrUtil.replaceWords("hello world", {"hello": "hi", "world": "Dart"});
  /// LogManager().debug(replacedWords); // 输出: "hi Dart"
  /// ```
  static String replaceWords(String str, Map<String, String> wordMap) {
    wordMap.forEach((key, value) {
      str = str.replaceAll(RegExp('\\b$key\\b'), value);
    });
    return str;
  }

  /// 将字符串中的特定模式替换为回调函数返回的字符串。
  ///
  /// - str: 原始字符串。
  /// - pattern: 要替换的模式（正则表达式）。
  /// - callback: 回调函数，接收匹配对象作为参数。
  /// - 返回值: 替换后的字符串。
  /// - 示例：
  /// ```dart
  /// var replacedCallback = StrUtil.replaceWithCallback("12345", r'\d', (m) => "(" + m.group(0)! + ")");
  /// LogManager().debug(replacedCallback); // 输出: "(1)(2)(3)(4)(5)"
  /// ```
  static String replaceWithCallback(
      String str, String pattern, String Function(Match match) callback) {
    return str.replaceAllMapped(RegExp(pattern), callback);
  }

  /// 移除字符串中的特定子串。
  ///
  /// - str: 原始字符串。
  /// - sub: 要移除的子串。
  /// - 返回值: 移除特定子串后的字符串。
  /// - 示例：
  /// ```dart
  /// var removedSubstring = StrUtil.removeSubstring("Hello world", "world");
  /// LogManager().debug(removedSubstring); // 输出: "Hello "
  /// ```
  static String removeSubstring(String str, String sub) {
    return str.replaceAll(sub, '');
  }

  /// 移除字符串中的所有非数字字符。
  ///
  /// - str: 原始字符串。
  /// - 返回值: 只包含数字的字符串。
  /// - 示例：
  /// ```dart
  /// var removedNonNumeric = StrUtil.removeNonNumeric("abc123");
  /// LogManager().debug(removedNonNumeric); // 输出: "123"
  /// ```
  static String removeNonNumeric(String str) {
    return str.replaceAll(RegExp(r'\D'), '');
  }

  /// 移除字符串中的HTML标签。
  ///
  /// - str: 原始字符串。
  /// - 返回值: 移除HTML标签后的字符串。
  /// - 示例：
  /// ```dart
  /// var removedHtmlTags = StrUtil.removeHtmlTags("<p>Hello World</p>");
  /// LogManager().debug(removedHtmlTags); // 输出: "Hello World"
  /// ```
  static String removeHtmlTags(String str) {
    return str.replaceAll(RegExp(r'<[^>]*>'), '');
  }

  /// 将字符串的每个字符替换为指定的映射字符。
  ///
  /// - str: 原始字符串。
  /// - mapping: 字符映射，例如 {'a': '1', 'b': '2'}。
  /// - 返回值: 替换后的字符串。
  /// - 示例：
  /// ```dart
  /// var replacedChars = StrUtil.replaceChars("ab", {"a": "1", "b": "2"});
  /// LogManager().debug(replacedChars); // 输出: "12"
  /// ```
  static String replaceChars(String str, Map<String, String> mapping) {
    mapping.forEach((key, value) {
      str = str.replaceAll(key, value);
    });
    return str;
  }

  /// 将字符串中的特定单词替换为另一个单词。
  ///
  /// - str: 原始字符串。
  /// - oldWord: 要替换的单词。
  /// - newWord: 新的单词。
  /// - 返回值: 替换后的字符串。
  /// - 示例：
  /// ```dart
  /// var replacedWord = StrUtil.replaceWord("Hello world", "world", "Dart");
  /// LogManager().debug(replacedWord); // 输出: "Hello Dart"
  /// ```
  static String replaceWord(String str, String oldWord, String newWord) {
    return str.replaceAll(RegExp('\\b$oldWord\\b'), newWord);
  }

  /// 移除字符串中的所有数字。
  ///
  /// - str: 原始字符串。
  /// - 返回值: 移除数字后的字符串。
  /// - 示例：
  /// ```dart
  /// var removedDigits = StrUtil.removeDigits("abc123");
  /// LogManager().debug(removedDigits); // 输出: "abc"
  /// ```
  static String removeDigits(String str) {
    return str.replaceAll(RegExp(r'\d'), '');
  }

  /// 反转字符串。
  ///
  /// - str: 要反转的字符串。
  /// - 返回值: 反转后的字符串。
  /// - 示例：
  /// ```dart
  /// var reversedStr = StrUtil.reverse("hello");
  /// LogManager().debug(reversedStr); // 输出: "olleh"
  /// ```
  static String reverse(String str) {
    return String.fromCharCodes(str.runes.toList().reversed);
  }

  /// 将字符串转换为反转的蛇形命名格式。
  ///
  /// - str: 要转换的字符串。
  /// - 返回值: 反转的蛇形命名格式的字符串。
  /// - 示例：
  /// ```dart
  /// var reversedSnakeCaseStr = StrUtil.reverseSnakeCase("hello_world");
  /// LogManager().debug(reversedSnakeCaseStr); // 输出: "HELLO-WORLD"
  /// ```
  static String reverseSnakeCase(String str) {
    return str.split('_').map((word) => word.toUpperCase()).join('-');
  }

  /// 从字符串中移除特定字符。
  ///
  /// - str: 原始字符串。
  /// - char: 要移除的字符。
  /// - 返回值: 处理后的字符串。
  /// - 示例：
  /// ```dart
  /// var removedCharStr = StrUtil.removeChar("hello", "l");
  /// LogManager().debug(removedCharStr); // 输出: "heo"
  /// ```
  static String removeChar(String str, String char) {
    return str.replaceAll(char, '');
  }

  /// 移除字符串中的所有空格。
  ///
  /// - str: 要处理的字符串。
  /// - 返回值: 移除空格后的字符串。
  /// - 示例：
  /// ```dart
  /// var removedSpacesStr = StrUtil.removeSpaces("hello world");
  /// LogManager().debug(removedSpacesStr); // 输出: "helloworld"
  /// ```
  static String removeSpaces(String str) {
    return str.replaceAll(' ', '');
  }

  /// 将多个空格替换为单个空格。
  ///
  /// - str: 原始字符串。
  /// - 返回值: 替换后的字符串。
  /// - 示例：
  /// ```dart
  /// var singleSpaceStr = StrUtil.replaceMultipleSpacesWithSingle("hello   world");
  /// LogManager().debug(singleSpaceStr); // 输出: "hello world"
  /// ```
  static String replaceMultipleSpacesWithSingle(String str) {
    return str.replaceAll(RegExp(r'\s+'), ' ');
  }

  /// 移除字符串中的中文字符。
  ///
  /// - str: 原始字符串。
  /// - 返回值: 移除中文字符后的字符串。
  /// - 示例：
  /// ```dart
  /// var removedChineseStr = StrUtil.removeChinese("hello世界");
  /// LogManager().debug(removedChineseStr); // 输出: "hello"
  /// ```
  static String removeChinese(String str) {
    return str.replaceAll(RegExp(r'[\u4e00-\u9fa5]'), '');
  }

  /// 将字符串中的换行符转换为特定的字符串。
  ///
  /// - str: 原始字符串。
  /// - replacement: 替换换行符的字符串。
  /// - 返回值: 替换后的字符串。
  /// - 示例：
  /// ```dart
  /// var replacedNewLinesStr = StrUtil.replaceNewLines("hello\nworld", " ");
  /// LogManager().debug(replacedNewLinesStr); // 输出: "hello world"
  /// ```
  static String replaceNewLines(String str, String replacement) {
    return str.replaceAll('\n', replacement);
  }

  /// 将字符串转换为反转的驼峰命名格式。
  ///
  /// - str: 要转换的字符串。
  /// - 返回值: 反转的驼峰命名格式的字符串。
  /// - 示例：
  /// ```dart
  /// var reverseCamelCaseStr = StrUtil.reverseCamelCase("HelloWorld");
  /// LogManager().debug(reverseCamelCaseStr); // 输出: "hello_world"
  /// ```
  static String reverseCamelCase(String str) {
    return str.replaceAllMapped(
      RegExp(r'[A-Z]'),
          (Match match) => '_${match[0]!.toLowerCase()}',
    );
  }

  /// 生成指定长度的随机字母字符串。
  ///
  /// - length: 字符串的长度。
  /// - 返回值: 生成的随机字母字符串。
  /// - 示例：
  /// ```dart
  /// var randomAlphabeticStr = StrUtil.randomAlphabetic(5);
  /// LogManager().debug(randomAlphabeticStr); // 输出: 随机的5个字母组成的字符串
  /// ```
  static String randomAlphabetic(int length) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
    final rnd = Random();
    return String.fromCharCodes(Iterable.generate(
        length, (_) => chars.codeUnitAt(rnd.nextInt(chars.length))));
  }

  /// 从字符串的指定位置提取特定长度的子串。
  ///
  /// - str: 原始字符串。
  /// - start: 起始位置。
  /// - length: 提取的长度。
  /// - 返回值: 提取的子串。
  /// - 示例：
  /// ```dart
  /// var substringFromIndexStr = StrUtil.substringFromIndex("hello world", 6, 5);
  /// LogManager().debug(substringFromIndexStr); // 输出: "world"
  /// ```
  static String substringFromIndex(String str, int start, int length) {
    return str.substring(start, start + length);
  }

  /// 将字符串转换为蛇形命名格式。
  ///
  /// - str: 要转换的字符串。
  /// - 返回值: 蛇形命名格式的字符串。
  /// - 示例：
  /// ```dart
  /// var snakeCaseStr = StrUtil.snakeCase("HelloWorld");
  /// LogManager().debug(snakeCaseStr); // 输出: "hello_world"
  /// ```
  static String snakeCase(String str) {
    return str.replaceAllMapped(RegExp(r'[A-Z]'), (Match match) {
      return '_${match[0]!.toLowerCase()}';
    }).toLowerCase();
  }

  /// 将字符串中的所有单词按照字典顺序排序。
  ///
  /// - str: 原始字符串。
  /// - 返回值: 排序后的字符串。
  /// - 示例：
  /// ```dart
  /// var sortedWordsStr = StrUtil.sortWords("world hello");
  /// LogManager().debug(sortedWordsStr); // 输出: "hello world"
  /// ```
  static String sortWords(String str) {
    List<String> words = str.split(' ');
    words.sort();
    return words.join(' ');
  }

  /// 将字符串中的所有单词按字母顺序排序。
  ///
  /// - str: 原始字符串。
  /// - 返回值: 单词按字母顺序排序后的字符串。
  /// - 示例：
  /// ```dart
  /// var sortAlphabeticallyStr = StrUtil.sortAlphabetically("world hello");
  /// LogManager().debug(sortAlphabeticallyStr); // 输出: "hello world"
  /// ```
  static String sortAlphabetically(String str) {
    var words = str.split(RegExp(r'\s+'));
    words.sort((a, b) => a.compareTo(b));
    return words.join(' ');
  }

  /// 将字符串分割为指定长度的子串列表。
  ///
  /// - str: 原始字符串。
  /// - chunkSize: 子串的长度。
  /// - 返回值: 子串列表。
  /// - 示例：
  /// ```dart
  /// var splitIntoChunksList = StrUtil.splitIntoChunks("hello world", 3);
  /// LogManager().debug(splitIntoChunksList); // 输出: ["hel", "lo ", "wor", "ld"]
  /// ```
  static List<String> splitIntoChunks(String str, int chunkSize) {
    return [
      for (int i = 0; i < str.length; i += chunkSize)
        str.substring(i, i + chunkSize)
    ];
  }

  /// 将字符串转换为List，按照指定的分隔符分割。
  ///
  /// - str: 原始字符串。
  /// - delimiter: 分隔符。
  /// - 返回值: 分割后的字符串列表。
  /// - 示例：
  /// ```dart
  /// var splitByDelimiterList = StrUtil.splitByDelimiter("hello,world", ",");
  /// LogManager().debug(splitByDelimiterList); // 输出: ["hello", "world"]
  /// ```
  static List<String> splitByDelimiter(String str, String delimiter) {
    return str.split(delimiter);
  }

  /// 将字符串转换为List，按照新行符分割。
  ///
  /// - str: 原始字符串。
  /// - 返回值: 分割后的字符串列表。
  /// - 示例：
  /// ```dart
  /// var splitByNewLineList = StrUtil.splitByNewLine("hello\nworld");
  /// LogManager().debug(splitByNewLineList); // 输出: ["hello", "world"]
  /// ```
  static List<String> splitByNewLine(String str) {
    return str.split('\n');
  }

  /// 将字符串中的每个单词首字母转换为大写。
  ///
  /// - str: 原始字符串。
  /// - 返回值: 每个单词首字母大写后的字符串。
  /// - 示例：
  /// ```dart
  /// var titleizeStr = StrUtil.titleize("hello world");
  /// LogManager().debug(titleizeStr); // 输出: "Hello World"
  /// ```
  static String titleize(String str) {
    return str.replaceAllMapped(
        RegExp(r'\b\w'), (Match m) => m.group(0)!.toUpperCase());
  }

  /// 将字符串转换为其ASCII码表示的字符串。
  ///
  /// - str: 原始字符串。
  /// - 返回值: ASCII码表示的字符串。
  /// - 示例：
  /// ```dart
  /// var toAsciiStringStr = StrUtil.toAsciiString("hello");
  /// LogManager().debug(toAsciiStringStr); // 输出: "104 101 108 108 111"
  /// ```
  static String toAsciiString(String str) {
    return str.codeUnits.map((c) => c.toString()).join(' ');
  }

  /// 将字符串转换为零宽字符表示。
  ///
  /// - str: 要转换的字符串。
  /// - 返回值: 转换为零宽字符表示的字符串。
  /// - 示例：
  /// ```dart
  /// var toZeroWidthStringStr = StrUtil.toZeroWidthString("hello");
  /// LogManager().debug(toZeroWidthStringStr); // 输出: 对应的零宽字符表示的字符串
  /// ```
  static String toZeroWidthString(String str) {
    return str.codeUnits.map((ch) => String.fromCharCode(0x200B + ch)).join();
  }

  /// 将字符串转换为首字母缩写词。
  ///
  /// - str: 原始字符串。
  /// - 返回值: 首字母缩写词。
  /// - 示例：
  /// ```dart
  /// var toAcronymStr = StrUtil.toAcronym("hello world");
  /// LogManager().debug(toAcronymStr); // 输出: "HW"
  /// ```
  static String toAcronym(String str) {
    return str
        .trim()
        .split(RegExp(r'\s+'))
        .map((word) => word[0].toUpperCase())
        .join();
  }

  /// 将字符串转换为其二进制表示的字符串。
  ///
  /// - str: 原始字符串。
  /// - 返回值: 二进制表示的字符串。
  /// - 示例：
  /// ```dart
  /// var toBinaryStringStr = StrUtil.toBinaryString("hello");
  /// LogManager().debug(toBinaryStringStr); // 输出: "1101000 1100101 1101100 1101100 1101111"
  /// ```
  static String toBinaryString(String str) {
    return str.codeUnits.map((c) => c.toRadixString(2)).join(' ');
  }

  /// 将字符串转换为安全的文件名（移除非法字符）。
  ///
  /// - str: 原始字符串。
  /// - 返回值: 安全的文件名。
  /// - 示例：
  /// ```dart
  /// var toSafeFileNameStr = StrUtil.toSafeFileName("hello:world.txt");
  /// LogManager().debug(toSafeFileNameStr); // 输出: "hello_world.txt"
  /// ```
  static String toSafeFileName(String str) {
    return str.replaceAll(RegExp(r'[\\/:*?"<>|]'), '_');
  }

  /// 将字符串转换为标题格式，忽略小词。
  ///
  /// - str: 原始字符串。
  /// - 返回值: 转换后的标题格式字符串。
  /// - 示例：
  /// ```dart
  /// var toTitleCaseIgnoringSmallWordsStr = StrUtil.toTitleCaseIgnoringSmallWords("hello and world");
  /// LogManager().debug(toTitleCaseIgnoringSmallWordsStr); // 输出: "Hello and World"
  /// ```
  static String toTitleCaseIgnoringSmallWords(String str) {
    var words = str.toLowerCase().split(' ');
    var smallWords = {
      'and',
      'or',
      'the',
      'a',
      'an',
      'in',
      'on',
      'at',
      'for',
      'with'
    };
    words = words.map((word) {
      if (smallWords.contains(word)) {
        return word;
      }
      return capitalize(word);
    }).toList();
    return words.join(' ');
  }

  /// 将字符串转换为Base64格式。
  ///
  /// - str: 要转换的字符串。
  /// - 返回值: 转换后的Base64字符串。
  /// - 示例：
  /// ```dart
  /// var toBase64Str = StrUtil.toBase64("hello world");
  /// LogManager().debug(toBase64Str); // 输出: "aGVsbG8gd29ybGQ="
  /// ```
  static String toBase64(String str) {
    return base64Encode(utf8.encode(str));
  }

  /// 将字符串转换为布尔值（处理字符串的ture）。
  ///
  /// - str: 要转换的字符串。
  /// - 返回值: 转换后的布尔值。
  /// - 示例：
  /// ```dart
  /// var toBooleanStr = StrUtil.toBoolean("true");
  /// LogManager().debug(toBooleanStr); // 输出: true
  /// ```
  static bool toBoolean(String str) {
    return str.toLowerCase() == 'true';
  }

  /// 将字符串转换为slug（URL友好格式）。
  ///
  /// - str: 原始字符串。
  /// - 返回值: 转换后的slug字符串。
  /// - 示例：
  /// ```dart
  /// var toSlugStr = StrUtil.toSlug("Hello World!");
  /// LogManager().debug(toSlugStr); // 输出: "hello-world"
  /// ```
  static String toSlug(String str) {
    return str
        .toLowerCase()
        .trim()
        .replaceAll(RegExp(r'\s+'), '-')
        .replaceAll(RegExp(r'[^a-z0-9-]'), '');
  }

  /// 修剪字符串的首尾指定字符。
  ///
  /// - str: 原始字符串。
  /// - char: 要修剪的字符。
  /// - 返回值: 修剪后的字符串。
  /// - 示例：
  /// ```dart
  /// var trimmedStr = StrUtil.trimCharFromEnds("!!hello!!", "!");
  /// LogManager().debug(trimmedStr); // 输出: "hello"
  /// ```
  static String trimCharFromEnds(String str, String char) {
    var regExp = RegExp('^$char+|$char+\$');
    return str.replaceAll(regExp, '');
  }

  /// 截断字符串到指定长度。
  ///
  /// - str: 要截断的字符串。
  /// - length: 指定的长度。
  /// - 返回值: 截断后的字符串。
  /// - 示例：
  /// ```dart
  /// var truncatedStr = StrUtil.truncate("hello world", 5);
  /// LogManager().debug(truncatedStr); // 输出: "hello"
  /// ```
  static String truncate(String str, int length) {
    return (str.length <= length) ? str : str.substring(0, length);
  }

  /// 将字符串中的每个单词的首字母大写。
  ///
  /// - str: 要处理的字符串。
  /// - 返回值: 处理后的字符串。
  /// - 示例：
  /// ```dart
  /// var titleCaseStr = StrUtil.titleCase("hello world");
  /// LogManager().debug(titleCaseStr); // 输出: "Hello World"
  /// ```
  static String titleCase(String str) {
    return str.replaceAllMapped(
      RegExp(r'\b\w'),
          (Match match) => match.group(0)!.toUpperCase(),
    );
  }

  /// 将字符串转换为int类型。
  ///
  /// - str: 要转换的字符串。
  /// - 返回值: 转换后的int值，如果无法转换则返回null。
  /// - 示例：
  /// ```dart
  /// var toIntStr = StrUtil.toInt("123");
  /// LogManager().debug(toIntStr); // 输出: 123
  /// ```
  static int? toInt(String str) {
    return int.tryParse(str);
  }

  /// 将字符串转换为List，每个元素是原字符串的一个字符。
  ///
  /// - str: 原始字符串。
  /// - 返回值: 字符串字符组成的List。
  /// - 示例：
  /// ```dart
  /// var toCharListStr = StrUtil.toCharList("hello");
  /// LogManager().debug(toCharListStr); // 输出: ['h', 'e', 'l', 'l', 'o']
  /// ```
  static List<String> toList(String str) {
    return str.split('');
  }

  /// 将字符串转换为double类型。
  ///
  /// - str: 要转换的字符串。
  /// - 返回值: 转换后的double值，如果无法转换则返回null。
  /// - 示例：
  /// ```dart
  /// var toDoubleStr = StrUtil.toDouble("123.45");
  /// LogManager().debug(toDoubleStr); // 输出: 123.45
  /// ```
  static double? toDouble(String str) {
    return double.tryParse(str);
  }

  /// 将字符串转换为日期时间对象。
  ///
  /// - str: 表示日期时间的字符串。
  /// - 返回值: 对应的DateTime对象，如果无法解析则返回null。
  /// - 示例：
  /// ```dart
  /// var toDateStr = StrUtil.toDate("2024-01-01");
  /// LogManager().debug(toDateStr); // 输出: DateTime对象表示的2024年1月1日
  /// ```
  static DateTime? toDate(String str) {
    try {
      return DateTime.parse(str);
    } catch (_) {
      return null;
    }
  }

  /// 移除字符串首尾的特定字符。
  ///
  /// - str: 原始字符串。
  /// - char: 要移除的字符。
  /// - 返回值: 移除首尾特定字符后的字符串。
  /// - 示例：
  /// ```dart
  /// var trimCharStr = StrUtil.trimChar("!!hello!!", "!");
  /// LogManager().debug(trimCharStr); // 输出: "hello"
  /// ```
  static String trimChar(String str, String char) {
    return str.trim().replaceAll(RegExp('^$char+|$char+\$'), '');
  }

  /// 将字符串中的特定单词转换为大写。
  ///
  /// - str: 原始字符串。
  /// - word: 要转换的单词。
  /// - 返回值: 转换后的字符串。
  /// - 示例：
  /// ```dart
  /// var uppercaseWordStr = StrUtil.uppercaseWord("hello world", "world");
  /// LogManager().debug(uppercaseWordStr); // 输出: "hello WORLD"
  /// ```
  static String uppercaseWord(String str, String word) {
    return str.replaceAll(word, word.toUpperCase());
  }

  /// 将转义的HTML字符串还原。
  ///
  /// - [str]: 要还原的字符串。
  /// - 返回值: 还原后的字符串。
  /// - 示例：
  /// ```dart
  /// var unescapedHtmlStr = StrUtil.unescapeHtml("&lt;div&gt;Hello World!&lt;/div&gt;");
  /// LogManager().debug(unescapedHtmlStr); // 输出: "<div>Hello World!</div>"
  /// ```
  static String unescapeHtml(String str) {
    var unescapedStr = str
        .replaceAll('&lt;', '<')
        .replaceAll('&gt;', '>')
        .replaceAll('&amp;', '&')
        .replaceAll('&quot;', '"')
        .replaceAll('&#x27;', '\'')
        .replaceAll('&#x2F;', '/');
    return unescapedStr;
  }

  /// 计算字符串的字节长度（UTF-8编码）。
  ///
  /// - str: 要计算的字符串。
  /// - 返回值: 字节长度。
  /// - 示例：
  /// ```dart
  /// var utf8LengthStr = StrUtil.utf8Length("hello");
  /// LogManager().debug(utf8LengthStr); // 输出: 5
  /// ```
  static int utf8Length(String str) {
    return utf8.encode(str).length;
  }

  /// 验证字符串是否符合指定的正则表达式。
  ///
  /// - str: 原始字符串。
  /// - regex: 正则表达式。
  /// - 返回值: 如果字符串符合正则表达式，则返回true，否则返回false。
  static bool validateRegex(String str, String regex) {
    return RegExp(regex).hasMatch(str);
  }

  /// 将字符串的单词按照指定的分隔符连接。
  ///
  /// - str: 原始字符串。
  /// - separator: 分隔符。
  /// - 返回值: 以分隔符连接的字符串。
  /// - 示例：
  /// ```dart
  /// var validateRegexStr = StrUtil.validateRegex("hello", r'^\w+$');
  /// LogManager().debug(validateRegexStr); // 输出: true
  /// ```
  static String wordsJoinWithSeparator(String str, String separator) {
    return str.split(RegExp(r'\s+')).join(separator);
  }

  /// 将字符串转换为小写，并替换所有空格为破折号。
  ///
  /// - str: 原始字符串。
  /// - 返回值: 转换后的字符串。
  /// - 示例：
  /// ```dart
  /// var convertedStr = xToDash("Hello World");
  /// LogManager().debug(convertedStr); // 输出: "hello-world"
  /// ```
  static String xToDash(String str) {
    return str.toLowerCase().replaceAll(' ', '-');
  }

  /// 年份格式化。
  ///
  /// - year: 要格式化的年份。
  /// - 返回值: 格式化后的年份字符串。
  /// - 示例：
  /// ```dart
  /// var formattedYear = yearFormat(2023);
  /// LogManager().debug(formattedYear); // 输出: "2023"
  /// ```
  static String yearFormat(int year) {
    return year.toString().padLeft(4, '0');
  }

  /// 将字符串转换为“是/否”表示。
  ///
  /// - str: 原始字符串。
  /// - 返回值: 如果字符串为"true", "yes", "y", "1"则返回"Yes"，否则返回"No"。
  /// - 示例：
  /// ```dart
  /// var result = yesOrNo("yes");
  /// LogManager().debug(result); // 输出: "Yes"
  /// ```
  static String yesOrNo(String str) {
    var lowerStr = str.toLowerCase();
    return (lowerStr == 'true' ||
        lowerStr == 'yes' ||
        lowerStr == 'y' ||
        lowerStr == '1')
        ? 'Yes'
        : 'No';
  }

  /// 将两个字符串压缩（zip）成一对的列表。
  ///
  /// - str1: 第一个字符串。
  /// - str2: 第二个字符串。
  /// - 返回值: 由第一个和第二个字符串中的字符对组成的列表。
  /// - 示例：
  /// ```dart
  /// var zippedList = zipStrings("abc", "123");
  /// LogManager().debug(zippedList); // 输出: [{"str1": "a", "str2": "1"}, {"str1": "b", "str2": "2"}, {"str1": "c", "str2": "3"}]
  /// ```
  static List<Map<String, String>> zip(String str1, String str2) {
    int minLength = min(str1.length, str2.length);
    return List.generate(minLength, (index) {
      return {'str1': str1[index], 'str2': str2[index]};
    });
  }

  /// 将字符串转换为使用零宽空格的隐写形式。
  ///
  /// - str: 要转换的字符串。
  /// - 返回值: 使用零宽空格编码的字符串。
  /// ```dart
  /// var encodedStr = zeroWidthEncode("secret");
  /// LogManager().debug(encodedStr); // 输出: "零宽空格编码的字符串"
  /// ```
  static String zeroWidthEncode(String str) {
    return str.codeUnits
        .map((unit) => unit.toRadixString(2).padLeft(8, '0'))
        .join('\u200B');
  }

  /// 从使用零宽空格编码的字符串中解码出原始字符串。
  ///
  /// - str: 使用零宽空格编码的字符串。
  /// - 返回值: 解码后的原始字符串。
  /// - 示例：
  /// ```dart
  /// var decodedStr = zeroWidthDecode("零宽空格编码的字符串");
  /// LogManager().debug(decodedStr); // 输出: "secret"
  /// ```
  static String zeroWidthDecode(String str) {
    var binaryStr = str.split('\u200B').join();
    return String.fromCharCodes(
      RegExp(r'.{8}')
          .allMatches(binaryStr)
          .map((match) => int.parse(match.group(0)!, radix: 2)),
    );
  }

  /// 对字符串执行简单的压缩算法（使用重复字符计数）。
  ///
  /// - str: 要压缩的字符串。
  /// - 返回值: 压缩后的字符串。
  /// - 示例：
  /// ```dart
  /// var compressedStr = zipCompress("aaabbbcc");
  /// LogManager().debug(compressedStr); // 输出: "a3b3c2"
  /// ```
  static String zipCompress(String str) {
    return str.replaceAllMapped(RegExp(r'(.)\1+'),
            (match) => '${match.group(0)![0]}${match.group(0)!.length}');
  }

  /// 解压使用简单压缩算法压缩的字符串。
  ///
  /// - str: 要解压的字符串。
  /// - 返回值: 解压后的字符串。
  /// - 示例：
  /// ```dart
  /// var decompressedStr = zipDecompress("a3b3c2");
  /// LogManager().debug(decompressedStr); // 输出: "aaabbbcc"
  /// ```
  static String zipDecompress(String str) {
    return str.replaceAllMapped(RegExp(r'(.)\d+'),
            (match) => match.group(1)! * int.parse(match.group(0)!.substring(1)));
  }

  /// 使用指定的分隔符将字符串转换为“键-值”对的映射。
  ///
  /// - str: 要处理的字符串。
  /// - delimiter: 键和值之间的分隔符。
  /// - 返回值: “键-值”对的映射。
  /// - 示例：
  /// ```dart
  /// var keyValueMap = zipToMap("key1=value1&key2=value2", "&");
  /// LogManager().debug(keyValueMap); // 输出: {"key1": "value1", "key2": "value2"}
  /// ```
  static Map<String, String> zipToMap(String str, String delimiter) {
    return {
      for (var item in str.split(delimiter))
        item.split('=')[0]: item.split('=')[1]
    };
  }
  /// 使用AES 128位加密数据（ECB模式，无填充）。
  ///
  /// [hexStringData] 需要加密的十六进制字符串数据。
  /// [hexStringKey] 用于加密的十六进制字符串密钥，如果不足16字节将进行填充。
  ///
  /// 返回加密后的数据，形式为十六进制字符串。
  static String aes128Encrypt(String hexStringData, String hexStringKey) {
    try {
      // 验证输入参数是否为空
      if (hexStringData.isEmpty || hexStringKey.isEmpty) {
        throw EncryptionException("加密数据或密钥不能为空");
      }

      // 将密钥转换为字节数组并调整长度为16字节
      final keyBytes = adjustKeyLength(hexStringToBytes(hexStringKey), 16);

      // 将明文数据转换为字节数组
      final dataBytes = hexStringToBytes(hexStringData);

      // 检查明文数据长度是否为16的倍数（因为使用无填充）
      if (dataBytes.length % 16 != 0) {
        throw EncryptionException("明文数据长度必须是16的倍数");
      }

      // 创建全零的16字节IV（尽管ECB模式不使用IV，但encrypt包可能要求提供）
      final iv = encrypt.IV(Uint8List(16));

      // 创建加密密钥
      final key = encrypt.Key(Uint8List.fromList(keyBytes));

      // 创建AES加密器
      final encrypter = encrypt.Encrypter(encrypt.AES(
        key,
        mode: encrypt.AESMode.ecb,
        padding: null,
      ));

      // 执行加密操作
      final encrypted = encrypter.encryptBytes(
        dataBytes,
        iv: iv,
      );

      // 将加密后的字节数组转换为十六进制字符串
      return bytesToHexString(encrypted.bytes);
    } on EncryptionException {
      rethrow; // 继续向上抛出自定义的加密异常
    } on FormatException catch (e) {
      // 处理格式异常，例如输入的十六进制字符串非法
      LogManager().debug('===加密失败：输入的十六进制字符串格式错误：$e');
      throw EncryptionException('输入的十六进制字符串格式错误：$e');
    } catch (e) {
      // 捕获其他未知异常
      LogManager().debug('===加密失败：$e');
      throw EncryptionException('加密过程中发生未知错误：$e');
    }
  }


  /// 使用AES 128位解密数据（ECB模式，无填充）。
  ///
  /// [hexStringData] 需要解密的十六进制字符串数据。
  /// [hexStringKey] 用于解密的十六进制字符串密钥，如果不足16字节将进行填充。
  ///
  /// 返回解密后的数据，形式为十六进制字符串。
  /// AES-128 解密方法，使用 ECB 模式，无填充
  static String aes128Decrypt(String hexStringData, String hexStringKey) {
    try {
      // 验证输入参数是否为空
      if (hexStringData.isEmpty || hexStringKey.isEmpty) {
        throw DecryptionException("加密数据或密钥不能为空");
      }

      // 将密钥转换为字节数组并调整长度为16字节
      final keyBytes = adjustKeyLength(hexStringToBytes(hexStringKey), 16);

      // 将加密数据转换为字节数组
      final dataBytes = hexStringToBytes(hexStringData);

      // 创建全零的16字节IV（尽管ECB模式不使用IV，但encrypt包可能要求提供）
      final iv = encrypt.IV(Uint8List(16));

      // 检查加密数据长度是否为16的倍数（因为使用无填充）
      if (dataBytes.length % 16 != 0) {
        throw DecryptionException("加密数据长度必须是16的倍数");
      }

      // 创建加密密钥
      final key = encrypt.Key(Uint8List.fromList(keyBytes));

      // 创建AES加密器
      final encrypter = encrypt.Encrypter(encrypt.AES(
        key,
        mode: encrypt.AESMode.ecb,
        padding: null,
      ));

      // 执行解密操作
      final decryptedBytes = encrypter.decryptBytes(
        encrypt.Encrypted(Uint8List.fromList(dataBytes)),
        iv: iv,
      );

      // 将解密后的字节数组转换为十六进制字符串
      final decryptedHexString = bytesToHexString(Uint8List.fromList(decryptedBytes));

      return decryptedHexString;
    } on DecryptionException {
      rethrow; // 继续向上抛出自定义的解密异常
    } on FormatException catch (e) {
      // 处理格式异常，例如输入的十六进制字符串非法
      LogManager().debug('===解密失败：输入的十六进制字符串格式错误：$e');
      throw DecryptionException('输入的十六进制字符串格式错误：$e');
    } catch (e) {
      // 捕获其他未知异常
      LogManager().debug('===解密失败：$e');
      throw DecryptionException('解密过程中发生未知错误：$e');
    }
  }

  /// 对两个十六进制字符串进行异或操作。
  ///
  /// [source] 需要进行异或操作的十六进制字符串数据。
  /// [key] 用于异或的十六进制字符串密钥。
  ///
  /// 返回异或后的十六进制字符串。
  static String xorEncodeData(String source, String key) {
    Uint8List dataBytes = hexStringToBytes(source);
    Uint8List keyBytes = hexStringToBytes(key);

    // 进行异或操作
    for (int i = 0; i < dataBytes.length; i++) {
      dataBytes[i] = dataBytes[i] ^ keyBytes[i % keyBytes.length];
    }

    // 将异或后的字节数组转换回十六进制字符串
    return bytesToHexString(dataBytes);
  }

  /// 将十六进制字符串转换为字节数组（Uint8List）。
  ///
  /// [hex] 需要转换的十六进制字符串。
  ///
  /// 返回转换后的字节数组。
  static Uint8List hexStringToBytes(String hex) {
    final length = hex.length;
    final Uint8List bytes = Uint8List(length ~/ 2);
    for (int i = 0; i < length; i += 2) {
      final byteString = hex.substring(i, i + 2);
      final byteValue = int.parse(byteString, radix: 16);
      bytes[i ~/ 2] = byteValue;
    }
    return bytes;
  }

  /// 将字节数组（Uint8List）转换为十六进制字符串。
  ///
  /// [bytes] 需要转换的字节数组。
  ///
  /// 返回转换后的十六进制字符串。
  static String bytesToHexString(Uint8List bytes) {
    final StringBuffer hexString = StringBuffer();
    for (final byte in bytes) {
      final hex = byte.toRadixString(16).padLeft(2, '0').toUpperCase();
      hexString.write(hex);
    }
    return hexString.toString();
  }

  /// 调整密钥长度以匹配AES 128位加密的要求。
  ///
  /// [keyBytes] 原始密钥字节数组。
  /// [length] 目标长度（例如16字节）。
  ///
  /// 返回调整后的密钥字节数组。
  static Uint8List adjustKeyLength(Uint8List keyBytes, int length) {
    if (keyBytes.length > length) {
      return keyBytes.sublist(0, length);
    } else if (keyBytes.length < length) {
      return Uint8List.fromList(
        List<int>.from(keyBytes)..addAll(List<int>.filled(length - keyBytes.length, 0)),
      );
    }
    return keyBytes;
  }

  // CRC16-CCITT-FALSE算法实现
  static int crc16CcittFalse(Uint8List bytes) {
    int crc = 0xFFFF; // 初始值
    int polynomial = 0x1021; // 多项式
    for (int index = 0; index < bytes.length; index++) {
      int b = bytes[index];
      for (int i = 0; i < 8; i++) {
        bool bit = ((b >> (7 - i) & 1) == 1);
        bool c15 = ((crc >> 15 & 1) == 1);
        crc = ((crc << 1) & 0xFFFF);
        if (c15 ^ bit) {
          crc ^= polynomial;
        }
      }
    }
    crc &= 0xFFFF;
    return crc;
  }

  /// 按二进制位从高位开始截取数据
  /// - 参数:
  ///   - data: 输入的原始数据，类型为 Uint8List
  ///   - startBit: 起始二进制位位置（从 0 开始计数）
  ///   - lengthInBits: 需要截取的位数
  static Uint8List subDataFromStartBit(Uint8List data, int startBit, int lengthInBits) {
    // 计算数据的总位数
    int dataLengthInBits = data.length * 8;

    // 检查起始位和长度是否合法
    if (startBit >= dataLengthInBits || lengthInBits == 0) {
      LogManager().debug("Error: Invalid start or length.");
      return Uint8List(0);
    }

    // 计算结束位的位置
    int endBit = startBit + lengthInBits;
    if (endBit > dataLengthInBits) {
      LogManager().debug("Error: Length exceeds input data.");
      return Uint8List(0);
    }

    // 计算结果数据的字节长度（向上取整）
    int resultLengthInBytes = (lengthInBits + 7) ~/ 8;
    Uint8List resultData = Uint8List(resultLengthInBytes);

    // 获取输入数据的字节数组
    List<int> inputBytes = data;

    // 遍历从 startBit 到 endBit 的每一位
    for (int i = startBit; i < endBit; i++) {
      // 计算输入数据中对应的字节索引和位索引
      int inputByteIndex = i ~/ 8;
      int inputBitIndex = 7 - (i % 8);

      // 获取输入数据中的对应位
      int inputBit = (inputBytes[inputByteIndex] >> inputBitIndex) & 1;

      // 计算结果数据中对应的字节索引和位索引
      int resultByteIndex = (i - startBit) ~/ 8;
      int resultBitIndex = 7 - ((i - startBit) % 8);

      // 将输入位设置到结果数据中
      resultData[resultByteIndex] |= inputBit << resultBitIndex;
    }

    // 根据需要调整结果数据，使其对齐
    int resultShift = 8 - (lengthInBits % 8);
    if (resultShift != 8) {
      for (int i = 0; i < resultData.length; i++) {
        // 右移结果数据中的每个字节
        resultData[i] = (resultData[i] >> resultShift) & 0xFF;
        if (i < resultData.length - 1) {
          // 将下一个字节的位拼接到当前字节
          resultData[i] |= (resultData[i + 1] << (8 - resultShift)) & 0xFF;
        }
      }
    }

    return resultData;
  }

  ///格式化整型字符串
  static String formatInt(String str,List<int> data){
    //先 str 转数组
    List<String> strs = str.split('%d');
    String endTemp = strs[strs.length-1];
    strs.removeLast();
    if(strs.length != data.length){
      throw Exception('%d count and data length inconsistent');
    }
    StringBuffer newStr = StringBuffer() ;
    for(int i = 0 ; i < strs.length ; i++){
      String temp = '${strs[i]}${data[i]}';
      newStr.write(temp);
    }
    newStr.write(endTemp);
    return newStr.toString();
  }

  ///格式化字符串
  static String formatString(String str,List<String> data){
    //先 str 转数组
    List<String> strs = str.split('%s');
    String endTemp = strs[strs.length-1];
    strs.removeLast();
    if(strs.length != data.length){
      throw Exception('%d count and data length inconsistent');
    }
    StringBuffer newStr = StringBuffer() ;
    for(int i = 0 ; i < strs.length ; i++){
      String temp = '${strs[i]}${data[i]}';
      newStr.write(temp);
    }
    newStr.write(endTemp);
    return newStr.toString();
  }

  ///格式化浮点字符串
  static String formatFloat(String str,List<double> data){
    //先 str 转数组
    List<String> strs = str.split('%f');
    String endTemp = strs[strs.length-1];
    strs.removeLast();
    if(strs.length != data.length){
      throw Exception('%d count and data length inconsistent');
    }
    StringBuffer newStr = StringBuffer() ;
    for(int i = 0 ; i < strs.length ; i++){
      String temp = '${strs[i]}${data[i]}';
      newStr.write(temp);
    }
    newStr.write(endTemp);
    return newStr.toString();
  }

  ///格式化综合字符串
  static String formatObjcet(String str,List<Object> data){
    List<int> intData = [];
    List<double> doubleData = [];
    List<String> stringData = [];
    for(Object mObject in data){
      if(mObject is String){
        stringData.add(mObject);
      }else if(mObject is int){
        intData.add(mObject);
      }else if(mObject is double){
        doubleData.add(mObject);
      }
    }
    String tempStr = formatString(str,stringData);
    tempStr = formatInt(tempStr,intData);
    tempStr = formatFloat(tempStr,doubleData);
    return tempStr;
  }
  ///字符串拼接
  static String textSplit(List<String> strList) {
    String text = "";
    for(String str in strList) {
      text += str;
    }
    return text;
  }


  /// 根据传进来的单位，将服务器返回的时间数据（xx分）转换为相应格式（xx时xx分）
  /// - 参数:
  ///   - time: 输入的原始数据，类型为 int
  ///   - hourUnit: 小时的单位
  ///   - minUnit: 分钟的单位
  static String getFormattedLeftChargeTime(int time, String hourUnit, String minUnit) {
    String result;
    int minTime = time;
    int min = 0;
    int h = 0;
    if (minTime>60){
        min = minTime%60;
        h = ((minTime-min)/60).floor();
    }
    else{
        min = minTime;
    }
    
    if (h>0) {
        if(min>0){
            result = '$h$hourUnit $min$minUnit';
        }
        else{
            result = '$h$hourUnit';
        }
    }
    else{
        result = '$min$minUnit';
    }
    return result;
    }
}

extension StringExtension on String {
  /// 是否是有效的字符串
  bool isValidStr() {
    if (isNotEmpty && this != 'null') {
      return true;
    }
    return false;
  }

  /// 是否是无效的字符串
  bool isNotValidStr() {
    if (isEmpty || this == 'null') {
      return true;
    }
    return false;
  }
}