import { image } from '@kit.ImageKit';  @Component export default struct Zoom {  @Prop mapViewId: string;  @State btnBackgroundColor: string;  @State outBtnBackgroundColor: string;  @State zoomIn: image.PixelMap | undefined;  @State zoomOut: image.PixelMap | undefined; clickZoomIn(): void; clickZoomOut(): void; aboutToAppear(): Promise<void>; private getPixmapFromMedia; build(): void; inBtnAnimation(); outBtnAnimation(); img(); zoomIcon(); zoomInIcon(); zoomOutIcon(); } 