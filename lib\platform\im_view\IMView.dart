

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../models/global_data.dart';
import '../../utils/manager/log_manager.dart';

class IMView extends StatefulWidget {
  final String type;
  final String token;
  final Map<String, dynamic>? params;
  final Map<String,Future Function(dynamic message)> callBacks;
  final Future<String> Function(bool isRefresh) getToken;
  final void Function(Map<String,dynamic> args)? chatPage;
  const IMView({super.key, required this.token, required this.getToken, required this.callBacks, this.chatPage, required this.type, this.params});

  @override
  State<IMView> createState() => _IMViewState();
}

class _IMViewState extends State<IMView> {
  late MethodChannel _channel;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    widget.callBacks['refresh'] = refresh;
  }

  @override
  Widget build(BuildContext context) {
    return _getPlatformFaceView();
  }

  Widget _getPlatformFaceView() {
    return OhosView(
      viewType: 'com.sgmw.flutter/im',
      onPlatformViewCreated: _onPlatformViewCreated,
      creationParams: <String, dynamic>{"token": widget.token,"type":widget.type}..addAll(widget.params??{}) ,
      creationParamsCodec: const StandardMessageCodec(),
    );
  }

  void _onPlatformViewCreated(int id) {
    _channel = MethodChannel('com.sgmw.flutter/im$id');
    _channel.setMethodCallHandler( (MethodCall call,)async{
      LogManager().debug("call.method============================${call.method}");
      switch (call.method) {
        case 'getToken':
          // 从native端获取数据
         await widget.getToken(call.arguments).then((value) {
              return value;
          });
         break;
        case 'chatPage':
          if(widget.chatPage != null){
            widget.chatPage!(call.arguments);
          }
          break;
      }
    });
  }

  Future refresh(dynamic message) {
    return _channel.invokeMethod(
      'refresh',
      message,
    );
  }

  void _sendPlatformViewCreated(dynamic message) {
     _channel.invokeMethod(
      'refresh',
      message,
    );
  }
}
