import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:iamgeqr_flutter_plugin/iamgeqr_flutter_plugin.dart';
import 'package:wuling_flutter_app/routes/jump_tool.dart';
import 'package:wuling_flutter_app/utils/manager/loading_manager.dart';
import 'package:wuling_flutter_app/utils/manager/log_manager.dart';

class QrScannerUtil{

  QrScannerUtil._privateConstructor();

  static final QrScannerUtil _instance = QrScannerUtil._privateConstructor();

  static late IamgeqrFlutterPlugin _iamgeqrFlutterPlugin;

  factory QrScannerUtil() {
    _iamgeqrFlutterPlugin = IamgeqrFlutterPlugin();
    return _instance;
  }

  Future<void> imageScan(Function(String) onCallBack,Function() onError) async {
    String platform;
    try {
      platform = await _iamgeqrFlutterPlugin.imageScanQr() ?? 'Unknown platform';
    } on PlatformException {
      platform = 'Failed to get platform version.';
    }
    if(platform.contains('error') || platform.contains('Unknown') || platform.contains('Failed')){
      LoadingManager.showToast("扫码失败。。。");
    }else {
      onCallBack(platform);
    }
  }

  void returnProcessing(BuildContext context,String data){
    LogManager().debug("扫描结果: $data");
    if (data.endsWith("-SGMW") && data.length >= 22){
      String vin = data.substring(data.length - 22, data.length - 5);
      if (vin.length == 17 && vin.startsWith("LZWA")) {
        //斑马车扫码登录
        LoadingManager.showToast("正在施工中。。。");
      } else {
        //跳网页
        _webProcessing(context,data);
      }
    }else{
      //跳网页
      _webProcessing(context,data);
    }
  }

  _webProcessing(BuildContext context,String data){
    String scanResult;
    if (data.contains("\n")){
      scanResult = data.replaceAll("\n", "");
    }else{
      scanResult = data;
    }
    if (scanResult != null) {
      if (scanResult.contains("00bang.cn") || scanResult.startsWith("https://cdn-m.00bang.cn/llb/html/share/scan.html?type=")) {
        JumpTool().openWeb(context, scanResult,false);
      } else if (scanResult.startsWith("https://cdn-m.baojun.net/share/code")) {
        scanResult = Uri.encodeComponent(scanResult);
        LogManager().log(scanResult);
        JumpTool().openWeb(context, 'https://m.baojun.net/lingClub/love-car/bindcar/$scanResult', false);
      } else if (scanResult.length == 17 && scanResult.startsWith("LZWA")) {//斑马车扫码绑车
        JumpTool().openWeb(context, 'https://m.baojun.net/lingClub/active-mandate/ActiveMandate/$scanResult',false);
      }else {
        JumpTool().openWeb(context, 'https://cdn-m.00bang.cn/llb/html/share/scan.html?type=0&content=$scanResult',false);
      }
    } else {
      JumpTool().openWeb(context, 'https://cdn-m.00bang.cn/llb/html/share/scan.html?type=0&content=$scanResult',false);
    }
  }
}