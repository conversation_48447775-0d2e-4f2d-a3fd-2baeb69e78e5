import 'package:flutter/services.dart';
import 'package:flutter_platform_utils/flutter_platform_utils.dart';

import '../constant/constant.dart';
import 'manager/log_manager.dart';

class WechatSharedUtil {

  final MethodChannel _channel = const MethodChannel('com.sgmw.flutter/wechat_shared');

  share(String postId, int postTypeId,String userIdStr,String title,String image,String desc) async {
   // print("postId: $postId, postTypeId: $postTypeId, userIdStr: $userIdStr, title: $title, image: $image, desc: $desc");
    String url = "";
    if (postTypeId == -1) {
      url = "${Constant.WEB_VIEW_BASE_URL}share/topic.html?topicId=$postId";
    } else if(postTypeId == 6 || postTypeId == 5) {
      url = "${Constant.WEB_VIEW_BASE_URL}share/videPost.html?postId=$postId";
    }else {
      url = "${Constant.WEB_VIEW_BASE_URL}share/post.html?postId=$postId";
    }

    if (PlatformUtils.isOhos) {
      try {
        await _channel.invokeMethod('shareWechat', {
          "url": url,
          "title": title ,
          "imageUrl": "$image?x-oss-process=image/format,webp/quality,q_80",
          "desc": desc
        });
      } on PlatformException catch (e) {
        LogManager().debug("Failed to open app detail page: ${e.message}");
      }
    }
  }
  shareImage(String url) async {
    if (PlatformUtils.isOhos) {
      try {
        await _channel.invokeMethod('shareImage', {
          "url": url
        });
      } on PlatformException catch (e) {
        LogManager().debug("Failed to open app detail page: ${e.message}");
      }
    }
  }
}