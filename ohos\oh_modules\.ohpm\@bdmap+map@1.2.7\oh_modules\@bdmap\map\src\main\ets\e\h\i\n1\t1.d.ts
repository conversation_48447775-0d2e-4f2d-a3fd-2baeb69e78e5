import type B<PERSON><PERSON><PERSON><PERSON> from "../c2/n1/b4"; import type OverlayListener from "../m/i2"; import Base<PERSON> from "./q1";         export default abstract class BaseGroupUI extends BaseUI { private mGroupInstance; private mViews; static WRAP_CONTENT: number; constructor(z45: number, instance: BmGroupUI);         setListener(listener: OverlayListener): void;         addView(child: BaseUI, index?: number): void;         removeAllViews(): void; } 