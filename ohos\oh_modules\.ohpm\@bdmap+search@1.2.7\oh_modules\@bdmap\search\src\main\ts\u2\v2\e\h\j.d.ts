/**
 * 推荐上车点服务端返回json
 */
import { SdkInnerError } from "../../../base/f1";
export interface RecommendStopJSONResult {
    status?: number;
    message?: string;
    station_info?: n42[];
    SDK_InnerError?: SdkInnerError;
    recommendStops?: RecommendStop[];
}
/**
 * Json解析-场站信息
 */
interface n42 {
    station_name?: string;
    recommendstops?: o42[];
}
/**
 * Json解析-场站信息下推荐上车点信息
 */
interface o42 {
    name?: string;
    address?: string;
    distance?: number;
    bd09ll_x?: number;
    bd09ll_y?: number;
    gcj02ll_x?: number;
    gcj02ll_y?: number;
    id?: string;
}
/**
 * Json解析-推荐上车点信息(未选择场站信息)
 */
export interface RecommendStop {
    name?: string;
    address?: string;
    distance?: number;
    bd09ll_x?: number;
    bd09ll_y?: number;
    gcj02ll_x?: number;
    gcj02ll_y?: number;
    id?: string;
}
export {};
