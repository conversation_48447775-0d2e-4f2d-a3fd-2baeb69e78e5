import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:wuling_flutter_app/models/common/advertise.dart';
import 'package:wuling_flutter_app/models/common/advertise_position_model.dart';
import 'package:wuling_flutter_app/widgets/common/custom_text_indicator.dart';
import 'package:wuling_flutter_app/widgets/common/image_view.dart';
import 'package:wuling_flutter_app/widgets/common/custom_page_view.dart';
import '../../models/common/shop_car_type.dart';
import '../../utils/app_config_util.dart';
import '../../utils/manager/dialog_manager.dart';
import '../../utils/manager/log_manager.dart';
import '../common/custom_dialog.dart';

class CarAdvertiseDetailGrid extends StatelessWidget {
  // final ImgText? advertise;
  final List<ImgText> imgTextList;
  final  h = 5;
  const CarAdvertiseDetailGrid({super.key,required this.imgTextList});

  @override
  Widget build(BuildContext context) {
    const double buttonHeight = 40.0;
    int columnCount = (10 / 2).ceil();
    double fullHeight = buttonHeight * columnCount;
    return Container(
      height: h * 150,

      color: Colors.blue,
      // alignment: Alignment.center,
      child: GridView.builder(
        physics: const NeverScrollableScrollPhysics(), // 禁止GridView滚动
        itemBuilder: (BuildContext context, int index) {
          return  GestureDetector(
            child: Padding(
              padding: const EdgeInsets.only(bottom: 20),
              child: Container(
                // height: 34,
                color: Colors.red,
                // child: Column(
                //   children: <Widget>[
                //     Image.asset(
                //         "assets/images/use_car_page/go_wulin.png",
                //         alignment: Alignment.center,
                //       height: 30,
                //     ),
                //     SizedBox(height: 15),
                //     Text(
                //       "五菱宾果",
                //       textAlign: TextAlign.center,
                //       style: TextStyle(
                //           fontSize: 16
                //       ),
                //     ),
                //   ],
                // ),
              ),
            ),
            onTap:() {
              showNotLoginAlertDialog(context);
            },
          ); // 通过索引构建子项
        },
        itemCount:6,
        scrollDirection: Axis.vertical,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 10,
          mainAxisSpacing: 10,
        ),
      ),
    );
  }

  List<Widget> _getListData() {
    var tempList = imgTextList.map((value) {
      return Container(
        color: Colors.red,
        width: 100,
        height: 50,
        child: Column(
          children: <Widget>[
            Image.asset("value['imageUrl']"),
            SizedBox(height: 15),
            Text(
              "heelo",
              textAlign: TextAlign.center,
              style: TextStyle(
                  fontSize: 20
              ),
            ),
          ],
        ),
        decoration: BoxDecoration(
            border: Border.all(
                color: Color.fromARGB(233, 233, 233, 1),
                width: 1
            )
        ),
      );
    });
    return tempList.toList();
  }

  static Widget getDialog(String item) {
    return  AlertDialog(
      title: Text(
        //标题
        '提示',
        style: new TextStyle(color: Colors.red[300], fontSize: 18),
      ),
      content: new Text(item), //提示语
    );
  }

  void showNotLoginAlertDialog(BuildContext context) {
    DialogManager().showCustomDialog(
      CustomDialog(
        content: "您还没有登录",
        contentFontSize: 18,
        contentColor: Colors.black,
        buttonHeight: 70,
        horizontalPadding: 20,
        buttons: [
          DialogButton(
              label: "继续逛逛",
              onPressed: () {
                LogManager().debug("取消按钮被点击");
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xFF9B9DA9)),
          DialogButton(
              label: "去登录",
              onPressed: () {
                // LoginManager().showLoginModal();
              },
              backgroundColor: Colors.white,
              textColor: const Color(0xffea0029)),
        ],
      ),
    );
  }
}

