import { MessageContent } from '../MessageContent';
/**
 * 消息标识
 * @version 1.2.0
 */
declare const VoiceMessageObjectName = "RC:VcMsg";
/**
 * 普通语音消息
 * @version 1.3.0
 */
declare class VoiceMessage extends MessageContent {
    /**
     * 语音数据的 base64 字符串
     */
    base64: string;
    /**
     * 语音时长，单位秒
     *```
     * duration 在 encode() 方法中用 Math.ceil() 做向上取整
     * 如果不希望向上取整，客户可以将时长按需取整之后设置给 duration
     *```
     */
    duration: number;
    /**
     * 无参构造方法
     */
    constructor();
    /**
     * 编码方法，将消息转为 json 字符串
     * @returns json 字符串
     */
    encode(): string;
    /**
     * 解码方法，将 json 字符串转为消息
     * @param contentString json 字符串
     */
    decode(contentString: string): void;
    /**
     * 获取类名
     * @returns 类名
     */
    getClassName(): string;
}
export { VoiceMessage, VoiceMessageObjectName };
