import { InstanceManager } from './InstanceManager';
import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import { PermissionRequestHostApi } from "./GeneratedOhosWebView";
export declare class PermissionRequestHostApiImpl extends PermissionRequestHostApi {
    private binaryMessenger;
    private instanceManager;
    constructor(binaryMessenger: BinaryMessenger, instanceManager: InstanceManager);
    grant(instanceId: number, resources: string[]): void;
    deny(instanceId: number): void;
    private getPermissionRequestInstance;
}
