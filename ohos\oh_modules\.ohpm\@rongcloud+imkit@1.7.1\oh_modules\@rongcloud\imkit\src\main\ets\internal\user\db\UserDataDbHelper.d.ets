// @keepTs
// @ts-nocheck
import { relationalStore } from '@kit.ArkData';
/**
 * 用户信息数据库
 * @version 1.4.0
*/
export declare class UserDataDbHelper {
    private static appKey;
    private static currentUserId;
    /**
     * 是否正在初始化中
     */
    private static initializingPromise;
    /**
     * 是否已经初始化完成
     */
    private static isInitialized;
    /**
     * 初始化数据库, 在主线程中调用，在异步线程中调用之前，需要确保在主线程中调用此方法
     *
     * 创建数据库、检查版本号、创建表、升级表结构
     * @param context
     * @param appKey
     * @param currentUserId
     */
    static initStore(s334: Context, t334: string, u334: string): Promise<void>;
    /**
     * 重置状态
     */
    private static reset;
    /**
     * 获取数据库配置
     */
    static getStoreConfig(): relationalStore.StoreConfig;
    /**
     * 初始化数据库并升级数据库表结构
     * @param context
     * @param appKey
     * @param currentUserId
     */
    private static initAndUpgradeStore;
    /**
     * 检查是否已有 User 表
     * @returns
     */
    private static checkUserTableExists;
    private static createTable;
    /**
     * 升级用户表结构，添加 userType 字段
     */
    private static addUserTypeColumn;
    /**
     * 清理用户数据库
     * @param context
     */
    static clear(a334: Context): Promise<void>;
}
