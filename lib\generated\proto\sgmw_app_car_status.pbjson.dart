//
//  Generated code. Do not modify.
//  source: sgmw_app_car_status.proto
//
// @dart = 2.12

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names, library_prefixes
// ignore_for_file: non_constant_identifier_names, prefer_final_fields
// ignore_for_file: unnecessary_import, unnecessary_this, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use sgmwAppCarStatusDescriptor instead')
const SgmwAppCarStatus$json = {
  '1': 'SgmwAppCarStatus',
  '2': [
    {'1': 'collectTime', '3': 1, '4': 1, '5': 3, '10': 'collectTime'},
    {'1': 'acStatus', '3': 2, '4': 1, '5': 9, '10': 'acStatus'},
    {'1': 'doorLockStatus', '3': 3, '4': 1, '5': 9, '10': 'doorLockStatus'},
    {'1': 'windowStatus', '3': 4, '4': 1, '5': 9, '10': 'windowStatus'},
    {'1': 'engineStatus', '3': 5, '4': 1, '5': 9, '10': 'engineStatus'},
    {'1': 'tailDoorLockStatus', '3': 6, '4': 1, '5': 9, '10': 'tailDoorLockStatus'},
    {'1': 'lowBeamLight', '3': 7, '4': 1, '5': 9, '10': 'lowBeamLight'},
    {'1': 'dipHeadLight', '3': 8, '4': 1, '5': 9, '10': 'dipHeadLight'},
    {'1': 'sentinelModeStatus', '3': 9, '4': 1, '5': 9, '10': 'sentinelModeStatus'},
    {'1': 'tailDoorOpenStatus', '3': 10, '4': 1, '5': 9, '10': 'tailDoorOpenStatus'},
    {'1': 'door1LockStatus', '3': 11, '4': 1, '5': 9, '10': 'door1LockStatus'},
    {'1': 'door2LockStatus', '3': 12, '4': 1, '5': 9, '10': 'door2LockStatus'},
    {'1': 'door3LockStatus', '3': 13, '4': 1, '5': 9, '10': 'door3LockStatus'},
    {'1': 'door4LockStatus', '3': 14, '4': 1, '5': 9, '10': 'door4LockStatus'},
    {'1': 'doorOpenStatus', '3': 15, '4': 1, '5': 9, '10': 'doorOpenStatus'},
    {'1': 'door1OpenStatus', '3': 16, '4': 1, '5': 9, '10': 'door1OpenStatus'},
    {'1': 'door2OpenStatus', '3': 17, '4': 1, '5': 9, '10': 'door2OpenStatus'},
    {'1': 'door3OpenStatus', '3': 18, '4': 1, '5': 9, '10': 'door3OpenStatus'},
    {'1': 'door4OpenStatus', '3': 19, '4': 1, '5': 9, '10': 'door4OpenStatus'},
    {'1': 'window1Status', '3': 20, '4': 1, '5': 9, '10': 'window1Status'},
    {'1': 'window2Status', '3': 21, '4': 1, '5': 9, '10': 'window2Status'},
    {'1': 'window3Status', '3': 22, '4': 1, '5': 9, '10': 'window3Status'},
    {'1': 'window4Status', '3': 23, '4': 1, '5': 9, '10': 'window4Status'},
    {'1': 'topWindowStatus', '3': 24, '4': 1, '5': 9, '10': 'topWindowStatus'},
    {'1': 'autoGearStatus', '3': 25, '4': 1, '5': 9, '10': 'autoGearStatus'},
    {'1': 'manualGearStatus', '3': 26, '4': 1, '5': 9, '10': 'manualGearStatus'},
    {'1': 'keyStatus', '3': 27, '4': 1, '5': 9, '10': 'keyStatus'},
    {'1': 'acTemperatureGear', '3': 28, '4': 1, '5': 9, '10': 'acTemperatureGear'},
    {'1': 'acWindGear', '3': 29, '4': 1, '5': 9, '10': 'acWindGear'},
    {'1': 'leftBatteryPower', '3': 30, '4': 1, '5': 9, '10': 'leftBatteryPower'},
    {'1': 'leftFuel', '3': 31, '4': 1, '5': 9, '10': 'leftFuel'},
    {'1': 'mileage', '3': 32, '4': 1, '5': 9, '10': 'mileage'},
    {'1': 'leftMileage', '3': 33, '4': 1, '5': 9, '10': 'leftMileage'},
    {'1': 'batterySoc', '3': 34, '4': 1, '5': 9, '10': 'batterySoc'},
    {'1': 'current', '3': 35, '4': 1, '5': 9, '10': 'current'},
    {'1': 'voltage', '3': 36, '4': 1, '5': 9, '10': 'voltage'},
    {'1': 'batAvgTemp', '3': 37, '4': 1, '5': 9, '10': 'batAvgTemp'},
    {'1': 'batMaxTemp', '3': 38, '4': 1, '5': 9, '10': 'batMaxTemp'},
    {'1': 'batMinTemp', '3': 39, '4': 1, '5': 9, '10': 'batMinTemp'},
    {'1': 'tmActTemp', '3': 40, '4': 1, '5': 9, '10': 'tmActTemp'},
    {'1': 'invActTemp', '3': 41, '4': 1, '5': 9, '10': 'invActTemp'},
    {'1': 'accActPos', '3': 42, '4': 1, '5': 9, '10': 'accActPos'},
    {'1': 'brakPedalPos', '3': 43, '4': 1, '5': 9, '10': 'brakPedalPos'},
    {'1': 'strWhAng', '3': 44, '4': 1, '5': 9, '10': 'strWhAng'},
    {'1': 'vehSpdAvgDrvn', '3': 45, '4': 1, '5': 9, '10': 'vehSpdAvgDrvn'},
    {'1': 'obcOtpCur', '3': 46, '4': 1, '5': 9, '10': 'obcOtpCur'},
    {'1': 'vecChrgingSts', '3': 47, '4': 1, '5': 9, '10': 'vecChrgingSts'},
    {'1': 'vecChrgStsIndOn', '3': 48, '4': 1, '5': 9, '10': 'vecChrgStsIndOn'},
    {'1': 'obcTemp', '3': 49, '4': 1, '5': 9, '10': 'obcTemp'},
    {'1': 'batSOH', '3': 50, '4': 1, '5': 9, '10': 'batSOH'},
    {'1': 'lowBatVol', '3': 51, '4': 1, '5': 9, '10': 'lowBatVol'},
    {'1': 'leftTurnLight', '3': 52, '4': 1, '5': 9, '10': 'leftTurnLight'},
    {'1': 'rightTurnLight', '3': 53, '4': 1, '5': 9, '10': 'rightTurnLight'},
    {'1': 'positionLight', '3': 54, '4': 1, '5': 9, '10': 'positionLight'},
    {'1': 'frontFogLight', '3': 55, '4': 1, '5': 9, '10': 'frontFogLight'},
    {'1': 'rearFogLight', '3': 56, '4': 1, '5': 9, '10': 'rearFogLight'},
    {'1': 'latitude', '3': 57, '4': 1, '5': 9, '10': 'latitude'},
    {'1': 'longitude', '3': 58, '4': 1, '5': 9, '10': 'longitude'},
    {'1': 'position', '3': 59, '4': 1, '5': 9, '10': 'position'},
    {'1': 'charging', '3': 60, '4': 1, '5': 9, '10': 'charging'},
    {'1': 'wireConnect', '3': 61, '4': 1, '5': 9, '10': 'wireConnect'},
    {'1': 'rechargeStatus', '3': 62, '4': 1, '5': 9, '10': 'rechargeStatus'},
    {'1': 'window1OpenDegree', '3': 63, '4': 1, '5': 9, '10': 'window1OpenDegree'},
    {'1': 'window2OpenDegree', '3': 64, '4': 1, '5': 9, '10': 'window2OpenDegree'},
    {'1': 'window3OpenDegree', '3': 65, '4': 1, '5': 9, '10': 'window3OpenDegree'},
    {'1': 'window4OpenDegree', '3': 66, '4': 1, '5': 9, '10': 'window4OpenDegree'},
    {'1': 'seat1WindStatus', '3': 67, '4': 1, '5': 9, '10': 'seat1WindStatus'},
    {'1': 'seat2WindStatus', '3': 68, '4': 1, '5': 9, '10': 'seat2WindStatus'},
    {'1': 'seat3WindStatus', '3': 69, '4': 1, '5': 9, '10': 'seat3WindStatus'},
    {'1': 'seat4WindStatus', '3': 70, '4': 1, '5': 9, '10': 'seat4WindStatus'},
    {'1': 'seat1HotStatus', '3': 71, '4': 1, '5': 9, '10': 'seat1HotStatus'},
    {'1': 'seat2HotStatus', '3': 72, '4': 1, '5': 9, '10': 'seat2HotStatus'},
    {'1': 'seat3HotStatus', '3': 73, '4': 1, '5': 9, '10': 'seat3HotStatus'},
    {'1': 'seat4HotStatus', '3': 74, '4': 1, '5': 9, '10': 'seat4HotStatus'},
  ],
};

/// Descriptor for `SgmwAppCarStatus`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List sgmwAppCarStatusDescriptor = $convert.base64Decode(
    'ChBTZ213QXBwQ2FyU3RhdHVzEiAKC2NvbGxlY3RUaW1lGAEgASgDUgtjb2xsZWN0VGltZRIaCg'
    'hhY1N0YXR1cxgCIAEoCVIIYWNTdGF0dXMSJgoOZG9vckxvY2tTdGF0dXMYAyABKAlSDmRvb3JM'
    'b2NrU3RhdHVzEiIKDHdpbmRvd1N0YXR1cxgEIAEoCVIMd2luZG93U3RhdHVzEiIKDGVuZ2luZV'
    'N0YXR1cxgFIAEoCVIMZW5naW5lU3RhdHVzEi4KEnRhaWxEb29yTG9ja1N0YXR1cxgGIAEoCVIS'
    'dGFpbERvb3JMb2NrU3RhdHVzEiIKDGxvd0JlYW1MaWdodBgHIAEoCVIMbG93QmVhbUxpZ2h0Ei'
    'IKDGRpcEhlYWRMaWdodBgIIAEoCVIMZGlwSGVhZExpZ2h0Ei4KEnNlbnRpbmVsTW9kZVN0YXR1'
    'cxgJIAEoCVISc2VudGluZWxNb2RlU3RhdHVzEi4KEnRhaWxEb29yT3BlblN0YXR1cxgKIAEoCV'
    'ISdGFpbERvb3JPcGVuU3RhdHVzEigKD2Rvb3IxTG9ja1N0YXR1cxgLIAEoCVIPZG9vcjFMb2Nr'
    'U3RhdHVzEigKD2Rvb3IyTG9ja1N0YXR1cxgMIAEoCVIPZG9vcjJMb2NrU3RhdHVzEigKD2Rvb3'
    'IzTG9ja1N0YXR1cxgNIAEoCVIPZG9vcjNMb2NrU3RhdHVzEigKD2Rvb3I0TG9ja1N0YXR1cxgO'
    'IAEoCVIPZG9vcjRMb2NrU3RhdHVzEiYKDmRvb3JPcGVuU3RhdHVzGA8gASgJUg5kb29yT3Blbl'
    'N0YXR1cxIoCg9kb29yMU9wZW5TdGF0dXMYECABKAlSD2Rvb3IxT3BlblN0YXR1cxIoCg9kb29y'
    'Mk9wZW5TdGF0dXMYESABKAlSD2Rvb3IyT3BlblN0YXR1cxIoCg9kb29yM09wZW5TdGF0dXMYEi'
    'ABKAlSD2Rvb3IzT3BlblN0YXR1cxIoCg9kb29yNE9wZW5TdGF0dXMYEyABKAlSD2Rvb3I0T3Bl'
    'blN0YXR1cxIkCg13aW5kb3cxU3RhdHVzGBQgASgJUg13aW5kb3cxU3RhdHVzEiQKDXdpbmRvdz'
    'JTdGF0dXMYFSABKAlSDXdpbmRvdzJTdGF0dXMSJAoNd2luZG93M1N0YXR1cxgWIAEoCVINd2lu'
    'ZG93M1N0YXR1cxIkCg13aW5kb3c0U3RhdHVzGBcgASgJUg13aW5kb3c0U3RhdHVzEigKD3RvcF'
    'dpbmRvd1N0YXR1cxgYIAEoCVIPdG9wV2luZG93U3RhdHVzEiYKDmF1dG9HZWFyU3RhdHVzGBkg'
    'ASgJUg5hdXRvR2VhclN0YXR1cxIqChBtYW51YWxHZWFyU3RhdHVzGBogASgJUhBtYW51YWxHZW'
    'FyU3RhdHVzEhwKCWtleVN0YXR1cxgbIAEoCVIJa2V5U3RhdHVzEiwKEWFjVGVtcGVyYXR1cmVH'
    'ZWFyGBwgASgJUhFhY1RlbXBlcmF0dXJlR2VhchIeCgphY1dpbmRHZWFyGB0gASgJUgphY1dpbm'
    'RHZWFyEioKEGxlZnRCYXR0ZXJ5UG93ZXIYHiABKAlSEGxlZnRCYXR0ZXJ5UG93ZXISGgoIbGVm'
    'dEZ1ZWwYHyABKAlSCGxlZnRGdWVsEhgKB21pbGVhZ2UYICABKAlSB21pbGVhZ2USIAoLbGVmdE'
    '1pbGVhZ2UYISABKAlSC2xlZnRNaWxlYWdlEh4KCmJhdHRlcnlTb2MYIiABKAlSCmJhdHRlcnlT'
    'b2MSGAoHY3VycmVudBgjIAEoCVIHY3VycmVudBIYCgd2b2x0YWdlGCQgASgJUgd2b2x0YWdlEh'
    '4KCmJhdEF2Z1RlbXAYJSABKAlSCmJhdEF2Z1RlbXASHgoKYmF0TWF4VGVtcBgmIAEoCVIKYmF0'
    'TWF4VGVtcBIeCgpiYXRNaW5UZW1wGCcgASgJUgpiYXRNaW5UZW1wEhwKCXRtQWN0VGVtcBgoIA'
    'EoCVIJdG1BY3RUZW1wEh4KCmludkFjdFRlbXAYKSABKAlSCmludkFjdFRlbXASHAoJYWNjQWN0'
    'UG9zGCogASgJUglhY2NBY3RQb3MSIgoMYnJha1BlZGFsUG9zGCsgASgJUgxicmFrUGVkYWxQb3'
    'MSGgoIc3RyV2hBbmcYLCABKAlSCHN0cldoQW5nEiQKDXZlaFNwZEF2Z0Rydm4YLSABKAlSDXZl'
    'aFNwZEF2Z0Rydm4SHAoJb2JjT3RwQ3VyGC4gASgJUglvYmNPdHBDdXISJAoNdmVjQ2hyZ2luZ1'
    'N0cxgvIAEoCVINdmVjQ2hyZ2luZ1N0cxIoCg92ZWNDaHJnU3RzSW5kT24YMCABKAlSD3ZlY0No'
    'cmdTdHNJbmRPbhIYCgdvYmNUZW1wGDEgASgJUgdvYmNUZW1wEhYKBmJhdFNPSBgyIAEoCVIGYm'
    'F0U09IEhwKCWxvd0JhdFZvbBgzIAEoCVIJbG93QmF0Vm9sEiQKDWxlZnRUdXJuTGlnaHQYNCAB'
    'KAlSDWxlZnRUdXJuTGlnaHQSJgoOcmlnaHRUdXJuTGlnaHQYNSABKAlSDnJpZ2h0VHVybkxpZ2'
    'h0EiQKDXBvc2l0aW9uTGlnaHQYNiABKAlSDXBvc2l0aW9uTGlnaHQSJAoNZnJvbnRGb2dMaWdo'
    'dBg3IAEoCVINZnJvbnRGb2dMaWdodBIiCgxyZWFyRm9nTGlnaHQYOCABKAlSDHJlYXJGb2dMaW'
    'dodBIaCghsYXRpdHVkZRg5IAEoCVIIbGF0aXR1ZGUSHAoJbG9uZ2l0dWRlGDogASgJUglsb25n'
    'aXR1ZGUSGgoIcG9zaXRpb24YOyABKAlSCHBvc2l0aW9uEhoKCGNoYXJnaW5nGDwgASgJUghjaG'
    'FyZ2luZxIgCgt3aXJlQ29ubmVjdBg9IAEoCVILd2lyZUNvbm5lY3QSJgoOcmVjaGFyZ2VTdGF0'
    'dXMYPiABKAlSDnJlY2hhcmdlU3RhdHVzEiwKEXdpbmRvdzFPcGVuRGVncmVlGD8gASgJUhF3aW'
    '5kb3cxT3BlbkRlZ3JlZRIsChF3aW5kb3cyT3BlbkRlZ3JlZRhAIAEoCVIRd2luZG93Mk9wZW5E'
    'ZWdyZWUSLAoRd2luZG93M09wZW5EZWdyZWUYQSABKAlSEXdpbmRvdzNPcGVuRGVncmVlEiwKEX'
    'dpbmRvdzRPcGVuRGVncmVlGEIgASgJUhF3aW5kb3c0T3BlbkRlZ3JlZRIoCg9zZWF0MVdpbmRT'
    'dGF0dXMYQyABKAlSD3NlYXQxV2luZFN0YXR1cxIoCg9zZWF0MldpbmRTdGF0dXMYRCABKAlSD3'
    'NlYXQyV2luZFN0YXR1cxIoCg9zZWF0M1dpbmRTdGF0dXMYRSABKAlSD3NlYXQzV2luZFN0YXR1'
    'cxIoCg9zZWF0NFdpbmRTdGF0dXMYRiABKAlSD3NlYXQ0V2luZFN0YXR1cxImCg5zZWF0MUhvdF'
    'N0YXR1cxhHIAEoCVIOc2VhdDFIb3RTdGF0dXMSJgoOc2VhdDJIb3RTdGF0dXMYSCABKAlSDnNl'
    'YXQySG90U3RhdHVzEiYKDnNlYXQzSG90U3RhdHVzGEkgASgJUg5zZWF0M0hvdFN0YXR1cxImCg'
    '5zZWF0NEhvdFN0YXR1cxhKIAEoCVIOc2VhdDRIb3RTdGF0dXM=');

