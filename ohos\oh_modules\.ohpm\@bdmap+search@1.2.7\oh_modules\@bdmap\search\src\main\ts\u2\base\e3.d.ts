import x4 from '@ohos.worker';
export declare class SearchParseWorker {
    private workerInstance;
    private workerPath;
    constructor(y4: string);
    createWorker(): x4.ThreadWorker;
    newWorker(): x4.ThreadWorker;
    getWorker(): x4.ThreadWorker | null;
    destroy(): void;
    reset(): x4.ThreadWorker;
}
export interface ParseModel {
    json: string;
    coordType: number;
    searchType: number;
}
