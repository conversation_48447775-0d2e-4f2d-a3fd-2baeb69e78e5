// @keepTs
// @ts-nocheck
/**
 * 引用消息提供者
 * Created on 2024/10/21
 * <AUTHOR>
 * @version 1.0.0
 */
import { ReferenceMessage } from '@rongcloud/imlib';
import { UiMessage } from '../../../../conversation/model/UiMessage';
import { BaseMessageItemProvider } from '../../../../conversation/item/provider/BaseMessageItemProvider';
export declare class ReferenceMessageItemProvider extends BaseMessageItemProvider<ReferenceMessage> {
    getMessageWrapBuilder(): WrappedBuilder<[
        Context,
        UiMessage,
        number
    ]>;
    isShowSummaryName(u194: Context, v194: ReferenceMessage): boolean;
    getSummaryTextByMessageContent(q194: Context, r194: ReferenceMessage): Promise<MutableStyledString>;
}
@Builder
export declare function bindTextMessageData(f194: Context, g194: UiMessage, h194: number): void;
@Component
export declare struct QuoteMessageView {
    @Require
    @Prop
    context: Context;
    @Require
    @Prop
    index: number;
    @ObjectLink
    uiMessage: UiMessage;
    @State
    referenceMessage: ReferenceMessage;
    @State
    referMsgUserName: string;
    private styledString;
    controller: TextController;
    private mentionUserIdList;
    private fontColor;
    private userDataListener;
    aboutToAppear(): Promise<void>;
    aboutToDisappear(): void;
    private updateMentionInfo;
    private handlerClick;
    build(): void;
    @Builder
    ReferenceView(): Promise<void>;
    @Builder
    SelfTextView(): void;
}
export interface FileInformation {
    name: string;
    remoteUrl: string;
    size: number;
    type: string;
    localPath: string;
}
