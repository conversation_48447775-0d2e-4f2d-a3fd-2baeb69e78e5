// @keepTs
// @ts-nocheck
import { PushNotificationLevel } from '@rongcloud/imlib';
import { NotificationService } from '../../notification/NotificationService';
import { NotificationInterceptor } from '../../notification/listener/NotificationInterceptor';
export declare class InnerNotificationServiceImpl implements NotificationService {
    private readonly TAG;
    private interceptor;
    private cache;
    private listener;
    onInit(): void;
    private handlerMessage;
    /**
     * 获取通知行为意图
     * @returns
     */
    private getWantAgent;
    /**
     * 获取BundleName
     * @returns
     */
    private getBundleName;
    /**
     * 获取AbilityName
     * @returns
     */
    private getAbilityName;
    setNotificationInterceptor(e324: NotificationInterceptor): void;
    removeNotificationInterceptor(d324: NotificationInterceptor): void;
    private getConversationNotificationLevel;
    /**
     * 设置会话免打扰级别缓存
     * @param key  conversationType + ";" + targetId + ";" + channelId
     * @param level 免打扰级别
     * @returns
     */
    setConversationNotificationLevel(w323: string, x323: PushNotificationLevel): void;
}
