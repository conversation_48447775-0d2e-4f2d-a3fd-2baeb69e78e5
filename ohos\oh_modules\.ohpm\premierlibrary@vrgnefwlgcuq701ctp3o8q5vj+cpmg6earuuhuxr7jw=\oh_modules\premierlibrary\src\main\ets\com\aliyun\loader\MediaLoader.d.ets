import { ErrorInfo } from '../player/bean/ErrorInfo';
export declare const OLD_CODE_EADDED: number;
export declare const OLD_CODE_ENOT_ENABLE: number;
export declare class MediaLoader {
    private mOnLoadStatusListener?;
    private static sInstance;
    static getInstance(): MediaLoader;
    constructor();
    setOnLoadStatusListener(r4: OnLoadStatusListener): void;
    /**
     * 开始加载文件。异步加载。可以同时preload多个。
     *
     * @param url  视频文件地址
     * @param duration 加载的时长大小.单位：毫秒
     */
    /****
     * Start loading. Asynchronous loading. Multiple preloads can be performed simultaneously.
     *
     * @param url Video file url
     * @param duration Load duration. Unit: millisecond
     */
    load(o4: string, p4: number): void;
    /**
     * 开始加载文件。异步加载。可以同时preload多个。
     *
     * @param url  视频文件地址
     * @param duration 加载的时长大小.单位：毫秒
     * @param bandwidth 加载多码率HLS时默认的码率，将会选择与之最接近的档位，单位:bps
     */
    /****
     * Start loading. Asynchronous loading. Multiple preloads can be performed simultaneously.
     *
     * @param url Video file url
     * @param duration Load duration. Unit: millisecond
     * @param bandwidth Load default bitrate for multi-bitrate HLS stream，the nearest stream will be selected. Unit:bps
     */
    loadWithBandWidth(k4: string, l4: number, m4: number): void;
    /**
     * 取消加载。注意：不会删除已经下载的文件。
     *
     * @param url 视频文件地址。如果为null或者空，则取消所有的加载
     */
    /****
     * Cancel loading. Note: Downloaded files will not be deleted.
     *
     * @param url Video file url. If null or empty, all loading be canceled.
     */
    cancel(i4: string | undefined): void;
    /**
     * 暂停加载
     *
     * @param url 视频文件地址。如果为null或者空，则暂停所有的加载
     */
    /****
     * Pause loading
     *
     * @param url Video file url. If null or empty, all loading be paused.
     */
    pause(g4: string): void;
    /**
     * 继续加载
     *
     * @param url 视频文件地址。如果为null或者空，则继续所有的加载
     */
    /****
     * Resume loading
     *
     * @param url Video file url. If null or empty, all loading be started.
     */
    resume(e4: string): void;
    protected onError: Function;
    protected onErrorV2: Function;
    protected onCompleted: Function;
    protected onCanceled: Function;
}
export interface OnLoadStatusListener {
    /**
     * 加载出错
     *
     * @param url  视频url
     * @param code 错误码，-300表示同一个url已经加载过，-301表示地缓存未打开，预加载失败
     * @param msg  错误描述
     * @Deprecated
     */
    /****
     * Load error
     *
     * @param url Video URL
     * @param code Error code，-300 means the same url has been loaded, -301 means the cache is not open, preload failed
     * @param msg Error description
     * @Deprecated
     */
    onError: (url: string, code: number, msg: string) => void;
    /**
     * 加载出错 V2，建议集成该回调
     *
     * @param url  视频url
     * @param code 错误码，-300表示同一个url已经加载过，-301表示地缓存未打开，预加载失败
     * @param msg  错误描述
     */
    /****
     * Load error V2
     *
     * @param url Video URL
     * @param code Error code，-300 means the same url has been loaded, -301 means the cache is not open, preload failed
     * @param msg Error description
     */
    onErrorV2: (url: string, errorInfo: ErrorInfo) => void;
    /**
     * 加载完成
     *
     * @param url 视频url
     */
    /****
     * Load complete
     *
     * @param url Video URL
     */
    onCompleted: (url: string) => void;
    /**
     * 取消加载
     *
     * @param url 视频url
     */
    /****
     * Load canceled
     *
     * @param url Video URL
     */
    onCancel: (url: string) => void;
}
