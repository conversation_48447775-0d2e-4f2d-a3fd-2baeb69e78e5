// @keepTs
// @ts-nocheck
/**
 * Created on 2024/07/15
 * <AUTHOR>
 */
import { ConversationIdentifier } from '@rongcloud/imlib';
import { GetDataDirection } from '../../internal/enum/RefreshDirectionType';
import { ConversationStateModel } from '../../internal/conversation/model/ConversationStateModel';
/**
 * ConversationComponent 的初始化数据，用于明确指定 ConversationComponent 加载哪个会话的消息
 * @version 1.0.0
 */
export declare class ConversationComponentData {
    private _state;
    private _timestamp?;
    private _direction;
    private _conId;
    /**
     * @deprecated 从 1.6.0 版本开始 消息ID满足不了部分场景，SDK内部统一使用 timestamp 判断。
     */
    private _messageId?;
    constructor(w26: ConversationIdentifier);
    get state(): ConversationStateModel;
    set state(v26: ConversationStateModel);
    set messageId(u26: number | undefined);
    get messageId(): number | undefined;
    set timestamp(t26: number | undefined);
    get timestamp(): number | undefined;
    get conId(): ConversationIdentifier;
    set direction(s26: GetDataDirection);
    get direction(): GetDataDirection;
}
