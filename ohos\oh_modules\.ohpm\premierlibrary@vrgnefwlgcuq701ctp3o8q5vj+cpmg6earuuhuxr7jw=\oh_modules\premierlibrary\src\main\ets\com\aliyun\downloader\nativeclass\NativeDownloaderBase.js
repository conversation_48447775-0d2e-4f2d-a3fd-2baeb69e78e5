import premierlibrary from 'libpremierlibrary.so';
import { Log } from '../../utils/Log';
import { findErrorCodeByValue } from '../../player/bean/PlayerErrorCode';
import { ErrorInfo } from '../../player/bean/ErrorInfo';
import { ObjCreateHelper } from '../../utils/ObjCreateHelper';
const log = new Log('NativeDownloaderBase');
export class NativeDownloaderBase {
    constructor(w1) {
        this.mNativeContext = 0;
        this.objHelper = new ObjCreateHelper();
        this.getNativeContext = () => {
            log.info(`getNativeContext, address: ${this.mNativeContext}`);
            return this.mNativeContext;
        };
        this.setNativeContext = (q2) => {
            log.info(`setNativeContext, address: ${q2}`);
            this.mNativeContext = q2;
        };
        this.getContext = () => {
            log.info(`getNativeContext, address: ${this.mNativeContext}`);
            return this.mContext;
        };
        this.onPrepared = (p2) => {
            log.info("onPrepared(mediaInfo) = " + p2);
            if (this.mOnPreparedListener != null) {
                this.mOnPreparedListener.onPrepared(p2);
            }
        };
        this.onCompletion = () => {
            log.info("onCompletion()");
            if (this.mOnCompletionListener != null) {
                this.mOnCompletionListener.onCompletion();
            }
        };
        this.onProgress = (n2, o2) => {
            log.info(`onProgress() .. type = ${n2}, percent = ${o2}`);
            if (this.mOnProgressListener != null) {
                if (n2 == 0) {
                    this.mOnProgressListener.onDownloadingProgress(o2);
                }
                else {
                    this.mOnProgressListener.onProcessingProgress(o2);
                }
            }
        };
        this.onError = (h2, i2, j2, k2) => {
            log.info(`onError() .. code = ${h2}, msg = ${i2}, extra = ${j2}, requestid = ${k2}`);
            let l2 = findErrorCodeByValue(h2);
            if (this.mOnErrorListener != null) {
                let m2 = new ErrorInfo();
                m2.setCode(l2);
                m2.setMsg(i2);
                m2.setExtra(j2);
                this.mOnErrorListener.onError(m2);
            }
        };
        this.nConvertURLCallback = (f2, g2) => {
            log.info(`nConvertURLCallback, srcURL = ${f2}, srcFormat = ${g2}`);
            if (NativeDownloaderBase.sConvertURLCallback != null) {
                return NativeDownloaderBase.sConvertURLCallback.convertURL(f2, g2);
            }
            return "";
        };
        log.info('constructor_');
        this.mContext = w1;
        premierlibrary.nDownloaderConstruct(this);
    }
    setOnPreparedListener(v1) {
        this.mOnPreparedListener = v1;
    }
    setOnCompletionListener(u1) {
        this.mOnCompletionListener = u1;
    }
    setOnProgressListener(t1) {
        this.mOnProgressListener = t1;
    }
    setOnErrorListener(s1) {
        this.mOnErrorListener = s1;
    }
    static setConvertURLCallback(r1) {
        NativeDownloaderBase.sConvertURLCallback = r1;
    }
    start() {
        log.info('nStart');
        premierlibrary.nDownloaderStart(this);
    }
    release() {
        log.info('Releasing Downloader');
        premierlibrary.nDownloaderRelease(this);
    }
    setSaveDir(q1) {
        log.info(`Setting save directory: ${q1}`);
        premierlibrary.nDownloaderSetSaveDir(this, q1);
    }
    prepareVidSts(p1) {
        log.info('Preparing with VidSts');
        premierlibrary.nDownloaderPrepareByVidSts(this, p1);
    }
    prepareVidAuth(o1) {
        log.info('prepareVidAuth');
        premierlibrary.nDownloaderPrepareByVidAuth(this, o1);
    }
    selectItem(n1) {
        log.info(`Selecting item at index: ${n1}`);
        premierlibrary.nDownloaderSelectItem(this, n1);
    }
    updateVidStsSource(m1) {
        log.info('Updating source with VidSts');
        premierlibrary.nDownloaderUpdateSourceByVidSts(this, m1);
    }
    updateVidAuthSource(l1) {
        log.info('Updating source with VidAuth');
        premierlibrary.nDownloaderUpdateSourceByVidAuth(this, l1);
    }
    stop() {
        log.info('Stopping Downloader');
        premierlibrary.nDownloaderStop(this);
    }
    deleteFile() {
        log.info('Deleting file');
        premierlibrary.nDownloaderDeleteFile(this);
    }
    getFilePath() {
        log.info('Getting file path');
        return premierlibrary.nDownloaderGetFilePath(this);
    }
    setDownloaderConfig(k1) {
        log.info('Setting downloader config');
        premierlibrary.nDownloaderSetDownloaderConfig(this, k1);
    }
    static deleteFileStatic(g1, h1, i1, j1) {
        log.info(`Deleting file statically with saveDir: ${g1}, vid: ${h1}, format: ${i1}, index: ${j1}`);
        return premierlibrary.nDownloaderSDeleteFile(g1, h1, i1, j1);
    }
}
