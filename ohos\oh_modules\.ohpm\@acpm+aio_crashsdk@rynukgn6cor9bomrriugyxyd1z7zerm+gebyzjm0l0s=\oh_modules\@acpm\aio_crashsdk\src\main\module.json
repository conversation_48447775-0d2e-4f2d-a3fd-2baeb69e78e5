{"app": {"bundleName": "com.alivc.acpm.test", "versionCode": 1000000, "versionName": "1.0.0", "minAPIVersion": 50000012, "targetAPIVersion": 50000012, "apiReleaseType": "Release", "compileSdkVersion": "********", "compileSdkType": "HarmonyOS", "appEnvironments": [], "bundleType": "app"}, "module": {"name": "aio_crashsdk", "type": "har", "description": "AIOCrashSDK", "deviceTypes": ["phone", "tablet", "2in1"], "requestPermissions": [{"name": "ohos.permission.INTERNET"}], "packageName": "@acpm/aio_crashsdk", "installationFree": false, "virtualMachine": "ark12.0.2.0", "compileMode": "esmodule", "dependencies": []}}