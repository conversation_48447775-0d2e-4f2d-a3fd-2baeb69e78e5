import { Context } from '@ohos.abilityAccessCtrl';
import { VidAuth } from '../../player/source/VidAuth';
import { VidSts } from '../../player/source/VidSts';
import { DownloaderConfig } from '../DownloaderConfig';
import { OnPreparedListener, OnCompletionListener, OnProgressListener, OnErrorListener, ConvertURLCallback } from '../AliMediaDownloader';
export declare class NativeDownloaderBase {
    private mNativeContext;
    private mContext?;
    private mOnPreparedListener?;
    private mOnCompletionListener?;
    private mOnProgressListener?;
    private mOnErrorListener?;
    private static sConvertURLCallback?;
    private objHelper;
    private getNativeContext;
    private setNativeContext;
    private getContext;
    constructor(w1: Context);
    private onPrepared;
    private onCompletion;
    private onProgress;
    private onError;
    private nConvertURLCallback;
    setOnPreparedListener(v1: OnPreparedListener): void;
    setOnCompletionListener(u1: OnCompletionListener): void;
    setOnProgressListener(t1: OnProgressListener): void;
    setOnErrorListener(s1: OnErrorListener): void;
    static setConvertURLCallback(r1: ConvertURLCallback): void;
    start(): void;
    release(): void;
    setSaveDir(q1: string): void;
    prepareVidSts(p1: VidSts): void;
    prepareVidAuth(o1: VidAuth): void;
    selectItem(n1: number): void;
    updateVidStsSource(m1: VidSts): void;
    updateVidAuthSource(l1: VidAuth): void;
    stop(): void;
    deleteFile(): void;
    getFilePath(): string;
    setDownloaderConfig(k1: DownloaderConfig): void;
    static deleteFileStatic(g1: string, h1: string, i1: string, j1: number): number;
}
