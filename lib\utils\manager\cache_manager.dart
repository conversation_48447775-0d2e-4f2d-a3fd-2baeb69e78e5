import 'dart:io';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:dio/dio.dart';

import 'log_manager.dart';

class CacheManager {
  /// 私有构造函数（单例模式）
  CacheManager._privateConstructor();

  /// 单例实例
  static final CacheManager instance = CacheManager._privateConstructor();

  /// 获取缓存大小（单位：字节）
  Future<int> getCacheSize() async {
    int totalSize = 0;
    totalSize += await _getDirectorySize(await getTemporaryDirectory());
    totalSize += await _getDirectorySize(await getApplicationDocumentsDirectory());
    return totalSize;
  }

  /// **清除缓存（带进度 & 延迟）**
  Future<void> clearCacheWithProgress(Function(double progress, String message) onProgress) async {
    int stepCount = 5;  // 共有 5 个清理步骤
    double stepProgress = 1 / stepCount;
    double currentProgress = 0.0;

    /// **更新进度，并增加 1 秒延迟**
    Future<void> updateProgress(String message) async {
      currentProgress += stepProgress;
      onProgress(currentProgress.clamp(0.0, 1.0), message);
      await Future.delayed(const Duration(milliseconds: 500)); // **增加 500毫秒延迟，方便调试**
    }

    // 1️⃣ 清除临时文件
    onProgress(0.0, "正在清理临时文件...");
    await _clearTempDirectory();
    await updateProgress("临时文件清理完成");

    // 2️⃣ 清除图片缓存
    onProgress(currentProgress, "正在清理图片缓存...");
    await _clearImageCache();
    await updateProgress("图片缓存清理完成");

    // // 3️⃣ 清除 SharedPreferences
    // onProgress(currentProgress, "正在清理应用设置缓存...");
    // await _clearSharedPreferences();
    // await updateProgress("应用设置缓存清理完成");

    // 4️⃣ 清除 WebView 缓存
    onProgress(currentProgress, "正在清理 WebView 缓存...");
    await _clearWebViewCache();
    await updateProgress("WebView 缓存清理完成");

    // 5️⃣ 清除 Dio 网络缓存
    onProgress(currentProgress, "正在清理网络请求缓存...");
    await _clearDioCache();
    await updateProgress("网络请求缓存清理完成");

    // **清理完成**
    onProgress(1.0, "清理完成！");
  }

  /// **获取目录大小**
  Future<int> _getDirectorySize(Directory dir) async {
    if (!dir.existsSync()) return 0;
    int totalSize = 0;
    try {
      for (var file in dir.listSync(recursive: true)) {
        if (file is File) {
          totalSize += await file.length();
        }
      }
    } catch (e) {
      LogManager().debug("获取缓存大小失败: $e");
    }
    return totalSize;
  }

  /// **清除临时目录**
  Future<void> _clearTempDirectory() async {
    final dir = await getTemporaryDirectory();
    if (dir.existsSync()) {
      dir.deleteSync(recursive: true);
    }
  }

  /// **清除 `cached_network_image` 图片缓存**
  Future<void> _clearImageCache() async {
    await DefaultCacheManager().emptyCache();
  }

  /// **清除 SharedPreferences**
  Future<void> _clearSharedPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }

  /// **清除 WebView 缓存**
  Future<void> _clearWebViewCache() async {
    await WebViewCookieManager().clearCookies();
  }

  /// **清除 Dio 网络缓存**
  Future<void> _clearDioCache() async {
    final dio = Dio();
    dio.interceptors.clear();
  }
}