import 'dart:typed_data';
import 'package:wuling_flutter_app/utils/str_util.dart';

import '../../utils/manager/log_manager.dart';

/// 控制响应类型枚举
enum ControlResponseType {
  failedType(0),     // 失败
  succeededType(1),  // 成功
  unknownType(2);    // 未知

  final int value;

  const ControlResponseType(this.value);
}

/// 泊车反馈状态码枚举
enum ParkingStatusCode {
  parkingStatusUnknown(0x00),          // 非泊车反馈状态码
  parkingStatusReady(0x01),            // Ready
  parkingStatusForward(0x02),          // Forward
  parkingStatusBackoff(0x03),          // Back OFF
  parkingStatusBreak(0x04),            // Break
  parkingStatusFinish(0x05),           // Finish
  parkingStatusFailure(0x06),          // Failure
  parkingStatusAdasTimeout(0x07),      // ADAS 反馈超时
  parkingStatusPathBarrier(0x08),      // 路径内有障碍物
  parkingStatusPlanTooMany(0x09),      // 规划次数过多
  parkingStatusDoorOpen(0x0A),         // 车门未关闭
  parkingStatusManualStop(0x0B),       // 手动停止
  parkingStatusCommandError(0x0C),     // 指令执行错误
  parkingStatusFunctionError(0x0D),    // 功能故障
  parkingStatusUcuSpi(0x0E),           // UCU SPI故障
  parkingStatusPowerOffFailure(0x0F),  // 下电失败
  parkingStatusParkTypeError(0x10),    // 泊出方式，泊出类型错误
  parkingStatusCommandTimeout(0x16),   // 指令超时
  ;

  final int value;

  const ParkingStatusCode(this.value);
}

/// 一键泊入反馈状态码枚举
enum MpParkinStatusCode {
  mpParkinStatusUnknown(0x00),               // 非泊车反馈状态码
  mpParkinStatusPending(0x01),               // 泊车进行中
  mpParkinStatusSuccess(0x02),               // 泊车成功，APA 系统结束
  mpParkinStatusFailure(0x03),               // 泊车失败，APA 系统结束
  mpParkinStatusManualTakeover(0x04),        // 人工接管
  mpParkinStatusOutVehicleNotWork(0x05),     // 车外泊车模式不可用，APA 退出
  mpParkinStatusFunctionNotWork(0x06),       // 功能不可用
  mpParkinStatusPause(0x07),                 // 泊车暂停
  mpParkinStatusTimeout(0x08),               // 超时
  ;

  final int value;

  const MpParkinStatusCode(this.value);
}

/// 一键泊出反馈状态码枚举
enum MpParkoutStatusCode {
  mpParkoutStatusUnknown(0x00),              // 非泊车反馈状态码
  mpParkoutStatusReady(0x01),                // 出库就绪
  mpParkoutStatusVehicleNotReady(0x02),      // 未上高压
  mpParkoutStatusPowerNotReady(0x03),        // 未进入遥控上电
  mpParkoutStatusNotReady(0x04),             // 功能未就绪
  mpParkoutStatusSuccess(0x05),              // 出库成功
  mpParkoutStatusFailure(0x06),              // 出库失败
  mpParkoutStatusManualTakeover(0x07),       // 人工接管，出库失败
  mpParkoutStatusFunctionNotWork(0x08),      // 功能不可用
  mpParkoutStatusPause(0x09),                // 出库暂停
  mpParkoutStatusPending(0x0A),              // 出库进行中
  mpParkoutStatusTimeout(0x0B),              // 超时
  ;

  final int value;

  const MpParkoutStatusCode(this.value);
}

/// EQ100智能泊车辅助运行状态码枚举
enum MpIpaOperationStatusCode {
  mpIpaOperationStatusUnknown(0x00),         // Off
  mpIpaOperationStatusNotReady(0x01),        // No Ready
  mpIpaOperationStatusReady(0x02),           // Ready
  mpIpaOperationStatusDetecting(0x03),       // Detecting
  mpIpaOperationStatusDetectSuspend(0x04),   // Detecting suspend
  mpIpaOperationStatusDetected(0x05),        // Detected
  mpIpaOperationStatusBgDetecting(0x06),     // BG_Detecting
  mpIpaOperationStatusBgDetected(0x07),      // BG_Detected
  mpIpaOperationStatusPrepare(0x08),         // Prepare
  mpIpaOperationStatusParking(0x09),         // Parking
  mpIpaOperationStatusParkingSuspend(0x0A),  // Parking Suspend
  mpIpaOperationStatusFinishSuccess(0x0B),   // Finish_Success
  mpIpaOperationStatusFinishFailure(0x0C),   // Finish_Failure
  mpIpaOperationStatusFinishTerminate(0x0D), // Finish_Termination
  mpIpaOperationStatusErrorIntFault(0x0E),   // Error_Int_Fault
  mpIpaOperationStatusErrorExtFault(0x0F),   // Error_Ext_Fault
  ;

  final int value;

  const MpIpaOperationStatusCode(this.value);
}

/// EQ100智能出库运行状态码枚举
enum MpApoOperationStatusCode {
  mpApoOperationStatusUnknown(0x00),         // Off
  mpApoOperationStatusNotReady(0x01),        // No Ready
  mpApoOperationStatusReady(0x02),           // Ready
  mpApoOperationStatusPrepare(0x03),         // Prepare
  mpApoOperationStatusParkingOut(0x04),      // ParkingOut
  mpApoOperationStatusSuspend(0x05),         // ParkingOut Suspend
  mpApoOperationStatusFinishSuccess(0x06),   // Finish_Success
  mpApoOperationStatusFinishFailure(0x07),   // Finish_Failure
  mpApoOperationStatusFinishTerminate(0x08), // Finish_Termination
  mpApoOperationStatusErrorIntFault(0x09),   // Error_Int_Fault
  mpApoOperationStatusErrorExtFault(0x0A),   // Error_Ext_Fault
  mpApoOperationStatusPrepareSuspend(0x0B),  // Prepare Suspend
  ;

  final int value;

  const MpApoOperationStatusCode(this.value);
}

/// UcuControlResponseFrame 类，用于解析 UCU 控制回复报文
class UcuControlResponseFrame {
  String? msgResource;      // 加密前缀
  String? serviceId;        // 服务类型
  String? subfunction;      // 功能类型
  String? randomData;       // 随机数
  String? payloadLength;    // payload长度
  String? errorCode;        // 错误码
  String? parkingStatus1;   // 泊车状态码1
  String? parkingStatus2;   // 泊车状态码2
  String? dataCheck;        // 数据校验
  String? encryptedFrame;   // 加密帧
  Uint8List? originalFrame; // 原始数据帧
  ControlResponseType? responseType; // 控制响应类型

  UcuControlResponseFrame();

  /// 使用数据帧和密钥初始化
  UcuControlResponseFrame.fromDataFrame(Uint8List ucuResponseData, String key) {
    try {
      // 将数据转换为十六进制字符串
      String ucuRequestDataStr = StrUtil.bytesToHexString(ucuResponseData);
      String mainDataStr;
      int len = ucuRequestDataStr.length;
      LogManager().debug("[E300BLE]---UCU控制报文解析---");
      if (len >= 6) {
        // 提取加密前缀
        msgResource = ucuRequestDataStr.substring(0, 2);
        LogManager().debug("[E300BLE]---加密前缀 = 0x${msgResource}---");
        if (msgResource == "00") {
          // 数据为加密，需要解密
          String encryptedStr = ucuRequestDataStr.substring(2, len);
          Uint8List aKey = StrUtil.hexStringToBytes(key);
          LogManager().debug("[E300BLE]---加密部分：$encryptedStr---");
          LogManager().debug("[E300BLE]---加密秘钥：$aKey---");
          mainDataStr = StrUtil.aes128Decrypt(encryptedStr, key);
          LogManager().debug("[E300BLE]---解密数据：$mainDataStr---");
        } else {
          // 数据未加密，直接获取主数据帧
          mainDataStr = ucuRequestDataStr.substring(2, len);
        }
        encryptedFrame = mainDataStr;
        int mainLen = encryptedFrame!.length;
        // 提取数据校验码
        dataCheck = encryptedFrame!.substring(mainLen - 4, mainLen);
        // 进行CRC校验
        if (crcCheckPassed()) {
          // CRC校验通过，解析数据
          LogManager().debug("[E300BLE]---主数据帧 = 0x$mainDataStr---");
          serviceId = mainDataStr.substring(0, 4);
          LogManager().debug("[E300BLE]---服务类型 = 0x${serviceId}---");
          subfunction = mainDataStr.substring(4, 8);
          LogManager().debug("[E300BLE]---功能类型 = 0x${subfunction}---");
          randomData = mainDataStr.substring(8, 16);
          LogManager().debug("[E300BLE]---随机数 = 0x${randomData}---");
          payloadLength = mainDataStr.substring(16, 18);
          LogManager().debug("[E300BLE]---payload长度 = 0x${payloadLength}---");
          errorCode = mainDataStr.substring(18, 26);
          LogManager().debug("[E300BLE]---错误码 = 0x${errorCode}---");
          if (errorCode == "00000000") {
            // 控制成功
            responseType = ControlResponseType.succeededType;
            LogManager().debug("[E300BLE]---回复类型：控制成功---");
          } else {
            // 控制失败
            responseType = ControlResponseType.failedType;
            LogManager().debug("[E300BLE]---回复类型：控制失败---");
          }
          // CAN 报文 0x37D 的 byte1-byte8 原始值
          parkingStatus1 = mainDataStr.substring(26, 42);
          // CAN 报文 0x39E 的 byte1-byte8 原始值
          parkingStatus2 = mainDataStr.substring(42, 58);
          if (serviceId != null && serviceId!.toUpperCase() == "C065") {
            LogManager().debug("[E300BLE]---泊车状态码1 = 0x${parkingStatus1}---");
            LogManager().debug("[E300BLE]---泊车状态码2 = 0x${parkingStatus2}---");
          }
        } else {
          // CRC校验未通过
          LogManager().debug("[E300BLE]---控制回复数据CRC校验未通过---");
        }
      }
    } catch (e) {
      LogManager().debug("[E300BLE]---解析过程中发生错误：$e---");
    }
  }

  /// CRC校验
  bool crcCheckPassed() {
    int crc;
    Uint8List encryptedData = StrUtil.hexStringToBytes(encryptedFrame!);
    crc = StrUtil.crc16CcittFalse(encryptedData);
    LogManager().debug("[E300BLE]---CRC校验数据：${encryptedFrame} 校验结果 = $crc ${crc == 0 ? "通过" : "未通过"}---");
    return crc == 0;
  }

  /// 获取控制响应类型
  ControlResponseType? getResponseType() {
    return responseType;
  }

  /// 获取服务类型
  String? getServiceId() {
    return serviceId;
  }

  /// 获取功能类型
  String? getSubfunction() {
    return subfunction;
  }

  /// 获取错误码
  String? getErrorCode() {
    return errorCode;
  }

  /// 获取泊车反馈状态码
  ParkingStatusCode getParkingStatusCode() {
    Uint8List errorCodeData = StrUtil.hexStringToBytes(errorCode!);
    if (errorCodeData.length == 4) {
      int byte0 = errorCodeData[0];
      int byte1 = errorCodeData[1];
      if (byte0 == 0x30) {
        // 遥控泊车状态反馈
        return ParkingStatusCode.values.firstWhere((e) => e.value == byte1, orElse: () => ParkingStatusCode.parkingStatusUnknown);
      }
    }
    return ParkingStatusCode.parkingStatusUnknown;
  }

  /// 获取记忆泊车一键泊入反馈状态码
  MpParkinStatusCode getMemoryParkInStatusCode() {
    Uint8List errorCodeData = StrUtil.hexStringToBytes(errorCode!);
    if (errorCodeData.length == 4) {
      int byte0 = errorCodeData[0];
      int byte1 = errorCodeData[1];
      if (byte0 == 0x32) {
        // 一键泊入状态反馈
        return MpParkinStatusCode.values.firstWhere((e) => e.value == byte1, orElse: () => MpParkinStatusCode.mpParkinStatusUnknown);
      }
    }
    return MpParkinStatusCode.mpParkinStatusUnknown;
  }

  /// 获取记忆泊车一键泊出反馈状态码
  MpParkoutStatusCode getMemoryParkOutStatusCode() {
    Uint8List errorCodeData = StrUtil.hexStringToBytes(errorCode!);
    if (errorCodeData.length == 4) {
      int byte0 = errorCodeData[0];
      int byte1 = errorCodeData[1];
      if (byte0 == 0x31) {
        // 一键出库状态反馈
        return MpParkoutStatusCode.values.firstWhere((e) => e.value == byte1, orElse: () => MpParkoutStatusCode.mpParkoutStatusUnknown);
      }
    }
    return MpParkoutStatusCode.mpParkoutStatusUnknown;
  }

  /// 获取一键泊出反馈状态码对应状态说明
  String getMemoryParkOutStatusStr(MpParkoutStatusCode statusCode) {
    List<String> statusStrArray = [
      "默认状态",
      "出库就绪",
      "未上高压",
      "未进入遥控上电",
      "功能未就绪",
      "出库成功",
      "出库失败",
      "人工接管，出库失败",
      "功能不可用",
      "出库暂停",
      "出库进行中",
      "超时",
    ];
    int index = statusCode.value;
    if (index < statusStrArray.length) {
      return statusStrArray[index];
    }
    return "非有效泊出反馈状态码";
  }

  /// 获取智能泊车信息提示信息码
  int getIpsInformationCode() {
    if (serviceId != null && serviceId!.toUpperCase() == "C065") {
      Uint8List parkingStatus1Data = StrUtil.hexStringToBytes(parkingStatus1!);
      if (parkingStatus1Data.length == 8) {
        int byte7 = parkingStatus1Data[7];
        LogManager().debug("[E300BLE]---parkingStatus1 = ${parkingStatus1}, ipsInfo = 0x${byte7.toRadixString(2)}");
        return byte7;
      }
    }
    return MpIpaOperationStatusCode.mpIpaOperationStatusUnknown.value;
  }

  /// 获取智能泊车辅助运行状态码
  MpIpaOperationStatusCode getIpaOperationStatusCode() {
    if (serviceId != null && serviceId!.toUpperCase() == "C065") {
      Uint8List parkingStatus2Data = StrUtil.hexStringToBytes(parkingStatus2!);
      if (parkingStatus2Data.length == 8) {
        // 从 Byte1 开始截取，跳过 Byte0 的 8 位，取 5 位
        Uint8List ipaOperStsData = StrUtil.subDataFromStartBit(parkingStatus2Data, 8, 5);
        int ipaOperSts = ipaOperStsData[0];
        LogManager().debug("[E300BLE]---parkingStatus2 = ${parkingStatus2}, ipaOperSts = 0x${ipaOperSts.toRadixString(2)}");
        return MpIpaOperationStatusCode.values.firstWhere((e) => e.value == ipaOperSts, orElse: () => MpIpaOperationStatusCode.mpIpaOperationStatusUnknown);
      }
    }
    return MpIpaOperationStatusCode.mpIpaOperationStatusUnknown;
  }

  /// 获取智能泊车辅助运行状态码对应信息字符串
  static String getIpaOperationStatusStr(MpIpaOperationStatusCode statusCode) {
    List<String> statusStrArray = [
      "Off",
      "No Ready",
      "Ready",
      "Detecting",
      "Detecting suspend",
      "Detected",
      "BG_Detecting",
      "BG_Detected",
      "Prepare",
      "Parking",
      "Parking Suspend",
      "Finish_Success",
      "Finish_Failure",
      "Finish_Termination",
      "Error_Int_Fault",
      "Error_Ext_Fault",
    ];
    int index = statusCode.value;
    if (index < statusStrArray.length) {
      return statusStrArray[index];
    }
    return "Not In List";
  }

  /// 获取智能出库运行状态码
  MpApoOperationStatusCode getApoOperationStatusCode() {
    if (serviceId != null && serviceId!.toUpperCase() == "C065") {
      Uint8List parkingStatus2Data = StrUtil.hexStringToBytes(parkingStatus2!);
      if (parkingStatus2Data.length == 8) {
        // 从 Byte1 开始截取，跳过 Byte0 的 8 位，再跳过 5 位，取 4 位
        Uint8List apoOperStsData = StrUtil.subDataFromStartBit(parkingStatus2Data, 5 + 8, 4);
        int apoOperSts = apoOperStsData[0];
        LogManager().debug("[E300BLE]---parkingStatus2 = ${parkingStatus2}, apoOperSts = 0x${apoOperSts.toRadixString(2)}");
        return MpApoOperationStatusCode.values.firstWhere((e) => e.value == apoOperSts, orElse: () => MpApoOperationStatusCode.mpApoOperationStatusUnknown);
      }
    }
    return MpApoOperationStatusCode.mpApoOperationStatusUnknown;
  }

  /// 获取智能出库运行状态码对应信息字符串
  static String getApoOperationStatusStr(MpApoOperationStatusCode statusCode) {
    List<String> statusStrArray = [
      "Off",
      "No Ready",
      "Ready",
      "Prepare",
      "ParkingOut",
      "ParkingOut Suspend",
      "Finish_Success",
      "Finish_Failure",
      "Finish_Termination",
      "Error_Int_Fault",
      "Error_Ext_Fault",
      "Prepare Suspend",
    ];
    int index = statusCode.value;
    if (index < statusStrArray.length) {
      return statusStrArray[index];
    }
    return "Not In List";
  }

  /// 获取 IBC 错误码
  int getIbcErrorCode() {
    Uint8List errorCodeData = StrUtil.hexStringToBytes(errorCode!);
    int ibcErrorCode = 0x00;
    if (errorCodeData.length == 4) {
      int byte0 = errorCodeData[0];
      int byte1 = errorCodeData[1];
      if (byte0 == 0x20) {
        // IBC 执行错误
        ibcErrorCode = byte1;
      }
    }
    LogManager().debug("[E300BLE]---ibcErrorCode = 0x${ibcErrorCode.toRadixString(2)}");
    return ibcErrorCode;
  }

  /// 获取 IBC 错误提示语
  static String getIbcErrorInfoStr(int ibcErrorCode) {
    Map<int, String> ibcErrorDictionary = {
      0x00: "无错误",
      0x01: "点火档位不在OFF档",
      0x02: "车未上锁",
      0x03: "PEPS未学习",
      0x04: "PEPS远程功能未使能",
      0x05: "IGN1输出失败",
      0x06: "START输出故障",
      0x07: "ACC、IGN2输出失败",
      0x08: "电压超出范围",
      0x09: "远程认证失败",
      0x10: "IMMO认证失败",
      0x11: "非自动挡车型",
      0x12: "远程上高压失败次数超过阈值（预留）",
      0x13: "ESCL不能解锁",
      0x14: "PEPS超时",
      0x15: "车门未关",
      0x16: "启动尝试超时",
      0x18: "存在发动机熄火条件",
      0x19: "电机故障",
      0x1A: "防盗报警触发",
      0x1B: "发生碰撞",
      0x1C: "大灯开关处于非OFF档",
      0x1D: "动力电池故障",
      0x20: "鉴权请求ID无效",
      0x21: "整车档位不在P档",
      0x22: "整车不在远程刷新模式",
      0x26: "燃油量低",
      0x27: "动力电池SOC过低",
      0x28: "ECM应答超时",
      0x2B: "天窗节点错误",
      0x2F: "未知原因",
      0x3C: "远程上高压控制状态超时熄火",
      0x41: "危险报警灯触发",
      0x43: "前舱盖未关",
      0x50: "控制请求未定义",
      0x51: "模式切换",
      0x52: "空调故障",
      0x53: "座椅加热失败",
      0x54: "整车不处于防盗状态",
      0x55: "车速大于2km/h或信号无效",
      0x56: "电子手刹或机械手刹未拉起",
      0x57: "刹车踏板踩下",
      0x58: "油门踏板踩下",
      0x59: "SSB开关按下",
      0x5A: "CAN总线BUSOFF",
      0x5B: "鉴权状态超出范围",
      0x5C: "远程上高压控制状态错误",
      0x5D: "手刹信号丢失",
      0x5E: "车速信号丢失",
      0x5F: "档位信号丢失",
      0x71: "油门踏板信号丢失",
      0x72: "燃油量信号丢失",
      0x73: "空调远程控制失败，信号丢失",
      0x74: "远程上高压失败，信号丢失",
      0x75: "通讯失败",
      0x77: "1.5小时计时超时",
      0xFF: "功能不支持",
    };
    String? description = ibcErrorDictionary[ibcErrorCode];
    return description ?? "无错误";
  }

  /// 错误码解析方法
  ///
  /// [errorCode]：错误码的十六进制字符串形式
  /// 返回对应的错误描述字符串
  String getErrorDescription() {
    if(errorCode == null || errorCode == '00000000'){
      return '非有效错误码';
    }
    String errorCodeHex = errorCode ?? '';
    // 将错误码的十六进制字符串转换为字节数组
    Uint8List errorCodeData = StrUtil.hexStringToBytes(errorCodeHex);
    StringBuffer description = StringBuffer();

    // 判断错误码数据的长度是否为 4 字节
    if (errorCodeData.length == 4) {
      List<int> bytes = errorCodeData;

      if (bytes[0] == 0x30) {
        // 遥控泊车状态反馈
        // 第 2 个字节对应错误描述
        Map<int, String> statusDescription = {
          // 您可以在此处添加对应的错误码映射
          // 例如：0x06: "Failure",
          // 0x07: "ADAS反馈超时",
        };

        String? status = statusDescription[bytes[1]];
        if (status != null) {
          description.write('$status$errorCode');
        }
      } else if (bytes[0] == 0x20) {
        // 新增：IBC 执行失败错误码
        // 第 2 个字节对应错误描述，详见文档
        Map<int, String> statusDescription = {
          0x01: "整车电源未关闭，不能进行此项操作",
          0x02: "车门未锁，不能进行此项操作",
          0x03: "钥匙未学习，请进站检查",
          0x04: "钥匙配置错误，请进站检查",
          0x05: "系统错误，请进站检查",
          0x06: "系统错误，请进站检查",
          0x07: "系统错误，请进站检查",
          0x08: "电压过高，请稍后重试",
          0x09: "远程认证失败，请稍后重试",
          0x10: "发动机防盗认证失败，请稍后重试",
          0x11: "车辆配置错误，请进站检查",
          0x12: "请上车启动车辆重新激活该功能",
          0x13: "电子转向锁解锁失败，请进站维修",
          0x14: "车辆未响应，请稍后重试",
          0x15: "车门未关，不能进行此项操作",
          0x16: "发动机未启动，请再次尝试",
          0x18: "发动机异常熄火，请稍后重试",
          0x19: "系统错误，请进站检查",
          0x1A: "防盗报警中，不能进行此项操作",
          0x1B: "系统错误，请进站检查",
          0x1C: "大灯开关处于非OFF档",
          0x1D: "系统错误，请进站检查",
          0x20: "远程认证失败，请稍后重试",
          0x21: "车辆档位不在P挡，不能进行此项操作",
          0x22: "整车不在远程刷新模式，请稍后重试",
          0x26: "油量不足，不能进行此项操作",
          0x27: "动力电池电量过低，不能进行此项操作",
          0x28: "车辆未响应，请稍后重试",
          0x2B: "天窗故障，请进站检查",
          0x2F: "哎呀，刚开小差了，再试试吧",
          0x3C: "任务已完成，发动机已关闭",
          0x41: "危险报警灯未关闭，不能进行此项操作",
          0x43: "前舱盖未关好，不能进行此项操作",
          0x50: "远程认证失败，请稍后重试",
          0x51: "远程切换本地模式失败，请在车内重新启动车辆",
          0x52: "空调未启动，请稍后重试",
          0x53: "功能未开启，请稍后重试",
          0x54: "请锁车后再使用此项功能",
          0x55: "车辆行驶过程中，不能进行此项操作",
          0x56: "手刹未拉，不能进行此项操作",
          0x57: "刹车踏板被踩下，不能进行此项操作",
          0x58: "油门踏板被踩下，不能进行此项操作",
          0x59: "一键启动开关信号异常，不能进行此项操作",
          0x5A: "车辆通讯错误，请稍后重试",
          0x5B: "远程认证失败，请稍后重试",
          0x5C: "暂时不能获取控制状态，请稍后重试",
          0x5D: "车内通讯未响应，请稍后重试",
          0x5E: "车内通讯未响应，请稍后重试",
          0x5F: "车内通讯未响应，请稍后重试",
          0x71: "车内通讯未响应，请稍后重试",
          0x72: "车内通讯未响应，请稍后重试",
          0x73: "车内通讯未响应，请稍后重试",
          0x74: "车内通讯未响应，请稍后重试",
          0x75: "通讯失败，请稍后重试",
          0x77: "计时超时，请稍后重试",
          0xFF: "功能不支持",
        };

        String? status = statusDescription[bytes[1]];
        if (status != null) {
          description.write(status);
        }
      } else {
        // 控制指令错误
        // 首字节错误码对应 description，2、3、4 字节错误码对应 detail 的 3 个列表
        Map<int, Map<String, dynamic>> errorDescriptionDict = {
          0x05: {"description": "数据长度错误"},
          0x07: {"description": "操作过于频繁，请稍后再试"},
          0x08: {"description": "车辆未响应，请稍后重试"},
          0x0B: {"description": "UCU无响应"},
          0x11: {
            "description": "",
            "detail": [
              [
                "车辆已启动，暂不支持远程控制",
                "",
                "电动窗电源输出开路",
                "电动窗电源输出短路到电源",
                "危险警报灯未关，请关闭后重试",
                "车门未关，请关门后重试",
                "蓝牙钥匙使用中",
                "车辆启动中"
              ],
              [
                "车辆正在运行",
                "电源已经在ON档",
                "电源已经在ACC档",
                "右转向灯开路",
                "右转向灯过载/短路到地",
                "左转向灯开路",
                "左转向灯过载/短路到地",
                "喇叭故障"
              ],
              [
                "",
                "",
                "",
                "变速箱当前档位为非P",
                "蓄电池电压低",
                "整车防盗认证失败",
                "智能钥匙在车内",
                "车灯未关，请关灯后重试"
              ]
            ]
          },
          0x12: {
            "description": "",
            "detail": [
              [
                "右后门未锁",
                "左后门未锁",
                "右前门未锁",
                "左前门未锁",
                "右后门未关",
                "左后门未关",
                "右前门未关，请关门后重试",
                "左前门未关，请关门后重试"
              ],
              [
                "",
                "车辆已解锁或启动，暂不支持远程控制",
                "车灯未关，请关灯后重试",
                "车辆已解锁或启动，暂不支持远程控制",
                "车辆已启动，暂不支持远程控制",
                "车辆已启动，请下电后重试",
                "车辆未充电，请充电后重试",
                "后备箱未关，请关闭后重试"
              ],
              [
                "",
                "",
                "",
                "",
                "操作过于频繁，请稍后再试",
                "变速箱当前档位为非P",
                "车辆正在运行",
                "车辆已启动，请下电后重试"
              ]
            ]
          },
          0x13: {"description": "钥匙失效，请重启APP后重试"},
          0x16: {"description": "车辆未响应，请稍后重试"},
          0x1E: {"description": "钥匙失效，请重启APP后重试"},
          0x7F: {"description": "钥匙失效，请重启APP后重试"},
        };

        Map<String, dynamic>? sourceDict = errorDescriptionDict[bytes[0]];
        if (sourceDict != null) {
          description.write(sourceDict["description"] ?? "");
          var detail = sourceDict["detail"];
          if (detail != null) {
            List<String> errorSet = [];
            for (int i = 1; i < 4; i++) {
              int errorByte = bytes[i];
              List<String> errorByteArray = detail[i - 1];
              for (int j = 0; j < 8; j++) {
                int bit = 1 << j;
                if ((errorByte & bit) != 0) {
                  String bitDescription = errorByteArray[j];
                  if (bitDescription.isNotEmpty) {
                    errorSet.add(bitDescription);
                  }
                }
              }
            }
            if (errorSet.isNotEmpty) {
              String separator = description.length > 0 ? "。" : "";
              description.write('$separator${errorSet.join(",")}');
            }
          }
          LogManager().debug("[E300BLE]---蓝牙操作失败错误码：$errorCode");
        }
      }
    }

    // 如果描述为空，则设置为默认的未知错误提示
    if (description.isEmpty) {
      description.write("未知错误，请重试");
    }

    return description.toString();
  }
}