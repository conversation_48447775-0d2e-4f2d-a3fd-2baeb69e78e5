import 'package:flutter/material.dart';
import 'package:flutter_uikit/flutter_uikit.dart';
import 'package:wuling_flutter_app/plugins/share_plugin/share_plugin.dart';

import '../../utils/manager/log_manager.dart';
import '../action.dart';

class WXShare extends StatelessWidget {
  const WXShare({super.key});

  @override
  Widget build(BuildContext context) {
    LogManager().debug("微信分享");
    return GestureDetector(
      onTap: (){
        NavigatorAction.init(context);
      },
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: GestureDetector(
          onTap: (){
            NavigatorAction.init(context);
          },
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Container(
                alignment: Alignment.bottomCenter,
                color: Colors.white,
                padding: EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom),
                child: <PERSON>umn(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Align(
                      alignment: Alignment.center,
                      child: UIText(data: "请选择分享方式",padding: EdgeInsets.symmetric(vertical: 14),),
                    ),
                    Row(
                      children: [
                        Expanded(flex: 1,child: UIButton(onPressed: () {
                          NavigatorAction.init(context,data: "weixin");
                        },buttonState: UIButtonState(
                            buttonType: UIButtonType.top,
                            title: "微信",
                            fontSize: 13,
                            color: 0xFF333333,
                            imgStr: "assets/images/store/weixin.png"
                        ),),),
                        Expanded(flex: 1,child: UIButton(onPressed: () {
                          NavigatorAction.init(context,data: "pyq");
                        },buttonState: UIButtonState(
                            buttonType: UIButtonType.top,
                            title: "朋友圈",
                            fontSize: 13,
                            color: 0xFF333333,
                            imgStr: "assets/images/store/pyq.png"
                        ),),)
                      ],
                    ),
                    UIButton(onPressed: (){
                      NavigatorAction.init(context);
                    },width: MediaQuery.of(context).size.width,height: 40,radius: 0,child: const UIText(data: "取消",color: 0xFF0000FF,),)
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
