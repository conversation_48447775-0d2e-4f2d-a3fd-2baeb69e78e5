//
//  CYUnifiedMQTTHelper.h
//  LingLingBang
//
//  Created by admin on 2022/2/22.
//  Copyright © 2022 linglingbang. All rights reserved.
//

#import <Foundation/Foundation.h>

#import "CarportPosition.pbobjc.h"
#import "CarStatus.pbobjc.h"
#import "ParkingInStatus.pbobjc.h"
#import "ParkingOutStatus.pbobjc.h"
#import "RemoteControlStatus.pbobjc.h"
#import "SummonLocation.pbobjc.h"
#import "SummonStatus.pbobjc.h"
#import "UnblockStatus.pbobjc.h"
#import "CarControl.pbobjc.h"
#import "SgmwAppCarStatus.pbobjc.h"
#import "SgmwAppControlResult.pbobjc.h"
#import "Sgmwbusiness.pbobjc.h"

#import <MQTTClient/MQTTClient.h>

NS_ASSUME_NONNULL_BEGIN

// MQTT订阅话题
typedef NS_ENUM(NSUInteger, CYUnifiedTopic) {
    CYUnifiedTopicCarStatus = 1,
    CYUnifiedTopicCarPortPosition,
    CYUnifiedTopicParkingInStatus,
    CYUnifiedTopicParkingOutStatus,
    CYUnifiedTopicRemoteControlStatus,
    CYUnifiedTopicSummonLocation,
    CYUnifiedTopicSummonStatus,
    CYUnifiedTopicUnblockStatus,
    CYUnifiedTopicCarControlResult,
    CYUnifiedTopicCarRemoteAsyncResult,
    CYUnifiedTopicCarControlAllStatus,
    CYUnifiedTopicCarCheckAuthorizeBusiness,
    CYUnifiedTopicCarParkingNotifyBusiness
};

@class CYUnifiedMQTTHelperPair;
@interface CYUnifiedMQTTHelper : NSObject <MQTTSessionDelegate>
@property (nonatomic, strong) MQTTSession *session;
@property (nonatomic, copy) NSString *vin;
@property (nonatomic, copy) NSString *username;
@property (nonatomic, copy) NSString *password;
@property (nonatomic, copy) NSString *clientId;
@property (nonatomic, copy) NSString *prefix;
@property (nonatomic,assign) BOOL isNetwork;
@property (nonatomic,assign) BOOL supportMqtt;


@property (nonatomic, strong) NSMutableArray<CYUnifiedMQTTHelperPair*> *pairs;

+ (instancetype)shared;

/// 获取token并登录
/// - Parameter vin: vin码
- (void)getTokenAndLoginWithVin:(NSString *)vin complete:(void(^_Nullable)(BOOL))complete;

+ (void)getTokenWithVin:(NSString *)vin success:(void (^_Nullable)(NSString *username, NSString *password, NSString *clientId, NSString *vin))success failure:(void (^_Nullable)(void))failure;
- (void)loginWithUsername:(NSString *)username password:(NSString *)password clientId:(NSString *)clientId vin:(NSString *)vin success:(void (^_Nullable)(void))success failure:(void (^_Nullable)(void))failure;

- (void)disconnect;

/// 添加订阅事件回调
/// @param target target
/// @param action 形如 [target action:model] 样式的方法，model是订阅返回的已解析数据
/// @param topic MQTT订阅话题
- (void)addTarget:(id)target action:(SEL)action forTopic:(CYUnifiedTopic)topic;

/// 移除订阅事件回调
/// @param target 对应的target，传nil则移除所有的target
- (void)removeTarget:(id)target;

/// 检查MQTT连接情况并尝试连接
- (void)checkAndConnectMQTT;

@end


@interface CYUnifiedMQTTHelperPair : NSObject

@property (nonatomic, weak) id target;
@property (nonatomic, assign) SEL action;
@property (nonatomic, assign) CYUnifiedTopic topic;

@end

NS_ASSUME_NONNULL_END
