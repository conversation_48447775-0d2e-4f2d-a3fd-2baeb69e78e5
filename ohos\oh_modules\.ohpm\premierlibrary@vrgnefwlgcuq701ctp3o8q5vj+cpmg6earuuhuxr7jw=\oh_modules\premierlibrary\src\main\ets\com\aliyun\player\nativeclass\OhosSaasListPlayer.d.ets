import { OhosUrlListPlayer } from './OhosUrlListPlayer';
import { StsInfo } from '../source/StsInfo';
import { PlayAuthInfo } from '../source/PlayAuthInfo';
export declare class OhosSaasListPlayer extends OhosUrlListPlayer {
    constructor(i30: Context, j30: number, k30: number, l30: boolean);
    addVid(g30: string, h30: string): void;
    setDefinition(f30: string): void;
    moveToNextWithSts(d30: StsInfo, e30: boolean): boolean;
    moveToPrevWithSts(c30: StsInfo): boolean;
    moveToWithSts(a30: StsInfo, b30: string): boolean;
    moveToNextWithAuthInfo(y29: PlayAuthInfo, z29: boolean): boolean;
    moveToPrevWithAuthInfo(x29: PlayAuthInfo): boolean;
    moveToWithAuthInfo(v29: PlayAuthInfo, w29: string): boolean;
}
