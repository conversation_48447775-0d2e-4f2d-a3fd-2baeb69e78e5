import { SearchResult } from '@bdmap/search/../../Index';
import { RoutePlanParser, SearchRequest } from '../../base/base';
import { DrivingRoutePlanOption, DrivingRouteResult, DrivingStep } from "../../../d/e/h/l";
import { DriveJson } from "./h/l";
import { RouteNode, TaxiInfo } from "../../../d/e/h/f1";
import { LatLng } from '@bdmap/base';
import { UrlProvider } from "../../base/d3";
export declare class DriveRouteParser extends RoutePlanParser {
    parseResult(b24: string, c24: SearchResult): void;
    parseJsonToDrivingRouteResult(b23: DriveJson.DrivingJsonParseResult, c23: DrivingRouteResult): boolean;
    parseRouteNodeWp(v22: DriveJson.Start[] | undefined, w22: RouteNode[]): RouteNode;
    parseTaxiInfos(p22: string | undefined): TaxiInfo[];
    parseStepForOneLine(g22: DriveJson.Stepi[], h22: DrivingStep[]): DrivingStep[];
    parseNodeSteps(r21: DriveJson.Step[] | undefined, s21: DriveJson.Stept[] | undefined): DrivingStep[];
    parseStepTraffic(f21: DriveJson.Stept): number[];
    parseStepPath(z20: number[]): LatLng[];
    parseRouteNode(v20: DriveJson.Start | undefined): RouteNode;
}
export declare class DriveRouteRequest extends SearchRequest {
    constructor(u20: DrivingRoutePlanOption);
    drivingrouteBuildParm(o20: DrivingRoutePlanOption): void;
    getUrlDomain(n20: UrlProvider): string;
}
