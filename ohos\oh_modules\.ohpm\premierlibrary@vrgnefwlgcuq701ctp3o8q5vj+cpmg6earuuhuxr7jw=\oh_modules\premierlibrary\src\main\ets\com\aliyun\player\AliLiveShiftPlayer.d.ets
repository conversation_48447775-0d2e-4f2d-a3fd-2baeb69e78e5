import { IPlayer } from './IPlayer';
import { LiveShift } from './source/LiveShift';
export interface AliLiveShiftPlayer extends IPlayer {
    /**
     * 设置数据源
     *
     * @param liveShift 本机地址或网络地址。见{@link LiveShift}。
     */
    /****
     * Specify a timeshift playback source.
     *
     * @param liveShift The specified timeshift playback source: a local address or a URL. See {@link LiveShift}.
     */
    setLiveShiftDataSource: (liveShift: LiveShift) => void;
    /**
     * 获取当前直播的现实时间
     * @return 现实时间
     */
    /****
     * Query the current broadcasting time.
     * @return The current broadcasting time.
     */
    getCurrentLiveTime: () => number;
    /**
     * 获取当前直播的播放时间
     * @return 播放时间
     */
    /****
     * Query the time that the player currently seeks to.
     * @return The time that the player currently seeks to.
     */
    getCurrentTime: () => number;
    /**
     * 时移到某个绝对时间
     * @param liveTime 时间。单位秒
     */
    /****
     * Seek to a specified time.
     * @param liveTime The specified time that the player will seek to. Unit: seconds.
     */
    seekToLiveTime: (liveTime: number) => void;
    /**
     * 设置时移时间更新监听事件
     * @param l 时移时间更新监听事件
     */
    /****
     * Set a timeshifting update callback.
     * @param l The timeshifting update callback.
     */
    setOnTimeShiftUpdaterListener: (l: OnTimeShiftUpdaterListener) => void;
    /**
     * 设置时移seek完成通知。
     * @param l seek完成通知。
     */
    /****
     * Set a timeshifting success callback.
     * @param l The timeshifting success callback.
     */
    setOnSeekLiveCompletionListener: (l: OnSeekLiveCompletionListener) => void;
}
/**
 * 时移时间更新监听事件
 */
/****
 * Timeshifting update callback.
 */
export interface OnTimeShiftUpdaterListener {
    /**
     * 时移时间更新
     * @param currentTime 当前现实时间
     * @param shiftStartTime 可时移的起始时间
     * @param shiftEndTime 可时移的结束时间
     */
    /****
     * Timeshifting update notification.
     * @param currentTime The current broadcasting time.
     * @param shiftStartTime The start of the time window for timeshift.
     * @param shiftEndTime The end of the time window for timeshift.
     */
    onUpdater: (currentTime: number, shiftStartTime: number, shiftEndTime: number) => void;
}
/**
 * 时移seek完成通知。
 */
/****
 * Timeshifting success callback.
 */
export interface OnSeekLiveCompletionListener {
    /**
     * 时移seek完成通知。
     * @param playTime 实际播放的时间。
     */
    /****
     * Timeshifting success notification.
     * @param playTime The time that the player seeks to.
     */
    onSeekLiveCompletion: (playTime: number) => void;
}
