import 'dart:collection';
import 'dart:convert';

import 'package:webview_flutter/webview_flutter.dart';
import 'package:wuling_flutter_app/constant/constant.dart';
import 'package:wuling_flutter_app/models/global_data.dart';

class WebViewJsMethod {
  static final WebViewJsMethod _instance = WebViewJsMethod._();

  factory WebViewJsMethod() => _instance;

  WebViewJsMethod._();

  String javaScript() {
    return code;
  }

  final String code = '''
  if (!window.jsObj) {
    window.jsObj = {};
  }
  
  window.jsObj.shareImage = function(url, type) {
  let jsonString = JSON.stringify({url: url, type: type});
    window.shareImageOhos.postMessage(jsonString);
  };
  
  window.jsObj.goShareToMini = function(shareDescribe, shareImg, shareUrl, shareTitle, shareType, shareActivityId, miniProgramPath, miniProgramUserName) {
    let jsonString = JSON.stringify({path: miniProgramPath, username: miniProgramUserName, title: shareTitle, cover: shareImg, description: shareDescribe});
    window.goShareToMiniOhos.postMessage(jsonString);
  };
  
  window.jsObj.goBack = function() {
    window.goBackOhos.postMessage("");
  };
  
  window.jsObj.goCustomerService = function() {
    window.goCustomerServiceOhos.postMessage("");
  };

  window.jsObj.closePage = function() {
    window.closePageOhos.postMessage("");
  };
  
  window.jsObj.toast = function(toastMessage) {
    window.toastOhos.postMessage(toastMessage);
  };
  
  window.jsObj.getPosition = function() {
    window.getPositionOhos.postMessage("");
  };

  window.jsObj.showNoLogin = function() {
    window.showNoLoginOhos.postMessage("");
  };
  
  window.jsObj.bangSafeCheckCode = function(type, state, query, json) {
    let jsonString = JSON.stringify({type: type, state: state, query: query, json: json});
    window.bangSafeCheckCodeOhos.postMessage(jsonString);
  };
  
  window.jsObj.getMessageToken = function() {
    window.getMessageTokenOhos.postMessage("");
  };
  
  window.jsObj.scanCode = function() {
    window.scanCodeOhos.postMessage("");
  }  
  
  window.jsObj.goPay = function(orderNo, splitFlag, splitNo, amount, orderType, machId) {
    window.goPayOhos.postMessage("");
  }  
  
  window.jsObj.goPayApp = function(orderNo, accessToken, cashierId, target, application) {
  let jsonString = JSON.stringify({orderNo: orderNo, accessToken: accessToken, cashierId: cashierId, target: target ,application:application});
    window.goPayAppOhos.postMessage(jsonString);
  }
  
  window.jsObj.goScorePayApp = function(orderNo, accessToken, cashierId, target, application) {
  let jsonString = JSON.stringify({orderNo: orderNo, accessToken: accessToken, cashierId: cashierId, target: target ,application:application});
    window.goScorePayAppOhos.postMessage(jsonString);
  }
  window.jsObj.goPost = function(postId) {
    window.goPostOhos.postMessage(postId);
  };
  window.jsObj.saveLocalImg = function(imgStr) {
    window.saveLocalImgOhos.postMessage(imgStr);
  };
  window.jsObj.goShareMiniProgramToWechatSession = function(a,b,c,d,e,f,g,h) {
    let jsonString = JSON.stringify({a:a,b:b,c:c,d:d,e:e,f:f,g:g,h:h});
    window.goShareMiniProgramToWechatSessionOhos.postMessage(jsonString);
  };
  window.jsObj.goMyInfo = function(postId) {
    window.goMyInfoOhos.postMessage(postId);
  };
  window.jsObj.goShare = function(shareContent,imgURL,shareURL,title,shareType,activityId) {
    let jsonString = JSON.stringify({shareContent: shareContent, imgURL: imgURL, shareURL: shareURL, title: title,shareType:shareType,activityId:activityId});
    window.goShareOhos.postMessage(jsonString);
  };
  window.jsObj.goShop = function() {
    window.goShopOhos.postMessage("");
  };
  window.jsObj.goCommodityDetail = function(commodityId, groupActivityId) {
    if (arguments.length > 1 && groupActivityId !== undefined) {
      let jsonString = JSON.stringify({id: commodityId, groupActivityId: groupActivityId, code: 0});
      window.goCommodityDetailOhos.postMessage(jsonString);
    } else {
      let jsonString = JSON.stringify({id: commodityId, code: 0});
      window.goCommodityDetailOhos.postMessage(jsonString);
    }
  };
  window.jsObj.goNewCommodityDetail = function(commodityId, shopId) {
    if (arguments.length > 1 && shopId !== undefined) {
      let jsonString = JSON.stringify({id: commodityId, shopId: shopId, code: 0});
      window.goNewCommodityDetailOhos.postMessage(jsonString);
    } else {
      let jsonString = JSON.stringify({id: commodityId, code: 0});
      window.goNewCommodityDetailOhos.postMessage(jsonString);
    }
  };
  window.jsObj.goCarTypeDetail = function(carTypeId, shopId, groupActivityId) {
  let jsonString = JSON.stringify({carTypeId: carTypeId, shopId: shopId, groupActivityId: groupActivityId});
    window.goCarTypeDetailOhos.postMessage(jsonString);
  };
  window.jsObj.openWXMiniProgram = function(userName, path) {
  let jsonString = JSON.stringify({userName: userName, path: path});
    window.openWXMiniProgramOhos.postMessage(jsonString);
  };
  window.jsObj.goSendLingPost = function() {
    window.goSendLingPostOhos.postMessage("");
  };
  window.jsObj.goNavigation = function(lat, lng, name) {
    let jsonString = JSON.stringify({latitude: lat, longitude: lng, dealerShortName: name});
    window.goNavigationOhos.postMessage(jsonString);
  };  
  window.jsObj.goToUser = function(userIdStr) {
    window.goToUserOhos.postMessage(userIdStr);
  };
  window.jsObj.goChat = function(message) {
    window.goChatOhos.postMessage(message);
  };
  window.jsObj.getADSkipWithLinkTypeAndLinkUrl = function(type, url) {
    let jsonString = JSON.stringify({type: type, url: url});
    window.getADSkipWithLinkTypeAndLinkUrlOhos.postMessage(jsonString);
  };
  window.jsObj.uploadLog = function() {
    window.uploadLogOhos.postMessage("");
  };
  window.jsObj.goAddressChoose = function() {
    window.goAddressChooseOhos.postMessage("");
  };
  window.jsObj.getADSkipWithLinkTypeAndLinkUrl = function(linkType, linkUrl) {
    let jsonString = JSON.stringify({linkType: linkType, linkUrl: linkUrl});
    window.getADSkipWithLinkTypeAndLinkUrlOhos.postMessage(jsonString);
  };
  window.jsObj.goTakePicture = function(json) {
    window.goTakePicture.postMessage(json);
  };
  
  window.jsObj.faceDetect = function(json) {
    window.faceDetect.postMessage(json);
  };
  
  window.jsObj.goCommonWeb = function(url, type) {
    let jsonString = JSON.stringify({url:url, type:type});
    window.goCommonWebOhos.postMessage(jsonString);
  };
  
  window.jsObj.carInfoChange = function(providerCode, vin, type) {
  let jsonString = JSON.stringify({providerCode: providerCode, vin: vin, type: type});
    window.carInfoChange.postMessage(jsonString);
  };
  ''';

  void messageToken(WebViewController controller) {
    Map<String, dynamic> map = HashMap();
    if (GlobalData().isLogin) {
      var user = GlobalData().userModel;
      map.putIfAbsent("userId", () => user?.userIdStr);
      map["phoneNum"] = user?.mobile;
      map["phone"] = user?.mobile;
      // 校验相关
      map["accessToken"] = GlobalData().oauthModel?.accessToken;
      map["client_id"] = Constant.APP_CLIENT_ID;
      map["client_secret"] = Constant.APP_CLIENT_SECRET;
      map["salt"] = Constant.APP_SALT;
      // map["globalId"] = user?.globalId;
      // 车主信息
      var carInfo = GlobalData().carInfoModel;
      map["vin"] = carInfo?.vin;
    }
    // if (Platform.isOhos) {
    //   OhosDeviceInfo info = await DeviceInfoUtil().get();
    //   print(info);
    // }

    map["sgmwappCode"] = "sgmw_llb";
    map["appVersionCode"] = "V8.2.6_dev";
    map["appVersionCodeNum"] = "1563";
    map["imei"] = "5bccbe2d-ca38-4910-850a-2111ac6e47fd";
    // map["imsi"] = application.imsi;
    // map["model"] = application.model;
    // map["osVersion"] = application.osVersion;
    String paramFinal = jsonEncode(map);
    controller.runJavaScript("javascript: messageToken('$paramFinal')");
    // //设置第三方值方法
    // Map<String, dynamic> openMap = HashMap();
    // openMap["appVersionCode"] = "V8.2.6_dev";
    // openMap["appVersionCodeNum"] = "1563";
    // String openParamFinal = jsonEncode(openMap);
    controller.runJavaScript("javascript: openMsgToken('$paramFinal')");

    //补充注入
    controller.runJavaScript(hideHtmlContent());
    controller.runJavaScript("javascript:addMessage();");
  }

  void openMsgToken(WebViewController controller) {
    Map<String, dynamic> map = HashMap();
    if (GlobalData().isLogin) {
      var user = GlobalData().userModel;
      map.putIfAbsent("userId", () => user?.userIdStr);
      map["phoneNum"] = user?.mobile;
      map["accessToken"] = GlobalData().oauthModel?.accessToken;
      map["nickName"] = user?.nickname;
      map["photo"] = user?.photo;
    }
    map["appVersionCode"] = "V8.2.6_dev";
    String jsonString = jsonEncode(map);
    controller.runJavaScript("javascript: openMsgToken('$jsonString')");
  }

  /// 注入js隐藏部分div元素，多个操作用多个js去做才能生效
  /// 因为前端有个界面用messageToken说获取不到,所以用注入代码方式传递信息
  String hideHtmlContent() {
    var user = GlobalData().userModel;
    var carInfo = GlobalData().carInfoModel;
    String javascriptq = "javascript:function addMessage() {" +
        "window.ff_userId='${user?.userIdStr}';" +
        "window.ff_phoneNum='${user?.mobile}';" +
        "window.ff_accessToken='${GlobalData().oauthModel?.accessToken}';" +
        "window.ff_client_id='${Constant.APP_CLIENT_ID}';" +
        "window.ff_client_secret='${Constant.APP_CLIENT_SECRET}';" +
        "window.ff_salt='${Constant.APP_SALT}';"
        // + "window.ff_globalId='${user?.globalId}';"
        // + "window.ff_is_owner='" + is_owner + "';"
        +
        "window.ff_vin='${carInfo?.vin}';" +
        "window.ff_phone='${user?.mobile}';" +
        "window.ff_appVersionCode='" +
        "V8.2.6_dev" +
        "';" +
        "window.ff_appVersionCodeNum='" +
        "1563" +
        "';" +
        "window.ff_imei='5bccbe2d-ca38-4910-850a-2111ac6e47fd';"
        // + "window.ff_imsi='" + imsi + "';"
        // + "window.ff_model='" + model + "';"
        // + "window.ff_osVersion='" + osVersion + "';"
        +
        "window.ff_sgmwappCode='sgmw_llb';" +
        "}";
    return javascriptq;
  }
}
