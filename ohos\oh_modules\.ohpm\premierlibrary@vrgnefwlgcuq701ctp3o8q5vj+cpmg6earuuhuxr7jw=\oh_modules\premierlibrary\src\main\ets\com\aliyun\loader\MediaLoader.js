import premierlibrary from 'libpremierlibrary.so';
import { AliPlayerGlobalSettings } from '../player/AliPlayerGlobalSettings';
import { ErrorInfo } from '../player/bean/ErrorInfo';
import { findErrorCodeByValue, PlayerErrorCode } from '../player/bean/PlayerErrorCode';
export const OLD_CODE_EADDED = -300;
export const OLD_CODE_ENOT_ENABLE = -301;
export class MediaLoader {
    static getInstance() {
        if (MediaLoader.sInstance == undefined) {
            MediaLoader.sInstance = new MediaLoader();
        }
        return MediaLoader.sInstance;
    }
    constructor() {
        this.onError = (d5, e5, f5) => {
            console.log('medialoader onError called, url: ' + d5 + " code: " + e5 + " msg " + f5);
            if (this.mOnLoadStatusListener != null) {
                this.mOnLoadStatusListener.onError(d5, e5, f5);
            }
            if (e5 != OLD_CODE_EADDED) {
                MediaLoader.getInstance().cancel(d5);
            }
        };
        this.onErrorV2 = (y4, z4, a5) => {
            console.log('medialoader onErrorV2 called, url: ' + y4 + " code: " + z4 + " msg " + a5);
            if (this.mOnLoadStatusListener != null) {
                let b5 = findErrorCodeByValue(z4);
                let c5 = new ErrorInfo();
                c5.setCode(b5);
                c5.setMsg(a5);
                this.mOnLoadStatusListener.onErrorV2(y4, c5);
            }
            if (z4 != PlayerErrorCode.MEDIALOADER_ERROR_ADDED) {
                MediaLoader.getInstance().cancel(y4);
            }
        };
        this.onCompleted = (x4) => {
            console.log('medialoader onCompleted called, url: ' + x4);
            if (this.mOnLoadStatusListener) {
                this.mOnLoadStatusListener.onCompleted(x4);
            }
        };
        this.onCanceled = (w4) => {
            console.log('medialoader onCanceled called, url: ' + w4);
            if (this.mOnLoadStatusListener) {
                this.mOnLoadStatusListener.onCancel(w4);
            }
        };
        premierlibrary.nMediaLoaderConstruct(this);
    }
    setOnLoadStatusListener(r4) {
        MediaLoader.getInstance().mOnLoadStatusListener = r4;
    }
    load(o4, p4) {
        let q4 = AliPlayerGlobalSettings.OnGetUrlHashCallback(o4);
        premierlibrary.nMediaLoaderLoad(o4, q4, p4);
    }
    loadWithBandWidth(k4, l4, m4) {
        let n4 = AliPlayerGlobalSettings.OnGetUrlHashCallback(k4);
        premierlibrary.nMediaLoaderLoadWithBandWidth(k4, n4, l4, m4);
    }
    cancel(i4) {
        if (i4 == undefined) {
            premierlibrary.nMediaLoaderCancelAll();
        }
        else {
            let j4 = AliPlayerGlobalSettings.OnGetUrlHashCallback(i4);
            premierlibrary.nMediaLoaderCancel(i4, j4);
        }
    }
    pause(g4) {
        let h4 = AliPlayerGlobalSettings.OnGetUrlHashCallback(g4);
        premierlibrary.nMediaLoaderPause(true, g4, h4);
    }
    resume(e4) {
        let f4 = AliPlayerGlobalSettings.OnGetUrlHashCallback(e4);
        premierlibrary.nMediaLoaderPause(false, e4, f4);
    }
}
